import React, { Component } from 'react'
import { Table, DatePicker, Divider, Select, Form, Input, Button } from 'antd'
import { connect } from 'dva'
const Option = Select.Option
const RangePicker = DatePicker.RangePicker
var moment = require('moment')

@connect(({ dailyWithdrawalWhitelist }) => ({
  model: dailyWithdrawalWhitelist
}))

class AssessmentHis extends Component {
  defaultPageValue = {
    defaultPageSize: 50,
    pageSizeOptions: ['50', '100', '500', '2000'],
    showSizeChanger: true,
    onChange: (page, size) => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = {
    visible: false,
    isUpdate: false,
    confirmVisible: false,
    deleteConfirmMsg: '',
    season: 0,
    type: 0,
    anchorUid: 0,
    yyno: 0,
    dateRange: [moment(), moment()],
    optType: 0
  }

  handleSearch = () => {
    const { modelName, dispatch } = this.props
    const { yyno, anchorUid, optType, dateRange } = this.state
    const data = { yyno: yyno, anchorUid: anchorUid, optType: optType, his: true }
    if (dateRange) {
      data.start = dateRange[0].format('YYYYMM')
      data.end = dateRange[1].format('YYYYMM')
    }

    dispatch({
      type: `${modelName}/getAssessmentList`,
      payload: data
    })
  }

  // content
  render () {
    const { model: { list } } = this.props
    const { dateRange } = this.state
    
    return (
      <Form>
        UID：
        <Input placeholder='uid' onChange={e => this.setState({ anchorUid: e.target.value })} style={{ width: 120 }} /> {/* 搜索按钮 */}
        <Divider type='vertical' /> {/* 分割线 */}
        YY号：
        <Input placeholder='YY号' onChange={e => this.setState({ yyno: e.target.value })} style={{ width: 120 }} /> {/* 搜索按钮 */}
        <Divider type='vertical' /> {/* 分割线 */}
        变动类型：
        <Select labelInValue defaultValue={{ key: 0 }} style={{ width: 100 }} onChange={(v) => this.setState({ optType: v.key })}>
          <Option value={0}>新增</Option>
          <Option value={1}>移除</Option>
        </Select>
        <Divider type='vertical' /> {/* 分割线 */}
        <RangePicker style={{ marginLeft: 10, width: 330 }} picker='month' defaultValue={dateRange} showTime format={'YYYY-MM'} onChange={e => this.setState({ dateRange: e })} />
        <Divider type='vertical' /> {/* 分割线 */}
        <Button type='primary' onClick={this.handleSearch}>搜索</Button>
        <Divider type='vertical' />
        <Table rowKey={(record, index) => index} dataSource={list} columns={this.columns} pagination={this.defaultPageValue} />
      </Form>
    )
  }
}

export default AssessmentHis
