import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Tabs, Row, Col, Table, Button, InputNumber, Popconfirm, Space, Typography, message } from 'antd'
// import { timeFormater } from '@/utils/common'
// import { propTypeOptions/*, isVoiceRoomPath*/ } from '../dropCommon'
// import PrizeSelector from '../DropMain/components/prizeSelector'
// import { broadcastOptionsJY, broadcastOptionsVR } from '../DropMain/components/list_common'
const namespace = 'clawMachinePrize'

@connect(({ clawMachinePrize }) => ({
  model: clawMachinePrize
}))

class clawMachinePrize extends Component {
  state = {
    // taskId: 1,
    // businessTag: 'jy', // jy-交友 yydog-幸运小狗
    appID: 2,
    editing: false
  }

  componentDidMount = () => {
    const businessTag = 'jy'
    const appID = 2
    this.setState({ businessTag, appID })
    this.getCompensateConfig()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    console.log('dispatch', funcName, params)
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // --------------------------------------------------

  // 获取所有奖励列表
  getAllPrize = () => {
    this.callModel('getAllPrizeList', {
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('获取奖励列表失败: msg=' + msg)
        }
      }
    })
  }

  // 获取配置
  getCompensateConfig = () => {
    this.callModel('getCompensateConfig', {
      // params: { taskID: id, tag: tag }
    })
  }

  // 检查数据
  checkConfig = (raw) => {
    console.debug(raw)
    if (!raw) {
      message.warning('道具池列表为空,请检查')
      return false
    }
    const poolList = raw
    for (let i = 0; i < poolList.length; i++) {
      // if (poolList[i].rate < 0) {
      //   message.warning('概率不能小于0, 请检查')
      //   return false
      // }
      if (poolList[i].propsId <= 0) {
        message.warning('奖励类型配置有误, 请检查')
        return false
      }
      if (poolList[i].propsCount !== 1) {
        message.warning('奖励数量配置有误, 请检查')
        return false
      }
    }
    return true
  }

  // 全量更新
  updateCompensateConfig = () => {
    const configData = this.props.model
    console.log('updateconfigData', configData)
    if (!this.checkConfig(configData)) {
      return
    }
    // const { } = this.state
    this.callModel('updateCompensateConfig', {
      params: configData,
      isDetailMode: true,
      isJsonMode: true,
      cbFunc: (ok) => {
        if (ok) {
          message.info('更新成功')
          this.setState({ editing: false })
          this.refreshData()
        }
      }
    })
  }

  // 切换道具池
  // onTagChange = (id) => {
  //   this.setState({ taskId: id })
  //   const { businessTag } = this.state
  //   this.getCompensateConfig(id, businessTag)
  // }

  // 切换业务
  // onBusinessTypeChange = (tag) => {
  //   this.setState({ businessTag: tag })
  //   const { taskId } = this.state
  //   this.getCompensateConfig(taskId, tag)
  // }

  // 重置信息
  refreshData = () => {
    this.setState({ editing: false })
    this.changeState({ configData: {} })
    // const { businessTag } = this.state
    this.getCompensateConfig()
  }

  // 增加奖励
  // addItemToPool = () => {
  //   const { configData } = this.props.model
  //   let cp = configData
  //   let defaultVal = { propsType: 0, count: 0, price: 0, dailyLimit: -1 }
  //   cp.poolList.push(defaultVal)
  //   this.changeState('configData', cp)
  // }

  // 移除奖励
  // removeItemFromPool = (index) => {
  //   const { configData } = this.props.model
  //   let newPoolList = []
  //   let newConfigData = configData
  //   console.debug('index=', index)
  //   configData.poolList.forEach((item, i) => {
  //     if (i !== index) newPoolList.push(item)
  //   })
  //   newConfigData.poolList = newPoolList
  //   this.changeState('configData', newConfigData)
  // }

  // 更新道具池列表
  // onPoolListChange = (index, v) => {
  //   const { appID } = this.state
  //   // console.debug('debugInfo===>', index, v, appID)
  //   const { configData, globalPrizeList } = this.props.model
  //   let detail = null
  //   for (let i = 0; i < globalPrizeList.length; i++) {
  //     if (globalPrizeList[i].appId === appID && globalPrizeList[i].id === v.value) {
  //       detail = globalPrizeList[i]
  //       break
  //     }
  //   }
  //   if (detail == null) return
  //   let newData = configData
  //   newData.list[index].propId = detail.id
  //   newData.list[index].appId = appID
  //   newData.list[index].name = detail.name
  //   newData.list[index].price = detail.price
  //   this.changeState('configData', newData)
  // }

  onInputChange = (row, field) => value => {
    let cp = this.props.model.configData
    cp[row][field] = value
    // this.changeState('compensateConfig', cp)
  }

  renderColumn = (before) => {
    // const { editing, appID, businessTag } = this.state
    const { editing } = this.state
    // const { globalPrizeList } = this.props.model
    // if (!editing) {
    //   return before.filter(item => { return item.dataIndex !== 'dailyLimit' }) // 查看时隐藏日上限字段，后面可能要改回去
    // }
    let after = []
    before.forEach(col => {
      // if (['propsType'].indexOf(col.dataIndex) > -1) {
      //   col.render = (text, record, index) => {
      //     let options = [propTypeOptions][['propsType'].indexOf(col.dataIndex)]
      //     return <Select onChange={this.onInputChange(index, col.dataIndex)} options={options} defaultValue={text} />
      //   }
      // }
      if (editing) {
        if (['possibilityAme', 'possibilityCurrency'].indexOf(col.dataIndex) > -1) {
          col.render = (text, record, index) => {
            return <InputNumber min={1} onChange={this.onInputChange(index, col.dataIndex)} defaultValue={text} />
          }
        }
      }

      // if (col.dataIndex === 'propId') {
      //   col.render = (v, r, i) => {
      //     return <PrizeSelector
      //       value={v}
      //       type='select'
      //       prizeList={globalPrizeList}
      //       appIDLimit={appID}
      //       onComfirm={(v) => {
      //         this.onPoolListChange(i, v)
      //       }}
      //     />
      //   }
      // }
      // if (col.dataIndex === 'bcType') {
      //   col.render = (v, r, i) => {
      //     return <Select options={businessTag === 'jy' ? broadcastOptionsJY : broadcastOptionsVR} value={v}
      //                    onChange={this.onInputChange(i, col.dataIndex)} />
      //   }
      // }
      after.push(col)
    })
    // after.push({ title: '操作', render: (v, r, i) => { return <Button danger size='small' onClick={() => { this.removeItemFromPool(i) }}>移除奖励</Button> } })
    return after
  }

  render () {
    const { route } = this.props
    const { editing, taskId, businessTag } = this.state
    const { configData } = this.props.model
    const { Text, Title } = Typography
    const { TabPane } = Tabs
    console.log('configData', configData)
    const columns = [
      // { title: '序号', render: (v, r, i) => { return i + 1 } },
      { dataIndex: 'prizeId', title: '奖品id' },
      { dataIndex: 'propsId', title: '礼物ID' },
      { dataIndex: 'propsName', title: '奖励道具名称' },
      { dataIndex: 'price', title: '单价(紫水晶)' },
      { dataIndex: 'propsCount', title: '数量' },
      { dataIndex: 'possibilityAme', title: '成功概率(紫水晶)' },
      { dataIndex: 'possibilityCurrency', title: '成功概率(布料/能量)' }
      // { dataIndex: 'dailyLimit', title: '每日限制' },
      // { dataIndex: 'todayCount', title: '当日已发' },
      // { dataIndex: 'propsType', title: '类型', render: (v, r) => { return propTypeOptions.find(item => item.value === v)?.label } },
      // { dataIndex: 'bcType', title: '广播类型', render: (v, r) => { return (businessTag === 'jy' ? broadcastOptionsJY : broadcastOptionsVR).find(item => item.value === v)?.label } }
    ]

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <div><font color='red'>注意事项:</font></div>
          <div><font color='red'> 更新前请主动刷新,概率最小为1,小于等于0将被按照1处理</font></div>
        </Card>
        <Card>

          <Title level={4}>{businessTag === 'jy' ? '交友抓娃娃' : '聊天室幸运小狗'}</Title>

          <Tabs defaultActiveKey={businessTag === 'jy' ? 1 : 2} type='line' size='small' onChange={(v) => this.onTagChange(v)} >
            {
              businessTag === 'jy'
                ? <TabPane tab='道具池' key={1} disabled={editing && taskId !== 1} />
                : ''
            }
          </Tabs>

          {/* <Space direction='vertical' style={{ marginBottom: '2em' }}>  FIXME:临时修改，晚点要还原回去
            <Text>1. 抽取道具次数： 用户在各个端中普通+超级模式抽取道具的总次数</Text>
            <Text>2. 上限： -1表示无上限, 0表示不发此奖励道具, 大于0表示发放上限</Text>
          </Space> */}

          <Row style={{ marginBottom: '1em' }}>
            <Space>
              <Button disabled={editing} onClick={() => this.setState({ editing: true })} >编辑配置</Button>
              <Popconfirm title='确定放弃更改吗?' onConfirm={() => this.refreshData()}>
                <Button danger type='dashed' hidden={!editing}>放弃修改</Button>
              </Popconfirm>
              {/* <Button hidden={!editing} type='primary' onClick={() => { this.addItemToPool() }}>新增奖励</Button> */}
              <Popconfirm title='确定提交修改吗?' onConfirm={() => this.updateCompensateConfig()}>
                <Button danger hidden={!editing} type='primary'>确认修改</Button>
              </Popconfirm>
            </Space>
          </Row>

          {/* <Row style={{ marginBottom: '2em' }}> FIXME:临时修改，晚点要还原回去
            <Space direction='vertical'>
              <Text type='secondary'>业务标签:  {businessTag}</Text>
              <Text type='secondary'>道具池id: {configData.taskId}</Text>
              <Text type='secondary'>更新人： {configData.uid}</Text>
              <Text type='secondary'>更新时间：{timeFormater(configData.timestamp)}</Text>
              <Text type='secondary'>版本号： {configData.version}</Text>
            </Space>
          </Row> */}

          {/* <Text type='warning' strong>抽取道具次数：</Text>
          <Row style={{ marginBottom: '2em' }}>
            {editing
              ? <InputNumber style={{ width: '10em' }} value={configData.total} onChange={(v) => { let cp = configData; cp.total = v; this.changeState('configData', cp) }} />
              : <InputNumber style={{ width: '10em' }} disabled value={configData.total} />
            }
          </Row> */}

          {/*  <Text type='warning' strong>道具池名称：</Text>
          <Row style={{ marginBottom: '2em' }}>
            {editing
              ? <Input style={{ width: '10em' }} value={configData.name} onChange={(v) => { let cp = configData; cp.name = v.target.value; this.changeState('configData', cp) }} />
              : <Input style={{ width: '10em' }} disabled value={configData.name} />
            }
          </Row> */}

          {/* <Text type='danger'>道具实际概率=该道具普通概率/全部道具普通概率之和</Text> */}
          <br />
          <Text type='warning' strong>道具池列表：</Text>
          <Row style={{ marginBottom: '2em' }}>
            <Col span={24}>
              <Table columns={this.renderColumn(columns)} bordered dataSource={configData || []} size='small' scroll={{ x: 'max-content' }} />
            </Col>
          </Row>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default clawMachinePrize
