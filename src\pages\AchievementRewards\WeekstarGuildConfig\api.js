import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/weekstar_new/get_week_guild_reward_config?${stringify(params)}`)
}

export function update (params) {
  return request(`/weekstar_new/update_week_guild_reward_config`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
