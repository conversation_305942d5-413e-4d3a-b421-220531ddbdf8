import React, { Component } from 'react'
import { Table, Divider, Button, Card, Form, DatePicker } from 'antd'
import exportExcel from '@/utils/exportExcel'
import { connect } from 'dva'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
const bizTypeMap = { 1: '魔豆', 2: '盖章', 3: '活动布料', 4: '活动布料盖章', 5: '布料', 6: '能量', 7: '常驻玩法盖章', 8: '活动玩法盖章' }

@connect(({ compereSummary }) => ({
  model: compereSummary
}))
class InteractCompereSummary extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
    this.setState()
  }

  // 日期 流水类型 总流水/紫水晶 互动主持流水汇总 互动主持流水占比
  // columns = [
  //   { title: '日期', dataIndex: 'date', align: 'center' },
  //   { title: '流水类型', dataIndex: 'kind', align: 'center', render: (text) => { return bizTypeMap[text] }, filters: [{ text: '魔豆', value: 1 }, { text: '盖章', value: 2 }, { text: '活动布料', value: 3 }, { text: '活动布料盖章', value: 4 }, { text: '布料', value: 5 }, { text: '能量', value: 6 }, { text: '常驻玩法盖章', value: 7 }, { text: '活动玩法盖章', value: 8 }], onFilter: (value, record) => record.kind === value },
  //   { title: '总流水/紫水晶', dataIndex: 'total', align: 'center' },
  //   { title: '互动主持流水汇总', dataIndex: 'hatking', align: 'center' },
  //   { title: '互动主持流水占比', dataIndex: 'percent', align: 'center' }
  // ]

    pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

    loadData = () => {
      const { dispatch } = this.props
      const { dateRange } = this.state
      const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
      const { modelName } = this.props
      dispatch({
        type: `${modelName}/getCompereSummaryList`,
        payload: data
      })
    }

    onQuery = () => {
      this.loadData()
    }

    onChange = (date, format) => {
      this.setState({ dateRange: date })
    }

    onStartChange = (value) => {
      this.onChange('startValue', value)
    }

    onEndChange = (value) => {
      this.onChange('endValue', value)
    }

    renderColumns = summaryList => {
      if (summaryList.length === 0) {
        return this.columns
      }

      // 固定列
      let renderCol = [
        { title: '日期', dataIndex: 'date', align: 'center', fixed: 'left' },
        { title: '流水类型', dataIndex: 'kind', align: 'center', render: text => { return bizTypeMap[text] }, filters: [{ text: '魔豆', value: 1 }, { text: '盖章', value: 2 }, { text: '活动布料', value: 3 }, { text: '活动布料盖章', value: 4 }, { text: '布料', value: 5 }, { text: '能量', value: 6 }, { text: '常驻玩法盖章', value: 7 }, { text: '活动玩法盖章', value: 8 }], onFilter: (value, record) => record.kind === value, fixed: 'left' },
        { title: '总流水/紫水晶', dataIndex: 'total', align: 'center', fixed: 'left' },
        // { title: '互动主持流水汇总', dataIndex: 'hatking', align: 'center', fixed: 'left' },
        { title: 'top20主持流水占比', dataIndex: 'percent', align: 'center', fixed: 'left' }
      ]

      // 动态解析
      Object.keys(summaryList[0]).map(key => {
        if (key === 'date' || key === 'kind' || key === 'total' || key === 'hatking' || key === 'percent') {
          return
        }

        let col = { title: key, dataIndex: key, align: 'center' }
        renderCol.push(col)
      })

      return renderCol
    }

    onExport = () => {
      const { model: { compereSummaryList } } = this.props
      let summaryList = compereSummaryList
      if (summaryList.length === 0) {
        return
      }

      // 固定列
      let renderCol = [
        { title: '日期', dataIndex: 'date' },
        { title: '流水类型', dataIndex: 'kind' },
        { title: '总流水/紫水晶', dataIndex: 'total' },
        // { title: '互动主持流水汇总', dataIndex: 'hatking' },
        { title: 'top20主持流水占比', dataIndex: 'percent' }
      ]

      // 动态解析
      Object.keys(summaryList[0]).map(key => {
        if (key === 'date' || key === 'kind' || key === 'total' || key === 'hatking' || key === 'percent') {
          return
        }

        let col = { title: key, dataIndex: key }
        renderCol.push(col)
      })

      let headers = []
      renderCol.forEach(function (item) {
        headers.push({ key: item.dataIndex, header: item.title })
      })

      var exportData = summaryList.map(item => {
        let v = $.extend(true, {}, item)
        v.kind = bizTypeMap[v.kind]
        return v
      })

      exportExcel(headers, exportData)
    }

    /* *******************************页面布局***************************************************************/
    render () {
      const { model: { compereSummaryList } } = this.props
      const { dateRange } = this.state
      return (
        <Card>
          <Form>
            <span style={{ marginLeft: 5 }}>时间范围:</span>
            <RangePicker style={{ marginLeft: 5 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onQuery}>查询</Button>
            <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
            <Divider />
            <Table dataSource={compereSummaryList} columns={this.renderColumns(compereSummaryList)} rowKey={(record, index) => index} pagination={this.pagination} size='small' scroll={{ x: 'max-content' }} />
          </Form>
        </Card>
      )
    }
}

export default InteractCompereSummary
