import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Table, Modal, Select, Form, InputNumber, TreeSelect, DatePicker, Button, Upload, message, Popover, Input, Divider, Tabs, Tooltip, Collapse } from 'antd'
import { connect } from 'dva'
import { UploadOutlined, CaretRightOutlined } from '@ant-design/icons'
import { exportExcel } from 'xlsx-oc'
import { CSVLink } from 'react-csv'
import { checkUid } from '@/utils/common'

const namespace = 'newCompereInfo'
const listCompereInfoURL = `${namespace}/listCompereInfo`
const importNewCompereURL = `${namespace}/importNewCompere`
const removeNewCompereURL = `${namespace}/removeNewCompere`
const approvalNewCompereURL = `${namespace}/approvalNewCompere`
const batchApprovalNewCompereURL = `${namespace}/batchApprovalNewCompere`
const getCompereInboundStatusURL = `${namespace}/getCompereInboundStatus`

const Option = Select.Option
const { TreeNode } = TreeSelect
const { Panel } = Collapse

var moment = require('moment')
var dateFormat = 'YYYY-MM-DD'

// 业务类型
const businessTypeJY = 1 // 交友
const businessTypePK = 2 // 约战
const businessTypeBABY = 3 // 宝贝
const businessTypeMap = { 0: '全部', 1: '交友', 2: '约战', 3: '宝贝' }

// 主持身份
const contractTypeNormal = 1 // 普通主持
const contractTypeSuper = 2 // 超级主持
const contractTypeHatSuper = 4 // 帽子超级
const contractTypeHatAce = 5 // 王牌主播
const contractTypeMap = {
  1: { 0: '已解约', 1: '普通主持', 2: '超级主持', 4: '帽子超级' },
  2: { 0: '已解约', 1: '普通主持', 5: '王牌主播' },
  3: { 0: '已解约', 1: '普通主持', 5: '王牌主播' }
}

// 公会身份
const guildTypeYY = 1 // 普通公会
const guildTypeShuiJing = 2 // 水晶公会
const guildTypeSuperShuiJing = 3 // 超级水晶公会
const guildTypeAceGuild = 4 // 王牌公会
const guildTypeNormalGuild = 5 // 普通公会
const guildTypeMap = {
  1: { 1: '普通公会', 2: '水晶公会', 3: '超级水晶公会' },
  2: { 4: '王牌公会', 5: '普通公会' },
  3: { 4: '王牌公会', 5: '普通公会' }
}

// 审批结果
// const approvalWait = 1 // 待审批
const approvalPass = 1 // 通过
const approvalReject = 2 // 不通过
const approvalFirstGoing = 3 // 一审中
const approvalSecondGoing = 4 // 二审中
const reviewMap = { 0: '待审批', 1: '通过', 2: '不通过', 3: '疑似' }
const approvalMap = { 0: '未开始', 1: '通过', 2: '不通过', 3: '一审中', 4: '二审中' }

// 运营审批
const reviewWait = 0 // 待审批
const reviewPass = 1 // 通过
const reviewReject = 2 // 不通过
const reviewSuspected = 3 // 疑似

// 是否
// const yesNoMap = { 0: '-', 1: '是', 2: '否' }

const yesBoolStatus = true // 是
const noBoolStatus = false // 否
const yesNoBoolMap = { true: '是', false: '否' }

// const importTypeSystem = 1
const importTypeUser = 2

const tabIdx1 = '1'
const tabIdx2 = '2'
const tabIdx3 = '3'
const tabIdx4 = '4'
const tabIdx5 = '5'
const tabIdx6 = '6'
const tabIdx7 = '7'

@connect(({ newCompereInfo }) => ({
  model: newCompereInfo
}))

class NewCompereInfo extends Component {
  constructor (props) {
    super(props)

    this.refreshCompereInfo(tabIdx1)
  }

  columns = [
    // { title: 'ID', dataIndex: 'idx', width: 80, align: 'center', fixed: 'left' },
    { title: '业务', dataIndex: 'business', width: 100, align: 'center', fixed: 'left', render: (text, record) => (businessTypeMap[record.business]) },
    { title: 'UID', dataIndex: 'uid', align: 'center', fixed: 'left' },
    { title: 'YY号', dataIndex: 'yy', align: 'center' },
    { title: '昵称', dataIndex: 'nickname', align: 'center' },
    // { title: '签约频道ID', dataIndex: 'sid', align: 'center' },
    { title: '短位id', dataIndex: 'asid', width: 120, align: 'center' },
    { title: '公会身份', dataIndex: 'guildType', width: 200, align: 'center', render: (text, record) => (guildTypeMap[record.business][record.guildType]) },
    { title: '主持身份', dataIndex: 'contractType', width: 200, align: 'center', render: (text, record) => (contractTypeMap[record.business][record.contractType]) },
    {
      title: '签约时间（开始-结束）',
      dataIndex: 'signTime',
      align: 'center',
      width: 200,
      render: (text, record) => {
        if (record.signStartTime === 0 || record.signEndTime === 0) {
          return ''
        }
        record.signTime = moment.unix(record.signStartTime).format(dateFormat) + '至' + moment.unix(record.signEndTime).format(dateFormat)
        return record.signTime
      }
    },
    { title: 'PC机器码', dataIndex: 'pcDevice', align: 'center', render: (text, record) => (record.pcDevice === 0 ? '' : <a href='https://jyboss-test.yy.com/n/1/21/online_info/related_acount_info' target='_blank'>{record.pcDevice}</a>) },
    { title: '机器码其他登录UID个数', dataIndex: 'deviceOtherUidCount', align: 'center' },
    { title: '机器码其他登录UID实名个数', dataIndex: 'deviceOtherUidRealCount', align: 'center' },
    { title: '机器码是否更新', dataIndex: 'isDeviceUpdate', align: 'center', render: (text, record) => (record.isDeviceUpdate === true ? <font color='red'>{yesNoBoolMap[record.isDeviceUpdate]}</font> : yesNoBoolMap[record.isDeviceUpdate]) },
    { title: '运营审核状态', dataIndex: 'reviewStatus', width: 130, align: 'center', render: (text, record) => (record.reviewStatus === approvalReject ? <font color='red'>{reviewMap[record.reviewStatus]}</font> : reviewMap[record.reviewStatus]) },
    { title: '业务审核状态', dataIndex: 'approvalStatus', width: 130, align: 'center', render: (text, record) => (record.approvalStatus === approvalReject ? <font color='red'>{approvalMap[record.approvalStatus]}</font> : approvalMap[record.approvalStatus]) },
    {
      title: '入库时间',
      dataIndex: 'inboundTime',
      align: 'center',
      width: 150,
      render: (text, record) => {
        if (record.inboundTime === 0) {
          return ''
        }
        return moment.unix(record.inboundTime).format('YYYY-MM-DD')
      }
    },
    {
      title: '扶持开始时间',
      dataIndex: 'supportStartTime',
      align: 'center',
      width: 150,
      render: (text, record) => {
        if (record.supportStartTime === undefined || record.supportStartTime === 0) {
          return ''
        }
        // 取整点
        record.supportStartTime = Math.floor(record.supportStartTime / 100) * 100
        return moment.unix(record.supportStartTime).format(dateFormat)
      }
    },
    {
      title: '扶持结束时间',
      dataIndex: 'supportEndTime',
      align: 'center',
      width: 150,
      render: (text, record) => {
        if (record.supportEndTime === 0) {
          return ''
        }
        // 取整点
        record.supportEndTime = Math.floor(record.supportEndTime / 100) * 100
        return moment.unix(record.supportEndTime - 1).format(dateFormat) + ' 24点'
      }
    },
    { title: '运营审核备注', dataIndex: 'reviewReason', width: 200, align: 'center' },
    { title: '操作', key: 'operation', align: 'center', render: (text, record) => (<span><Popover content={this.renderContent(record)} trigger='click'><a>出库</a></Popover></span>) },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => {
        if (record.reviewStatus === undefined || record.reviewStatus === null) {
          return
        }

        if (record.reviewStatus === reviewWait) {
          return <span><Popover content={this.renderApprovalContent(record, reviewSuspected)} trigger='click'><a>疑似</a></Popover><Divider type='vertical' /><Popover content={this.renderApprovalContent(record, reviewPass)} trigger='click'><a>通过</a></Popover></span>
        }

        if (record.reviewStatus === reviewPass && record.approvalStatus === approvalPass) {
          return <span><Popover content={this.renderApprovalContent(record, reviewSuspected)} trigger='click'><a>疑似</a></Popover><Divider type='vertical' /><Popover content={this.renderApprovalContent(record, reviewReject)} trigger='click'><a>不通过</a></Popover></span>
        }

        if (record.reviewStatus === reviewSuspected) {
          return <span><Popover content={this.renderApprovalContent(record, reviewPass)} trigger='click'><a>通过</a></Popover><Divider type='vertical' /><Popover content={this.renderApprovalContent(record, reviewReject)} trigger='click'><a>不通过</a></Popover></span>
        }

        if (record.reviewStatus === reviewReject || (record.reviewStatus === reviewPass && record.approvalStatus !== approvalPass)) {
          return <span><Popover content={this.renderApprovalContent(record, reviewPass)} trigger='click'><a>通过</a></Popover></span>
        }
      }
    }
  ]

  columnsHadOver = [
    { title: '业务', dataIndex: 'business', width: 100, align: 'center', fixed: 'left', render: (text, record) => (businessTypeMap[record.business]) },
    { title: 'UID', dataIndex: 'uid', align: 'center', fixed: 'left' },
    { title: 'YY号', dataIndex: 'yy', align: 'center' },
    { title: '昵称', dataIndex: 'nickname', align: 'center' },
    { title: '短位id', dataIndex: 'asid', width: 120, align: 'center' },
    { title: '公会身份', dataIndex: 'guildType', width: 200, align: 'center', render: (text, record) => (guildTypeMap[record.business][record.guildType]) },
    { title: '主持身份', dataIndex: 'contractType', width: 200, align: 'center', render: (text, record) => (contractTypeMap[record.business][record.contractType]) },
    {
      title: '签约时间（开始-结束）',
      dataIndex: 'signTime',
      align: 'center',
      width: 200,
      render: (text, record) => {
        if (record.signStartTime === 0 || record.signEndTime === 0) {
          return ''
        }
        record.signTime = moment.unix(record.signStartTime).format(dateFormat) + '至' + moment.unix(record.signEndTime).format(dateFormat)
        return record.signTime
      }
    },
    { title: 'PC机器码', dataIndex: 'pcDevice', align: 'center', render: (text, record) => (record.pcDevice === 0 ? '' : <a href='https://jyboss-test.yy.com/n/1/21/online_info/related_acount_info' target='_blank'>{record.pcDevice}</a>) },
    { title: '机器码其他登录UID个数', dataIndex: 'deviceOtherUidCount', align: 'center' },
    { title: '机器码其他登录UID实名个数', dataIndex: 'deviceOtherUidRealCount', align: 'center' },
    { title: '机器码是否更新', dataIndex: 'isDeviceUpdate', align: 'center', render: (text, record) => (record.isDeviceUpdate === true ? <font color='red'>{yesNoBoolMap[record.isDeviceUpdate]}</font> : yesNoBoolMap[record.isDeviceUpdate]) },
    { title: '运营审核状态', dataIndex: 'reviewStatus', width: 130, align: 'center', render: (text, record) => (record.reviewStatus === approvalReject ? <font color='red'>{reviewMap[record.reviewStatus]}</font> : reviewMap[record.reviewStatus]) },
    { title: '业务审核状态', dataIndex: 'approvalStatus', width: 130, align: 'center', render: (text, record) => (record.approvalStatus === approvalReject ? <font color='red'>{approvalMap[record.approvalStatus]}</font> : approvalMap[record.approvalStatus]) },
    {
      title: '入库时间',
      dataIndex: 'inboundTime',
      align: 'center',
      width: 150,
      render: (text, record) => {
        if (record.inboundTime === 0) {
          return ''
        }
        return moment.unix(record.inboundTime).format('YYYY-MM-DD')
      }
    },
    {
      title: '扶持开始时间',
      dataIndex: 'supportStartTime',
      align: 'center',
      width: 150,
      render: (text, record) => {
        if (record.supportStartTime === undefined || record.supportStartTime === 0) {
          return ''
        }
        // 取整点
        record.supportStartTime = Math.floor(record.supportStartTime / 100) * 100
        return moment.unix(record.supportStartTime).format(dateFormat)
      }
    },
    {
      title: '扶持结束时间',
      dataIndex: 'supportEndTime',
      align: 'center',
      width: 150,
      render: (text, record) => {
        if (record.supportEndTime === 0) {
          return ''
        }
        // 取整点
        record.supportEndTime = Math.floor(record.supportEndTime / 100) * 100
        return moment.unix(record.supportEndTime - 1).format(dateFormat) + ' 24点'
      }
    },
    { title: '运营审核备注', dataIndex: 'reviewReason', width: 200, align: 'center' },
    { title: '系统备注', dataIndex: 'removeReason', width: 200, align: 'center' }
  ]

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    // onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  state = {
    visible: false,
    tabIdx: '1'
  }

  renderContent = (record) => {
    return (
      <div>
        <Input.TextArea onChange={this.onTextChange} row={4} placeholder={'必填，最多100字符'} />
        <Button onClick={this.removeNewCompere(record)} style={{ marginLeft: 120, marginTop: 5 }} type='primary'>确定</Button>
      </div>
    )
  }

  renderApprovalContent = (record, reviewStatus) => {
    return (
      <div>
        <Input.TextArea onChange={this.onApprovalTextChange} row={4} placeholder={'选填，最多100字符'} />
        <Button onClick={this.approvalRecord(record, reviewStatus)} style={{ marginLeft: 50, marginTop: 5 }} type='primary'>确定</Button>
      </div>
    )
  }

  // 批量审批-待审核页面(opt: 1-批量通过/2-审批不通过/3-批量疑似)
  batchRenderApprovalWaitPage = (opt) => {
    const { exportKey1 } = this.state
    return (
      <div>
        <Input.TextArea onChange={this.batchOnApprovalTextChange} row={4} placeholder={'选填，最多100字符'} />
        <Button onClick={this.batchApprovalRecord(exportKey1, opt)} style={{ marginLeft: 50, marginTop: 5 }} type='primary'>确定</Button>
      </div>
    )
  }

  // 批量审批-疑似页面(opt: 1-批量通过/2-审批不通过)
  batchRenderApprovalSuspectedPage = (opt) => {
    const { exportKey2 } = this.state
    return (
      <div>
        <Input.TextArea onChange={this.batchOnApprovalTextChange} row={4} placeholder={'选填，最多100字符'} />
        <Button onClick={this.batchApprovalRecord(exportKey2, opt)} style={{ marginLeft: 50, marginTop: 5 }} type='primary'>确定</Button>
      </div>
    )
  }

  // 批量审批-审批通过页面(opt: 2-审批不通过)
  batchRenderApprovalPassPage = (opt) => {
    const { exportKey4 } = this.state
    return (
      <div>
        <Input.TextArea onChange={this.batchOnApprovalTextChange} row={4} placeholder={'选填，最多100字符'} />
        <Button onClick={this.batchApprovalRecord(exportKey4, opt)} style={{ marginLeft: 50, marginTop: 5 }} type='primary'>确定</Button>
      </div>
    )
  }

  onTextChange = e => {
    this.setState({ removeReason: e.target.value })
  }

  onApprovalTextChange = e => {
    this.setState({ reviewReason: e.target.value })
  }

  batchOnApprovalTextChange = e => {
    this.setState({ batchReviewReason: e.target.value })
  }

  refreshCompereInfo = (tabIdx) => {
    const { searchBusiness1, searchUID1, searchYY1, searchNick1, searchSID1, searchASID1, searchGuildType1, searchContractType1, searchSignStartTime1, searchSignEndTime1, searchInboundTime1, searchSupportStartTime1, searchSupportEndTime1, searchDeviceOtherUid1, searchApprovalStatus1 } = this.state

    const { searchBusiness2, searchUID2, searchYY2, searchNick2, searchSID2, searchASID2, searchGuildType2, searchContractType2, searchSignStartTime2, searchSignEndTime2, searchInboundTime2, searchSupportStartTime2, searchSupportEndTime2, searchDeviceOtherUid2, searchApprovalStatus2 } = this.state

    const { searchBusiness3, searchUID3, searchYY3, searchNick3, searchSID3, searchASID3, searchGuildType3, searchContractType3, searchSignStartTime3, searchSignEndTime3, searchInboundTime3, searchSupportStartTime3, searchSupportEndTime3, searchDeviceOtherUid3, searchApprovalStatus3 } = this.state

    const { searchBusiness4, searchUID4, searchYY4, searchNick4, searchSID4, searchASID4, searchGuildType4, searchContractType4, searchSignStartTime4, searchSignEndTime4, searchInboundTime4, searchSupportStartTime4, searchSupportEndTime4, searchDeviceOtherUid4, searchApprovalStatus4 } = this.state

    const { searchBusiness5, searchUID5, searchYY5, searchNick5, searchSID5, searchASID5, searchGuildType5, searchContractType5, searchSignStartTime5, searchSignEndTime5, searchInboundTime5, searchSupportStartTime5, searchSupportEndTime5, searchDeviceOtherUid5, searchApprovalStatus5 } = this.state

    const { searchBusiness6, searchUID6, searchYY6, searchNick6, searchSID6, searchASID6, searchGuildType6, searchContractType6, searchSignStartTime6, searchSignEndTime6, searchInboundTime6, searchSupportStartTime6, searchSupportEndTime6, searchDeviceOtherUid6, searchApprovalStatus6 } = this.state

    const { searchBusiness, searchUID } = this.state

    if (tabIdx === tabIdx1) {
      this.getData(tabIdx, searchBusiness1, searchUID1, searchYY1, searchNick1, searchSID1, searchASID1, searchGuildType1, searchContractType1, searchSignStartTime1, searchSignEndTime1, searchInboundTime1, searchSupportStartTime1, searchSupportEndTime1, searchDeviceOtherUid1, searchApprovalStatus1)
    } else if (tabIdx === tabIdx2) {
      this.getData(tabIdx, searchBusiness2, searchUID2, searchYY2, searchNick2, searchSID2, searchASID2, searchGuildType2, searchContractType2, searchSignStartTime2, searchSignEndTime2, searchInboundTime2, searchSupportStartTime2, searchSupportEndTime2, searchDeviceOtherUid2, searchApprovalStatus2)
    } else if (tabIdx === tabIdx3) {
      this.getData(tabIdx, searchBusiness3, searchUID3, searchYY3, searchNick3, searchSID3, searchASID3, searchGuildType3, searchContractType3, searchSignStartTime3, searchSignEndTime3, searchInboundTime3, searchSupportStartTime3, searchSupportEndTime3, searchDeviceOtherUid3, searchApprovalStatus3)
    } else if (tabIdx === tabIdx4) {
      this.getData(tabIdx, searchBusiness4, searchUID4, searchYY4, searchNick4, searchSID4, searchASID4, searchGuildType4, searchContractType4, searchSignStartTime4, searchSignEndTime4, searchInboundTime4, searchSupportStartTime4, searchSupportEndTime4, searchDeviceOtherUid4, searchApprovalStatus4)
    } else if (tabIdx === tabIdx5) {
      this.getData(tabIdx, searchBusiness5, searchUID5, searchYY5, searchNick5, searchSID5, searchASID5, searchGuildType5, searchContractType5, searchSignStartTime5, searchSignEndTime5, searchInboundTime5, searchSupportStartTime5, searchSupportEndTime5, searchDeviceOtherUid5, searchApprovalStatus5)
    } else if (tabIdx === tabIdx6) {
      this.getData(tabIdx, searchBusiness6, searchUID6, searchYY6, searchNick6, searchSID6, searchASID6, searchGuildType6, searchContractType6, searchSignStartTime6, searchSignEndTime6, searchInboundTime6, searchSupportStartTime6, searchSupportEndTime6, searchDeviceOtherUid6, searchApprovalStatus6)
    } else if (tabIdx === tabIdx7) {
      this.getCompereInboundStatusData(searchBusiness, searchUID)
    }
  }

  getData = (tabIdx, searchBusiness, searchUID, searchYY, searchNick, searchSID, searchASID, searchGuildType, searchContractType, searchSignStartTime, searchSignEndTime, searchInboundTime, searchSupportStartTime, searchSupportEndTime, searchDeviceOtherUid, searchApprovalStatus) => {
    if (searchGuildType > 10) {
      message.warn('公会身份选择出错')
      return
    }

    if (searchContractType > 10) {
      message.warn('主持签约身份选择出错')
      return
    }

    let searchInboundTimeTmp = ''
    if (searchInboundTime) {
      searchInboundTimeTmp = moment(searchInboundTime).format(dateFormat) + ' 00:00:00'
    }

    let searchSupportEndTimeTmp = ''
    if (searchSupportEndTime) {
      searchSupportEndTimeTmp = moment(searchSupportEndTime).unix()
    }

    let searchSupportStartTimeTmp = ''
    if (searchSupportStartTime) {
      searchSupportStartTimeTmp = moment(searchSupportStartTime).unix()
    }

    let signStartTimeTmp = 0
    let signEndTimeTmp = 0
    if (searchSignStartTime) {
      signStartTimeTmp = moment(searchSignStartTime).unix() * 1000
    }
    if (searchSignEndTime) {
      signEndTimeTmp = moment(searchSignEndTime).unix() * 1000
    }

    let data = { tabIdx: Number(tabIdx), business: searchBusiness, uid: searchUID, yy: searchYY, nick: searchNick, sid: searchSID, asid: searchASID, guildType: searchGuildType, contractType: searchContractType, signStartTime: signStartTimeTmp, signEndTime: signEndTimeTmp, inboundTime: searchInboundTimeTmp, supportTime: searchSupportEndTimeTmp, supportStartTime: searchSupportStartTimeTmp, deviceOtherUid: searchDeviceOtherUid, approvalStatus: searchApprovalStatus }
    console.log(data)
    this.props.dispatch({
      type: listCompereInfoURL,
      payload: data
    })
  }

  getCompereInboundStatusData = (searchBusiness, searchUID) => {
    this.props.dispatch({
      type: getCompereInboundStatusURL,
      payload: { business: Number(searchBusiness), uid: Number(searchUID) }
    })
  }

  removeNewCompere = (record) => () => {
    const { removeReason } = this.state
    this.props.dispatch({
      type: removeNewCompereURL,
      payload: { uid: record.uid, business: record.business, removeReason: removeReason }
    })
  }

  approvalRecord = (record, reviewStatus) => () => {
    const { reviewReason, tabIdx } = this.state
    if (reviewReason === 'system') {
      message.warn('不能输入此描述')
      return
    }
    let data = { tabIdx: Number(tabIdx), id: record.id, yy: record.yy, nickname: record.nickname, sid: record.sid, asid: record.asid, guildType: record.guildType, contractType: record.contractType, pcDevice: record.pcDevice, deviceOtherUid: record.deviceOtherUid, reviewReason: reviewReason, reviewStatus: reviewStatus }
    this.props.dispatch({
      type: approvalNewCompereURL,
      payload: data
    })
  }

  batchApprovalRecord = (exportKey, reviewStatus) => () => {
    const { batchReviewReason, tabIdx } = this.state
    if (exportKey === undefined || exportKey === null || exportKey.length === 0) {
      message.warn('请选择记录')
      return
    }
    console.log(exportKey)
    let data = []
    exportKey.forEach(function (record) {
      let one = { tabIdx: Number(tabIdx), id: record.id, yy: record.yy, nickname: record.nickname, sid: record.sid, asid: record.asid, guildType: record.guildType, contractType: record.contractType, pcDevice: record.pcDevice, deviceOtherUid: record.deviceOtherUid, reviewReason: batchReviewReason, reviewStatus: reviewStatus }
      data.push(one)
    })

    let param = { tabIdx: tabIdx, data: data, reviewStatus: reviewStatus, reviewReason: batchReviewReason }
    this.props.dispatch({
      type: batchApprovalNewCompereURL,
      payload: param
    })
    console.log(param)
    this.setState({ exportKey: [], selectedRowKeys: [], selectedRows: [] })
  }

  UpLoadOnChange = info => {
    if (info.file.status !== 'done') {
      return
    }
    if (info.file.response.status === 0) {
      message.success(`${info.file.name} uploaded successfully`)
    } else {
      message.error(info.file.response.msg)
    }
  }

  searchHandle = () => () => {
    const { tabIdx } = this.state
    this.refreshCompereInfo(tabIdx)
  }

  importHandle = () => () => {
    this.setState({ visible: true })
  }

  rowSelection1 = {
    onChange: (selectedRowKeys, selectedRows) => {
      this.setState({ exportKey1: selectedRows })
    }
  }

  rowSelection2 = {
    onChange: (selectedRowKeys, selectedRows) => {
      this.setState({ exportKey2: selectedRows })
    }
  }

  rowSelection3 = {
    onChange: (selectedRowKeys, selectedRows) => {
      this.setState({ exportKey3: selectedRows })
    }
  }

  rowSelection4 = {
    onChange: (selectedRowKeys, selectedRows) => {
      this.setState({ exportKey4: selectedRows })
    }
  }

  rowSelection5 = {
    onChange: (selectedRowKeys, selectedRows) => {
      this.setState({ exportKey5: selectedRows })
    }
  }

  rowSelection6 = {
    onChange: (selectedRowKeys, selectedRows) => {
      this.setState({ exportKey6: selectedRows })
    }
  }

  onExportHandle1 = () => () => {
    let headers = []
    let columns = this.columns
    const { exportKey1 } = this.state
    columns.forEach(function (item) {
      if (item.title !== '操作') {
        headers.push({ k: item.dataIndex, v: item.title })
      }
    })
    let list = []
    exportKey1.forEach(function (item) {
      let business = businessTypeMap[item.business]
      let guildType = guildTypeMap[item.business][item.guildType]
      let contractType = contractTypeMap[item.business][item.contractType]
      let inboundTime = moment.unix(item.inboundTime).format('YYYY-MM-DD')
      let isDeviceUpdate = yesNoBoolMap[item.isDeviceUpdate]
      let approvalStatus = approvalMap[item.approvalStatus]
      let reviewStatus = reviewMap[item.reviewStatus]

      let supportEndTime = ''
      if (item.supportEndTime > 0) {
        item.supportEndTime = Math.floor(item.supportEndTime / 100) * 100
        supportEndTime = moment.unix(item.supportEndTime - 1).format('YYYY-MM-DD') + ' 24点'
      }

      let supportStartTime = ''
      if (item.supportStartTime > 0) {
        item.supportStartTime = Math.floor(item.supportStartTime / 100) * 100
        supportStartTime = moment.unix(item.supportStartTime).format(dateFormat)
      }

      let one = { idx: item.idx, business: business, uid: item.uid, yy: item.yy, nickname: item.nickname, asid: item.asid, guildType: guildType, contractType: contractType, signTime: item.signTime, pcDevice: item.pcDevice, deviceOtherUidCount: item.deviceOtherUidCount, deviceOtherUidRealCount: item.deviceOtherUidRealCount, isDeviceUpdate: isDeviceUpdate, reviewStatus: reviewStatus, approvalStatus: approvalStatus, inboundTime: inboundTime, supportStartTime: supportStartTime, supportEndTime: supportEndTime, reviewReason: item.reviewReason }
      list.push(one)
    })
    exportExcel(headers, list, '新主持信息库.xlsx')
  }

  onExportHandle2 = () => () => {
    let headers = []
    let columns = this.columns
    const { exportKey2 } = this.state
    columns.forEach(function (item) {
      if (item.title !== '操作') {
        headers.push({ k: item.dataIndex, v: item.title })
      }
    })
    let list = []
    exportKey2.forEach(function (item) {
      let business = businessTypeMap[item.business]
      let guildType = guildTypeMap[item.business][item.guildType]
      let contractType = contractTypeMap[item.business][item.contractType]
      let inboundTime = moment.unix(item.inboundTime).format('YYYY-MM-DD')
      let isDeviceUpdate = yesNoBoolMap[item.isDeviceUpdate]
      let approvalStatus = approvalMap[item.approvalStatus]
      let reviewStatus = reviewMap[item.reviewStatus]

      let supportEndTime = ''
      if (item.supportEndTime > 0) {
        item.supportEndTime = Math.floor(item.supportEndTime / 100) * 100
        supportEndTime = moment.unix(item.supportEndTime - 1).format('YYYY-MM-DD') + ' 24点'
      }

      let supportStartTime = ''
      if (item.supportStartTime > 0) {
        item.supportStartTime = Math.floor(item.supportStartTime / 100) * 100
        supportStartTime = moment.unix(item.supportStartTime).format(dateFormat)
      }

      let one = { idx: item.idx, business: business, uid: item.uid, yy: item.yy, nickname: item.nickname, asid: item.asid, guildType: guildType, contractType: contractType, signTime: item.signTime, pcDevice: item.pcDevice, deviceOtherUidCount: item.deviceOtherUidCount, deviceOtherUidRealCount: item.deviceOtherUidRealCount, isDeviceUpdate: isDeviceUpdate, reviewStatus: reviewStatus, approvalStatus: approvalStatus, inboundTime: inboundTime, supportStartTime: supportStartTime, supportEndTime: supportEndTime, reviewReason: item.reviewReason }
      list.push(one)
    })
    exportExcel(headers, list, '新主持信息库.xlsx')
  }

  onExportHandle3 = () => () => {
    let headers = []
    let columns = this.columns
    const { exportKey3 } = this.state
    columns.forEach(function (item) {
      if (item.title !== '操作') {
        headers.push({ k: item.dataIndex, v: item.title })
      }
    })
    let list = []
    exportKey3.forEach(function (item) {
      let business = businessTypeMap[item.business]
      let guildType = guildTypeMap[item.business][item.guildType]
      let contractType = contractTypeMap[item.business][item.contractType]
      let inboundTime = moment.unix(item.inboundTime).format('YYYY-MM-DD')
      let isDeviceUpdate = yesNoBoolMap[item.isDeviceUpdate]
      let approvalStatus = approvalMap[item.approvalStatus]
      let reviewStatus = reviewMap[item.reviewStatus]

      let supportEndTime = ''
      if (item.supportEndTime > 0) {
        item.supportEndTime = Math.floor(item.supportEndTime / 100) * 100
        supportEndTime = moment.unix(item.supportEndTime - 1).format('YYYY-MM-DD') + ' 24点'
      }

      let supportStartTime = ''
      if (item.supportStartTime > 0) {
        item.supportStartTime = Math.floor(item.supportStartTime / 100) * 100
        supportStartTime = moment.unix(item.supportStartTime).format(dateFormat)
      }

      let one = { idx: item.idx, business: business, uid: item.uid, yy: item.yy, nickname: item.nickname, asid: item.asid, guildType: guildType, contractType: contractType, signTime: item.signTime, pcDevice: item.pcDevice, deviceOtherUidCount: item.deviceOtherUidCount, deviceOtherUidRealCount: item.deviceOtherUidRealCount, isDeviceUpdate: isDeviceUpdate, reviewStatus: reviewStatus, approvalStatus: approvalStatus, inboundTime: inboundTime, supportStartTime: supportStartTime, supportEndTime: supportEndTime, reviewReason: item.reviewReason }
      list.push(one)
    })
    exportExcel(headers, list, '新主持信息库.xlsx')
  }

  onExportHandle4 = () => () => {
    let headers = []
    let columns = this.columns
    const { exportKey4 } = this.state
    columns.forEach(function (item) {
      if (item.title !== '操作') {
        headers.push({ k: item.dataIndex, v: item.title })
      }
    })
    let list = []
    exportKey4.forEach(function (item) {
      let business = businessTypeMap[item.business]
      let guildType = guildTypeMap[item.business][item.guildType]
      let contractType = contractTypeMap[item.business][item.contractType]
      let inboundTime = moment.unix(item.inboundTime).format('YYYY-MM-DD')
      let isDeviceUpdate = yesNoBoolMap[item.isDeviceUpdate]
      let approvalStatus = approvalMap[item.approvalStatus]
      let reviewStatus = reviewMap[item.reviewStatus]

      let supportEndTime = ''
      if (item.supportEndTime > 0) {
        item.supportEndTime = Math.floor(item.supportEndTime / 100) * 100
        supportEndTime = moment.unix(item.supportEndTime - 1).format('YYYY-MM-DD') + ' 24点'
      }

      let supportStartTime = ''
      if (item.supportStartTime > 0) {
        item.supportStartTime = Math.floor(item.supportStartTime / 100) * 100
        supportStartTime = moment.unix(item.supportStartTime).format(dateFormat)
      }

      let one = { idx: item.idx, business: business, uid: item.uid, yy: item.yy, nickname: item.nickname, asid: item.asid, guildType: guildType, contractType: contractType, signTime: item.signTime, pcDevice: item.pcDevice, deviceOtherUidCount: item.deviceOtherUidCount, deviceOtherUidRealCount: item.deviceOtherUidRealCount, isDeviceUpdate: isDeviceUpdate, reviewStatus: reviewStatus, approvalStatus: approvalStatus, inboundTime: inboundTime, supportStartTime: supportStartTime, supportEndTime: supportEndTime, reviewReason: item.reviewReason }
      list.push(one)
    })
    exportExcel(headers, list, '新主持信息库.xlsx')
  }

  onExportHandle5 = () => () => {
    let headers = []
    let columns = this.columns
    const { exportKey5 } = this.state
    columns.forEach(function (item) {
      if (item.title !== '操作') {
        headers.push({ k: item.dataIndex, v: item.title })
      }
    })
    let list = []
    exportKey5.forEach(function (item) {
      let business = businessTypeMap[item.business]
      let guildType = guildTypeMap[item.business][item.guildType]
      let contractType = contractTypeMap[item.business][item.contractType]
      let inboundTime = moment.unix(item.inboundTime).format('YYYY-MM-DD')
      let isDeviceUpdate = yesNoBoolMap[item.isDeviceUpdate]
      let approvalStatus = approvalMap[item.approvalStatus]
      let reviewStatus = reviewMap[item.reviewStatus]

      let supportEndTime = ''
      if (item.supportEndTime > 0) {
        item.supportEndTime = Math.floor(item.supportEndTime / 100) * 100
        supportEndTime = moment.unix(item.supportEndTime - 1).format('YYYY-MM-DD') + ' 24点'
      }

      let supportStartTime = ''
      if (item.supportStartTime > 0) {
        item.supportStartTime = Math.floor(item.supportStartTime / 100) * 100
        supportStartTime = moment.unix(item.supportStartTime).format(dateFormat)
      }

      let one = { idx: item.idx, business: business, uid: item.uid, yy: item.yy, nickname: item.nickname, asid: item.asid, guildType: guildType, contractType: contractType, signTime: item.signTime, pcDevice: item.pcDevice, deviceOtherUidCount: item.deviceOtherUidCount, deviceOtherUidRealCount: item.deviceOtherUidRealCount, isDeviceUpdate: isDeviceUpdate, reviewStatus: reviewStatus, approvalStatus: approvalStatus, inboundTime: inboundTime, supportStartTime: supportStartTime, supportEndTime: supportEndTime, reviewReason: item.reviewReason }
      list.push(one)
    })
    exportExcel(headers, list, '新主持信息库.xlsx')
  }

  onExportHandle6 = () => () => {
    let headers = []
    let columns = this.columns
    const { exportKey6 } = this.state
    columns.forEach(function (item) {
      if (item.title !== '操作') {
        headers.push({ k: item.dataIndex, v: item.title })
      }
    })
    let list = []
    exportKey6.forEach(function (item) {
      let business = businessTypeMap[item.business]
      let guildType = guildTypeMap[item.business][item.guildType]
      let contractType = contractTypeMap[item.business][item.contractType]
      let inboundTime = moment.unix(item.inboundTime).format('YYYY-MM-DD')
      let isDeviceUpdate = yesNoBoolMap[item.isDeviceUpdate]
      let approvalStatus = approvalMap[item.approvalStatus]
      let reviewStatus = reviewMap[item.reviewStatus]

      let supportEndTime = ''
      if (item.supportEndTime > 0) {
        item.supportEndTime = Math.floor(item.supportEndTime / 100) * 100
        supportEndTime = moment.unix(item.supportEndTime - 1).format('YYYY-MM-DD') + ' 24点'
      }

      let supportStartTime = ''
      if (item.supportStartTime > 0) {
        item.supportStartTime = Math.floor(item.supportStartTime / 100) * 100
        supportStartTime = moment.unix(item.supportStartTime).format(dateFormat)
      }

      let one = { idx: item.idx, business: business, uid: item.uid, yy: item.yy, nickname: item.nickname, asid: item.asid, guildType: guildType, contractType: contractType, signTime: item.signTime, pcDevice: item.pcDevice, deviceOtherUidCount: item.deviceOtherUidCount, deviceOtherUidRealCount: item.deviceOtherUidRealCount, isDeviceUpdate: isDeviceUpdate, reviewStatus: reviewStatus, approvalStatus: approvalStatus, inboundTime: inboundTime, supportStartTime: supportStartTime, supportEndTime: supportEndTime, reviewReason: item.reviewReason }
      list.push(one)
    })
    exportExcel(headers, list, '新主持信息库.xlsx')
  }

  onExport = () => {
    const { model: { list } } = this.props

    let listNew = []
    list.forEach(function (item) {
      let business = businessTypeMap[item.business]
      let guildType = guildTypeMap[item.business][item.guildType]
      let contractType = contractTypeMap[item.business][item.contractType]
      let inboundTime = moment.unix(item.inboundTime).format('YYYY-MM-DD')
      let approvalStatus = approvalMap[item.approvalStatus]
      let reviewStatus = reviewMap[item.reviewStatus]
      let isDeviceUpdate = yesNoBoolMap[item.isDeviceUpdate]

      let supportEndTime = ''
      if (item.supportEndTime > 0) {
        item.supportEndTime = Math.floor(item.supportEndTime / 100) * 100
        supportEndTime = moment.unix(item.supportEndTime - 1).format('YYYY-MM-DD') + ' 24点'
      }

      let supportStartTime = ''
      if (item.supportStartTime > 0) {
        item.supportStartTime = Math.floor(item.supportStartTime / 100) * 100
        supportStartTime = moment.unix(item.supportStartTime).format(dateFormat)
      }

      let signTime = ''
      if (item.signStartTime > 0 && item.signEndTime > 0) {
        signTime = moment.unix(item.signStartTime).format(dateFormat) + '至' + moment.unix(item.signEndTime).format(dateFormat)
      }

      let one = { '序号': item.idx, '业务类型': business, 'UID': item.uid, 'YY号': item.yy, '昵称': item.nickname, '签约频道短位': item.asid, '公会身份': guildType, '主持签约身份': contractType, '签约时间': signTime, 'PC机器码': item.pcDevice, '机器码其他登录UID个数': item.deviceOtherUidCount, '机器码其他登录UID实名个数': item.deviceOtherUidRealCount, '机器码是否更新': isDeviceUpdate, '运营审核状态': reviewStatus, '业务审核状态': approvalStatus, '入库时间': inboundTime, '扶持开始时间': supportStartTime, '扶持结束时间': supportEndTime, '运营审核备注': item.reviewReason }
      listNew.push(one)
    })

    return listNew
  }

  hiddenModal = () => {
    this.setState({ visible: false })
  }

  handleCancel = e => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.hiddenModal()
  }

  handleSubmit = e => {
    if (this.formRef) {
      this.formRef.submit()
    }
    this.hiddenModal()
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onFinish = values => {
    const { dispatch } = this.props
    const { tabIdx } = this.state

    let fileURL = ''
    if (values && values.fileURL.file.response.urls[0]) {
      fileURL = values.fileURL.file.response.urls[0]
    }
    let data = { tabIdx: Number(tabIdx), notCheckWarehousing: true, importType: values.importType, business: values.business, fileUrl: fileURL }

    dispatch({
      type: importNewCompereURL,
      payload: data
    })

    this.hiddenModal()
    if (this.formRef) {
      this.formRef.resetFields()
    }
  }

  onTagChange = (idx) => {
    this.setState({ tabIdx: idx })
    if (idx === tabIdx1) {
      this.refreshCompereInfo(tabIdx1)
    } else if (idx === tabIdx2) {
      this.refreshCompereInfo(tabIdx2)
    } else if (idx === tabIdx3) {
      this.refreshCompereInfo(tabIdx3)
    } else if (idx === tabIdx4) {
      this.refreshCompereInfo(tabIdx4)
    } else if (idx === tabIdx5) {
      this.refreshCompereInfo(tabIdx5)
    } else if (idx === tabIdx6) {
      this.refreshCompereInfo(tabIdx6)
    }
  }

  // ============================================ //
  // 批量输入完成
  onBatchInputCompleteUID1 = () => {
    const { searchMultiUID1 } = this.state
    let tmpInput = searchMultiUID1.trim()
    if (tmpInput === '') {
      message.warn('数据为空, 请检查')
      return
    }
    if (tmpInput.indexOf(',') >= 0) {
      message.warn('输入格式不正确，请使用换行副隔开每个元素')
      return
    }
    let result = tmpInput.replaceAll('\n', ',')
    let tmpSlice = result.split(',')
    if (tmpSlice.length > 100 || result.length > 2000) { // 限制批量查询数量,确保不超出url长度限制
      message.warn('批量查询数据量过大，请检查输入')
      return
    }
    for (let i = 0; i < tmpSlice.length; i++) {
      if (!checkUid(tmpSlice[i])) {
        return
      }
    }
    if (this.inputRefUID1) { // 设置小输入框的值
      this.inputRefUID1.setValue(result)
    }
    console.log(result)
    this.setState({ searchUID1: result })
    this.setState({ modelVisableUID1: false }) // 隐藏模态框
  }

  // 批量输入完成
  onBatchInputCompleteYY1 = () => {
    const { searchMultiYY1 } = this.state
    let tmpInput = searchMultiYY1.trim()
    if (tmpInput === '') {
      message.warn('数据为空, 请检查')
      return
    }
    if (tmpInput.indexOf(',') >= 0) {
      message.warn('输入格式不正确，请使用换行副隔开每个元素')
      return
    }
    let result = tmpInput.replaceAll('\n', ',')
    let tmpSlice = result.split(',')
    if (tmpSlice.length > 100 || result.length > 2000) { // 限制批量查询数量,确保不超出url长度限制
      message.warn('批量查询数据量过大，请检查输入')
      return
    }
    if (this.inputRefYY1) { // 设置小输入框的值
      this.inputRefYY1.setValue(result)
    }
    console.log(result)
    this.setState({ searchYY1: result })
    this.setState({ modelVisableYY1: false }) // 隐藏模态框
  }

  // ============================================ //
 // 批量输入完成
 onBatchInputCompleteUID2 = () => {
   const { searchMultiUID2 } = this.state
   let tmpInput = searchMultiUID2.trim()
   if (tmpInput === '') {
     message.warn('数据为空, 请检查')
     return
   }
   if (tmpInput.indexOf(',') >= 0) {
     message.warn('输入格式不正确，请使用换行副隔开每个元素')
     return
   }
   let result = tmpInput.replaceAll('\n', ',')
   let tmpSlice = result.split(',')
   if (tmpSlice.length > 100 || result.length > 2000) { // 限制批量查询数量,确保不超出url长度限制
     message.warn('批量查询数据量过大，请检查输入')
     return
   }
   for (let i = 0; i < tmpSlice.length; i++) {
     if (!checkUid(tmpSlice[i])) {
       return
     }
   }
   if (this.inputRefUID2) { // 设置小输入框的值
     this.inputRefUID2.setValue(result)
   }
   console.log(result)
   this.setState({ searchUID2: result })
   this.setState({ modelVisableUID2: false }) // 隐藏模态框
 }

 // 批量输入完成
 onBatchInputCompleteYY2 = () => {
   const { searchMultiYY2 } = this.state
   let tmpInput = searchMultiYY2.trim()
   if (tmpInput === '') {
     message.warn('数据为空, 请检查')
     return
   }
   if (tmpInput.indexOf(',') >= 0) {
     message.warn('输入格式不正确，请使用换行副隔开每个元素')
     return
   }
   let result = tmpInput.replaceAll('\n', ',')
   let tmpSlice = result.split(',')
   if (tmpSlice.length > 100 || result.length > 2000) { // 限制批量查询数量,确保不超出url长度限制
     message.warn('批量查询数据量过大，请检查输入')
     return
   }
   if (this.inputRefYY2) { // 设置小输入框的值
     this.inputRefYY2.setValue(result)
   }
   console.log(result)
   this.setState({ searchYY2: result })
   this.setState({ modelVisableYY2: false }) // 隐藏模态框
 }

 // ============================================ //
 // 批量输入完成
 onBatchInputCompleteUID3 = () => {
   const { searchMultiUID3 } = this.state
   let tmpInput = searchMultiUID3.trim()
   if (tmpInput === '') {
     message.warn('数据为空, 请检查')
     return
   }
   if (tmpInput.indexOf(',') >= 0) {
     message.warn('输入格式不正确，请使用换行副隔开每个元素')
     return
   }
   let result = tmpInput.replaceAll('\n', ',')
   let tmpSlice = result.split(',')
   if (tmpSlice.length > 100 || result.length > 2000) { // 限制批量查询数量,确保不超出url长度限制
     message.warn('批量查询数据量过大，请检查输入')
     return
   }
   for (let i = 0; i < tmpSlice.length; i++) {
     if (!checkUid(tmpSlice[i])) {
       return
     }
   }
   if (this.inputRefUID3) { // 设置小输入框的值
     this.inputRefUID3.setValue(result)
   }
   console.log(result)
   this.setState({ searchUID3: result })
   this.setState({ modelVisableUID3: false }) // 隐藏模态框
 }

// 批量输入完成
onBatchInputCompleteYY3 = () => {
  const { searchMultiYY3 } = this.state
  let tmpInput = searchMultiYY3.trim()
  if (tmpInput === '') {
    message.warn('数据为空, 请检查')
    return
  }
  if (tmpInput.indexOf(',') >= 0) {
    message.warn('输入格式不正确，请使用换行副隔开每个元素')
    return
  }
  let result = tmpInput.replaceAll('\n', ',')
  let tmpSlice = result.split(',')
  if (tmpSlice.length > 100 || result.length > 2000) { // 限制批量查询数量,确保不超出url长度限制
    message.warn('批量查询数据量过大，请检查输入')
    return
  }
  if (this.inputRefYY3) { // 设置小输入框的值
    this.inputRefYY3.setValue(result)
  }
  console.log(result)
  this.setState({ searchYY3: result })
  this.setState({ modelVisableYY3: false }) // 隐藏模态框
}

 // ============================================ //
 // 批量输入完成
 onBatchInputCompleteUID4 = () => {
   const { searchMultiUID4 } = this.state
   let tmpInput = searchMultiUID4.trim()
   if (tmpInput === '') {
     message.warn('数据为空, 请检查')
     return
   }
   if (tmpInput.indexOf(',') >= 0) {
     message.warn('输入格式不正确，请使用换行副隔开每个元素')
     return
   }
   let result = tmpInput.replaceAll('\n', ',')
   let tmpSlice = result.split(',')
   if (tmpSlice.length > 100 || result.length > 2000) { // 限制批量查询数量,确保不超出url长度限制
     message.warn('批量查询数据量过大，请检查输入')
     return
   }
   for (let i = 0; i < tmpSlice.length; i++) {
     if (!checkUid(tmpSlice[i])) {
       return
     }
   }
   if (this.inputRefUID4) { // 设置小输入框的值
     this.inputRefUID4.setValue(result)
   }
   console.log(result)
   this.setState({ searchUID4: result })
   this.setState({ modelVisableUID4: false }) // 隐藏模态框
 }

 // 批量输入完成
 onBatchInputCompleteYY4 = () => {
   const { searchMultiYY4 } = this.state
   let tmpInput = searchMultiYY4.trim()
   if (tmpInput === '') {
     message.warn('数据为空, 请检查')
     return
   }
   if (tmpInput.indexOf(',') >= 0) {
     message.warn('输入格式不正确，请使用换行副隔开每个元素')
     return
   }
   let result = tmpInput.replaceAll('\n', ',')
   let tmpSlice = result.split(',')
   if (tmpSlice.length > 100 || result.length > 2000) { // 限制批量查询数量,确保不超出url长度限制
     message.warn('批量查询数据量过大，请检查输入')
     return
   }
   if (this.inputRefYY4) { // 设置小输入框的值
     this.inputRefYY4.setValue(result)
   }
   console.log(result)
   this.setState({ searchYY4: result })
   this.setState({ modelVisableYY4: false }) // 隐藏模态框
 }

 // ============================================ //
 // 批量输入完成
 onBatchInputCompleteUID5 = () => {
   const { searchMultiUID5 } = this.state
   let tmpInput = searchMultiUID5.trim()
   if (tmpInput === '') {
     message.warn('数据为空, 请检查')
     return
   }
   if (tmpInput.indexOf(',') >= 0) {
     message.warn('输入格式不正确，请使用换行副隔开每个元素')
     return
   }
   let result = tmpInput.replaceAll('\n', ',')
   let tmpSlice = result.split(',')
   if (tmpSlice.length > 100 || result.length > 2000) { // 限制批量查询数量,确保不超出url长度限制
     message.warn('批量查询数据量过大，请检查输入')
     return
   }
   for (let i = 0; i < tmpSlice.length; i++) {
     if (!checkUid(tmpSlice[i])) {
       return
     }
   }
   if (this.inputRefUID5) { // 设置小输入框的值
     this.inputRefUID5.setValue(result)
   }
   console.log(result)
   this.setState({ searchUID5: result })
   this.setState({ modelVisableUID5: false }) // 隐藏模态框
 }

 // 批量输入完成
 onBatchInputCompleteYY5 = () => {
   const { searchMultiYY5 } = this.state
   let tmpInput = searchMultiYY5.trim()
   if (tmpInput === '') {
     message.warn('数据为空, 请检查')
     return
   }
   if (tmpInput.indexOf(',') >= 0) {
     message.warn('输入格式不正确，请使用换行副隔开每个元素')
     return
   }
   let result = tmpInput.replaceAll('\n', ',')
   let tmpSlice = result.split(',')
   if (tmpSlice.length > 100 || result.length > 2000) { // 限制批量查询数量,确保不超出url长度限制
     message.warn('批量查询数据量过大，请检查输入')
     return
   }
   if (this.inputRefYY5) { // 设置小输入框的值
     this.inputRefYY5.setValue(result)
   }
   console.log(result)
   this.setState({ searchYY5: result })
   this.setState({ modelVisableYY5: false }) // 隐藏模态框
 }

 // ============================================ //
 // 批量输入完成
 onBatchInputCompleteUID6 = () => {
   const { searchMultiUID6 } = this.state
   let tmpInput = searchMultiUID6.trim()
   if (tmpInput === '') {
     message.warn('数据为空, 请检查')
     return
   }
   if (tmpInput.indexOf(',') >= 0) {
     message.warn('输入格式不正确，请使用换行副隔开每个元素')
     return
   }
   let result = tmpInput.replaceAll('\n', ',')
   let tmpSlice = result.split(',')
   if (tmpSlice.length > 100 || result.length > 2000) { // 限制批量查询数量,确保不超出url长度限制
     message.warn('批量查询数据量过大，请检查输入')
     return
   }
   for (let i = 0; i < tmpSlice.length; i++) {
     if (!checkUid(tmpSlice[i])) {
       return
     }
   }
   if (this.inputRefUID6) { // 设置小输入框的值
     this.inputRefUID6.setValue(result)
   }
   console.log(result)
   this.setState({ searchUID6: result })
   this.setState({ modelVisableUID6: false }) // 隐藏模态框
 }

 // 批量输入完成
 onBatchInputCompleteYY6 = () => {
   const { searchMultiYY6 } = this.state
   let tmpInput = searchMultiYY6.trim()
   if (tmpInput === '') {
     message.warn('数据为空, 请检查')
     return
   }
   if (tmpInput.indexOf(',') >= 0) {
     message.warn('输入格式不正确，请使用换行副隔开每个元素')
     return
   }
   let result = tmpInput.replaceAll('\n', ',')
   let tmpSlice = result.split(',')
   if (tmpSlice.length > 100 || result.length > 2000) { // 限制批量查询数量,确保不超出url长度限制
     message.warn('批量查询数据量过大，请检查输入')
     return
   }
   if (this.inputRefYY6) { // 设置小输入框的值
     this.inputRefYY6.setValue(result)
   }
   console.log(result)
   this.setState({ searchYY6: result })
   this.setState({ modelVisableYY6: false }) // 隐藏模态框
 }

  // 待审核
  tableHTML1 = () => {
    const { model: { list } } = this.props
    return <div><span>业务</span>
      <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchBusiness1: v })} allowClear>
        <Option key={1} value={businessTypeJY}>交友</Option>
        <Option key={2} value={businessTypePK}>约战</Option>
        <Option key={3} value={businessTypeBABY}>宝贝</Option>
      </Select>

      <Tooltip title='批量输入请双击'>
        <span style={{ marginLeft: 15 }}>UID</span>
        <Input
          allowClear
          ref={(input) => { this.inputRefUID1 = input }}
          style={{ width: 120, marginLeft: 3 }}
          placeholder={'输入UID'}
          onChange={(e) => { this.setState({ searchUID1: e.target.value }) }}
          onDoubleClick={(e) => { this.setState({ modelVisableUID1: true }) }}
        />
      </Tooltip>
      <Tooltip title='批量输入请双击'>
        <span style={{ marginLeft: 15 }}>YY号</span>
        <Input
          allowClear
          ref={(input) => { this.inputRefYY1 = input }}
          style={{ width: 120, marginLeft: 3 }}
          placeholder={'输入YY'}
          onChange={(e) => { this.setState({ searchYY1: e.target.value }) }}
          onDoubleClick={(e) => { this.setState({ modelVisableYY1: true }) }}
        />
      </Tooltip>

      <span style={{ marginLeft: 15 }}>昵称</span>
      <Input placeholder='请输入' onChange={e => this.setState({ searchNick1: e.target.value })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>签约短位ID</span>
      <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID1: e })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>公会身份</span>
      <TreeSelect showSearch style={{ marginLeft: 5, width: 150 }} placeholder='全部' allowClear treeDefaultExpandAll onChange={(v) => this.setState({ searchGuildType1: v })}>
        <TreeNode key={11} value={0} title='全部' />
        <TreeNode key={12} value={businessTypeJY + 10} title='交友'>
          <TreeNode key={13} value={guildTypeYY} title='普通公会' />
          <TreeNode key={14} value={guildTypeShuiJing} title='水晶公会' />
          <TreeNode key={15} value={guildTypeSuperShuiJing} title='超级水晶公会' />
        </TreeNode>
        <TreeNode key={16} value={businessTypePK + 10} title='约战'>
          <TreeNode key={17} value={guildTypeAceGuild} title='王牌公会' />
          <TreeNode key={18} value={guildTypeNormalGuild} title='普通公会' />
        </TreeNode>
        <TreeNode key={19} value={businessTypeBABY + 10} title='宝贝'>
          <TreeNode key={20} value={guildTypeAceGuild} title='王牌公会' />
          <TreeNode key={21} value={guildTypeNormalGuild} title='普通公会' />
        </TreeNode>
      </TreeSelect>
      <span style={{ marginLeft: 15 }}>主持身份</span>
      <TreeSelect showSearch style={{ marginLeft: 5, width: 150 }} placeholder='全部' allowClear treeDefaultExpandAll onChange={(v) => this.setState({ searchContractType1: v })}>
        <TreeNode key={11} value={0} title='全部' />
        <TreeNode key={12} value={businessTypeJY + 10} title='交友'>
          <TreeNode key={13} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={14} value={contractTypeSuper} title='超级主持' />
          <TreeNode key={16} value={contractTypeHatSuper} title='帽子超级' />
        </TreeNode>
        <TreeNode key={17} value={businessTypePK + 10} title='约战'>
          <TreeNode key={18} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={19} value={contractTypeHatAce} title='王牌主播' />
        </TreeNode>
        <TreeNode key={20} value={businessTypeBABY + 10} title='宝贝'>
          <TreeNode key={21} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={22} value={contractTypeHatAce} title='王牌主播' />
        </TreeNode>
      </TreeSelect>
      <div style={{ marginBottom: 10 }} />
      <span>签约时间</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='开始时间'
        onChange={(v) => this.setState({ searchSignStartTime1: v })}
        style={{ marginLeft: 10 }}
      />
      <span style={{ marginLeft: 3 }}>~</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='结束时间'
        onChange={(v) => this.setState({ searchSignEndTime1: v })}
        style={{ marginLeft: 3 }}
      />
      <span style={{ marginLeft: 15 }}>入库时间</span>
      <DatePicker onChange={(v) => this.setState({ searchInboundTime1: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>扶持开始时间</span>
      <DatePicker onChange={(v) => this.setState({ searchSupportStartTime1: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>扶持结束时间</span>
      <DatePicker onChange={(v) => this.setState({ searchSupportEndTime1: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>业务审核状态</span>
      <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchApprovalStatus1: v })} allowClear>
        <Option key={1} value={approvalPass}>通过</Option>
        <Option key={2} value={approvalReject}>不通过</Option>
        <Option key={2} value={approvalFirstGoing}>一审中</Option>
        <Option key={2} value={approvalSecondGoing}>二审中</Option>
      </Select>
      <div style={{ marginBottom: 10 }} />
      <span>机器码是否更新</span>
      <Select style={{ marginLeft: 5, width: 70 }} placeholder='全部' onChange={(v) => this.setState({ searchApprovalStatus1: v })} allowClear>
        <Option key={0} value={yesBoolStatus}>是</Option>
        <Option key={1} value={noBoolStatus}>否</Option>
      </Select>
      <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>

      <Popover content={this.batchRenderApprovalWaitPage(reviewPass)} trigger='click'><Button style={{ marginLeft: 40 }} type='primary'>批量通过</Button></Popover>
      <Popover content={this.batchRenderApprovalWaitPage(reviewReject)} trigger='click'><Button style={{ marginLeft: 10 }} type='primary'>批量不通过</Button></Popover>
      <Popover content={this.batchRenderApprovalWaitPage(reviewSuspected)} trigger='click'><Button style={{ marginLeft: 10 }} type='primary'>批量疑似</Button></Popover>

      <Button style={{ marginLeft: 60 }} type='primary' onClick={this.importHandle()}>导入新主持</Button>
      <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onExportHandle1()}>选择导出名单</Button>
      <CSVLink data={this.onExport()} filename='新主持信息库.csv' target='_blank'><Button type='primary' style={{ marginLeft: 20 }}>导出全量名单</Button></CSVLink>
      <div>
        <div><font>注释：</font></div>
        <div><font>1、入库规则：</font></div>
        <div><font>①UID完成线上普通签约</font></div>
        <div><font>②UID已完成实名认证</font></div>
        <div><font>③UID对应实名身份证下，三业务均无其他产生签约关系UID</font></div>
        <div><font>④运营及业务审批通过</font></div>
        <div><font>2、出库：人工出库</font></div>
        <div><font>3、扶持结束tab：主持解约/扶持时间结束，名单归到扶持结束tab</font></div>
        <div><font>4、表格排序：按照入库时间从最新到最久</font></div>
        <div><font>5、机器码更新：每周三及每周日24点，对【待审核/审核通过/疑似】状态主持，判断信息库内PC开播机器码，与主持最近开播机器码，是否发生变化。变化显示“是”，并更新机器码。无变化显示“否”</font></div>
      </div>
      <Table tabelLayout='fixed' rowSelection={this.rowSelection1} rowKey='idx' dataSource={list} columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
    </div>
  }

  // 疑似
  tableHTML2 = () => {
    const { model: { list } } = this.props
    return <div><span>业务</span>
      <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchBusiness2: v })} allowClear>
        <Option key={1} value={businessTypeJY}>交友</Option>
        <Option key={2} value={businessTypePK}>约战</Option>
        <Option key={3} value={businessTypeBABY}>宝贝</Option>
      </Select>

      <Tooltip title='批量输入请双击'>
        <span style={{ marginLeft: 15 }}>UID</span>
        <Input
          allowClear
          ref={(input) => { this.inputRefUID2 = input }}
          style={{ width: 120, marginLeft: 3 }}
          placeholder={'输入UID'}
          onChange={(e) => { this.setState({ searchUID2: e.target.value }) }}
          onDoubleClick={(e) => { this.setState({ modelVisableUID2: true }) }}
        />
      </Tooltip>
      <Tooltip title='批量输入请双击'>
        <span style={{ marginLeft: 15 }}>YY号</span>
        <Input
          allowClear
          ref={(input) => { this.inputRefYY2 = input }}
          style={{ width: 120, marginLeft: 3 }}
          placeholder={'输入YY'}
          onChange={(e) => { this.setState({ searchYY2: e.target.value }) }}
          onDoubleClick={(e) => { this.setState({ modelVisableYY2: true }) }}
        />
      </Tooltip>

      <span style={{ marginLeft: 15 }}>昵称</span>
      <Input placeholder='请输入' onChange={e => this.setState({ searchNick2: e.target.value })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>签约短位ID</span>
      <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID2: e })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>公会身份</span>
      <TreeSelect showSearch style={{ marginLeft: 5, width: 150 }} placeholder='全部' allowClear treeDefaultExpandAll onChange={(v) => this.setState({ searchGuildType2: v })}>
        <TreeNode key={11} value={0} title='全部' />
        <TreeNode key={12} value={businessTypeJY + 10} title='交友'>
          <TreeNode key={13} value={guildTypeYY} title='普通公会' />
          <TreeNode key={14} value={guildTypeShuiJing} title='水晶公会' />
          <TreeNode key={15} value={guildTypeSuperShuiJing} title='超级水晶公会' />
        </TreeNode>
        <TreeNode key={16} value={businessTypePK + 10} title='约战'>
          <TreeNode key={17} value={guildTypeAceGuild} title='王牌公会' />
          <TreeNode key={18} value={guildTypeNormalGuild} title='普通公会' />
        </TreeNode>
        <TreeNode key={19} value={businessTypeBABY + 10} title='宝贝'>
          <TreeNode key={20} value={guildTypeAceGuild} title='王牌公会' />
          <TreeNode key={21} value={guildTypeNormalGuild} title='普通公会' />
        </TreeNode>
      </TreeSelect>
      <span style={{ marginLeft: 15 }}>主持身份</span>
      <TreeSelect showSearch style={{ marginLeft: 5, width: 150 }} placeholder='全部' allowClear treeDefaultExpandAll onChange={(v) => this.setState({ searchContractType2: v })}>
        <TreeNode key={11} value={0} title='全部' />
        <TreeNode key={12} value={businessTypeJY + 10} title='交友'>
          <TreeNode key={13} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={14} value={contractTypeSuper} title='超级主持' />
          <TreeNode key={16} value={contractTypeHatSuper} title='帽子超级' />
        </TreeNode>
        <TreeNode key={17} value={businessTypePK + 10} title='约战'>
          <TreeNode key={18} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={19} value={contractTypeHatAce} title='王牌主播' />
        </TreeNode>
        <TreeNode key={20} value={businessTypeBABY + 10} title='宝贝'>
          <TreeNode key={21} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={22} value={contractTypeHatAce} title='王牌主播' />
        </TreeNode>
      </TreeSelect>
      <div style={{ marginBottom: 10 }} />
      <span>签约时间</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='开始时间'
        onChange={(v) => this.setState({ searchSignStartTime2: v })}
        style={{ marginLeft: 10 }}
      />
      <span style={{ marginLeft: 3 }}>~</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='结束时间'
        onChange={(v) => this.setState({ searchSignEndTime2: v })}
        style={{ marginLeft: 3 }}
      />
      <span style={{ marginLeft: 15 }}>入库时间</span>
      <DatePicker onChange={(v) => this.setState({ searchInboundTime2: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>扶持开始时间</span>
      <DatePicker onChange={(v) => this.setState({ searchSupportStartTime2: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>扶持结束时间</span>
      <DatePicker onChange={(v) => this.setState({ searchSupportEndTime2: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>业务审核状态</span>
      <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchApprovalStatus2: v })} allowClear>
        <Option key={1} value={approvalPass}>通过</Option>
        <Option key={2} value={approvalReject}>不通过</Option>
        <Option key={2} value={approvalFirstGoing}>一审中</Option>
        <Option key={2} value={approvalSecondGoing}>二审中</Option>
      </Select>
      <div style={{ marginBottom: 10 }} />
      <span>机器码是否更新</span>
      <Select style={{ marginLeft: 5, width: 70 }} placeholder='全部' onChange={(v) => this.setState({ searchApprovalStatus2: v })} allowClear>
        <Option key={0} value={yesBoolStatus}>是</Option>
        <Option key={1} value={noBoolStatus}>否</Option>
      </Select>
      <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>

      <Popover content={this.batchRenderApprovalSuspectedPage(reviewPass)} trigger='click'><Button style={{ marginLeft: 40 }} type='primary'>批量通过</Button></Popover>
      <Popover content={this.batchRenderApprovalSuspectedPage(reviewReject)} trigger='click'><Button style={{ marginLeft: 10 }} type='primary'>批量不通过</Button></Popover>

      <Button style={{ marginLeft: 60 }} type='primary' onClick={this.importHandle()}>导入新主持</Button>
      <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onExportHandle2()}>选择导出名单</Button>
      <CSVLink data={this.onExport()} filename='新主持信息库.csv' target='_blank'><Button type='primary' style={{ marginLeft: 20 }}>导出全量名单</Button></CSVLink>
      <div>
        <div><font>注释：</font></div>
        <div><font>1、入库规则：</font></div>
        <div><font>①UID完成线上普通签约</font></div>
        <div><font>②UID已完成实名认证</font></div>
        <div><font>③UID对应实名身份证下，三业务均无其他产生签约关系UID</font></div>
        <div><font>④运营及业务审批通过</font></div>
        <div><font>2、出库：人工出库</font></div>
        <div><font>3、扶持结束tab：主持解约/扶持时间结束，名单归到扶持结束tab</font></div>
        <div><font>4、表格排序：按照入库时间从最新到最久</font></div>
        <div><font>5、机器码更新：每周三及每周日24点，对【待审核/审核通过/疑似】状态主持，判断信息库内PC开播机器码，与主持最近开播机器码，是否发生变化。变化显示“是”，并更新机器码。无变化显示“否”</font></div>
      </div>
      <Table tabelLayout='fixed' rowSelection={this.rowSelection2} rowKey='idx' dataSource={list} columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
    </div>
  }

  // 审核中
  tableHTML3 = () => {
    const { model: { list } } = this.props
    return <div><span>业务</span>
      <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchBusiness3: v })} allowClear>
        <Option key={1} value={businessTypeJY}>交友</Option>
        <Option key={2} value={businessTypePK}>约战</Option>
        <Option key={3} value={businessTypeBABY}>宝贝</Option>
      </Select>

      <Tooltip title='批量输入请双击'>
        <span style={{ marginLeft: 15 }}>UID</span>
        <Input
          allowClear
          ref={(input) => { this.inputRefUID3 = input }}
          style={{ width: 120, marginLeft: 3 }}
          placeholder={'输入UID'}
          onChange={(e) => { this.setState({ searchUID3: e.target.value }) }}
          onDoubleClick={(e) => { this.setState({ modelVisableUID3: true }) }}
        />
      </Tooltip>
      <Tooltip title='批量输入请双击'>
        <span style={{ marginLeft: 15 }}>YY号</span>
        <Input
          allowClear
          ref={(input) => { this.inputRefYY3 = input }}
          style={{ width: 120, marginLeft: 3 }}
          placeholder={'输入YY'}
          onChange={(e) => { this.setState({ searchYY3: e.target.value }) }}
          onDoubleClick={(e) => { this.setState({ modelVisableYY3: true }) }}
        />
      </Tooltip>

      <span style={{ marginLeft: 15 }}>昵称</span>
      <Input placeholder='请输入' onChange={e => this.setState({ searchNick3: e.target.value })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>签约短位ID</span>
      <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID3: e })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>公会身份</span>
      <TreeSelect showSearch style={{ marginLeft: 5, width: 150 }} placeholder='全部' allowClear treeDefaultExpandAll onChange={(v) => this.setState({ searchGuildType3: v })}>
        <TreeNode key={11} value={0} title='全部' />
        <TreeNode key={12} value={businessTypeJY + 10} title='交友'>
          <TreeNode key={13} value={guildTypeYY} title='普通公会' />
          <TreeNode key={14} value={guildTypeShuiJing} title='水晶公会' />
          <TreeNode key={15} value={guildTypeSuperShuiJing} title='超级水晶公会' />
        </TreeNode>
        <TreeNode key={16} value={businessTypePK + 10} title='约战'>
          <TreeNode key={17} value={guildTypeAceGuild} title='王牌公会' />
          <TreeNode key={18} value={guildTypeNormalGuild} title='普通公会' />
        </TreeNode>
        <TreeNode key={19} value={businessTypeBABY + 10} title='宝贝'>
          <TreeNode key={20} value={guildTypeAceGuild} title='王牌公会' />
          <TreeNode key={21} value={guildTypeNormalGuild} title='普通公会' />
        </TreeNode>
      </TreeSelect>
      <span style={{ marginLeft: 15 }}>主持身份</span>
      <TreeSelect showSearch style={{ marginLeft: 5, width: 150 }} placeholder='全部' allowClear treeDefaultExpandAll onChange={(v) => this.setState({ searchContractType3: v })}>
        <TreeNode key={11} value={0} title='全部' />
        <TreeNode key={12} value={businessTypeJY + 10} title='交友'>
          <TreeNode key={13} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={14} value={contractTypeSuper} title='超级主持' />
          <TreeNode key={16} value={contractTypeHatSuper} title='帽子超级' />
        </TreeNode>
        <TreeNode key={17} value={businessTypePK + 10} title='约战'>
          <TreeNode key={18} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={19} value={contractTypeHatAce} title='王牌主播' />
        </TreeNode>
        <TreeNode key={20} value={businessTypeBABY + 10} title='宝贝'>
          <TreeNode key={21} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={22} value={contractTypeHatAce} title='王牌主播' />
        </TreeNode>
      </TreeSelect>
      <div style={{ marginBottom: 10 }} />
      <span>签约时间</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='开始时间'
        onChange={(v) => this.setState({ searchSignStartTime3: v })}
        style={{ marginLeft: 10 }}
      />
      <span style={{ marginLeft: 3 }}>~</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='结束时间'
        onChange={(v) => this.setState({ searchSignEndTime3: v })}
        style={{ marginLeft: 3 }}
      />
      <span style={{ marginLeft: 15 }}>入库时间</span>
      <DatePicker onChange={(v) => this.setState({ searchInboundTime3: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>扶持开始时间</span>
      <DatePicker onChange={(v) => this.setState({ searchSupportStartTime3: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>扶持结束时间</span>
      <DatePicker onChange={(v) => this.setState({ searchSupportEndTime3: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>业务审核状态</span>
      <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchApprovalStatus3: v })} allowClear>
        <Option key={1} value={approvalPass}>通过</Option>
        <Option key={2} value={approvalReject}>不通过</Option>
        <Option key={2} value={approvalFirstGoing}>一审中</Option>
        <Option key={2} value={approvalSecondGoing}>二审中</Option>
      </Select>
      <div style={{ marginBottom: 10 }} />
      <span>机器码是否更新</span>
      <Select style={{ marginLeft: 5, width: 70 }} placeholder='全部' onChange={(v) => this.setState({ searchApprovalStatus3: v })} allowClear>
        <Option key={0} value={yesBoolStatus}>是</Option>
        <Option key={1} value={noBoolStatus}>否</Option>
      </Select>
      <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
      <Button style={{ marginLeft: 60 }} type='primary' onClick={this.importHandle()}>导入新主持</Button>
      <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onExportHandle3()}>选择导出名单</Button>
      <CSVLink data={this.onExport()} filename='新主持信息库.csv' target='_blank'><Button type='primary' style={{ marginLeft: 20 }}>导出全量名单</Button></CSVLink>
      <div>
        <div><font>注释：</font></div>
        <div><font>1、入库规则：</font></div>
        <div><font>①UID完成线上普通签约</font></div>
        <div><font>②UID已完成实名认证</font></div>
        <div><font>③UID对应实名身份证下，三业务均无其他产生签约关系UID</font></div>
        <div><font>④运营及业务审批通过</font></div>
        <div><font>2、出库：人工出库</font></div>
        <div><font>3、扶持结束tab：主持解约/扶持时间结束，名单归到扶持结束tab</font></div>
        <div><font>4、表格排序：按照入库时间从最新到最久</font></div>
        <div><font>5、机器码更新：每周三及每周日24点，对【待审核/审核通过/疑似】状态主持，判断信息库内PC开播机器码，与主持最近开播机器码，是否发生变化。变化显示“是”，并更新机器码。无变化显示“否”</font></div>
      </div>
      <Table tabelLayout='fixed' rowSelection={this.rowSelection3} rowKey='idx' dataSource={list} columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
    </div>
  }

  // 审核通过
  tableHTML4 = () => {
    const { model: { list } } = this.props
    return <div><span>业务</span>
      <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchBusiness4: v })} allowClear>
        <Option key={1} value={businessTypeJY}>交友</Option>
        <Option key={2} value={businessTypePK}>约战</Option>
        <Option key={3} value={businessTypeBABY}>宝贝</Option>
      </Select>

      <Tooltip title='批量输入请双击'>
        <span style={{ marginLeft: 15 }}>UID</span>
        <Input
          allowClear
          ref={(input) => { this.inputRefUID4 = input }}
          style={{ width: 120, marginLeft: 3 }}
          placeholder={'输入UID'}
          onChange={(e) => { this.setState({ searchUID4: e.target.value }) }}
          onDoubleClick={(e) => { this.setState({ modelVisableUID4: true }) }}
        />
      </Tooltip>
      <Tooltip title='批量输入请双击'>
        <span style={{ marginLeft: 15 }}>YY号</span>
        <Input
          allowClear
          ref={(input) => { this.inputRefYY4 = input }}
          style={{ width: 120, marginLeft: 3 }}
          placeholder={'输入YY'}
          onChange={(e) => { this.setState({ searchYY4: e.target.value }) }}
          onDoubleClick={(e) => { this.setState({ modelVisableYY4: true }) }}
        />
      </Tooltip>

      <span style={{ marginLeft: 15 }}>昵称</span>
      <Input placeholder='请输入' onChange={e => this.setState({ searchNick4: e.target.value })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>签约短位ID</span>
      <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID4: e })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>公会身份</span>
      <TreeSelect showSearch style={{ marginLeft: 5, width: 150 }} placeholder='全部' allowClear treeDefaultExpandAll onChange={(v) => this.setState({ searchGuildType4: v })}>
        <TreeNode key={11} value={0} title='全部' />
        <TreeNode key={12} value={businessTypeJY + 10} title='交友'>
          <TreeNode key={13} value={guildTypeYY} title='普通公会' />
          <TreeNode key={14} value={guildTypeShuiJing} title='水晶公会' />
          <TreeNode key={15} value={guildTypeSuperShuiJing} title='超级水晶公会' />
        </TreeNode>
        <TreeNode key={16} value={businessTypePK + 10} title='约战'>
          <TreeNode key={17} value={guildTypeAceGuild} title='王牌公会' />
          <TreeNode key={18} value={guildTypeNormalGuild} title='普通公会' />
        </TreeNode>
        <TreeNode key={19} value={businessTypeBABY + 10} title='宝贝'>
          <TreeNode key={20} value={guildTypeAceGuild} title='王牌公会' />
          <TreeNode key={21} value={guildTypeNormalGuild} title='普通公会' />
        </TreeNode>
      </TreeSelect>
      <span style={{ marginLeft: 15 }}>主持身份</span>
      <TreeSelect showSearch style={{ marginLeft: 5, width: 150 }} placeholder='全部' allowClear treeDefaultExpandAll onChange={(v) => this.setState({ searchContractType4: v })}>
        <TreeNode key={11} value={0} title='全部' />
        <TreeNode key={12} value={businessTypeJY + 10} title='交友'>
          <TreeNode key={13} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={14} value={contractTypeSuper} title='超级主持' />
          <TreeNode key={16} value={contractTypeHatSuper} title='帽子超级' />
        </TreeNode>
        <TreeNode key={17} value={businessTypePK + 10} title='约战'>
          <TreeNode key={18} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={19} value={contractTypeHatAce} title='王牌主播' />
        </TreeNode>
        <TreeNode key={20} value={businessTypeBABY + 10} title='宝贝'>
          <TreeNode key={21} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={22} value={contractTypeHatAce} title='王牌主播' />
        </TreeNode>
      </TreeSelect>
      <div style={{ marginBottom: 10 }} />
      <span>签约时间</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='开始时间'
        onChange={(v) => this.setState({ searchSignStartTime4: v })}
        style={{ marginLeft: 10 }}
      />
      <span style={{ marginLeft: 3 }}>~</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='结束时间'
        onChange={(v) => this.setState({ searchSignEndTime4: v })}
        style={{ marginLeft: 3 }}
      />
      <span style={{ marginLeft: 15 }}>入库时间</span>
      <DatePicker onChange={(v) => this.setState({ searchInboundTime4: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>扶持开始时间</span>
      <DatePicker onChange={(v) => this.setState({ searchSupportStartTime4: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>扶持结束时间</span>
      <DatePicker onChange={(v) => this.setState({ searchSupportEndTime4: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>业务审核状态</span>
      <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchApprovalStatus4: v })} allowClear>
        <Option key={1} value={approvalPass}>通过</Option>
        <Option key={2} value={approvalReject}>不通过</Option>
        <Option key={2} value={approvalFirstGoing}>一审中</Option>
        <Option key={2} value={approvalSecondGoing}>二审中</Option>
      </Select>
      <div style={{ marginBottom: 10 }} />
      <span>机器码是否更新</span>
      <Select style={{ marginLeft: 5, width: 70 }} placeholder='全部' onChange={(v) => this.setState({ searchApprovalStatus4: v })} allowClear>
        <Option key={0} value={yesBoolStatus}>是</Option>
        <Option key={1} value={noBoolStatus}>否</Option>
      </Select>
      <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>

      <Popover content={this.batchRenderApprovalPassPage(reviewReject)} trigger='click'><Button style={{ marginLeft: 40 }} type='primary'>批量不通过</Button></Popover>

      <Button style={{ marginLeft: 60 }} type='primary' onClick={this.importHandle()}>导入新主持</Button>
      <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onExportHandle4()}>选择导出名单</Button>
      <CSVLink data={this.onExport()} filename='新主持信息库.csv' target='_blank'><Button type='primary' style={{ marginLeft: 20 }}>导出全量名单</Button></CSVLink>
      <div>
        <div><font>注释：</font></div>
        <div><font>1、入库规则：</font></div>
        <div><font>①UID完成线上普通签约</font></div>
        <div><font>②UID已完成实名认证</font></div>
        <div><font>③UID对应实名身份证下，三业务均无其他产生签约关系UID</font></div>
        <div><font>④运营及业务审批通过</font></div>
        <div><font>2、出库：人工出库</font></div>
        <div><font>3、扶持结束tab：主持解约/扶持时间结束，名单归到扶持结束tab</font></div>
        <div><font>4、表格排序：按照入库时间从最新到最久</font></div>
        <div><font>5、机器码更新：每周三及每周日24点，对【待审核/审核通过/疑似】状态主持，判断信息库内PC开播机器码，与主持最近开播机器码，是否发生变化。变化显示“是”，并更新机器码。无变化显示“否”</font></div>
      </div>
      <Table tabelLayout='fixed' rowSelection={this.rowSelection4} rowKey='idx' dataSource={list} columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
    </div>
  }

  // 审核不通过
  tableHTML5 = () => {
    const { model: { list } } = this.props
    return <div><span>业务</span>
      <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchBusiness5: v })} allowClear>
        <Option key={1} value={businessTypeJY}>交友</Option>
        <Option key={2} value={businessTypePK}>约战</Option>
        <Option key={3} value={businessTypeBABY}>宝贝</Option>
      </Select>

      <Tooltip title='批量输入请双击'>
        <span style={{ marginLeft: 15 }}>UID</span>
        <Input
          allowClear
          ref={(input) => { this.inputRefUID5 = input }}
          style={{ width: 120, marginLeft: 3 }}
          placeholder={'输入UID'}
          onChange={(e) => { this.setState({ searchUID5: e.target.value }) }}
          onDoubleClick={(e) => { this.setState({ modelVisableUID5: true }) }}
        />
      </Tooltip>
      <Tooltip title='批量输入请双击'>
        <span style={{ marginLeft: 15 }}>YY号</span>
        <Input
          allowClear
          ref={(input) => { this.inputRefYY5 = input }}
          style={{ width: 120, marginLeft: 3 }}
          placeholder={'输入YY'}
          onChange={(e) => { this.setState({ searchYY5: e.target.value }) }}
          onDoubleClick={(e) => { this.setState({ modelVisableYY5: true }) }}
        />
      </Tooltip>

      <span style={{ marginLeft: 15 }}>昵称</span>
      <Input placeholder='请输入' onChange={e => this.setState({ searchNick5: e.target.value })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>签约短位ID</span>
      <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID5: e })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>公会身份</span>
      <TreeSelect showSearch style={{ marginLeft: 5, width: 150 }} placeholder='全部' allowClear treeDefaultExpandAll onChange={(v) => this.setState({ searchGuildType5: v })}>
        <TreeNode key={11} value={0} title='全部' />
        <TreeNode key={12} value={businessTypeJY + 10} title='交友'>
          <TreeNode key={13} value={guildTypeYY} title='普通公会' />
          <TreeNode key={14} value={guildTypeShuiJing} title='水晶公会' />
          <TreeNode key={15} value={guildTypeSuperShuiJing} title='超级水晶公会' />
        </TreeNode>
        <TreeNode key={16} value={businessTypePK + 10} title='约战'>
          <TreeNode key={17} value={guildTypeAceGuild} title='王牌公会' />
          <TreeNode key={18} value={guildTypeNormalGuild} title='普通公会' />
        </TreeNode>
        <TreeNode key={19} value={businessTypeBABY + 10} title='宝贝'>
          <TreeNode key={20} value={guildTypeAceGuild} title='王牌公会' />
          <TreeNode key={21} value={guildTypeNormalGuild} title='普通公会' />
        </TreeNode>
      </TreeSelect>
      <span style={{ marginLeft: 15 }}>主持身份</span>
      <TreeSelect showSearch style={{ marginLeft: 5, width: 150 }} placeholder='全部' allowClear treeDefaultExpandAll onChange={(v) => this.setState({ searchContractType5: v })}>
        <TreeNode key={11} value={0} title='全部' />
        <TreeNode key={12} value={businessTypeJY + 10} title='交友'>
          <TreeNode key={13} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={14} value={contractTypeSuper} title='超级主持' />
          <TreeNode key={16} value={contractTypeHatSuper} title='帽子超级' />
        </TreeNode>
        <TreeNode key={17} value={businessTypePK + 10} title='约战'>
          <TreeNode key={18} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={19} value={contractTypeHatAce} title='王牌主播' />
        </TreeNode>
        <TreeNode key={20} value={businessTypeBABY + 10} title='宝贝'>
          <TreeNode key={21} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={22} value={contractTypeHatAce} title='王牌主播' />
        </TreeNode>
      </TreeSelect>
      <div style={{ marginBottom: 10 }} />
      <span>签约时间</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='开始时间'
        onChange={(v) => this.setState({ searchSignStartTime5: v })}
        style={{ marginLeft: 10 }}
      />
      <span style={{ marginLeft: 3 }}>~</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='结束时间'
        onChange={(v) => this.setState({ searchSignEndTime5: v })}
        style={{ marginLeft: 3 }}
      />
      <span style={{ marginLeft: 15 }}>入库时间</span>
      <DatePicker onChange={(v) => this.setState({ searchInboundTime5: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>扶持开始时间</span>
      <DatePicker onChange={(v) => this.setState({ searchSupportStartTime5: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>扶持结束时间</span>
      <DatePicker onChange={(v) => this.setState({ searchSupportEndTime5: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>业务审核状态</span>
      <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchApprovalStatus5: v })} allowClear>
        <Option key={1} value={approvalPass}>通过</Option>
        <Option key={2} value={approvalReject}>不通过</Option>
        <Option key={2} value={approvalFirstGoing}>一审中</Option>
        <Option key={2} value={approvalSecondGoing}>二审中</Option>
      </Select>
      <div style={{ marginBottom: 10 }} />
      <span>机器码是否更新</span>
      <Select style={{ marginLeft: 5, width: 70 }} placeholder='全部' onChange={(v) => this.setState({ searchApprovalStatus5: v })} allowClear>
        <Option key={0} value={yesBoolStatus}>是</Option>
        <Option key={1} value={noBoolStatus}>否</Option>
      </Select>
      <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
      <Button style={{ marginLeft: 60 }} type='primary' onClick={this.importHandle()}>导入新主持</Button>
      <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onExportHandle5()}>选择导出名单</Button>
      <CSVLink data={this.onExport()} filename='新主持信息库.csv' target='_blank'><Button type='primary' style={{ marginLeft: 20 }}>导出全量名单</Button></CSVLink>
      <div>
        <div><font>注释：</font></div>
        <div><font>1、入库规则：</font></div>
        <div><font>①UID完成线上普通签约</font></div>
        <div><font>②UID已完成实名认证</font></div>
        <div><font>③UID对应实名身份证下，三业务均无其他产生签约关系UID</font></div>
        <div><font>④运营及业务审批通过</font></div>
        <div><font>2、出库：人工出库</font></div>
        <div><font>3、扶持结束tab：主持解约/扶持时间结束，名单归到扶持结束tab</font></div>
        <div><font>4、表格排序：按照入库时间从最新到最久</font></div>
        <div><font>5、机器码更新：每周三及每周日24点，对【待审核/审核通过/疑似】状态主持，判断信息库内PC开播机器码，与主持最近开播机器码，是否发生变化。变化显示“是”，并更新机器码。无变化显示“否”</font></div>
      </div>
      <Table tabelLayout='fixed' rowSelection={this.rowSelection5} rowKey='idx' dataSource={list} columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
    </div>
  }

  // 扶持结束
  tableHTML6 = () => {
    const { model: { list } } = this.props
    return <div><span>业务</span>
      <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchBusiness6: v })} allowClear>
        <Option key={1} value={businessTypeJY}>交友</Option>
        <Option key={2} value={businessTypePK}>约战</Option>
        <Option key={3} value={businessTypeBABY}>宝贝</Option>
      </Select>

      <Tooltip title='批量输入请双击'>
        <span style={{ marginLeft: 15 }}>UID</span>
        <Input
          allowClear
          ref={(input) => { this.inputRefUID6 = input }}
          style={{ width: 120, marginLeft: 3 }}
          placeholder={'输入UID'}
          onChange={(e) => { this.setState({ searchUID6: e.target.value }) }}
          onDoubleClick={(e) => { this.setState({ modelVisableUID6: true }) }}
        />
      </Tooltip>
      <Tooltip title='批量输入请双击'>
        <span style={{ marginLeft: 15 }}>YY号</span>
        <Input
          allowClear
          ref={(input) => { this.inputRefYY6 = input }}
          style={{ width: 120, marginLeft: 3 }}
          placeholder={'输入YY'}
          onChange={(e) => { this.setState({ searchYY6: e.target.value }) }}
          onDoubleClick={(e) => { this.setState({ modelVisableYY6: true }) }}
        />
      </Tooltip>

      <span style={{ marginLeft: 15 }}>昵称</span>
      <Input placeholder='请输入' onChange={e => this.setState({ searchNick6: e.target.value })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>签约短位ID</span>
      <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID6: e })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>公会身份</span>
      <TreeSelect showSearch style={{ marginLeft: 5, width: 150 }} placeholder='全部' allowClear treeDefaultExpandAll onChange={(v) => this.setState({ searchGuildType6: v })}>
        <TreeNode key={11} value={0} title='全部' />
        <TreeNode key={12} value={businessTypeJY + 10} title='交友'>
          <TreeNode key={13} value={guildTypeYY} title='普通公会' />
          <TreeNode key={14} value={guildTypeShuiJing} title='水晶公会' />
          <TreeNode key={15} value={guildTypeSuperShuiJing} title='超级水晶公会' />
        </TreeNode>
        <TreeNode key={16} value={businessTypePK + 10} title='约战'>
          <TreeNode key={17} value={guildTypeAceGuild} title='王牌公会' />
          <TreeNode key={18} value={guildTypeNormalGuild} title='普通公会' />
        </TreeNode>
        <TreeNode key={19} value={businessTypeBABY + 10} title='宝贝'>
          <TreeNode key={20} value={guildTypeAceGuild} title='王牌公会' />
          <TreeNode key={21} value={guildTypeNormalGuild} title='普通公会' />
        </TreeNode>
      </TreeSelect>
      <span style={{ marginLeft: 15 }}>主持身份</span>
      <TreeSelect showSearch style={{ marginLeft: 5, width: 150 }} placeholder='全部' allowClear treeDefaultExpandAll onChange={(v) => this.setState({ searchContractType6: v })}>
        <TreeNode key={11} value={0} title='全部' />
        <TreeNode key={12} value={businessTypeJY + 10} title='交友'>
          <TreeNode key={13} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={14} value={contractTypeSuper} title='超级主持' />
          <TreeNode key={16} value={contractTypeHatSuper} title='帽子超级' />
        </TreeNode>
        <TreeNode key={17} value={businessTypePK + 10} title='约战'>
          <TreeNode key={18} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={19} value={contractTypeHatAce} title='王牌主播' />
        </TreeNode>
        <TreeNode key={20} value={businessTypeBABY + 10} title='宝贝'>
          <TreeNode key={21} value={contractTypeNormal} title='普通主持' />
          <TreeNode key={22} value={contractTypeHatAce} title='王牌主播' />
        </TreeNode>
      </TreeSelect>
      <div style={{ marginBottom: 10 }} />
      <span>签约时间</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='开始时间'
        onChange={(v) => this.setState({ searchSignStartTime6: v })}
        style={{ marginLeft: 10 }}
      />
      <span style={{ marginLeft: 3 }}>~</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='结束时间'
        onChange={(v) => this.setState({ searchSignEndTime6: v })}
        style={{ marginLeft: 3 }}
      />
      <span style={{ marginLeft: 15 }}>入库时间</span>
      <DatePicker onChange={(v) => this.setState({ searchInboundTime6: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>扶持开始时间</span>
      <DatePicker onChange={(v) => this.setState({ searchSupportStartTime6: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>扶持结束时间</span>
      <DatePicker onChange={(v) => this.setState({ searchSupportEndTime6: v })} style={{ marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>业务审核状态</span>
      <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchApprovalStatus6: v })} allowClear>
        <Option key={1} value={approvalPass}>通过</Option>
        <Option key={2} value={approvalReject}>不通过</Option>
        <Option key={2} value={approvalFirstGoing}>一审中</Option>
        <Option key={2} value={approvalSecondGoing}>二审中</Option>
      </Select>
      <div style={{ marginBottom: 10 }} />
      <span>机器码是否更新</span>
      <Select style={{ marginLeft: 5, width: 70 }} placeholder='全部' onChange={(v) => this.setState({ searchApprovalStatus6: v })} allowClear>
        <Option key={0} value={yesBoolStatus}>是</Option>
        <Option key={1} value={noBoolStatus}>否</Option>
      </Select>
      <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
      <Button style={{ marginLeft: 60 }} type='primary' onClick={this.importHandle()}>导入新主持</Button>
      <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onExportHandle6()}>选择导出名单</Button>
      <CSVLink data={this.onExport()} filename='新主持信息库-扶持结束.csv' target='_blank'><Button type='primary' style={{ marginLeft: 20 }}>导出全量名单</Button></CSVLink>
      <div>
        <div><font>注释：</font></div>
        <div><font>1、入库规则：</font></div>
        <div><font>①UID完成线上普通签约</font></div>
        <div><font>②UID已完成实名认证</font></div>
        <div><font>③UID对应实名身份证下，三业务均无其他产生签约关系UID</font></div>
        <div><font>④运营及业务审批通过</font></div>
        <div><font>2、出库：人工出库</font></div>
        <div><font>3、扶持结束tab：主持解约/扶持时间结束，名单归到扶持结束tab</font></div>
        <div><font>4、表格排序：按照入库时间从最新到最久</font></div>
        <div><font>5、机器码更新：每周三及每周日24点，对【待审核/审核通过/疑似】状态主持，判断信息库内PC开播机器码，与主持最近开播机器码，是否发生变化。变化显示“是”，并更新机器码。无变化显示“否”</font></div>
      </div>
      <Table tabelLayout='fixed' rowSelection={this.rowSelection6} rowKey='idx' dataSource={list} columns={this.columnsHadOver} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
    </div>
  }

  tableHTML7 = () => {
    const { model: { inboundStatus } } = this.props
    console.log(inboundStatus)
    return <div>
      <span style={{ marginLeft: 300 }}>UID</span>
      <Input
        style={{ width: 120, marginLeft: 10 }}
        placeholder={'输入UID'}
        onChange={(e) => { this.setState({ searchUID: e.target.value }) }}
      />
      <span style={{ marginLeft: 15 }}>业务类型</span>
      <Select style={{ marginLeft: 10, width: 150 }} placeholder='全部' onChange={(v) => this.setState({ searchBusiness: v })} allowClear>
        <Option key={1} value={businessTypeJY}>交友</Option>
        <Option key={2} value={businessTypePK}>约战</Option>
        <Option key={3} value={businessTypeBABY}>宝贝</Option>
      </Select>
      <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>

      <div style={{ marginTop: 10 }}><span>uid<Input style={{ marginLeft: 47, width: 200 }} disabled value={inboundStatus.uid !== null && inboundStatus.uid !== 0 ? inboundStatus.uid : ''} /></span></div>
      <div style={{ marginTop: 10 }}><span>业务类型<Input style={{ marginLeft: 10, width: 200 }} disabled value={inboundStatus.business} /></span></div>
      <Divider />
      <div style={{ marginTop: 10 }}><span>实名类型<Input style={{ marginLeft: 10, width: 200 }} disabled value={inboundStatus.realNameType} /></span></div>
      <div style={{ marginTop: 10 }}><span>实名状态<Input style={{ marginLeft: 10, width: 200 }} disabled value={inboundStatus.realNameStatus} /></span></div>
      <div style={{ marginTop: 10 }}><span>实名时间<Input style={{ marginLeft: 10, width: 200 }} disabled value={inboundStatus.realNameTime} /></span></div>
      <Divider />

      <div style={{ marginTop: 10 }}><span>关联uid及其签约状态列表<Collapse
        bordered={false}
        expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
        className='site-collapse-custom-collapse'
      >
        {inboundStatus.relateUidContracts.map((item, index) => (<Panel header={'实名下关联UID' + index + ': ' + item.uid} key={index} className='site-collapse-custom-panel'><p>{'是否曾经签约: ' + item.contractStatus + ' ' + ' 业务类型: ' + item.contractBusiness}</p></Panel>))}
      </Collapse></span></div>
      <Divider />

      <div style={{ marginTop: 10 }}><span>当前uid是否有签约过其他业务<Input style={{ marginLeft: 10, width: 200 }} disabled defaultValue='' value={inboundStatus.uid === 0 ? '' : inboundStatus.curHadContractOther === true ? '是' : '否'} /></span></div>
      <div style={{ marginTop: 10 }}><span>当前uid是否有签约过其他业务详情<Collapse
        bordered={false}
        expandIcon={({ isActive }) => <CaretRightOutlined rotate={isActive ? 90 : 0} />}
        className='site-collapse-custom-collapse'
      >
        {inboundStatus.curHadContractOtherExpand.map((item, index) => (<Panel header={item.substring(0, item.indexOf(','))} key={index} className='site-collapse-custom-panel'><p>{item.substring(item.indexOf(',') + 1)}</p></Panel>))}
      </Collapse></span></div>
    </div>
  }

  render () {
    const { visible } = this.state
    const { route, model: { tabSumInfo } } = this.props
    const { TabPane } = Tabs
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 2 },
        sm: { span: 15 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='1' onChange={(record) => this.onTagChange(record)} type='card' size='large'>
            <TabPane tab={<span>待审核 ({tabSumInfo !== null ? tabSumInfo.firstTab : 0})</span>} key='1'>
              {this.tableHTML1()}
            </TabPane>
            <TabPane tab={<span>疑似 ({tabSumInfo !== null ? tabSumInfo.secondTab : 0})</span>} key='2'>
              {this.tableHTML2()}
            </TabPane>
            <TabPane tab={<span>审核中 ({tabSumInfo !== null ? tabSumInfo.thirdTab : 0})</span>} key='3'>
              {this.tableHTML3()}
            </TabPane>
            <TabPane tab={<span>审核通过 ({tabSumInfo !== null ? tabSumInfo.fourthTab : 0})</span>} key='4'>
              {this.tableHTML4()}
            </TabPane>
            <TabPane tab={<span>审核不通过 ({tabSumInfo !== null ? tabSumInfo.fifthTab : 0})</span>} key='5'>
              {this.tableHTML5()}
            </TabPane>
            <TabPane tab={<span>扶持结束 ({tabSumInfo !== null ? tabSumInfo.sixthTab : 0})</span>} key='6'>
              {this.tableHTML6()}
            </TabPane>
            <TabPane tab={<span>主持信息查询</span>} key='7'>
              {this.tableHTML7()}
            </TabPane>
          </Tabs>
        </Card>

        <Modal forceRender width={600} visible={visible} title='批量导入新主持' onCancel={this.handleCancel} onOk={this.handleSubmit}>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <Form.Item label='入库方式' name='importType' rules={[{ required: true }]}>
              <Select style={{ marginLeft: 5 }} placeholder='请选择'>
                <Option value={importTypeUser}>手工入库</Option>
                {/* <Option value={importTypeSystem}>系统入库</Option> */}
              </Select>
            </Form.Item>
            <Form.Item label='业务' name='business' rules={[{ required: true }]}>
              <Select style={{ marginLeft: 5 }} placeholder='请选择'>
                <Option value={businessTypeJY}>交友</Option>
                <Option value={businessTypePK}>约战</Option>
                <Option value={businessTypeBABY}>宝贝</Option>
              </Select>
            </Form.Item>
            <span>
              <Form.Item label='导入名单' name='fileURL' rules={[{ required: true }]}>
                <Upload name='file' action='https://fts.yy.com/fs/uploadfiles' data={file => ({ bucket: 'makefriends', files: file })} onChange={this.UpLoadOnChange} multiple={false}>
                  <Button type='primary'>
                    <UploadOutlined /> 上传
                  </Button>
                </Upload>
              </Form.Item>
              <Button type='primary' style={{ marginLeft: 135 }}>
                <a href='https://makefriends.bs2dl.yy.com/1621419787_f677c5ca82554c3353c374703f2cfadf.xlsx' onClick={this.downLoad}>下载模板</a>
              </Button>
            </span>
            <div style={{ marginLeft: 135 }}>
              <div><font>1、请下载模板，按照模板中字段要求，完成输入</font></div>
              <div><font>2、上传文件仅使用模板文件格式及字段，数量1个</font></div>
              <div><font>3、提交后，名单会自动导入新主持库</font></div>
            </div>
          </Form>
        </Modal>

        <Modal
          title={'批量查询UID'}
          visible={this.state.modelVisableUID1}
          onCancel={() => { this.setState({ modelVisableUID1: false }) }}
          onOk={(v) => { this.onBatchInputCompleteUID1(v) }}
          centered
        >
          <Input.TextArea
            // ref={(input) => { this.inputRefUID1 = input }}
            allowClear
            placeholder='tip:每项数据占一行, 限制100行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiUID1: e.target.value }) }}
          />
        </Modal>

        <Modal
          title={'批量查询YY'}
          visible={this.state.modelVisableYY1}
          onCancel={() => { this.setState({ modelVisableYY1: false }) }}
          onOk={(v) => { this.onBatchInputCompleteYY1(v) }}
          centered
        >
          <Input.TextArea
            // ref={(input) => { this.inputRefYY1 = input }}
            allowClear
            placeholder='tip:每项数据占一行, 限制100行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiYY1: e.target.value }) }}
          />
        </Modal>

        <Modal
          title={'批量查询UID'}
          visible={this.state.modelVisableUID2}
          onCancel={() => { this.setState({ modelVisableUID2: false }) }}
          onOk={(v) => { this.onBatchInputCompleteUID2(v) }}
          centered
        >
          <Input.TextArea
            // ref={(input) => { this.inputRefUID2 = input }}
            allowClear
            placeholder='tip:每项数据占一行, 限制100行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiUID2: e.target.value }) }}
          />
        </Modal>

        <Modal
          title={'批量查询YY'}
          visible={this.state.modelVisableYY2}
          onCancel={() => { this.setState({ modelVisableYY2: false }) }}
          onOk={(v) => { this.onBatchInputCompleteYY2(v) }}
          centered
        >
          <Input.TextArea
            // ref={(input) => { this.inputRefYY2 = input }}
            allowClear
            placeholder='tip:每项数据占一行, 限制100行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiYY2: e.target.value }) }}
          />
        </Modal>

        <Modal
          title={'批量查询UID'}
          visible={this.state.modelVisableUID3}
          onCancel={() => { this.setState({ modelVisableUID3: false }) }}
          onOk={(v) => { this.onBatchInputCompleteUID3(v) }}
          centered
        >
          <Input.TextArea
            // ref={(input) => { this.inputRefUID3 = input }}
            allowClear
            placeholder='tip:每项数据占一行, 限制100行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiUID3: e.target.value }) }}
          />
        </Modal>

        <Modal
          title={'批量查询YY'}
          visible={this.state.modelVisableYY3}
          onCancel={() => { this.setState({ modelVisableYY3: false }) }}
          onOk={(v) => { this.onBatchInputCompleteYY3(v) }}
          centered
        >
          <Input.TextArea
            // ref={(input) => { this.inputRefYY3 = input }}
            allowClear
            placeholder='tip:每项数据占一行, 限制100行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiYY3: e.target.value }) }}
          />
        </Modal>

        <Modal
          title={'批量查询UID'}
          visible={this.state.modelVisableUID4}
          onCancel={() => { this.setState({ modelVisableUID4: false }) }}
          onOk={(v) => { this.onBatchInputCompleteUID4(v) }}
          centered
        >
          <Input.TextArea
            // ref={(input) => { this.inputRefUID4 = input }}
            allowClear
            placeholder='tip:每项数据占一行, 限制100行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiUID4: e.target.value }) }}
          />
        </Modal>

        <Modal
          title={'批量查询YY'}
          visible={this.state.modelVisableYY4}
          onCancel={() => { this.setState({ modelVisableYY4: false }) }}
          onOk={(v) => { this.onBatchInputCompleteYY4(v) }}
          centered
        >
          <Input.TextArea
            // ref={(input) => { this.inputRefYY4 = input }}
            allowClear
            placeholder='tip:每项数据占一行, 限制100行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiYY4: e.target.value }) }}
          />
        </Modal>

        <Modal
          title={'批量查询UID'}
          visible={this.state.modelVisableUID5}
          onCancel={() => { this.setState({ modelVisableUID5: false }) }}
          onOk={(v) => { this.onBatchInputCompleteUID5(v) }}
          centered
        >
          <Input.TextArea
            // ref={(input) => { this.inputRefUID5 = input }}
            allowClear
            placeholder='tip:每项数据占一行, 限制100行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiUID5: e.target.value }) }}
          />
        </Modal>

        <Modal
          title={'批量查询YY'}
          visible={this.state.modelVisableYY5}
          onCancel={() => { this.setState({ modelVisableYY5: false }) }}
          onOk={(v) => { this.onBatchInputCompleteYY5(v) }}
          centered
        >
          <Input.TextArea
            // ref={(input) => { this.inputRefYY5 = input }}
            allowClear
            placeholder='tip:每项数据占一行, 限制100行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiYY5: e.target.value }) }}
          />
        </Modal>

        <Modal
          title={'批量查询UID'}
          visible={this.state.modelVisableUID6}
          onCancel={() => { this.setState({ modelVisableUID6: false }) }}
          onOk={(v) => { this.onBatchInputCompleteUID6(v) }}
          centered
        >
          <Input.TextArea
            // ref={(input) => { this.inputRefUID6 = input }}
            allowClear
            placeholder='tip:每项数据占一行, 限制100行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiUID6: e.target.value }) }}
          />
        </Modal>

        <Modal
          title={'批量查询YY'}
          visible={this.state.modelVisableYY6}
          onCancel={() => { this.setState({ modelVisableYY6: false }) }}
          onOk={(v) => { this.onBatchInputCompleteYY6(v) }}
          centered
        >
          <Input.TextArea
            // ref={(input) => { this.inputRefYY6 = input }}
            allowClear
            placeholder='tip:每项数据占一行, 限制100行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiYY6: e.target.value }) }}
          />
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default NewCompereInfo
