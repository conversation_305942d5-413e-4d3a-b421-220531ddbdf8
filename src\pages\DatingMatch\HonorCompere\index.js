import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Input, Tooltip, Modal, Popconfirm } from 'antd'
import { connect } from 'dva'
import moment from 'moment'
import 'moment/locale/zh-cn'
import { QuestionCircleOutlined } from '@ant-design/icons'
moment.locale('zh-cn')

const namespace = 'HonorCompere'
const FormItem = Form.Item

@connect(({ HonorCompere }) => ({
  model: HonorCompere
}))
class HonorCompere extends Component {
  genTitle = (title, tips) => {
    return <span>
      <Tooltip title={tips}>
        <QuestionCircleOutlined style={{ marginRight: '0.25em' }} />
        {title}
      </Tooltip>
    </span>
  }

  // 需要修改
  ColumnHonorCompere = [
    { title: '主持UID', dataIndex: 'uid', align: 'center' },
    { title: '主持昵称', dataIndex: 'nick', align: 'center' },
    { title: '主持YY号', dataIndex: 'yy', align: 'center' },
    // { title: '短位频道', dataIndex: 'asid', align: 'center' },
    { title: '签约频道', dataIndex: 'sid', align: 'center' },
    { title: '荣誉介绍', dataIndex: 'title', align: 'center' },
    { title: '操作',
      align: 'center',
      render: (text, record) => {
        return <Popconfirm title='确认删除?' type='primary' onConfirm={this.removeItem(record.uid)} okText='确认' cancelText='取消'><a href=''>删除</a></Popconfirm>
      }
    }
  ]

  defaultPageValue = { pageSizeOptions: ['50', '60', '100', '1000'], showSizeChanger: true, pageSize: 50, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` } // 分页设置，可以不改

  // 需要修改
  state = {
    visible: false,
    addVisible: false,
    value: {}
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = { }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  removeItem = (uid) => () => {
    var getListParam = { }
    const { dispatch } = this.props
    var payload = { uid: uid }
    dispatch({
      type: `${namespace}/remove`,
      payload: payload,
      getListParam: getListParam
    })
  }

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record) => () => {
    if (this.formRef) {
      this.formRef.setFieldsValue(record)
    }
    this.setState({ value: record, addVisible: true, title: '添加' })
  }

  // hide total modal
  hideModal = () => {
    this.setState({ addVisible: false })
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  saveAddFormRef = (formRef) => {
    this.addFormRef = formRef
  }

  handleAddSubmit = () => {
    this.formRef.submit()
    this.setState({ addVisible: false })
  }

  // 添加
  onFinish = values => {
    const { dispatch } = this.props
    var getListParam = { }
    const url = `${namespace}/addItem`
    dispatch({
      type: url,
      payload: values,
      getListParam: getListParam
    })
    this.formRef.resetFields()
  }

  // 实际的页面信息
  render () {
    const { route, model: { list } } = this.props // 基本不需要修改
    const { addVisible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Button type='primary' onClick={this.showModal(false, null)}>新增</Button>
            <Divider /> {/* 分割线 */}
            <Table dataSource={list} columns={this.ColumnHonorCompere} pagination={this.defaultPageValue} rowKey={(record, index) => index} /> {/* 显示的列表 */}
          </Form>
        </Card>

        <Modal visible={addVisible} title={title} onCancel={this.hideModal} onOk={this.handleAddSubmit}>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem label='YY' name='yy' rules={[{ required: true, message: '主持YY号不能为空' }]}>
              <Input />
            </FormItem>
            <FormItem label='荣誉介绍' name='title' rules={[{ required: true, message: '荣誉介绍不能为空' }]}>
              <Input />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default HonorCompere
