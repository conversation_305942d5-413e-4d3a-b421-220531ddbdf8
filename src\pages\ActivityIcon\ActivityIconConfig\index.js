import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
// import PicturesWall from '@/components/PicturesWall'
// import PopImage from '@/components/PopImage'
import dateString from '@/utils/dateString'
import { Table, Divider, Button, Form, Card, Input, Select, Modal, DatePicker, Popconfirm, Space } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const namespace = 'activityIconConfig'
const getListUri = `${namespace}/getList`
const addItemUri = `${namespace}/addItem`
const TextArea = Input.TextArea
// const rmoveItem = `${namespace}/removeItem`
const FormItem = Form.Item
const Option = Select.Option
// const RadioGroup = Radio.Group
const typeMap = { 1: '尾灯', 2: '勋章', 3: '铭牌', 4: '迷你资料卡', 5: '公会勋章', 6: '贵族图标' }

@connect(({ activityIconConfig }) => ({
  model: activityIconConfig
}))
class activityIconConfig extends Component {
  // 列表结构
  columns = [
    { title: '图标id', dataIndex: 'id', key: 'id', align: 'center', width: 70 },
    { title: '图标类型',
      dataIndex: 'icon_type',
      align: 'center',
      width: 70,
      render: (text, record) => {
        switch (text) {
          case 1: return '尾灯'
          case 2: return '勋章'
          case 3: return '铭牌'
          case 4: return '迷你资料卡'
          case 5: return '公会勋章'
          case 6: return '贵族图标'
        }
        return ''
      }
    },
    // { title: '业务类型',
    //   dataIndex: 'activity_id',
    //   key: 'activity_id',
    //   align: 'center',
    //   width: 70,
    //   render: (v) => {
    //     v = this.groupNameFormater(v)
    //     switch (v) {
    //       case 1: return '交友'
    //       case 2: return '追玩'
    //       case 3: return '约战'
    //       case 4: return '宝贝'
    //       case 5:return '游戏直播'
    //     }
    //     return ''
    //   }
    // },
    { title: '活动id', dataIndex: 'activity_id', align: 'center', width: 70 },
    // { title: '活动名称', dataIndex: 'activity_id', align: 'center', width: 150, render: (v) => { return this.activityNameFormater(v) } },
    { title: '开始时间', dataIndex: 'start_time', align: 'center', render: text => dateString(text), width: 160 },
    { title: '结束时间', dataIndex: 'end_time', align: 'center', render: text => dateString(text), width: 160 },
    { title: '冒烟开始时间', dataIndex: 'smoke_start_time', align: 'center', render: text => dateString(text), width: 160 },
    { title: '冒烟结束时间', dataIndex: 'smoke_end_time', align: 'center', render: text => dateString(text), width: 160 },
    { title: '前缀', dataIndex: 'prefix', align: 'center', width: 50 },
    { title: '服务名', dataIndex: 'service_name', align: 'center', width: 70 },
    { title: '是否支持降级',
      dataIndex: 'support_downgrade',
      key: 'support_downgrade',
      align: 'center',
      width: 80,
      render: (text, record) => {
        switch (text) {
          case false: return '不支持'
          case true: return '支持'
        }
        return ''
      }
    },
    { title: '操作',
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(true, record)}>更新</a><Divider type='vertical' />
          <Popconfirm title='确认?' onConfirm={this.handleRemove(record)}>
            <a href=''>删除</a>
          </Popconfirm>
        </span>)
    }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  defaultValue = { id: null, icon_type: null, business_type: null, prefix: null, support_downgrade: null, service_name: null, start_time: moment().unix(), end_time: moment().unix(), smoke_start_time: moment().unix(), smoke_end_time: moment().unix() }

  state = { visible: false, isUpdate: false, value: {} }

  // 获取列表
  componentDidMount () {
    this.callModel('getActivityList')
    // this.callModel('getGroupList')
    this.getList()
  }
  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 格式化活动名称
  activityNameFormater = (id) => {
    const { activityList } = this.props.model
    if (!activityList) return '?'
    return activityList.find(item => { return item.actId === id })?.actName
  }
  // 格式化业务名称
  groupNameFormater = (id) => {
    const { activityList } = this.props.model
    if (!activityList) return '?'
    return activityList.find(item => { return item.actId === id })?.groupId
  }

  infoFormater (values) {
    console.log('infoFormater', values)
    let info = '更新图标信息为：\n 图标id:' + values.id + '\n'
    info += '图标类型：' + typeMap[values.icon_type] + '\n'
    // // transfer Moment object to timestamp !!!
    info += '线上开始时间:' + dateString(Number(values.start_time / 1000)) + ' 结束时间:' + dateString(Number(values.end_time / 1000)) + '\n'
    info += '冒烟开始时间:' + dateString(Number(values.smoke_start_time / 1000)) + ' 结束时间:' + dateString(Number(values.smoke_end_time / 1000)) + '\n'
    values.support_downgrade = Boolean(values.support_downgrade)
    info += values.support_downgrade ? '' : '不' + '支持降级' + '\n'
    console.log('info', info)
    return info
  }

  // infoFormater2 = (values) => {
  //   return (
  //     <Card>
  //       <Row>
  //         <Col span={24}>
  //           更新图标信息为：
  //         </Col>
  //         <Col span={12}>
  //           图标id: {values.id}
  //         </Col>
  //         <Col span={12}>
  //           是否支持降级：{Boolean(values.support_downgrade) === true ? '是' : '否'}
  //         </Col>
  //         <Col span={12}>
  //           图标类型: {typeMap[values.icon_type]}
  //         </Col>
  //         <Col span={12}>
  //           线上开始时间: {dateString(Number(values.start_time / 1000))} 线上开始时间: {dateString(Number(values.end_time / 1000))}
  //         </Col>
  //         <Col span={12}>
  //           冒烟开始时间: {dateString(Number(values.smoke_start_time / 1000))} 冒烟结束时间: {dateString(Number(values.smoke_end_time / 1000))}
  //         </Col>
  //       </Row>
  //     </Card>
  //   )
  // }

  infoFormater2 = (values) => {
    return (
      <Card>
        <div><font color='black'>更新图标信息为:</font></div>
        <div><font color='black'> 图标id:</font> <font color='red'>{values.id}</font></div>
        <div><font color='black'> 图标类型:</font> <font color='red'>{typeMap[values.icon_type]}</font></div>
        <div><font color='black'> 是否支持降级：</font> <font color='red'>{Boolean(values.support_downgrade) === true ? '是' : '否'}</font></div>
        <div><font
          color='black'> 冒烟开始时间:</font> <font color='red'>{dateString(Number(values.smoke_start_time / 1000))}</font> <font color='black'> 冒烟结束时间: </font> <font color='red'>{dateString(Number(values.smoke_end_time / 1000))}</font>
        </div>
        <div><font color='black'> 线上开始时间:</font> <font color='red'>{dateString(Number(values.start_time / 1000))}</font> <font color='black'> 线上开始时间: </font> <font color='red'> {dateString(Number(values.end_time / 1000))}</font>
        </div>
      </Card>
    )
  }

  getList () {
    const { dispatch } = this.props
    dispatch({
      type: getListUri
    })
  }

  handleSubmit = () => {
    console.log('submit', this.props)
    this.formRef.submit() // 提交表单 触发 onFinish 事件
    this.setState({ visible: false })
  }

  // 添加 与 编辑
  onFinish = values => {
    const { dispatch } = this.props
    // transfer Moment object to timestamp !!!
    values.start_time = values.start_time.unix()
    values.end_time = values.end_time.unix()
    values.smoke_start_time = values.smoke_start_time.unix()
    values.smoke_end_time = values.smoke_end_time.unix()
    values.support_downgrade = Boolean(values.support_downgrade)
    console.log('values', values)
    var url = addItemUri
    dispatch({
      type: url,
      payload: values
    }).then(res => {
      this.getList()
    })
    this.formRef.resetFields()
  }

  // 显示弹窗
  showModal = (isUpdate, record) => e => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.icon_type = v.icon_type == null ? 1 : v.icon_type
      v.business_type = v.business_type == null ? 0 : v.business_type
      v.support_downgrade = v.support_downgrade == null ? 0 : v.support_downgrade
      v.start_time = isUpdate === false ? moment() : moment.unix(v.start_time)
      v.end_time = isUpdate === false ? moment() : moment.unix(v.end_time)
      v.smoke_start_time = isUpdate === false ? moment() : moment.unix(v.smoke_start_time)
      v.smoke_end_time = isUpdate === false ? moment() : moment.unix(v.smoke_end_time)
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, value: record, isUpdate: isUpdate, title: isUpdate ? '更新图标配置' : '添加图标配置' })
  }

  // 关闭弹窗
  hidModal = () => {
    this.setState({ visible: false })
  }

  // 删除
  handleRemove = record => e => {
    const { dispatch } = this.props
    const data = { id: record.id, icon_type: record.icon_type }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // 更新
  handleUpdate = id => () => {
    const { dispatch } = this.props
    var data = { id: id }
    dispatch({
      type: `${namespace}/addItem`,
      payload: data
    })
  }

  saveFormRef = formRef => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list } } = this.props
    const { visible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 6 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 18 },
        sm: { span: 18 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Button type='primary' onClick={this.showModal(false, this.defaultValue)}>添加</Button>
          <div><font color='red'>注意事项:</font></div>
          <div><font color='red'> 1.图标id必填，活动开始结束时间必填</font></div>
          <div><font color='red'> 2.目前仅尾灯需要填写前缀和服务名称,活动尾灯id不要填9,因为活动尾灯的id各个业务都用这个,所以这里活动尾灯的id和前缀那个数字一致就好,如尾灯前缀116_则填116</font></div>
          <div><font color='red'> 3.冒烟时段为活动图标线上验证时段且不能与正式活动时间重合</font></div>
          <div><font color='red'> 4.以上请保持谨慎操作</font></div>
          <Divider />
          <Table dataSource={list} columns={this.columns} scroll={{ x: 'max-content', y: 560 }} rowKey={(record, index) => index} pagination={this.defaultPageValue} />
        </Card>

        <Modal forceRender visible={visible} title={title} footer={false} onCancel={this.hidModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} {...formItemLayout} ref={form => { this.formRef = form }}>
            <FormItem label='图标id' name='id' rules={[{ required: true, message: 'id不能为空' }]}>
              <Input placeholder='需为数字' />
            </FormItem>
            <FormItem label='图标类型' name='icon_type' rules={[{ required: true, message: 'key can not be null' }]}>
              <Select>
                <Option value={1}>尾灯</Option>
                <Option value={2}>勋章</Option>
                <Option value={3}>铭牌</Option>
                <Option value={4}>迷你资料卡</Option>
                <Option value={5}>公会勋章</Option>
                <Option value={6}>贵族图标</Option>
              </Select>
            </FormItem>
            <FormItem label='前缀' name='prefix'>
              <TextArea row={1} />
            </FormItem>
            <FormItem label='服务名称' name='service_name'>
              <TextArea row={1} />
            </FormItem>
            <FormItem label='是否支持降级' name='support_downgrade' rules={[{ required: true, message: '仅麦序尾灯支持降级' }]}>
              <Select>
                <Option value={0}>不支持</Option>
                <Option value={1}>支持</Option>
              </Select>
            </FormItem>
            <FormItem label='活动id' name='activity_id' >
              <Input placeholder='活动id int32' />
            </FormItem>
            <FormItem label='冒烟开始时间' name='smoke_start_time' rules={[{ required: true, message: '冒烟时段不能与正式活动时间重合' }]}>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </FormItem>
            <FormItem label='冒烟结束时间' name='smoke_end_time' rules={[{ required: true, message: '冒烟时段不能与正式活动时间重合' }]}>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </FormItem>
            <FormItem label='开始时间' name='start_time' rules={[{ required: true, message: '时间不能为空' }]}>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </FormItem>
            <FormItem label='结束时间' name='end_time' rules={[{ required: true, message: '时间不能为空' }]}>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </FormItem>
            <FormItem name='id'>
              <Input hidden />
            </FormItem>
            <div style={{ float: 'right', margin: '-1em' }}>
              <Space direction='horizontal'>
                <Button size='small' onClick={this.hidModal} >取消</Button>
                <Popconfirm title={() => { return this.infoFormater2(this.formRef.getFieldsValue()) }} cancelText='返回编辑' onConfirm={() => { this.handleSubmit() }} >
                  <Button size='small' type='primary' color='blue' >确定</Button>
                </Popconfirm>
              </Space>
            </div>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default activityIconConfig
