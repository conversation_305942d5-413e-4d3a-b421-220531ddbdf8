import React, { Component } from 'react'
import { connect } from 'dva'
import { Row, Col, Space, Divider, Button, DatePicker, Table, Select, Typography, InputNumber, message } from 'antd'
import { businessTypeOptisonsV2, businessTypeFormater, commodityTypeFormater, getCommodityTypeOptisons, getConfigIdOptisons } from './common'
import { timeFormater, deepClone } from '@/utils/common'
const { Text } = Typography
const namespace = 'fragmentReport'

@connect(({ fragmentReport }) => ({
  model: fragmentReport
}))

class ExchangeReport extends Component {
  state = {
    queryParams: {
      timeRange: [null, null],
      pageSize: 20,
      page: 1,
      businessType: 6,
      commodityType: 0,
      uid: null,
      configId: 0
    },
    totalData: 0
  }

  componentDidMount = () => {
    const { queryParams } = this.state
    this.queryConfig()
    this.queryData(queryParams)
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  queryConfig = () => {
    this.callModel('getFragmentConfig')
  }

  queryData = (queryParams, isDownload) => {
    let fixParams = deepClone(queryParams)
    const { timeRange } = queryParams
    if (timeRange && timeRange[0] && timeRange[1]) {
      fixParams.timeRange = undefined
      fixParams.startTime = timeRange[0].unix()
      fixParams.endTime = timeRange[1].endOf('hour').unix()
    }

    let funcName = 'getExchangeList'
    if (isDownload) {
      funcName = 'downloadExchangeList'
    }

    this.callModel(funcName, {
      params: fixParams,
      isRawResp: true,
      isDetailMode: true,
      isDownloadMode: isDownload,
      cbFunc: (ret) => {
        console.debug(ret)
        const { status, msg, total, list } = ret
        if (status !== 0) {
          message.warn('查询数据失败: ' + msg)
          return
        }
        this.setState({ totalData: total })
        this.changeState('exchangeList', list)
      }
    })
  }

  updateParams = (field, value) => {
    let { queryParams } = this.state
    queryParams[field] = value
    queryParams.page = 1
    this.setState({ queryParams: queryParams })
  }

  changePage = (pageSize, pageNo) => {
    let { queryParams } = this.state
    queryParams.pageSize = pageSize
    queryParams.page = pageNo
    this.setState({ queryParams: queryParams })
    this.queryData(queryParams)
  }

  fixName = (v) => {
    if (String(v).indexOf('兑换') === 0) {
      return String(v).substr(2)
    }
    return v
  }

  render () {
    const { queryParams, totalData } = this.state
    const { exchangeList, fragmentConfig } = this.props.model

    const mainColumns = [
      { title: '业务', dataIndex: 'businessType', render: (v) => { return businessTypeFormater(v) } },
      { title: '兑换时间', dataIndex: 'timestamp', render: (v) => { return timeFormater(v) } },
      { title: '兑换UID', dataIndex: 'uid' },
      { title: '商品类型', dataIndex: 'commodityType', render: (v) => { return commodityTypeFormater(v, fragmentConfig) } },
      { title: '商品名称', dataIndex: 'name', render: (v) => { return this.fixName(v) } },
      { title: '商品id', dataIndex: 'commodityId' },
      { title: '商品单价/紫水晶', dataIndex: 'price' },
      { title: '兑换数量', dataIndex: 'commodityCount' },
      { title: '兑换总价/紫水晶', dataIndex: 'frags' }
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <div>
        <Row>
          <Col span={24}>
            <Space>
              <Text>时间范围:</Text>
              <DatePicker.RangePicker showTime format={'YYYY-MM-DD HH时'} value={queryParams.timeRange} onChange={v => this.updateParams('timeRange', v)} />
              <Text>业务:</Text>
              <Select style={{ width: '6em' }} options={businessTypeOptisonsV2} value={queryParams.businessType} onChange={v => this.updateParams('businessType', v)} />
              <Text>商品类型:</Text>
              <Select style={{ width: '8em' }} options={getCommodityTypeOptisons(fragmentConfig)} value={queryParams.commodityType} onChange={v => this.updateParams('commodityType', v)} />
              <Text>兑换UID:</Text>
              <InputNumber style={{ width: '10em' }} value={queryParams.uid} onChange={(v) => this.updateParams('uid', v)} />
              <Text>商品名称:</Text>
              <Select style={{ width: '10em' }} options={getConfigIdOptisons(fragmentConfig, queryParams.businessType)} value={queryParams.configId} onChange={v => this.updateParams('configId', v)} />

              <Button onClick={() => { this.queryData(queryParams) }} type='primary'>查询</Button>
              <Button onClick={() => { this.queryData(queryParams, true) }}>导出</Button>
            </Space>
          </Col>
          <Divider />
          <Col span={24}>
            <Table columns={mainColumns} dataSource={exchangeList} pagination={{
              pageSize: queryParams.pageSize,
              total: totalData,
              current: queryParams.page,
              showTotal: (total, range) => {
                return `每页${queryParams.pageSize}行, 共${totalData}行`
              },
              onChange: (pageNo, pageSize) => {
                this.changePage(pageSize, pageNo)
              },
              showSizeChanger: true,
              onShowSizeChange: (current, pageSize) => {
                this.setState({ pageSize: pageSize })
              }
            }} />
          </Col>
        </Row>
      </div>
    )
  }
}

export default ExchangeReport
