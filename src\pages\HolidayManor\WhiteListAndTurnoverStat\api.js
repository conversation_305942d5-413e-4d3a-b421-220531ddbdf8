﻿import request from '@/utils/request'
import { stringify } from 'qs'
import moment from 'moment'

export function getLists (params) {
  let url = `/account/boss/holidaymanor/query_conf?${stringify(params)}`
  return request(url, { jsonp: true })
}

export function getStatLists (params) {
  let url = `/account/boss/holidaymanor/query_turnover_stat?${stringify(params)}`
  return request(url, { jsonp: true })
}

export function whiteListAdd (params) {
  params['startDate'] = moment(params['startDate']).format('YYYY-MM-DD')
  params['endDate'] = moment(params['endDate']).format('YYYY-MM-DD')
  console.log('--->', params)
  let url = '/account/boss/holidaymanor/add_conf'
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function whiteListUpdate (params) {
  let url = '/account/boss/holidaymanor/update_conf'
  params['startDate'] = moment(params['startDate']).format('YYYY-MM-DD')
  params['endDate'] = moment(params['endDate']).format('YYYY-MM-DD')
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function whiteListDel (params) {
  let url = '/account/boss/holidaymanor/del_conf'
  params['uid'] = parseInt(params['uid'], 10)
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
