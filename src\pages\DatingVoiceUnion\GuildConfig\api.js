import request from '@/utils/request'
import { stringify } from 'qs'
import { doPost } from '../common'

export function pageListGuildConfig (params) {
  params.pageNo = params.pageNo || -1
  params.pageSize = params.pageSize || -1
  return request(`/cross_business_play/boss/dating_yyf_guild/list?${stringify(params)}`)
}

export function addGuildConfig (params) {
  return doPost(`/cross_business_play/boss/dating_yyf_guild/add`, params)
}

export function updateGuildConfig (params) {
  return doPost(`/cross_business_play/boss/dating_yyf_guild/update`, params)
}

export function removeGuildConfig (params) {
  return doPost(`/cross_business_play/boss/dating_yyf_guild/delete`, params)
}

export function pageContractCompereList (params) {
  params.pageNo = params.pageNo || -1
  params.pageSize = params.pageSize || -1
  return request(`/cross_business_play/boss/dating_yyf_guild/list_contract_compere?${stringify(params)}`)
}

export function syncContract (params) {
  return doPost(`/cross_business_play/boss/dating_yyf_guild/sync_contract`, params)
}

export function pageContractCompereSyncList (params) {
  params.pageNo = params.pageNo || -1
  params.pageSize = params.pageSize || -1
  return request(`/cross_business_play/boss/dating_yyf_guild/list_contract_compere_sync_history?${stringify(params)}`)
}

export function pageDatingYyfGuildChangeHistoryList (params) {
  params.pageNo = params.pageNo || -1
  params.pageSize = params.pageSize || -1
  return request(`/cross_business_play/boss/dating_yyf_guild/list_change_history?${stringify(params)}`)
}

export function getAllowSyncUIDList () {
  return request(`/cross_business_play/boss/dating_yyf_guild/list_allow_sync_uid`)
}

export function updateAllowSyncUIDList (params) {
  return doPost(`/cross_business_play/boss/dating_yyf_guild/update_allow_sync_uid`, params)
}
