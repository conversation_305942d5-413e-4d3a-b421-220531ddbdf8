import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Table, Drawer } from 'antd'
import moment from 'moment'
import styles from '../index.module.less'
import ApprovalDetailComponent from './approval_detail'
import { Link } from 'dva/router'
import { fixURI } from '@/utils/common'

const namespace = 'adminApproval'

@connect(({ adminApproval }) => ({
  model: adminApproval
}))
class AdminApprovalMyApprovals extends Component {
  columns = [
    // eslint-disable-next-line no-eval
    { title: '序号', align: 'center', render: (v, _, index) => index + 1 },
    { title: 'ID', dataIndex: 'proposer', align: 'center', render: v => v.nid },
    { title: '审批规则', dataIndex: 'rule', align: 'center', render: v => v.name },
    { title: '快速跳转', dataIndex: 'rule', align: 'center', render: v => v.link ? <Link to={fixURI(v.link)}>快速跳转</Link> : '' },
    { title: '审批人', dataIndex: 'creator', align: 'center', render: v => v.passport },
    { title: '审批时间', dataIndex: 'timestamp', align: 'center', render: v => moment.unix(v).format('YYYY-MM-DD HH:mm:ss') },
    { title: '审批内容', align: 'center', render: (v, rec) => <a onClick={this.showContent(rec)}>查看详情</a> },
    { title: '审批结果', dataIndex: 'result', align: 'center', render: v => ['', '驳回', '通过', '过期', '终止'][['', 'Rejected', 'Passed', 'Expired', 'Abort'].indexOf(v)] },
    { title: '审批说明', dataIndex: 'reason', align: 'center', render: (v, rec) => `${v}${rec.specText ? `|${rec.specText}` : ''}` }
  ]

  state = { decoder: {}, content: {}, visible: false }

  dfs (uri, index, menuList) {
    menuList = menuList || []
    for (let i = 0; i < menuList.length; i++) {
      if (menuList[i].uri === uri) {
        return `${index}/${uri.replace(/^\//, '').replace('.html', '')}`
      }

      let path = this.dfs(uri, `${index}/${i}`, menuList[i].menuItemList)
      if (path !== undefined) {
        return path
      }
    }
  }

  jump = url => e => {
    const { model: { menuList } } = this.props
    let path = this.dfs(url, '', menuList)
    console.log(menuList, url, path)
    if (path !== undefined) {
      window.open(`/n/${path.replace(/^\//, '')}`)
    }
  }

  componentDidMount () {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/getMyApproval`
    })
  }

  // 显示审批信息
  showContent = rec => e => {
    this.setState({ pid: rec.pid, rule: rec.rule, value: rec.proposer.text, visible: true })
  }

  render () {
    const { model: { myApprovalList } } = this.props
    const { pid, rule, value, visible } = this.state

    return (
      <Card>
        <Table
          size='small'
          rowKey='id'
          pagination={{ pageSize: 50 }}
          columns={this.columns}
          rowClassName={(rec, index) => index % 2 ? styles.backgroundGray : ''}
          dataSource={myApprovalList}
        />
        <Drawer closable={false} visible={visible} placement='top' onClose={() => { this.setState({ visible: false }) }}>
          <ApprovalDetailComponent id={pid} rule={rule} value={value} />
        </Drawer>
      </Card>
    )
  }
}

export default AdminApprovalMyApprovals
