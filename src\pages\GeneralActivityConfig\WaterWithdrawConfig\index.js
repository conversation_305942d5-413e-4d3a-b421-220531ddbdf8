import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, message, Row, Col, Table, Typography, Divider, Space, Button, InputNumber, Modal } from 'antd'
const { Title, Link, Text } = Typography

const namespace = 'waterWithdraw'

@connect(({ waterWithdraw }) => ({
  model: waterWithdraw
}))

class WaterWithdraw extends Component {
  state = {
    rawConfig: {},
    hasUpdate: false,
    isEditing: false,
    addModalVisible: false,
    newLimit: 0,
    newLimitIsCash: true
  }
  componentDidMount = () => {
    this.getConfigData()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 查询数据配置
  getConfigData = () => {
    this.callModel('getConfigData', {
      cbFunc: (ret) => {
        console.debug('ret==>', ret)
        const { status, msg, list } = ret
        if (status !== 0) {
          message.warn(`获取数据失败: status=${status} msg=${msg}`)
          return
        }
        console.debug('list===>', list)
        this.setState({ rawConfig: list, isEditing: false })
      }
    })
  }

  // 数据转换
  parseCashDataSouce = (raw, isCash) => {
    let { withdrawLimit, withdrawStat } = raw
    if (!isCash) {
      withdrawLimit = raw.exchangeLimit
      withdrawStat = raw.exchangeStat
    }
    let dataSource = []
    for (let level in withdrawLimit) {
      dataSource.push({ level: level, withdrawLimit: withdrawLimit[level] })
    }
    for (let level in withdrawStat) {
      let stat = withdrawStat[level]
      let pos = dataSource.findIndex(item => { return item.level === level })
      if (pos >= 0) {
        dataSource[pos].stat = stat
      }
    }
    return dataSource
  }

  // 修改 暂存配置
  updateTempConrfig = (before, isCash, level, newLimit) => {
    let cp = { ...before }
    if (isCash) {
      typeof newLimit === 'number' ? cp.withdrawLimit[level] = newLimit : delete cp.withdrawLimit[level]
    } else {
      typeof newLimit === 'number' ? cp.exchangeLimit[level] = newLimit : delete cp.exchangeLimit[level]
    }
    this.setState({ rawConfig: cp, hasUpdate: true })
  }

  // 提交本地修改
  submitTempConfig = (data) => {
    this.callModel('updateConfigData', {
      params: data,
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error(`修改错误: status=${status}  msg=${msg}`)
          return
        }
        message.info('修改成功！')
        this.getConfigData()
      }
    })
  }

  render () {
    const { route } = this.props
    const { rawConfig, hasUpdate, isEditing, addModalVisible, newLimit, newLimitIsCash } = this.state

    const columsCash = [
      { title: '门槛/元', dataIndex: 'level', sorter: (a, b) => { return a.level > b.level ? 1 : -1 }, render: (v, r) => { return v / 100 } },
      { title: '份额', dataIndex: 'withdrawLimit', sorter: (a, b) => { return a.withdrawLimit > b.withdrawLimit ? 1 : -1 }, render: (v, r) => { return !isEditing ? v : <InputNumber value={v} onChange={value => this.updateTempConrfig(rawConfig, true, r.level, value)} /> } },
      { title: '总金额/元', dataIndex: 'level', render: (v, r) => { return r.level * r.withdrawLimit / 100 } },
      { title: '当前消耗份额', dataIndex: 'stat', sorter: (a, b) => { return a.stat > b.stat ? 1 : -1 }, render: (v, r) => { return v || 0 } },
      { title: '当前消耗金额/元', dataIndex: 'stat', render: (v, r) => { return v ? v * r.level / 100 : 0 } },
      { title: '操作', dataIndex: 'level', render: (v, r) => { return isEditing ? <Space><Link type='danger' onClick={() => this.updateTempConrfig(rawConfig, true, r.level, null)} >删除</Link></Space> : '-' } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    const cycstleCash = [
      { title: '门槛/元', dataIndex: 'level', sorter: (a, b) => { return a.level > b.level ? 1 : -1 }, render: (v, r) => { return v / 100 } },
      { title: '份额', dataIndex: 'withdrawLimit', sorter: (a, b) => { return a.withdrawLimit > b.withdrawLimit ? 1 : -1 }, render: (v, r) => { return !isEditing ? v : <InputNumber value={v} onChange={value => this.updateTempConrfig(rawConfig, false, r.level, value)} /> } },
      { title: '总金额/元', dataIndex: 'level', render: (v, r) => { return r.level * r.withdrawLimit / 100 } },
      { title: '当前消耗份额', dataIndex: 'stat', sorter: (a, b) => { return a.stat > b.stat ? 1 : -1 }, render: (v, r) => { return v || 0 } },
      { title: '当前消耗金额/元', dataIndex: 'stat', render: (v, r) => { return v ? v * r.level / 100 : 0 } },
      { title: '操作', dataIndex: 'level', render: (v, r) => { return isEditing ? <Space><Link type='danger' onClick={() => this.updateTempConrfig(rawConfig, false, r.level, null)} >删除</Link></Space> : '-' } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Row>
            <Col span={24} >
              <Space>
                <Button type='primary' hidden={isEditing} onClick={() => this.setState({ isEditing: true })}>修改</Button>
                <Button type='primary' hidden={!isEditing} disabled={!hasUpdate} onClick={() => this.submitTempConfig(rawConfig)} >提交当前配置</Button>
                <Button type='primary' hidden={!isEditing} onClick={() => this.getConfigData()}>取消</Button>
              </Space>
            </Col>
            <Divider />
            <Col span={24}>
              <Title level={5}>提现设置-现金</Title>
              <Link hidden={!isEditing} onClick={() => this.setState({ newLimitIsCash: true, addModalVisible: true })} >新增</Link>
            </Col>
            <Col span={24}>
              <Table size='small' columns={columsCash} dataSource={this.parseCashDataSouce(rawConfig, true)} pagination={false} />
            </Col>
            <br />
            <Col span={24}>
              <Title level={5}>提现设置-红钻</Title>
              <Link hidden={!isEditing} onClick={() => this.setState({ newLimitIsCash: false, addModalVisible: true })} >新增</Link>
            </Col>
            <Col span={24}>
              <Table size='small' columns={cycstleCash} dataSource={this.parseCashDataSouce(rawConfig, false)} pagination={false} />
            </Col>
          </Row>
          <Modal visible={addModalVisible} onCancel={() => { this.setState({ addModalVisible: false }) }} title={null} footer={null}>
            <Text>{newLimitIsCash ? '请输入提现门槛' : '请输入兑换红钻门槛' } (单位:分)</Text><br />
            <InputNumber onChange={v => this.setState({ newLimit: v })} />
            <Button onClick={() => { this.updateTempConrfig(rawConfig, newLimitIsCash, newLimit, 0); this.setState({ addModalVisible: false }) }}>确认添加</Button>
          </Modal>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default WaterWithdraw
