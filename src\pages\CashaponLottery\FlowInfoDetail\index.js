import React, { Component } from 'react'
// import dateString from '@/utils/dateString'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Button, Table, Card, Form, Divider, DatePicker, Input, message } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const namespace = 'hatkingBetDetail'
const getListUri = `${namespace}/getList`

// const dateFormat = 'YYYY-MM-DD HH:mm'
// const { RangePicker } = DatePicker

@connect(({ hatkingBetDetail }) => ({
  model: hatkingBetDetail
}))
class HatkingBetDetail extends Component {
  // 定义列表结构，
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    // { title: '竞猜模式', dataIndex: 'mode', align: 'center', render: (text, record) => (text === 1 ? '单个(X8)' : text === 2 ? '相邻2个(X4)' : '一列3个(X2)') },
    { title: '充值(YB)', dataIndex: 'charge', align: 'center' },
    { title: '抽奖(YB)', dataIndex: 'lottery', align: 'center' },
    { title: '获赠(YB)', dataIndex: 'recv', align: 'center' },
    { title: '赠出(YB)', dataIndex: 'send', align: 'center' }
    // { title: '竞猜时间', dataIndex: 'betTime', align: 'center', render: text => dateString(text) },
    // { title: '竞猜轮次', dataIndex: 'roundStart', align: 'center', render: text => dateString(text) },
    // { title: '获奖位置', dataIndex: 'winPosition', align: 'center' }
    // { title: '是否猜中', dataIndex: 'isWinner', align: 'center', render: (text, record) => (text === 0 ? '否' : '是') },
    // { title: '奖品发放', dataIndex: 'hasIssued', align: 'center', render: (text, record) => (text === 0 ? '否' : '是') }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  pagination = { pageSizeOptions: ['20', '50', '100'], showSizeChanger: true, pageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, startValue: null, endValue: null, uid: 0 }

  // 获取列表
  componentDidMount () {
    const { dispatch, model: { list } } = this.props
    dispatch({
      type: getListUri
    })

    this.setState({ list })
  }

  disabledStartDate = (startValue) => {
    const endValue = this.state.endValue
    if (!startValue || !endValue) {
      return false
    }
    return startValue.valueOf() > endValue.valueOf()
  }

  disabledEndDate = (endValue) => {
    const startValue = this.state.startValue
    if (!endValue || !startValue) {
      return false
    }
    return endValue.valueOf() <= startValue.valueOf()
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  onClick = () => {
    const { dispatch } = this.props
    const { startValue, endValue, uid } = this.state
    if (uid <= 0) {
      message.error('uid不能为空且大于0')
      return
    }
    var data = { startDate: moment(startValue).format('YYYY-MM-DD'), endDate: moment(endValue).format('YYYY-MM-DD'), uid: uid }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  render () {
    const { route, model: { list } } = this.props
    const { startValue, endValue } = this.state
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <div>
            UID
            <Input onChange={e => this.setState({ uid: e.target.value })} style={{ marginLeft: 10, width: 150 }} /> {/* 搜索按钮 */}
            <span style={{ marginLeft: 10 }}>时间范围</span>
            <DatePicker
              disabledDate={this.disabledStartDate}
              // showTime={{ format: 'HH:mm' }}
              format='YYYY-MM-DD'
              value={startValue}
              placeholder='开始时间'
              onChange={this.onStartChange}
              style={{ marginLeft: 10 }}
            />
            <span style={{ marginLeft: 10 }}>~</span>
            <DatePicker
              disabledDate={this.disabledEndDate}
              // showTime={{ format: 'HH:mm' }}
              format='YYYY-MM-DD'
              value={endValue}
              placeholder='结束时间'
              onChange={this.onEndChange}
              style={{ marginLeft: 10 }}
            />
            <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
            {/* <font style={{ marginLeft: 100 }} color='red'>单位（扭蛋券数）</font> */}
          </div>
          <Divider />
          <Form>
            <Table dataSource={list} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
          </Form>
        </Card>
      </PageHeaderWrapper>
    )
  }
}
export default HatkingBetDetail
