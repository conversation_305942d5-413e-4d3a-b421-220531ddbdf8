import {
  Card,
  Form,
  Table,
  // Typography,
  // message,
  Divider, Button, Input
} from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import exportExcel from '@/utils/exportExcel'
import moment from 'moment'

import { getExportFinishDescDetail, getFinishDescDetail } from './common'

const namespace = 'hatSuperCompereTask'
const getListUri = `${namespace}/getTaskProgress`

@connect(({ taskProgressReport }) => ({
  model: taskProgressReport
}))

class TaskProgressReport extends Component {
  constructor (props) {
    super(props)

    const { dataInfo, progressSummaryInfo } = props
    // console.log('TaskProgressReport dataInfo', dataInfo)
    this.state = { list: dataInfo, summaryInfo: progressSummaryInfo, searchASID: 0, searchIMID: 0 }
  }

  componentDidMount () {
    this.tick()
    this.timerID = setInterval(
      () => this.tick(),
      600000
    )
  }

  onClick = () => {
    this.tick()
  }

  // 定时刷新数据
  tick () {
    const { dispatch } = this.props
    const { searchASID } = this.state
    let data = { asid: searchASID }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  componentWillReceiveProps (nextProps) {
    const { dataInfo, progressSummaryInfo } = nextProps
    this.setState({ list: dataInfo, summaryInfo: progressSummaryInfo })
  }

  state = {
    visible: false,
    isUpdate: false,
    isNotifyValue: true,
    searchInput: '',
    searchKeyword: '',
    channel: { key: -1 },
    gameType: { key: -1 }
  }

  tableInitValue = {}

  columns = [
    { title: '任务时间', width: 50, dataIndex: 'month', fixed: 'left' },
    { title: '短位ID', width: 50, dataIndex: 'asid', fixed: 'left' },
    { title: '主持YY号', width: 50, dataIndex: 'imid', fixed: 'left' },
    { title: '昵称', dataIndex: 'nick', align: 'center', fixed: 'left' },
    { title: '公会礼物流水/元', dataIndex: 'guildAmount', fixed: 'left' },
    { title: '主持盖章流水/元', width: 50, dataIndex: 'compereSealAmount', fixed: 'left' },
    {
      title: '基础任务',
      children: [
        {
          title: '任务完成情况',
          dataIndex: 'baseTaskStatus',
          align: 'center',
          render: (text, record) => (getFinishDescDetail(record.baseTask))
        },
        {
          title: '达标获得奖励/元',
          dataIndex: 'baseTaskReward',
          align: 'center',
          render: (text, record) => (record.baseTask.systemReward)
        }
      ]
    },
    {
      title: '进阶任务',
      children: [
        {
          title: '任务完成情况',
          dataIndex: 'advancedTaskStatus',
          align: 'center',
          render: (text, record) => (getFinishDescDetail(record.advancedTask))
        },
        {
          title: '达标获得奖励/元',
          dataIndex: 'advancedTaskReward',
          align: 'center',
          render: (text, record) => (record.advancedTask.systemReward)
        }
      ]
    },
    {
      title: '冲刺任务',
      children: [
        {
          title: '任务完成情况',
          dataIndex: 'sprintTaskStatus',
          align: 'center',
          render: (text, record) => (getFinishDescDetail(record.sprintTask))
        },
        {
          title: '达标获得奖励/元',
          dataIndex: 'sprintTaskReward',
          align: 'center',
          render: (text, record) => (record.sprintTask.systemReward)
        }
      ]
    }
  ].map(item => {
    item.align = 'center'
    item.ellipsis = true
    return item
  })

  pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  getFilterList = () => {
    const { list } = this.state
    const { searchASID, searchIMID } = this.state
    let filterList = list
    if (parseInt(searchASID) > 0) {
      filterList = filterList.filter((v) => { return v.asid === parseInt(searchASID) })
    }
    if (parseInt(searchIMID) > 0) {
      filterList = filterList.filter((v) => { return v.imid === parseInt(searchIMID) })
    }
    return filterList
  }

  showModal = (isUpdate, record) => () => {
    if (record == null) {
      record = this.tableInitValue
    }
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(record)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate })
  }

  onExport = () => {
    let list = this.getFilterList()

    let exportData = list.map(item => {
      let v = $.extend(true, {}, item)

      v.baseTaskStatus = getExportFinishDescDetail(v.baseTask)
      v.baseTaskReward = v.baseTask.systemReward
      delete v.baseTask

      v.advancedTaskStatus = getExportFinishDescDetail(v.advancedTask)
      v.advancedTaskReward = v.advancedTask.systemReward
      delete v.advancedTask

      v.sprintTaskStatus = getExportFinishDescDetail(v.sprintTask)
      v.sprintTaskReward = v.sprintTask.systemReward
      delete v.sprintTask

      return v
    })
    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.export === undefined || col.export) {
        if (Array.isArray(col.children)) {
          for (let i = 0; i < col.children.length; i++) {
            exportHeader.push({ key: col.children[i].dataIndex, header: col.title + '-' + col.children[i].title })
          }
        } else {
          exportHeader.push({ key: col.dataIndex, header: col.title })
        }
      }
    })
    let fileName = '帽子超主流水激励任务完成进度' + moment().format('YYYYMMDD') + '.xlsx'
    exportExcel(exportHeader, exportData, fileName)
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  render () {
    const { summaryInfo } = this.state
    return (
      <Card>
        <Form>
          <Divider type='vertical' />
          短位ID：
          <Input style={{ width: 100, marginRight: 10 }} placeholder='请输入' onChange={(e) => { this.setState({ 'searchASID': e.target.value }) }} />
          主持YY号：
          <Input style={{ width: 100, marginRight: 10 }} placeholder='请输入' onChange={(e) => { this.setState({ 'searchIMID': e.target.value }) }} />

          {/* <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button> */}
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onExport}>导出</Button>
          <Divider type='vertical' />
          <p style={{ marginTop: 10, color: 'red' }}> {summaryInfo.year}年{summaryInfo.month}月任务 </p>
          <p style={{ marginTop: 10, color: 'black' }}>1. 整体情况：授权帽子超级主持{summaryInfo.hatSuperCount}人，获得任务主持{summaryInfo.taskCompereCount}人。目前合计盖章流水{summaryInfo.totalSealAmount}元，{summaryInfo.reachTaskCompere}人达标任务 </p>
          <Table style={{ marginTop: 10 }} dataSource={this.getFilterList()} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' scroll={{ x: 'max-content' }} />
        </Form>
      </Card>
    )
  }
}

export default TaskProgressReport
