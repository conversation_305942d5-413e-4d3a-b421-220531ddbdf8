/* eslint-disable eqeqeq */
import { getLists, blackListAdd, blackListDel } from './api'
import { Modal, message } from 'antd'
import { checkUid } from '@/utils/common'
export default {
  namespace: 'compereCreateActivityBlackList',
  state: {
    updating: false, // 添加黑名单事件是否在处理中
    displayData: [],
    selectUid: 0, // 删除黑名单选中的uid
    confirmMsg: '确认要删除吗？', // 删除白名单确认框提示信息
    newUid: ''
  },

  reducers: {
    // 更新data到黑名单数据列表
    displayList (state, { payload: data }) {
      if (!Array.isArray(data)) {
        console.error('unexpect data type: data=', data)
        return
      }
      return {
        ...state,
        displayData: data
      }
    },
    // 设置‘添加标签页-添加’按钮的状态，true为正在处理中
    setBtnStatus (state, { payload: status }) {
      if (status !== true && status !== false) {
        console.error('unexpect argument in setBtnStatus: status=', status)
        Modal.error('发生错误，请查看控制台')
        return
      }
      return {
        ...state,
        updating: status
      }
    }
  },

  effects: {
    // 请求并刷新主持开播黑名单列表数据
    * getBlackListData ({ params }, { select, call, put }) {
      let resp = yield call(getLists)
      let { data: { status, compereList } } = resp
      if (compereList === null) {
        message.warning('数据为空')
        yield put({
          type: 'displayList',
          payload: []
        })
        return
      }
      if (status !== 0 || !Array.isArray(compereList)) {
        console.error('getBlackListData() get data error: response=', resp)
        Modal.error({ content: '获取主持开播黑名单数据失败，请检查控制台' })
        return
      }
      for (let i = 0; i < compereList.length; i++) {
        compereList[i].idx = i + 1
      }
      yield put({
        type: 'displayList',
        payload: compereList
      })
    },
    // 添加主持开播黑名单
    * addBlackListByUid ({ payload }, { call, put }) {
      const { newUid, callback } = payload
      if (!checkUid(newUid)) {
        return
      }
      yield put({
        type: 'setBtnStatus',
        payload: true
      })
      let resp = yield call(blackListAdd, newUid)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[添加黑名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 1) {
        message.success('添加成功')
        if (callback) {
          callback()
        }
      } else {
        console.error('addBlackListByUid()：[添加主持开播黑名单] 返回结果为：', resp)
        Modal.warn({ content: '添加失败, 请检查控制台' })
      }
      yield put({
        type: 'setBtnStatus',
        payload: false
      })
    },
    // 删除主持开播黑名单
    * delBlackListByUid ({ payload }, { call, put }) {
      const uid = payload
      if (!checkUid(uid)) {
        return
      }
      let resp = yield call(blackListDel, uid)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 1) {
        message.success('删除成功')
        yield put({ // 更新列表
          type: 'getBlackListData'
        })
      } else {
        Modal.error({ content: '删除失败：status=' + status })
        console.error('delBlackListByUid() resp=', resp)
      }
    }
  }
}
