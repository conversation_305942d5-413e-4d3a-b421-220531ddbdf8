/* eslint-disable no-template-curly-in-string */
import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Row, Typography, Col, Table, Space, Divider, DatePicker, Select, Button } from 'antd'
import { onExportExcel } from '@/utils/common'
import { statTypeOptions, userTypeOptions, optionsFormater, valueFormater } from '../statCommon'
import moment from 'moment'

const { Text } = Typography
const namespace = 'cupidStat'

@connect(({ cupidStat }) => ({
  model: cupidStat
}))

class NewUserList extends Component {
  state = {
    timeRange: [moment().subtract(7, 'day'), moment()],
    selectStatType: 1000,
    selectBallType: 0
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  componentDidMount = () => {
    this.queryNewUserList()
  }

  // 查询数据
  queryNewUserList = () => {
    const { timeRange, selectStatType, selectBallType } = this.state
    const { originID } = this.props
    const [ t1, t2 ] = timeRange

    this.callModel('getNewUserList', {
      params: {
        start: t1.format('YYYYMMDD'),
        end: t2.format('YYYYMMDD'),
        statType: selectStatType,
        ballType: selectBallType,
        originId: originID
      }
    })
  }

  render () {
    const { timeRange, selectStatType } = this.state
    const { newUserList } = this.props.model

    const columns = [
      { title: '日期', dataIndex: 'date' },
      { title: '用户类型', dataIndex: 'newUser', render: (v) => { return optionsFormater(userTypeOptions, v) } },
      { title: '用户数', dataIndex: 'totalCount', render: (v) => { return v } },
      { title: '总投入', dataIndex: 'totalIn', render: (v) => { return valueFormater(v) } },
      { title: '次日留存', dataIndex: 'dayUid1' },
      { title: '7日留存', dataIndex: 'dayUid7' },
      { title: '30日留存', dataIndex: 'dayUid30' },
      { title: '次日参与流水留存', dataIndex: 'dayFlow1' },
      { title: '7日参与流水留存', dataIndex: 'dayFlow7' },
      { title: '30日参与流水留存', dataIndex: 'dayFlow30' }
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <Card>
        <Row>
          <Col span={24} style={{ marginBottom: '1em' }}>
            {/* 筛选项 */}
            <Space>
              <Text>时间范围:</Text>
              <DatePicker.RangePicker format='YYYY-MM-DD' value={timeRange} onChange={v => { this.setState({ timeRange: v }) }} />
              <Divider type='vertical' />

              <Text>渠道：</Text>
              <Select style={{ width: '7em' }} options={statTypeOptions} value={selectStatType} onChange={(v) => this.setState({ selectStatType: v })} />
              <Divider type='vertical' />

              <Button type='primary' onClick={() => this.queryNewUserList()}>查询</Button>
              <Button disabled={newUserList.length === 0}
                onClick={() => { onExportExcel(columns, newUserList, 'TOP10用户.xlsx') }}>导出</Button>
            </Space>
          </Col>
          <Col span={24}>
            <Table columns={columns} dataSource={newUserList} />
          </Col>
        </Row>
      </Card>
    )
  }
}

export default NewUserList
