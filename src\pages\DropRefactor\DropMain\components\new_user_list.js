import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, DatePicker, Button, Typography, Divider, Modal, Table, Row, Col, Input, Space, Tooltip, Popconfirm } from 'antd'
import { PlusOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { parseNumberList, timeFormater, exportTable2 } from '@/utils/common'
import message from 'antd/lib/message'
var moment = require('moment')

const namespace = 'dropMain'

@connect(({ dropMain }) => ({
  model: dropMain
}))

class NewUserList extends Component {
  state = {
    modalVisible: false,
    importType: 'normal',
    playerListStr: '',
    selectTimeRange: [moment().startOf('day'), moment().endOf('day')],
    exportTimeRange: null
  }
  componentDidMount = () => {
    this.refreshUserList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 查询目标用户列表
  refreshUserList = () => {
    const { selectTimeRange } = this.state
    this.callModel('getNewUserList', {
      startTime: selectTimeRange[0].unix(),
      endTime: selectTimeRange[1].unix(),
      cbFunc: (resp) => {
        if (resp === 1) {
          message.warning('数据为空')
          this.setState({ exportTimeRange: null })
        } else {
          this.setState({ exportTimeRange: selectTimeRange })
        }
      }
    })
  }
  // 导入目标用户
  importNewPlayer = () => {
    const { importType, playerListStr } = this.state
    let playerList = parseNumberList(playerListStr)
    if (!playerList) {
      message.warning('uid列表输入有误, 请检查~')
      return
    }
    if (playerList.length > 200) {
      message.warning('批量导出数量不得超过200')
      return
    }
    this.callModel('importNewPlayer', {
      params: {
        userType: importType,
        uidList: playerList
      },
      cbFunc: (ok) => {
        if (ok) {
          message.success('导入成功')
          this.refreshUserList()
        } else {
          message.error('导入失败,请联系管理员～')
        }
      }
    })
  }
  // 使uid失效
  disableUID = (uid) => {
    this.callModel('disableUID', {
      uid: uid,
      cbFunc: (resp) => {
        if (resp === 1) {
          message.success('操作成功')
          this.refreshUserList()
        } else {
          message.warning('操作失败，请稍后再试')
        }
      }
    })
  }
  // 表头显示提示语
  genColumnTooltip = (title) => {
    return {
      filterDropdown: (<span />),
      filterIcon: (
        <Tooltip placement='top' title={title}>
          <QuestionCircleOutlined style={{ fontSize: '16px' }} />
        </Tooltip>
      )
    }
  }
  // 导出报表-浏览器方式导出
  exportTable = (columns, dataSource) => {
    const { exportTimeRange } = this.state
    let startTime = exportTimeRange[0].format('YY年M月D日-H时m分S秒')
    let endTime = exportTimeRange[1].format('YY年M月D日-H时m分S秒')
    exportTable2(columns, dataSource, ['操作'], `${startTime}~${endTime}_新用户列表.xlsx`)
  }
  // 导出报表-服务器导出
  exportTable2 = () => {
    const { selectTimeRange: [startTime, endTime] } = this.state
    let url = `/drop/admin/get_new_player_user_list?startTime=${startTime.unix()}&endTime=${endTime.unix()}&type=xlsx`
    window.open(url)
  }

  columnsFixer = (before) => {
    for (let i = 0; i < before.length; i++) {
      before[i].align = 'center'
    }
    return before
  }
  render () {
    const { newUserList } = this.props.model
    const { modalVisible, importType, selectTimeRange } = this.state
    const { Text } = Typography

    const columns = [
      { title: '序号', dataIndex: 'index' },
      { title: 'UID', dataIndex: 'uid' },
      { title: '昵称', dataIndex: 'nick' },
      { title: '添加时间', dataIndex: 'timestamp', width: '13em', render: (v) => timeFormater(v), ...this.genColumnTooltip('开始成为新用户的时间') },
      // { title: '用户类型', dataIndex: 'userType', render: (v) => ['普通用户', '有效的新用户', '完成整个策略的失效新用户', '策略过期的失效新用户', '手动失效的新用户'][v] },
      { title: '命中策略状态', dataIndex: 'userType', render: (v, r) => { return ['未中策略', r.group > 0 ? '已中策略-有效-体验策略中' : '未中策略', '已中策略-失效-到期前已完成所有策略', '已中策略-失效-到期前未完成所有策略', '手动强制失效'][v] } },
      { title: '来源渠道', dataIndex: 'opType', render: (v) => ['系统生成', '运营导入普通', '运营导入特殊'][v] },
      { title: '用户组', dataIndex: 'group', render: (v) => ['未知', '实验组', '对照组'][v] },
      { title: '命中策略时间', dataIndex: 'startTime', width: '13em', render: (v) => { return v === 0 ? '未开始' : timeFormater(v) }, ...this.genColumnTooltip('成为新用户后第一次空投的时间') },
      { title: '操作',
        render: (v, r, i) => {
          return (
            <Popconfirm title='确认让该UID失效么?' onConfirm={() => { this.disableUID(r.uid) }}>
              <a hidden={r.userType === 4}>强制失效</a>
            </Popconfirm>
          )
        } }
    ]
    return (
      <Card>
        <Row>
          <Space>
            <Button onClick={() => { this.setState({ modalVisible: true }) }}><PlusOutlined />添加</Button>
            <Divider type='vertical' />
            <DatePicker.RangePicker showTime value={selectTimeRange} format='YYYY-MM-DD HH:mm:ss' onChange={(v) => this.setState({ selectTimeRange: v })} />
            <Button type='primary' onClick={e => this.refreshUserList()}>查询</Button>
            {/* <Button type='dashed' disabled={!selectTimeRange} onClick={e => { this.exportTable(columns, newUserList) }}>导出</Button> */}
            <Button type='dashed' disabled={!selectTimeRange} onClick={e => { this.exportTable2() }}>导出</Button>
            <Divider type='vertical' />
            <Text type='secondary'>说明： 1.查询包括：手动添加UID, 系统每日自动更新UID; 2. 状态设置为失效用户不会命令策略</Text>
          </Space>
        </Row>
        <Divider />
        <Row>
          <Col span={24}>
            <Table columns={this.columnsFixer(columns)} dataSource={newUserList} />
          </Col>
        </Row>
        <Modal visible={modalVisible} title='批量添加目标用户' okText='确认添加'
          onCancel={() => this.setState({ modalVisible: false })}
          onOk={() => this.importNewPlayer()}>
          <Space style={{ marginBottom: '1em' }}>
            <Button type={importType === 'normal' ? 'primary' : 'dashed'} onClick={() => { this.setState({ importType: 'normal' }) }} >普通用户</Button>
            <Button type={importType === 'special' ? 'primary' : 'dashed'} onClick={() => { this.setState({ importType: 'special' }) }} >特殊用户</Button>
          </Space>
          <Input.TextArea placeholder='请输入uid，输入多个时英文逗号或换行符隔开' onChange={(e) => this.setState({ playerListStr: e.target.value })} />
          <Row>
            <Text type='secondary'>添加说明：<br /> 1. 普通用户执行用户判断标准+风控规则; <br />2. 特殊用户直接执行风控规则; <br />3. 添加默认状态为生效状态; <br />4. 添加后当日即生效; </Text>
          </Row>
        </Modal>
      </Card>
    )
  }
}

export default NewUserList
