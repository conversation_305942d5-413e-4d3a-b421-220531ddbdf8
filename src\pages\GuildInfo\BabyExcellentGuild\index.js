import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import dateString from '@/utils/dateString'
import { Card, Table, Input, Modal, Form, Button, InputNumber, Popover } from 'antd'
import { stringify } from 'qs'

const namespace = 'babyExcellentGuild'

@connect(({ babyExcellentGuild }) => ({
  model: babyExcellentGuild
}))

class BabyExcellentGuild extends Component {
  constructor (props) {
    super(props)

    this.refreshInfo()
  }

  columns = [
    { title: '公会sid', dataIndex: 'sid', align: 'center' },
    { title: '添加时间', dataIndex: 'createDate', align: 'center', render: (text, record) => (record.createDate === 0 ? '' : dateString(record.createDate)) },
    { title: '操作人', dataIndex: 'operator', align: 'center' },
    { title: '操作', key: 'operation', align: 'center', render: (text, record) => (<span><Popover content={this.renderContent(record)} trigger='click'><a>删除</a></Popover></span>) }
  ]

  state = {
    visible: false
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  refreshInfo = () => {
    let data = this.getParam()
    console.log(data)
    this.props.dispatch({
      type: `${namespace}/listInfo`,
      payload: data
    })
  }

  renderContent = (record) => {
    return (
      <div>
        <Input.TextArea onChange={this.onTextChange} row={5} placeholder={'删除原因选填，最多100字符'} />
        <Button onClick={this.deleteHandle(record)} style={{ marginLeft: 120, marginTop: 5 }} type='primary'>确定</Button>
      </div>
    )
  }

  onTextChange = e => {
    this.setState({ removeReason: e.target.value })
  }

  deleteHandle = (record) => () => {
    const { removeReason } = this.state
    const { dispatch } = this.props
    let data = { sid: Number(record.sid), removeReason: removeReason }
    console.log(data)
    dispatch({
      type: `${namespace}/deleteInfo`,
      payload: data
    })
  }

  showModel = () => () => {
    this.setState({ visible: true })
  }

  searchHandle = () => () => {
    this.refreshInfo()
  }

  exportURL = () => {
    let params = this.getParam()
    params['export'] = 1

    return `/guild/boss/bbexcellentguild/list?${stringify(params)}`
  }

  getParam = () => {
    const { searchSID, searchASID } = this.state

    let params = { sid: searchSID, asid: searchASID }
    return params
  }

  hiddenModal = () => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.setState({ visible: false })
  }

  handleCancel = e => {
    this.hiddenModal()
  }

  handleSubmit = e => {
    if (this.formRef) {
      this.formRef.submit()
    }
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onFinish = values => {
    const { dispatch } = this.props

    let data = { sid: Number(values.sid) }
    console.log(data)
    dispatch({
      type: `${namespace}/addInfo`,
      payload: data
    })

    this.hiddenModal()
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      var value = selectedRows.map(item => item.familyId).join(',')
      this.setState({ removeKey: value })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User',
      name: record.name
    })
  }

  render () {
    const { visible } = this.state
    const { route, model: { list } } = this.props

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 10 },
        sm: { span: 20 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <div><Button type='primary' onClick={this.showModel()}>添加</Button></div>
          <div style={{ marginTop: 10 }} />
          <span style={{ marginLeft: 15 }}>公会SID</span>
          <InputNumber min={0} onChange={e => this.setState({ searchSID: e })} style={{ width: 150, marginLeft: 3 }} />
          <span style={{ marginLeft: 15 }}>短位频道</span>
          <InputNumber min={0} onChange={e => this.setState({ searchASID: e })} style={{ width: 150, marginLeft: 3 }} />

          <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
          <Button style={{ marginLeft: 20 }} type='primary'><a href={this.exportURL()} target='_blank'>导出</a></Button>
          <Table style={{ marginTop: 10 }} tabelLayout='fixed' rowSelection={this.rowSelection} rowKey={(record, index) => index} bordered dataSource={list} columns={this.columns} pagination={this.defaultPageValue} />
        </Card>

        <Modal forceRender visible={visible} title='添加独家公会' onCancel={this.handleCancel} onOk={this.handleSubmit} okText='提交'>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <Form.Item label='公会sid' name='sid' rules={[{ required: true }]}>
              <Input style={{ width: 200 }} />
            </Form.Item>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default BabyExcellentGuild
