import {
  listTaskConfig,
  getConfirmTaskConfig,
  getAllConfirmTaskConfig,
  flushConfirmTaskConfig,
  approvalTaskConfig,
  geTaskConfigInvalid,
  debugInitSystemTaskConfig,
  getTaskProgress,
  listAwardDetail,
  listRewardConfirm,
  approvalRewardConfirm, flushRewardConfirm
} from './api'
import { message } from 'antd'

export default {
  namespace: 'hatSuperCompereTask',

  state: {
    taskConfigList: [],
    confirmTaskConfig: [],
    taskConfigInvalidList: [],
    tableLoadingInvalidWin: false,
    tableLoadingConfirmTaskConfigWin: false,
    confirmTaskConfigInfo: { title: '' },
    confirmTaskConfigCal: [],
    totalTaskConfig: 0,
    canJoinTaskConfig: 0,
    hatKingTaskVirtualTime: '',
    // 进度、明细
    taskProgressList: [],
    rewardDetailList: [],
    awardDetailList: [],
    progressSummaryInfo: { year: 0, month: 0, hatSuperCount: 0, taskCompereCount: 0, totalSealAmount: 0, reachTaskCompere: 0 },

    // 发奖确认
    rewardConfirmInfo: { list: [], remark: '', title: '', countCompere: 0, hadCompere: 0, reachCompere: 0, compereSealWater: 0 },
    tableLoadingRewardConfirm: false
  },

  reducers: {

    // 单个修改某个state成员
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },

    updateTaskConfigList (state, { payload }) {
      console.log('updateTaskConfigList', payload)
      return {
        ...state,
        taskConfigList: payload
      }
    },

    updateConfirmTaskConfig (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      for (let i = 0; i < payload.length; i++) {
        payload[i].idx = i + 1
      }
      return {
        ...state,
        confirmTaskConfig: payload
      }
    },

    taskConfigInvalidList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      for (let i = 0; i < payload.length; i++) {
        payload[i].idx = i + 1
      }
      return {
        ...state,
        taskConfigInvalidList: payload
      }
    },

    updateAwardDetail (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        awardDetailList: payload
      }
    },

    updateProgressList (state, { payload, summaryInfo }) {
      console.log('updateProgressList', payload, summaryInfo)
      return {
        ...state,
        taskProgressList: payload,
        progressSummaryInfo: summaryInfo
      }
    },

    updateRewardConfirm (state, { payload }) {
      return {
        ...state,
        rewardConfirmInfo: payload
      }
    }
  },

  effects: {
    // ------------------------任务配置-----------------------------------//
    * listTaskConfig ({ payload }, { call, put }) {
      try {
        let { data: { data, status, msg } } = yield call(listTaskConfig, payload)
        if (status !== 0) {
          message.error('错误' + msg)
          return
        }
        data = Array.isArray(data) ? data : []
        yield put({
          type: 'updateTaskConfigList',
          payload: data
        })
      } catch (e) {
        message.error('exception', e)
      }
    },

    * getConfirmTaskConfig ({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingConfirmTaskConfigWin', newValue: true }
      })

      let callbackFn = payload.callback
      if (callbackFn) {
        delete payload.callback
      }

      try {
        let { data: { data, status, msg } } = yield call(getConfirmTaskConfig, payload)
        if (status !== 0) {
          message.error('错误: ' + msg)
          yield put({
            type: 'updateState',
            payload: { name: 'tableLoadingConfirmTaskConfigWin', newValue: false }
          })
          if (callbackFn) {
            callbackFn(false, {})
          }
          return
        }
        let list = []
        list = data.list !== null && Array.isArray(data.list) ? data.list : []
        yield put({
          type: 'updateConfirmTaskConfig',
          payload: list
        })

        yield put({
          type: 'updateState',
          payload: { name: 'confirmTaskConfigInfo', newValue: { title: data.title } }
        })

        let confirmTaskConfigCal = []
        confirmTaskConfigCal.push({
          idx: 1,
          title1: '基础任务',
          systemSealWater: data.systemBase.targetAmount,
          systemReward: data.systemBase.reward,
          title2: '基础任务',
          configSealWater: data.configBase.targetAmount,
          configReward: data.configBase.reward
        })

        confirmTaskConfigCal.push({
          idx: 2,
          title1: '进阶任务',
          systemSealWater: data.systemAdvance.targetAmount,
          systemReward: data.systemAdvance.reward,
          title2: '基础任务',
          configSealWater: data.configAdvance.targetAmount,
          configReward: data.configAdvance.reward
        })

        confirmTaskConfigCal.push({
          idx: 3,
          title1: '冲刺任务',
          systemSealWater: data.systemSprint.targetAmount,
          systemReward: data.systemSprint.reward,

          title2: '冲刺任务',
          configSealWater: data.configSprint.targetAmount,
          configReward: data.configSprint.reward

        })

        yield put({
          type: 'updateState',
          payload: { name: 'confirmTaskConfigCal', newValue: confirmTaskConfigCal }
        })

        yield put({
          type: 'updateState',
          payload: { name: 'totalTaskConfig', newValue: data.total }
        })

        let confirmTaskConfigMap = {}
        for (let i = 0; i < list.length; i++) {
          confirmTaskConfigMap[list[i].uid] = {
            configSealWaterBase: list[i].hatKingTaskBaseInfo.configSealWater > 0 ? list[i].hatKingTaskBaseInfo.configSealWater / 10000 : '',
            configRewardBase: list[i].hatKingTaskBaseInfo.configReward > 0 ? list[i].hatKingTaskBaseInfo.configReward / 10000 : '',
            configSealWaterAdvanced: list[i].hatKingTaskAdvancedInfo.configSealWater > 0 ? list[i].hatKingTaskAdvancedInfo.configSealWater / 10000 : '',
            configRewardAdvanced: list[i].hatKingTaskAdvancedInfo.configReward > 0 ? list[i].hatKingTaskAdvancedInfo.configReward / 10000 : '',
            configSealWaterSprint: list[i].hatKingTaskSprintInfo.configSealWater > 0 ? list[i].hatKingTaskSprintInfo.configSealWater / 10000 : '',
            configRewardSprint: list[i].hatKingTaskSprintInfo.configReward > 0 ? list[i].hatKingTaskSprintInfo.configReward / 10000 : '',
            remark: '',
            isModify: true
          }

          list[i].configSealWaterBase = confirmTaskConfigMap[list[i].uid].configSealWaterBase
          list[i].configRewardBase = confirmTaskConfigMap[list[i].uid].configSealWaterBase

          list[i].configSealWaterAdvanced = confirmTaskConfigMap[list[i].uid].configSealWaterAdvanced
          list[i].configRewardAdvanced = confirmTaskConfigMap[list[i].uid].configRewardAdvanced

          list[i].configSealWaterSprint = confirmTaskConfigMap[list[i].uid].configSealWaterSprint
          list[i].configRewardSprint = confirmTaskConfigMap[list[i].uid].configRewardSprint
        }

        if (callbackFn) {
          callbackFn(true, confirmTaskConfigMap)
        }
      } catch (e) {
        message.error('exception', e)
      }

      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingConfirmTaskConfigWin', newValue: false }
      })
    },

    * getAllConfirmTaskConfig ({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingConfirmTaskConfigWin', newValue: true }
      })

      let callbackFn = payload.callback
      if (callbackFn) {
        delete payload.callback
      }

      try {
        let { data: { data, status, msg } } = yield call(getAllConfirmTaskConfig, payload)
        if (status !== 0) {
          message.error('错误: ' + msg)
          yield put({
            type: 'updateState',
            payload: { name: 'tableLoadingConfirmTaskConfigWin', newValue: false }
          })
          if (callbackFn) {
            callbackFn(false, {})
          }
          return
        }
        let list = []
        list = data.list !== null && Array.isArray(data.list) ? data.list : []
        yield put({
          type: 'updateConfirmTaskConfig',
          payload: list
        })

        console.log(list)
        yield put({
          type: 'updateState',
          payload: { name: 'confirmTaskConfigInfo', newValue: { title: data.title } }
        })

        let confirmTaskConfigCal = []
        confirmTaskConfigCal.push({
          idx: 1,
          title1: '基础任务',
          systemSealWater: data.systemBase.targetAmount,
          systemReward: data.systemBase.reward,
          title2: '基础任务',
          configSealWater: data.configBase.targetAmount,
          configReward: data.configBase.reward
        })

        confirmTaskConfigCal.push({
          idx: 2,
          title1: '进阶任务',
          systemSealWater: data.systemAdvance.targetAmount,
          systemReward: data.systemAdvance.reward,
          title2: '基础任务',
          configSealWater: data.configAdvance.targetAmount,
          configReward: data.configAdvance.reward
        })

        confirmTaskConfigCal.push({
          idx: 3,
          title1: '冲刺任务',
          systemSealWater: data.systemSprint.targetAmount,
          systemReward: data.systemSprint.reward,

          title2: '冲刺任务',
          configSealWater: data.configSprint.targetAmount,
          configReward: data.configSprint.reward

        })

        yield put({
          type: 'updateState',
          payload: { name: 'confirmTaskConfigCal', newValue: confirmTaskConfigCal }
        })

        yield put({
          type: 'updateState',
          payload: { name: 'totalTaskConfig', newValue: data.total }
        })

        let confirmTaskConfigMap = {}
        for (let i = 0; i < list.length; i++) {
          confirmTaskConfigMap[list[i].uid] = {
            configSealWaterBase: list[i].hatKingTaskBaseInfo.configSealWater > 0 ? list[i].hatKingTaskBaseInfo.configSealWater / 10000 : '',
            configRewardBase: list[i].hatKingTaskBaseInfo.configReward > 0 ? list[i].hatKingTaskBaseInfo.configReward / 10000 : '',
            configSealWaterAdvanced: list[i].hatKingTaskAdvancedInfo.configSealWater > 0 ? list[i].hatKingTaskAdvancedInfo.configSealWater / 10000 : '',
            configRewardAdvanced: list[i].hatKingTaskAdvancedInfo.configReward > 0 ? list[i].hatKingTaskAdvancedInfo.configReward / 10000 : '',
            configSealWaterSprint: list[i].hatKingTaskSprintInfo.configSealWater > 0 ? list[i].hatKingTaskSprintInfo.configSealWater / 10000 : '',
            configRewardSprint: list[i].hatKingTaskSprintInfo.configReward > 0 ? list[i].hatKingTaskSprintInfo.configReward / 10000 : '',
            remark: '',
            isModify: true
          }
        }

        if (callbackFn) {
          callbackFn(true, confirmTaskConfigMap)
        }
      } catch (e) {
        message.error('exception', e)
      }

      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingConfirmTaskConfigWin', newValue: false }
      })
    },

    * flushConfirmTaskConfig ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(flushConfirmTaskConfig, payload)
      if (status !== 0) {
        message.error('错误' + msg)
        return
      }
      console.log(data)

      let confirmTaskConfigCal = []
      confirmTaskConfigCal.push({
        idx: 1,
        title1: '基础任务',
        systemSealWater: data.systemBase.targetAmount,
        systemReward: data.systemBase.reward,
        title2: '基础任务',
        configSealWater: data.configBase.targetAmount,
        configReward: data.configBase.reward
      })

      confirmTaskConfigCal.push({
        idx: 2,
        title1: '进阶任务',
        systemSealWater: data.systemAdvance.targetAmount,
        systemReward: data.systemAdvance.reward,
        title2: '基础任务',
        configSealWater: data.configAdvance.targetAmount,
        configReward: data.configAdvance.reward
      })

      confirmTaskConfigCal.push({
        idx: 3,
        title1: '冲刺任务',
        systemSealWater: data.systemSprint.targetAmount,
        systemReward: data.systemSprint.reward,

        title2: '冲刺任务',
        configSealWater: data.configSprint.targetAmount,
        configReward: data.configSprint.reward

      })

      yield put({
        type: 'updateState',
        payload: { name: 'confirmTaskConfigCal', newValue: confirmTaskConfigCal }
      })

      yield put({
        type: 'updateState',
        payload: { name: 'canJoinTaskConfig', newValue: data.canJoinCount }
      })
    },

    * approvalTaskConfig ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(approvalTaskConfig, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        message.success('ok')
        yield put({
          type: 'listTaskConfig',
          payload: {}
        })
      }
    },

    * geTaskConfigInvalid ({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingInvalidWin', newValue: true }
      })

      try {
        let { data: { data, status, msg } } = yield call(geTaskConfigInvalid, payload)
        if (status !== 0) {
          message.error('错误: ' + msg)
          return
        }
        data = Array.isArray(data) ? data : []
        yield put({
          type: 'taskConfigInvalidList',
          payload: data
        })
      } catch (e) {
        message.error('exception', e)
      }

      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingInvalidWin', newValue: false }
      })
    },

    * debugInitSystemTaskConfig ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(debugInitSystemTaskConfig, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        message.success('ok')
        yield put({
          type: 'listTaskConfig',
          payload: {}
        })
      }
    },

    // * debugHatKingTaskVirtualTime ({ payload }, { call, put }) {
    //   let { data: { status, msg } } = yield call(debugHatKingTaskVirtualTime, payload)
    //   if (status !== 0) {
    //     message.error({ content: msg })
    //   } else {
    //     yield put({
    //       type: 'debugGetHatKingTaskVirtualTime',
    //       payload: {}
    //     })
    //   }
    // },

    * getTaskProgress ({ payload }, { call, put }) {
      try {
        let { data: { data, status, msg } } = yield call(getTaskProgress, payload)
        if (status !== 0) {
          message.error('错误' + msg)
          return
        }
        let list = Array.isArray(data.list) ? data.list : []
        yield put({
          type: 'updateProgressList',
          payload: list,
          summaryInfo: data.summaryInfo
        })
      } catch (e) {
        message.error('exception', e)
      }
    },

    * listAwardDetail ({ payload }, { call, put }) {
      try {
        let { data: { data, status, msg } } = yield call(listAwardDetail, payload)
        console.log(status, msg)
        data = Array.isArray(data) ? data : []
        for (let i = 0; i < data.length; i++) {
          data[i].idx = i + 1
        }
        yield put({
          type: 'updateAwardDetail',
          payload: data
        })
      } catch (e) {
        message.error('exception', e)
      }
    },
    // ------------------------任务配置-----------------------------------//

    // 发奖确认
    * listRewardConfirm ({ payload }, { call, put }) {
      try {
        let { data: { data, status, msg } } = yield call(listRewardConfirm, payload)
        if (status !== 0) {
          message.error('错误' + msg)
          return
        }
        data.list = Array.isArray(data.list) ? data.list : []
        for (let i = 0; i < data.list.length; i++) {
          data.list[i].idx = i
        }
        yield put({
          type: 'updateRewardConfirm',
          payload: data
        })
      } catch (e) {
        message.error('exception', e)
      }
    },

    * approvalRewardConfirm ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(approvalRewardConfirm, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        message.success('ok')
        yield put({
          type: 'listRewardConfirm',
          payload: {}
        })
      }
    },

    * approvalListRewardConfirm ({ payload }, { call, put }) {
      let callbackFn = payload.callback
      if (callbackFn) {
        delete payload.callback
      }

      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingRewardConfirm', newValue: true }
      })

      try {
        let { data: { data, status, msg } } = yield call(listRewardConfirm, payload)
        if (status !== 0) {
          message.error('错误' + msg)
          return
        }
        data.list = Array.isArray(data.list) ? data.list : []
        for (let i = 0; i < data.list.length; i++) {
          data.list[i].idx = i
        }

        if (callbackFn) {
          callbackFn(data)
        }

        yield put({
          type: 'updateRewardConfirm',
          payload: data
        })
        yield put({
          type: 'updateState',
          payload: { name: 'tableLoadingRewardConfirm', newValue: false }
        })
      } catch (e) {
        message.error(e)
      }
    },

    * flushRewardConfirm ({ payload }, { call, put }) {
      let callbackFn = payload.callback
      if (callbackFn) {
        delete payload.callback
      }

      let { data: { data, status, msg } } = yield call(flushRewardConfirm, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        if (callbackFn) {
          callbackFn(data)
        }
      }
    }
  }
}
