import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
// import dateString from '@/utils/dateString'
import { Table, Divider, Form, Card, Input, Modal, Popconfirm, DatePicker, TimePicker } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const namespace = 'BabyRunPoisonConfig'
const getListUri = `${namespace}/getList`
const addItemUri = `${namespace}/addItem`
const updateItemUri = `${namespace}/updateItem`
const confirmItemUri = `${namespace}/confirmItem`
const { RangePicker: DateRangePicker } = DatePicker
const { RangePicker: TimeRangePicker } = TimePicker
// const rmoveItem = `${namespace}/removeItem`
const FormItem = Form.Item

@connect(({ BabyRunPoisonConfig }) => ({
  model: BabyRunPoisonConfig
}))
class BabyRunPoisonConfig extends Component {
  // 列表结构
  columns = [
    { title: '业务',
      dataIndex: 'appId',
      key: 'appId',
      align: 'center',
      width: 100,
      render: (text, record) => {
        switch (text) {
          case 14: return '约战'
          case 36: return '宝贝'
        }
        return ''
      }
    },
    { title: '开启日期', dataIndex: 'dateRange', align: 'center', width: 220, render: text => text.map(i => moment.unix(i).format('YYYY-MM-DD')).join('~') },
    { title: '开启时段', dataIndex: 'timeRange', align: 'center', width: 180, render: text => text.map(i => moment.unix(i).format('HH:mm:ss')).join('~') },
    { title: '描述', dataIndex: 'describe', align: 'center' },
    { title: '状态',
      dataIndex: 'progress',
      key: 'progress',
      align: 'center',
      width: 100,
      render: (text, record) => {
        switch (text) {
          case 1: return '未开始'
          case 2: return '生效中'
          case 3: return '已结束'
        }
        return ''
      }
    },
    { title: '操作',
      align: 'center',
      render: (text, record) => (<Popconfirm onConfirm={this.showModal(true, record)} title='确认修改?' ><a>修改</a></Popconfirm>
      )
    }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  defaultValue = { appId: 36 }

  state = { visible: false, isUpdate: false, value: {} }

  // 获取列表
  componentDidMount () {
    this.getList()
  }

  getList () {
    const { dispatch } = this.props
    dispatch({
      type: getListUri,
      payload: { appId: 36 }
    })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // 添加 与 编辑
  onFinish = values => {
    const { dispatch } = this.props
    const { isUpdate } = this.state
    // transfer Moment object to timestamp !!!
    //  values.range = values.submitTime.unix()
    values.dateRange = values.dateRange.map(i => i.unix())
    values.timeRange = values.timeRange.map(i => i.unix())
    values.appId = 36
    console.log(values, isUpdate)
    var url = isUpdate ? updateItemUri : addItemUri
    dispatch({
      type: url,
      payload: values
    }).then(res => {
      this.getList()
    })
    this.setState({ visible: false })
  }

  // 显示弹窗
  showModal = (isUpdate, record) => e => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      let now = moment()
      v.dateRange = Array.isArray(v.dateRange) ? v.dateRange.map(i => moment.unix(i)) : [now, now]
      v.timeRange = Array.isArray(v.timeRange) ? v.timeRange.map(i => moment.unix(i)) : [now, now]
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, value: {}, isUpdate: isUpdate, title: isUpdate ? '更新' : '添加' })
    this.getList()
  }

  // 关闭弹窗
  hidModal = () => {
    this.setState({ visible: false })
  }

  // 删除
  handleRemove = record => e => {
    const { dispatch } = this.props
    const data = { id: record.id }
    console.log(record.id)
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    }).then(res => {
      this.getList()
    })
  }

  // 下线
  handleDown = record => e => {
    const { dispatch } = this.props
    const data = { id: record.id }
    console.log(record.id)
    dispatch({
      type: `${namespace}/downItem`,
      payload: data
    }).then(res => {
      this.getList()
    })
  }

  // 添加 与 编辑
  handleConfirm = record => e => {
    const { dispatch } = this.props
    const data = { id: record.id }
    console.log(record.id)
    var url = confirmItemUri
    dispatch({
      type: url,
      payload: data
    })
  }

  // 更新
  handleUpdate = id => () => {
    const { dispatch } = this.props
    var data = { id: id }
    dispatch({
      type: `${namespace}/addItem`,
      payload: data
    })
  }

  saveFormRef = formRef => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list, extra } } = this.props
    const { visible, title } = this.state
    const formLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    console.log(this.props)
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <div>注意事项:</div><div> 1、请刷新页面。</div><div> 2、请谨慎修改配置。</div><div> 3、设置开启时段请不要跨越一天，比如活动时段不能从前一天的晚上22点到第二天的凌晨1点</div><div> 4、如遇报错请及时联系开发</div>
            <br /><div><font color='red'>{extra}</font></div>
            <Divider />
            <Table dataSource={list} columns={this.columns} rowKey={(record, index) => index} pagination={this.defaultPageValue} />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hidModal} onOk={this.handleSubmit}>
          <Form {...formLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem label='开启日期' name='dateRange' rules={[{ type: 'array', required: true, message: 'Please select time!' }]}>
              <DateRangePicker style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='开启时段' name='timeRange' rules={[{ type: 'array', required: true, message: 'Please select time!' }]}>
              <TimeRangePicker style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='描述' name='describe'>
              <Input />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default BabyRunPoisonConfig
