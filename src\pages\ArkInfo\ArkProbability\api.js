import request from '@/utils/request'
import { stringify } from 'qs'

export function getProbabilitySetting (params) {
  return request(`/lottery_ark/boss/get_ark_probability_setting?${stringify(params)}`)
}

export function updateProbabilitySetting (params) {
  return request(`/lottery_ark/boss/update_ark_probability_setting`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function doApprovalFromServer (params) {
  return request(`/approval/admin/do_approval`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
