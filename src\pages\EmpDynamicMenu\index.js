import React, { Component } from 'react'
import EmpReactAdepter from '../../components/EmpReactAdepter'
import { withRouter } from 'dva/router'
import request from '../../utils/request'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'

class EmpDynamicMenu extends Component {
  // eslint-disable-next-line no-useless-constructor
  state = {
    config: null,
    status: -1
  }
  
  componentDidMount () {
    const { empId } = this.props.route
    // 请求配置信息
    request(`/fts_component/emp/component_config?id=${empId}`).then(res => {
      console.log('component_config==>', res)
      if (res.data.status === 0) {
        const { content } = res.data.payload
        try {
          const config = JSON.parse(content)
          this.setState({
            config,
            status: 1
          })
        } catch (e) {
          this.setState({
            status: 0
          })
        }
      }
    })
  }

  render () {
    const { status, config } = this.state
    const { route } = this.props

    if (status === -1) {
      return <div>加载中...</div>
    } else if (status === 0) {
      return <div>配置出错</div>
    }
    return (
      <PageHeaderWrapper title={route.name}>
        <EmpReactAdepter {...config} />
      </PageHeaderWrapper>
    ) 
  }
}

export default withRouter(EmpDynamicMenu)
