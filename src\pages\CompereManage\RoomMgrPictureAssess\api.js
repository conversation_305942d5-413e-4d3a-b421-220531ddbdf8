import request from '@/utils/request'
import { stringify } from 'qs'

// 获取列表 包含搜索
export function getList (params) {
  return request(`/recommend_data_base/boss/list_roommgr_picture?${stringify(params)}`)
}

export function updateItem (params) {
  return request(`/recommend_data_base/boss/update_roommgr_status?${stringify(params)}`)
}

export function batchApproval (params) {
  return request(`/recommend_data_base/boss/batch_update_roommgr_status`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
