import { getCompereLevelInfo, setCompereLevel, getUserLevelInfo, setUserLevel, getCompereLevelOptHistory, getUserLevelOptHistory } from './api'
import { message } from 'antd'

export default {
  namespace: 'levelInfo', // 全局唯一标识

  state: {
    list: [],
    listUser: [],
    listHistory: [],
    listUserHistory: [],
    loadingHistory: false,
    loadingUserHistory: false
  },

  reducers: {
    // 单个修改某个state成员
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },

    updateInfo (state, { payload }) {
      return {
        ...state,
        list: payload
      }
    },
    updateInfoUser (state, { payload }) {
      return {
        ...state,
        listUser: payload
      }
    },

    updateHistory (state, { payload }) {
      return {
        ...state,
        listHistory: payload
      }
    },
    updateUserHistory (state, { payload }) {
      return {
        ...state,
        listUserHistory: payload
      }
    }
  },

  effects: {
    * getCompereLevelInfo ({ payload }, { call, put }) {
      const { data: { status, msg, data } } = yield call(getCompereLevelInfo, payload)
      if (status !== 0) {
        message.error(msg)
      }
      let list = []
      if (data !== null) {
        list.push(data)
      }
      yield put({
        type: 'updateInfo',
        payload: list
      })
    },

    * setCompereLevel ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(setCompereLevel, payload.params)
        if (status === 0) {
          const cb = payload.cbFunc
          if (cb !== null) cb()
          message.success('已提交修改申请，请审批')
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('exception', e)
      }
    },

    * getCompereLevelHistory ({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: { name: 'loadingHistory', newValue: true }
      })

      const { data: { status, msg, data } } = yield call(getCompereLevelOptHistory, payload)
      if (status !== 0) {
        message.error(msg)
      }
      yield put({
        type: 'updateHistory',
        payload: data
      })

      yield put({
        type: 'updateState',
        payload: { name: 'loadingHistory', newValue: false }
      })
    },

    * getUserLevelInfo ({ payload }, { call, put }) {
      const { data: { status, msg, data } } = yield call(getUserLevelInfo, payload)
      if (status !== 0) {
        message.error(msg)
      }
      let list = []
      if (data !== null) {
        list.push(data)
      }
      yield put({
        type: 'updateInfoUser',
        payload: list
      })
    },

    * setUserLevel ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(setUserLevel, payload.params)
        if (status === 0) {
          const cb = payload.cbFunc
          if (cb !== null) cb()
          message.success('已提交修改申请，请审批')
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('exception', e)
      }
    },

    * getUserLevelHistory ({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: { name: 'loadingUserHistory', newValue: true }
      })

      const { data: { status, msg, data } } = yield call(getUserLevelOptHistory, payload)
      if (status !== 0) {
        message.error(msg)
      }
      yield put({
        type: 'updateUserHistory',
        payload: data
      })

      yield put({
        type: 'updateState',
        payload: { name: 'loadingUserHistory', newValue: false }
      })
    }
  }
}
