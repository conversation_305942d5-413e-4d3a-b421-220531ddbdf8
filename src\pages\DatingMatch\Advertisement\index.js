import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, message, Modal, Table, Row, Col, Typography, Space, Select, Button, Tooltip, Form, Input, InputNumber, DatePicker, Popconfirm } from 'antd'
import { ImagesList } from '@/components/SimpleComponents'
import moment from 'moment'
import InputPicture from '@/components/InputPicture'
const { Text, Link } = Typography

const namespace = 'advertisement'

const statusOptions = [
  { label: '全部', value: -1 },
  { label: '已过期', value: 0 },
  { label: '待生效', value: 1 },
  { label: '生效中', value: 2 }
]

@connect(({ advertisement }) => ({
  model: advertisement
}))

class DatingMatchAdvertisement extends Component {
  state = {
    selectStatus: -1,
    showModal: false,
    opType: 'ADD'
  }

  componentDidMount = () => {
    this.refreshList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  refreshList = () => {
    this.callModel('getAdvertisementList')
  }

  statusFormater = (startStr, endStr) => {
    const start = moment(startStr, 'YYYY-MM-DD HH:mm').unix()
    const end = moment(endStr, 'YYYY-MM-DD HH:mm').unix()
    const now = moment().unix()
    if (now < start) {
      return '待生效'
    }
    if (now > end) {
      return '已过期'
    }
    return <Text type='success'>生效中</Text>
  }

  dataFilter = (before, status) => {
    const now = moment().unix()

    before = before.map(item => {
      const start = moment(item.beginTime, 'YYYY-MM-DD HH:mm').unix()
      const end = moment(item.endTime, 'YYYY-MM-DD HH:mm').unix()
      if (now < start) {
        item.status = 1
      }
      if (now > start && now < end) {
        item.status = 2
      }
      if (now > end) {
        item.status = 0
      }
      return item
    })

    let after = []
    before.forEach((item, index) => {
      if (status === -1 || item.status === status) {
        after.push(item)
      }
    })

    return after.sort((a, b) => {
      if (a.status !== b.status) {
        return a.status < b.status ? 1 : -1
      }
      return a.id - b.id
    })
  }

  checkParmas = (params) => {
    const { jumpUrl } = params
    if (jumpUrl && jumpUrl.indexOf('http') !== 0) {
      return '跳转链接请以http开头'
    }
    return ''
  }

  onSubmitUpdate = (value, opType) => {
    let reason = this.checkParmas(value)
    if (reason) {
      message.warn('参数有误:' + reason)
      return
    }
    value.module = 1 // hgame背景
    value.dimension = 1 // 活动
    const optisons = {
      params: value,
      isDetailMode: true,
      isJsonMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('操作失败: ' + msg)
          return
        }
        message.success('操作成功')
        this.refreshList()
        this.setState({ showModal: false })
      }
    }

    if (opType === 'ADD') {
      this.callModel('addAdvertisement', optisons)
    }
    if (opType === 'UPDATE') {
      this.callModel('updateAdvertisement', optisons)
    }
    if (opType === 'DELETE') {
      this.callModel('deleteAdvertisement', optisons)
    }
  }

  render () {
    const { route } = this.props
    const { list } = this.props.model
    const { selectStatus, showModal, opType } = this.state

    const columns = [
      { title: 'ID', dataIndex: 'id' },
      { title: '活动名称', dataIndex: 'actName', render: (v) => { return v || <Text type='secondary'>-</Text> } },
      { title: '广告图', dataIndex: 'url', render: (v) => { return v ? <ImagesList imgList={v} width={40} height={40} /> : <Text type='secondary'>-</Text> } },
      { title: '跳转链接', dataIndex: 'jumpUrl', render: (v) => { return v ? <Tooltip title={v}><Link onClick={() => window.open(v, '_blank')}>详细链接</Link></Tooltip> : <Text type='secondary'>-</Text> } },
      { title: '开始时间', dataIndex: 'beginTime' },
      { title: '结束时间', dataIndex: 'endTime' },
      { title: '权重', dataIndex: 'weight' },
      { title: '生效状态', dataIndex: 'id', render: (v, r) => { return this.statusFormater(r.beginTime, r.endTime) } },
      { title: '操作',
        width: '10em',
        dataIndex: 'id',
        render: (v, r) => {
          return <div><Space>
            <Link onClick={() => { this.formRef.setFieldsValue(r); this.setState({ opType: 'UPDATE', showModal: true }) }} >
              编辑
            </Link>
            <Popconfirm title='确认删除?' onConfirm={() => this.onSubmitUpdate(r, 'DELETE')}>
              <Link type='danger'>
                删除
              </Link>
            </Popconfirm>
          </Space></div>
        } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Row>
            <Col span={24} style={{ marginBottom: '1em' }}>
              <Space>
                <Select options={statusOptions} value={selectStatus} onChange={(v) => this.setState({ selectStatus: v })} />
                <Button type='primary' onClick={() => { this.formRef.resetFields(); this.setState({ opType: 'ADD', showModal: true }) }} >添加</Button>
              </Space>
            </Col>
            <Col span={24}>
              <Table columns={columns} dataSource={this.dataFilter(list, selectStatus)} pagination={{ pageSize: 50 }} />
            </Col>
          </Row>
        </Card>
        <Modal visible={showModal} forceRender title={opType === 'ADD' ? '新增配置' : '更新配置'} okText='保存'
          onCancel={() => { this.setState({ showModal: false }) }}
          onOk={() => { this.formRef.submit() }}
        >
          <Form ref={ref => { this.formRef = ref }} labelCol={{ span: 5 }} onFinish={f => { this.onSubmitUpdate(f, opType) }}
            initialValues={{ id: 0, actName: '活动名称', jumpUrl: '', weight: 0, beginTime: '', endTime: '', url: '' }} >
            <Form.Item name='id' hidden>
              <Input />
            </Form.Item>
            <Form.Item label='活动名称' name='actName'>
              <Input style={{ width: '16em' }} />
            </Form.Item>
            <Form.Item label='跳转链接' name='jumpUrl'>
              <Input style={{ width: '16em' }} placeholder='请填写完整的url地址,以http开头～' />
            </Form.Item>
            <Form.Item label='权重' name='weight'>
              <InputNumber />
            </Form.Item>
            <Form.Item label='开始时间' name='beginTime' rules={[{ required: true, message: '请填写时间范围' }]} >
              <FixDatePicker />
            </Form.Item>
            <Form.Item label='结束时间' name='endTime' rules={[{ required: true, message: '请填写时间范围' }]} >
              <FixDatePicker />
            </Form.Item>
            <Form.Item label='广告图' name='url' rules={[{ required: true, message: '请填写广告图' }]}>
              <InputPicture key={this.formRef?.getFieldValue('url')} inputType='string' sizeLimit={2} width={130} allowHeight={526} allowWidth={1920} />
            </Form.Item>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

const FixDatePicker = (props) => {
  const { value, onChange } = props
  const stdFormat = 'YYYY-MM-DD HH:mm'
  const fixInput = (v) => { return v ? moment(value, stdFormat) : null }
  const fixOutput = (v) => { return v ? v.format(stdFormat) : '' }
  return <DatePicker value={fixInput(value)} onChange={v => { onChange(fixOutput(v)) }} showTime format={stdFormat} />
}

export default DatingMatchAdvertisement
