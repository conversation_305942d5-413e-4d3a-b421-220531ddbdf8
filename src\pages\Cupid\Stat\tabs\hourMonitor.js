/* eslint-disable no-template-curly-in-string */
import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Row, Typography, Col, Table, Space, Divider, DatePicker, Select, Button } from 'antd'
import { onExportExcel } from '@/utils/common'
import { statTypeOptions, ballTypeOptions, valueFormater, optionsFormater } from '../statCommon'
import moment from 'moment'

const { Text } = Typography
const namespace = 'cupidStat'

@connect(({ cupidStat }) => ({
  model: cupidStat
}))

class HourMonitor extends Component {
  state = {
    timeRange: [moment().subtract(7, 'day'), moment()],
    selectStatType: 1000,
    selectBallType: 0
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  componentDidMount = () => {
    this.getHourData()
  }

  // 查询数据
  getHourData = () => {
    const { timeRange, selectStatType, selectBallType } = this.state
    const { originID } = this.props
    const [ t1, t2 ] = timeRange

    this.callModel('getHourMonitorData', {
      params: {
        start: t1.format('YYYYMMDD'),
        end: t2.format('YYYYMMDD'),
        statType: selectStatType,
        ballType: selectBallType,
        originId: originID
      }
    })
  }

  render () {
    const { timeRange, selectStatType, selectBallType } = this.state
    const { hourStatList } = this.props.model

    const columns = [
      { title: '日期', dataIndex: 'date' },
      { title: '奖池名称', dataIndex: 'ballType', render: (v, r) => { return optionsFormater(ballTypeOptions, v) } },
      // { title: '渠道类型', dataIndex: '' },
      { title: '参与时段', dataIndex: 'hour', render: (v) => { return `[${v}, ${v + 1})` } },
      { title: '奖池总产出', dataIndex: 'totalOut', render: (v) => { return valueFormater(v) } },
      { title: '用户总投入', dataIndex: 'totalIn', render: (v) => { return valueFormater(v) } },
      { title: '付费用户数', dataIndex: 'totalCount', render: (v) => { return v } },
      { title: '发放占比', dataIndex: 'rebate', render: (v, r) => { return `${Number(r.totalOut * 100.0 / r.totalIn).toFixed(2)}%` } }, // 总支出/总收入
      { title: '总偏移', dataIndex: 'totalOffset', render: (v) => { return valueFormater(v) } },
      { title: '常规产出', dataIndex: 'normalOut', render: (v) => { return valueFormater(v) } },
      { title: '乐园祝福产出', dataIndex: 'blessOut', render: (v) => { return valueFormater(v) } },
      { title: '狂欢时刻产出', dataIndex: 'crazyOut', render: (v) => { return valueFormater(v) } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <Card>
        <Row>
          <Col span={24} style={{ marginBottom: '1em' }}>
            {/* 筛选项 */}
            <Space>
              <Text>时间范围:</Text>
              <DatePicker.RangePicker format='YYYY-MM-DD' value={timeRange} onChange={v => { this.setState({ timeRange: v }) }} />
              <Divider type='vertical' />

              <Text>奖池名称：</Text>
              <Select style={{ width: '7em' }} options={ballTypeOptions} value={selectBallType} onChange={(v) => this.setState({ selectBallType: v })} />

              <Text>渠道：</Text>
              <Select style={{ width: '7em' }} options={statTypeOptions} value={selectStatType} onChange={(v) => this.setState({ selectStatType: v })} />
              <Divider type='vertical' />

              <Button type='primary' onClick={() => this.getHourData()}>查询</Button>
              <Button disabled={hourStatList.length === 0}
                onClick={() => { onExportExcel(columns, hourStatList, '丘比特分时段监控.xlsx') }}>导出</Button>
            </Space>
          </Col>
          <Col span={24}>
            <Table columns={columns} dataSource={hourStatList} />
          </Col>
        </Row>
      </Card>
    )
  }
}

export default HourMonitor
