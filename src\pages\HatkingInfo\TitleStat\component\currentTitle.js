import React, { Component } from 'react'
import { connect } from 'dva'
import { Row, Col, Button, Table, Space, Divider } from 'antd'
import moment from 'moment'
const namespace = 'hatkingInfoTitleInfo'

@connect(({ hatkingInfoTitleInfo }) => ({
  model: hatkingInfoTitleInfo
}))

class CurrentTitle extends Component {
  state = {
    selectTimeRange: [moment().subtract(7, 'day'), moment()]
  }
  componentDidMount = () => {
    this.queryList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 查询表格
  queryList = () => {
    const params = { opType: 'query' }
    this.callModel('queryCurrentTitle', { params })
  }

  // 导出表格
  exportList = () => {
    const params = { opType: 'export' }
    this.callModel('queryCurrentTitle', {
      params,
      isDownloadMode: true
    })
  }

  parseHeader = (table) => {
    if (!Array.isArray(table) || table.length === 0) {
      return []
    }
    const header = table[0]
    return header.map((title, index) => {
      return {
        title: title,
        dataIndex: `f${index}`,
        align: 'cneter'
      }
    })
  }

  parseDataSource = (table) => {
    if (!Array.isArray(table) || table.length === 0) {
      return []
    }
    let dataSource = []
    for (let i = 1; i < table.length; i++) {
      let row = table[i]
      let item = {}
      row.map((value, index) => {
        item[`f${index}`] = value
      })
      dataSource.push(item)
    }
    return dataSource
  }

  render () {
    const { currentTitle } = this.props.model

    return (
      <>
        <Row>
          <Col span={24}>
            <Space>
              <Button onClick={this.queryList} type='primary'>刷新</Button>
              <Button onClick={this.exportList}>导出</Button>
            </Space>
          </Col>
          <Divider />
          <Col span={24}>
            {`共${currentTitle.length}条数据`}
          </Col>
          <Col span={24}>
            <Table bordered
              columns={this.parseHeader(currentTitle)}
              dataSource={this.parseDataSource(currentTitle)}
              pagination={{ pageSize: 100 }} />
          </Col>
        </Row>
      </>
    )
  }
}

export default CurrentTitle
