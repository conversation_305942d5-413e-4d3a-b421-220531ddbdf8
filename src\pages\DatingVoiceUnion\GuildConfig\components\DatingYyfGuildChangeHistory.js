import { connect } from 'dva'
import React, { Component } from 'react'
import { But<PERSON>, Col, DatePicker, Divider, Form, Input, InputNumber, Row, Select, Table, Tooltip } from 'antd'
import { formatTimestamp } from '../../common'
import { formatOptions } from '@/utils/common'

const namespace = 'GuildConfigManage'
const defaultPageSize = 10

@connect(({ DatingYyfGuildChangeHistory }) => ({
  model: DatingYyfGuildChangeHistory
}))

class DatingYyfGuildChangeHistory extends Component { // 默认页面组件，不需要修改
  constructor (props) {
    super(props)
    this.initState(props.sid)
    this.searchHandle()
  }

  initState = (sid) => {
    this.state = {
      sid: sid,
      dataList: [],
      opTypeOptions: [],
      pagination: {
        pageSize: defaultPageSize,
        total: 0,
        current: 1,
        defaultCurrent: 1,
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        onChange: (page, pageSize) => {
          this.pageChange(page, pageSize)
        },
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      }
    }
  }

  updatePagination = (page, pageSize, total, dataList) => {
    const { pagination } = this.state
    pagination.current = page || 1
    pagination.pageSize = pageSize || defaultPageSize
    if (total !== undefined) {
      pagination.total = total
    }
    let update = { pagination: pagination }
    if (dataList) {
      update.dataList = dataList
    }
    this.setState(update)
    return pagination
  }

  // 分页信息变更
  pageChange = (page, pageSize, total) => {
    let pagination = this.updatePagination(page, pageSize, total)
    this.searchHandle(pagination.current, pagination.pageSize)
  }

  // 获取当前查询条件
  getQueryCondition = (page, size, cond) => {
    const { pagination, sid } = this.state
    page = page || pagination.current || 1
    size = size || pagination.pageSize || defaultPageSize
    let pageInfo = {
      pageNo: page,
      pageSize: size,
      sid: sid
    }
    if (!cond) {
      return pageInfo
    }

    let query = Object.assign(cond, pageInfo)
    if (query.startTime) {
      query.begChangeTime = query.startTime.unix()
      delete query.startTime
    }
    if (query.endTime) {
      query.endChangeTime = query.endTime.unix()
      delete query.endTime
    }
    return query
  }

  // 处理查询事件
  searchHandle = (page, size, cond) => {
    const { dispatch } = this.props

    let query = this.getQueryCondition(page, size, cond)
    if (!query) {
      return
    }
    let self = this
    dispatch({
      type: `${namespace}/pageDatingYyfGuildChangeHistoryList`,
      payload: query,
      callback: (data) => {
        self.updatePagination(query.pageNo || 1, query.pageSize || defaultPageSize, data ? data.total : 0, (data.list || []))
        self.setState({ opTypeOptions: data.opTypeOptions })
      }
    })
  }

  onQueryClick = (values) => {
    const { pagination } = this.state
    let size = pagination.pageSize || defaultPageSize
    this.searchHandle(1, size, values)
  }

  getShowColumns = () => {
    const { opTypeOptions } = this.state
    return [
      { title: 'TraceID', dataIndex: 'traceID', align: 'left' },
      { title: 'SID', dataIndex: 'sid', align: 'left' },
      { title: '家族ID', dataIndex: 'familyId', align: 'left' },
      { title: '操作类型', dataIndex: 'opType', align: 'left', render: (v) => formatOptions(v, opTypeOptions) },
      { title: '操作人', dataIndex: 'operator', align: 'left' },
      { title: '操作时间', dataIndex: 'changeTime', align: 'left', render: (v) => formatTimestamp(v) },
      { title: '备注', dataIndex: 'remark', align: 'left' },
      {
        title: '个播厅白名单ssid',
        dataIndex: 'ssidList',
        align: 'left',
        width: 400,
        ellipsis: true,
        render: (v) => {
          v = v || []
          return <Tooltip title={<Input.TextArea rows={10} value={v.join('\n')} />} placement={'leftBottom'}>{v.join(',')}</Tooltip>
        }
      }
    ]
  }

  // 渲染函数
  render () {
    const { pagination, dataList, opTypeOptions } = this.state
    const columns = this.getShowColumns()

    return (
      <>
        <Row style={{ marginBottom: '1em' }}>
          <Form layout={'inline'} ref={form => {
            this.formRefQuery = form
          }} onFinish={this.onQueryClick}>
            <Form.Item name={'sid'} label={'SID'}>
              <InputNumber placeholder='输入主持sid' style={{ width: 150 }} allowClear />
            </Form.Item>
            <Form.Item name={'operator'} label={'操作人'}>
              <InputNumber placeholder='输入操作人uid' style={{ width: 150 }} allowClear />
            </Form.Item>
            <Form.Item name={'opType'} label={'操作类型'}>
              <Select style={{ width: 80 }} allowClear options={opTypeOptions || []} />
            </Form.Item>
            <Form.Item label='操作开始时间' name='startTime'>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </Form.Item>
            <Form.Item label='操作结束时间' name='endTime'>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </Form.Item>

            <Button type='primary' htmlType='submit'>查询</Button>
            <Divider type={'vertical'} />
            <Button type='primary' onClick={() => {
              this.formRefQuery.resetFields()
              this.formRefQuery.submit()
            }}>重置</Button>
          </Form>
        </Row>
        <Row style={{ marginBottom: '1em' }}>
          <Col span={24}>
            <Table columns={columns}
              dataSource={dataList || []}
              size='small'
              pagination={pagination}
              showSorterTooltip={false}
              rowKey={record => record.id}
            />
          </Col>
        </Row>
      </>
    )
  }
}

export default DatingYyfGuildChangeHistory
