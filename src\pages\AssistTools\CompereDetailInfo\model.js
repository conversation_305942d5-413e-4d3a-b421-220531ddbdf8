import { getCompereDetailInfo } from '../common'
import { newCommonReducers } from '@/utils/common'

export default {
  namespace: 'CompereDetailInfo',
  state: {
    compereInfo: {}
  },

  reducers: newCommonReducers(),

  // 数据变更
  effects: {
    * getCompereDetailInfo ({ payload, callback }, { call, put }) {
      const { data } = yield call(getCompereDetailInfo, payload)

      let compereInfo = data.data || {}

      yield put({
        type: 'updateState',
        payload: {
          compereInfo: compereInfo
        }
      })

      if (callback) {
        callback(compereInfo)
      }
    }
  }
}
