import { getLists } from './api'
import { message } from 'antd'

export default {
  namespace: 'photoCheck', // 只有这里需要修改

  state: {
    list: []
  },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getLists, payload)

      yield put({
        type: 'updateList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * getItemByKey ({ payload }, { call, put }) {
      const { data: { status, list } } = yield call(payload)
      if (status === 0) {
        yield put({
          type: 'updateList',
          payload: list
        })
      } else {
        message.warning('not found')
      }
    }
  }
}
