import request from '@/utils/request'
import { stringify } from 'qs'

export function getActCfgListFromServer () {
  return request('/rank/activity200701/get_act_cfg_list')
}

export function removeActCfgFromServer (params) {
  return request(`/rank/activity200701/remove_act_cfg?${stringify(params)}`)
}

export function upsetActCfgFromServer (params) {
  return request(`/rank/activity200701/update_act_cfg_list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function getMoneyCfgListFromServer () {
  return request('/rank/activity200701/get_money_list')
}

export function removeMoneyCfgFromServer (params) {
  return request(`/rank/activity200701/remove_money?${stringify(params)}`)
}

export function upsetMoneyCfgFromServer (params) {
  return request(`/rank/activity200701/update_money_list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function getDescCfgListFromServer () {
  return request('/rank/activity200701/get_desc_list')
}

export function removeDescCfgFromServer (params) {
  return request(`/rank/activity200701/remove_desc?${stringify(params)}`)
}

export function upsetDescCfgFromServer (params) {
  return request(`/rank/activity200701/update_desc_list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function getScoreRewardListFromServer () {
  return request('/rank/activity200701/get_score_reward_record')
}

export function preScoreRewardFromServer () {
  return request('/rank/activity200701/score_reward_pre_settlement')
}

export function settlementScoreRewardFromServer () {
  return request('/rank/activity200701/score_reward_settlement')
}

export function upsetScoreRewardFromServer (params) {
  return request(`/rank/activity200701/upset_grant_score_reward_record`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function getBlackCompereFromServer () {
  return request('/rank/activity200701/black_compere_list')
}

export function addBlackCompereFromServer (params) {
  return request(`/rank/activity200701/add_black_compere?${stringify(params)}`)
}

export function delBlackCompereFromServer (params) {
  return request(`/rank/activity200701/remove_black_compere?${stringify(params)}`)
}

export function getPkRewrdDetailFromServer (params) {
  return request(`/rank/activity200701/pk_reward_detail?${stringify(params)}`)
}
