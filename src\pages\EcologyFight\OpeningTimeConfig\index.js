import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Button, Table, Card, Form, Divider, Popconfirm, Modal, Select, DatePicker, TimePicker, Input } from 'antd'
import dateString from '@/utils/dateString'
import { connect } from 'dva'
import moment from 'moment'
const Option = Select.Option

const namespace = 'openingTimeConfig'
const FormItem = Form.Item

const { RangePicker } = DatePicker
// const { RangePickerTest } = TimePicker.RangePicker

@connect(({ openingTimeConfig }) => ({
  model: openingTimeConfig
}))

class OpeningTimeConfig extends Component {
  // 定义列表结构，
  columns = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: '开启日期', dataIndex: 'date', align: 'center' },
    { title: '开启时间段', dataIndex: 'time', align: 'center' },
    { title: '状态', dataIndex: 'status', align: 'center', render: text => ['', '已过期', <font color='red'>生效中</font>, <font color='blue'>未生效</font>][text] },
    { title: '添加时间', dataIndex: 'opTime', align: 'center', render: text => dateString(text) },
    { title: '添加人', dataIndex: 'opUsername', align: 'center' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (record) => (
        <span>
          <Button type='primary' onClick={this.showModal(true, record)}>编辑</Button><Divider type='vertical' />
          <Popconfirm title='确认删除?' onConfirm={this.handleDel(record.id)}> <Button type='danger'>删除</Button> </Popconfirm>
        </span>),
      export: false
    }
  ]

  paginationProps = { showSizeChanger: true, pageSize: 10, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, status: '', dataSource: [], searchDone: false, dateRange: [moment().subtract(7, 'days'), moment().subtract(0, 'days')] }

  // 获取列表
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`
    })
  }

  // show modal
  showModal = (isUpdate, record) => () => {
    if (record == null) record = { uid: '' }

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.timeLimit = v.startDate && v.endDate ? [moment.unix(v.startDate), moment.unix(v.endDate)] : []
      v.startTime = moment((v.startTime === '' || isUpdate === false ? '00:00:00' : v.startTime), 'HH:mm:ss')
      v.endTime = moment((v.endTime === '' || isUpdate === false ? '23:59:59' : v.endTime), 'HH:mm:ss')
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // add
  onFinish = values => {
    const { dispatch } = this.props
    const data = { id: values.id, startDate: values.timeLimit[0].unix(), endDate: values.timeLimit[1].unix(), startTime: values.startTime.format('HH:mm:ss'), endTime: values.endTime.format('HH:mm:ss'), createTime: moment().unix() }
    console.log('handleSubmit', data)
    dispatch({
      type: `${namespace}/addItem`,
      payload: data
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { id: key }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // search
  handleSearch = e => {
    const { model: { list } } = this.props
    const { status } = this.state

    var dataSource = list
    const statusId = parseInt(status || 0, 10)
    if (isNaN(statusId)) {
      return
    }
    if (statusId > 0) {
      dataSource = dataSource.filter(data => data.status === statusId)
    }
    this.setState({ dataSource: dataSource, searchDone: true })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list } } = this.props
    const { dataSource, searchDone, visible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }
    let headers = []
    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.expand === undefined || !col.expand) {
        headers.push(col)
      }

      if (col.export === undefined || col.export) {
        exportHeader.push(col)
      }
    })

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form layout='inline'>
            <Form.Item>
              <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 120 }} onChange={e => this.setState({ status: e.key })}>
                <Option value='0'>全部</Option>
                <Option value='1'>已过期</Option>
                <Option value='2'>生效中</Option>
                <Option value='3'>未生效</Option>
              </Select>
              <Button style={{ marginLeft: 15, marginRight: 15 }} type='primary' onClick={this.handleSearch}>搜索</Button>
              <Button type='primary' style={{ marginLeft: 0 }} onClick={this.showModal(false)}>添加</Button>
            </Form.Item>
          </Form>
          <Divider />
          <Form>
            <Table
              dataSource={searchDone ? dataSource : list}
              columns={headers}
              rowKey={(record, index) => index}
              pagination={this.paginationProps}
              size='small'
            />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <FormItem name='id' hidden>
              <Input hidden />
            </FormItem>
            <FormItem label='开启日期' name='timeLimit' rules={[{ required: true }]}>
              <RangePicker showTime format='YYYY-MM-DD' />
            </FormItem>
            <FormItem label='开始时间' name='startTime' rules={[{ required: true, message: '开始时间不能为空' }]}>
              <TimePicker style={{ width: '60%' }} />
            </FormItem>
            <FormItem label='结束时间' name='endTime' rules={[{ required: true, message: '结束时间不能为空' }]}>
              <TimePicker style={{ width: '60%' }} />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default OpeningTimeConfig
