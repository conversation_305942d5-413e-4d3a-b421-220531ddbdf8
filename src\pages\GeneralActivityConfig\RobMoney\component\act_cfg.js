import React, { Component } from 'react'
import { <PERSON>, Di<PERSON><PERSON>, But<PERSON>, Modal, Form, Table, DatePicker, Popconfirm, message, InputNumber } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import { connect } from 'dva'
import moment from 'moment'
import styles from '../index.module.less'

const format = 'YYYY-MM-DD HH:mm:ss'
const namespace = 'robMoney' // model 的 namespace
const FormItem = Form.Item

@connect(({ rob<PERSON><PERSON> }) => ({ // model 的 namespace
  model: robMoney // model 的 namespace
}))
class ActCfgComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false
    }
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getActCfgList`
    })
  }

  // 需要修改
  columns = [
    { title: '周期', dataIndex: 'period', align: 'center' },
    { title: '开始时间', dataIndex: 'begin', align: 'center', render: text => moment.unix(text).format(format) },
    { title: '结束时间', dataIndex: 'end', align: 'center', render: text => moment.unix(text).format(format) },
    { title: '预赛开始', dataIndex: 'preBegin', align: 'center', render: text => moment.unix(text).format(format) },
    { title: '预赛结束', dataIndex: 'preEnd', align: 'center', render: text => moment.unix(text).format(format) },
    { title: 'PK开始', dataIndex: 'pkBegin', align: 'center', render: text => moment.unix(text).format(format) },
    { title: 'PK结束', dataIndex: 'pkEnd', align: 'center', render: text => moment.unix(text).format(format) },
    { title: '操作',
      align: 'center',
      render: (text, record) => (
        <div>
          <a><DeleteOutlined style={{ marginRight: 10 }} onClick={this.showModal(true, record)} /></a>
          <Popconfirm onConfirm={this.handleRemove(record.period)} title='确认删除？'><a><DeleteOutlined style={{ color: 'red' }} type='delete' /></a></Popconfirm>
        </div>
      )
    }
  ]

  now = moment().unix()
  defaultValue = {
    begin: this.now,
    end: this.now + 1,
    preBegin: this.now,
    preEnd: this.now + 1,
    grayBegin: this.now,
    grayEnd: this.now + 1,
    pkBegin: this.now,
    pkEnd: this.now + 1
  }

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.begin = moment.unix(v.begin)
      v.end = moment.unix(v.end)
      v.grayBegin = moment.unix(v.grayBegin)
      v.grayEnd = moment.unix(v.grayEnd)
      v.sidList = Array.isArray(v.sidList) ? v.sidList.join('\n') : v.sidList
      v.preBegin = moment.unix(v.preBegin)
      v.preEnd = moment.unix(v.preEnd)
      v.pkBegin = moment.unix(v.pkBegin)
      v.pkEnd = moment.unix(v.pkEnd)
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // 隐藏弹窗，不需要修改
  hideModal = () => {
    this.setState({ visible: false })
  }

  onFinish = values => {
    const { dispatch, model: { tagList } } = this.props
    const { isUpdate } = this.state
    values.begin = values.begin.unix()
    values.end = values.end.unix()
    values.grayBegin = 0
    values.grayEnd = 0
    values.sidList = []
    values.preBegin = values.preBegin.unix()
    values.preEnd = values.preEnd.unix()
    values.pkBegin = values.pkBegin.unix()
    values.pkEnd = values.pkEnd.unix()

    if (values.end - values.pkEnd < 600) {
      values.end += 600
    }

    if (values.begin >= values.end) {
      message.error('结束时间必须大于开始时间')
      return
    }

    if (values.preBegin >= values.preEnd) {
      message.error('预赛结束时间必须大于预赛开始时间')
      return
    }

    if (values.pkBegin < values.preEnd) {
      message.error('PK开始必须小于预赛结束')
      return
    }

    if (values.pkBegin >= values.pkEnd) {
      message.error('PK结束时间必须大于PK开始时间')
      return
    }

    if (values.period <= 0) {
      message.error('周期必须大于0', 10)
      return
    }

    var list = tagList
    if (!isUpdate) {
      for (var i = 0; Array.isArray(list) && i < list.length; i++) {
        if (list[i].tagId === values.tagId) {
          message.error('period exist', 5)
          return
        }
      }
    }

    // console.log(values, list)
    dispatch({
      type: `${namespace}/upsetActCfgList`,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 更新与删除
  handleSubmit = () => {
    this.formRef.submit()
  }

  handleRemove = id => () => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/removeActCfgList`,
      payload: { id: id }
    })
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onRowClassName = record => {
    let ts = moment().unix()
    return ts >= record.end ? styles.grayColumn : ''
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { actCfgList } } = this.props
    const { visible, title, isUpdate } = this.state
    const formItemLayout = { // 不需要修改
      labelCol: {
        xs: { span: 4 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 20 },
        sm: { span: 20 }
      }
    }

    return (
      <Card>
        <Button onClick={this.showModal(false, this.defaultValue)}>添加</Button>
        <Divider />
        <Table rowClassName={this.onRowClassName} rowKey={(record, index) => index} dataSource={actCfgList} columns={this.columns} size='small' pagination={false} /> {/* 显示的列表 */}

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} ref={form => { this.formRef = form }} {...formItemLayout}>
            <FormItem label='周期' name='period' rules={[{ required: true }]}>
              <InputNumber min={1} readOnly={isUpdate} />
            </FormItem>
            <FormItem label='开始时间' name='begin' rules={[{ required: true }]}>
              <DatePicker format={format} showTime />
            </FormItem>
            <FormItem label='结束时间' name='end' rules={[{ required: true }]}>
              <DatePicker formort={format} showTime />
            </FormItem>
            <FormItem label='预赛开始' name='preBegin' rules={[{ required: true }]}>
              <DatePicker showTime format={format} />
            </FormItem>
            <FormItem label='预赛结束' name='preEnd' rules={[{ required: true }]}>
              <DatePicker showTime format={format} />
            </FormItem>
            <FormItem label='PK开始' name='pkBegin' rules={[{ required: true }]}>
              <DatePicker showTime format={format} />
            </FormItem>
            <FormItem label='PK结束' name='pkEnd' rules={[{ required: true }]}>
              <DatePicker showTime format={format} />
            </FormItem>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default ActCfgComponent
