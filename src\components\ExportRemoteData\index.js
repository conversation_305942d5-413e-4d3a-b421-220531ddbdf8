import React, { Component } from 'react'
import PropTypes from 'prop-types'
import { But<PERSON>, Spin, message, Modal, Popconfirm } from 'antd'
import request from '@/utils/request'
import moment from 'moment'
import exportExcel from '@/utils/exportExcel'
import { convertResToDataList, detectDataListTableColumns } from '@/utils/common'

const defaultPageSize = 1000

/**
 * 分页数据下载
 */
class ExportRemoteData extends Component {
  static propTypes = {
    /**
     * 标题，默认是 "导出"
     */
    title: PropTypes.string,
    /**
     * 按钮是否可用
     */
    disabled: PropTypes.bool,
    /**
     * 确认消息
     */
    confirm: PropTypes.string,
    /**
     * 请求方法，GET|POST, 默认是 GET
     */
    method: PropTypes.string,
    /**
     * 最大查询页码，默认是 0 表示查询到没有数据为止，如果 > 0 那么查询到 maxPage 位置，有些接口是不分页，直接查询所有数据的，这个时候就可以传 1
     */
    maxPage: PropTypes.number,
    /**
     * 每次分页查询多少数据，默认是 1000
     */
    pageSize: PropTypes.number,
    /**
     * 数据提供者，返回要导出的数据列表，一般是选中某些记录导出的情况， dataProvider 和 uriBuilder 两个至少要有一个提供，优先使用 dataProvider
     */
    dataProvider: PropTypes.func,
    /**
     * 获取请求数据的URI 地址，包含参数 func(page, pageSize) string， 比如 return /super_compere_agent/video_compere_lib/query?query=JSON.stringify(params)
     */
    uriBuilder: PropTypes.func,
    /**
     * 请求 body 构造器，返回编码后的 body, 函数 func(page, pageSize) string
     */
    bodyBuilder: PropTypes.func,
    /**
     * 请求头 Content-Type，FORM|JSON
     */
    contentType: PropTypes.string,
    /**
     * 结果转换器，返回对象列表，如果响应有问题，请 throw error
     */
    resConverter: PropTypes.func,
    /**
     * 导出的文件名，不包含文件后缀，会自动加上 xlsx 后缀，如过不填写的话，生成一个当前时间 yyyyMMddHHmmss 作为名称
     */
    filename: PropTypes.any,
    /**
     * 表格列定义，可以复用 table 的columns，如果定义了 func exportRender(fieldValue, record) 那么会使用 exportRender 进行渲染
     * 如没有定义 exportRender ，但是定义了 render，则使用 render 计算结果，如结果不是字符，那么直接使用属性值
     * {
     *  title: '列名',
     *  dataIndex: '属性名',
     *  render: (fieldValue, record) {},  // 渲染函数
     *  exportRender: (fieldValue, record), // 渲染函数，优先于 render 使用
     *  exportIgnore: true | false // 是否需要忽略导出
     * }
     */
    columns: PropTypes.any,

    /**
     * 忽略哪些标题，会通过 title，dataIndex 来计算是否需要忽略
     */
    ignoreHeaders: PropTypes.array,
    /**
     * 数据过滤，返回 true 则表示需要导出，提供函数 function(record) boolean
     */
    dataFilter: PropTypes.func,
    /**
     * 数据预处理，比如可以遍历数据，然后加上序号之类。 function (recordList)
     */
    prepareHandle: PropTypes.func,
    /**
     * 参考 Button 本身的 type 属性
     */
    buttonType: PropTypes.string,
    /**
     * 参考 Button 本身的 style 属性
     */
    buttonStyle: PropTypes.object,
    /**
     * 参考 Button 本身的 size 属性
     */
    buttonSize: PropTypes.string
  }

  constructor (props) {
    super(props)
    let { method, dataProvider, uriBuilder, bodyBuilder, contentType, resConverter, columns, ignoreHeaders, dataFilter } = this.props

    if (!dataProvider && !uriBuilder) {
      throw new Error('dataProvider or uriBuilder must give one')
    }
    if (dataProvider && typeof dataProvider !== 'function') {
      throw new Error('dataProvider must function')
    }
    if (uriBuilder && typeof uriBuilder !== 'function') {
      throw new Error('uriBuilder must function')
    }
    if (resConverter && typeof resConverter !== 'function') {
      throw new Error('resConverter func is required')
    }
    if (bodyBuilder && typeof bodyBuilder !== 'function') {
      throw new Error('bodyBuilder func is required')
    }
    let ct = (contentType || 'FORM').toUpperCase()
    if (ct !== 'FORM' && ct !== 'JSON') {
      throw new Error('contentType must one of [FORM, JSON]')
    }
    let m = (method || 'GET').toUpperCase()
    if (m !== 'GET' && m !== 'POST') {
      throw new Error('method must one of [GET, POST]')
    }
    if (columns && typeof columns !== 'function' && !(columns instanceof Array)) {
      throw new Error('columns must function() array or array')
    }

    let ignoreFields = {}
    if (ignoreHeaders && ignoreHeaders.length > 0) {
      ignoreHeaders.forEach(v => {
        if (v && v.length > 0) {
          ignoreFields[v] = true
        }
      })
    }
    ignoreFields['操作'] = true

    let filter = dataFilter
    if (!filter) {
      filter = () => {
        return true
      }
    }

    this.state = {
      loadingVisible: false,
      ignoreFields: ignoreFields,
      dataFilter: filter
    }
  }

  fetchRemotePageData = async (method, fetchURI, contentType, body) => {
    if (contentType === 'JSON') {
      contentType = 'application/json;charset=UTF-8'
    } else {
      contentType = 'application/x-www-form-urlencoded;charset=UTF-8'
    }

    let options = {}
    if (body) {
      options = {
        method: method,
        headers: {
          'Content-Type': contentType
        },
        body: body
      }
    }
    const resp = await request(fetchURI, options)
    return resp
  }

  onExportClick = () => {
    let { method, maxPage, pageSize, dataProvider, uriBuilder, bodyBuilder, contentType } = this.props
    const { dataFilter } = this.state
    method = (method || 'GET').toUpperCase()
    pageSize = pageSize || defaultPageSize
    // 查询页码
    let page = 0

    let self = this

    // 待导出的数据列表
    let exportedDataList = []

    if (dataProvider) {
      exportedDataList = dataProvider() || []
      if (dataFilter) {
        exportedDataList.filter(dataFilter)
      }
      self.doExport(exportedDataList)
      return
    }

    let fetchData = () => {
      page++
      let fetchURI
      let body

      try {
        let uriInfo = uriBuilder(page, pageSize)
        fetchURI = uriInfo
        if (typeof uriInfo === 'object') {
          fetchURI = uriInfo.uri
          body = uriInfo.body
        } else {
          if (bodyBuilder) {
            body = bodyBuilder(page, pageSize)
          }
        }
        if (!fetchURI) {
          message.error('数据连接构造异常：' + fetchURI)
          return
        }
      } catch (e) {
        message.error(e)
        return
      }

      this.setState({ loadingVisible: true, currentPage: page, currentFetch: exportedDataList.length })
      this.fetchRemotePageData(method, fetchURI, contentType, body).then(resHandler)
    }

    let resHandler = (res) => {
      if (res && res.err) {
        this.setState({ loadingVisible: false })
        message.error('读取数据异常：' + res.err)
        return
      }
      try {
        let dataList = convertResToDataList(res)
        if (dataList && dataList.length > 0) {
          dataList.forEach(record => {
            if (dataFilter(record)) {
              exportedDataList.push(record)
            }
          })
        }
        if (!dataList || dataList.length < pageSize || dataList.length > pageSize || (maxPage > 0 && page >= maxPage)) {
          self.doExport(exportedDataList)
          return
        }
        fetchData()
      } catch (e) {
        this.setState({ loadingVisible: false })
        message.error('读取数据异常：' + e)
      }
    }
    fetchData()
  }

  /**
   * 导出的文件名，不包含文件后缀，会自动加上 xlsx 后缀，如过不填写的话，生成一个当前时间 yyyyMMddHHmmss 作为名称
   */
  getFilename = () => {
    const { filename } = this.props
    const current = moment.unix(new Date().getTime() / 1000).format('YYYYMMDDHHmmss')
    if (filename) {
      let fn = filename
      if (typeof filename === 'function') {
        fn = filename()
      }
      fn = fn + ''
      if (fn) {
        let idx = fn.lastIndexOf('.')
        if (idx >= 0) {
          fn = fn.substring(0, idx)
        }
        if (fn) {
          return fn + '_' + current + '.xlsx'
        }
      }
    }
    // 时间座位文件名
    return current + '.xlsx'
  }

  /**
   * 获取表头，如过配置了则使用配置的表头，否则就用字段名称
   * @param dataList
   */
  getHeaders = (dataList) => {
    if (!dataList || dataList.length === 0) {
      return []
    }
    const { columns } = this.props
    const { ignoreFields } = this.state

    let exportColumns = columns

    if (!columns || columns.length < 1) {
      exportColumns = detectDataListTableColumns(dataList, true)
    }

    if (columns) {
      if (typeof columns === 'function') {
        exportColumns = columns(dataList)
      }
      if (!exportColumns || exportColumns.length <= 0) {
        exportColumns = detectDataListTableColumns(dataList, true)
      }
      let headers = []
      let existsKey = {}
      let index = 0
      exportColumns.forEach(function (item) {
        if (ignoreFields[item.title] || ignoreFields[item.dataIndex] || item.exportIgnore === true) {
          return
        }
        let key = item.dataIndex
        if (existsKey[key]) {
          key = key + '_' + index
        }
        existsKey[key] = true
        headers.push({
          key: key,
          dataIndex: item.dataIndex,
          header: item.title || item.dataIndex,
          render: item.exportRender || item.render
        })
        index++
      })
      return headers
    } else {
      // 没有传，使用字段名称来
      let existsFields = {}
      let headers = []
      for (let field in dataList[0]) {
        if (existsFields[field] || ignoreFields[field]) {
          return
        }
        existsFields[field] = field
        headers.push({ key: field, header: field, dataIndex: field })
      }
      return headers
    }
  }

  /**
   * 执行导出到 excel
   * @param dataList
   */
  doExport = (dataList) => {
    if (!dataList || dataList.length < 1) {
      message.error('待导出数据为空')
      // 导出完成
      this.setState({ loadingVisible: false })
      return
    }
    const { prepareHandle } = this.props

    if (prepareHandle) {
      prepareHandle(dataList)
    }
    let filename = this.getFilename()
    let headers = this.getHeaders(dataList)
    // 数据转换，转成可以导出的数据
    dataList.forEach(record => {
      let old = Object.assign({}, record)
      for (let i = 0; i < headers.length; i++) {
        let header = headers[i]
        if (header.render) {
          record[header.key] = header.render(old[header.dataIndex], old)
        } else {
          record[header.key] = old[header.dataIndex]
        }
      }
    })

    // 执行导出逻辑
    exportExcel(headers, dataList, filename)

    // 导出完成
    this.setState({ loadingVisible: false })
  }

  render () {
    const { title, buttonType, buttonStyle, buttonSize, pageSize, disabled, confirm } = this.props
    const { loadingVisible, currentPage, currentFetch } = this.state
    let btnType = 'primary'
    let btnStyle = {}
    let btnSize = ''
    btnType = buttonType || btnType
    btnStyle = buttonStyle || btnStyle
    btnSize = buttonSize || btnSize
    let perPage = pageSize || 1000
    return (
      <>
        {!confirm
          ? <Button disabled={disabled} type={btnType} size={btnSize} style={btnStyle} onClick={this.onExportClick}>{title || '导出'}</Button>
          : <Popconfirm placement='topLeft' title={confirm} onConfirm={this.onExportClick}>
            <Button disabled={disabled} type={btnType} size={btnSize} style={btnStyle}>{title || '导出'}</Button>
          </Popconfirm>
        }

        <Modal visible={loadingVisible} footer={null} closable={false} centered>
          <Spin
            tip={'正在加载第[' + currentPage + ']页数据（每页[' + perPage + ']条）, 当前已加载[' + currentFetch + ']......'} />
        </Modal>
      </>
    )
  }
}

export default ExportRemoteData
