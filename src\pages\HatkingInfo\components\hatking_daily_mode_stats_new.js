import React, { Component } from 'react'
import { Table, Divider, Button, Card, Form, DatePicker } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
const gameMap = { 0: 'ALL', 1: '武器大师', 2: '热血屠龙', 3: '星球漫步', 4: '明星衣橱', 5: '活动玩法' }
const chanMap = { all: 'ALL', pc: 'PC端', zhuiwan: 'YO交友', yomi: 'YO语音' }

@connect(({ hatkingJy }) => ({ // model 的 namespace
  model1: hatkingJy // model 的 namespace
}))
class HatKingDailyModeStatsNewComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      list: [],
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
  }

  // 日期 渠道类型 单参与流水 单参与成功金额 单参与发放道具比 双参与流水 双参与成功金额 双参与发放道具比 一列流水 一列成功金额 一列发放道具比
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '玩法类型', dataIndex: 'arenaId', align: 'center', render: (text, record, index) => { return gameMap[text] }, filters: [{ text: 'ALL', value: 0 }, { text: '武器大师', value: 1 }, { text: '热血屠龙', value: 2 }, { text: '星球漫步', value: 3 }, { text: '明星衣橱', value: 4 }, { text: '活动玩法', value: 5 }], defaultFilteredValue: ['0'], onFilter: (value, record) => record.arenaId === value },
    { title: '渠道类型', dataIndex: 'platform', align: 'center', render: (text, record, index) => { return chanMap[text] }, filters: [{ text: 'ALL', value: 'all' }, { text: 'PC端', value: 'pc' }, { text: 'YO交友', value: 'zhuiwan' }, { text: 'YO语音', value: 'yomi' }], defaultFilteredValue: ['all'], onFilter: (value, record) => record.platform.includes(value) },
    { title: '单参与流水', dataIndex: 'betAmethyst1', align: 'center' },
    { title: '单参与人数', dataIndex: 'betUser1', align: 'center' },
    { title: '单参与成功金额', dataIndex: 'winAmethyst1', align: 'center' },
    { title: '单参与发放道具比', dataIndex: 'betRebateRatio1', align: 'center' },
    { title: '双参与流水', dataIndex: 'betAmethyst2', align: 'center' },
    { title: '双参与人数', dataIndex: 'betUser2', align: 'center' },
    { title: '双参与成功金额', dataIndex: 'winAmethyst2', align: 'center' },
    { title: '双参与发放道具比', dataIndex: 'betRebateRatio2', align: 'center' },
    { title: '一列流水', dataIndex: 'betAmethyst3', align: 'center' },
    { title: '一列人数', dataIndex: 'betUser3', align: 'center' },
    { title: '一列成功金额', dataIndex: 'winAmethyst3', align: 'center' },
    { title: '一列发放道具比', dataIndex: 'betRebateRatio3', align: 'center' }
  ]

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    console.log(data)
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getModeStatsListNew2`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { modeStatsListNew2 } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form>
          <span style={{ marginLeft: 10 }}>时间范围:</span>
          <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Divider />
          <Table dataSource={modeStatsListNew2} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default HatKingDailyModeStatsNewComponent
