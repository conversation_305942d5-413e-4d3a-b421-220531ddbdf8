import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/run_poison_billboard/get_billboard_config_info?${stringify(params)}`) // 修改 url 即可
}

export function update (params) {
  return request(`/run_poison_billboard/update_billboard_config_info?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
