import React, { Component } from 'react'
import { connect } from 'dva'
import { tableStyle, Inputlayout } from '@/utils/common'
import { Tabs, Card, Input, Form, Row, Col, Button, Table, Popconfirm } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'

const namespace = 'compereLiveNotifyWhiteList'

@connect(({ compereLiveNotifyWhiteList }) => ({
  model: compereLiveNotifyWhiteList
}))

class CompereLiveNotifyWhiteList extends Component {
  constructor (props) {
    super(props)
    this.refreshVidioComperList()
  }

  // 标签页发生切换
  onTagChange = (record) => {
    if (record === '1') { // 切换到'添加标签页'
      this.initForm()
    }
    if (record === '2') { // 切换到'弹窗白名单标签页'
      this.refreshVidioComperList()
    }
  }

    // 清空输入表单
    initForm = () => {
      this.setState({ newSid: '', newSsid: '' })
      if (this.formRef) {
        this.formRef.resetFields()
      }
    }

  // 获取/刷新白名单列表数据
  refreshVidioComperList = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getWhiteListData`,
      payload: null
    })
  }

  // 点击 添加标签页-添加按钮
  onAddBtnClick = () => {
    const { newUid, newSid } = this.state
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/addWhiteListByUid`,
      payload: { newUid: newUid, newSid: newSid, callback: this.initForm }
    })
  }

  // '添加'标签页html代码
  addBlackLIstHtml = () => {
    const { updating } = this.props.model
    return (
      <Row>
        <Col span='24'>
          <Form {...Inputlayout}
            initialValues={{ uid: '', sid: '' }}
            ref={form => { this.formRef = form }}
          >
            <Form.Item label='用户ID [uid]:' name='uid'>
              <Input placeholder='如50041789'
                onChange={e => this.setState({ newUid: e.target.value })}
                maxLength={20}
              />
            </Form.Item>
            <Form.Item label='顶级频道[sid]:' name='sid'>
              <Input placeholder='如87814665'
                onChange={e => this.setState({ newSid: e.target.value })}
                maxLength={20}
              />
            </Form.Item>
            <Form.Item>
              <Button type='primary' htmlType='submit' loading={updating} onClick={() => this.onAddBtnClick()}>
                添加
              </Button>
            </Form.Item>
          </Form>
        </Col>
      </Row>
    )
  }

  // 确认删除选中的白名单
  onComfirmDel = (uid, sid) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/delWhiteListByUidSid`,
      payload: { uid: uid, sid: sid }
    })
  }

  // 白名单列表-删除操作html代码
  deleteWhiteListHtml = (record) => {
    let { uid, sid } = record
    let tmpStr = `确定要删除弹窗白名单 [uid=${uid}, sid=${sid}] 吗？`
    return (
      <Popconfirm placement='bottom' title={tmpStr}
        okType='danger' okText='删除' cancelText='取消' onConfirm={() => this.onComfirmDel(uid, sid)}>
        <a href='#'><DeleteOutlined style={{ color: '#ff3535', fontSize: '1.2em' }} /></a>
      </Popconfirm>
    )
  }

  // ‘白名单展示列表标签页’html代码
  displayWhiteListHtml = () => {
    const columns = [
      { title: '#', dataIndex: 'idx' },
      { title: '短位频道', dataIndex: 'sid' },
      { title: '频道号', dataIndex: 'asid' },
      { title: '主持uid', dataIndex: 'uid' },
      { title: '操作', render: (record) => this.deleteWhiteListHtml(record) }
    ]
    const { displayData } = this.props.model
    return (
      <Row >
        <Col span={24}>
          <Table columns={columns} dataSource={displayData} size='small' pagination={tableStyle} />
        </Col>
      </Row>
    )
  }

  render () {
    const { TabPane } = Tabs
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='2' onChange={(record) => this.onTagChange(record)} type='card' size='large'>
            <TabPane tab='添加' key='1'>
              {this.addBlackLIstHtml()}
            </TabPane>
            <TabPane tab='弹窗白名单' key='2'>
              {this.displayWhiteListHtml()}
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default CompereLiveNotifyWhiteList
