import React, { Component } from 'react'
import { Card, Table, DatePicker, Form, Row, Col, Button, Input } from 'antd'
import { connect } from 'dva'
import moment from 'moment'
import { SearchOutlined } from '@ant-design/icons'
import { parseRewardFlowSourceOptions } from '../../dropCommon'

const namespace = 'RewardFlowReport' // model 的 namespace
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker

@connect(({ RewardFlowReport }) => ({ // model 的 namespace
  model: RewardFlowReport // model 的 namespace
}))

class DetailReportComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      compereUid: 0,
      supportUid: 0,
      dateRange: [moment().subtract(7, 'days'), moment().subtract(0, 'days')],
      visible: false
    }
  }

  componentDidMount () {
    const { dispatch } = this.props
    const { dateRange, compereUid, supportUid } = this.state
    let data = { begin: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat), compereUid: compereUid, supportUid: supportUid }

    dispatch({
      type: `${namespace}/getDetailReportList`,
      payload: data
    })
  }

  onFinish = values => {
    const { dispatch } = this.props
    const { dateRange, compereUid, supportUid } = this.state
    let data = { begin: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat), compereUid: compereUid, supportUid: supportUid }
    dispatch({
      type: `${namespace}/getDetailReportList`,
      payload: data
    })
  }

  // 需要修改
  columns = [
    { title: '日期', dataIndex: 'timestamp', align: 'center', render: timestamp => moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss') },
    { title: '渠道', dataIndex: 'source', align: 'center', render: text => parseRewardFlowSourceOptions(text) },
    { title: '主持uid', dataIndex: 'compereUid', align: 'center' },
    { title: '主持昵称', dataIndex: 'compereNick', align: 'center' },
    { title: '厅级频道短位', dataIndex: 'asid', align: 'center' },
    { title: '子频道', dataIndex: 'ssid', align: 'center' },
    { title: '物资内容', dataIndex: 'rewardDesc', align: 'center' },
    { title: '比例', dataIndex: 'percent', align: 'center', render: text => `${text}%` },
    { title: '获得紫金币数量', dataIndex: 'extraReward', align: 'center' },
    { title: '消耗(紫水晶)', dataIndex: 'used', align: 'center' },
    { title: '空投福利(紫水晶)', dataIndex: 'reward', align: 'center' },
    { title: '贡献用户uid', dataIndex: 'uid', align: 'center' },
    { title: '贡献用户昵称', dataIndex: 'nick', align: 'center' }
  ]

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { detailReportList } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form onFinish={this.onFinish}>
          <Row gutter={12}>
            <Col>
              <Form.Item name='dateRange' >
                <RangePicker defaultValue={dateRange} onChange={(date, format) => this.setState({ dateRange: date })} format={'YYYY-MM-DD'} />
              </Form.Item>
            </Col>
            <Col>
              主持uid
              <Input onChange={e => this.setState({ compereUid: e.target.value })} style={{ marginLeft: 10, width: 150 }} /> {/* 搜索按钮 */}
            </Col>
            <Col>
              贡献用户uid
              <Input onChange={e => this.setState({ supportUid: e.target.value })} style={{ marginLeft: 10, width: 150 }} /> {/* 搜索按钮 */}
            </Col>
            <Col>
              <Form.Item>
                <Button type='primary' htmlType='submit'><SearchOutlined />Search</Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Table rowKey='index' dataSource={detailReportList} columns={this.columns} size='small' pagination={{ pageSize: 500 }} /> {/* 显示的列表 */}
      </Card>
    )
  }
}

export default DetailReportComponent
