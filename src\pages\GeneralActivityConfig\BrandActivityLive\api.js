import request from '@/utils/request'
import { stringify } from 'qs'

// 魔法任务配置列表
export function getMagicConfigList (params) {
  return request(`/brand_prop_activity_boss/magic_task/stage_config_list?${stringify(params)}`)
}

// 更新魔法任务配置
export function updateMagicConfig (params) {
  return request(`/brand_prop_activity_boss/magic_task/stage_config_update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

// 删除魔法任务配置
export function deleteMagicConfig (params) {
  return request(`/brand_prop_activity_boss/magic_task/stage_config_delete?${stringify(params)}`)
}

// 常规任务配置列表
export function getRoutineConfigList (params) {
  return request(`/brand_routine_boss/stage_config_list?${stringify(params)}`)
}

// 常规任务配置
export function updateRoutineConfig (params) {
  return request(`/brand_routine_boss/stage_config_update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

// 更新常规任务配置
export function deleteRoutineConfig (params) {
  return request(`/brand_routine_boss/stage_config_delete?${stringify(params)}`)
}

// ========================= 活动玩法 ==========================================

// 魔法任务配置列表
export function getLiveConfigList (params) {
  return request(`/brand_prop_activity_boss/live_task/stage_config_list?${stringify(params)}`)
}

// 更新魔法任务配置
export function updateLiveConfig (params) {
  return request(`/brand_prop_activity_boss/live_task/stage_config_update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

// 删除魔法任务配置
export function deleteLiveConfig (params) {
  return request(`/brand_prop_activity_boss/live_task/stage_config_delete?${stringify(params)}`)
}

// ========================= 手Y专属任务 ==========================================

// 查询配置列表
export function getYYMobileConfigList (params) {
  return request(`/brand_routine_boss/yymobile/stage_config_list?${stringify(params)}`)
}

// 更新配置信息
export function updateYYMobileConfig (params) {
  return request(`/brand_routine_boss/yymobile/stage_config_update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

// 删除配置信息
export function deleteYYMobileConfig (params) {
  return request(`/brand_routine_boss/yymobile/stage_config_delete?${stringify(params)}`)
}
