import request from '@/utils/request'
import { stringify } from 'qs'

// 获取列表 包含搜索
export function getList (params) {
  return request(`/fts_compere_picture/search_picture_list?${stringify(params)}`)
}

// 审批: 一健通过、一键不通过 支持批量
export function approvalItem (params) {
  return request(`/fts_compere_picture/batch_update_picture_status`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function updateItem (params) {
  return request(`/fts_compere_picture/update_picture_status?${stringify(params)}`)
}

// 主持推荐图审核详情
export function getStatusDetail (params) {
  return request(`/new_compere_info/compere_pic_status_detail?${stringify(params)}`)
}
