import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Table, Popover, Input, Button, Divider, message, Drawer, Popconfirm, Typography, Space, Tooltip } from 'antd'
import moment from 'moment'
import AdminApprovalProgress from './progress'
import ApprovalDetailComponent from './approval_detail'
import { getDecoderObject, setColumnUrlRender } from './approval_decoder'
import Modal from 'antd/lib/modal/Modal'
import { Link } from 'dva/router'
import { fixURI } from '@/utils/common'
import { SearchSelect } from '@/components/SimpleComponents'
const namespace = 'adminApproval'
const { Title } = Typography

@connect(({ adminApproval }) => ({
  model: adminApproval
}))

class AdminApprovalToApproval extends Component {
  columns = [
    // eslint-disable-next-line no-eval
    { title: '序号', align: 'center', render: (v, _, index) => index + 1 },
    { title: '审批号', dataIndex: 'nid', align: 'center' },
    { title: '审批规则', dataIndex: 'rule', align: 'center', render: v => v.name },
    { title: '快速跳转', dataIndex: 'rule', align: 'center', render: v => v.link ? <Link to={fixURI(v.link)}>快速跳转</Link> : '' },
    { title: '申请人', dataIndex: 'creator', align: 'center', render: (v) => <Tooltip title={`uid=${v.uid}`} >{v.passport || '(昵称为空)'}</Tooltip> },
    { title: '申请时间', dataIndex: 'timestamp', align: 'center', sorter: (a, b) => { return a.timestamp > b.timestamp ? 1 : -1 }, render: v => moment.unix(v).format('YYYY-MM-DD HH:mm:ss') },
    { title: '审批内容', align: 'center', render: (v, rec) => <a onClick={this.showContent(rec)}>查看详情</a> },
    { title: '审批进度', dataIndex: 'rule', align: 'center', render: (v, r) => { return this.ProgressFormater(r) } },
    { title: '操作',
      align: 'center',
      render: (_, rec) => <div>
        <Popover content={this.renderContent(rec, 'Passed')} trigger='click'><a>通过</a></Popover>
        <Divider type='vertical' />
        <Popover content={this.renderContent(rec, 'Rejected')} trigger='click'><a>驳回</a></Popover>
      </div>
    }
  ]

  state = { decoder: {}, content: {}, visible: false, text: '', specText: {}, selectedRowKeys: [], selectedRows: [], reason: {}, append: '' }

  dfs (uri, index, menuList) {
    menuList = menuList || []
    for (let i = 0; i < menuList.length; i++) {
      if (menuList[i].uri === uri) {
        return `${index}/${uri.replace(/^\//, '').replace('.html', '')}`
      }

      let path = this.dfs(uri, `${index}/${i}`, menuList[i].menuItemList)
      if (path !== undefined) {
        return path
      }
    }
  }

  showContent = rec => e => {
    this.setState({ pid: rec.pid, rule: rec.rule, value: rec.text, visible: true, append: rec.append })
  }

  ProgressFormater = (r) => { 
    return <Popover content={this.renderProgress(r.rule, r)}>
      {
        r.aprLeader && r.aprLeader?.length > 0
          ? '直属上级审批'
          : r?.approvals?.length > 0 ? '二级审批人' : '一级审批人'
      } 
    </Popover>
  }

  renderProgress = (v, rec) => {
    return <AdminApprovalProgress rule={v} proposer={rec} approvals={rec.approvals} result={rec.result} />
  }
  
  onSpecChange = value => {
    let { specText } = this.state
    if (specText[value.id] === undefined) {
      specText[value.id] = { [value.index]: value.value }
    } else {
      specText[value.id][value.index] = value.value
    }

    // console.log(value, specText)

    this.setState({ specText })
  }

  componentDidMount () {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/getToApproval`
    })

    dispatch({
      type: `${namespace}/getMenuItem`
    })
  }

  renderContent = (rec, result) => {
    const mustReason = result === 'Rejected'
    return (
      <div>
        <Input.TextArea onChange={this.onTextChange} row={2} placeholder={mustReason ? '必填' : '选填...'} />
        <Button onClick={this.onApproval(rec, result, mustReason)} style={{ marginLeft: 120, marginTop: 5 }} type='primary'>确定</Button>
      </div>
    )
  }

  onTextChange = e => {
    this.setState({ text: e.target.value })
  }

  onApproval = (rec, result, mustReason) => () => {
    const { text, specText } = this.state
    const { dispatch } = this.props
    if (mustReason && !text) {
      message.warn('原因必填')
      return
    }
    rec.reason = text || '(空)'
    rec.result = result
    rec.specText = specText[rec.pid] ? JSON.stringify(Object.keys(specText[rec.pid])?.map(v => { return { index: parseInt(v), text: specText[rec.pid][v] } })) : ''

    // console.log(rec)

    dispatch({
      type: `${namespace}/doApproval`,
      payload: rec
    })

    this.setState({ text: '' })
  }

  getRuleOpts = () => {
    let arr = this.props.model.toApprovalList?.map(v => { return { label: v.rule.name + (v.rule.innerMode > 0 ? '(审批内容外显)' : ''), value: v.rid } }).sort((a, b) => { return a.value - b.value })
    let list = [{ label: '全部待审批', value: 0 }]
    arr.forEach(item => {
      if (list[list.length - 1].value === item.value) {
        return
      }

      list.push(item)
    })

    return list
  }

  filterByRule = rid => {
    this.props.dispatch({
      type: rid > 0 ? `${namespace}/getToApprovalByRule` : `${namespace}/getToApproval`,
      payload: { rid }
    })
  }

  renderColumns = toApproval => {
    if (toApproval.length === 0) {
      return this.columns
    }

    let rule = toApproval[0].rule
    if (rule === undefined || rule.rid === 0 || rule.innerMode !== 1) { // 不启用审批外显模式
      return this.columns
    }

    // 固定模块
    let renderCol = [
      { title: '序号', align: 'center', render: (v, _, index) => index + 1 },
      { title: '审批号', dataIndex: 'nid', align: 'center' }
      // { title: '审批规则', dataIndex: 'rule', align: 'center', render: v => v.name }
    ]

    // 动态解析
    let decoderObject = getDecoderObject(rule)
    Object.keys(decoderObject).map(key => {
      let col = { title: decoderObject[key], dataIndex: ['parseText', key], align: 'center' }

      setColumnUrlRender(rule, col)

      renderCol.push(col)
    })

    renderCol.push(...[
      { title: '审批进度', dataIndex: 'rule', align: 'center', render: (v, r) => { return this.ProgressFormater(r) } },
      { title: '备注',
        align: 'center',
        render: (v, rec) => <Input onChange={this.onReasonChange(rec.pid)} style={{ width: 100 }} placeholder='审批说明' />
      }])

    return renderCol
  }

  onReasonChange = pid => e => {
    // console.log(pid, e.target.value)
    let reason = $.extend({}, true, this.state.reason)
    reason[pid] = e.target.value
    this.setState({ reason })
  }

  // 选中Table左侧Checkbox
  onSelectChange = (selectedRowKeys, selectedRows) => {
    // console.log(selectedRows, selectedRowKeys)

    this.setState({ selectedRowKeys, selectedRows })
  }

  onBatchApproval = result => () => {
    const { selectedRows, reason } = this.state
    const { dispatch } = this.props

    if (selectedRows === undefined || selectedRows.length === 0) {
      message.warn('未选择审批记录')
      return
    }

    let payload = $.extend([], true, selectedRows)
    for (let i = 0; i < payload.length; i++) {
      let mustReason = (result === 'Rejected')
      if (mustReason && !reason[payload[i].pid]) {
        message.warn('驳回时原因必填')
        return
      }
      payload[i].reason = reason[payload[i].pid]
      payload[i].result = result
    }

    dispatch({
      type: `${namespace}/doBatchApproval`,
      payload: payload
    })

    this.setState({ selectedRowKeys: [], selectedRows: [] })
  }

  onCancel = () => {
    this.setState({ visible: false })
  }

  // 按照指定字段将数组ary分组, 结果返回二维数组
  groupArrayByFiled (ary, field) {
    let fieldToIndex = {}
    let result = []

    ary.forEach((item) => {
      const key = item[field]
      if (fieldToIndex[key] === undefined) {
        let index = result.length
        result[index] = [item]
        fieldToIndex[key] = index
        return
      }
      result[fieldToIndex[key]].push(item)
    })
    return result
  }

  render () {
    const { model: { toApprovalList } } = this.props
    const { visible, rule, value, pid, append, selectedRowKeys } = this.state
    const approvalGroup = this.groupArrayByFiled(toApprovalList, 'rid')

    return (
      <Card>
        <Space direction='horizontal' size='small'>
          <SearchSelect onChange={this.filterByRule} placeholder='按审批规则过滤' style={{ width: 240 }} options={this.getRuleOpts()} />
          <Popconfirm onConfirm={this.onBatchApproval('Passed')} title='确定通过？' okText='确定' cancelText='取消' trigger='click'>
            <Button disabled={selectedRowKeys.length === 0} type='primary' >一键通过</Button>
          </Popconfirm>
          <Popconfirm onConfirm={this.onBatchApproval('Rejected')} title='确定驳回？' okText='确定' cancelText='取消' trigger='click'>
            <Button disabled={selectedRowKeys.length === 0} danger type='primary' >一键驳回</Button>
          </Popconfirm>
        </Space>
        <Divider />

        {
          approvalGroup.map((list) => {
            return <>
              <Title level={5}>{list[0].rule.name}</Title>
              <Table
                size='small'
                rowKey='pid'
                // selectedRowKeys={selectedRowKeys}
                rowSelection={{ selectedRowKeys: selectedRowKeys, onChange: this.onSelectChange }}
                pagination={false}
                columns={this.renderColumns(list)}
                dataSource={list}
              />
              <Divider />
            </>
          })
        }

        {
        rule?.showMode === 3 //  3-弹窗显示模式，
          ? <Modal width={750} visible={visible} footer={null} title='审批详情' onCancel={this.onCancel}>
            <ApprovalDetailComponent onChange={this.onSpecChange} id={pid} rule={rule} value={value} append={append} />
          </Modal>
          : <Drawer height='50%' closable={false} visible={visible} placement='top' onClose={this.onCancel}>
            <ApprovalDetailComponent onChange={this.onSpecChange} id={pid} rule={rule} value={value} append={append} />
          </Drawer>
        }
      </Card>
    )
  }
}

export default AdminApprovalToApproval
