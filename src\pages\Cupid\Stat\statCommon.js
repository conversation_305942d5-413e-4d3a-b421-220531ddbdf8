const probabilityBase = 10000

// 奖池枚举
export const ballTypeOptions = [
  { label: '所有', value: 0 },
  { label: '汇总', value: 1000 },
  { label: '甜蜜气球', value: 2 },
  { label: '梦幻气球', value: 3 },
  { label: '璀璨气球', value: 4 }
]

// statType 渠道统计 1000-汇总 0-pc 1-手Y 2-追玩(yo交友) 3-yomi 4-web 7-h5
export const statTypeOptions = [
  { label: '汇总', value: 1000 },
  { label: 'PC', value: 0 },
  { label: 'Yo语音', value: 3 },
  { label: 'WEB', value: 4 },
  { label: 'H5', value: 7 },
  // { label: 'Yo交友', value: 2 },
  { label: '好看', value: 8 },
  { label: '贴吧', value: 9 }
]

// 新老用户类型选项
export const userTypeOptions = [
  { label: '老用户', value: 0 },
  { label: '新用户', value: 1 },
  { label: '全部', value: 1000 }
]

export const optionsFormater = (options, value) => {
  let label = `${value}?`
  options.forEach(item => {
    if (item.value === value) {
      label = item.label
    }
  })
  return label
}

// 紫水晶转元
export const valueFormater = (v) => {
  return Number(v)
}

// 流水区间格式化
export const flowRangeFormater = (v) => {
  // [0,1000）、[1000,5000）、[5000,10000）、10000
  if (v < 1000) {
    return ' [0,1000)'
  }
  if (v < 5000) {
    return '[1000,5000)'
  }
  if (v < 10000) {
    return '[5000,10000)'
  }
  return '10000+'
}

// 流水区间格式化
export const flowRangeFormaterV2 = (v) => {
  const start = v * 1000
  const end = start + 1000
  return `[${start}, ${end})`
}

// 概率格式化
export const rateForamter = (v) => {
  if (v === 0 || v === undefined) {
    return 'NAN'
  }
  return `${Number(v * 100.0 / probabilityBase).toFixed(2)}%`
}

export const extOutFormater = (r) => {
  const { flowBase, flowRate, totalIn } = r
  if (flowRate === 0 || flowRate === undefined) {
    return 'NAN'
  }
  const val = Number(totalIn * flowRate / flowBase).toFixed(0)
  return valueFormater(val)
}

// 累计金额 ( 附加池流入 - 附加池产出 )
// 附加池流入 = 当天玩法流水（totalIn） * rate/base
// 附加池产出 = 狂欢产出 + 祝福产出
export const sumIncome = (r) => {
  const { crazyOut, blessOut, flowBase, flowRate, totalIn } = r
  if (flowRate === 0 || flowRate === undefined) {
    return 'NAN'
  }
  const extOut = Number(crazyOut + blessOut) // 附加池产出
  const extIn = Number(totalIn * flowRate / flowBase) // 流入
  const val = Number(extIn - extOut).toFixed(0)
  return Number(val)
}

export const totalOutFormater = (r) => {
  const { totalOut, frags } = r
  return valueFormater(totalOut + frags)
}

export const outRateFormater = (r) => {
  const { totalOut, totalIn, frags } = r
  if (totalIn === 0) {
    return '0%'
  }
  return `${((totalOut + frags) * 100.0 / totalIn).toFixed(1)}%`
}

export const outRateFormaterV2 = (r) => {
  const { totalOut, totalIn } = r
  if (totalIn === 0) {
    return '0%'
  }
  return `${((totalOut) * 100.0 / totalIn).toFixed(1)}%`
}

export const totalOffsetFormater = (r) => {
  const { totalOffset, frags } = r
  return valueFormater(totalOffset - frags)
}
