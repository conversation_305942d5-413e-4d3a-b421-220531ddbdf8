import React, { Component } from 'react'
import { Tabs as AntTabs } from 'antd'
import { getQueryVariable, changeURLParam } from '@/utils/common'

export const TabPane = AntTabs.TabPane

/* 简介： 对Tabs进行封装, 实现刷新页面后不会重置tab位置, 使用方法与antd Tabs一致
额外属性:
id: string, 必填,url参数的前缀
hideSuffix: bool, 默认false
*/
export default class Tabs extends Component {
  state = {
    currentKey: ''
  }

  getTabId = () => {
    const { id, hideSuffix } = this.props
    return `${id || ''}${hideSuffix ? '' : '_tab'}`
  }

  componentDidMount = () => {
    let key = getQueryVariable(this.getTabId())
    let defaultKey = this.props.defaultActiveKey
    if (key) {
      this.setState({ currentKey: key })
    } else if (defaultKey) {
      this.setState({ currentKey: defaultKey })
    }
  }

  onChange = (key) => {
    changeURLParam(this.getTabId(), key)
    this.setState({ currentKey: key })
    if (this.props.onChange) {
      this.props.onChange(key)
    }
  }

  render () {
    const { currentKey } = this.state
    return (
      <AntTabs {...this.props} defaultActiveKey={currentKey} activeKey={currentKey} onChange={this.onChange}>
        {this.props.children}
      </AntTabs>
    )
  }
}
