import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Input, Tabs, Tooltip, Modal, Select, Popconfirm } from 'antd'
import { connect } from 'dva'
import dateString from '@/utils/dateString'
import { exportExcel } from 'xlsx-oc'
import moment from 'moment'
import PopImage2 from '@/components/PopImage2'
import 'moment/locale/zh-cn'
import { QuestionCircleOutlined } from '@ant-design/icons'
moment.locale('zh-cn')

const namespace = 'esignAuditing'
const TabPane = Tabs.TabPane
const Option = Select.Option
const FormItem = Form.Item

@connect(({ esignAuditing }) => ({
  model: esignAuditing
}))
class ESignAuditing extends Component {
  genTitle = (title, tips) => {
    return <span>
      <Tooltip title={tips}>
        <QuestionCircleOutlined style={{ marginRight: '0.25em' }} />
        {title}
      </Tooltip>
    </span>
  }

  // 需要修改
  ColumnOptorAudit = [
    { title: '业务',
      dataIndex: 'btype',
      align: 'center',
      width: 60,
      fixed: 'left',
      render: (text, record) => {
        if (text === 0) {
          return '交友'
        } else if (text === 1) {
          return '约战'
        } else if (text === 2) {
          return '宝贝'
        }
        return ''
      }
    },
    { title: '提交审批时间', dataIndex: 'subtime', align: 'center', width: 160, fixed: 'left', render: text => dateString(text) },
    { title: '厅管UID', dataIndex: 'uid', width: 120, fixed: 'left', align: 'center' },
    { title: '厅管昵称', dataIndex: 'nick', width: 120, fixed: 'left', align: 'center' },
    { title: '厅管YY号', dataIndex: 'yy', width: 120, fixed: 'left', align: 'center' },
    { title: '真实姓名', dataIndex: 'rname', width: 120, fixed: 'left', align: 'center' },
    { title: '身份证号', dataIndex: 'cardno', width: 180, fixed: 'left', align: 'center' },
    { title: '签约公司', dataIndex: 'corpname', width: 180, fixed: 'left', align: 'center' },
    { title: '顶级频道', dataIndex: 'sid', width: 100, lign: 'center' },
    { title: '电子签约频道', dataIndex: 'asid', width: 120, align: 'center' },
    { title: '刷脸认证', dataIndex: 'faceimg', align: 'center', width: 120, render: text => <PopImage2 width={60} height={120} marginLeft={1} value={text} /> },
    { title: '身份证正面',
      width: 100,
      align: 'center',
      dataIndex: 'front',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.front, '身份证正面')}>查看</a>
        </span>
      )
    },
    { title: '身份证反面',
      width: 100,
      align: 'center',
      dataIndex: 'back',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.back, '身份证反面')}>查看</a>
        </span>
      )
    },
    { title: '手持身份证正面',
      width: 100,
      align: 'center',
      dataIndex: 'holdfront',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.holdfront, '手持身份证正面')}>查看</a>
        </span>
      )
    },
    { title: '手持身份证反面',
      width: 100,
      align: 'center',
      dataIndex: 'holdback',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.holdback, '手持身份证反面')}>查看</a>
        </span>
      )
    },
    { title: '微信实名认证截图',
      width: 100,
      align: 'center',
      dataIndex: 'wechatscreenshot',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.wechatscreenshot, '微信实名认证截图')}>查看</a>
        </span>
      )
    },
    { title: '支付宝实名认证截图',
      width: 100,
      align: 'center',
      dataIndex: 'alipayscreenshot',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.alipayscreenshot, '支付宝实名认证截图')}>查看</a>
        </span>
      )
    },
    { title: '微信实名认证录屏',
      width: 100,
      dataIndex: 'wechatrecordscreen',
      align: 'center',
      render: (text, record) => (
        <div>
          {record.wechatrecordscreen ? <div><a href={record.wechatrecordscreen}>查看</a></div> : '' }
        </div>
      )
    },
    { title: '支付宝实名认证录屏',
      width: 100,
      dataIndex: 'alipayrecordscreen',
      align: 'center',
      render: (text, record) => (
        <div>
          {record.alipayrecordscreen ? <div><a href={record.alipayrecordscreen}>查看</a></div> : '' }
        </div>
      )
    },
    { title: '查看合同1',
      width: 100,
      dataIndex: 'pdf1',
      align: 'center',
      render: (text, record) => (
        <div>
          <div><a href={'/esign_boss/op_esign_contract_url?type=1&id=' + record.id} target='view_window'>查看</a></div>
        </div>
      )
    },
    { title: '查看合同2',
      width: 100,
      dataIndex: 'pdf2',
      align: 'center',
      render: (text, record) => (
        <div>
          <div><a href={'/esign_boss/op_esign_contract_url?type=2&id=' + record.id} target='view_window'>查看</a></div>
        </div>
      )
    },
    { title: '查看承诺保证书',
      width: 100,
      dataIndex: 'guaranteepdf',
      align: 'center',
      render: (text, record) => (
        <div>
          <div><a href={'/esign_boss/op_esign_contract_url?type=3&id=' + record.id} target='view_window'>查看</a></div>
        </div>
      )
    },
    { title: '签约视频',
      width: 100,
      dataIndex: 'videofile',
      align: 'center',
      render: (text, record) => (
        <div>
          {record.videofile ? <div><a href={record.videofile}>查看</a></div> : '' }
        </div>
      )
    },
    { title: '厅管资料',
      width: 100,
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showProfileModal(record)}>查看</a>
        </span>
      )
    },
    { title: '审批状态',
      width: 100,
      dataIndex: 'stage',
      align: 'center',
      render: (text, record) => {
        switch (text) {
          case 4:
            return '未审批'
          case 5:
          case 6:
          case 8:
          case 10:
            return '已接受'
          case 7:
            return '已拒绝'
        }
      }
    },
    { title: '操作',
      width: 140,
      align: 'center',
      render: (record) => {
        switch (record.stage) {
          case 4:
            return (<div><Popconfirm title='确认接受厅管的签约申请?' type='primary' onConfirm={this.preHandleAudit(record.id, 1, 'optor')} okText='确认' cancelText='取消'>
              <a href=''>接受</a></Popconfirm><Divider type='vertical' /><a onClick={this.showFillRefuse(record.id, 'optor')}>拒绝</a></div>)
          case 5:
            return '已提交财务审核'
          case 6:
            return '已归档'
          case 7:
            return <a onClick={this.showRefuseModal(record)}>查看拒绝理由</a>
          case 8:
            return '财务已拒绝'
          case 10:
            return '已失效'
        }
      }
    }
  ]

  ColumnFinanceAudit = [
    { title: '业务',
      dataIndex: 'btype',
      width: 60,
      fixed: 'left',
      align: 'center',
      render: (text, record) => {
        if (text === 0) {
          return '交友'
        } else if (text === 1) {
          return '约战'
        } else if (text === 2) {
          return '宝贝'
        }
        return ''
      }
    },
    { title: '提交审批时间', dataIndex: 'subtime', align: 'center', width: 160, fixed: 'left', render: text => dateString(text) },
    { title: '厅管UID', dataIndex: 'uid', width: 120, fixed: 'left', align: 'center' },
    { title: '厅管昵称', dataIndex: 'nick', width: 120, fixed: 'left', align: 'center' },
    { title: '厅管YY号', dataIndex: 'yy', width: 120, fixed: 'left', align: 'center' },
    { title: '真实姓名', dataIndex: 'rname', width: 120, fixed: 'left', align: 'center' },
    { title: '身份证号', dataIndex: 'cardno', width: 180, fixed: 'left', align: 'center' },
    { title: '签约公司', dataIndex: 'corpname', width: 180, fixed: 'left', align: 'center' },
    { title: '顶级频道', dataIndex: 'sid', width: 100, align: 'center' },
    { title: '电子签约频道', dataIndex: 'asid', width: 120, align: 'center' },
    { title: '刷脸认证', dataIndex: 'facefile', align: 'center', width: 120, render: text => <PopImage2 width={60} height={120} marginLeft={1} value={text} /> },
    { title: '身份证正面',
      width: 100,
      align: 'center',
      dataIndex: 'front',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.front, '身份证正面')}>查看</a>
        </span>
      )
    },
    { title: '身份证反面',
      width: 100,
      align: 'center',
      dataIndex: 'back',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.back, '身份证反面')}>查看</a>
        </span>
      )
    },
    { title: '手持身份证正面',
      width: 100,
      align: 'center',
      dataIndex: 'holdfront',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.holdfront, '手持身份证正面')}>查看</a>
        </span>
      )
    },
    { title: '手持身份证反面',
      width: 100,
      align: 'center',
      dataIndex: 'holdback',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.holdback, '手持身份证反面')}>查看</a>
        </span>
      )
    },
    { title: '微信实名认证截图',
      width: 100,
      align: 'center',
      dataIndex: 'wechatscreenshot',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.wechatscreenshot, '微信实名认证截图')}>查看</a>
        </span>
      )
    },
    { title: '支付宝实名认证截图',
      width: 100,
      align: 'center',
      dataIndex: 'alipayscreenshot',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.alipayscreenshot, '支付宝实名认证截图')}>查看</a>
        </span>
      )
    },
    { title: '微信实名认证录屏',
      width: 100,
      dataIndex: 'wechatrecordscreen',
      align: 'center',
      render: (text, record) => (
        <div>
          {record.wechatrecordscreen ? <div><a href={record.wechatrecordscreen}>查看</a></div> : '' }
        </div>
      )
    },
    { title: '支付宝实名认证录屏',
      width: 100,
      dataIndex: 'alipayrecordscreen',
      align: 'center',
      render: (text, record) => (
        <div>
          {record.alipayrecordscreen ? <div><a href={record.alipayrecordscreen}>查看</a></div> : '' }
        </div>
      )
    },
    { title: '查看合同1',
      width: 100,
      dataIndex: 'pdf1',
      align: 'center',
      render: (text, record) => (
        <div>
          <div><a href={'/esign_boss/op_esign_contract_url?type=1&id=' + record.id} target='view_window'>查看</a></div>
        </div>
      )
    },
    { title: '查看合同2',
      width: 100,
      dataIndex: 'pdf2',
      align: 'center',
      render: (text, record) => (
        <div>
          <div><a href={'/esign_boss/op_esign_contract_url?type=2&id=' + record.id} target='view_window'>查看</a></div>
        </div>
      )
    },
    { title: '查看承诺保证书',
      width: 100,
      dataIndex: 'guaranteepdf',
      align: 'center',
      render: (text, record) => (
        <div>
          <div><a href={'/esign_boss/op_esign_contract_url?type=3&id=' + record.id} target='view_window'>查看</a></div>
        </div>
      )
    },
    { title: '签约视频',
      width: 100,
      dataIndex: 'videofile',
      align: 'center',
      render: (text, record) => (
        <div>
          {record.videofile ? <div><a href={record.videofile}>查看</a></div> : '' }
        </div>
      )
    },
    { title: '厅管资料',
      width: 100,
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showProfileModal(record)}>查看</a>
        </span>
      )
    },
    { title: '审批状态',
      width: 100,
      dataIndex: 'stage',
      align: 'center',
      render: (text, record) => {
        switch (text) {
          case 5:
            return '未审批'
          case 6:
          case 10:
            return '已接受'
          case 8:
            return '已拒绝'
        }
      }
    },
    { title: '操作',
      width: 140,
      align: 'center',
      render: (record) => {
        console.log(record.stage)
        switch (record.stage) {
          case 5:
            return (<div><Popconfirm title='确认接受厅管的签约申请?' type='primary' onConfirm={this.preHandleAudit(record.id, 1, 'financial')} okText='确认' cancelText='取消'>
              <a href=''>接受</a></Popconfirm><Divider type='vertical' /><a onClick={this.showFillRefuse(record.id, 'financial')}>拒绝</a></div>)
          case 6:
            return '已归档'
          case 8:
            return <a onClick={this.showRefuseModal(record)}>查看拒绝理由</a>
          case 10:
            return '已失效'
        }
      }
    }
  ]

  ColumnsArchived = [
    { title: '业务',
      dataIndex: 'btype',
      align: 'center',
      width: 60,
      fixed: 'left',
      render: (text, record) => {
        if (text === 0) {
          return '交友'
        } else if (text === 1) {
          return '约战'
        } else if (text === 2) {
          return '宝贝'
        }
        return ''
      }
    },
    { title: '提交审批时间', dataIndex: 'subtime', align: 'center', width: 160, fixed: 'left', render: text => dateString(text) },
    { title: '厅管UID', dataIndex: 'uid', width: 120, fixed: 'left', align: 'center' },
    { title: '厅管昵称', dataIndex: 'nick', width: 120, fixed: 'left', align: 'center' },
    { title: '厅管YY号', dataIndex: 'yy', width: 120, fixed: 'left', align: 'center' },
    { title: '真实姓名', dataIndex: 'rname', width: 120, fixed: 'left', align: 'center' },
    { title: '身份证号', dataIndex: 'cardno', width: 180, fixed: 'left', align: 'center' },
    { title: '签约公司', dataIndex: 'corpname', width: 180, fixed: 'left', align: 'center' },
    { title: '顶级频道', dataIndex: 'sid', width: 100, align: 'center' },
    { title: '电子签约频道', dataIndex: 'asid', width: 120, align: 'center' },
    { title: '三方合同ID', dataIndex: 'contractid2', align: 'center', width: 180 },
    { title: '刷脸认证', dataIndex: 'faceimg', align: 'center', width: 120, render: text => <PopImage2 width={60} height={120} marginLeft={1} value={text} /> },
    { title: '身份证正面',
      width: 100,
      align: 'center',
      dataIndex: 'front',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.front, '身份证正面')}>查看</a>
        </span>
      )
    },
    { title: '身份证反面',
      width: 100,
      align: 'center',
      dataIndex: 'back',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.back, '身份证反面')}>查看</a>
        </span>
      )
    },
    { title: '手持身份证正面',
      width: 100,
      align: 'center',
      dataIndex: 'holdfront',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.holdfront, '手持身份证正面')}>查看</a>
        </span>
      )
    },
    { title: '手持身份证反面',
      width: 100,
      align: 'center',
      dataIndex: 'holdback',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.holdback, '手持身份证反面')}>查看</a>
        </span>
      )
    },
    { title: '微信实名认证截图',
      width: 100,
      align: 'center',
      dataIndex: 'wechatscreenshot',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.wechatscreenshot, '微信实名认证截图')}>查看</a>
        </span>
      )
    },
    { title: '支付宝实名认证截图',
      width: 100,
      align: 'center',
      dataIndex: 'alipayscreenshot',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.alipayscreenshot, '支付宝实名认证截图')}>查看</a>
        </span>
      )
    },
    { title: '微信实名认证录屏',
      width: 100,
      dataIndex: 'wechatrecordscreen',
      align: 'center',
      render: (text, record) => (
        <div>
          {record.wechatrecordscreen ? <div><a href={record.wechatrecordscreen}>查看</a></div> : '' }
        </div>
      )
    },
    { title: '支付宝实名认证录屏',
      width: 100,
      dataIndex: 'alipayrecordscreen',
      align: 'center',
      render: (text, record) => (
        <div>
          {record.alipayrecordscreen ? <div><a href={record.alipayrecordscreen}>查看</a></div> : '' }
        </div>
      )
    },
    { title: '查看合同1',
      width: 100,
      dataIndex: 'pdf1',
      align: 'center',
      render: (text, record) => (
        <div>
          {text.length > 0 ? <div><a href={text}>查看</a></div> : <div><a href={'/esign_boss/op_esign_contract_url?type=1&id=' + record.id} target='view_window'>查看</a></div> }
        </div>
      )
    },
    { title: '查看合同2',
      width: 100,
      dataIndex: 'pdf2',
      align: 'center',
      render: (text, record) => (
        <div>
          {text.length > 0 ? <div><a href={text}>查看</a></div> : <div><a href={'/esign_boss/op_esign_contract_url?type=2&id=' + record.id} target='view_window'>查看</a></div> }
        </div>
      )
    },
    { title: '查看承诺保证书',
      width: 100,
      dataIndex: 'guaranteepdf',
      align: 'center',
      render: (text, record) => (
        <div>
          {text.length > 0 ? <div><a href={text}>查看</a></div> : <div><a href={'/esign_boss/op_esign_contract_url?type=3&id=' + record.id} target='view_window'>查看</a></div> }
        </div>
      )
    },
    { title: '查看存证页1',
      width: 100,
      dataIndex: 'attachment1',
      align: 'center',
      render: (text, record) => (
        <div>
          {text.length > 0 ? <div><a href={text}>查看</a></div> : <div><a href={'/esign_boss/op_esign_contract_url?type=4&id=' + record.id} target='view_window'>查看</a></div> }
        </div>
      )
    },
    { title: '查看存证页2',
      width: 100,
      dataIndex: 'attachment2',
      align: 'center',
      render: (text, record) => (
        <div>
          {text.length > 0 ? <div><a href={text}>查看</a></div> : <div><a href={'/esign_boss/op_esign_contract_url?type=5&id=' + record.id} target='view_window'>查看</a></div> }
        </div>
      )
    },
    { title: '查看承诺保证书存证页',
      width: 100,
      dataIndex: 'gattachment',
      align: 'center',
      render: (text, record) => (
        <div>
          {text.length > 0 ? <div><a href={text}>查看</a></div> : <div><a href={'/esign_boss/op_esign_contract_url?type=6&id=' + record.id} target='view_window'>查看</a></div> }
        </div>
      )
    },
    { title: '签约视频',
      width: 100,
      dataIndex: 'videofile',
      align: 'center',
      render: (text, record) => (
        <div>
          {record.videofile ? <div><a href={record.videofile}>查看</a></div> : '' }
        </div>
      )
    },
    { title: '厅管资料',
      width: 100,
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showProfileModal(record)}>查看</a>
        </span>
      )
    },
    { title: '操作',
      width: 140,
      align: 'center',
      render: (record) => {
        return <a onClick={this.showFillCancelContract(record)}><font style={{ marginLeft: 10 }} color='red'>终止合同</font></a>
      }
    }
  ]

  ColumnsContractCancel = [
    { title: '业务',
      dataIndex: 'btype',
      align: 'center',
      width: 60,
      fixed: 'left',
      render: (text, record) => {
        if (text === 0) {
          return '交友'
        } else if (text === 1) {
          return '约战'
        } else if (text === 2) {
          return '宝贝'
        }
        return ''
      }
    },
    { title: '提交审批时间', dataIndex: 'subtime', align: 'center', width: 160, fixed: 'left', render: text => dateString(text) },
    { title: '厅管UID', dataIndex: 'uid', width: 120, fixed: 'left', align: 'center' },
    { title: '厅管昵称', dataIndex: 'nick', width: 120, fixed: 'left', align: 'center' },
    { title: '厅管YY号', dataIndex: 'yy', width: 120, fixed: 'left', align: 'center' },
    { title: '真实姓名', dataIndex: 'rname', width: 120, fixed: 'left', align: 'center' },
    { title: '身份证号', dataIndex: 'cardno', width: 180, fixed: 'left', align: 'center' },
    { title: '签约公司', dataIndex: 'corpname', width: 180, fixed: 'left', align: 'center' },
    { title: '顶级频道', dataIndex: 'sid', width: 100, align: 'center' },
    { title: '电子签约频道', dataIndex: 'asid', width: 120, align: 'center' },
    { title: '合同ID', dataIndex: 'contractid2', align: 'center', width: 180 },
    { title: '刷脸认证', dataIndex: 'faceimg', align: 'center', width: 120, render: text => <PopImage2 width={60} height={120} marginLeft={1} value={text} /> },
    { title: '身份证正面',
      width: 100,
      align: 'center',
      dataIndex: 'front',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.front, '身份证正面')}>查看</a>
        </span>
      )
    },
    { title: '身份证反面',
      width: 100,
      align: 'center',
      dataIndex: 'back',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.back, '身份证反面')}>查看</a>
        </span>
      )
    },
    { title: '手持身份证正面',
      width: 100,
      align: 'center',
      dataIndex: 'holdfront',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.holdfront, '手持身份证正面')}>查看</a>
        </span>
      )
    },
    { title: '手持身份证反面',
      width: 100,
      align: 'center',
      dataIndex: 'holdback',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.holdback, '手持身份证反面')}>查看</a>
        </span>
      )
    },
    { title: '微信实名认证截图',
      width: 100,
      align: 'center',
      dataIndex: 'wechatscreenshot',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.wechatscreenshot, '微信实名认证截图')}>查看</a>
        </span>
      )
    },
    { title: '支付宝实名认证截图',
      width: 100,
      align: 'center',
      dataIndex: 'alipayscreenshot',
      render: (text, record) => (
        <span>
          <a onClick={this.showIdCardModal(record.alipayscreenshot, '支付宝实名认证截图')}>查看</a>
        </span>
      )
    },
    { title: '微信实名认证录屏',
      width: 100,
      dataIndex: 'wechatrecordscreen',
      align: 'center',
      render: (text, record) => (
        <div>
          {record.wechatrecordscreen ? <div><a href={record.wechatrecordscreen}>查看</a></div> : '' }
        </div>
      )
    },
    { title: '支付宝实名认证录屏',
      width: 100,
      dataIndex: 'alipayrecordscreen',
      align: 'center',
      render: (text, record) => (
        <div>
          {record.alipayrecordscreen ? <div><a href={record.alipayrecordscreen}>查看</a></div> : '' }
        </div>
      )
    },
    { title: '查看合同1',
      width: 100,
      dataIndex: 'pdf1',
      align: 'center',
      render: (text, record) => (
        <div>
          <div><a href={'/esign_boss/op_esign_contract_url?type=1&id=' + record.id}>查看</a></div>
        </div>
      )
    },
    { title: '查看合同2',
      width: 100,
      dataIndex: 'pdf2',
      align: 'center',
      render: (text, record) => (
        <div>
          <div><a href={'/esign_boss/op_esign_contract_url?type=2&id=' + record.id}>查看</a></div>
        </div>
      )
    },
    { title: '查看承诺保证书',
      width: 100,
      dataIndex: 'guaranteepdf',
      align: 'center',
      render: (text, record) => (
        <div>
          <div><a href={'/esign_boss/op_esign_contract_url?type=3&id=' + record.id}>查看</a></div>
        </div>
      )
    },
    { title: '查看存证页1',
      width: 100,
      dataIndex: 'attachment1',
      align: 'center',
      render: (text, record) => (
        <div>
          <div><a href={'/esign_boss/op_esign_contract_url?type=4&id=' + record.id}>查看</a></div>
        </div>
      )
    },
    { title: '查看存证页2',
      width: 100,
      dataIndex: 'attachment2',
      align: 'center',
      render: (text, record) => (
        <div>
          <div><a href={'/esign_boss/op_esign_contract_url?type=5&id=' + record.id}>查看</a></div>
        </div>
      )
    },
    { title: '查看承诺保证书存证页',
      width: 100,
      dataIndex: 'gattachment',
      align: 'center',
      render: (text, record) => (
        <div>
          <div><a href={'/esign_boss/op_esign_contract_url?type=6&id=' + record.id}>查看</a></div>
        </div>
      )
    },
    { title: '签约视频',
      width: 100,
      dataIndex: 'videofile',
      align: 'center',
      render: (text, record) => (
        <div>
          {record.videofile ? <div><a href={record.videofile}>查看</a></div> : '' }
        </div>
      )
    },
    { title: '厅管资料',
      width: 100,
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showProfileModal(record)}>查看</a>
        </span>
      )
    },
    { title: '操作人', dataIndex: 'operator', width: 140, align: 'center' },
    { title: '终止理由', dataIndex: 'creason', width: 160, align: 'center' }
  ]

  defaultPageValue = { pageSizeOptions: ['50', '60', '100', '1000'], showSizeChanger: true, pageSize: 50, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` } // 分页设置，可以不改

  // 需要修改
  state = {
    profile: {},
    visible: false,
    active: '1',
    tabid: '1',
    value: {},
    addVisible: false,
    profileVisible: false,
    idCardVisible: false,
    fillRefuseVisible: false,
    refuseRecId: 0,
    refuseExecutor: '',
    searchOpAudit: 0,
    searchOpAsid: 0,
    searchOpText: '',
    searchFnAudit: 0,
    searchHistoryAudit: -1,
    searchFnAsid: 0,
    searchFnText: '',
    searchArchAsid: 0,
    searchHistoryAsid: 0,
    searchCancelAsid: 0,
    searchCancelText: '',
    searchArchText: '',
    searchHistoryText: '',
    searchOpBusinessType: -1,
    searchFnBusinessType: -1,
    searchArchBusinessType: -1,
    searchCancelBusinessType: -1,
    searchHistoryBusinessType: -1
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = { tabid: this.state.tabid, type: 3 }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // 显示用户资料弹窗
  showProfileModal = (record) => () => {
    this.setState({ profile: record, profileVisible: true })
  }

  // 关闭用户资料弹窗
  hideProfileModal = () => {
    this.setState({ profileVisible: false })
  }

  showIdCardModal = (fileName, title) => () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getFileUrl`,
      payload: { file_name: fileName }
    })

    this.setState({ idCardVisible: true, title: title })
  }

  hideIdCardModal = () => {
    this.setState({ idCardVisible: false })
  }

  showRefuseModal = (record) => () => {
    Modal.warning({
      title: '拒绝理由',
      content: record.refuse
    })
  }

  saveRefuseFormRef = (formRef) => {
    this.refuseFormRef = formRef
  }

  saveProfileFormRef = (formRef) => {
    this.profFormRef = formRef
  }

  saveIdCardFormRef = (formRef) => {
    this.idCardRef = formRef
  }

  saveCancelContractFormRef = (formRef) => {
    this.cancelContractRef = formRef
  }

  showFillRefuse = (refuseRecId, refuseExecutor) => () => {
    this.setState({ refuseRecId: refuseRecId, refuseExecutor: refuseExecutor })
    this.setState({ fillRefuseVisible: true })
  }

  showFillCancelContract = (record) => () => {
    this.setState({ recId: record.id })
    this.setState({ fillCancelContractVisible: true })
  }

  hideFillRefuse = () => {
    this.setState({ fillRefuseVisible: false })
  }

  hideFillCancelContract = () => {
    this.setState({ fillCancelContractVisible: false })
  }

  onSubmitRefuseReasonFinish = values => {
    const { refuseRecId, refuseExecutor } = this.state
    this.submitRefuseFormRef.resetFields()
    this.handleAudit(refuseRecId, 2, refuseExecutor, values.refuse, values.resetStage)
  }

  onSubmitCancelContractFinish = values => {
    const { recId } = this.state
    const { dispatch } = this.props
    var getListParam = this.buildSearchParam()
    dispatch({
      type: `${namespace}/cancelContract`,
      payload: { tabid: getListParam.tabid, id: recId, reason: values.reason },
      getListParam: getListParam
    })
    this.cancelContractRef.resetFields()
  }

  onSubmitRefuseFormRef = form => {
    this.submitRefuseFormRef = form
  }

  onSubmitCancelContractFormRef = form => {
    this.cancelContractRef = form
  }

  submitRefuseReason = () => {
    this.submitRefuseFormRef.submit()
    this.setState({ fillRefuseVisible: false })
  }

  submitCancelContractReason = () => {
    this.cancelContractRef.submit()
    this.setState({ fillCancelContractVisible: false })
  }

  preHandleAudit = (id, op, executor) => () => {
    this.handleAudit(id, op, executor, '')
  }

  handleAudit = (id, op, executor, reason, resetStage) => {
    // op:1 接受 op:2 拒绝
    console.log('audit:', id, op, executor, reason)
    var getListParam = this.buildSearchParam()
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/auditItem`,
      payload: { id: id, op: op, executor: executor, reason: reason, resetStage: resetStage },
      getListParam: getListParam
    })
  }

  onSearch = () => {
    this.handleSearch()
  }

  exportUri = () => {
    var payload = this.buildSearchParam()
    var params = Object.keys(payload).map(function (key) {
      return encodeURIComponent(key) + '=' + encodeURIComponent(payload[key])
    }).join('&')
    return '/esign_boss/op_esign_export?' + params
  }

  onChangeTab = tabid => {
    // this.setState 是在 render 时, state 才会改变调用的,
    // setState 是异步的. 组件在还没有渲染之前, this.setState 还没有被调用.这么做的目的是为了提升性能
    // setState 函数接受两个参数，一个是一个对象，就是设置的状态，还有一个是一个回调函数，就是设置状态成功之后执行的
    this.setState({ tabid: tabid }, function () {
      console.log('onChangeTab:', tabid, this.state.tabid)
      this.handleSearch()
    })
  }

  buildSearchParam = () => {
    var audit = 0
    var asid = 0
    var text = ''
    var businessType = -1
    switch (this.state.tabid) {
      case '1':
        audit = this.state.searchOpAudit
        asid = this.state.searchOpAsid
        text = this.state.searchOpText
        businessType = this.state.searchOpBusinessType
        break
      case '2':
        audit = this.state.searchFnAudit
        asid = this.state.searchFnAsid
        text = this.state.searchFnText
        businessType = this.state.searchFnBusinessType
        break
      case '3':
        asid = this.state.searchArchAsid
        text = this.state.searchArchText
        businessType = this.state.searchArchBusinessType
        break
      case '4':
        asid = this.state.searchCancelAsid
        text = this.state.searchCancelText
        businessType = this.state.searchCancelBusinessType
        break
      case '5':
        asid = this.state.searchHistoryAsid
        text = this.state.searchHistoryText
        audit = this.state.searchHistoryAudit
        businessType = this.state.searchHistoryBusinessType
        break
    }

    return { tabid: this.state.tabid, audit: audit, asid: asid, text: text, businessType: businessType, type: 3 }
  }

  // 查询，不需要修改
  handleSearch = () => {
    const payload = this.buildSearchParam()
    console.log(payload)
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`,
      payload: payload
    })
  }

  onExport = () => {
    let headers = []
    let columns = this.ColumnsArchived
    const { model: { list } } = this.props
    columns.forEach(function (item) {
      headers.push({ k: item.dataIndex, v: item.title })
    })

    for (var i = 0; i < list.length; i++) {
      list[i].subtime = dateString(list[i].subtime)
      switch (list[i].htype) {
        case 0:
          list[i].htype = '音频'
          break
        case 1:
          list[i].htype = '视频'
          break
      }
    }
    exportExcel(headers, list)
  }

  // 实际的页面信息
  render () {
    const { route, model: { list, idCard } } = this.props // 基本不需要修改
    const { profile } = this.state
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='1' onChange={this.onChangeTab}>
            <TabPane tab='待运营审核' key='1'>
              <Form>
                审核状态
                <Divider type='vertical' />
                <Select labelInValue defaultValue={{ key: 0 }} style={{ width: 120 }} onChange={value => this.setState({ searchOpAudit: value.key })}>
                  <Option value={-1}>全部</Option>
                  <Option value={0}>未审批</Option>
                  <Option value={1}>已审批</Option>
                </Select>
                <Divider type='vertical' /> {/* 分割线 */}
                业务
                <Divider type='vertical' /> {/* 分割线 */}
                <Select labelInValue defaultValue={{ key: -1 }} style={{ width: 120 }} onChange={value => this.setState({ searchOpBusinessType: value.key })}>
                  <Option value={-1}>全部</Option>
                  <Option value={0}>交友</Option>
                  <Option value={1}>约战</Option>
                  <Option value={2}>宝贝</Option>
                </Select>
                <Divider type='vertical' /> {/* 分割线 */}
                <Input placeholder='搜索短位频道' onChange={e => this.setState({ searchOpAsid: isNaN(parseInt(e.target.value)) ? 0 : parseInt(e.target.value) })} style={{ width: 150 }} />
                <Input placeholder='搜索内容' onChange={e => this.setState({ searchOpText: e.target.value })} style={{ width: 150 }} />
                <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onSearch}>搜索</Button>
                <Divider type='vertical' /> {/* 分割线 */}
                <Button style={{ marginLeft: 5 }} type='primary' href={this.exportUri()}>导出</Button>
                <Divider /> {/* 分割线 */}
                <Table rowKey={(record, index) => index} dataSource={list} bordered columns={this.ColumnOptorAudit} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} /> {/* 显示的列表 */}
              </Form>
            </TabPane>
            <TabPane tab='待财务审核' key='2'>
              <Form>
                审核状态
                <Divider type='vertical' />
                <Select labelInValue defaultValue={{ key: 0 }} style={{ width: 120 }} onChange={value => this.setState({ searchFnAudit: value.key })}>
                  <Option value={-1}>全部</Option>
                  <Option value={0}>未审批</Option>
                  <Option value={1}>已审批</Option>
                </Select>
                <Divider type='vertical' /> {/* 分割线 */}
                业务
                <Divider type='vertical' /> {/* 分割线 */}
                <Select labelInValue defaultValue={{ key: -1 }} style={{ width: 120 }} onChange={value => this.setState({ searchFnBusinessType: value.key })}>
                  <Option value={-1}>全部</Option>
                  <Option value={0}>交友</Option>
                  <Option value={1}>约战</Option>
                  <Option value={2}>宝贝</Option>
                </Select>
                <Divider type='vertical' /> {/* 分割线 */}
                <Input placeholder='搜索短位频道' onChange={e => this.setState({ searchFnAsid: isNaN(parseInt(e.target.value)) ? 0 : parseInt(e.target.value) })} style={{ width: 150 }} />
                <Input placeholder='搜索内容' onChange={e => this.setState({ searchFnText: e.target.value })} style={{ width: 150 }} />
                <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onSearch}>搜索</Button>
                <Divider type='vertical' /> {/* 分割线 */}
                <Button style={{ marginLeft: 5 }} type='primary' href={this.exportUri()}>导出</Button>
                <Divider /> {/* 分割线 */}
                <Table rowKey={(record, index) => index} dataSource={list} bordered columns={this.ColumnFinanceAudit} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} /> {/* 显示的列表 */}
              </Form>
            </TabPane>
            <TabPane tab='已归档' key='3'>
              <Form>
                业务
                <Divider type='vertical' /> {/* 分割线 */}
                <Select labelInValue defaultValue={{ key: -1 }} style={{ width: 120 }} onChange={value => this.setState({ searchArchBusinessType: value.key })}>
                  <Option value={-1}>全部</Option>
                  <Option value={0}>交友</Option>
                  <Option value={1}>约战</Option>
                  <Option value={2}>宝贝</Option>
                </Select>
                <Divider type='vertical' /> {/* 分割线 */}
                <Input placeholder='搜索短位频道' onChange={e => this.setState({ searchArchAsid: isNaN(parseInt(e.target.value)) ? 0 : parseInt(e.target.value) })} style={{ width: 150 }} />
                <Input placeholder='搜索内容' onChange={e => this.setState({ searchArchText: e.target.value })} style={{ width: 150 }} />
                <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onSearch}>搜索</Button>
                <Divider type='vertical' /> {/* 分割线 */}
                <Button style={{ marginLeft: 5 }} type='primary' href={this.exportUri()}>导出</Button>
                <Divider /> {/* 分割线 */}
                <Table rowKey={(record, index) => index} dataSource={list} bordered columns={this.ColumnsArchived} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} /> {/* 显示的列表 */}
              </Form>
            </TabPane>
            <TabPane tab='已失效' key='4'>
              <Form>
                业务
                <Divider type='vertical' /> {/* 分割线 */}
                <Select labelInValue defaultValue={{ key: -1 }} style={{ width: 120 }} onChange={value => this.setState({ searchCancelBusinessType: value.key })}>
                  <Option value={-1}>全部</Option>
                  <Option value={0}>交友</Option>
                  <Option value={1}>约战</Option>
                  <Option value={2}>宝贝</Option>
                </Select>
                <Divider type='vertical' /> {/* 分割线 */}
                <Input placeholder='搜索短位频道' onChange={e => this.setState({ searchCancelAsid: isNaN(parseInt(e.target.value)) ? 0 : parseInt(e.target.value) })} style={{ width: 150 }} />
                <Input placeholder='搜索内容' onChange={e => this.setState({ searchCancelText: e.target.value })} style={{ width: 150 }} />
                <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onSearch}>搜索</Button>
                <Divider type='vertical' /> {/* 分割线 */}
                <Button style={{ marginLeft: 5 }} type='primary' href={this.exportUri()}>导出</Button>
                <Divider /> {/* 分割线 */}
                <Table rowKey={(record, index) => index} dataSource={list} bordered columns={this.ColumnsContractCancel} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} /> {/* 显示的列表 */}
              </Form>
            </TabPane>
          </Tabs>
        </Card>
        <RefuseModal wrappedComponentRef={this.saveRefuseFormRef} onRef={this.onSubmitRefuseFormRef} onFinish={this.onSubmitRefuseReasonFinish} {...this.state} onCancel={this.hideFillRefuse} onOk={this.submitRefuseReason} />
        <ProfileModal wrappedComponentRef={this.saveProfileFormRef} {...this.state} profile={profile} onCancel={this.hideProfileModal} onOk={this.hideProfileModal} />
        <IdCardModal wrappedComponentRef={this.saveIdCardFormRef} {...this.state} idCard={idCard} onCancel={this.hideIdCardModal} onOk={this.hideIdCardModal} />
        <CancelContractModal wrappedComponentRef={this.saveCancelContractFormRef} onRef={this.onSubmitCancelContractFormRef} onFinish={this.onSubmitCancelContractFinish} {...this.state} onCancel={this.hideFillCancelContract} onOk={this.submitCancelContractReason} />
      </PageHeaderWrapper>
    )
  }
}

// 拒绝理由
class CancelContractModal extends Component {
  render () {
    const { fillCancelContractVisible, onOk, onCancel, onRef, onFinish } = this.props
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 12 }
      }
    }
    return (
      <Modal visible={fillCancelContractVisible} title='提示' cancelText='取消' okText='确认' onOk={onOk} onCancel={onCancel} >
        <Form onFinish={onFinish} ref={onRef} {...formItemLayout}>
          <FormItem label='终止原因' name='reason' rules={[{ required: true }]}>
            <Input />
          </FormItem>
        </Form>
      </Modal>
    )
  }
}

// 拒绝理由
class RefuseModal extends Component {
  render () {
    const { fillRefuseVisible, onOk, onCancel, onRef, onFinish } = this.props
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 12 }
      }
    }
    return (
      <Modal visible={fillRefuseVisible} title='提示' cancelText='取消' okText='确认' onOk={onOk} onCancel={onCancel} >
        <Form onFinish={onFinish} ref={onRef} {...formItemLayout}>
          <FormItem label='拒绝理由' name='refuse' rules={[{ required: true }]}>
            <Input />
          </FormItem>
          <FormItem label='重新签署阶段' name='resetStage' rules={[{ required: true }]}>
            <Select>
              <Option value={1}>重新签署合同</Option>
              <Option value={9}>重新完善资料</Option>
              <Option value={0}>重新实名</Option>
            </Select>
          </FormItem>
          <FormItem>{'注：拒绝理由将直接发送给厅管'}</FormItem>
        </Form>
      </Modal>
    )
  }
}

class ProfileModal extends Component {
  render () {
    const { profile, profileVisible, onOk, onCancel } = this.props
    return (
      <Modal visible={profileVisible} title='查看厅管资料' cancelText='关闭' okText='知道了' onOk={onOk} onCancel={onCancel} >
        <p>短位频道: {profile.asid}</p>
        <p>签约频道: {profile.sid}</p>
        <p>YY号: {profile.yy}</p>
        <p>UID: {profile.uid}</p>
        <p>签约时间: {moment.unix(profile.signtm1).format('YYYY-MM-DD HH:mm')}</p>
        <p><font color='red'>真实姓名: </font>{profile.rname}</p>
        <p><font color='red'>身份证号码: </font>{profile.cardno}</p>
        <p><font color='red'>联系地址: </font>{profile.addr}</p>
        <p><font color='red'>身份证地址: </font>{profile.idaddr}</p>
        <p><font color='red'>联系电话: </font>{profile.phone}</p>
        <p><font color='red'>电子邮箱: </font>{profile.email}</p>
        <p><font color='red'>微信号: </font>{profile.wechat}</p>
        <p><font color='red'>支付宝账号: </font>{profile.alipay}</p>
        <p><font color='red'>昵称（艺名）: </font>{profile.artnick}</p>
      </Modal>
    )
  }
}

class IdCardModal extends Component {
  render () {
    const { idCard, title, idCardVisible, onOk, onCancel } = this.props
    return (
      <Modal visible={idCardVisible} title={title} cancelText='关闭' okText='知道了' onOk={onOk} onCancel={onCancel} >
        <Card
          hoverable
          cover={<img src={idCard.url} />}
        />
      </Modal>
    )
  }
}

export default ESignAuditing
