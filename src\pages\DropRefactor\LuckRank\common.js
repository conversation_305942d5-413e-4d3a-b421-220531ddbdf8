import moment from 'moment'
import React from 'react'
import { Typography } from 'antd'
const { Text, Paragraph } = Typography

export const actTypeOptions = [
  { label: '周活动', value: 7, key: 7 },
  { label: '双周活动', value: 14, key: 15 },
  { label: '月活动', value: 30, key: 30 }
]

export const propsTypeOptions = [
  { label: '礼物', value: 0, key: 0 },
  { label: '货币', value: 1, key: 1 },
  { label: '入场秀', value: 2, key: 2 },
  { label: '勋章', value: 3, key: 3 }
]

// 默认新增礼物
export const defaultPrizeItem = { propsType: 0, prizeId: 20250, name: '爱心', price: 100, url: 'https://turnover-yy.oss-cn-shanghai.aliyuncs.com/friend/friend_icon/staticIcon/20250.png', desc: '爱心×10', totalPrice: 10, count: 1 }

// 可选货币选项 (value=营收ID)
export const currencyOptions = [
  { label: '紫水晶券', value: 1001, key: 1001, raw: { id: 1001, name: '紫水晶券', price: 1, url: 'https://res.yy.com/fts/client/prop/amethyst_ticket.png' } },
  { label: 'Y币', value: 4, key: 4, raw: { id: 4, name: 'Y币', price: 1000, url: 'https://res.yy.com/fts/client/prop/4.png' } },
  { label: '队友金', value: 54, key: 54, raw: { id: 54, name: '队友金', price: 1, url: 'https://res.yy.com/fts/client/prop/54.png' } }
]

// 入场秀选项
export const enterShowOptions = [
  { label: '幸运飞船入场秀', value: 130, key: 130, raw: { id: 130, name: '幸运飞船入场秀', price: 0, url: 'https://res.yy.com/fts/client/privilege/enter/show_130_v1.png' } }
]

// 勋章
export const medalOptions = [
  { label: '空投LV1勋章', value: 1151, key: 1151, raw: { id: 1151, name: '空投LV1勋章', price: 0, url: 'https://res.yy.com/fts/client/privilege/medal/1151_v1.png' } },
  { label: '空投LV2勋章', value: 1152, key: 1152, raw: { id: 1152, name: '空投LV2勋章', price: 0, url: 'https://res.yy.com/fts/client/privilege/medal/1152_v1.png' } },
  { label: '空投LV3勋章', value: 1153, key: 1153, raw: { id: 1153, name: '空投LV3勋章', price: 0, url: 'https://res.yy.com/fts/client/privilege/medal/1153_v1.png' } }
]

// 默认活动
export const defaultItem = {
  actType: 7,
  timeRange: [null, null],
  autoWeek: 0,
  rewards: [
    { propsType: 0, prizeId: 20250, name: '爱心', price: 100, url: 'https://turnover-yy.oss-cn-shanghai.aliyuncs.com/friend/friend_icon/staticIcon/20250.png', count: 10, desc: '爱心×10' },
    { propsType: 0, prizeId: 20250, name: '爱心', price: 100, url: 'https://turnover-yy.oss-cn-shanghai.aliyuncs.com/friend/friend_icon/staticIcon/20250.png', count: 10, desc: '爱心×10' },
    { propsType: 0, prizeId: 20250, name: '爱心', price: 100, url: 'https://turnover-yy.oss-cn-shanghai.aliyuncs.com/friend/friend_icon/staticIcon/20250.png', count: 10, desc: '爱心×10' }
  ]
}

// 时间范围格式化
export const timeRangeFormater = (r, autoWeek) => {
  const { startTime, endTime } = r
  return <Paragraph>
    {autoWeek > 0 ? <Text> 共 {autoWeek} 周 <br /></Text> : ''}
    <Text>{moment(startTime * 1000).format('YYYY-MM-DD HH:mm:ss')} <br /></Text>
    <Text>~ <br /></Text>
    <Text>{moment(endTime * 1000).format('YYYY-MM-DD HH:mm:ss')}</Text>
  </Paragraph>
}

// 奖励描述格式化
export const rewardFormater = (r) => {
  if (Array.isArray(r.firstRewards) && r.firstRewards.length > 0) {
    let total1 = 0
    let total2 = 0
    let total3 = 0
    return (
      <Paragraph>

        <Text>第1名：
          {r.firstRewards.map((item, index) => {
            total1 += item.price * item.count
            return <span>{item.name}×{item.count} </span>
          })}
          共{(total1 / 1000).toLocaleString()}元</Text> <br />

        <Text>第2名：
          {r.secondRewards.map((item, index) => {
            total2 += item.price * item.count
            return <span>{item.name}×{item.count} </span>
          })}
          共{(total2 / 1000).toLocaleString()}元</Text> <br />

        <Text>第3名：
          {r.thirdRewards.map((item, index) => {
            total3 += item.price * item.count
            return <span>{item.name}×{item.count} </span>
          })}
          共{(total3 / 1000).toLocaleString()}元</Text> <br />
        <Text>合计{((total1 + total2 + total3) / 1000).toLocaleString()}元</Text>
      </Paragraph>
    )
  }
  let rewards = r.rewards
  if (!rewards) return '???'
  let total = 0
  return (
    <Paragraph>
      {rewards.map((item, index) => {
        total += item.price * item.count
        return <Text>第{index + 1}名, {item.name} × {item.count}, 共{(item.price * item.count / 1000).toLocaleString()}元<br /></Text>
      })}
      <Text>合计{(total / 1000).toLocaleString()}元</Text>
    </Paragraph>
  )
}

// 配置状态格式化
export const statusFormater = (record) => {
  const { startTime, endTime, aprInfo: { status } } = record
  if (status !== 'Passed') {
    return '-'
  }
  const now = moment().unix()
  if (now < startTime) {
    return '未开始'
  }
  if (now > endTime) {
    return '已结束'
  }
  return <Text type='success'>生效中</Text>
}

// 审批状态格式化
export const approvalFormater = (record) => {
  const { opInfo: { opDesc }, aprInfo: { desc } } = record
  return (
    <Paragraph>
      <Text>{opDesc}<br /></Text>
      <Text>{desc}</Text>
    </Paragraph>
  )
}

// 第一名默认礼物
export const defaultFirstConfigList = [
  {
    idx: 1,
    ranking: 1,
    propsType: 1,
    prizeId: 1001,
    name: '紫水晶券',
    price: 1,
    url: 'https://res.yy.com/fts/client/prop/amethyst_ticket.png',
    desc: '紫水晶券×3888000',
    totalPrice: 10,
    count: 3888000
  },
  {
    idx: 2,
    ranking: 1,
    propsType: 2,
    prizeId: 130,
    name: '幸运飞船入场秀',
    price: 0,
    url: 'https://res.yy.com/fts/client/privilege/enter/show_130_v1.png',
    desc: '幸运飞船入场秀×7',
    totalPrice: 10,
    count: 7
  },
  {
    idx: 3,
    ranking: 1,
    propsType: 3,
    prizeId: 1153,
    name: '空投LV3勋章',
    price: 0,
    url: 'https://res.yy.com/fts/client/privilege/medal/1153_v1.png',
    desc: '空投LV3勋章×7',
    totalPrice: 10,
    count: 7
  }
]

export const defaultSecondConfigList = [
  {
    idx: 1,
    ranking: 2,
    propsType: 1,
    prizeId: 1001,
    name: '紫水晶券',
    price: 1,
    url: 'https://res.yy.com/fts/client/prop/amethyst_ticket.png',
    desc: '紫水晶券×1888000',
    totalPrice: 10,
    count: 1888000
  },
  {
    idx: 2,
    ranking: 2,
    propsType: 2,
    prizeId: 130,
    name: '幸运飞船入场秀',
    price: 0,
    url: 'https://res.yy.com/fts/client/privilege/enter/show_130_v1.png',
    desc: '幸运飞船入场秀×3',
    totalPrice: 10,
    count: 3
  },
  {
    idx: 3,
    ranking: 2,
    propsType: 3,
    prizeId: 1152,
    name: '空投LV2勋章',
    price: 0,
    url: 'https://res.yy.com/fts/client/privilege/medal/1152_v1.png',
    desc: '空投LV2勋章×7',
    totalPrice: 10,
    count: 7
  }
]

export const defaultThridConfigList = [
  {
    idx: 1,
    ranking: 3,
    propsType: 1,
    prizeId: 1001,
    name: '紫水晶券',
    price: 1,
    url: 'https://res.yy.com/fts/client/prop/amethyst_ticket.png',
    desc: '紫水晶券×588000',
    totalPrice: 10,
    count: 588000
  },
  {
    idx: 2,
    ranking: 3,
    propsType: 3,
    prizeId: 1151,
    name: '空投LV1勋章',
    price: 0,
    url: 'https://res.yy.com/fts/client/privilege/medal/1151_v1.png',
    desc: '空投LV1勋章×7',
    totalPrice: 10,
    count: 7
  }
]
