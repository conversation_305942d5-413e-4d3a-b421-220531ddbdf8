import {
  listAwardConfirm,
  listAwardDetail,
  approvalReward,
  getRewardSummary,
  getTaskProgress,
  flushAwardConfirm
} from './api'
import { message } from 'antd'

export default {
  namespace: 'guildTaskBabyRevision',

  state: {
    rewardDetailList: [],
    taskProgressList: [],
    awardConfirmList: [],
    awardConfirmReviewList: [],
    awardConfirmReviewScaleList: [],
    awardConfirmInfo: {},
    awardDetailList: [],
    reviewInfo: { preSettlement: 0, preTurnoverSettlement: 0, adjustMaxReward: 0, adjustMinReward: 0, adjustTurnoverMaxReward: 0, adjustTurnoverMinReward: 0 },
    isCheckOk: false
  },

  reducers: {

    updateSummaryList (state, { payload }) {
      console.log('updateSummaryList', payload)
      return {
        ...state,
        rewardDetailList: payload
      }
    },

    updateProgressList (state, { payload }) {
      console.log('updateProgressList', payload)
      return {
        ...state,
        taskProgressList: payload
      }
    },

    updateAwardConfirm (state, { payload, awardConfirmInfo }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      let bak = JSON.parse(JSON.stringify(payload))
      let reviewList = []
      for (let i = 0; i < bak.length; i++) {
        let one = {
          idx: i + 1,
          sid: bak[i].sid,
          asid: bak[i].asid,
          month: bak[i].month,
          turnover: bak[i].turnover,
          turnoverSettlement: bak[i].turnoverSettlement,
          settlement: bak[i].settlement,
          turnoverNextStep: bak[i].turnoverNextStep,
          turnoverNextReward: bak[i].turnoverNextReward,
          turnoverMaxReward: bak[i].turnoverMaxReward,
          lastMonthTurnover: bak[i].lastMonthTurnover,
          curMonthReferenceTurnover: bak[i].curMonthReferenceTurnover,
          isReachReferenceTurnover: bak[i].isReachReferenceTurnover,

          turnoverPrizeRule: 0,
          turnoverAdjustReward: 0,
          turnoverRewardDiff: '',

          adjustReward: ''
        }
        reviewList.push(one)
      }
      return {
        ...state,
        awardConfirmList: payload,
        awardConfirmReviewList: reviewList,
        awardConfirmInfo: awardConfirmInfo
      }
    },

    updateAwardDetail (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        awardDetailList: payload
      }
    },

    updateReviewInfo (state, { payload, isCheckOk }) {
      if (payload === null) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        reviewInfo: payload,
        isCheckOk: isCheckOk
      }
    },

    cleanReviewInfo (state, { payload }) {
      let data = { preSettlement: 0, preTurnoverSettlement: 0, adjustMaxReward: 0, adjustMinReward: 0, adjustTurnoverMaxReward: 0, adjustTurnoverMinReward: 0 }

      return {
        ...state,
        reviewInfo: data,
        isCheckOk: false
      }
    }

  },

  effects: {

    * getSummaryList ({ payload }, { call, put }) {
      try {
        let { data: { data, status, msg } } = yield call(getRewardSummary, payload)
        if (status !== 0) {
          message.error('错误' + msg)
          return
        }
        data = Array.isArray(data) ? data : []
        yield put({
          type: 'updateSummaryList',
          payload: data
        })
      } catch (e) {
        message.error('exception', e)
      }
    },

    * getTaskProgress ({ payload }, { call, put }) {
      try {
        let { data: { data, status, msg } } = yield call(getTaskProgress, payload)
        if (status !== 0) {
          message.error('错误' + msg)
          return
        }
        data = Array.isArray(data) ? data : []
        yield put({
          type: 'updateProgressList',
          payload: data
        })
      } catch (e) {
        message.error('exception', e)
      }
    },

    * listAwardConfirm ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(listAwardConfirm, payload)
      console.log(status, msg)
      let list = data !== null ? Array.isArray(data.list) ? data.list : [] : []
      for (let i = 0; i < list.length; i++) {
        list[i].idx = i + 1
      }
      let awardConfirmInfo = { year: data.year, month: data.month, dateRange: data.dateRange, guildCount: data.guildCount, reviewRemark: data.reviewRemark, reachCount: data.reachCount }
      yield put({
        type: 'updateAwardConfirm',
        payload: list,
        awardConfirmInfo: awardConfirmInfo
      })
    },

    * flushAwardConfirm ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(flushAwardConfirm, payload)
      console.log(status, msg)
      let info = {
        preSettlement: 0,
        preTurnoverSettlement: 0,
        adjustMaxReward: 0,
        adjustMinReward: 0,
        adjustTurnoverMaxReward: 0,
        adjustTurnoverMinReward: 0
      }
      if (data !== null) {
        info.preSettlement = data.preSettlement
        info.preTurnoverSettlement = data.preTurnoverSettlement
        info.adjustMaxReward = data.adjustMaxReward
        info.adjustMinReward = data.adjustMinReward
        info.adjustTurnoverMaxReward = data.adjustTurnoverMaxReward
        info.adjustTurnoverMinReward = data.adjustTurnoverMinReward
      }
      let isCheckOk = false
      if (status === 0) {
        isCheckOk = true
      } else {
        message.error({ content: msg })
      }
      yield put({
        type: 'updateReviewInfo',
        payload: info,
        isCheckOk: isCheckOk
      })
    },

    * listAwardDetail ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(listAwardDetail, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      for (let i = 0; i < data.length; i++) {
        data[i].idx = i + 1
      }
      yield put({
        type: 'updateAwardDetail',
        payload: data
      })
    },

    * approvalReward ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(approvalReward, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        message.success('ok')
        yield put({
          type: 'listAwardConfirm',
          payload: {}
        })
      }
    }

  }
}
