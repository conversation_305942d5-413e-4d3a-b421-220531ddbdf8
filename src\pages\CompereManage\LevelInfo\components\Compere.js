import React, { Component } from 'react'
import { connect } from 'dva'
import { Table, Input, Form, InputNumber, Modal, Button, Card, message } from 'antd'
import { timeFormater } from '@/utils/common'
import HistoryRecord from './HistoryRecord'
import './style.css'

const { Search } = Input

const namespace = 'levelInfo'

@connect(({ levelInfo }) => ({
  model: levelInfo
}))

class Compere extends Component {
  constructor (props) {
    super(props)

    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/updateInfo`,
      payload: []
    })
  }

  columns = [
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '主持等级', dataIndex: 'level' },
    { title: '当前积分', dataIndex: 'currentScore', align: 'center' },
    { title: '升级需要总积分', dataIndex: 'totalScore', align: 'center' },
    { title: '当前成长值', dataIndex: 'currentGrowth', align: 'center' },
    { title: '升级需要总的成长值', dataIndex: 'totalGrowth', align: 'center' },
    { title: '累积总积分', dataIndex: 'sumTotalScore', align: 'center' },
    { title: '累积总成长值', dataIndex: 'sumTotalGrowth', align: 'center' },
    { title: '操作人', dataIndex: 'optUid', render: (text, record) => (record.optUid && record.optUid > 0 ? record.optUid : '-') },
    { title: '操作时间', dataIndex: 'optTime', render: (text, record) => (record.optTime && record.optTime > 0 ? timeFormater(record.optTime, 1) : '-') },
    { title: '变更历史', key: 'operation', render: (text, record) => (<span><a onClick={this.showModelHistory(record.uid)}>变更历史</a></span>) },
    { title: '操作', key: 'operation', align: 'center', render: (text, record) => (<span><a onClick={this.showModel(record.uid, record.level, true)}>修改等级</a></span>) }
  ]

  columnsHistory = [
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    {
      title: '变更前',
      className: 'before',
      children: [
        { title: '主持等级', dataIndex: 'level', render: (text, record) => (record.oldInfo.level) },
        { title: '当前积分', dataIndex: 'currentScore', render: (text, record) => (record.oldInfo.currentScore) },
        { title: '升级需总积分', dataIndex: 'totalScore', render: (text, record) => (record.oldInfo.totalScore) },
        { title: '当前成长值', dataIndex: 'currentGrowth', render: (text, record) => (record.oldInfo.currentGrowth) },
        { title: '升级需总的成长值', dataIndex: 'totalGrowth', render: (text, record) => (record.oldInfo.totalGrowth) },
        { title: '累积总积分', dataIndex: 'sumTotalScore', render: (text, record) => (record.oldInfo.sumTotalScore) },
        { title: '累积总成长值', dataIndex: 'sumTotalGrowth', render: (text, record) => (record.oldInfo.sumTotalGrowth) }
      ]
    },
    {
      title: '变更后',
      className: 'after',
      children: [
        { title: '主持等级', dataIndex: 'level', render: (text, record) => (record.newInfo.level) },
        { title: '当前积分', dataIndex: 'currentScore', render: (text, record) => (record.newInfo.currentScore) },
        { title: '升级需总积分', dataIndex: 'totalScore', render: (text, record) => (record.newInfo.totalScore) },
        { title: '当前成长值', dataIndex: 'currentGrowth', render: (text, record) => (record.newInfo.currentGrowth) },
        { title: '升级需总的成长值', dataIndex: 'totalGrowth', render: (text, record) => (record.newInfo.totalGrowth) },
        { title: '累积总积分', dataIndex: 'sumTotalScore', render: (text, record) => (record.newInfo.sumTotalScore) },
        { title: '累积总成长值', dataIndex: 'sumTotalGrowth', render: (text, record) => (record.newInfo.sumTotalGrowth) }
      ]
    },
    { title: '操作人', dataIndex: 'optUid', render: (text, record) => (record.optUid && record.optUid > 0 ? record.optUid : '-') },
    { title: '操作时间', dataIndex: 'optTime', render: (text, record) => (record.optTime && record.optTime > 0 ? timeFormater(record.optTime, 1) : '-') }
  ]

  state = {
    visible: false
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  search = (value) => {
    const { dispatch } = this.props

    if (value === null || value === undefined || value === '') {
      message.warn('请输入uid')
      return
    }

    const data = { uid: Number(value) }
    console.log(data)
    dispatch({
      type: `${namespace}/getCompereLevelInfo`,
      payload: data
    })
  }

  showModelHistory = (uid) => () => {
    this.props.dispatch({
      type: `${namespace}/getCompereLevelHistory`,
      payload: { uid: uid }
    })

    this.setState({ visibleHistory: true })
  }

  handleCancelHistory = e => {
    this.setState({ visibleHistory: false })
  }

  showModel = (uid, level, isUpdate) => () => {
    console.log(uid, level)
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue({ uid: uid, level: level })
    }
    this.setState({ visible: true, isUpdate: isUpdate })
  }

  hiddenModal = () => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.setState({ visible: false, isUpdate: null })
  }

  handleCancel = e => {
    this.hiddenModal()
  }

  handleSubmit = e => {
    if (this.formRef) {
      this.formRef.submit()
    }
  }

  onFinish = values => {
    console.log(values)

    let param = {
      uid: values.uid,
      level: values.level
    }

    this.callModel('setCompereLevel', {
      params: param,
      cbFunc: () => {
        setTimeout(() => { this.search(values.uid) }, 1000)
      }
    })

    this.hiddenModal()
  }

  render () {
    const { visible, isUpdate, visibleHistory } = this.state
    const { model: { list, listHistory, loadingHistory } } = this.props

    return (
      <Card>
        <div>
          <Search placeholder='输入uid' onSearch={value => this.search(value)} style={{ width: 200 }} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.showModel(null, null, false)}>设置主持等级</Button>
        </div>
        <Table style={{ marginTop: 10 }} bordered dataSource={list} columns={this.columns} />

        <Modal width={400} title={'修改主持等级'}
          visible={visible}
          forceRender
          onOk={this.handleSubmit}
          onCancel={this.handleCancel}>
          <Form autoComplete='off' ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <Form.Item name='id' hidden>
              <Input hidden />
            </Form.Item>
            <Form.Item label='UID&nbsp;' name='uid' rules={[{ required: true }]}>
              <InputNumber disabled={isUpdate} style={{ width: 150 }} />
            </Form.Item>
            <Form.Item label='等级' name='level' rules={[{ required: true }]}>
              <InputNumber min={1} max={80} style={{ width: 150 }} />
            </Form.Item>
          </Form>
        </Modal>

        <HistoryRecord visible={visibleHistory} list={listHistory} loading={loadingHistory} columns={this.columnsHistory} cancelHandler={this.handleCancelHistory} />
        {/* <Modal footer={null} forceRender width={1800} visible={visibleHistory} title='变更历史' onCancel={this.handleCancelHistory}>
          <Table loading={loadingHistory} style={{ marginTop: 10 }} size='small' columns={this.columnsHistory} dataSource={listHistory} />
        </Modal> */}
      </Card>
    )
  }
}

export default Compere
