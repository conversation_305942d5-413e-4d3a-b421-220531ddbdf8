import { message } from 'antd'
import { getCurActConfig, delActConfig, addAupdateActConfig } from './api'

export default {
  namespace: 'cowBossConfig',
  state: {
    page: 0,
    size: 0,
    displayList: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },

  effects: {
    * queryList ({ params }, { select, call, put }) {
      let resp = yield call(getCurActConfig)
      const { data: { status, act } } = resp
      if (status !== 0) {
        message.warn('请求数据失败, message=', resp.data.message)
        console.error('queryList get bad response: ', resp)
        return
      }
      const list = []
      list[0] = act
      yield put({
        type: 'updateState',
        payload: { name: 'displayList', newValue: list }
      })
    },
    // 新增或修改配置
    * modifyOrAddConfig ({ payload }, { select, call, put }) {
      const { callback, params } = payload
      let resp = yield call(addAupdateActConfig, params)
      const { data: { status } } = resp
      if (status !== 0) {
        message.error('修改配置失败，message=' + resp.data.message)
        console.error('modifyOrAddConfig() return bad response: params=', params, 'resp=', resp)
      } else {
        message.info('OK')
      }
      callback()
    },
    // 删除配置
    * deleteConfig ({ payload }, { select, call, put }) {
      const { actID, callback } = payload
      let resp = yield call(delActConfig, actID)
      const { data: { status } } = resp
      if (status !== 0) {
        message.error('删除配置失败， message=' + resp.data.message)
        console.error('deleteConfig() return bad response: resp=', resp)
      } else {
        message.info('删除成功')
        callback()
      }
    }

  }
}
