import React, { Component } from 'react'
import { connect } from 'dva'
import { Tabs, Card, Input, Form, Row, Col, Button, Table, Popconfirm, Alert } from 'antd'
import { tableStyle, Inputlayout } from '@/utils/common'
import { DeleteOutlined } from '@ant-design/icons'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'

const namespace = 'jumpChannelWhiteList'

@connect(({ jumpChannelWhiteList }) => ({
  model: jumpChannelWhiteList
}))

class JumpChannelWhiteList extends Component {
  constructor (props) {
    super(props)
    this.refreshVidioComperList()
  }

  // 标签页发生切换
  onTagChange = (record) => {
    if (record === '1') { // 切换到'添加标签页'
      // todo: 清空输入框
    }
    if (record === '2') { // 切换到'频道跳转白名单标签页'
      this.refreshVidioComperList()
    }
  }

  // 获取/刷新频道跳转白名单列表数据
  refreshVidioComperList = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getWhiteListData`,
      payload: null
    })
  }

  // 点击 批量添加标签页-添加按钮
  onAddBtnClick = () => {
    const { sidSsidList } = this.state
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/addWhiteListByList`,
      payload: { sidSsidList: sidSsidList }
    })
  }
  // 点击 批量删除标签页-删除按钮
  onDelBtnClick = () => {
    const { sidSsidList } = this.state
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/delWhiteListByList`,
      payload: { sidSsidList: sidSsidList }
    })
  }
  // '批量添加'标签页html代码
  addBlackLIstHtml = () => {
    const { TextArea } = Input
    const { updating } = this.props.model
    return (
      <div>
        <Row>
          <Col span='24'>
            <Form {...Inputlayout} name='input-sid'>
              <Alert message='批量添加提示：格式为sid:ssid,一行一组' type='info' closable />
              <TextArea rows={6} onChange={e => this.setState({ sidSsidList: e.target.value })} />
              <Form.Item>
                <Button type='primary' htmlType='submit' loading={updating} onClick={() => this.onAddBtnClick()}>
                  添加
                </Button>
              </Form.Item>
            </Form>
          </Col>
        </Row>
      </div>
    )
  }

  // 确认删除选中的白名单
  onComfirmDel = (sid, ssid) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/delWhiteListBySidSsid`,
      payload: { sid: sid, ssid: ssid }
    })
  }

  // 频道跳转白名单-删除操作html代码
  deleteWhiteListHtml = (record) => {
    const { sid, ssid } = record
    let tmpStr = `确定要将 [sid-ssid = ${sid}-${ssid}] 从频道跳转白名单删除吗？`
    return (
      <Popconfirm placement='bottom' title={tmpStr}
        okType='danger' okText='删除' cancelText='取消' onConfirm={() => this.onComfirmDel(sid, ssid)}>
        <a href='#'><DeleteOutlined style={{ color: '#ff3535', fontSize: '1.2em' }} /></a>
      </Popconfirm>
    )
  }

  // ‘白名单展示列表标签页’html代码
  displayWhiteListHtml = () => {
    const columns = [
      { title: '#', dataIndex: 'idx' },
      { title: '短位频道', dataIndex: 'asid' },
      { title: '频道号', dataIndex: 'sid' },
      { title: '子频道号', dataIndex: 'ssid' },
      { title: '操作', render: (record) => this.deleteWhiteListHtml(record) }
    ]
    const { displayData } = this.props.model
    return (
      <Row >
        <Col span={24}>
          <Table columns={columns} dataSource={displayData} size='small' pagination={tableStyle} />
        </Col>
      </Row>
    )
  }

  // ‘批量删除标签页’ html 代码
  bathDeleteHtml = () => {
    const { TextArea } = Input
    const { updating } = this.props.model
    return (
      <div>
        <Row>
          <Col span='24'>
            <Form {...Inputlayout} name='input-sid'>
              <Alert message='批量删除提示：格式为sid:ssid,一行一组' type='info' closable />
              <TextArea rows={6} onChange={e => this.setState({ sidSsidList: e.target.value })} />
              <Form.Item>
                <Button type='primary' htmlType='submit' loading={updating} onClick={() => this.onDelBtnClick()}>
                  删除
                </Button>
              </Form.Item>
            </Form>
          </Col>
        </Row>
      </div>
    )
  }

  render () {
    const { TabPane } = Tabs
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='2' onChange={(record) => this.onTagChange(record)} type='card' size='large'>
            <TabPane tab='批量添加' key='1'>
              {this.addBlackLIstHtml()}
            </TabPane>
            <TabPane tab='频道跳转白名单' key='2'>
              {this.displayWhiteListHtml()}
            </TabPane>
            <TabPane tab='批量删除' key='3'>
              {this.bathDeleteHtml()}
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default JumpChannelWhiteList
