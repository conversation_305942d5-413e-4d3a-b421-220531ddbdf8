import { getLists, update } from './api'
import { message } from 'antd'

export default {
  namespace: 'BabyRunPoisonConfig', // 只有这里需要修改

  state: {
    list: [],
    extra: ''
  },

  reducers: {
    updateList (state, { payload, extra }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload,
        extra: extra
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { list, extra } } = yield call(getLists, payload)

      yield put({
        type: 'updateList',
        payload: Array.isArray(list) ? list : [],
        extra: extra
      })
    },

    * updateItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(update, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * getItemByKey ({ payload }, { call, put }) {
      const { data: { status, list } } = yield call(payload)
      if (status === 0) {
        yield put({
          type: 'updateList',
          payload: list
        })
      } else {
        message.warning('not found')
      }
    }
  }
}
