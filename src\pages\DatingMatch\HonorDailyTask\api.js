import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/dating_match_bosssvr/honor_daily_task/list?${stringify(params)}`)
}

export function add (params) {
  return request(`/dating_match_bosssvr/honor_daily_task/update?${stringify(params)}`)
}

export function remove (params) {
  return request(`/dating_match_bosssvr/honor_daily_task/remove`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: stringify(params)
  })
}

export function update (params) {
  return request(`/dating_match_bosssvr/honor_daily_task/update?${stringify(params)}`)
}

export function updateRecommendStatus (params) {
  return request(`/dating_match_bosssvr/honor_daily_task/updateRecommendStatus?${stringify(params)}`)
}
