import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Modal, Input, DatePicker, Select, message } from 'antd'
import { connect } from 'dva'
// import { CSVLink } from 'react-csv'

const namespace = 'HonorDailyTask'
const FormItem = Form.Item
const Search = Input.Search
const Option = Select.Option
var moment = require('moment')

@connect(({ HonorDailyTask }) => ({
  HonorDailyTask
}))

class Index extends Component {
  // column structs.
  columns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: '配置频道', dataIndex: 'sid', key: 'sid', align: 'center' },
    { title: '配置子频道', dataIndex: 'ssid', key: 'ssid', align: 'center' },
    { title: '开播状态', dataIndex: 'liveStatus', key: 'onliliveStatusne', align: 'center', render: text => text === true ? '已开播' : '未开播' },
    { title: '推荐开始时间', dataIndex: 'startTime', key: 'startTime', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD HH:mm:ss') },
    { title: '推荐结束时间', dataIndex: 'endTime', key: 'endTime', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD HH:mm:ss') },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <Button style={{ marginRight: 10 }} size='small' type='primary' onClick={this.showModal(true, record)}>修改</Button>
          { record.recommendStatus === 1 ? <Button size='small' type='primary' onClick={this.updateRecommendStatus(record.id, 2)}>下推荐</Button> : <Button size='small' type='primary' onClick={this.updateRecommendStatus(record.id, 1)}>上推荐</Button>}
        </span>)
    }
  ]

  defaultPageValue = {
    defaultPageSize: 50,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, selectedRowKeys: [] }

  // show modal
  showModal = (isUpdate, record) => () => {
    if (record == null) record = { startTime: moment().unix(), endTime: moment().unix() }

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.startTime = moment.unix(v.startTime)
      v.endTime = moment.unix(v.endTime)
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? '修改' : '添加' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  onSelectChange = (selectedRowKeys, selectedRows) => {
    var value = selectedRows.map(item => item.id).join(',')
    console.log(value)
    this.setState({ selectedRowKeys })
    this.setState({ removeKey: value })
    this.setState({ exportKey: selectedRows })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // add and update
  onFinish = values => {
    const { dispatch } = this.props
    // const { isUpdate } = this.state
    console.log('values', values)
    values.startTime = values.startTime.unix()
    values.endTime = values.endTime.unix()
    const url = `${namespace}/updateItem`
    dispatch({
      type: url,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { ids: this.state.removeKey }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
    this.setState({ selectedRowKeys: null })
  }

  // handleDelOne = id => e => {
  //   const { dispatch } = this.props
  //   const data = { ids: id }
  //   dispatch({
  //     type: `${namespace}/removeItem`,
  //     payload: data
  //   })
  // }

  updateRecommendStatus = (id, recomm) => e => {
    const { dispatch } = this.props
    const data = { id: id, recommendStatus: recomm }
    dispatch({
      type: `${namespace}/updateRecommendStatus`,
      payload: data
    })
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = {}
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // reset search info
  handleReset = clearFilters => () => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  // get list from server.
  searchBySid = (value) => {
    const { dispatch } = this.props
    const data = { sid: this.state.searchSid, liveStatus: this.state.liveStatus }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  searchBySsid = (value) => {
    const { dispatch } = this.props
    const data = { ssid: this.state.searchSsid, liveStatus: this.state.liveStatus }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  handleLiveStateChange = (value) => {
    const { dispatch } = this.props
    const data = { liveStatus: value.key, zone: this.state.zone, mode: this.state.mode }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })

    this.setState({ liveStatus: value.key })
  }

  UpLoadOnChange = info => {
    if (info.file.status !== 'done') {
      return
    }
    if (info.file.response.status === 0) {
      message.success(`${info.file.name} file uploaded successfully`)
    } else {
      message.error(info.file.response.msg)
    }
    const { dispatch } = this.props
    const data = {}
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // content
  render () {
    // const { selectedRowKeys } = this.state
    // const rowSelection = {
    //   selectedRowKeys,
    //   onChange: this.onSelectChange
    // }
    const { route, HonorDailyTask: { list } } = this.props
    const { visible, isUpdate, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Search onSearch={value => this.searchBySid(value)} placeholder='search by sid' onChange={e => this.setState({ searchSid: e.target.value })} style={{ width: 150 }} /> {/* 搜索按钮 */}
            <Search onSearch={value => this.searchBySsid(value)} placeholder='search by ssid' onChange={e => this.setState({ searchSsid: e.target.value })} style={{ width: 150 }} /> {/* 搜索按钮 */}
            <Divider type='vertical' /> {/* 分割线 */}
            开播状态
            <Divider type='vertical' /> {/* 分割线 */}
            <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 120 }} onChange={this.handleLiveStateChange}>
              <Option value='0'>全部</Option>
              <Option value='1'>开播中</Option>
              <Option value='2'>未开播</Option>
            </Select>
            <Divider type='vertical' /> {/* 分割线 */}
            <Button type='primary' onClick={this.showModal(false)}>添加</Button>
            {/* <Button type='primary' onClick={this.handleDel(1)}>删除</Button> */}
            <Divider type='vertical' /> {/* 分割线 */}
            {/* <CSVLink data={list} filename={'output.csv'} headers={headers}>导出</CSVLink> */}
            <Divider />`
            {/* <Table rowKey={record => record.id} rowSelection={rowSelection} dataSource={list} columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} /> */}
            <Table rowKey={record => record.id} dataSource={list} columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem label='sid' name='sid' rules={[{ required: true }]}>
              <Input readOnly={isUpdate} />
            </FormItem>
            <FormItem label='ssid' name='ssid' rules={[{ required: true }]}>
              <Input readOnly={isUpdate} />
            </FormItem>
            <FormItem label='开始时间' name='startTime' rules={[{ required: true, message: '开始时间不能为空' }]}>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </FormItem>
            <FormItem label='结束时间' name='endTime' rules={[{ required: true, message: '结束时间不能为空' }]}>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default Index
