import React, { Component } from 'react'
import { Card, Table, Popconfirm } from 'antd'
import { connect } from 'dva'
import dateString from '@/utils/dateString'

const namespace = 'SynthesisConfig' // model 的 namespace
const stateTransfer = { 0: '提交人', 1: '审批人' }

@connect(({ SynthesisConfig }) => ({ // model 的 namespace
  model: SynthesisConfig // model 的 namespace
}))
class AuditComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false
    }
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getAuditList`
    })
  }

  // 需要修改
  columns = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: '合成礼物ID', dataIndex: 'targetPropsID', align: 'center' },
    { title: '合成礼物名称', dataIndex: 'targetPropsName', align: 'center' },
    { title: '合成礼物单价(紫水晶)', dataIndex: 'targetPropsPrice', width: 90, align: 'center' },
    { title: '单次合成数量', dataIndex: 'onceTargetCount', align: 'center' },
    { title: '扣除道具方式', dataIndex: 'consumeGiftType', align: 'center', render: text => ['未定义', '按订单扣', '按次数扣'][text] },
    { title: '可用合成材料', dataIndex: 'allowPropsType', align: 'center', render: text => ['豆荚/包裹礼物', '豆荚', '包裹礼物'][text] },
    { title: '魔法棒', dataIndex: 'giftA', align: 'center' },
    { title: '藏宝图', dataIndex: 'giftB', align: 'center' },
    { title: '能量石', dataIndex: 'giftC', align: 'center' },
    { title: '荣耀星石', dataIndex: 'giftD', align: 'center' },
    { title: '标签文案', dataIndex: 'labelName', align: 'center' },
    { title: '标签背景颜色', dataIndex: 'labelColor', align: 'center' },
    { title: '分类', dataIndex: 'classification', align: 'center', render: text => text === undefined ? '-' : ['普通', '稀有', '周星'][text] },
    { title: '排序权重', dataIndex: 'weight', align: 'center' },
    { title: '开始时间', dataIndex: 'startTime', align: 'center', width: 90, render: (text, record) => (text === 0 ? '无' : dateString(text)) },
    { title: '结束时间', dataIndex: 'endTime', align: 'center', width: 90, render: (text, record) => (text === 0 ? '无' : dateString(text)) },
    { title: '审批流', dataIndex: 'progress', align: 'center', width: 90, render: text => Array.isArray(text) ? text.map(i => <div key={Math.random().toString(32)}>{stateTransfer[i.state] + '： ' + i.passport}</div>) : '' },
    { title: '操作', dataIndex: 'commitType', align: 'center', render: text => <font color={'red'}> {['待审批', '新增', '修改', '删除', '审核'][text]} </font> },
    { title: '操作',
      align: 'center',
      render: (text, record) => (
        <span>
          {
            this.props.model.allowAudit > 0
              ? <div>
                <Popconfirm onConfirm={this.handleApproval(record.id, 1)} title='确认同意？'><a style={{ marginRight: 20 }}>同意</a></Popconfirm>
                <Popconfirm onConfirm={this.handleApproval(record.id, 2)} title='确认拒绝？'><a style={{ marginRight: 20 }}>拒绝</a></Popconfirm>
              </div>
              : '无权限'
          }
        </span>)
    }
  ]

  defaultValue = {}

  // 隐藏弹窗，不需要修改
  hideModal = () => {
    this.setState({ visible: false })
  }

  // 更新与删除
  handleSubmit = () => {
    this.formRef.submit()
  }

  handleApproval = (id, approval) => () => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/approvalItem`,
      payload: { id, approval }
    })
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { auditList } } = this.props

    return (
      <Card>
        <Table rowKey={record => record.index} dataSource={auditList} columns={this.columns} size='small' pagination={false} /> {/* 显示的列表 */}
      </Card>
    )
  }
}

export default AuditComponent
