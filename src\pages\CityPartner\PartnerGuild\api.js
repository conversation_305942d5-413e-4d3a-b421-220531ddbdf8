import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists () {
  return request('/dating_match_bosssvr/city_partner/get_partner_guild_list')
}

export function add (params) {
  return request(`/dating_match_bosssvr/city_partner/add_partner_guild`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function remove (params) {
  return request(`/dating_match_bosssvr/city_partner/remove_partner_guild?${stringify(params)}`)
}

export function update (params) {
  return request(`/dating_match_bosssvr/city_partner/update_partner_guild`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function search (params) {
  return request(`/dating_match_bosssvr/city_partner/search_partner_guild?${stringify(params)}`)
}
