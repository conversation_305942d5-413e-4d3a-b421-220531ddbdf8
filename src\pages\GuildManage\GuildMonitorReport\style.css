.sortTable .ant-table-tbody {
  display: none;
}

.guildContent .guildContent-wrap {
  display: flex;
  width: 100%;
  height: 110px;
}

.guildContent .guildContent-card {
  width: 24%;
  height: 100%;
}

.guildContent .guildContent-card:nth-child(1) {
  width: 40%;
  display: flex;
  align-items: flex-start;
}

.guildContent .guildContent-card:nth-child(5) {
  width: 30%;
}

.guildContent .guildContent-card:nth-child(1) .guildContent-card-info {
  padding-left: 15px;
}

.guildContent .guildContent-card:nth-child(2),.guildContent-card:nth-child(3),.guildContent-card:nth-child(4),.guildContent-card:nth-child(5) {
  margin-top: 32px;
}

.guildContent .guildContent-card .guildContent-card-guildName {
  font-size: 16px;
  font-weight: bold;
}

.guildContent .guildContent-card .guildContent-card-pic {
  width: 100px;
}

.guildContent .guildContent-card .guildContent-card-level {
  font-size: 13px;
  font-weight: bold;
}

.guildContent .guildContent-card font {
  color: #9a9999;
}

.guildContent .guildContent-card .guildContent-card-operatorTimeDesc {
  color: red;
}

.guildContent .guildContent-card .guildContent-card-grade {
  color: red;
}

.guildContent .guildContent-card:nth-child(2)  {
  padding-left: 1px;
}

.guildContent .guildContent-card:nth-child(3)  {
  padding-left: 20px;
}

/*.ant-table-tbody > tr > td {*/
/*  !*border-bottom: 20px solid #fff;*!*/
/*}*/

.sortTable  {
  float: left;
  display: inline-block;
}

.sortBtn {
  display: inline-block;
  float: right;
  margin-right: 3px;
  margin-top: 13px;
}

.guildContent-card-guildTag {
  width: 28px;
  height: 28px;
  float: left;
  display: inline-block;
  margin-right: 5px;
}

.guildContent-card-guildTag-div::after {
  content: "";
  clear: both;
  display: table;
}
