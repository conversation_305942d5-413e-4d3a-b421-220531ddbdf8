import { addRuleData, pageListBusiness, pageListRuleData, removeRuleData, updateRuleData } from '../common'

export default {
  namespace: 'RuleDataManage',
  state: {
    dataList: [],
    businessList: []
  },

  reducers: {
    // 更新数据列表，usage:
    // yield put({
    //  type: 'updateList',
    //  payload: {
    //    name: 'configList', list: Array.isArray(data) ? data : []
    //  }
    // })
    updateList (state, { payload }) {
      let obj = { ...state }
      obj[payload.name] = (payload.list || []).map((i, index) => {
        i.index = index + 1
        return i
      })
      return obj
    }
  },

  // 数据变更
  effects: {
    // 分页查询规则函数列表
    * pageListRuleData ({ payload, callback }, { call, put }) {
      const { data } = yield call(pageListRuleData, payload)
      yield put({
        type: 'updateList',
        payload: {
          name: 'dataList',
          list: data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
        }
      })

      if (callback) {
        callback(data.data)
      }
    },
    * listAllBusiness ({ payload }, { call, put }) {
      const { data } = yield call(pageListBusiness, { pageNo: -1, pageSize: -1 })
      yield put({
        type: 'updateList',
        payload: {
          name: 'businessList',
          list: data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
        }
      })
    },

    // 更新规则数据
    * updateRuleData ({ payload, callback }, { call, put }) {
      const { data } = yield call(updateRuleData, payload)
      if (callback) {
        callback(data)
      }
    },

    // 添加规则数据
    * addRuleData ({ payload, callback }, { call, put }) {
      const { data } = yield call(addRuleData, payload)
      if (callback) {
        callback(data)
      }
    },

    // 删除规则数据
    * removeRuleData ({ payload, callback }, { call, put }) {
      const { data } = yield call(removeRuleData, payload)
      if (callback) {
        callback(data)
      }
    }

  }
}
