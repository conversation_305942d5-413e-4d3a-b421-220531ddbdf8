import React, { Component } from 'react'
import <PERSON>HeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import { connect } from 'dva'
import TaskConfig from './components/task_config.js'
import RewardConfirm from './components/reward_confirm.js'
import TaskProgress from './components/task_progress.js'
import RewardHistory from './components/reward_history.js'
const TabPane = Tabs.TabPane

@connect(({ hatMonthTask }) => ({ // model 的 namespace
  model: hatMonthTask // model 的 namespace
}))
class Index extends Component { // 默认页面组件，不需要修改
  /** *******************************页面布局*************************************************************/
  render () {
    const { route, model: { taskConfigInfo, rewardHistoryList, taskProgressInfo, rewardConfirmInfo } } = this.props
    console.log(rewardConfirmInfo)
    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs type='card' defaultActiveKey='2' >
          <TabPane tab='任务配置' key='1'>
            <TaskConfig dataInfo={taskConfigInfo} navGroup={1} />
          </TabPane>
          <TabPane tab='发奖确认' key='2'>
            <RewardConfirm dataInfo={rewardConfirmInfo.list} canAllConfirm={rewardConfirmInfo.canAllConfirm} guildSum={rewardConfirmInfo.guildSum} navGroup={1} />
          </TabPane>
          <TabPane tab='任务完成进度报表' key='3'>
            <TaskProgress dataInfo={taskProgressInfo.list} progressSummaryInfo={taskProgressInfo.info} navGroup={1} />
          </TabPane>
          <TabPane tab='发奖明细' key='4'>
            <RewardHistory dataInfo={rewardHistoryList} navGroup={1} />
          </TabPane>
        </Tabs>
      </PageHeaderWrapper>
    )
  }
}

export default Index // 保证唯一
