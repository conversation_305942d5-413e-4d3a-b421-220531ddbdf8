import request from '@/utils/request'
import { stringify } from 'qs'

function filterNumberArray (value) {
  if (!value) {
    return ''
  }
  value = value.replace(/([^\d]|[\s，, ])+/g, ',')
  if (value.endsWith(',')) {
    value = value.substring(0, value.length - 1)
  }
  return value
}

// 获取列表
export function listCompere (params) {
  params.uid = filterNumberArray(params.uid)
  params.yyno = filterNumberArray(params.yyno)
  return request(`/compere_data_base/list_compere?${stringify(params)}`)
}

// 刷新
export function refreshCompereList (params) {
  params.uids = filterNumberArray(params.uids)
  return request(`/compere_data_base/refresh_compere_list?${stringify(params)}`)
}

// 查询备注信息
export function getRemarkInfo (params) {
  return request(`/compere_data_base/get_remark_info?${stringify(params)}`)
}

// 更新备注信息
export function updateRemarkInfo (params) {
  return request(`/compere_data_base/update_remark?${stringify(params)}`)
}

// 更新备注信息
export function getRemarkHistory (params) {
  return request(`/compere_data_base/get_remark_history?${stringify(params)}`)
}

// 添加或删除白名单
export function whiteListAddOrDelete (operator, readySuper, uid) {
  if (operator !== 'DELETE' && operator !== 'ADD' && operator !== 'UPGRADE') {
    console.error('unexpect params: operator=' + operator)
    return
  }

  let apiUrl = ''
  switch (operator) {
    case 'DELETE':
      apiUrl = '/white_list/super_compere_del'
      break
    case 'ADD':
      if (readySuper) {
        apiUrl = '/white_list/super_approaching_compere_add'
      } else {
        apiUrl = '/white_list/super_compere_add'
      }
      break
    case 'UPGRADE':
      apiUrl = '/white_list/super_compere_add'
      break
    default:
      console.error('unexpect params: operator=' + operator)
      return
  }

  let form = 'uid=' + uid
  return request(apiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: form
  })
}

export function getLists (readySuper) {
  if (!readySuper) {
    let url = `/white_list/super_compere_list`
    return request(url, { jsonp: true })
  }
  let url = '/white_list/super_approaching_compere_list'
  return request(url, {
    method: 'POST'
  })
}

// 更改超级主持白名单信息
export function modSuperCompereWhite (uid, privilege) {
  let form = 'uid=' + uid + '&privilege=' + privilege
  return request(`/white_list/super_compere_mod`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: form
  })
}

export function getCompereDetailInfo (params) {
  return request(`/compere_data_base/get_compere_detail_info?${stringify(params)}`)
}

// 取消超主身份
export function cancelSuperCompere (params) {
  return request(`/compere_data_base/cancel_super_compere`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
