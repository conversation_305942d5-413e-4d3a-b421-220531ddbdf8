// import React, { Component } from 'react'

import React, { Component } from 'react'
import { Table, Divider, Form, Card, Modal, Input, Popconfirm, Select, Row, Col, Button, InputNumber, message } from 'antd'
import { connect } from 'dva'
import { deepClone } from '@/utils/common'
import { propTypeOptions } from '../../dropCommon'
import PrizeSelector from './prizeSelector'

const namespace = 'dropMain'
const FormItem = Form.Item

const prizeOptions = [
  { label: '交友奖励道具', value: 2 },
  { label: '宝贝奖励道具', value: 36 },
  { label: '追玩奖励道具', value: 34 }
]

const selOptoins = [ // value对应道具池ID
  { label: '交友空投', value: 1000, prizeType: 2 },
  { label: '交友普通空投', value: 17000, prizeType: 2 },
  { label: '宝贝空投', value: 7000, prizeType: 36 },
  { label: '追玩语音房', value: 9000, prizeType: 34 },
  { label: '语音房幸运小狗-普通', value: 15000, prizeType: 34 },
  { label: '语音房幸运小狗-超级', value: 16000, prizeType: 34 }
]

@connect(({ dropMain }) => ({
  model: dropMain
}))

class ProgressConfigComponent extends Component {
  state = {
    visible: false,
    isUpdate: false,
    pid: this.props.groupType === 'vr' ? 9000 : 17000,
    prizeType: this.props.groupType === 'vr' ? 34 : 2
  }

  getColumn = (pid) => {
    let valueDesc = (pid === 15000 || pid === 16000) ? '金钻' : '紫水晶'
    let columns = [
      { title: '普通抽取道具进度%', dataIndex: 'progress', align: 'center' },
      { title: '礼物渠道', dataIndex: 'prizeType', align: 'center', render: v => { return prizeOptions.find(item => { return item.value === v })?.label } },
      { title: '概率', dataIndex: 'rate', align: 'center', render: (v) => { return rateForamter(v) } },
      { title: '奖励道具ID', dataIndex: 'propsId', align: 'center' },
      { title: '奖励道具名称', dataIndex: 'propsName', align: 'center' },
      { title: '稀有度', dataIndex: 'propsType', align: 'center', render: (text, record) => { return propTypeOptions.find(val => val.value === text)?.label } },
      { title: '数量', dataIndex: 'count', align: 'center' },
      { title: `单价(${valueDesc})`, dataIndex: 'value', align: 'center' },
      { title: `总价值(${valueDesc})`, align: 'center', render: (_, record) => record.value * record.count },
      { title: '操作',
        key: 'operation',
        align: 'center',
        render: (text, record) => (
          <span>
            <a onClick={this.showModal(true, record)}>更新</a><Divider type='vertical' />
            <Popconfirm title='确认?' onConfirm={this.handleDel(record.id)}>
              <a href=''>删除</a>
            </Popconfirm>
          </span>)
      }
    ]
    return deepClone(columns)
  }

  // show modal
  showModal = (isUpdate, record) => () => {
    if (this.formRef) {
      let v = $.extend({}, true, record)
      this.formRef.resetFields()
      if (isUpdate) {
        v.join = [record.propsId, record.propsName, record.value].join('-')
      }
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? '更新' : '添加' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // add and update
  onFinish = values => {
    const { dispatch } = this.props
    const { pid, prizeType } = this.state
    const url = `${namespace}/upsetProgress`

    let jn = values.join.split('-')
    if (jn.length !== 3) {
      message.error('无效礼物配置' + values.join)
      return
    }
    values.propsId = parseInt(jn[0])
    values.propsName = jn[1]
    values.value = parseInt(jn[2])
    values.count = parseInt(values.count)
    values.pid = pid
    values.prizeType = prizeType
    values.appId = prizeType
    values.id = [pid, values.progress].join(':')

    dispatch({
      type: url,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/removeProgress`,
      payload: { id: key }
    })
  }

  // get list from server.
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getProgressList`
    })
  }

  onSelChange = v => {
    this.setState({ pid: v, prizeType: selOptoins.find(item => item.value === v).prizeType })
  }

  // 选项过滤
  optionsFilter = (before) => {
    const { poolNameOptions } = this.props.model
    const after = before.filter(item => {
      return poolNameOptions.some(entry => { return entry.value === item.value })
    })
    return after
  }

  // content
  render () {
    const { model: { progressList, globalPrizeList } } = this.props
    const { title, visible, pid, isUpdate, prizeType } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 8 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 16 },
        sm: { span: 16 }
      }
    }

    return (
      <Card>
        <Row gutter={4}>
          <Col span={3}>
            <Select style={{ width: '100%' }} onChange={this.onSelChange} defaultValue={pid} options={this.optionsFilter(selOptoins)} />
          </Col>
          <Col>
            <Select disabled value={prizeType} options={prizeOptions} />
          </Col>
          <Col>
            <Button onClick={this.showModal(false)} type='primary'>新增</Button>
          </Col>
        </Row>
        <Divider />
        <Table rowKey={(record, index) => index} dataSource={progressList.filter(v => v.pid === pid)} columns={this.getColumn(pid)} />
        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem hidden name='id'>
              <Input />
            </FormItem>
            <FormItem label='普通抽取道具进度%' name='progress' rules={[{ required: true, message: '请填写普通抽取道具进度%' }]}>
              <InputNumber min={1} max={100} style={{ width: '100%' }} readOnly={isUpdate} />
            </FormItem>
            <FormItem label={prizeOptions.find(v => v.value === prizeType).label} name='join' rules={[{ required: true, message: '请填写营收ID' }]}>
              <PrizeSelector
                type='select'
                prizeList={globalPrizeList}
                appIDLimit={prizeType}
                onComfirm={(v) => {
                  const { id, name, price } = v.raw
                  this.formRef.setFieldsValue({ join: [id, name, price].join('-') })
                }}
              />
            </FormItem>
            <FormItem label='稀有度' name='propsType' rules={[{ required: true, message: '请选择稀有度' }]}>
              <Select options={propTypeOptions} />
            </FormItem>
            <FormItem label='概率' name='rate' >
              <RateEditor />
            </FormItem>
            <FormItem label='数量' name='count' rules={[{ required: true, message: '请填写数量' }]}>
              <Input onChange={this.handleCountChange} placeholder='必须非负整数' />
            </FormItem>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default ProgressConfigComponent

// 概率编辑器
const RateEditor = (props) => {
  console.debug('props===>', props)
  const { value, onChange } = props
  const probabilityBase = 10000
  const fixedValue = Number(value * 100.0 / probabilityBase).toFixed(2)
  return <div>
    <InputNumber value={fixedValue} onChange={v => { onChange(Number(v) * probabilityBase / 100.0) }} /><span> %</span>
  </div>
}

// 概率格式化
const rateForamter = (v) => {
  const probabilityBase = 10000
  if (v === 0 || v === undefined) {
    return '0.00%'
  }
  return `${Number(v * 100.0 / probabilityBase).toFixed(2)}%`
}
