import request from '@/utils/request'
import { stringify } from 'qs'

export function getPropsList () {
  return request(`/lottery/get_props_config_list`)
}

export function getPropsDetailList () {
  return request(`/glory_hall_boss/get_props_detail_config_list`)
}

export function updatePropsDetail (params) {
  return request(`/glory_hall_boss/set_props_detail_config_info?${stringify(params)}`)
}

export function deletePropsDetail (params) {
  return request(`/glory_hall_boss/del_props_detail_config_info?${stringify(params)}`)
}

export function getPropsClassList (params) {
  return request(`/glory_hall_boss/get_props_class_config_list?${stringify(params)}`)
}
