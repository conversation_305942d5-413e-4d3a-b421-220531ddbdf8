import { getCompereDetailInfo, lisCompereWholeLib, listCompereWholeLibHistory } from './api'
import { message } from 'antd'
// import { message } from 'antd'

export default {
  namespace: 'compereBaseInfo', // 全局唯一标识

  state: {
    tableLoadingHistory: false,
    list: [],
    listHistory: [],
    total: 0,
    exportTotalList: [],
    exportTotal: 0,
    compereDetailInfo: {}
  },

  reducers: {
    // 单个修改某个state成员
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },

    updateList (state, { payload, total }) {
      return {
        ...state,
        list: payload,
        total: total
      }
    },

    updateListHistory (state, { payload }) {
      return {
        ...state,
        listHistory: payload
      }
    },

    listExport (state, { payload, total }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }

      return {
        ...state,
        exportTotalList: payload,
        exportTotal: total
      }
    }
  },

  effects: {
    * lisCompereWholeLib ({ payload }, { call, put }) {
      try {
        let { data: { data } } = yield call(lisCompereWholeLib, payload)
        let list = data !== null ? data.data : []
        let total = data !== null ? data.total : 0
        list = Array.isArray(list) ? list : []
        for (let i = 0; i < list.length; i++) {
          list[i].idx = i + 1
        }

        yield put({
          type: 'updateList',
          payload: list,
          total: total
        })
      } catch (e) {
        message.error('exception: ', e)
      }
    },

    * exportCompereWholeLibList ({ payload }, { call, put }) {
      const { cbFunc } = payload
      let { data: { data } } = yield call(lisCompereWholeLib, payload)
      let list = data !== null ? data.data : []
      let total = data !== null ? data.total : 0
      list = Array.isArray(list) ? list : []
      for (let i = 0; i < list.length; i++) {
        list[i].idx = i + 1
      }
      yield put({
        type: 'listExport',
        payload: list,
        total
      })

      cbFunc()
    },

    * listCompereWholeLibHistory ({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingHistory', newValue: true }
      })

      try {
        let { data: { data } } = yield call(listCompereWholeLibHistory, payload)
        var list = Array.isArray(data) ? data : []
        for (let i = 0; i < list.length; i++) {
          list[i].idx = i + 1
        }
        yield put({
          type: 'updateListHistory',
          payload: list
        })
      } catch (e) {
        message.error('exception: ', e)
      }

      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingHistory', newValue: false }
      })
    },

    * getCompereDetailInfo ({ payload }, { call, put }) {
      try {
        let { data: { data, status, msg } } = yield call(getCompereDetailInfo, payload)

        if (status !== 0) {
          message.error(msg)
          return
        }
        yield put({
          type: 'updateState',
          payload: { name: 'compereDetailInfo', newValue: data }
        })
      } catch (e) {
        message.error('exception: ', e)
      }
    }
  }
}
