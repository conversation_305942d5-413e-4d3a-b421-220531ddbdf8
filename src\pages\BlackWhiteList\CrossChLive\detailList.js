import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Row, Col, Typography, Space, Table, InputNumber, Button } from 'antd'
import { onExportExcel } from '@/utils/common'
const { Text } = Typography

const namespace = 'crossLive'

@connect(({ crossLive }) => ({
  model: crossLive
}))

class DetailList extends Component {
  state = {
    searchSid: '',
    searchAsid: '',
    searchPlaySid: '',
    searchUid: '',
    searchYY: ''
  }

  componentDidMount = () => {
    this.refreshList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 查询列表数据
  refreshList = () => {
    const { searchAsid, searchSid, searchPlaySid, searchUid, searchYY } = this.state
    const { getDataFunc } = this.props
    this.callModel(getDataFunc, {
      params: {
        asid: searchAsid,
        sid: searchSid,
        playSid: searchPlaySid,
        uid: searchUid,
        yy: searchYY
      }
    })
  }

  // 导出数据
  onExportData = (columns, dataSource, fileName) => {
    onExportExcel(columns, dataSource, fileName)
  }

  render () {
    const { dataSource, name, type } = this.props
    const { searchAsid, searchSid, searchPlaySid, searchUid, searchYY } = this.state
    const target = type === 'mgr' ? '房管' : '主持'

    const columns = [
      { title: '短位频道', dataIndex: 'asid' },
      { title: '签约频道', dataIndex: 'sid' },
      { title: '经营频道', dataIndex: 'playAsid' },
      { title: '经营子频道', dataIndex: 'ssid' },
      { title: `${target}UID`, dataIndex: 'uid' },
      { title: `${target}YY`, dataIndex: 'yy' },
      { title: '房管昵称', dataIndex: 'nick' }
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <Card>
        <Row>

          <Col span={24} style={{ marginBottom: '1em' }}>
            <Space>

              <div>
                <Text>短位频道：</Text>
                <InputNumber style={{ width: '12em' }} placeholder='搜索短位频道' value={searchAsid} onChange={v => this.setState({ searchAsid: v })} />
              </div>
              <div>
                <Text>签约频道: </Text>
                <InputNumber style={{ width: '12em' }} placeholder='搜索签约频道' value={searchSid} onChange={v => this.setState({ searchSid: v })} />
              </div>
              <div>
                <Text>经营频道: </Text>
                <InputNumber style={{ width: '12em' }} placeholder='经营频道' value={searchPlaySid} onChange={v => this.setState({ searchPlaySid: v })} />
              </div>
              <div>
                <Text>{`${target}UID: `}</Text>
                <InputNumber style={{ width: '12em' }} placeholder={`${target}UID`} value={searchUid} onChange={v => this.setState({ searchUid: v })} />
              </div>
              <div>
                <Text>{`${target}YY: `}</Text>
                <InputNumber style={{ width: '12em' }} placeholder={`${target}YY`} value={searchYY} onChange={v => this.setState({ searchYY: v })} />
              </div>

              <Button type='primary' onClick={this.refreshList}>搜索</Button>
              <Button onClick={() => this.onExportData(columns, dataSource, `${name}.xlsx`)} >导出</Button>
            </Space>
          </Col>

          <Col span={24}>
            <Table columns={columns} dataSource={dataSource} />
          </Col>

        </Row>
      </Card>
    )
  }
}

export default DetailList
