import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { connect } from 'dva'
import { Card, message } from 'antd'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import { initGlobalBossConfig } from '../globalConfig'
import PoolListConfig from './tabs/poolList'
import PoolEditor from './tabs/poolEdit'
import BundleRewardTmp from './tabs/bundleRewardTmp'
import WarnConfig from './tabs/warnConfig'

const namespace = 'dropBoxWar'

@connect(({ dropBoxWar }) => ({
  model: dropBoxWar
}))

class DropBoxWar extends Component {
  state = { }

  componentDidMount () {
    initGlobalBossConfig(this.changeState, 'bw')
    this.getAllPrize()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

   // 获取所有奖励列表
   getAllPrize = () => {
     this.callModel('getAllPrizeList', {
       cbFunc: (ret) => {
         const { status, msg } = ret
         if (status !== 0) {
           message.warn('获取奖励列表失败: msg=' + msg)
         }
       }
     })
   }

   render () {
     return (
       <PageHeaderWrapper>
         <Card>
           <Tabs id='boxwar' type='card' defaultActiveKey='1'>
             <TabPane tab='普通道具池查看' key='1'>
               <PoolListConfig />
             </TabPane>
             <TabPane tab='普通道具池配置和审批' key='3'>
               <PoolEditor />
             </TabPane>
             <TabPane tab='组合大礼查看' key='2'>
               <BundleRewardTmp isProd key='prod' />
             </TabPane>
             <TabPane tab='组合大礼配置和审批' key='4'>
               <BundleRewardTmp key='temp' />
             </TabPane>
             <TabPane tab='预警配置' key='5'>
               <WarnConfig />
             </TabPane>
           </Tabs>
         </Card>
       </PageHeaderWrapper>
     )
   }
}

export default DropBoxWar
