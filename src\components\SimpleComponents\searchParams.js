import React, { Component } from 'react'
import { Form, InputNumber, DatePicker, Select, message, Input, Space, Button } from 'antd'

/* 条件搜索栏表单_参数:
formProps： {}, 传到From组件的属性
onChange: (v)=>{...}
formatTimeRange: boolean, 传true时将时间类型的数据转换到秒级时间戳整数
columns: 元素数组, 控制字段和类型：

columns元素属性解析：{
    label: string, 标题
    fieldName: string, 字段名
    inputType： string, 控制输入类型，支持: [InputNumber, Input, TimeRange, Time, Selecter, Button]
    defaultValue: any, 默认值
    extProps： any, 传到输入器的属性
  }

columns 参考: parmasColumn = [
  { label: '数字输入', fieldName: 'number', inputType: 'InputNumber', defaultValue: 123123, extProps: { width: '5em' } },
  { label: '字符串', fieldName: 'name', inputType: 'Input', defaultValue: '啦啦啦', extProps: { width: '5em' } },
  { label: '时间范围', fieldName: 'timeRange', inputType: 'TimeRange', defaultValue: [moment().startOf('day'), moment().endOf('day')], extProps: { placeholder: ['开始时间', '结束时间'], style: { width: '16em' }, allowClear: true } },
  { label: '时间', fieldName: 'timestamp', inputType: 'Time', defaultValue: moment().startOf('day'), extProps: { style: { width: '12em' } } },
  { label: '日期', fieldName: 'date', inputType: 'DatePicker', defaultValue: moment().startOf('day'), extProps: { style: { width: '8em' } } },
  { label: '下拉框', fieldName: 'status', inputType: 'Selecter', defaultValue: 1, extProps: { style: { width: '7em' }, options: [{ label: '选项A', value: 1 }, { label: '选项B', value: 2 }] } },
  { label: '查询', inputType: 'Button', extProps: { onClick: () => { console.debug('...') } } },
]

*/
export default class SearchParams extends Component {
  // formStyle = { float: 'left', marginRight: '1em' }
  formStyle = { }
  state = {
    fromValue: {}
  }

  componentDidMount = () => {
    let fromValue = {}
    const { columns } = this.props
    columns.forEach((item) => {
      const { fieldName, defaultValue } = item
      fromValue[fieldName] = defaultValue
    })
    this.formRef.setFieldsValue(fromValue)
    this.changeValue(fromValue)
  }

  changeValue = (v) => {
    const { onChange } = this.props
    onChange(this.valueFormater(v))
  }

  valueFormater = (before) => {
    const { columns, formatTimeRange } = this.props
    if (!formatTimeRange) return before
    let after = { ...before }
    columns.forEach(item => {
      const { inputType, fieldName } = item
      if (inputType === 'TimeRange') { // 将数组表示的时间范围解构到两个整数字段
        after[`${fieldName}1`] = before[fieldName] ? before[fieldName][0]?.unix() : 0
        after[`${fieldName}2`] = before[fieldName] ? before[fieldName][1]?.unix() : 0
        delete after[fieldName]
      }
      if (inputType === 'Time' || inputType === 'DatePicker') {
        after[fieldName] = before[fieldName]?.unix() || 0
      }
    })
    return after
  }

  InputNumberRender = (name, label, extProps) => {
    return (
      <Form.Item name={name} label={label} style={this.formStyle}>
        <InputNumber {...extProps} />
      </Form.Item>
    )
  }
  InputRender = (name, label, extProps) => {
    return (
      <Form.Item name={name} label={label} style={this.formStyle}>
        <Input {...extProps} />
      </Form.Item>
    )
  }
  TimeRangeRender = (name, label, extProps) => {
    return (
      <Form.Item name={name} label={label} style={this.formStyle}>
        <DatePicker.RangePicker format='YYYY-MM-DD HH:mm:ss' {...extProps} showTime />
      </Form.Item>
    )
  }
  TimePickerRender = (name, label, extProps) => {
    return (
      <Form.Item name={name} label={label} style={this.formStyle}>
        <DatePicker.TimePicker format='YYYY-MM-DD HH:mm:ss' {...extProps} showTime />
      </Form.Item>
    )
  }
  DatePickerRender = (name, label, extProps) => {
    return (
      <Form.Item name={name} label={label} style={this.formStyle}>
        <DatePicker format='YYYY-MM-DD' {...extProps} />
      </Form.Item>
    )
  }
  SelecterRender = (name, label, extProps) => {
    return (
      <Form.Item name={name} label={label} style={this.formStyle}>
        <Select {...extProps} />
      </Form.Item>
    )
  }
  ButtonRender = (label, extProps) => {
    return (
      <Form.Item>
        <Button {...extProps}>{label}</Button>
      </Form.Item>
    )
  }

  render () {
    const { columns, formProps } = this.props

    return (
      <Form ref={(r) => { this.formRef = r }} onFinish={this.changeValue} onValuesChange={() => { this.formRef.submit() }} {...formProps} >
        <Space align='center' size='large'>
          {
            columns.map(item => {
              const { inputType, fieldName, label, extProps } = item
              switch (inputType) {
                case 'InputNumber':
                  return this.InputNumberRender(fieldName, label, extProps)
                case 'Input':
                  return this.InputRender(fieldName, label, extProps)
                case 'Selecter':
                  return this.SelecterRender(fieldName, label, extProps)
                case 'TimeRange':
                  return this.TimeRangeRender(fieldName, label, extProps)
                case 'DatePicker':
                  return this.DatePickerRender(fieldName, label, extProps)
                case 'Time':
                  return this.TimePickerRender(fieldName, label, extProps)
                case 'Button':
                  return this.ButtonRender(label, extProps)
              }
              message.warn(`使用方法错误,请检查代码: (SearchParams_${inputType})`)
              return ''
            })
          }
        </Space>
      </Form>
    )
  }
}
