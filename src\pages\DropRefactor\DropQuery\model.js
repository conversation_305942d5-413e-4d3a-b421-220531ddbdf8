import { getSignalFromServer, getDropFromServer } from './api'
import { message } from 'antd'
import { genGetRequireTemplate } from '@/utils/common'

const getSummaryStat = genGetRequireTemplate('/drop_stat/user_summary', 'summaryStat')

export default {
  namespace: 'dropQuery',

  state: {
    signalList: [], // 道具池列表
    dropList: [], // 空投记录
    offsetList: [], // 偏移量列表
    summaryStat: [] // 用户统计信息
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },
    updateSignal (state, { payload }) { return { ...state, signalList: payload } },
    updateDrop (state, { payload }) { return { ...state, dropList: payload } },
    updateOffset (state, { payload }) { return { ...state, offsetList: payload } }
  },

  effects: {
    getSummaryStat,
    * getSignal ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getSignalFromServer, payload)

      yield put({
        type: 'updateSignal',
        payload: Array.isArray(list) ? list : []
      })
    },

    * getDrop ({ payload }, { call, put }) {
      const { data: { list, status, msg } } = yield call(getDropFromServer, payload)
      if (status !== 0) {
        message.warn(msg)
      }

      yield put({
        type: 'updateDrop',
        payload: Array.isArray(list) ? list : []
      })
    }
  }
}
