import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const qyertTemplate = genGetRequireTemplate('/fts_component/template/query_config', 'templateList', (raw) => {
  return raw.map(item => {
    item.sid = item.sid.join(',')
    item.ssid = item.ssid.join(',')
    item.newSid = item.newSid ? item.newSid.join(',') : ''
    item.newSsid = item.newSsid ? item.newSsid.join(',') : ''
    item.excludeSid = item.excludeSid.join(',')
    item.excludeSsid = item.excludeSsid.join(',')
    item.ticket = item.ticket ? item.ticket.join('\n') : ''
    item.excludeTicket = item.excludeTicket ? item.excludeTicket.join('\n') : ''
    return item
  })
})
const getDebugResult = genGetRequireTemplate('/fts_component/template/gateway_debug', 'debugResult')
const addTemplate = genUpdateTemplate('/fts_component/template/new_config')
const updateTemplate = genUpdateTemplate('/fts_component/template/update_config')
const getChanMatchInfo = genGetRequireTemplate('/fts_component/template/channel_match_info')
// const getChanDetail = genUpdateTemplate('/fts_component/template/channel_detail_info')
// const getChanUpdateHistory = genGetRequireTemplate('/fts_component/template/get_channel_setting_history', 'updateHistory')
// const udpateChanSetting = genGetRequireTemplate('/fts_component/template/update_channel_setting')

export default {
  namespace: 'templateGateWay',
  state: {
    templateList: [],
    debugResult: [],
    updateHistory: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    qyertTemplate,
    getDebugResult,
    getChanMatchInfo,
    addTemplate,
    updateTemplate
    // getChanDetail,
    // getChanUpdateHistory,
    // udpateChanSetting
  }
}
