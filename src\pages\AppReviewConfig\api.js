import request from '@/utils/request'
import { stringify } from 'qs'

export const doPost = function (apiPath, params) {
  return request(apiPath, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function getConfigList (params) {
  return request(`/playcenter_boss/app_review_config_list?${stringify(params)}`)
}

export function getApprovalList (params) {
  return request(`/playcenter_boss/app_approval_list?${stringify(params)}`)
}

export function updateConfig (params) {
  return doPost(`/playcenter_boss/app_review_config_update`, params)
}
