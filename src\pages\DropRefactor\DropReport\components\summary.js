import React, { Component } from 'react'
import { Card, Table, DatePicker, Row, Col, Button, Select, Form, Tooltip } from 'antd'
import { connect } from 'dva'
import moment from 'moment' 
import { GenTip, totalOutFormater, outRateFormaterV2, outRateFormater, numberFormater, totalOffsetFormater, prizeOutFormater } from './common'
import { genColumnTooltip } from '@/components/SimpleComponents'
import { onExportExcel } from '@/utils/common'

const namespace = 'dropReport'
const defaultDropType = 1000
const defaultStatType = 1000

// 统计渠道: 汇总，PC，Yo语音， 追玩，手Y
const statTypeOptions = [
  { label: '汇总', value: 1000 },
  { label: 'PC', value: 0 },
  { label: '手Y', value: 1 },
  { label: '追玩', value: 2 },
  { label: 'Yomi', value: 3 },
  { label: 'Web', value: 4 },
  { label: 'h5', value: 7 }
]

// 玩法
const dropTypeOptions = [
  { label: '汇总', value: 1000 },
  { label: '抢空投', value: 0 },
  { label: '抢物资', value: 1 },
  // { label: '幸运小狗', value: 2 },
  { label: '物资大战', value: 3 }
]

@connect(({ dropReport }) => ({
  model: dropReport
}))

// 抢空投-空投日报-交友日报
class DropReportSummaryComponent extends Component {
  state={
    dropType: defaultDropType
  }

  componentDidMount () {
    let begin = moment().add(-7, 'days')
    let end = moment()
    const params = { appID: 2, dropType: defaultDropType, statType: defaultStatType, begin: begin.format('YYYYMMDD'), end: end.format('YYYYMMDD') }
    this.formRef.setFieldsValue({ range: [begin, end], dropType: defaultDropType, statType: defaultStatType })
    this.callModel('getDailySummary', { params })
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  avgFormater = (a, b) => {
    if (b === 0) {
      return 0
    }
    return Number(a / b).toFixed(0)
  }

  // 空投用的表头
  columnsDrop = [
    { title: '日期', dataIndex: 'id' },
    { title: '普通模拟收入', dataIndex: 'normalIn', ...genColumnTooltip('普通空投收入'), render: v => numberFormater(v) },
    { title: '普通支出', dataIndex: 'normalOut', ...genColumnTooltip('普通道具池支出+普通进度支出'), render: (v, r) => numberFormater(v) },
    { title: '普通人数', dataIndex: 'normalCount', render: v => numberFormater(v) },
    { title: '疯狂模拟收入', dataIndex: 'crazyIn', render: v => numberFormater(v) },
    { title: '疯狂支出', dataIndex: 'crazyOut', ...genColumnTooltip('疯狂道具池支出+疯狂进度支出'), render: v => numberFormater(v) },
    { title: '疯狂人数', dataIndex: 'crazyCount', render: v => numberFormater(v) },
    { title: '翻倍人数', dataIndex: 'extraCount', ...genColumnTooltip('当日发放了翻倍礼物的去重人数') },
    { title: '翻倍支出', dataIndex: 'extraOut', render: v => numberFormater(v), ...genColumnTooltip('翻倍礼物总支出') },
    { title: '模拟收入', dataIndex: 'totalIn', render: v => numberFormater(v) },
    { title: '礼物支出', dataIndex: '_5', render: (v, r) => prizeOutFormater(r, false), ...genColumnTooltip('普通支出+疯狂支出+翻倍支出+龙宫支出+补给箱支出') },
    { title: '人均支出', dataIndex: '_7', align: 'center', render: (_, r) => { return this.avgFormater(r.totalIn, r.totalCount) }, ...genColumnTooltip('模拟收入 / 抽取道具人数') },
    { title: '总支出', dataIndex: '_6', render: (v, r) => prizeOutFormater(r, true), ...genColumnTooltip('礼物支出+装扮碎片流水') }, 
    { title: '粗发放占比', dataIndex: '_2', render: (v, r) => { return outRateFormaterV2(r) }, ...genColumnTooltip('礼物支出/模拟收入') },
    { title: '发放占比', dataIndex: '_3', render: (v, r) => outRateFormater(r), ...genColumnTooltip('总支出/模拟收入') },
    { title: '装扮碎片流水', dataIndex: 'frags', render: v => numberFormater(v) },
    { title: '抽取道具人数', dataIndex: 'totalCount' },
    { title: '龙宫支出', dataIndex: 'dragonOut', render: v => numberFormater(v), ...genColumnTooltip('龙宫支出') },  
    { title: '龙宫开启次数', dataIndex: 'dragonTimes', render: v => v || 0 },
    { title: '补给箱支出', dataIndex: 'compensateOut', ...genColumnTooltip('补给箱支出'), render: v => v || 0 }, 
    { title: '粗偏移', dataIndex: 'normalOffset', render: v => numberFormater(v), ...genColumnTooltip('模拟收入-礼物支出') },
    { title: '总偏移', dataIndex: '_4', render: (v, r) => totalOffsetFormater(r), ...genColumnTooltip('模拟收入-礼物支出-装扮碎片流水') }
  ].map(item => {
    item.align = 'center'
    return item
  })

  // 抢物资用的表头
  columnsGood = this.columnsDrop.filter(item => { return !['extraCount', 'extraOut'].includes(item.dataIndex) })

  // 汇总用的表头
  columnsSum = [
    { title: '日期', dataIndex: 'id' },
    // { title: '渠道', dataIndex: 'pid', render: text => text > 0 && text !== 7000 ? statTypeOptions.find(v => v.value === text).label : '汇总' },
    { title: '模拟收入', dataIndex: 'userOut', render: v => numberFormater(v) },
    { title: '人均支出', dataIndex: '_4', align: 'center', render: (_, r) => { return this.avgFormater(r.userOut, r.totalCount) }, ...genColumnTooltip('模拟收入 / 抽取道具人数') },
    { title: '总支出', dataIndex: '_1', render: (v, r) => totalOutFormater(r), ...genColumnTooltip('礼物支出+装扮碎片流水') },
    { title: '礼物支出', dataIndex: 'totalOut', render: (v) => { return numberFormater(v) } },
    { title: '粗发放占比', dataIndex: '_2', render: (v, r) => { return outRateFormaterV2(r) }, ...genColumnTooltip('礼物支出/模拟收入') },
    { title: '发放占比', dataIndex: '_totalIn', render: (v, r) => outRateFormater(r), ...genColumnTooltip('总支出/模拟收入') },
    { title: '装扮碎片流水', dataIndex: 'frags', render: v => numberFormater(v) },
    { title: '抽取道具人数', dataIndex: 'totalCount', render: v => numberFormater(v) },
    { title: '粗偏移', dataIndex: 'normalOffset', render: v => numberFormater(v), ...genColumnTooltip('模拟收入-礼物支出') },
    { title: '总偏移', dataIndex: '_3', render: (v, r) => totalOffsetFormater(r), ...genColumnTooltip('模拟收入-礼物支出-装扮碎片流水') }
  ].map(item => {
    item.align = 'center'
    return item
  })

  // 物资大战用的表头
  columnBoxwar = [
    { title: '日期', dataIndex: 'id' },
    // { title: '渠道', dataIndex: 'pid', render: text => text > 0 && text !== 7000 ? statTypeOptions.find(v => v.value === text).label : '汇总' },
    { title: '模拟收入', dataIndex: 'userOut', render: v => numberFormater(v) },
    { title: '普通物资支出', dataIndex: 'normalOut', render: v => numberFormater(v) },
    { title: '组合大礼支出', dataIndex: 'bundleOut', render: v => numberFormater(v) },
    { title: '人均支出', dataIndex: '_5', align: 'center', render: (_, r) => { return this.avgFormater(r.userOut, r.normalCount) }, ...genColumnTooltip('模拟收入 / 抽取道具人数') },
    { title: '总支出', dataIndex: '_1', render: (v, r) => totalOutFormater(r) },
    { title: '礼物支出', dataIndex: 'totalOut', render: (v) => { return numberFormater(v) }, ...genColumnTooltip('普通礼物支出+额外礼物支出') },
    { title: '粗发放占比', dataIndex: '_2', render: (v, r) => { return outRateFormaterV2(r) }, ...genColumnTooltip('礼物支出/模拟收入') },
    { title: '发放占比', dataIndex: '_3', render: (v, r) => outRateFormater(r), ...genColumnTooltip('总支出/模拟收入') },
    { title: '装扮碎片流水', dataIndex: 'frags', render: v => numberFormater(v) },
    { title: '抽取道具人数', dataIndex: 'normalCount', render: v => numberFormater(v) },
    { title: '粗偏移', dataIndex: 'normalOffset', render: v => numberFormater(v), ...genColumnTooltip('模拟收入-礼物支出') },
    { title: '总偏移', dataIndex: '_4', render: (v, r) => totalOffsetFormater(r), ...genColumnTooltip('模拟收入-礼物支出-装扮碎片流水') }
  ].map(item => {
    item.align = 'center'
    return item
  })

  // 查询数据
  onFinish = values => {
    let begin = values.range[0].format('YYYYMMDD')
    let end = values.range[1].format('YYYYMMDD')
    const dropType = parseInt(values.dropType)
    const statType = parseInt(values.statType)
    let params = { appID: 2, dropType: dropType, statType: statType, begin: begin, end: end }
    this.callModel('getDailySummary', { params })
  }

  genExportItem = (column, item) => {
    let result = {}
    column.forEach(v => {
      const { title, dataIndex, render } = v
      result[title] = render ? render(item[dataIndex], item) : item[dataIndex]
    })
    return result
  }

  getColumnByPropsType = (dropType) => {
    switch (dropType) {
      case 0:
        return this.columnsDrop
      case 1:
        return this.columnsGood
      // case 2:
      case 1000:
        return this.columnsSum
      case 3:
        return this.columnBoxwar
    }
    return []
  }

  onDropTypeChange = (v) => {
    this.setState({ dropType: v })
    this.formRef.submit()
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { dailysummary = [] } } = this.props
    const { dropType } = this.state
    return (
      <Card>
        <Form ref={form => { this.formRef = form }} onFinish={this.onFinish}>
          <Row gutter={6}>
            <Col>
              <Tooltip title='时间范围'>
                <Form.Item name='range'>
                  <DatePicker.RangePicker />
                </Form.Item>
              </Tooltip>
            </Col>
            <Col>
              <Tooltip title='玩法'>
                <Form.Item name='dropType' >
                  <Select defaultValue={0} options={dropTypeOptions} style={{ width: '11em' }} onChange={(v) => this.onDropTypeChange(v)} />
                </Form.Item>
              </Tooltip>
            </Col>
            <Col>
              <Tooltip title='渠道'>
                <Form.Item name='statType' >
                  <Select defaultValue={0} options={statTypeOptions} style={{ width: '10em' }} />
                </Form.Item>
              </Tooltip>
            </Col>
            <Col>
              <Button htmlType='submit' type='primary'>查询</Button>
            </Col>
            <Col>
              <Button onClick={() => { onExportExcel(this.getColumnByPropsType(dropType), dailysummary, '抢空投-空投日报-交友日报.xlsx') }} >导出</Button>
            </Col>
          </Row>
        </Form>

        <GenTip />
        <Table
          columns={this.getColumnByPropsType(dropType)}
          scroll={{ x: 'max-content' }}
          rowKey={(record, index) => index}
          bordered
          dataSource={dailysummary}
        />
      </Card>
    )
  }
}

export default DropReportSummaryComponent
