import React, { Component } from 'react'
import { connect } from 'dva'
import { tableStyle, Inputlayout } from '@/utils/common'
import { Tabs, Card, Input, Form, Row, Col, Button, Table, Popconfirm, Alert } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'

const namespace = 'messageWhiteList'

@connect(({ messageWhiteList }) => ({
  model: messageWhiteList
}))

class MessageWhiteList extends Component {
  constructor (props) {
    super(props)
    this.refreshVidioComperList()
  }

  // 标签页发生切换
  onTagChange = (record) => {
    if (record === '1') { // 切换到'添加标签页'
      // todo: 清空输入框
    }
    if (record === '2') { // 切换到'消息中心白名单标签页'
      this.refreshVidioComperList()
    }
  }

  // 获取/刷新消息中心白名单列表数据
  refreshVidioComperList = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getWhiteListData`,
      payload: null
    })
  }

  // 点击 批量添加标签页-添加按钮
  onAddBtnClick = () => {
    const { uidList } = this.state
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/addWhiteListByList`,
      payload: { uidList: uidList }
    })
  }
  // 点击 批量删除标签页-删除按钮
  onDelBtnClick = () => {
    const { uidList } = this.state
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/delWhiteListByList`,
      payload: { uidList: uidList }
    })
  }
  // '批量添加'标签页html代码
  addBlackLIstHtml = () => {
    const { TextArea } = Input
    const { updating } = this.props.model
    return (
      <div>
        <Row>
          <Col span='24'>
            <Form {...Inputlayout} name='input-sid'>
              <Alert message='批量添加提示：格式为uid, 每行一个' type='info' closable />
              <TextArea rows={6} onChange={e => this.setState({ uidList: e.target.value })} />
              <Form.Item>
                <Button type='primary' htmlType='submit' loading={updating} onClick={() => this.onAddBtnClick()}>
                  添加
                </Button>
              </Form.Item>
            </Form>
          </Col>
        </Row>
      </div>
    )
  }

  // 确认删除选中的白名单
  onComfirmDel = (uid) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/delWhiteListByUids`,
      payload: uid
    })
  }

  // 消息中心白名单-删除操作html代码
  deleteWhiteListHtml = (record) => {
    const { uid } = record
    let tmpStr = `确定要将 [uid=${uid}] 从消息中心白名单删除吗？`
    return (
      <Popconfirm placement='bottom' title={tmpStr}
        okType='danger' okText='删除' cancelText='取消' onConfirm={() => this.onComfirmDel(uid)}>
        <a href='#'><DeleteOutlined style={{ color: '#ff3535', fontSize: '1.2em' }} /></a>
      </Popconfirm>
    )
  }

  // ‘白名单展示列表标签页’html代码
  displayWhiteListHtml = () => {
    const columns = [
      { title: '#', dataIndex: 'idx' },
      { title: 'uid', dataIndex: 'uid' },
      { title: '操作', render: (record) => this.deleteWhiteListHtml(record) }
    ]
    const { displayData } = this.props.model
    return (
      <Row >
        <Col span={24}>
          <Table columns={columns} dataSource={displayData} size='small' pagination={tableStyle} />
        </Col>
      </Row>
    )
  }

  // ‘批量删除标签页’ html 代码
  bathDeleteHtml = () => {
    const { TextArea } = Input
    const { updating } = this.props.model
    return (
      <div>
        <Row>
          <Col span='24'>
            <Form {...Inputlayout} name='input-sid'>
              <Alert message='批量删除提示：格式为uid,一行一组' type='info' closable />
              <TextArea rows={6} onChange={e => this.setState({ uidList: e.target.value })} />
              <Form.Item>
                <Button type='primary' htmlType='submit' loading={updating} onClick={() => this.onDelBtnClick()}>
                  删除
                </Button>
              </Form.Item>
            </Form>
          </Col>
        </Row>
      </div>
    )
  }

  render () {
    const { TabPane } = Tabs
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='2' onChange={(record) => this.onTagChange(record)} type='card' size='large'>
            <TabPane tab='批量添加' key='1'>
              {this.addBlackLIstHtml()}
            </TabPane>
            <TabPane tab='特定消息推送名单' key='2'>
              {this.displayWhiteListHtml()}
            </TabPane>
            <TabPane tab='批量删除' key='3'>
              {this.bathDeleteHtml()}
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default MessageWhiteList
