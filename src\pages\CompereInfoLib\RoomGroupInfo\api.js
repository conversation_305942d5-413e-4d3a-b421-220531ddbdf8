import request from '@/utils/request'
import { stringify } from 'qs'

// 获取列表
export function listRoomGroup (params) {
  return request(`/new_compere_info/list_room_group_info_lib?${stringify(params)}`)
}

// 手工出库
export function removeRoomGroup (params) {
  return request(`/new_compere_info/remove_room_group_info_lib?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 手工导入
export function importRoomGroup (params) {
  return request(`/new_compere_info/import_room_group_info_lib?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}
