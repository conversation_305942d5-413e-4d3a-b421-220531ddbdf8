import * as api from './api'

export default {
  namespace: 'helpGroupReport',

  state: {
    listReport: []
  },

  reducers: {
    displayReport (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        listReport: payload
      }
    }
  },

  effects: {
    * listStatistics ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listStatistics, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      for (let i = 0; i < data.length; i++) {
        data[i].idx = i + 1
      }
      yield put({
        type: 'displayReport',
        payload: data
      })
    }
  }
}
