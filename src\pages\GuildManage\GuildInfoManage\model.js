import * as api from './api'
import { message } from 'antd'

export default {
  namespace: 'guildInfoManage',

  state: {
    list: [],
    userInfo: {},
    privateUrlList: []
  },

  reducers: {
    displayList (state, { payload: data }) {
      if (!Array.isArray(data)) {
        console.error('unexpect data type: data=', data)
        return
      }
      return {
        ...state,
        list: data
      }
    },

    userInfo (state, { payload: data }) {
      return {
        ...state,
        userInfo: data
      }
    },

    updatePrivateUrlList (state, { payload: data }) {
      return {
        ...state,
        privateUrlList: data
      }
    },

    clearPrivateUrlList (state) {
      return {
        ...state,
        privateUrlList: []
      }
    }
  },

  effects: {
    * getGuildInfoList ({ payload }, { call, put }) {
      let { data: { data, status } } = yield call(api.queryGuildInfoList, payload)
      if (status !== 0) {
        message.error({ content: '获取公会信息失败，请检查控制台' })
        return
      }
      data = Array.isArray(data) ? data : []
      if (data !== []) {
        for (let i = 0; i < data.length; i++) {
          data[i].idx = i + 1

          if (data[i].fileUrlList === null) {
            data[i].file1 = ''
            data[i].file2 = ''
          } else {
            if (data[i].fileUrlList.length === 1) {
              data[i].file1 = data[i].fileUrlList[0].url.replace('http://', 'https://')
              data[i].file2 = ''
              data[i].file1Name = data[i].fileUrlList[0].name
              data[i].file2Name = ''
            } else if (data[i].fileUrlList.length === 2) {
              data[i].file1 = data[i].fileUrlList[0].url.replace('http://', 'https://')
              data[i].file2 = data[i].fileUrlList[1].url.replace('http://', 'https://')
              data[i].file1Name = data[i].fileUrlList[0].name
              data[i].file2Name = data[i].fileUrlList[1].name
            }
          }

          if (data[i].imageUrlList === null) {
            data[i].image1 = ''
            data[i].image2 = ''
            data[i].image3 = ''
            data[i].image4 = ''
            data[i].image5 = ''
          } else {
            if (data[i].imageUrlList.length === 1) {
              data[i].image1 = data[i].imageUrlList[0].url.replace('http://', 'https://')
              data[i].image2 = ''
              data[i].image3 = ''
              data[i].image4 = ''
              data[i].image5 = ''
              data[i].image1Name = data[i].imageUrlList[0].name
              data[i].image2Name = ''
              data[i].image3Name = ''
              data[i].image4Name = ''
              data[i].image5Name = ''
            } else if (data[i].imageUrlList.length === 2) {
              data[i].image1 = data[i].imageUrlList[0].url.replace('http://', 'https://')
              data[i].image2 = data[i].imageUrlList[1].url.replace('http://', 'https://')
              data[i].image3 = ''
              data[i].image4 = ''
              data[i].image5 = ''
              data[i].image1Name = data[i].imageUrlList[0].name
              data[i].image2Name = data[i].imageUrlList[1].name
              data[i].image3Name = ''
              data[i].image4Name = ''
              data[i].image5Name = ''
            } else if (data[i].imageUrlList.length === 3) {
              data[i].image1 = data[i].imageUrlList[0].url.replace('http://', 'https://')
              data[i].image2 = data[i].imageUrlList[1].url.replace('http://', 'https://')
              data[i].image3 = data[i].imageUrlList[2].url.replace('http://', 'https://')
              data[i].image4 = ''
              data[i].image5 = ''
              data[i].image1Name = data[i].imageUrlList[0].name
              data[i].image2Name = data[i].imageUrlList[1].name
              data[i].image3Name = data[i].imageUrlList[2].name
              data[i].image4Name = ''
              data[i].image5Name = ''
            } else if (data[i].imageUrlList.length === 4) {
              data[i].image1 = data[i].imageUrlList[0].url.replace('http://', 'https://')
              data[i].image2 = data[i].imageUrlList[1].url.replace('http://', 'https://')
              data[i].image3 = data[i].imageUrlList[2].url.replace('http://', 'https://')
              data[i].image4 = data[i].imageUrlList[3].url.replace('http://', 'https://')
              data[i].image5 = ''
              data[i].image1Name = data[i].imageUrlList[0].name
              data[i].image2Name = data[i].imageUrlList[1].name
              data[i].image3Name = data[i].imageUrlList[2].name
              data[i].image4Name = data[i].imageUrlList[3].name
              data[i].image5Name = ''
            } else if (data[i].imageUrlList.length === 5) {
              data[i].image1 = data[i].imageUrlList[0].url.replace('http://', 'https://')
              data[i].image2 = data[i].imageUrlList[1].url.replace('http://', 'https://')
              data[i].image3 = data[i].imageUrlList[2].url.replace('http://', 'https://')
              data[i].image4 = data[i].imageUrlList[3].url.replace('http://', 'https://')
              data[i].image5 = data[i].imageUrlList[4].url.replace('http://', 'https://')
              data[i].image1Name = data[i].imageUrlList[0].name
              data[i].image2Name = data[i].imageUrlList[1].name
              data[i].image3Name = data[i].imageUrlList[2].name
              data[i].image4Name = data[i].imageUrlList[3].name
              data[i].image5Name = data[i].imageUrlList[4].name
            }
          }
        }
      }
      yield put({
        type: 'displayList',
        payload: data
      })
    },

    * addContractItem ({ payload }, { call, put }) {
      const { data: { status } } = yield call(api.addContract, payload)
      if (status === 0) {
        message.success('addContractItem success')
        yield put({
          type: 'getGuildInfoList'
        })
      } else {
        message.error('addContractItem failed')
      }
    },

    * updateContractItem ({ payload }, { call, put }) {
      const { data: { status } } = yield call(api.updateContract, payload)
      if (status === 0) {
        message.success('updateContractItem success')
        yield put({
          type: 'getGuildInfoList'
        })
      } else {
        message.error('updateContractItem failed')
      }
    },

    * approveContractItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(api.approveContract, payload)
      if (status === 0) {
        yield put({
          type: 'getGuildInfoList'
        })
      } else {
        message.error(msg)
      }
    },

    * getGuildContractList ({ payload }, { call, put }) {
      let { data: { data, status } } = yield call(api.queryGuildContractList, payload)
      if (status !== 0) {
        message.error({ content: '获取签约记录失败，请检查控制台' })
        return
      }
      data = Array.isArray(data) ? data : []
      if (data !== []) {
        for (let i = 0; i < data.length; i++) {
          data[i].idx = i + 1
        }
      }
      yield put({
        type: 'displayList',
        payload: data
      })
    },

    * approveOneStartItem ({ payload }, { call, put }) {
      const { data: { status } } = yield call(api.approveOneStart, payload)
      if (status !== 0) {
        message.error('无权限')
      }
    },

    * importGuildInfoManageButton ({ payload }, { call, put }) {
      const { data: { status } } = yield call(api.importGuildInfoManage, payload)
      if (status !== 0) {
        message.error('无导入权限')
      } else {
        message.success('导入成功')
      }
    },

    * getUserApproveInfo ({ payload }, { call, put }) {
      let { data: { data, status } } = yield call(api.getUserApproveInfo, payload)
      if (status !== 0) {
        message.success('getUserApproveInfo failed')
      } else {
        yield put({
          type: 'userInfo',
          payload: data
        })
      }
    },

    * getPrivateFileTokenItem ({ payload }, { call, put }) {
      const { data: { data, status } } = yield call(api.getPrivateFileToken, payload)
      if (status !== 0) {
        console.log('error: ' + status)
      } else {
        yield put({
          type: 'updatePrivateUrlList',
          payload: data.url
        })
      }
    }
  }
}
