import request from '@/utils/request'
import { stringify } from 'qs'

export function getHatkingCrucialInfo (params) { // 关键信息
  return request(`/amethyst_lottery/lottery_hatking_crucial_info?${stringify(params)}`, { jsonp: true })
}

export function getHatkingCrucialInfoNew (params) { // 关键信息
  return request(`/lottery_hatking/lottery_hatking_crucial_info?${stringify(params)}`, { jsonp: true })
}

export function getHatkingCrucialInfoNew2 (params) { // 关键信息
  return request(`/aggregator/lottery_hatking_crucial_info?${stringify(params)}`, { jsonp: true })
}

export function getHatkingDailyInfo (params) { // 每日玩法信息
  return request(`/amethyst_lottery/lottery_hatking_daily_info?${stringify(params)}`, { jsonp: true })
}

export function getHatkingComboInfo (params) { // 连胜信息
  return request(`/amethyst_lottery/lottery_hatking_combo_info?${stringify(params)}`, { jsonp: true })
}

export function getHatkingModouInfo (params) { // 魔豆信息
  return request(`/amethyst_lottery/lottery_hatking_modou_info_new?${stringify(params)}`, { jsonp: true })
}

export function getHatkingModeStatsInfo (params) { // 总道具数额汇总发放道具监控
  return request(`/lottery_hatking/get_hatking_daily_mode_stats?${stringify(params)}`, { jsonp: true })
}

export function getHatkingModeStatsInfoNew (params) { // 总道具数额汇总发放道具监控
  return request(`/lottery_hatking/get_hatking_daily_mode_stats_new?${stringify(params)}`, { jsonp: true })
}

export function getHatkingModeStatsInfoNew2 (params) { // 总道具数额汇总发放道具监控
  return request(`/aggregator/get_hatking_daily_mode_stats_new?${stringify(params)}`, { jsonp: true })
}

export function getHatkingUpgradeCrucialInfo (params) { // 升级场发放道具监控
  return request(`/lottery_hatking/get_hatking_upgrade_daily_crucial_info?${stringify(params)}`, { jsonp: true })
}

export function getHatkingPaidRangeInfo (params) { // 参与用户区间分布
  return request(`/aggregator/get_hatking_paid_range_info?${stringify(params)}`, { jsonp: true })
}

export function getHatkingPeriodRangeInfo (params) { // 分时段监控
  return request(`/aggregator/get_hatking_period_range_info?${stringify(params)}`, { jsonp: true })
}

export function getHatkingRoundStatInfo (params) { // 每一轮的数据统计
  return request(`/aggregator/get_hatking_round_stat_info?${stringify(params)}`, { jsonp: true })
}
