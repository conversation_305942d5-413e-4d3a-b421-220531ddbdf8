import React, { Component } from 'react'
import { CSVLink } from 'react-csv'
import { Button } from 'antd'

class CSVExport extends Component {
  constructor (props) {
    super(props)

    this.state = {}
  }

  onExport = () => {
    let headers = {}
    const { columns, content } = this.props
    columns.forEach(function (item) {
      headers[item.dataIndex] = item.title
    })

    let list = []
    content.forEach(item => {
      let obj = {}

      for (let k in item) {
        if (headers[k] !== undefined) {
          obj[headers[k]] = item[k]
        }
      }

      list.push(obj)
    })

    return list
  }

  render () {
    return (
      <CSVLink data={this.onExport()} target='_blank'><Button type='primary' style={{ marginLeft: 5 }}>导出</Button></CSVLink>
    )
  }
}

export default CSVExport
