import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Row, Col, Button, Table, Modal, Form, Select, message, Input } from 'antd'
import { UserAddOutlined } from '@ant-design/icons'
import { timeFormater } from '@/utils/common'
import TextArea from 'antd/lib/input/TextArea'

const namespace = 'esignWhiteListCfg'

@connect(({ esignWhiteListCfg }) => ({
  model: esignWhiteListCfg
}))

class ESignWhiteListCfg extends Component {
  state = {
    modalVisable: false
  }
  componentDidMount = () => {
    this.getWhitelistRequire()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 提交新增白名单请求
  submitAddWhitelistRequire = (r) => {
    console.debug('whitelist=', r)
    this.callModel('addWhitelistReq', {
      params: r,
      cbFunc: (ok) => {
        if (ok) {
          message.success('添加白名单申请已发出，正在等待审核~')
          this.getWhitelistRequire()
        } else {
          message.warn('新增白名单失败，请稍后再试')
        }
        this.setState({ modalVisable: false })
      }
    })
  }
  // 请求白名单列表
  getWhitelistRequire = (r) => {
    this.callModel('getWhitelistReq')
  }

  render () {
    const { route } = this.props
    const { whiteList } = this.props.model
    const { modalVisable } = this.state

    const columns = [
      { title: 'uid', dataIndex: 'uid', sorter: (a, b) => a.uid - b.uid },
      { title: 'yy号', dataIndex: 'yyNo', sorter: (a, b) => a.yyNo - b.yyNo },
      { title: '昵称', dataIndex: 'nick', sorter: (a, b) => a.nick > b.nick },
      { title: '申请人', dataIndex: 'proposerNick' },
      { title: '审批人', dataIndex: 'approvalNick' },
      { title: '备注信息', dataIndex: 'remark' },
      { title: '状态', dataIndex: 'status', render: (v) => { return [v + '?', '审批通过', '审批中', '审批拒绝'][v == null ? 0 : v + 1] } },
      { title: '审批时间', dataIndex: 'applyTime', render: (v) => { return v > 0 ? timeFormater(v) : '未审批' } },
      { title: '申请时间', dataIndex: 'timestamp', render: (v) => timeFormater(v), sorter: (a, b) => a.timestamp - b.timestamp }
      // { title: '操作', render:(v, r)=>{return <Popconfirm title='确认删除么?' okText='确认' cancelText='否' />}}
    ]
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Row style={{ marginBottom: '1em' }}>
            <Button type='primary' onClick={() => { this.setState({ modalVisable: true }); setTimeout(() => { this.formRef.resetFields() }, 100) }} >
              <UserAddOutlined />申请添加白名单
            </Button>
          </Row>
          <Row>
            <Col span={24}>
              <Table columns={columns} dataSource={whiteList} size='small' scroll={{ x: 'max-content' }} pagination={{ defaultPageSize: 50 }} />
            </Col>
          </Row>
        </Card>
        <Modal visible={modalVisable}
          title='新增厅管签约资格白名单' okText='提交申请' cancelText='取消'
          onCancel={() => { this.setState({ modalVisable: false }) }}
          onOk={() => { this.formRef.submit() }}>
          <Form ref={(v) => { this.formRef = v }}
            onFinish={(v) => { this.submitAddWhitelistRequire(v) }}
            labelCol={{ span: 4, align: 'left' }}
            initialValues={{ uidList: '', appId: 2, remark: '' }}>
            <Form.Item label='业务类型' name='appId' rules={[{ required: true, message: '业务类型不能为空' }]}>
              <Select>
                <Select.Option key={2} value={2}>交友</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item label='uid列表' name='uidList' rules={[{ required: true, message: 'uid列表不能为空' }, { pattern: /^[0-9,]+$/, message: 'uid列表格式不正确~' }]}>
              <TextArea placeholder='请输入白名单uid,需添加多个时使用逗号(,)隔开每个uid~' />
            </Form.Item>
            <Form.Item label='备注' name='remark' rules={[{ required: true, message: '请填写备注~' }]}>
              <Input />
            </Form.Item>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default ESignWhiteListCfg
