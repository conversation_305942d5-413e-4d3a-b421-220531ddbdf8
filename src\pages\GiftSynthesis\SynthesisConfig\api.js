import request from '@/utils/request'
import { stringify } from 'qs'

export function getConfigList () {
  return request('/gift_synthesis/boss/config_list')
}

export function removeConfig (params) {
  return request(`/gift_synthesis/boss/config_del?${stringify(params)}`)
}

export function upsetItem (params) {
  return request(`/gift_synthesis/boss/config_add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function auditUpdate (params) {
  return request(`/gift_synthesis/boss/audit_update?${stringify(params)}`)
}

export function getAuditList () {
  return request('/gift_synthesis/boss/audit_list')
}

export function getHistoryList () {
  return request('/gift_synthesis/boss/audit_history')
}
