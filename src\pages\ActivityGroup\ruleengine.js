const returnRegex = /[\s]*{?[\s]*(.*)[\s]*}?[\s]*$/i
const commentRegex = /\/\/.*/

// 解析规则
export const parseRule = function (source) {
  if (!source) {
    throw new Error('规则源码不能为空')
  }
  // 去掉注释语句
  source = source.replace(commentRegex, '')
  source = source.trim()
  if (source[0] !== '{') {
    source = '{' + source + '}'
  }
  let topNode = { expressionIndex: 0, rightBracketIndex: source.length - 1 }
  // 括号匹配栈
  let stack = []
  // 遍历每一个元素，然后进行括号匹配
  for (let index = 0; index < source.length; index++) {
    let r = source[index]
    if (r === '{') { // 左括号，入栈
      let leftIndex = index
      if (stack.length > 0) {
        let parent = stack[stack.length - 1]
        if (parent.children && parent.children.length > 0) {
          leftIndex = parent.children[parent.children.length - 1].rightBracketIndex // 父级之前已有子节点，那么应个读取最后一个子节点
        } else {
          leftIndex = parent.leftBracketIndex
        }
      }
      let curNode = { leftBracketIndex: index, expressionIndex: leftIndex }
      if (index > leftIndex) {
        curNode.expression = source.substring(leftIndex + 1, index).trim()
      }
      // 取出父级，然后设置子级
      let parent = topNode
      if (stack.length > 0) {
        parent = stack[stack.length - 1]
      }
      if (!parent.children) {
        parent.children = []
      }
      parent.children.push(curNode)
      // 入栈
      stack.push(curNode)
    }
    if (r === '}') { // 右括号
      // 判断当前栈是否为空，为空的话，说明括号不匹配，需要返回错误
      if (stack.length === 0) {
        throw new Error('第' + index + '个结尾括号 "}" 没有匹配的 "{"')
      }
      let curNode = stack[stack.length - 1]
      curNode.rightBracketIndex = index

      if (!curNode.children || curNode.children === 0) {
        // 最后一个节点
        curNode.expression = source.substring(curNode.leftBracketIndex + 1, curNode.rightBracketIndex).trim()
        // 计算out 值
        if (returnRegex.test(curNode.expression)) {
          curNode.expression = curNode.expression.replace(returnRegex, '$1')
        }
      }

      // 栈不为空，说明有左括号，弹出栈顶元素
      stack = stack.splice(0, stack.length - 1)
    }
  }
  if (stack.length > 0) {
    throw new Error('"{}" 括号不匹配，规则格式错误！')
  }
  // 转成规则
  return nodeToRule(topNode)
}

// 将节点装成规则对象
function nodeToRule (b, key) {
  key = key || '0'
  let topBracket = b
  if (!b.expression && b.children && b.children === 1) {
    topBracket = b.children[0]
  }
  let expression = topBracket.expression
  let rule = { expression: expression, key: key, children: [] }
  if (topBracket.children && topBracket.children.length > 0) {
    topBracket.children.forEach((child, index) => {
      rule.children.push(nodeToRule(child, key + '-' + index))
    })
  }
  // 如果当前规则表达式为空，并且只有一个子节点，那么使用子节点
  while (!rule.expression && rule.children && rule.children.length === 1) {
    rule = rule.children[0]
  }
  return rule
}

// 创建词法解析器流对象
function LexerStream (source) {
  this.source = source
  this.length = source.length
  this.position = 0
  return this
}

// 是否可读
LexerStream.prototype.canRead = function () {
  return this.position < this.length
}

// 获取当前读取位置
LexerStream.prototype.getPosition = function () {
  return this.position
}

// 重置position
LexerStream.prototype.resetPosition = function (position) {
  this.position = position
}

// 读取一个字符
LexerStream.prototype.readChar = function () {
  this.position++
  return this.source[this.position - 1]
}

// 游标往回走 step 步
LexerStream.prototype.rewind = function (step) {
  this.position -= step
  if (this.position < 0) {
    this.position = 0
  }
  return this
}

// 跳过符合条件的字符，
// @param matcher function(c) bool 如果返回true 表示跳过，遇到第一个不跳过的则立即返回
LexerStream.prototype.skip = function (matcher) {
  while (this.canRead() && matcher(this.readChar())) {
  }
  return this
}

// 从当前位置开始读取，直到返回 匹配
// includeLast 返回字符串中，是否包含最后匹配的字符
// matcher 接收一个字符，返回 true 则中断
LexerStream.prototype.readUntilMatch = function (includeLast, matcher) {
  let arr = []
  while (this.canRead()) {
    let c = this.readChar()
    let preChar = this.position - 2 < this.length ? this.source[this.position - 2] : ''
    if (matcher(c, preChar)) {
      if (includeLast) {
        arr.push(c)
      }
      return arr.join('')
    }
    arr.push(c)
  }
  return arr.join('')
}

// 从当前位置开始读取，直到不匹配就返回
// matcher 接收一个字符，返回 false 则中断
LexerStream.prototype.readUntilUnMatch = function (matcher) {
  let arr = []
  while (this.canRead()) {
    let c = this.readChar()
    if (!matcher(c)) {
      this.rewind(1)
      return arr.join('')
    }
    arr.push(c)
  }
  return arr.join('')
}

// 读取下一个相邻字符，如果匹配的话就返回，不匹配的话，回退一格
LexerStream.prototype.readNextIfMatch = function (matcher) {
  if (this.canRead()) {
    let c = this.readChar()
    if (matcher(c)) {
      return c
    }
    this.rewind(1)
    return ''
  }
  return ''
}

export const Kind = {
  REF: 'REF', // 引用类型，格式 $[xxx]
  PREFIX: 'PREFIX', // 前缀，如 -,!,~
  COMPARE: 'COMPARE', // 比较类型，值为：==, >, >=, <, <=
  LOGIC: 'LOGIC', // 逻辑操作，值为： &&, ||
  BOOL: 'BOOL', // 布尔类型, 值为： true， false
  STRING: 'STRING', // 字符串类型，值为字符串
  NUMBER: 'NUMBER', // 数值类型
  SEP: 'SEP', // 分隔符，值为：,
  IN: 'IN', // IN比较类型，值为：Token 数组
  CLAUSE: 'CLAUSE', // 子句，值为 (,),{,}
  COMMENT: 'COMMENT', // 注释， LINE 表示行注释
  VAR: 'VAR', // 变量，值为变量名称
  UNKNOWN: 'UNKNOWN',
  BLANK: 'BLANK'
}

// Token
function Token (kind, value) {
  this.kind = kind
  this.value = value
  return this
}

Token.prototype.ToString = function () {
  switch (this.kind) {
    case Kind.REF:
      return '$[' + this.value + ']'
    case Kind.PREFIX:
      return this.value
    case Kind.COMPARE:
      return this.value
    case Kind.LOGIC:
      return this.value
    case Kind.BOOL:
      return this.value
    case Kind.STRING:
      return '\'' + this.value + '\''
    case Kind.NUMBER:
      return this.value
    case Kind.SEP:
      return this.value
    case Kind.CLAUSE:
      return this.value
    case Kind.COMMENT:
      return this.value
    case Kind.VAR:
      return this.value
    case Kind.UNKNOWN:
      return this.value
    case Kind.BLANK:
      return this.value
    case Kind.IN:
      let arr = ['IN ']
      this.value.forEach((item) => {
        arr.push(item.ToString())
      })
      return arr.join('')
    default:
      break
  }
  return this.kind
}

function TokenParser (hit, parser) {
  this.hit = hit
  this.parser = parser
  return this
}

TokenParser.prototype.support = function (c) {
  if (this.hit === c) {
    return true
  }
  if (typeof this.hit === 'function') {
    return this.hit(c)
  }
  if (Array.isArray(this.hit)) {
    for (let i = 0; i <= this.hit.length; i++) {
      if (this.hit[i] === c) {
        return true
      }
    }
  }
  return false
}

// 解析，然后返回 token 对象
TokenParser.prototype.parse = function (c, stream) {
  return this.parser(c, stream)
}

function refTokenParser (c, stream) {
  let s = stream.readUntilMatch(false, (c) => c === ']')
  s = s.trim()
  s = s.substring(1)
  return new Token(Kind.REF, s.trim())
}

function commentTokenParser (c, stream) {
  if (stream.canRead()) {
    let cc = stream.readChar()
    if (cc === '/') {
      let token = new Token(Kind.COMMENT, '// ' + stream.readUntilMatch(false, (c) => c === '\n').trim())
      // stream.rewind(1)
      return token
    }
  }
}

export function isNumberChar (c) {
  return c >= '0' && c <= '9'
}

export function isLetter (c) {
  return c >= 'A' && c <= 'z'
}

// 是否是可以作为变量名称的字符
export function isVarNameChar (c) {
  return c === '_' || isNumberChar(c) || isLetter(c)
}

export function isNumberString (s) {
  return s && /(^[1-9][0-9]*$)|(&^[1-9][0-9]*\.[0-9]*$)|(^\.[0-9]*$)/.test(s)
}

let mixTokenParser = new TokenParser(
  (c) => { // 匹配规则，数字、字母、下划线、. 时候适合这个解析器
    return c === '_' || isNumberChar(c) || isVarNameChar(c)
  },
  (c, stream) => {
    let s = c + stream.readUntilUnMatch(c => {
      return c === '.' || c === '_' || isNumberChar(c) || isVarNameChar(c)
    })
    s = s.trim()
    // 判断是不是bool
    let lower = s.toLowerCase()
    if (lower === 'true' || lower === 'false') {
      return new Token(Kind.BOOL, lower === 'true')
    }
    // 判断是不是IN语句
    if (lower === 'in') {
      return new Token(Kind.IN, 'IN')
    }
    // 判断是不是数字
    if (isNumberString(s)) {
      if (s.indexOf('.') >= 0) {
        return new Token(Kind.NUMBER, parseFloat(s))
      }
      return new Token(Kind.NUMBER, parseInt(s))
    }
    // 剩下就当成变量
    return new Token(Kind.VAR, s)
  }
)

let tokenParsers = [
  // 空白字符
  new TokenParser([' ', '\t', '\r', '\n'], (c, stream) => new Token(Kind.BLANK, c)),

  // 引用类型 $[xx]， 继续向后读取，直到遇到 ] 截止
  new TokenParser('$', (c, stream) => refTokenParser(c, stream)),

  // 行注释
  new TokenParser('/', (c, stream) => commentTokenParser(c, stream)),

  // ==, >, >=, <, <=
  new TokenParser(['=', '<', '>'], (c, stream) => new Token(Kind.COMPARE, c + stream.readNextIfMatch((c) => c === '='))),

  // 逻辑 && 与
  new TokenParser('&', (c, stream) => new Token(Kind.LOGIC, c + stream.readNextIfMatch((c) => c === '&'))),

  // 逻辑 || 或
  new TokenParser('|', (c, stream) => new Token(Kind.LOGIC, c + stream.readNextIfMatch((c) => c === '|'))),

  // 括号
  new TokenParser(['(', ')', '{', '}'], (c, stream) => new Token(Kind.CLAUSE, c)),

  // 字符串，以单双引号开头
  new TokenParser(['\'', '"'], (c, stream) => new Token(Kind.STRING, stream.readUntilMatch(false, (c1, pc) => c1 === c && pc !== '\\'))),

  // 变量，数字，bool 解析
  mixTokenParser,

  // 逗号分隔符
  new TokenParser(',', (c, stream) => new Token(Kind.SEP, c)),

  // 负号
  new TokenParser(['-', '!', '+', '*', '/', '^', '%'], (c, stream) => new Token(Kind.PREFIX, c))

]

// 解析规则表达式为 Token 数组
// 如果格式不正确的话，抛出异常
export function parseRuleSourceTokens (source, throwError) {
  if (!source || source.length < 1) {
    throw new Error('source is required')
  }
  let tokenList = []
  let stream = new LexerStream(source)
  throwError = throwError === undefined || throwError === null || throwError

  while (stream.canRead()) {
    let c = stream.readChar()
    let position = stream.getPosition()
    let token = null
    for (let i = 0; i < tokenParsers.length; i++) {
      let parser = tokenParsers[i]
      if (parser.support(c)) {
        token = parser.parse(c, stream)
        if (!token) { // 重置位置
          stream.resetPosition(position)
        } else {
          break
        }
      }
    }
    if (token) {
      tokenList.push(token)
    } else {
      if (throwError && c.trim() !== '') {
        throw new Error('非法字符\'' + c + '\'')
      }
    }
  }
  return tokenList
}

let clauseMap = {
  '(': ')', '{': '}', ')': '(', '}': '{'
}

// 获取规则token列表错误信息，如果有错误的话就返回字符串错误信息，没有的话返回空字符串
// 验证规则：
// 1. 括号配对， (), {}
// 2.
export function getRuleTokensError (tokenList, validator) {
  if (!tokenList || tokenList.length < 1) {
    return
  }
  let clauseStack = []
  let len = tokenList.length
  // 代码必须以 }, 或者注释行结尾
  let lastToken = tokenList[len - 1]
  console.log(tokenList)
  if ((lastToken.kind !== Kind.CLAUSE && lastToken.kind !== Kind.COMMENT) || (lastToken.kind === Kind.CLAUSE && lastToken.value !== '}')) {
    return '必须以 \'}\' 或者注释行结尾'
  }
  for (let i = 0; i < len; i++) {
    let token = tokenList[i]
    if (validator) {
      let msg = validator(token, i)
      if (msg) {
        return msg
      }
    }
    if (token.kind === Kind.CLAUSE) {
      if (token.value === '(' || token.value === '{') {
        clauseStack.push(token.value) // 入栈
      }
      if (token.value === ')' || token.value === '}') {
        let pre = clauseStack[clauseStack.length - 1] // 栈顶
        if (token.value !== clauseMap[pre]) {
          return '括号未配对，请仔细检查规则代码'
        }
        // 出栈
        clauseStack.splice(clauseStack.length - 1, 1)
      }
    }
    // REF 引用类型, 提取
  }
  if (clauseStack.length !== 0) {
    return '括号未配对，请仔细检查规则代码'
  }
}

// 如果是 IN，那么合并后面()内的元素，变成数组
export function mergeKindInToken (tokenList) {
  if (!tokenList || tokenList.length < 4) {
    return tokenList
  }
  let size = tokenList.length
  let newList = []
  for (let i = 0; i < size; i++) {
    let item = tokenList[i]
    if (item.kind === Kind.IN) {
      let arr = []
      for (let j = i + 1; j < size; j++) {
        i++
        let v = tokenList[j]
        arr.push(v)
        if (v.kind === Kind.RKH) {
          break
        }
      }
      item.value = arr
    }
    newList.push(item)
  }
  return newList
}
