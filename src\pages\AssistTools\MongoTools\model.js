import { message } from 'antd'
import { getMongoCollectionInfo, getMongoDocsPage, getQueryExplain, listMongoCollections } from '../common'
import { convertResToDataList, detectDataListTableColumns, newCommonReducers } from '@/utils/common'

export default {
  namespace: 'MongoTools',
  state: {
    collections: [], // 数据列表
    collectionInfo: {},
    queryExplain: {}, // 查询计划
    docList: [],
    docColumns: [],
    total: 0
  },

  reducers: newCommonReducers(),

  // 数据变更
  effects: {
    * listCollections ({ payload, callback }, { call, put }) {
      if (payload.clear) {
        yield put({
          type: 'updateList',
          payload: {
            ignoreIndex: true,
            name: 'collections',
            list: []
          }
        })

        if (callback) {
          callback()
        }
        return
      }

      const { data } = yield call(listMongoCollections, payload)
      let dataList = convertResToDataList(data)
      yield put({
        type: 'updateList',
        payload: {
          ignoreIndex: true,
          name: 'collections',
          list: dataList
        }
      })

      if (callback) {
        callback(dataList)
      }
    },

    * getCollectionInfo ({ payload, callback }, { call, put }) {
      const { data } = yield call(getMongoCollectionInfo, payload)

      let collectionInfo = data.data

      // 首行记录
      if (collectionInfo.firstRec) {
        collectionInfo.columns = detectDataListTableColumns([collectionInfo.firstRec], true)
      }

      yield put({
        type: 'updateState',
        payload: {
          collectionInfo: collectionInfo
        }
      })

      if (callback) {
        callback(collectionInfo)
      }
    },

    * getQueryExplain ({ payload, callback }, { call, put }) {
      const { data } = yield call(getQueryExplain, payload)

      let failed = data.data.code !== 0 && data.data.errmsg
      if (failed) {
        message.error(data.data.errmsg)
      }

      yield put({
        type: 'updateState',
        payload: {
          queryExplain: failed ? [] : data.data
        }
      })

      if (callback) {
        callback(data.data)
      }
    },

    * getMongoDocsPage ({ payload, callback }, { call, put }) {
      const { data } = yield call(getMongoDocsPage, payload)

      let failed = data.status !== 0 && data.msg
      if (failed) {
        message.error(data.msg)
      }
      let dataList = failed ? [] : convertResToDataList(data)
      let docColumns = detectDataListTableColumns(dataList)
      yield put({
        type: 'updateList',
        payload: {
          ignoreIndex: true,
          name: 'docList',
          list: dataList,
          state: {
            total: data.total || dataList.length,
            docColumns: docColumns
          }
        }
      })

      if (callback) {
        callback(dataList)
      }
    }
  }
}
