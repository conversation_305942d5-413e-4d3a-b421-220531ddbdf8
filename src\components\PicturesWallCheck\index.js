import React, { Component } from 'react'
import { Upload, Modal, Popover, message } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import PropTypes from 'prop-types'

class PicturesWallCheck extends Component {
  static propTypes = {
    minSize: PropTypes.number,
    maxSize: PropTypes.number,
    minWidth: PropTypes.number,
    minHeight: PropTypes.number,
    title: PropTypes.string
  }
  constructor (props) {
    super(props)
    console.log('props', props)
    this.state = {
      fileList: props.value ? [urlValue(props.value)] : [],
      previewImage: '',
      previewVisible: false
    }
  }

  componentWillReceiveProps (nextProps) {
    // 受控组件， 用于更新
    const { value } = nextProps
    console.log('componentWillReceiveProps', value)
    this.setState({
      fileList: value ? [urlValue(value)] : []
    })
  }

  handlePreview = file => {
    this.setState({
      previewImage: file.url || file.thumbUrl,
      previewVisible: true
    })
  }

  handleCancel = () => this.setState({ previewVisible: false })

  handleChange = ({ fileList }) => {
    if (fileList.length === 0) {
      this.props.onChange()
    }
    if (fileList.length > 0 && fileList[fileList.length - 1].status === 'done' && fileList[fileList.length - 1].response.status === 0) {
      var url = fileList[fileList.length - 1].response.urls[0]
      this.props.onChange(url) // update getFiledDecorator
    }
    this.setState({ fileList })
  }

  // 上传图片尺寸限制
  // checkImageWH = (file, width, height) => { // 参数分别是上传的file，想要限制的宽，想要限制的高
  //   return new Promise((resolve, reject) => {
  //     // eslint-disable-next-line no-undef
  //     let filereader = new FileReader()
  //     filereader.onload = e => {
  //       let src = e.target.result
  //       const image = new Image()
  //       image.onload = function () {
  //         if (this.width >= width && this.height >= height) { // 上传图片的宽高与传递过来的限制宽高作比较，超过限制则调用失败回调
  //           console.log('checkImageWH', this.width, this.height)
  //           reject(new Error('图片尺寸不符合要求'))
  //         } else {
  //           resolve()
  //         }
  //       }
  //       image.onerror = reject
  //       image.src = src
  //     }
  //     filereader.readAsDataURL(file)
  //   })
  // }
  // 检测尺寸
  isSize = (file, requireWidth, requireHeight) => {
    return new Promise((resolve, reject) => {
      // let width = this.props.width
      // let height = this.props.height
      // let width = 100
      let _URL = window.URL || window.webkitURL
      // eslint-disable-next-line no-undef
      let img = new Image()
      img.onload = () => {
        console.log('isSize onload', img.width, img.height, requireWidth, requireHeight)
        let valid = img.width >= requireWidth && img.height >= requireHeight
        valid ? resolve() : reject(new Error('图片尺寸' + img.width + '*' + img.height + '不符合要求，请修改后重新上传,要求 ' + requireWidth + '*' + requireHeight))
        if (!valid) {
          message.error(file.name + '图片尺寸' + img.width + '*' + img.height + '不符合要求，请修改后重新上传,要求 ' + requireWidth + '*' + requireHeight)
        }
      }
      img.src = _URL.createObjectURL(file)
    }).then(
      () => {
        return file
      },
      () => {
        // message.error('图片尺寸不符合要求，请修改后重新上传bbb！')
        // message.error(file.name + '图片尺寸不符合要求，请修改后重新上传,要求 ' + requireWidth + '*' + requireHeight)
        return Promise.reject(new Error('图片尺寸不符合要求，请修改后重新上传,要求 ' + requireWidth + '*' + requireHeight))
      }
    )
  }
  getSizeDesc = size => {
    if (size < 1024) {
      return size
    }
    if (size / 1024 < 1024) {
      return size / 1024 + 'K'
    }
    return size / 1024 / 1024 + 'M'
  }
  beforeUpload1 = (file) => {
    // this.handleFilebeforeUpload(file)
    //   .then(() => {
    //     message.success(`上传成功`)
    //   })
    //   .catch(() => {
    //     Modal.error({
    //       title: '上传图片的宽高不符合要求，请重传！（宽高不得超过1500）'
    //     })
    //   })
    const { minSize, maxSize, minWidth, minHeight } = this.props
    console.log('beforeUpload1', minSize, maxSize, minWidth, minHeight)
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png'
    if (!isJpgOrPng) {
      message.error('只能上传 JPG/PNG 格式的文件! ')
      return Promise.reject(new Error('只能上传 JPG/PNG 格式的文件! '))
    }
    const isLt2M = minSize <= file.size && file.size <= maxSize
    if (!isLt2M) {
      message.error('图片大小要在' + this.getSizeDesc(minSize) + '~' + this.getSizeDesc(maxSize) + '范围内! 当前文件大小:' + parseInt(file.size / 1024) + 'k')
      return Promise.reject(new Error('图片大小要在50k-2M范围内! 当前文件大小:' + parseInt(file.size / 1024) + 'k'))
    }

    let isSize = this.isSize(file, minWidth, minHeight)
    // this.checkImageWH(file, 20, 20)
    // if (!isLtSize) {
    //   message.error('图片尺寸需大于等于 400x500 px!')
    //   return Promise.reject(new Error('图片尺寸需大于等于 400x500 px!'))
    // }
    return isJpgOrPng && isLt2M && isSize
  }

  renderImage = () => {
    const { value } = this.props
    const content = (
      <div>
        <img src={value} style={{ maxHeight: 200, maxWidth: 200 }} />
      </div>
    )
    return (
      <Popover placement='right' content={content} title={null}>
        <img width='102' height='102' src={value} />
        <i className='anticon-delete' />
      </Popover>
    )
  }

  render () {
    const { minSize, maxSize, minWidth, minHeight, title } = this.props
    console.log('fileSize', minSize, maxSize, minWidth, minHeight, title)
    const { previewVisible, previewImage, fileList } = this.state
    const uploadButton = (
      <div>
        <PlusOutlined />
        <div className='ant-upload-text'>Upload</div>
      </div>
    )

    return (
      <div className='clearfix'>
        <Upload
          action='https://fts.yy.com/fs/uploadfiles'
          listType='picture-card'
          fileList={fileList}
          beforeUpload={this.beforeUpload1}
          onPreview={this.handlePreview}
          onChange={this.handleChange}
          // showUploadList={false}
          data={file => ({ bucket: 'makefriends', files: file })}
        >
          {fileList.length > 0 ? null : uploadButton}
        </Upload>
        <Modal visible={previewVisible} footer={null} onCancel={this.handleCancel}>
          <img alt='example' style={{ width: '100%' }} src={previewImage} />
        </Modal>
        {title !== undefined && title.length > 0 ? <font color='blue' style={{ marginLeft: 35 }} >{title}</font> : null }
      </div>
    )
  }
}

function urlValue (url) {
  return { uid: -1, status: 'done', url: url, thumbUrl: url }
}

export default PicturesWallCheck
