import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card } from 'antd'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import LuckRankActivity from './activityConfig'
import LuckRankApproval from './approvalList'
import SimplePage from '@/components/SearchParams/simplePage'

const namespace = 'dropLuckRank'

@connect(({ dropLuckRank }) => ({
  model: dropLuckRank
}))

class LuckRank extends Component {
  state = {}
  componentDidMount = () => {
    this.callModel('getPrizeList')
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  render () {
    const { route } = this.props
    // const { list, config } = this.props.model
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='config' type='card' >
            <TabPane tab='活动配置' key='config'>
              <LuckRankActivity />
            </TabPane>
            <TabPane tab='审批记录' key='approval'>
              <LuckRankApproval />
            </TabPane>
            <TabPane tab='数据监控' key='monitor'>
              <SimplePage uri='/rank/drop_luck_rank_boss/query_reward_history'
                tableProps={{ pagination: { pageSize: 50, size: 'small' } }} />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default LuckRank
