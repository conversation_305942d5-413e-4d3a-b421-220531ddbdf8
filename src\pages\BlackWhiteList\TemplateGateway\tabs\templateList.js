import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Row, Col, Table, Typography, Select, Space, Button, Modal, Input, Divider, Form, message, Badge, Collapse } from 'antd'
import { templateIdOptions } from '../common'
import { checkIsNumberList, checkIsUrl, parseNumberList, timeFormater } from '@/utils/common'
import { PlusOutlined } from '@ant-design/icons'
import ApprovalButton from '@/components/ApprovalButton'
import { GrandText, InfoBoard, ArrayEditor, CopyPasteButton } from '@/components/SimpleComponents'

const { Text, Link } = Typography
const { TextArea } = Input
const namespace = 'templateGateWay'
const defaultFormValue = {
  id: 'newConfig',
  ruleVersion: 1,
  isPublic: false,
  isDisable: false,
  verison: 1,
  templateId: '',
  url: '',
  sid: '1',
  ssid: '1',
  newSid: '',
  newSsid: '',
  excludeSid: '',
  excludeSsid: '',
  excludeTicket: '',
  ticket: '',
  note: '',
  templateType: 1,
  isNewTemplate: false
}

@connect(({ templateGateWay }) => ({
  model: templateGateWay
}))

class TemplateList extends Component {
  state = {
    selectTemplateID: 'makefriend',
    editTemplateId: '',
    opType: '',
    showDetail: false,
    selectItem: null,
    isDisable: false,
    loading: false
  }

  componentDidMount = () => {
    this.getTemplateList('makefriend')
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 获取白名单配置列表
  getTemplateList = (id) => {
    this.callModel('qyertTemplate', {
      params: { templateId: id }
    })
  }

  parseTicket (str) {
    if (!str) {
      return []
    }
    str = str.replace(/,/g, '\n')
    str = str.trim()
    let numberList = str.split(`\n`)
    let res = []
    numberList.forEach(item => {
      res.push(item.trim())
    })
    return res
  }

  // 提交新增或更新请求
  updateConfig = (params, opType, selectTemplateID) => {
    this.setState({ loading: true })

    let fixedParams = this.checkAndFixParams(params)
    if (fixedParams === null) {
      this.setState({ loading: false })
      return
    }

    // console.debug('fixedParams==>', fixedParams)

    let funcName = opType === 'ADD' ? 'addTemplate' : 'updateTemplate'
    this.callModel(funcName, {
      params: fixedParams,
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        this.setState({ loading: false })
        if (status !== 0) {
          message.warn('提交更新失败,请联系管理员: ' + msg)
          return
        }
        message.success(opType === 'ADD' ? '已提交审批～' : '更新成功')
        this.getTemplateList(selectTemplateID)
        this.setState({ opType: '' })
      }
    })
  }

  // 检查和修正参数，不通过时返回null
  checkAndFixParams = (params) => {
    const { sid, ssid, ruleVersion, newSid, newSsid, excludeSid, excludeSsid, url, excludeTicket, ticket, isPublic, isDisable } = params
    let fixParams = { ...params }
    let reason = ''
    // console.debug('parmas1===>', params)
    if (!checkIsNumberList(sid)) {
      reason = 'sid白名单不合法'
    }
    if (!checkIsNumberList(ssid)) {
      reason = 'ssid白名单不合法'
    }
    if (ruleVersion > 0 && newSid && !checkIsNumberList(newSid)) {
      reason = '全量sid白名单不合法'
    }
    if (ruleVersion > 0 && newSsid && !checkIsNumberList(newSsid)) {
      reason = '全量ssid白名单不合法'
    }
    if (excludeSid && !checkIsNumberList(excludeSid)) {
      reason = 'sid黑名单不合法'
    }
    if (excludeSsid && !checkIsNumberList(excludeSsid)) {
      reason = 'ssid黑名单不合法'
    }
    if (!checkIsUrl(url)) {
      reason = 'url 不合法'
    }
    if (ruleVersion > 0 && !isDisable && !isPublic && newSid.length === 0 && newSsid.length === 0) {
      reason = '非全量状态请填写白名单'
    }
    if (reason !== '') {
      message.warn('参数校验失败: ' + reason)
      return null
    }
    fixParams.sid = parseNumberList(sid) || []
    fixParams.ssid = parseNumberList(ssid) || []
    fixParams.newSid = parseNumberList(newSid) || []
    fixParams.newSsid = parseNumberList(newSsid) || []
    fixParams.excludeSid = parseNumberList(excludeSid) || []
    fixParams.excludeSsid = parseNumberList(excludeSsid) || []
    fixParams.ticket = this.parseTicket(ticket)
    fixParams.excludeTicket = this.parseTicket(excludeTicket)
    return fixParams
  }

  // 渲染更新模态框
  showUpdateModal = (params) => {
    this.updateFormValue(params)
    this.setState({ opType: 'UPDATE', editTemplateId: params.templateId })
  }

  // 展示详情模态框
  showDetailModal = (params) => {
    this.setState({ selectItem: params, showDetail: true })
  }

  // 状态展示
  statusFormater = (r) => {
    const { aprInfo, sid, ssid, isPublic, isDisable, ruleVersion } = r

    if (aprInfo.aprStatus !== 'OnGoing' && aprInfo.aprStatus !== 'Passed') {
      return <Badge text='已驳回' status='error' />
    }
    if (aprInfo.aprStatus === 'OnGoing') {
      return <Badge text='审核中' status='warning' />
    }
    if (isDisable) {
      return <Badge text='禁用' status='error' />
    }
    if (ruleVersion === 0 && sid === '0' && ssid === '0') {
      return <Badge text='全量' status='success' />
    }
    if (ruleVersion > 0 && isPublic) {
      return <Badge text='全量' status='success' />
    }
    return <Badge text='灰度中' color='#1890ff' />
  }

  updateFormValue = (v) => {
    const { isPublic, isDisable, ruleVersion } = v
    this.formRef.setFieldsValue(v)
    this.setState({ isPublic: isPublic, isDisable: isDisable, ruleVersion: ruleVersion })
  }

  render () {
    const { selectTemplateID, opType, showDetail, selectItem, isPublic, isDisable, loading } = this.state
    const { templateList } = this.props.model

    const baseInfoList = [
      { label: '配置ID', dataIndex: 'id', span: 24 },
      { label: '模板ID', dataIndex: 'templateId', span: 24 },
      { label: '版本', dataIndex: 'version', span: 24 },
      { label: 'URL', dataIndex: 'url', span: 24 },
      { label: '备注信息', dataIndex: 'note', span: 24 }
    ]
    const sidConfig = [
      { label: '匹配策略', dataIndex: 'ruleVersion', span: 24, render: (v) => { return v === 0 ? '旧方法' : '新方法' } },
      { label: 'sid白名单', dataIndex: 'sid', span: 24, render: (v) => { return <ArrayEditor value={v.split(',')} isEdit={false} /> } },
      { label: 'sid白名单', dataIndex: 'sid', span: 24, render: (v) => { return <ArrayEditor value={v.split(',')} isEdit={false} /> } },
      { label: '飞机票白名单', dataIndex: 'ticket', span: 24, render: (v) => { return v ? <ArrayEditor value={v.split('\n')} isEdit={false} /> : '空' } },
      { label: 'sid黑名单', dataIndex: 'excludeSid', span: 24, render: (v) => { return v ? <ArrayEditor value={v.split(',')} isEdit={false} /> : '空' } },
      { label: 'ssid黑名单', dataIndex: 'excludeSsid', span: 24, render: (v) => { return v ? <ArrayEditor value={v.split(',')} isEdit={false} /> : '空' } },
      { label: '飞机票黑名单', dataIndex: 'excludeTicket', span: 24, render: (v) => { return v ? <ArrayEditor value={v.split('\n')} isEdit={false} /> : '空' } }
    ]
    const sidConfigNew = [
      { label: '匹配策略', dataIndex: 'ruleVersion', span: 24, render: (v) => { return v === 0 ? '旧方法' : '新方法' } },
      { label: '全量sid白名单', dataIndex: 'newSid', span: 24, render: (v) => { return v ? <ArrayEditor value={v.split(',')} isEdit={false} /> : '空' } },
      { label: '全量ssid白名单', dataIndex: 'newSsid', span: 24, render: (v) => { return v ? <ArrayEditor value={v.split(',')} isEdit={false} /> : '空' } },
      { label: 'sid黑名单', dataIndex: 'excludeSid', span: 24, render: (v) => { return v ? <ArrayEditor value={v.split(',')} isEdit={false} /> : '空' } },
      { label: 'ssid黑名单', dataIndex: 'excludeSsid', span: 24, render: (v) => { return v ? <ArrayEditor value={v.split(',')} isEdit={false} /> : '空' } },
      { label: '是否全量', dataIndex: 'isPublic', span: 24, render: (v) => { return v ? '是' : '否' } },
      { label: '是否禁用', dataIndex: 'isDisable', span: 24, render: (v) => { return v ? '是' : '否' } }
    ]
    const updateInfo = [
      { label: '创建时间', dataIndex: 'createTime', span: 24, render: (v) => { return timeFormater(v) } },
      { label: '更新时间', dataIndex: 'updateTime', span: 24, render: (v) => { return timeFormater(v) } },
      { label: '更新次数', dataIndex: 'opCount', span: 24 },
      { label: '更新人', dataIndex: 'opNick', span: 24, render: (v, r) => { return `${v} (uid=${r.opUid})` } },
      { label: '更新描述', dataIndex: 'desc', span: 24 }
    ]
    const aprInfo = [
      { label: '审批流ID', dataIndex: 'aprId', span: 24 },
      { label: '创建人', dataIndex: 'opNick', span: 24, render: (v, r) => { return `${v} (uid=${r.opUid})` } },
      { label: '审批人', dataIndex: 'aprNick', span: 24, render: (v, r) => { return `${v} (uid=${r.aprUid})` } },
      { label: '审批时间', dataIndex: 'aprTime', span: 24, render: (v) => { return timeFormater(v) } },
      { label: '审批备注', dataIndex: 'aprReason', span: 24 }
    ]

    const columns = [
      { title: 'ID', dataIndex: 'id' },
      { title: '状态', dataIndex: 'id', render: (v, r) => { return this.statusFormater(r) } },
      { title: '模板ID', dataIndex: 'templateId' },
      { title: '版本', dataIndex: 'version' }, 
      { title: '访问地址', dataIndex: 'url', width: '12em', render: (v) => { return <GrandText type='popover' width='10em' value={v} /> } },
      { title: '更新信息', dataIndex: 'updateInfo', width: '12em', render: (v, r) => { return <GrandText type='popover' width='10em' value={v.desc} /> } },
      { title: '备注', dataIndex: 'note', width: '12em', render: (v) => { return <GrandText type='popover' width='10em' value={v} /> } },
      { title: '操作',
        dataIndex: 'id',
        render: (v, r) => {
          return <dev>
            <Link onClick={() => { this.showDetailModal(r) }} >查看详情</Link>
            <Divider type='vertical' />
            {
              r.aprInfo.aprStatus !== 'Passed'
                ? ''
                : <Link onClick={() => { this.showUpdateModal(r) }} >更新</Link>
            }
            {
              r.aprInfo.aprStatus === 'OnGoing'
                ? <ApprovalButton aprId={r.aprInfo.aprId} >
                  <Link type='warning'>审批</Link>
                </ApprovalButton>
                : ''
            }
          </dev>
        } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    const isNewRule = this.formRef?.getFieldValue('ruleVersion') === 1

    return (
      <Card>
        <Row>
          <Col span={24} style={{ marginBottom: '1em' }}>
            <Space>
              <Text>模板ID:</Text>
              <Select
                options={[{ label: '全部', value: '' }, ...templateIdOptions]}
                value={selectTemplateID}
                style={{ width: '10em' }}
                onChange={(v) => { this.setState({ selectTemplateID: v }); this.getTemplateList(v) }}
              />
              {/* <Button type='primary' onClick={() => this.getTemplateList(selectTemplateID)}>查询</Button> */}
              <Divider type='vertical' />
              <Button onClick={() => { this.setState({ opType: 'ADD' }); this.updateFormValue(defaultFormValue) }}><PlusOutlined />新增</Button>
            </Space>
          </Col>
          <Col span={24}>
            <Table size='small' pagination={{ pageSize: 30 }} columns={columns} dataSource={templateList} />
          </Col>
        </Row>

        <Modal title='配置详情' visible={showDetail} onCancel={() => this.setState({ showDetail: false, selectItem: null })} footer={null} >
          <InfoBoard columns={baseInfoList} dataSource={selectItem} title='基本信息' />
          <InfoBoard columns={selectItem?.ruleVersion > 0 ? sidConfigNew : sidConfig} dataSource={selectItem} title='灰度策略' />
          <InfoBoard columns={updateInfo} dataSource={selectItem?.updateInfo} title='更新信息' />
          <InfoBoard columns={aprInfo} dataSource={selectItem?.aprInfo} title='审批信息' />
        </Modal>

        <Modal title={opType === 'ADD' ? '新增白名单' : '更新白名单'} visible={opType !== ''} forceRender
          onOk={() => { this.formRef.submit() }}
          okButtonProps={{ loading: loading }}
          onCancel={() => this.setState({ opType: '' })} >
          <Form ref={r => { this.formRef = r }} onFinish={v => this.updateConfig(v, opType, selectTemplateID)} labelCol={{ span: 6 }} initialValues={defaultFormValue} >
            <Form.Item label='' name='id' hidden disabled>
              <Input />
            </Form.Item>
            <Form.Item label='模板ID' name='templateId' rules={[{ required: true }]}>
              <Select options={templateIdOptions} disabled={opType !== 'ADD'} onChange={v => { this.setState({ editTemplateId: v }) }} />
            </Form.Item>
            <Form.Item label='URL' name='url' rules={[{ required: true }]} >
              <Input disabled={opType !== 'ADD' && ENV_TAG === 'prod'} />
            </Form.Item>
            <Form.Item label='版本' name='version' hidden={opType === 'ADD'}>
              <Input disabled />
            </Form.Item>

            <Form.Item name='ruleVersion' hidden>
              <Input disabled />
            </Form.Item>

            <Form.Item label='是否全量' name='isPublic' tooltip='开启后只要不命中黑名单就能访问,否则只有命中白名单才能访问' hidden={!isNewRule}>
              <Select options={[{ label: '否', value: false }, { label: '是', value: true }]} onChange={v => this.setState({ isPublic: v })} disabled={isDisable} />
            </Form.Item>
            <Form.Item label='是否禁用' name='isDisable' tooltip='开启后任何频道都不会访问到这个模板' hidden={!isNewRule}>
              <Select options={[{ label: '否', value: false }, { label: '是', value: true }]} onChange={v => this.setState({ isDisable: v })} />
            </Form.Item>

            <Form.Item label='全量sid白名单' name='newSid' tooltip='频道sid命中本列表就能访问' hidden={!isNewRule} >
              <TextArea autoSize={{ minRows: 2 }} placeholder='输入多个时用逗号或换行隔开' disabled={isPublic || isDisable} />
            </Form.Item>
            <Form.Item label='全量ssid白名单' name='newSsid' tooltip='频道ssid命中本列表就能访问' hidden={!isNewRule} >
              <TextArea autoSize={{ minRows: 2 }} disabled={isPublic || isDisable} />
            </Form.Item>

            <Form.Item label='sid白名单' name='sid' tooltip='sid白名单和ssid白名单构成组合，其中一个为0，则不构成组合' rules={[{ required: true }]} hidden={isNewRule} >
              <TextArea autoSize={{ minRows: 2 }} placeholder='输入多个时用逗号或换行隔开' />
            </Form.Item>
            <Form.Item label='ssid白名单' name='ssid' tooltip='sid白名单和ssid白名单构成组合，其中一个为0，则不构成组合' rules={[{ required: true }]} hidden={isNewRule} >
              <TextArea autoSize={{ minRows: 2 }} placeholder='输入多个时用逗号或换行隔开' />
            </Form.Item>

            <Form.Item label='sid黑名单' name='excludeSid' tooltip='在列表中的sid,不能匹配当前版本。'>
              <TextArea autoSize={{ minRows: 2 }} placeholder='输入多个时用逗号或换行隔开' disabled={isDisable} />
            </Form.Item>
            <Form.Item label='ssid黑名单' name='excludeSsid' tooltip='在列表中的sid,不能匹配当前版本。'>
              <TextArea autoSize={{ minRows: 2 }} placeholder='输入多个时用逗号或换行隔开' disabled={isDisable} />
            </Form.Item>

            <Form.Item label='备注' name='note' rules={[{ required: true }]}>
              <TextArea autoSize={{ minRows: 2 }} />
            </Form.Item>

            <Collapse ghost>
              <Collapse.Panel forceRender header={<a>更多高级配置</a>} >
                <Form.Item label='飞机票白名单' name='ticket' tooltip='指定让某个频道能够匹配当前版本。输入多个时用逗号或换行隔开'>
                  <TextArea autoSize={{ minRows: 2 }} placeholder='参考格式: yy://pd-[sid=123&subid=123]' disabled={isDisable} />
                </Form.Item>
                <Form.Item label='飞机票黑名单' name='excludeTicket' tooltip='指定让某个频道不能匹配当前版本。输入多个时用逗号或换行隔开'>
                  <TextArea autoSize={{ minRows: 2 }} placeholder='参考格式: yy://pd-[sid=123&subid=123]' disabled={isDisable} />
                </Form.Item>

                <Form.Item label='快捷选项' name='null' tooltip='可用此方法将测试环境配置快速配置到线上'>
                  <CopyPasteButton getCopyVal={() => { return this.formRef.getFieldsValue() }} pasteVal={(val) => { this.formRef.setFieldsValue(val) }} />
                </Form.Item>

              </Collapse.Panel>

            </Collapse>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default TemplateList
