import React, { Component } from 'react'
import { connect } from 'dva'
import { Divider, Table, message, Popconfirm, Typography, Row, Col, Button, Space } from 'antd'
import { deepClone, getCookie } from '@/utils/common'
import { Tip, ConfigItem, viewColumns, MyNumberInput } from '../common'
import ApprovalButton from '@/components/ApprovalButton'
import { AprInfoDesc } from '../../../../components/SimpleComponents'
const { Link, Text } = Typography
const namespace = 'flameGame'

@connect(({ flameGame }) => ({
  model: flameGame
}))

class PoolEdit extends Component {
  state = {
    rawConfig: {}, // 对应PoolConfig结构
    poolConfig: {},
    editingConfig: {},
    isEditing: false
  }

  componentDidMount = () => {
    this.getPoolConifig()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // 获取奖池配置
  getPoolConifig = () => {
    this.callModel('getFlameConfig', {
      params: { id: this.props.poolId },
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg, data } = ret
        if (status < 0) {
          message.warn('获取配置失败: ' + msg)
          return
        }
        const poolConfig = JSON.parse(data.temporary.content)
        const editingConfig = JSON.parse(data.temporary.content)
        this.setState({ rawConfig: data, poolConfig: poolConfig, editingConfig: editingConfig })
      }
    })
  }

  // 取消编辑
  onCancelEdit = () => {
    const { poolConfig } = this.state
    this.setState({ editingConfig: deepClone(poolConfig), isEditing: false })
  }

  // 更新临时数值
  onEditTempConfig = (field, value) => {
    const { editingConfig } = this.state
    let cp = deepClone(editingConfig)
    cp[field] = value
    this.setState({ editingConfig: cp })
  }

  // 增加档位
  onAddLevel = () => {
    const { editingConfig } = this.state
    let cp = deepClone(editingConfig)
    if (!cp.levelConfig) {
      cp.levelConfig = []
    }
    let maxEnd = 0
    cp.levelConfig.forEach(e => {
      if (e.endPoint > maxEnd) {
        maxEnd = e.endPoint
      }
    })
    let newlevel = { level: cp.levelConfig.length + 1, startPoint: maxEnd, endPoint: maxEnd + 100000, rateA: 1, rateB: 1, rateC: 1, rateTotal: 3 }
    cp.levelConfig.push(newlevel)
    this.setState({ editingConfig: cp })
  }

  // 删除档位
  onDelLevel = (level) => {
    const { editingConfig } = this.state
    let cp = deepClone(editingConfig)
    let list = []
    cp.levelConfig.forEach(e => {
      if (e.level !== level) {
        list.push(e)
      }
    })
    list = list.map((item, i) => {
      item.level = i + 1
      return item
    })
    cp.levelConfig = list
    this.setState({ editingConfig: cp })
  }

  // 更新档位配置数组
  onUpdateLevelConfig = (row, field, value) => {
    const { editingConfig } = this.state
    let cp = deepClone(editingConfig)
    cp.levelConfig[row][field] = value
    this.setState({ editingConfig: cp })
  }

  // 提交审批
  onSubmitUpdate = () => {
    const { rawConfig, editingConfig } = this.state
    let fixedConfig = this.getFixedConfig(editingConfig)
    if (!fixedConfig) {
      return
    }
    let params = deepClone(rawConfig)
    params.temporary.content = JSON.stringify(fixedConfig)
    // console.debug('rawConfig====>', rawConfig)
    let levelConfigDesc = ''
    fixedConfig.levelConfig.forEach(item => {
      levelConfigDesc += `等级${item.level}失败率${Number(item.rateC * 100.0 / item.rateTotal).toFixed(1)}%;  `
    })
    const dataMap = {
      name: params.name,
      dailyLimitGlobal: `${fixedConfig.dailyLimitGlobal / 1000}元`,
      dailyLimitUser: `${fixedConfig.dailyLimitUser / 1000}元`,
      deductRate: `${fixedConfig.deductRate}%`,
      levelConfig: levelConfigDesc
    }
    params.aprData = dataMap
    params.aprText = `${getCookie('username')}申请更新烈焰秘境奖池配置:
    全平台日兑额度: ${dataMap.dailyLimitGlobal}
    单uid日兑额度: ${dataMap.dailyLimitUser}
    升级失败损耗度: ${dataMap.deductRate}
    升级率配置: ${dataMap.levelConfig}
    `

    this.callModel('udpateFlameConfig', {
      params: params,
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('送审失败: ' + msg)
          return
        }
        message.success('送审成功～')
        this.setState({ isEditing: false })
        setTimeout(() => {
          this.getPoolConifig()
        }, 1000)
      }
    })
  }

  // 检查配置并修复配置字段，若配置有误返回 false
  getFixedConfig = (before) => {
    const { dailyLimitGlobal, dailyLimitUser, deductRate, levelConfig } = before
    if (dailyLimitGlobal < -1000 || dailyLimitUser < -1000) {
      message.warn('每日限额配置有误')
      return false
    }
    if (deductRate < 0 || deductRate > 100) {
      message.warn('扣除损耗率配置有误')
      return false
    }
    if (levelConfig.length === 0) {
      message.warn('成功率档位未配置')
      return false
    }
    let fixLevel = levelConfig
    fixLevel.sort((a, b) => {
      return a.startPoint < b.startPoint ? -1 : 1
    })
    for (let i = 0; i < fixLevel.length; i++) {
      fixLevel[i].level = i + 1
      let item = fixLevel[i]
      let total = item.rateA + item.rateB + item.rateC
      if (item.startPoint >= item.endPoint) {
        message.warn('价值区间配置有误: level=' + item.level)
        return false
      }
      if (total <= 0 || item.rateA < 0 || item.rateB < 0 || item.rateC < 0) {
        message.warn('概率配置有误: level=' + item.level)
        return false
      }
      if (i > 0 && item.startPoint !== fixLevel[i - 1].endPoint) {
        message.warn('档位区间不连续: level=' + item.level)
        return false
      }
      fixLevel[i].rateTotal = total
    }
    fixLevel[fixLevel.length - 1].endPoint = 1000 * 10000 * 10000 // 1亿元
    before.levelConfig = fixLevel
    return before
  }

  render () {
    const { rawConfig, poolConfig, isEditing, editingConfig } = this.state
    const styleInput = { width: '10em', marginRight: '1em' }
    const aprInfo = rawConfig?.temporary?.aprInfo

    const editColumns = [
      { title: '等级', dataIndex: 'level' },
      { title: '价值区间(元)',
        dataIndex: '_',
        render: (_, r, i) => {
          return <Space>
            <MyNumberInput value={r.startPoint} base={1000} onChange={(v) => { this.onUpdateLevelConfig(i, 'startPoint', v) }} />
            <Text>~</Text>
            <MyNumberInput value={r.endPoint} base={1000} onChange={(v) => { this.onUpdateLevelConfig(i, 'endPoint', v) }} />
          </Space>
        } },
      { title: '冰霜之心', dataIndex: 'rateA', render: (v, _, i) => { return <MyNumberInput value={v} base={1} onChange={(v) => { this.onUpdateLevelConfig(i, 'rateA', v) }} /> } },
      { title: '凤凰火焰戒', dataIndex: 'rateB', render: (v, _, i) => { return <MyNumberInput value={v} base={1} onChange={(v) => { this.onUpdateLevelConfig(i, 'rateB', v) }} /> } },
      { title: '升级失败', dataIndex: 'rateC', render: (v, _, i) => { return <MyNumberInput value={v} base={1} onChange={(v) => { this.onUpdateLevelConfig(i, 'rateC', v) }} /> } },
      { title: '失败概率', dataIndex: '_', render: (_, r) => { return `${(r.rateC * 100.0 / (r.rateA + r.rateB + r.rateC)).toFixed(2)}%` } },
      { title: '操作', dataIndex: '_', render: (_, r, i) => { return <Popconfirm title='确认删除该档位么?' onConfirm={() => { this.onDelLevel(r.level) }}><Link>删除</Link></Popconfirm> } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <div>
        <Tip />
        <div style={{ marginTop: '1em' }}>
          <AprInfoDesc value={aprInfo} />
        </div>

        <Divider />

        <Row>
          <Col span={24}>
            {
              !isEditing
                ? <Space>
                  <Button type='primary' onClick={() => { this.setState({ isEditing: true }) }}>编辑</Button>
                  { aprInfo?.aprResult === 'OnGoing' ? <ApprovalButton aprId={aprInfo.aprId}><Button>审批</Button></ApprovalButton> : '' }
                </Space>
                : <Space>
                  <Button type='primary' danger onClick={() => this.onCancelEdit()} >取消编辑</Button>
                  <Button onClick={() => this.onSubmitUpdate()}>提交审批</Button>
                </Space>
            }
          </Col>
        </Row>

        <div>
          <ConfigItem title='全平台日兑换紫水晶券总额度' tooltip='每日该玩法下,可产出紫水晶券的总上限'>
            <MyNumberInput readOnly={!isEditing} style={styleInput}
              value={editingConfig.dailyLimitGlobal} base={1000} onChange={(v) => this.onEditTempConfig('dailyLimitGlobal', v)} />元
          </ConfigItem>
          <ConfigItem title='单UID紫水晶券总额度(耐久度)' tooltip='单日该玩法下，单人可产出紫水晶券的总上限'>
            <MyNumberInput readOnly={!isEditing} style={styleInput}
              value={editingConfig.dailyLimitUser} base={1000} onChange={(v) => this.onEditTempConfig('dailyLimitUser', v)} />元
          </ConfigItem>
          <ConfigItem title='扣除损耗率' tooltip='单次升级失败扣除的损耗度'>
            <MyNumberInput readOnly={!isEditing} style={styleInput}
              value={editingConfig.deductRate} base={1} onChange={(v) => this.onEditTempConfig('deductRate', v)} />%
          </ConfigItem>
          <ConfigItem title='升级成功率' tooltip='单次升级的总价值区分对应获得礼物的概率，输入0%代表必失败，必获得紫水晶券'>
            {
              isEditing
                ? <Row>
                  <Col span={24}>
                    <Button onClick={() => this.onAddLevel()}>增加档位</Button>
                  </Col>
                  <Col span={24}>
                    <Table size='small' columns={editColumns} pagination={false} dataSource={editingConfig.levelConfig} />
                  </Col>
                </Row>
                : <Row>
                  <Col span={24}>
                    <Table size='small' columns={viewColumns} pagination={false} dataSource={poolConfig.levelConfig} />
                  </Col>
                </Row>
            }
          </ConfigItem >
        </div>
      </div>
    )
  }
}

export default PoolEdit
