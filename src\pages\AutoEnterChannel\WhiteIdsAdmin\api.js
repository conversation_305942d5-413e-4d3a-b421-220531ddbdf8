import request from '@/utils/request'
import { stringify } from 'qs'

export function getAdmin (params) {
  return request(`/AutoEnterChannelCtrl/GetAdmin?${stringify(params)}`) // 修改 url 即可
}

export function getCompere (params) {
  return request(`/AutoEnterChannelCtrl/GetCompere?${stringify(params)}`) // 修改 url 即可
}

export function getWhiteIds (params) {
  return request(`/AutoEnterChannelCtrl/GetWhiteIds?${stringify(params)}`) // 修改 url 即可
}

export function getBlackIds (params) {
  return request(`/AutoEnterChannelCtrl/GetBlackIds?${stringify(params)}`) // 修改 url 即可
}

export function addCompere (params) {
  return request(`/AutoEnterChannelCtrl/SaveCompere?${stringify(params)}`)
}

export function addWhiteIds (params) {
  return request(`//AutoEnterChannelCtrl/SaveWhiteIds?${stringify(params)}`)
}

export function addBlackIds (params) {
  return request(`/AutoEnterChannelCtrl/SaveBlackIds?${stringify(params)}`)
}

export function removeCompere (params) {
  return request(`/AutoEnterChannelCtrl/DelCompere?${stringify(params)}`)
}

export function removeWhiteIds (params) {
  return request(`/AutoEnterChannelCtrl/DelWhiteIds?${stringify(params)}`)
}

export function removeBlackIds (params) {
  return request(`/AutoEnterChannelCtrl/DelBlackIds?${stringify(params)}`)
}

export function updateAdminOpen (params) {
  return request(`/AutoEnterChannelCtrl/UpdateAdminOpen?${stringify(params)}`)
}

export function updateCompere (params) {
  return request(`/AutoEnterChannelCtrl/UpdateCompere?${stringify(params)}`)
}

export function updateWhiteIds (params) {
  return request(`/AutoEnterChannelCtrl/UpdateWhiteIds?${stringify(params)}`)
}

export function updateBlackIds (params) {
  return request(`/AutoEnterChannelCtrl/UpdateBlackIds?${stringify(params)}`)
}
