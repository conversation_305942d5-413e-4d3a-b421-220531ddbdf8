import React, { Component } from 'react'
import { Card, Table, DatePicker, Divider, Row, Col, Button } from 'antd'
import { connect } from 'dva'
import moment from 'moment'

const namespace = 'SynthesisReport' // model 的 namespace

@connect(({ SynthesisReport }) => ({ // model 的 namespace
  model: SynthesisReport // model 的 namespace
}))
class RealTimeComponent extends Component {
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getRealTimeList`
    })
  }

  state = { today: moment() }

  // 需要修改
  columns = [
    { title: '道具名称', dataIndex: 'giftName', align: 'center' },
    { title: '当前库存总量', dataIndex: 'giftSum', align: 'center' },
    { title: '道具[1,3)用户数', dataIndex: 'rangeOne', align: 'center' },
    { title: '道具[3,5)用户数', dataIndex: 'rangeTwo', align: 'center' },
    { title: '道具[5,10)用户数', dataIndex: 'rangeThree', align: 'center' },
    { title: '道具[10,100)用户数', dataIndex: 'rangeFour', align: 'center' },
    { title: '道具[100+）用户数', dataIndex: 'rangeFive', align: 'center' }
  ]

  onClick = day => () => {
    const { dispatch } = this.props
    const { today } = this.state

    today.add(day, 'days')
    // console.log(today.format('YYYY-MM-DD'))

    dispatch({
      type: `${namespace}/getRealTimeList`,
      payload: { date: today.format('YYYYMMDD') }
    })

    let v = $.extend({}, true, today) // 深拷贝
    this.setState({ today: v })
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { realtimeList } } = this.props
    const { today } = this.state

    return (
      <Card>
        <Row gutter={6}>
          <Col>
            <DatePicker value={today} disabled />
          </Col>
          <Col>
            <Button onClick={this.onClick(-1)}>前一天</Button>
          </Col>
          <Col>
            <Button onClick={this.onClick(1)}>后一天</Button>
          </Col>
        </Row>
        <Divider />
        <Table rowKey={(record, index) => index} dataSource={realtimeList} columns={this.columns} pagination={false} /> {/* 显示的列表 */}
        <div><font color='red'>说明：每种道具库存区间对应用户数</font></div>
      </Card>
    )
  }
}

export default RealTimeComponent
