import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import {
  <PERSON><PERSON>,
  Card,
  Divider,
  Form,
  Input,
  Modal,
  Popconfirm,
  Table,
  Select
} from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
var moment = require('moment')

const namespace = 'luxuryActivityBlackList'
const FormItem = Form.Item
const Option = Select.Option
const { TextArea } = Input
const yearConfig = [
  { value: 2023, label: '2023' }
  // { value: 2024, label: '2024' },
  // { value: 2025, label: '2025' },
  // { value: 2026, label: '2026' },
  // { value: 2027, label: '2027' },
  // { value: 2028, label: '2028' }
]

let yearFilters = []
let yearOptions = []
yearFilters.push(<Option key={0} value={'all'}>{''}</Option>)
for (let i = 0; i < yearConfig.length; i++) {
  yearFilters.push(<Option key={i + 1} value={yearConfig[i].value}>{yearConfig[i].label}</Option>)
  yearOptions.push(<Option key={i} value={yearConfig[i].value}>{yearConfig[i].label}</Option>)
}

@connect(({ luxuryActivityBlackList, loading }) => ({
  model: luxuryActivityBlackList,
  loading: loading.effects['userConsumeLocker/getList']
}))

class luxuryActivityBlackList extends Component {
  columns = [
    { title: '#', dataIndex: 'index', align: 'center' },
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: 'yy号', dataIndex: 'yy', align: 'center' },
    { title: 'nick', dataIndex: 'nick', align: 'center' },
    { title: '业务',
      dataIndex: 'businessType',
      key: 'businessType',
      align: 'center',
      width: 70,
      render: (v) => {
        switch (v) {
          case 'love': return '交友'
          case 'mimi': return '宝贝'
        }
        return ''
      }
    },
    { title: '年份', dataIndex: 'year', align: 'center' },
    { title: '添加日期', dataIndex: 'timestamp', align: 'center', render: text => this.dateString(text), width: 150 },
    { title: '提交人', dataIndex: 'opNick', align: 'center' },
    { title: '操作',
      align: 'center',
      render: (text, record) => (
        <span>
          <Popconfirm title='Sure to delete?' onConfirm={this.handleDel(record.id)}>
            <a>删除</a>
          </Popconfirm>
        </span>)
    }
  ]

  pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }
  state = { visible: false, isUpdate: false, value: {} }

  showModal = (isUpdate, record) => () => {
    if (record == null) record = { uid: '' }

    let v = $.extend(true, {}, record)
    v.uid = String(v.uid)
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: v, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  hideModal = () => {
    this.setState({ visible: false })
  }

  dateString (timestamp) {
    if (timestamp === 0) {
      return '-'
    }
    return moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss')
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  onFinish = values => {
    const { dispatch } = this.props
    // const { isUpdate } = this.state

    // if (!isUpdate) {
    //   let dupUID = []
    //   let inputUIDList = values.uid.split('\n').map(e => parseInt(e))
    //   for (var i = 0; i < list.length; i++) {
    //     inputUIDList.forEach(e => { if (e === list[i].uid) { dupUID.push(e) } })
    //   }
    //
    //   if (dupUID.length > 0) {
    //     message.error('不能添加重复的uid: ' + dupUID.join(','))
    //     return
    //   }
    // }

    values.uid = values.uid.replaceAll('\n', '|')
    // values.year = values.
    console.log('values', values)
    const url = `${namespace}/addItem`
    dispatch({
      type: url,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  onSearch = () => {
    const { dispatch } = this.props
    const { uid, businessType, year } = this.state
    const data = { uid: uid, businessType: businessType, year: year }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { id: key }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`
    })
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list }, loading } = this.props
    const { visible, title, isUpdate } = this.state
    const formLayout = {
      labelCol: {
        xs: { span: 6 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 14 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            UID
            <Input onChange={e => this.setState({ uid: e.target.value })} style={{ marginLeft: 5, width: 150 }} /> {/* 搜索按钮 */}
            业务：
            <Select labelInValue defaultValue={{ key: '' }} onChange={e => this.setState({ businessType: e.key })} style={{ width: 100 }}>
              <Option value={'mimi'}>宝贝</Option>
              <Option value={'love'}>交友</Option>
              <Option value={''}>all</Option>
            </Select>
            年份：
            <Select style={{ width: 100 }} >{yearFilters}</Select>
            <Button type='primary' onClick={this.onSearch}>查询</Button>
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.showModal(false)}>添加</Button>
            <div>
              <p style={{ marginTop: 10, color: 'red' }}>豪华礼物活动主持黑名单</p>
            </div>
            <Divider />
            <Table loading={loading} dataSource={list} columns={this.columns} rowKey={record => record.index} pagination={this.pagination} size='small' />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form ref={this.saveFormRef} onFinish={this.onFinish} {...formLayout} >
            <FormItem label='UID' name='uid' tooltip='UID(回车换行)' rules={[{ required: true, message: '请输入UID(多个UID回车换行)!' }]} >
              <TextArea placeholder='请输入UID(多个UID回车换行,最后一行不需要换行)!' disabled={isUpdate} rows={4} />
            </FormItem>
            <FormItem label='业务' name='businessType' rules={[{ required: true, message: '业务不能为空' }]}>
              <Select>
                <Option value={'mimi'}>宝贝</Option>
                <Option value={'love'}>交友</Option>
              </Select>
            </FormItem>
            <FormItem label='年份' name='year' rules={[{ required: true, message: '年份不能为空' }]}>
              <Select style={{ width: '100%' }} >{yearOptions}</Select>
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default luxuryActivityBlackList
