import { getLists, update, getProfile, getFileUrl, cancelContract } from './api'
import { message } from 'antd'

// const mockList = [
//   { 'id': 1630574543473, 'uid': 1343189388, 'yy': 1482557976, 'nick': '大帝', 'sid': 69595230, 'asid': 2564, 'owuid': 918704550, 'stage': 4, 'certdone': 1, 'signtm1': 1630576444, 'signtm2': 1630576530, 'signguaranteetm': 1630576614, 'htype': 0, 'subtime': 1630670244, 'rname': '吴海锋', 'cardno': '130823199811096017', 'shotimg': '', 'orderno': '2866731027789776897', 'faceimg': 'http://makefriends.bs2dl.yy.com/1630575976_b98765baa502511141f2c9c0854875e2.png', 'sigvideo': 'http://makefriends.bs2dl.yy.com/1630575977_3a859ae187482f87db29acb1d3eaa691.mp4', 'contractid2': '163057653101000039', 'guaranteeid': '163057661501000031', 'pdf1': '', 'pdf2': '', 'guaranteepdf': '', 'attachment1': '', 'attachment2': '', 'gattachment': '', 'refuse': '', 'btype': 0, 'restage': 0, 'operator': '', 'creason': '', 'addr': '辽宁省大连市金州区和平路748号', 'idaddr': '河北省平泉市', 'phone': '15998402173', 'wechat': 'k889938', 'email': '<EMAIL>', 'artnick': '大帝', 'front': '1630576169_7d8c1f0fb202492c404a1fb838bbd689.jpeg', 'back': '1630576192_dac234f02fe58f604e2989ed2cae228e.jpeg', 'holdfront': '1630576216_329be1c02f80f727cc056d4c916e6e75.jpeg', 'holdback': '1630576232_63a0032c2dc9b4df79d0df8f0760b144.jpeg', 'change': 0, 'cstarttime': 0, 'cendtime': 0, 'facefile': 'https://jyesign.bs2dl.yy.com/1630575976_b98765baa502511141f2c9c0854875e2.png?token=ak_bee:2M3CoDqXIR1hPVsrw928Q3F-NTk=:1630897640', 'videofile': 'https://jyesign.bs2dl.yy.com/1630575977_3a859ae187482f87db29acb1d3eaa691.mp4?token=ak_bee:Yke4pDB3Q533xM1UckzY89MvFko=:1630897640', 'alipay': '13253182472', 'wechatscreenshot': '1630576260_da8b0997e11560efcedd0d62e5f96fa0.png', 'alipayscreenshot': '1630576275_c29955aad92b9f9f17bd929f70aab1a3.png', 'wechatrecordscreen': 'https://jyesign.bs2dl.yy.com/1630576299_3dbc67b014a577ac03a95667b26ac69a.mp4?token=ak_bee:o4Wlz9QApQZ-RnSwkfD5BgbK3Zg=:1630897640', 'alipayrecordscreen': 'https://jyesign.bs2dl.yy.com/1630576318_98056f4d979f2713cb7baef465eb6c12.mp4?token=ak_bee:ObvCvnE7LCzFyohJFrNuFdl7Quk=:1630897640', 'corpname': '武汉宇之辰网络科技有限公司', 'contractAsid': 0 },
//   { 'id': 1630578848628, 'uid': 7429586, 'yy': 12068658, 'nick': '无良人', 'sid': 69595230, 'asid': 2564, 'owuid': 918704550, 'stage': 4, 'certdone': 1, 'signtm1': 1630585982, 'signtm2': 1630586306, 'signguaranteetm': 1630586498, 'htype': 0, 'subtime': 1630669527, 'rname': '谢蒙蒙', 'cardno': '320323198811201618', 'shotimg': '', 'orderno': '2866812523200384003', 'faceimg': 'http://makefriends.bs2dl.yy.com/1630585692_a0257e901fbaa4efae237a7c215d1b27.png', 'sigvideo': 'http://makefriends.bs2dl.yy.com/1630585693_4a7575d27961c4bd10db344a37eece76.mp4', 'contractid2': '163058630801000004', 'guaranteeid': '163058649901000054', 'pdf1': '', 'pdf2': '', 'guaranteepdf': '', 'attachment1': '', 'attachment2': '', 'gattachment': '', 'refuse': '', 'btype': 0, 'restage': 0, 'operator': '', 'creason': '', 'addr': '江苏省徐州市铜山区大彭镇雁群村孔店8组67号', 'idaddr': '江苏省徐州市铜山区大彭镇雁群村孔店8组67号', 'phone': '18601413521', 'wechat': '18601413521', 'email': '<EMAIL>', 'artnick': '男孩', 'front': '1630585835_3109cbd86d609c2b98cc6a71cadf1e6b.jpeg', 'back': '1630585848_8054022e06d5676c6ab0506c6166a136.jpeg', 'holdfront': '1630585864_b340b9a43fc89b657f656a4bfc21b3ab.jpeg', 'holdback': '1630585876_eb29365901d834f6adc393f0f73d9acd.jpeg', 'change': 0, 'cstarttime': 0, 'cendtime': 0, 'facefile': 'https://jyesign.bs2dl.yy.com/1630585692_a0257e901fbaa4efae237a7c215d1b27.png?token=ak_bee:0xuwvMNQ9DNhVQrEfn28Dn_HgnU=:1630897640', 'videofile': 'https://jyesign.bs2dl.yy.com/1630585694_4a7575d27961c4bd10db344a37eece76.mp4?token=ak_bee:R2Pe83o0dDRR63U1pwamHHoATok=:1630897640', 'alipay': '18601413521', 'wechatscreenshot': '1630585890_8535a0da9b36e3046d7b54f5aadceb0d.png', 'alipayscreenshot': '1630585904_8b1d400a3238883cb23da91ac0e08611.jpeg', 'wechatrecordscreen': 'https://jyesign.bs2dl.yy.com/1630585932_9540a8b4dc58e57f972b5e1e951623d6.mp4?token=ak_bee:z7GOrSnpgIx9OWNRCmfZQKSdKCE=:1630897640', 'alipayrecordscreen': 'https://jyesign.bs2dl.yy.com/1630585948_174f8e2e618a3bc1f54abc8a6b572e1b.mp4?token=ak_bee:Q-m6AuiRSzxARB_sRBnLhypWpuo=:1630897640', 'corpname': '武汉宇之辰网络科技有限公司', 'contractAsid': 0 },
//   { 'id': 1630574399750, 'uid': 2394081699, 'yy': 2395928359, 'nick': '轻音☌宁静广播部', 'sid': 69595230, 'asid': 2564, 'owuid': 918704550, 'stage': 4, 'certdone': 1, 'signtm1': 1630589050, 'signtm2': 1630589129, 'signguaranteetm': 1630589186, 'htype': 0, 'subtime': 1630669460, 'rname': '韩文婷', 'cardno': '341226199203131604', 'shotimg': '', 'orderno': '2866835144474363913', 'faceimg': 'http://makefriends.bs2dl.yy.com/1630588395_636707c9eb53ba92ef51b6fdd9a6922d.png', 'sigvideo': 'http://makefriends.bs2dl.yy.com/1630588396_db9b13e9ddd9e273187f27c6830e3ffb.mp4', 'contractid2': '163058913001000054', 'guaranteeid': '163058918701000005', 'pdf1': '', 'pdf2': '', 'guaranteepdf': '', 'attachment1': '', 'attachment2': '', 'gattachment': '', 'refuse': '', 'btype': 0, 'restage': 0, 'operator': '', 'creason': '', 'addr': '广东省惠州市惠阳区淡水街道太东高地1栋20201', 'idaddr': '安徽省颖上县江口镇郭罗村韩海子104号', 'phone': '15857580787', 'wechat': '493923835', 'email': '<EMAIL>', 'artnick': '包子', 'front': '1630588691_9c2f8fbbd1d0dfd71c70fae6a15bc91e.jpeg', 'back': '1630588700_8a6c92c1f1d0a67e02b43bd241bfafac.jpeg', 'holdfront': '1630588708_c6f77957db24ca167f10c346699ac095.jpeg', 'holdback': '1630588901_d9776547bf7038ba48f4cb6f027cc833.jpeg', 'change': 0, 'cstarttime': 0, 'cendtime': 0, 'facefile': 'https://jyesign.bs2dl.yy.com/1630588395_636707c9eb53ba92ef51b6fdd9a6922d.png?token=ak_bee:s28m5Hug-9XbYfGTpZOLmn9DE5s=:1630897640', 'videofile': 'https://jyesign.bs2dl.yy.com/1630588397_db9b13e9ddd9e273187f27c6830e3ffb.mp4?token=ak_bee:pPHGNUQPuYcPREMjqAM5-HrWNz8=:1630897640', 'alipay': '13486513142', 'wechatscreenshot': '1630588998_de72a2f0dcfc39408496f0b2a109d7d2.png', 'alipayscreenshot': '1630589006_c7d39851817c484b9cbfa59fada01cc1.png', 'wechatrecordscreen': 'https://jyesign.bs2dl.yy.com/1630589036_2bd7ee44911dd5a48514f1af227ce253.mp4?token=ak_bee:0gnv1PAe7RKNBwnAO67YbeTUdLs=:1630897640', 'alipayrecordscreen': 'https://jyesign.bs2dl.yy.com/1630589043_898bc1391de41dc9fd9941a83e5d1d88.mp4?token=ak_bee:Mcjc-lGB4MZ8RlLijjiV7GginiY=:1630897640', 'corpname': '武汉宇之辰网络科技有限公司', 'contractAsid': 0 }
// ]

export default {
  namespace: 'esignAuditing', // 只有这里需要修改

  state: {
    list: [],
    profile: {},
    idCard: {}
  },

  reducers: {
    updateList (state, { payload, status }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload,
        // list: mockList,
        status: status
      }
    },
    updateProfile (state, { payload }) {
      return {
        ...state,
        profile: payload
      }
    },
    updateFileUrl (state, { payload }) {
      return {
        ...state,
        idCard: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { list, status, msg } } = yield call(getLists, payload)

      yield put({
        type: 'updateList',
        payload: Array.isArray(list) ? list : [],
        status: status
      })

      if (status !== 0) {
        message.error('failed:' + msg)
      }
    },

    * auditItem ({ payload, getListParam }, { call, put }) {
      const { data: { status, msg } } = yield call(update, payload)
      console.log('getListParam:', getListParam)
      if (status === 0) {
        message.success('操作成功')
        yield put({
          type: 'getList',
          payload: getListParam
        })
      } else {
        message.error('操作失败:' + msg, 15)
      }
    },

    * cancelContract ({ payload, getListParam }, { call, put }) {
      const { data: { status, msg } } = yield call(cancelContract, payload)
      console.log('getListParam:', getListParam)
      if (status === 0) {
        message.success('操作成功')
        yield put({
          type: 'getList',
          payload: getListParam
        })
      } else {
        message.error('操作失败:' + msg)
      }
    },

    * getProfile ({ payload }, { call, put }) {
      const { data: { profile, status, msg } } = yield call(getProfile, payload)
      console.log('profile:', profile, status)
      if (status === 0) {
        yield put({
          type: 'updateProfile',
          payload: profile
        })
      } else {
        message.error('failed:' + msg)
      }
    },

    * getFileUrl ({ payload }, { call, put }) {
      const { data: { token, status, msg } } = yield call(getFileUrl, payload)
      console.log(token, status)
      if (status === 0) {
        yield put({
          type: 'updateFileUrl',
          payload: token
        })
      } else {
        message.error('failed:' + msg)
      }
    }
  }
}
