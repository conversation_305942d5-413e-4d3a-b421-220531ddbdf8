import request from '@/utils/request'

export const doPost = function (apiPath, params) {
  return request(apiPath, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

// 查询 WEBDB 用户信息，头像、昵称、YY号等, 返回用户列表
export function getWebdbUserInfo (uidList) {
  return doPost(`/assist_tools/webdb/batch_get_userinfo`, {
    uidList: uidList
  })
}

// 获取MongoDB中的集合名称列表
export function listMongoCollections (params) {
  return doPost(`/assist_tools/mongo/list_collections`, params)
}

// 获取集合信息，包括首条记录和索引信息
export function getMongoCollectionInfo (params) {
  return doPost(`/assist_tools/mongo/get_collection_info`, params)
}

// 获取执行计划
export function getQueryExplain (params) {
  return doPost(`/assist_tools/mongo/get_query_explain`, params)
}

// 分页查询集合文档
export function getMongoDocsPage (params) {
  return doPost(`/assist_tools/mongo/list_collection_docs`, params)
}

// 查询主持详情
export function getCompereDetailInfo (params) {
  return doPost(`/assist_tools/compere/get_compere_info`, params)
}
