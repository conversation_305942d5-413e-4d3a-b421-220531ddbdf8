import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { tableStyle } from '@/utils/common'
import { Table, Button, Form, Card, Modal, Input, Popconfirm } from 'antd'
import { connect } from 'dva'
const namespace = 'punishChannel'
const FormItem = Form.Item

@connect(({ punishChannel }) => ({
  model: punishChannel
}))

class Index extends Component {
  // column structs.
  columns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: '频道', dataIndex: 'sid', key: 'sid', align: 'center' },
    { title: '子频道', dataIndex: 'ssid', key: 'ssid', align: 'center' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <Popconfirm title={'确认删除sid ' + record.sid} type='primary' onConfirm={this.handleDelOne(record)} okText='是的' cancelText='暂不'>
          <a href=''>删除</a>
        </Popconfirm>
      )
    }
  ]

  state = {
    visible: false,
    isUpdate: false,
    confirmVisible: false,
    deleteConfirmMsg: '',
    value: { sid: '', ssid: '' }
  }

  // show modal
  showModal = (record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, title: '添加频道' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = () => {
    this.formRef.submit()
  }

  // add and update
  onFinish = values => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/addItem`,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  handleDelOne = record => e => {
    const { dispatch } = this.props
    const data = { sid: record.sid, ssid: record.ssid }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = { type: this.state.type }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  // content
  render () {
    const { route, model: { list } } = this.props
    const { visible, isUpdate, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Button type='primary' onClick={this.showModal(this.state.value)}>添加</Button>
            <Table rowKey={(record, index) => index} dataSource={list} columns={this.columns} pagination={tableStyle} />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} {...formItemLayout} ref={form => { this.formRef = form }}>
            <FormItem label='频道' name='sid' rules={[{ required: true }]}>
              <Input readOnly={isUpdate} />
            </FormItem>
            <FormItem label='子频道' name='ssid'>
              <Input readOnly={isUpdate} />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default Index
