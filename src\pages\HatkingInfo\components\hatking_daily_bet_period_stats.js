import React, { Component } from 'react'
import { Table, Divider, Button, Card, Form, DatePicker } from 'antd'
import { connect } from 'dva'
import exportExcel from '@/utils/exportExcel'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
const gameMap = { 0: 'ALL', 1: '武器大师', 2: '热血屠龙', 3: '星球漫步', 4: '明星衣橱', 5: '活动玩法' }
const chanMap = { all: 'ALL', pc: 'PC端', zhuiwan: 'YO交友', yomi: 'YO语音' }

let periodRangeMap = {}
let periodRangeFilter = []
for (let i = 0; i < 24; i++) {
  let label = '[' + (i).toString() + ',' + (i + 1).toString() + ')'
  periodRangeFilter.push({ text: label, value: i })
  periodRangeMap[i] = label
}

@connect(({ hatkingJy }) => ({ // model 的 namespace
  model1: hatkingJy // model 的 namespace
}))
class HatKingDailyBetPeriodStatsComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      list: [],
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
  }

  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '玩法类型', dataIndex: 'arenaId', align: 'center', render: text => { return gameMap[text] }, filters: [{ text: 'ALL', value: 0 }, { text: '武器大师', value: 1 }, { text: '热血屠龙', value: 2 }, { text: '星球漫步', value: 3 }, { text: '明星衣橱', value: 4 }, { text: '活动玩法', value: 5 }], defaultFilteredValue: ['0'], onFilter: (value, record) => record.arenaId === value },
    { title: '渠道类型', dataIndex: 'platform', align: 'center', render: text => { return chanMap[text] }, filters: [{ text: 'ALL', value: 'all' }, { text: 'PC端', value: 'pc' }, { text: 'YO交友', value: 'zhuiwan' }, { text: 'YO语音', value: 'yomi' }], defaultFilteredValue: ['all'], onFilter: (value, record) => record.platform.includes(value) },
    { title: '参与玩法时段', dataIndex: 'rangeId', align: 'center', render: text => { return periodRangeMap[text] }, filters: periodRangeFilter, onFilter: (value, record) => record.rangeId === value },
    { title: '模拟流水(元)', dataIndex: 'betAmethyst', align: 'center' },
    { title: '成功流水(元)', dataIndex: 'betRewardAmethyst', align: 'center' },
    { title: '模拟发放占比', dataIndex: 'betRewardRatio', align: 'center' },
    { title: '参与用户数', dataIndex: 'betUser', align: 'center' },
    { title: '成功用户数', dataIndex: 'betRewardUser', align: 'center' },
    { title: '累计成功用户数', dataIndex: 'profitUser', align: 'center' },
    { title: '累计失败用户数', dataIndex: 'lossUser', align: 'center' }
  ]

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    console.log(data)
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getPeriodRangeList`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  onExport = () => {
    let headers = []
    let columns = this.columns
    columns.forEach(function (item) {
      headers.push({ key: item.dataIndex, header: item.title })
    })

    const { model: { periodRangeList } } = this.props
    var exportData = periodRangeList.map(item => {
      let v = $.extend(true, {}, item)
      v.arenaId = gameMap[v.arenaId]
      v.platform = chanMap[v.platform]
      v.rangeId = periodRangeMap[v.rangeId]
      return v
    })

    exportExcel(headers, exportData)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { periodRangeList } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form>
          <span style={{ marginLeft: 10 }}>时间范围:</span>
          <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
          <Divider />
          <Table dataSource={periodRangeList} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default HatKingDailyBetPeriodStatsComponent
