import { getLists, add, remove, update, search } from './api'
import { message } from 'antd'

export default {
  namespace: 'partnerGuild',

  state: { list: [] },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getLists)

      yield put({
        type: 'updateList',
        // payload: list.length !== 0 ? list : [],为空时会出错，所以不能用这个，要用下面的判断
        payload: Array.isArray(list) ? list : []
      })
    },

    * addItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(add, payload)
      if (status === 0) {
        message.success('add success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * updateItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(update, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * removeItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(remove, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * searchItem ({ payload }, { call, put }) {
      const { data: { status, list, msg, start, stop } } = yield call(search, payload)
      if (status === 0) {
        message.success('search success')
        console.log(list)
        yield put({
          type: 'updateList',
          payload: Array.isArray(list) ? list : [],
          start: start,
          stop: stop
        })
      } else {
        message.error('failed' + msg)
      }
    }
  }
}
