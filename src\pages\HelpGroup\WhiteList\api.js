import request from '@/utils/request'
import { stringify } from 'qs'

// 发送白名单列表
export function listSendWhiteList (params) {
  return request(`/fts_hgame/boss/help_group/list_send_white_list?${stringify(params)}`)
}

// 接收白名单列表
export function listRecvWhiteList (params) {
  return request(`/fts_hgame/boss/help_group/list_recv_white_list?${stringify(params)}`)
}

// 数据统计列表
export function listStatistics (params) {
  return request(`/fts_hgame/boss/help_group/list_statistics?${stringify(params)}`)
}

// 删除接收白名单
export function deleteWhiteList (params) {
  return request(`/fts_hgame/boss/help_group/delete_white_list?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 检测符合uid列表
export function checkWhiteList (params) {
  return request(`/fts_hgame/boss/help_group/check_white_list?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 发起审核
export function approvalWhiteList (params) {
  return request(`/fts_hgame/boss/help_group/approval_white_list?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}
