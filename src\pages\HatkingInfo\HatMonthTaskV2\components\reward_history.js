import {
  Card,
  Form,
  Table,
  Divider, Button, Input, DatePicker, Tooltip
} from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import exportExcel from '@/utils/exportExcel'
import moment from 'moment'

const namespace = 'hatMonthTaskv2'
const getListUri = `${namespace}/listRewardHistory`

@connect(({ rewardHistory }) => ({
  model: rewardHistory
}))

class RewardHistory extends Component {
  constructor (props) {
    super(props)

    const { dataInfo } = props
    this.state = { list: dataInfo, searchASID: 0, searchTaskMonth: '' }
  }

  componentDidMount () {
    const { dispatch } = this.props
    const { searchASID, searchTaskMonth } = this.state
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    let data = { asid: searchASID, month: searchMonthTmp }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  onClick = () => {
    const { dispatch } = this.props
    const { searchASID, searchTaskMonth } = this.state
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    let data = { asid: searchASID, month: searchMonthTmp }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  componentWillReceiveProps (nextProps) {
    const { dataInfo } = nextProps
    this.setState({ list: dataInfo })
  }

  columns = [
    { title: '任务时间', width: 40, dataIndex: 'month' },
    { title: '实际生效asid', dataIndex: 'guildAsid' },
    { title: '经营asid', dataIndex: 'asid' },
    { title: '签约asid', dataIndex: 'contractAsid' },
    { title: '厅ssid', width: 50, dataIndex: 'ssid' },
    { title: '厅名', width: 50, dataIndex: 'tingName', render: (v, r) => (r.tingName.length > 20 ? <Tooltip title={r.tingName}>{ r.tingName.substring(0, 20) + '...'}</Tooltip> : r.tingName) },
    { title: '厅盖章流水/元', dataIndex: 'tingSealAmount', align: 'center' },
    { title: '公会礼物流水/元', dataIndex: 'guildGiftAmount', align: 'center' },
    { title: '达标阶段', width: 50, dataIndex: 'reachStep' },
    { title: '【系统结算】奖励金额/元', width: 50, dataIndex: 'settlementReward' },
    { title: '【配置】发奖规则', width: 50, dataIndex: 'awardRule' },
    { title: '【配置】达标奖励/元', width: 50, dataIndex: 'adjustReward' },
    { title: '最终发奖规则', width: 50, dataIndex: 'finalRule' },
    { title: '最终发奖金额/元', width: 50, dataIndex: 'finalReward' },
    { title: '备注', width: 50, dataIndex: 'systemRemark', render: (text, record) => (text === '' ? '无' : text) }
  ].map(item => {
    item.align = 'center'
    item.ellipsis = true
    return item
  })

  pagination = {
    pageSizeOptions: ['10', '20', '50', '100'],
    showSizeChanger: true,
    defaultPageSize: 20,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  getFilterList = () => {
    const { list } = this.state
    const { searchASID, searchTaskMonth } = this.state
    let filterList = list
    if (parseInt(searchASID) > 0) {
      filterList = filterList.filter((v) => { return v.asid === parseInt(searchASID) || v.guildAsid === parseInt(searchASID) || v.contractAsid === parseInt(searchASID) })
    }
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    if (parseInt(searchMonthTmp) > 0) {
      filterList = filterList.filter((v) => { return parseInt(v.month) === parseInt(searchMonthTmp) })
    }
    return filterList
  }

  onExport = () => {
    let list = this.getFilterList()

    let exportData = list.map(item => {
      let v = $.extend(true, {}, item)
      return v
    })
    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.export === undefined || col.export) {
        exportHeader.push({ key: col.dataIndex, header: col.title })
      }
    })
    let fileName = '盖章月度任务发奖明细-' + moment().format('YYYYMMDD') + '.xlsx'
    exportExcel(exportHeader, exportData, fileName)
  }

  render () {
    return (
      <Card>
        <Form>
          <Divider type='vertical' />
          任务时间：
          <DatePicker
            format='YYYY-MM'
            picker='month'
            placeholder='任务时间'
            onChange={(v) => this.setState({ searchTaskMonth: v })}
            style={{ width: '8em', marginRight: '1em' }}
          />
          短位ID：
          <Input style={{ width: '12em', marginRight: '1em' }} placeholder='请输入' onChange={(e) => { this.setState({ 'searchASID': e.target.value }) }} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onExport}>导出</Button>
          <Divider type='vertical' />
          <Table style={{ marginTop: 10 }} dataSource={this.getFilterList()} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} scroll={{ x: 'max-content' }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default RewardHistory
