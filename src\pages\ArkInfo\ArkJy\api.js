import request from '@/utils/request'
import { stringify } from 'qs'

export function getArkCrucialInfo (params) { // 关键信息
  return request(`/aggregator/lottery_ark_crucial_info?${stringify(params)}`)
}

export function getArkPaidRangeInfo (params) { // 参与用户区间分布
  return request(`/aggregator/get_ark_paid_range_info?${stringify(params)}`)
}

export function getArkPeriodRangeInfo (params) { // 分时段监控
  return request(`/aggregator/get_ark_period_range_info?${stringify(params)}`)
}

export function getArkPositionStatsInfo (params) { // 参与位置统计信息
  return request(`/aggregator/get_ark_position_stats_info?${stringify(params)}`)
}

export function getArkRetainStatsInfo (params) { // 留存率
  return request(`/aggregator/get_ark_retention_info?${stringify(params)}`)
}

export function getArkTopNUserInfo (params) { // 留存率
  return request(`/aggregator/get_ark_top_10_user?${stringify(params)}`)
}
