import { connect } from 'dva'
import React, { Component } from 'react'
import <PERSON><PERSON>eaderWrapper from '@/components/PageHeaderWrapper'
import { Card } from 'antd'
import WhiteList from './Tabs/whiteList'
import ApprovalList from './Tabs/approval'
import RewardList from './Tabs/reward'
import EventLogs from './Tabs/eventLog'
import StatExport from './Tabs/stat'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'

const namespace = 'hgameTingTools'

@connect(({ hgameTingTools }) => ({
  model: hgameTingTools
}))

class TingTools extends Component {
  componentDidMount = () => {
    this.getPkConfig()
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  getPkConfig = () => {
    this.callModel('getTingToolConfig')
  }

  render () {
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name} >
        <Card>
          <Tabs id='tingpk' defaultActiveKey='approval' type='card' size='small'>
            <TabPane tab='活动审批' key='approval'>
              <ApprovalList />
            </TabPane>
            <TabPane tab='奖励列表' key='reward'>
              <RewardList />
            </TabPane>
            <TabPane tab='厅战资格白名单' key='whiteList'>
              <WhiteList />
            </TabPane>
            <TabPane tab='事件日志' key='eventLog'>
              <EventLogs />
            </TabPane>
            <TabPane tab='活动报表' key='stat'>
              <StatExport />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default TingTools
