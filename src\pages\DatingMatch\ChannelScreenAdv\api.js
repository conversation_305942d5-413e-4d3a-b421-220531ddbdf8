
import request from '@/utils/request'
import { stringify } from 'qs'

export function getConfigList (params) {
  var url = ['box_advertisement', 'channel_advertisement', 'charge_advertisement'][params.request]
  return request(`/dating_match/${url}`, { jsonp: true })
}

export function add (params) {
  var url = ['add_box_adv', 'add_channel_adv', 'add_charge_adv'][params.request]
  return request(`/dating_match/${url}?${stringify(params)}`, { jsonp: true })
}

export function update (params) {
  var url = ['update_box_adv', 'update_channel_adv', 'update_charge_adv'][params.request]
  return request(`/dating_match/${url}?${stringify(params)}`, { jsonp: true })
}

export function remove (params) {
  var url = ['del_box_adv', 'del_channel_adv', 'del_charge_adv'][params.request]
  return request(`/dating_match/${url}?${stringify(params)}`, { jsonp: true })
}
