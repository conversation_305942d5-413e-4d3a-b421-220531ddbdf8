import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const getActivityConfigList = genGetRequireTemplate('/group_activity/boss/v3/get_activity_config', 'configList')
const udpateActivityConfig = genUpdateTemplate('/group_activity/boss/v3/activity_config/update')
const insertActivityConfig = genUpdateTemplate('/group_activity/boss/v3/activity_config/insert')
const deleteActivityConfig = genUpdateTemplate('/group_activity/boss/v3/activity_config/delete')
const queryNick = genGetRequireTemplate('/server_debug/get_yy_nick', 'userNick')

export default {
  namespace: 'groupActivityv3',
  state: {
    list: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getActivityConfigList,
    udpateActivityConfig,
    insertActivityConfig,
    deleteActivityConfig,
    queryNick
  }
}
