import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import dateString from '@/utils/dateString'
import { Card, Table, message, Input, Modal, Form, Popover, Button, InputNumber } from 'antd'
import { stringify } from 'qs'

const namespace = 'starCompereInfo'

const { TextArea } = Input

@connect(({ starCompereInfo }) => ({
  model: starCompereInfo
}))

class StarCompereInfo extends Component {
  constructor (props) {
    super(props)

    this.refreshInfo()
  }

  columns = [
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: 'YY号', dataIndex: 'yy', align: 'center' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: '签约短号频道', dataIndex: 'asid', align: 'center' },
    { title: '房管UID', dataIndex: 'roomMgrUid', align: 'center' },
    { title: '房管YY号', dataIndex: 'roomMgrYy', align: 'center' },
    { title: '绑定子频道', dataIndex: 'bindSsid', align: 'center' },
    { title: '子频道名称', dataIndex: 'ssidName', align: 'center' },
    { title: '状态', dataIndex: 'status', align: 'center', render: (text, record) => (record.status === undefined || record.status === null || record.status === 0 ? '未生效' : '生效中') },
    { title: '操作人', dataIndex: 'optUid', align: 'center' },
    { title: '申请时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (record.timestamp === 0 ? '' : dateString(record.timestamp)) },
    { title: '操作', key: 'operation', align: 'center', render: (text, record) => (<span><Popover content={this.renderContent(record)} trigger='click'><a>删除</a></Popover></span>) }
  ]

  state = {
    visible: false,
    hadClickCheck: false,
    conformUidNum: 0,
    unConformUidNum: 0
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  refreshInfo = () => {
    const { searchUID, searchYY, searchNick, searchASID, searchRoomMgrUID, searchRoomMgrYY } = this.state

    let data = { uid: searchUID, yy: searchYY, nick: searchNick, asid: searchASID, roomMgrUid: searchRoomMgrUID, roomMgrYy: searchRoomMgrYY }
    console.log(data)
    this.props.dispatch({
      type: `${namespace}/listInfo`,
      payload: data
    })
  }

  renderContent = (record) => {
    return (
      <div>
        <Input.TextArea onChange={this.onTextChange} row={5} placeholder={'删除原因选填，最多100字符'} />
        <Button onClick={this.deleteHandle(record)} style={{ marginLeft: 120, marginTop: 5 }} type='primary'>确定</Button>
      </div>
    )
  }

  onTextChange = e => {
    this.setState({ removeReason: e.target.value })
  }

  deleteHandle = (record) => () => {
    const { removeReason } = this.state
    const { dispatch } = this.props
    let data = { uid: record.uid, roomMgrUid: record.roomMgrUid, bindSid: record.bindSid, bindSsid: record.bindSsid, removeReason: removeReason }
    console.log(data)
    dispatch({
      type: `${namespace}/deleteInfo`,
      payload: data
    })
  }

  showModel = () => () => {
    this.setState({ visible: true })
  }

  searchHandle = () => () => {
    this.refreshInfo()
  }

  exportURL = () => {
    const { searchUID, searchYY, searchNick, searchASID, searchRoomMgrUID, searchRoomMgrYY } = this.state

    let params = { uid: searchUID, yy: searchYY, nick: searchNick, asid: searchASID, roomMgrUid: searchRoomMgrUID, roomMgrYy: searchRoomMgrYY, export: 1 }

    return `/new_compere_info/boss/star_compere/list?${stringify(params)}`
  }

  hiddenModal = () => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.setState({ visible: false, inputUIDList: '', conformUidList: [], hadClickCheck: false, conformUidNum: 0, unConformUidNum: 0 })
  }

  handleCancel = e => {
    this.hiddenModal()
  }

  handleSubmit = e => {
    if (this.formRef) {
      this.formRef.submit()
    }
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onFinish = values => {
    const { conformUidList, hadClickCheck } = this.state
    const { dispatch } = this.props
    console.log(values, conformUidList)

    if (!hadClickCheck) {
      message.warning('未检测结果, 请先检测结果')
      return
    }

    if (conformUidList === null || conformUidList === undefined || conformUidList === '') {
      message.warning('没有符合的uid添加')
      return
    }

    let data = { roomMgrUid: Number(values.roomMgrUid), bindSsid: Number(values.bindSsid), uidList: conformUidList }
    console.log(data)
    dispatch({
      type: `${namespace}/addInfo`,
      payload: data
    })

    this.hiddenModal()
  }

  checkHandle = () => () => {
    const { dispatch } = this.props
    const { inputUIDList, roomMgrUidInput, bindSsidInput } = this.state

    if (inputUIDList === undefined || inputUIDList === '') {
      message.warning('请输入要修改的UID')
      return
    }
    if (roomMgrUidInput === null || roomMgrUidInput === undefined || roomMgrUidInput === '') {
      message.warning('请输入房管uid')
      return
    }
    if (bindSsidInput === null || bindSsidInput === undefined || bindSsidInput === '') {
      message.warning('请输入绑定子频道')
      return
    }

    let conformUidList = []
    if (inputUIDList && inputUIDList.length > 0) {
      let uidListTmp = inputUIDList.split('\n')
      for (let i = 0; i < uidListTmp.length; i++) {
        const uid = Number(uidListTmp[i])
        conformUidList.push(uid)
      }
    }

    let updateUIDInfo = (conformUid, unConformUid) => {
      let conformUidNumTmp = 0
      let unConformUidNumTmp = 0
      if (this.formRef) {
        let conformUidStr = ''
        if (conformUid && conformUid.length > 0) {
          conformUidStr = conformUid.join('\n')
          conformUidNumTmp = conformUid.length
        }
        let unConformUidStr = ''
        if (unConformUid && unConformUid.length > 0) {
          unConformUidStr = unConformUid.join('\n')
          unConformUidNumTmp = unConformUid.length
        }
        this.formRef.setFieldsValue({ conformUid: conformUidStr, unConformUid: unConformUidStr })
        this.setState({ conformUidList: conformUid, hadClickCheck: true, conformUidNum: conformUidNumTmp, unConformUidNum: unConformUidNumTmp })
      }
    }

    dispatch({
      type: `${namespace}/checkInfo`,
      payload: { play: { roomMgrUid: Number(roomMgrUidInput), bindSsid: Number(bindSsidInput), uidList: conformUidList }, func: updateUIDInfo }
    })
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      var value = selectedRows.map(item => item.familyId).join(',')
      this.setState({ removeKey: value })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User',
      name: record.name
    })
  }

  render () {
    const { visible, searchRoomMgrYY, conformUidNum, unConformUidNum } = this.state
    const { route, model: { list } } = this.props

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 10 }
      },
      wrapperCol: {
        xs: { span: 2 },
        sm: { span: 15 }
      }
    }

    console.log(searchRoomMgrYY)
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <div><Button type='primary' onClick={this.showModel()}>添加</Button></div>
          <div style={{ marginTop: 10 }} />
          <span id={'cities'}>UID</span>
          <InputNumber min={0} onChange={e => this.setState({ searchUID: e })} style={{ width: 150, marginLeft: 3 }} />
          <span style={{ marginLeft: 15 }}>YY</span>
          <InputNumber min={0} onChange={e => this.setState({ searchYY: e })} style={{ width: 150, marginLeft: 3 }} />
          <span style={{ marginLeft: 15 }}>短位频道</span>
          <InputNumber min={0} onChange={e => this.setState({ searchASID: e })} style={{ width: 150, marginLeft: 3 }} />
          <span style={{ marginLeft: 15 }}>昵称</span>
          <Input onChange={e => this.setState({ searchNick: e.target.value })} style={{ width: 150, marginLeft: 3 }} />
          <span style={{ marginLeft: 15 }}>房管UID</span>
          <InputNumber min={0} onChange={e => this.setState({ searchRoomMgrUID: e })} style={{ width: 150, marginLeft: 3 }} />
          <span style={{ marginLeft: 15 }}>房管YY</span>
          <InputNumber min={0} onChange={e => this.setState({ searchRoomMgrYY: e })} style={{ width: 150, marginLeft: 3 }} />

          <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
          <Button style={{ marginLeft: 20 }} type='primary'><a href={this.exportURL()} target='_blank'>导出</a></Button>
          <Table style={{ marginTop: 10 }} tabelLayout='fixed' rowSelection={this.rowSelection} rowKey={(record, index) => index} bordered dataSource={list} columns={this.columns} pagination={this.defaultPageValue} />
        </Card>

        <Modal forceRender width={500} visible={visible} title='添加星光主持' onCancel={this.handleCancel} onOk={this.handleSubmit} okText='提交审核'>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <Form.Item label='房管UID' name='roomMgrUid' rules={[{ required: true }]}>
              <Input list='browsers' onChange={e => this.setState({ roomMgrUidInput: e.target.value })} style={{ width: 150, marginLeft: 3 }} />
            </Form.Item>
            <Form.Item label='绑定ssid' name='bindSsid' rules={[{ required: true }]}>
              <Input onChange={e => this.setState({ bindSsidInput: e.target.value })} style={{ width: 150, marginLeft: 3 }} />
            </Form.Item>
            <Form.Item label='主持UID' name='uidList' rules={[{ required: true }]}>
              <TextArea rows={4} placeholder='UID(回车换行)' onChange={e => this.setState({ inputUIDList: e.target.value })} />
            </Form.Item>
            <Button style={{ marginLeft: 350 }} type='primary' onClick={this.checkHandle()}>检查结果</Button>
            <div style={{ marginTop: 10 }} />
            <Form.Item label='符合要求的UID' name='conformUid'>
              <TextArea readOnly rows={4} />
            </Form.Item>
            <div style={{ marginLeft: 400 }}>数量:{conformUidNum}</div>
            <Form.Item label='不符合要求的UID' name='unConformUid'>
              <TextArea disabled rows={4} />
            </Form.Item>
            <div style={{ marginLeft: 400 }}>数量:{unConformUidNum}</div>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default StarCompereInfo
