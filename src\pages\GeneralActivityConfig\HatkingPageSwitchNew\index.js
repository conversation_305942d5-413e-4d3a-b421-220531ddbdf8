import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
// import LineWrap from '@/components/LineWrapper'
import { Table, Card, Form, Input, Modal, DatePicker, Select, Row, Col, message } from 'antd'
import { connect } from 'dva'
import { Editor } from '@tinymce/tinymce-react'
import dateString from '@/utils/dateString'

var moment = require('moment')
const namespace = 'hatkingPageSwitch'
const getListUri = `${namespace}/getList`
const updateItemUri = `${namespace}/updateItem`
const { RangePicker: DateRangePicker } = DatePicker
const FormItem = Form.Item
const { Option } = Select

@connect(({ hatkingPageSwitch }) => ({
  model: hatkingPageSwitch
}))

class HatkingPageSwitch extends Component {
  // 列表结构
  columns = [
    { title: '配置名', dataIndex: 'activityName', width: 150, align: 'center' },
    { title: '是否开启', dataIndex: 'isOpen', width: 80, align: 'center', render: (text, record) => (text === 0 ? '否' : '是') },
    { title: '开始时间', dataIndex: 'startTime', width: 150, align: 'center', render: (text, record) => (text === 0 ? '无' : dateString(text)) },
    { title: '结束时间', dataIndex: 'endTime', width: 150, align: 'center', render: (text, record) => (text === 0 ? '无' : dateString(text)) },
    { title: '活动配置', dataIndex: 'activityConfig', width: 200, align: 'left', render: (text, record) => this.renderDetail(record) },
    // { title: '活动说明', dataIndex: 'activityTips', width: 200, align: 'center', render: text => (<LineWrap title={text} lineClampNum={1} />) },
    { title: '查看更多URL', dataIndex: 'moreUrl', width: 200, align: 'center' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      width: 100,
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(true, record)}>更新</a>
        </span>)
    }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, editorContent: '' }

  // 获取列表
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: getListUri
    })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  renderDetail (record) {
    let content = null
    if (record.rankName1.length && record.compereList1.length) {
      content = <div>榜单1-名称：{record.rankName1} <br />榜单1-主持：{record.compereList1}</div>
    }

    if (record.rankName2.length && record.compereList2.length) {
      content = <div>{content} 榜单2-名称：{record.rankName2} <br /> 榜单2-主持：{record.compereList2}</div>
    }

    if (record.rankName3.length && record.compereList3.length) {
      content = <div>{content} 榜单3-名称：{record.rankName3} <br /> 榜单3-主持：{record.compereList3}</div>
    }

    if (record.rankName4.length && record.compereList4.length) {
      content = <div>{content} 榜单4-名称：{record.rankName4} <br /> 榜单4-主持：{record.compereList4}</div>
    }

    return <span>{content}</span>
  }

  // 添加 与 编辑
  onFinish = values => {
    const { dispatch } = this.props
    values.startTime = parseInt(values.dateRange[0].unix() / 60) * 60
    values.endTime = parseInt(values.dateRange[1].unix() / 60) * 60

    var rankCount = 0
    if (values.compereList1.length && values.rankName1.length) {
      rankCount++
    }

    if (values.compereList2.length && values.rankName2.length) {
      rankCount++
    }

    if (values.compereList3.length && values.rankName3.length) {
      rankCount++
    }

    if (values.compereList4.length && values.rankName4.length) {
      rankCount++
    }

    console.log(values)

    if (rankCount === 0) {
      message.error('至少要配置一个榜单!')
      return
    }

    dispatch({
      type: updateItemUri,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 显示弹窗
  showModal = (isUpdate, record) => () => {
    var editorContent = ''
    let v = $.extend(true, {}, record)
    if (isUpdate) {
      v.dateRange = [moment.unix(v.startTime), moment.unix(v.endTime)]
    }

    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(v)
    }

    // console.log(v)
    this.setState({ visible: true, value: record, isUpdate: isUpdate, editorContent: editorContent, title: '更新模块开关' })
  }

  // 关闭弹窗
  hidModal = () => {
    this.setState({ visible: false })
  }

  saveFormRef = formRef => {
    this.formRef = formRef
  }

  getValue = e => {
    // console.log(e)
    return e.target.getContent()
  }

  render () {
    const { route, model: { list } } = this.props
    const { visible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    const formLayoutCol1 = {
      labelCol: {
        xs: { span: 10 },
        sm: { span: 10 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 12 }
      }
    }

    const formLayoutCol2 = {
      labelCol: {
        xs: { span: 6 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 12 }
      }
    }

    // const TinymceSrc = 'https://cdnjs.cloudflare.com/ajax/libs/tinymce/5.4.1/tinymce.min.js'
    const TinymceSrc = `https://jy${ENV_TAG !== 'prod' ? '-test' : ''}.yystatic.com/jy/jy-boss-201809-feat-pc/tinymce/5.4.1/tinymce.min.js`

    const editorConfiguration = {
      menubar: false,
      branding: false,
      statusbar: false,
      elementpath: false,
      plugins: 'paste autolink visualchars link hr nonbreaking toc insertdatetime advlist lists noneditable preview image',
      toolbar: 'undo redo | bold italic underline strikethrough | outdent indent numlist bullist forecolor removeformat link image preview fontsizeselect',
      toolbar_drawer: 'sliding',
      automatic_uploads: true,
      paste_data_images: true,
      images_reuse_filename: true,
      fontsize_formats: '12px 13px 14px 16px 18px 20px',
      images_upload_handler: function (file, success, failure, progress) {
        var xhr, formData

        // eslint-disable-next-line no-undef
        xhr = new XMLHttpRequest()
        xhr.withCredentials = false
        xhr.open('POST', 'https://fts.yy.com/fs/uploadfiles')

        xhr.upload.onprogress = function (e) {
          progress(e.loaded / e.total * 100)
        }

        xhr.onload = function () {
          if (xhr.status < 200 || xhr.status >= 300) {
            failure('HTTP Error: ' + xhr.status)
            return
          }

          var json = JSON.parse(xhr.responseText)
          if (!json) {
            failure('Invalid JSON: ' + xhr.responseText)
            return
          }

          json.location = json.urls[0]
          success(json.location)
        }

        xhr.onerror = function () {
          failure('Image upload failed due to a XHR Transport error. Code: ' + xhr.status)
        }

        // eslint-disable-next-line no-undef
        formData = new FormData()
        formData.append('files', file.blob(), file.filename())
        formData.append('bucket', 'makefriends')
        xhr.send(formData)
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Table rowKey='activityName' dataSource={list} columns={this.columns} pagination={this.defaultPageValue} size='small' />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hidModal} onOk={this.handleSubmit} width='60%'>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem name='id' hidden>
              <Input hidden />
            </FormItem>
            <Row>
              <Col span={12}>
                <FormItem label='配置名' name='activityName' rules={[{ required: true }]} {...formLayoutCol1}>
                  <Input />
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label='是否开启' name='isOpen' rules={[{ required: true }]} {...formLayoutCol2}>
                  <Select><Option value={0}>否</Option><Option value={1}>是</Option></Select>
                </FormItem>
              </Col>
            </Row>
            <FormItem label='活动时间' name='dateRange' rules={[{ type: 'array', required: true, message: 'Please select time!' }]}>
              <DateRangePicker style={{ width: '100%' }} showTime format='YYYY-MM-DD HH:mm' />
            </FormItem>
            <FormItem label='榜单1-名称' name='rankName1' rules={[{ type: 'string', max: 4, message: '最多4个字' }]}>
              <Input placeholder='最多4个字' />
            </FormItem>
            <FormItem label='榜单1-主持' name='compereList1' rules={[{ pattern: /^\d+(,\d+)*$/, message: '多个主持以英文逗号(,)分隔' }]}>
              <Input placeholder='多个主持以英文逗号(,)分隔' />
            </FormItem>
            <FormItem label='榜单2-名称' name='rankName2' rules={[{ type: 'string', max: 4, message: '最多4个字' }]}>
              <Input placeholder='最多4个字' />
            </FormItem>
            <FormItem label='榜单2-主持' name='compereList2' rules={[{ pattern: /^\d+(,\d+)*$/, message: '多个主持以英文逗号(,)分隔' }]}>
              <Input placeholder='多个主持以英文逗号(,)分隔' />
            </FormItem>
            <FormItem label='榜单3-名称' name='rankName3' rules={[{ type: 'string', max: 4, message: '最多4个字' }]}>
              <Input placeholder='最多4个字' />
            </FormItem>
            <FormItem label='榜单3-主持' name='compereList3' rules={[{ pattern: /^\d+(,\d+)*$/, message: '多个主持以英文逗号(,)分隔' }]}>
              <Input placeholder='多个主持以英文逗号(,)分隔' />
            </FormItem>
            <FormItem label='榜单4-名称' name='rankName4' rules={[{ type: 'string', max: 4, message: '最多4个字' }]}>
              <Input placeholder='最多4个字' />
            </FormItem>
            <FormItem label='榜单4-主持' name='compereList4' rules={[{ pattern: /^\d+(,\d+)*$/, message: '多个主持以英文逗号(,)分隔' }]}>
              <Input placeholder='多个主持以英文逗号(,)分隔' />
            </FormItem>
            <FormItem getValueFromEvent={this.getValue} label='活动说明' name='activityTips' rules={[{ required: true }]}>
              <Editor
                init={editorConfiguration}
                tinymceScriptSrc={TinymceSrc}
              />
            </FormItem>
            <FormItem label='查看更多URL' name='moreUrl' rules={[{ required: true }]}>
              <Input />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default HatkingPageSwitch
