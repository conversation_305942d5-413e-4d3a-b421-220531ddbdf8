import request from '@/utils/request'

export function getLists () {
  let url = `/white_list/video_link_channel_list`
  return request(url, { jsonp: true })
}

export function whiteListAdd (sid) {
  let form = 'sid=' + sid
  return request(`/white_list/video_link_channel_add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: form
  })
}

export function whiteListDel (sid) {
  let form = 'sid=' + sid
  return request(`/white_list/video_link_channel_del`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: form
  })
}
