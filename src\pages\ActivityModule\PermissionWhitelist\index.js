import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Popconfirm, Modal, Input } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const namespace = 'activityModuleWhitelist'
// const Search = Input.Search
const FormItem = Form.Item

@connect(({ activityModuleWhitelist }) => ({
  activityModuleWhitelist
}))
class ActivityModuleWhitelist extends Component {
  // column structs.
  columns = [
    { title: '频道', dataIndex: 'asid', key: 'asid', align: 'center' },
    { title: 'OW YY号', dataIndex: 'ow_imid', align: 'center' },
    { title: '昵称', dataIndex: 'ow_nick', align: 'center' },
    { title: '有效天数', dataIndex: 'effective_days', align: 'center' },
    { title: '每日有效时间(h)', dataIndex: 'top_limit_daily' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(true, record)}>更新</a><Divider type='vertical' />
          <Popconfirm title='确认?' onConfirm={this.handleDel(record.sid)}>
            <a href=''>删除</a>
          </Popconfirm>
        </span>)
    }
  ]

  state = { visible: false, isUpdate: false, value: {} }

  // show modal
  showModal = (isUpdate, record) => () => {
    var now = moment().unix()
    if (record == null) record = { key: '', desp: '', beginTime: now, endTime: now }

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? '更新' : '添加' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = () => {
    this.formRef.submit()
  }

  // add and update
  onFinish = values => {
    const { dispatch } = this.props
    const { isUpdate } = this.state
    const url = isUpdate ? `${namespace}/updateItem` : `${namespace}/addItem`
    dispatch({
      type: url,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { id: key }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // search
  handleSearch = (key) => {
    const { dispatch } = this.props
    const data = { key: key }
    dispatch({
      type: `${namespace}/getItemByKey`,
      payload: data
    })
  }

  // get list from server.
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`
    })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  // content
  render () {
    const { route, activityModuleWhitelist: { list } } = this.props
    const { visible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Button type='primary' onClick={this.showModal(false)}>添加权限</Button>
            <Table dataSource={list} columns={this.columns} />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} {...formItemLayout} ref={form => { this.formRef = form }}>
            <FormItem label='频道号' name='id' rules={[{ required: true, message: 'key can not be null' }]}>
              <Input placeholder='请输入频道短位/长位Id' />
            </FormItem>
            <FormItem label='有效天数' name='effective_days' rules={[{ required: true, message: '请填写有效天数' }]}>
              <Input placeholder='请填写有效天数(整数)' />
            </FormItem>
            <FormItem label='每日上限' name='top_limit_daily' rules={[{ required: true, message: '请填写每日上限' }]}>
              <Input placeholder='请填写每日上限(0-24)' />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default ActivityModuleWhitelist
