import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/lottery_hatking/get_hatking_upgrade_config_info?${stringify(params)}`) // 修改 url 即可
}

export function update (params) {
  return request(`/lottery_hatking/update_hatking_upgrade_config?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function add (params) {
  return request(`/lottery_hatking/add_new_hatking_upgrade_config?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function remove (params) {
  return request(`/lottery_hatking/delete_hatking_upgrade_config?${stringify(params)}`)
}

export function confirm (params) {
  return request(`/lottery_hatking/confirm_hatking_upgrade_config?${stringify(params)}`)
}

export function down (params) {
  return request(`/lottery_hatking/down_hatking_upgrade_config?${stringify(params)}`)
}
