
//  7 聊天室 6 交友
export const businessTypeOptisons = [
  { label: '全部', value: null },
  { label: '汇总', value: 1000 },
  { label: '交友', value: 6 },
  { label: '聊天室', value: 7 }
]

export const businessTypeOptisonsV2 = [
  { label: '交友', value: 6 },
  { label: '聊天室', value: 7 }
]

export const businessTypeFormater = (v) => {
  return businessTypeOptisons.find(item => { return item.value === v })?.label
}

export const productTypeOptions = [
  { label: '全部', value: null },
  { label: '汇总', value: 1000 },
  { label: '抢空投', value: 30 },
  { label: '物资大战', value: 31 },
  { label: '抢物资', value: 32 },
  { label: '幸运小狗', value: 33 },
  { label: '海底秘境', value: 34 },
  { label: '银河漫游', value: 35 },
  { label: '丘比特', value: 36 },
  { label: '诺亚方舟', value: 37 },
  { label: '超能运动会', value: 38 },
  { label: '桃花签', value: 39 },
  { label: '明星衣橱/钢铁统帅', value: 40 },
  { label: '帽子活动玩法', value: 41 },
  { label: '星际巡航', value: 42 },
  { label: '初级漂流瓶', value: 43 },
  { label: '高级漂流瓶', value: 44 }
]

export const productTypeFormater = (v) => {
  return productTypeOptions.find(item => { return item.value === v })?.label
}

// 商品类型 1 头像框 2 入场秀 3 聊天气泡 4靓号 0 为所有类型
export const commodityTypeOptisons = [
  { label: '所有', value: 0 },
  { label: '头像框', value: 1 },
  { label: '入场秀', value: 2 },
  { label: '聊天气泡', value: 3 },
  { label: '4靓号', value: 4 }
]

export const getCommodityTypeOptisons = (data) => {
  const { commodityTypeList } = data
  let options = [{ label: '全部', value: 0 }]
  if (commodityTypeList) {
    commodityTypeList.map(item => {
      options.push({ label: item.name, value: item.id })
    })
  }
  return options
}

export const commodityTypeFormater = (v, data) => {
  const { commodityTypeList } = data
  let label = `${v}?`
  if (commodityTypeList) {
    commodityTypeList.forEach(item => {
      if (item.id === v) {
        label = item.name
      }
    })
  }
  return label
}

export const getConfigIdOptisons = (data, businessType) => {
  const { commodityNameListJy, commodityNameListChat } = data
  let options = [{ label: '全部', value: 0 }]

  if (commodityNameListJy && (businessType === 6 || businessType === 0)) {
    commodityNameListJy.map(item => {
      options.push({ label: item.name, value: item.id })
    })
  }

  if (commodityNameListChat && (businessType === 7 || businessType === 0)) {
    commodityNameListChat.map(item => {
      options.push({ label: item.name, value: item.id })
    })
  }

  return options
}
