import { getLists, remove, add } from './api'
import { message } from 'antd'

export default {
  namespace: 'HonorCompere', // 只有这里需要修改

  state: {
    list: []
  },

  reducers: {
    refreshList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getLists, payload)
      yield put({
        type: 'refreshList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * remove ({ payload, getListParam }, { call, put }) {
      // console.log('getListParam:', getListParam)
      const { data: { status, msg } } = yield call(remove, payload)
      if (status === 0) {
        message.success('删除成功')
        yield put({
          type: 'getList',
          payload: getListParam
        })
      } else {
        message.error('失败:' + msg)
      }
    },

    * addItem ({ payload, getListParam }, { call, put }) {
      const { data: { status, msg } } = yield call(add, payload)
      if (status === 0) {
        message.success('添加成功')
        yield put({
          type: 'getList',
          payload: getListParam
        })
      } else {
        message.error('失败:' + msg)
      }
    }
  }
}
