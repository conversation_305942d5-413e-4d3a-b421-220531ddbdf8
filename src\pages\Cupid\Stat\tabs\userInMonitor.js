/* eslint-disable no-template-curly-in-string */
import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Row, Typography, Col, Table, Space, Divider, DatePicker, Select, Button } from 'antd'
import { onExportExcel } from '@/utils/common'
import { statTypeOptions, ballTypeOptions, optionsFormater, valueFormater, flowRangeFormaterV2 } from '../statCommon'
import moment from 'moment'

const { Text } = Typography
const namespace = 'cupidStat'

@connect(({ cupidStat }) => ({
  model: cupidStat
}))

class UserInMonitor extends Component {
  state = {
    timeRange: [moment().subtract(7, 'day'), moment()],
    selectStatType: 1000,
    selectBallType: 0
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  componentDidMount = () => {
    this.queryUserIn()
  }

  // 查询数据
  queryUserIn = () => {
    const { timeRange, selectStatType, selectBallType } = this.state
    const { originID } = this.props
    const [ t1, t2 ] = timeRange

    this.callModel('getUserInData', {
      params: {
        start: t1.format('YYYYMMDD'),
        end: t2.format('YYYYMMDD'),
        statType: selectStatType,
        ballType: selectBallType,
        originId: originID
      }
    })
  }

  render () {
    const { timeRange, selectStatType, selectBallType } = this.state
    const { userInList } = this.props.model

    const columns = [
      { title: '日期', dataIndex: 'date' },
      // { title: '奖池名称', dataIndex: 'ballName' },
      { title: '奖池名称', dataIndex: 'ballType', render: (v, r) => { return optionsFormater(ballTypeOptions, v) } },
      // { title: '渠道类型', dataIndex: '' },
      { title: '参与流水区间', dataIndex: 'rangeId', render: (v, r) => { return flowRangeFormaterV2(v) } },
      { title: '奖池总产出', dataIndex: 'totalOut', render: (v) => { return valueFormater(v) } },
      { title: '用户总投入', dataIndex: 'totalIn', render: (v) => { return valueFormater(v) } },
      { title: '用户数', dataIndex: 'totalCount', render: (v) => { return valueFormater(v) } },
      { title: '发放占比', dataIndex: 'rebate', render: (v, r) => { return `${Number(r.totalOut * 100 / r.totalIn).toFixed(2)}%` } }, // 总支出/总收入
      { title: '总偏移', dataIndex: 'totalOffset', render: (v) => { return valueFormater(v) } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <Card>
        <Row>
          <Col span={24} style={{ marginBottom: '1em' }}>
            {/* 筛选项 */}
            <Space>
              <Text>时间范围:</Text>
              <DatePicker.RangePicker format='YYYY-MM-DD' value={timeRange} onChange={v => { this.setState({ timeRange: v }) }} />
              <Divider type='vertical' />

              <Text>奖池名称：</Text>
              <Select style={{ width: '7em' }} options={ballTypeOptions} value={selectBallType} onChange={(v) => this.setState({ selectBallType: v })} />

              <Text>渠道：</Text>
              <Select style={{ width: '7em' }} options={statTypeOptions} value={selectStatType} onChange={(v) => this.setState({ selectStatType: v })} />
              <Divider type='vertical' />

              <Button type='primary' onClick={() => this.queryUserIn()}>查询</Button>
              <Button disabled={userInList.length === 0}
                onClick={() => { onExportExcel(columns, userInList, '丘比特用户投入区间.xlsx') }}>导出</Button>
            </Space>
          </Col>
          <Col span={24}>
            <Table columns={columns} dataSource={userInList} />
          </Col>
        </Row>
      </Card>
    )
  }
}

export default UserInMonitor
