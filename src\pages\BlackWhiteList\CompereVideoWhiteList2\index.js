import React, { Component } from 'react'
import { connect } from 'dva'
import { Tabs, Card, Input, Form, Row, Col, Button, Table, Popconfirm } from 'antd'
import { tableStyle, Inputlayout } from '@/utils/common'
import { DeleteOutlined } from '@ant-design/icons'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import exportExcel from '@/utils/exportExcel'
import moment from 'moment/moment'

const namespace = 'compereVideoWhiteList2'

@connect(({ compereVideoWhiteList2 }) => ({
  model: compereVideoWhiteList2
}))

class TestPage extends Component {
  constructor (props) {
    super(props)
    this.state = { newUid: '', newSid: '', addReason: '', searchUID: 0, searchYY: 0, searchASID: 0, searchSID: 0, searchResult: [], searchDone: false }
    this.refreshVidioComperList()
  }

  columns = [
    { title: '#', dataIndex: 'idx' },
    { title: '短位频道', dataIndex: 'asid' },
    { title: '频道号', dataIndex: 'sid' },
    { title: '主持uid', dataIndex: 'uid' },
    { title: '主持YY', dataIndex: 'imid' },
    { title: '昵称', dataIndex: 'nick' },
    { title: '操作', export: false, render: (record) => this.deleteRowSubHtml(record) }
  ]

  // 标签页发生切换
  onTagChange = (record) => {
    // eslint-disable-next-line eqeqeq
    if (record === '2') { // 切换到列表展示页面时自动刷新列表
      this.refreshVidioComperList()
    }
    if (record === '1') { // 切换到新增标签页时初始化数据
      this.initForm()
    }
  }

  onExport = () => {
    const { model: { displayData } } = this.props
    let exportData = displayData.map(item => {
      let v = $.extend(true, {}, item)
      return v
    })
    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.export === undefined || col.export) {
        exportHeader.push({ key: col.dataIndex, header: col.title })
      }
    })
    const fileName = '视频开播权限白名单-' + moment().format('YYYYMMDD') + '.xlsx'
    exportExcel(exportHeader, exportData, fileName)
  }

  // 清空输入表单
  initForm = () => {
    this.setState({ newUid: '', newSid: '', addReason: '', searchUID: 0, searchYY: 0, searchASID: 0, searchSID: 0, searchResult: [], searchDone: false })
    if (this.formRef) {
      this.formRef.resetFields()
    }
  }

  // 获取/刷新视频主持白名单列表数据
  refreshVidioComperList = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getVidioComperData`,
      payload: null
    })
  }

  // 点击 添加标签页-添加按钮
  onAddVidioComperClick = () => {
    const { newUid, newSid, addReason } = this.state
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/addVidioComperData`,
      payload: { uid: newUid, sid: newSid, reason: addReason, callback: this.initForm }
    })
  }

  // '添加白名单'标签页html代码
  addVidioCompereHtml = () => {
    const { updating } = this.props.model
    return (
      <Row>
        <Col span='24'>
          <Form {...Inputlayout}
            initialValues={{ uid: '', sid: '', reason: '' }}
            ref={form => { this.formRef = form }}
          >
            <Form.Item label='用户ID [uid]:' name='uid'>
              <Input placeholder='如50041789'
                onChange={e => this.setState({ newUid: e.target.value })}
                maxLength={20}
              />
            </Form.Item>
            <Form.Item label='顶级频道 [sid]:' name='sid'>
              <Input placeholder='如87814665'
                onChange={e => this.setState({ newSid: e.target.value })}
                maxLength={20}
              />
            </Form.Item>
            <Form.Item label='修改原因:' name='reason'>
              <Input placeholder='请填写修改原因'
                onChange={e => this.setState({ addReason: e.target.value })}
                maxLength={50}
              />
            </Form.Item>
            <Form.Item>
              <Button type='primary' htmlType='submit' loading={updating} onClick={() => this.onAddVidioComperClick()}>
                添加
              </Button>
            </Form.Item>
          </Form>
        </Col>
      </Row>
    )
  }

  // 确认删除选中的白名单
  onComfirmDel = (record) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/delVidioComperData`,
      payload: record
    })
  }

  // search
  handleSearch = e => {
    const { model: { displayData } } = this.props
    const { searchUID, searchYY, searchSID, searchASID } = this.state
    let dataSource = displayData
    const searchUIDInt = parseInt(searchUID || 0, 10)
    const searchYYInt = parseInt(searchYY || 0, 10)
    const searchSIDInt = parseInt(searchSID || 0, 10)
    const searchASIDInt = parseInt(searchASID || 0, 10)
    if (searchUIDInt === 0 && searchSIDInt === 0 && searchASIDInt === 0 && searchYYInt === 0) {
      this.setState({ searchDone: false })
      return
    }
    console.log('handleSearch', searchYYInt)
    if (searchUIDInt > 0) {
      dataSource = dataSource.filter(v => v.uid === searchUIDInt)
    }
    if (searchYYInt > 0) {
      dataSource = dataSource.filter(v => v.imid === searchYYInt)
    }
    if (searchSIDInt > 0) {
      dataSource = dataSource.filter(v => v.sid === searchSIDInt)
    }
    if (searchASIDInt > 0) {
      dataSource = dataSource.filter(v => v.asid === searchASIDInt)
    }
    console.log('dataSource', dataSource)
    this.setState({ searchResult: dataSource, searchDone: true })
  }

  // 白名单列表-删除操作html代码
  deleteRowSubHtml = (record) => {
    let tmpStr = `确定要删除白名单 [uid=${record.uid} sid=${record.sid}] 吗？`
    return (
      <Popconfirm placement='bottom' title={tmpStr}
        okType='danger' okText='删除' cancelText='取消' onConfirm={() => this.onComfirmDel(record)}>
        <a href='#'><DeleteOutlined style={{ color: '#ff3535', fontSize: '1.2em' }} /></a>
      </Popconfirm>
    )
  }

  // ‘白名单展示列表标签页’html代码
  showVidioComperHtml = () => {
    const { displayData } = this.props.model
    const { searchDone, searchResult } = this.state
    return (
      <Row >
        <Col span={24}>
          短位频道:
          <Input placeholder='搜索短位频道' onChange={e => this.setState({ searchASID: e.target.value })} style={{ width: 200, marginLeft: 10, marginRight: 10 }} /> {/* 搜索按钮 */}
          频道号:
          <Input placeholder='搜索频道号' onChange={e => this.setState({ searchSID: e.target.value })} style={{ width: 200, marginLeft: 10, marginRight: 10 }} /> {/* 搜索按钮 */}
          主持uid:
          <Input placeholder='搜索主持uid' onChange={e => this.setState({ searchUID: e.target.value })} style={{ width: 200, marginLeft: 10, marginRight: 10 }} /> {/* 搜索按钮 */}
          主持YY:
          <Input placeholder='搜索主持YY' onChange={e => this.setState({ searchYY: e.target.value })} style={{ width: 200, marginLeft: 10, marginRight: 10 }} /> {/* 搜索按钮 */}
          <Button type='primary' style={{ marginLeft: 10 }} onClick={this.handleSearch}>搜索</Button>
          <Button type='primary' style={{ marginLeft: 10 }} onClick={this.onExport}>导出</Button>
          <Table rowKey={(record, index) => index} columns={this.columns} dataSource={searchDone ? searchResult : displayData} size='small' pagination={tableStyle} />
        </Col>
      </Row>
    )
  }

  render () {
    const { TabPane } = Tabs
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='2' onChange={(record) => this.onTagChange(record)} type='card' size='large'>
            <TabPane tab='添加' key='1'>
              {this.addVidioCompereHtml()}
            </TabPane>
            <TabPane tab='主持视频白名单' key='2'>
              {this.showVidioComperHtml()}
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default TestPage
