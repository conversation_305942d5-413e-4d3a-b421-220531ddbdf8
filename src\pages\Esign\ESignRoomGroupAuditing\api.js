import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/esign_boss/op_esign_auditing_get?${stringify(params)}`) // 修改 url 即可
}

export function update (params) {
  return request(`/esign_boss/op_esign_auditing_update_state?${stringify(params)}`)
}

export function cancelContract (params) {
  return request(`/esign_boss/op_esign_cancel_contract?${stringify(params)}`)
}

export function getProfile (params) {
  return request(`/esign_boss/op_esign_get_profile?${stringify(params)}`)
}

export function getFileUrl (params) {
  return request(`/esign_boss/op_esign_get_file_url?${stringify(params)}`)
}
