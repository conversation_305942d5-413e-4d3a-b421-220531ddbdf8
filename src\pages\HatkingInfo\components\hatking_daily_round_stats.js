import React, { Component } from 'react'
import { Table, Divider, Button, Card, Form, DatePicker, Space } from 'antd'
import exportExcel from '@/utils/exportExcel'
import { connect } from 'dva'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker

@connect(({ hatkingJy }) => ({ // model 的 namespace
  model1: hatkingJy // model 的 namespace
}))
class HatKingDailyRewardPosStatsComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      list: [],
      dateRange: [moment().subtract(1, 'days'), moment().add(1, 'days')]
    }
  }

  pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  componentDidMount () {
    this.loadData()
  }

  // 日期 渠道类型 单参与流水 单参与成功金额 单参与发放道具比 双参与流水 双参与成功金额 双参与发放道具比 一列流水 一列成功金额 一列发放道具比
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center', render: (text, record) => (text = moment.unix(record.roundStart).format('YYYYMMDD')) },
    { title: '轮次', dataIndex: 'round', align: 'center', render: (text, record) => (text = moment.unix(record.roundStart).format('HH:mm')) },
    { title: '总模拟金额', dataIndex: 'betAmethyst', align: 'center' },
    { title: '总模拟发放金额', dataIndex: 'winAmethyst', align: 'center' },
    { title: '成功位置', dataIndex: 'winPosition', align: 'center' },
    { title: '模拟金额(1)', dataIndex: 'payAmethyst1', align: 'center' },
    { title: '期望金额(1)', dataIndex: 'expectAmethyst1', align: 'center' },
    { title: '模拟金额(2)', dataIndex: 'payAmethyst2', align: 'center' },
    { title: '期望金额(2)', dataIndex: 'expectAmethyst2', align: 'center' },
    { title: '模拟金额(3)', dataIndex: 'payAmethyst3', align: 'center' },
    { title: '期望金额(3)', dataIndex: 'expectAmethyst3', align: 'center' },
    { title: '模拟金额(4)', dataIndex: 'payAmethyst4', align: 'center' },
    { title: '期望金额(4)', dataIndex: 'expectAmethyst4', align: 'center' },
    { title: '模拟金额(5)', dataIndex: 'payAmethyst5', align: 'center' },
    { title: '期望金额(5)', dataIndex: 'expectAmethyst5', align: 'center' },
    { title: '模拟金额(6)', dataIndex: 'payAmethyst6', align: 'center' },
    { title: '期望金额(6)', dataIndex: 'expectAmethyst6', align: 'center' },
    { title: '模拟金额(7)', dataIndex: 'payAmethyst7', align: 'center' },
    { title: '期望金额(7)', dataIndex: 'expectAmethyst7', align: 'center' },
    { title: '模拟金额(8)', dataIndex: 'payAmethyst8', align: 'center' },
    { title: '期望金额(8)', dataIndex: 'expectAmethyst8', align: 'center' },
    { title: '模拟金额(9)', dataIndex: 'payAmethyst9', align: 'center' },
    { title: '期望金额(9)', dataIndex: 'expectAmethyst9', align: 'center' }
  ]

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    console.log(data)
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getRoundStatList`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onExport = () => {
    let headers = []
    let columns = this.columns
    columns.forEach(function (item) {
      headers.push({ key: item.dataIndex, header: item.title })
    })

    const { model: { roundStatList } } = this.props
    var exportData = roundStatList.map(item => {
      let v = $.extend(true, {}, item)
      v.date = moment.unix(v.roundStart).format('YYYYMMDD')
      v.round = moment.unix(v.roundStart).format('HH:mm')
      return v
    })

    exportExcel(headers, exportData)
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { roundStatList } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form>
          <Space align='center'>
            <span style={{ marginLeft: 10 }}>时间范围:</span>
            <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
            <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
            <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
            <div>
              <p style={{ marginTop: 10, color: 'red' }}>Tips: 金额单位为元</p>
            </div>
          </Space>
          <Divider />
          <Table dataSource={roundStatList} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
        </Form>
      </Card>
    )
  }
}

export default HatKingDailyRewardPosStatsComponent
