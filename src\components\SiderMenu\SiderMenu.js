/* eslint-disable eqeqeq */
import React, { PureComponent } from 'react'
import { Layout, Input } from 'antd'
import classNames from 'classnames'
import { Link } from 'dva/router'
import styles from './index.module.less'
import BaseMenu, { getMenuMatches } from './BaseMenu'
import { urlToList } from '../_utils/pathTools'
import { getSelectedMenuKeys } from '@/utils/common'

const { Sider } = Layout 

/**
 * 获得菜单子节点
 * @memberof SiderMenu
 */
const getDefaultCollapsedSubMenus = props => {
  const { location: { pathname } } = props
  const { bossFlatMenuKeys } = window
  return urlToList(pathname)
    .map(item => getMenuMatches(bossFlatMenuKeys, item)[0])
    .filter(item => item)
}

export default class SiderMenu extends PureComponent {
  constructor (props) {
    super(props)
    // console.debug('siderMenus--->props:', props)
    this.state = {
      filterMenuData: props.menuData,
      openKeys: getDefaultCollapsedSubMenus(props)
    }
  }

  // getDerivedStateFromProps 在 render 方法之前调用，返回一个对象来更新 state，如果返回 null 则不更新任何内容
  static getDerivedStateFromProps (props, state) {
    const { pathname } = state
    // console.debug('siderMenu--->getDerivedStateFromProps--->pathName:', pathname, props.location.pathname)
    if (props.location.pathname !== pathname) {
      // console.debug('siderMenu--->getDerivedStateFromProps--->getDefaultCollapsedSubMenus(props):', getDefaultCollapsedSubMenus(props))
      return {
        pathname: props.location.pathname,
        openKeys: getDefaultCollapsedSubMenus(props)
      }
    }
    return null
  }

  handleOpenChange = openKeys => {
    // 一级菜单展开多于一个时，放弃旧的那个，只展开最新的一级菜单
    const moreThanOne = openKeys.filter(key => /^\/[0-9]$/.test(key)).length > 1
    this.setState({
      openKeys: moreThanOne ? [openKeys.pop()] : [...openKeys]
    })
  };

  // 根据当前页面key获取所有父菜单的key
  getAllKeysBySelectKey = (selectKey) => {
    let ary = selectKey.split('/')
    if (ary == null || ary.length === 0) return []
    let res = []
    let last = ''
    for (let i = 1; i < ary.length; i++) {
      last += ('/' + ary[i])
      res = res.concat(last)
      continue
    }
    return res
  }

  // 菜单搜索
  searchMenuData (menuData, searchText, parentMatch) {
    menuData = Array.isArray(menuData) ? menuData : []
    let list = []
    menuData.forEach(item => {
      let afterfilter = []
      afterfilter = this.searchMenuData(item.children, searchText)
      if (afterfilter.length > 0) { // 命中页面
        item.children = afterfilter
        list.push(item)
        return
      }
      if (item.name.indexOf(searchText) > -1) { // 命中父菜单
        list.push(item)
      }
    })
    return list
  }

  // fast search
  onSearch = e => {
    const { menuData } = this.props
    this.setState({ filterMenuData: this.searchMenuData($.extend(true, [], menuData), e.target.value) })
  }

  getTitle = () => {
    const groupType = window.groupType
    if (groupType === 1) {
      return 'Boss产运后台'
    }
    if (groupType === 2) {
      return 'Boss技术后台'
    }
    return 'Boss 后台'
  }

  getSidebarImg = () => {
    const groupType = window.groupType
    if (ENV_TAG === 'local') { // 本地开发
      return 'local_logo.png' 
    }
    if (groupType === 2) { // 开发专属后台
      return `${ENV_TAG}_dev.png`
    }
    if (groupType === 1) { // 运营专属
      return `${ENV_TAG}_logo.png`
    }
    return ''
  }

  render () {
    const { collapsed, onCollapse, fixSiderbar, theme } = this.props
    const { openKeys, filterMenuData } = this.state

    // ------ 这里控制导航菜单的展开逻辑 ------------
    let selectedKeys = getSelectedMenuKeys(this.props)
    let allKeys = this.getAllKeysBySelectKey(selectedKeys)
    // console.debug('siderMenu--->render--->props', this.props)
    // console.debug('siderMenu--->render--->selectedKeys:', selectedKeys)
    // console.debug('siderMenu--->render--->allKeys:', allKeys)
    // console.debug('siderMenu--->render--->openKeys:', openKeys)
    // console.debug('siderMenu--->filterMenuData:', filterMenuData)
    // ----------------------------------------

    const siderClassName = classNames(styles.sider, {
      [styles.fixSiderbar]: fixSiderbar,
      [styles.light]: theme === 'light'
    })

    return (
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        breakpoint='lg'
        onCollapse={onCollapse}
        width={256}
        theme={theme}
        className={siderClassName}
      >
        <div className={styles.logo} id='logo'>
          <Link to='/'>
            <img src={require(`@/img/${this.getSidebarImg()}`)} alt='logo' />
            <h1>{this.getTitle()}</h1> 
            <span style={{ color: 'white', position: 'absolute', margin: '0 7px', fontSize: '14px', top: '-7px' }}>
              ({ENV_TAG === 'prod' ? '正式' : ENV_TAG === 'test' ? '测试' : '本地'})
            </span>

          </Link>
        </div>
        <Input style={{ background: 'rgb(8,45,74)', borderColor: '#001526', color: 'white' }} onPressEnter={this.onSearch} placeholder='搜索菜单' />
        <BaseMenu
          {...this.props}
          mode='inline'
          handleOpenChange={this.handleOpenChange}
          style={{ padding: '16px 0', width: '100%' }}
          openKeys={openKeys.length > 0 ? openKeys : allKeys}
          selectedKeys={selectedKeys}
          menuData={filterMenuData}
        />
      </Sider>
    )
  }
}
