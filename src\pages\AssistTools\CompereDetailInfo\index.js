import { connect } from 'dva'
import React, { Component } from 'react'
import { Button, Form, Input, message, Row, Collapse, Card, Col } from 'antd'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'

const { Panel } = Collapse

const namespace = 'CompereDetailInfo'

const bizDescMap = {
  'jy': '【交友】',
  'pk': '【约战】',
  'bb': '【宝贝】',
  'ordinary_jy': '【交友】普通主持',
  'ordinary_pk': '【约战】普通主持',
  'ordinary_bb': '【宝贝】普通主持',
  'super_jy': '【交友】超级主持',
  'super_pk': '【约战】超级主持',
  'super_bb': '【宝贝】超级主持'
}

@connect(({ CompereDetailInfo }) => ({
  model: CompereDetailInfo
}))

class CompereDetailInfo extends Component {
  // 查询文档
  onQuery = () => {
    let uid = parseInt((this.formRefQuery ? this.formRefQuery.getFieldValue('uid') : '') || 0)
    if (!uid || uid < 1) {
      message.error('请输入主持UID')
      return
    }
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getCompereDetailInfo`,
      payload: { uid: uid },
      callback: (info) => {
      }
    })
  }

  // 营收签约信息渲染
  readerZbaseContractInfo = () => {
    const { model: { compereInfo } } = this.props

    let contractInfoMap = compereInfo && compereInfo.zbaseContractInfoMap ? compereInfo.zbaseContractInfoMap : {}
    let cols = []
    for (let biz in contractInfoMap) {
      cols.push(<Col>
        <Card title={bizDescMap[biz] || biz} bordered>
          <pre>{JSON.stringify(contractInfoMap[biz], null, 2)}</pre>
        </Card>
      </Col>)
    }

    if (cols.length < 1) {
      return '没有数据'
    }

    return <div className='site-card-wrapper'>
      <Row gutter={16}>
        {cols}
      </Row>
    </div>
  }

  // 主持名单明细
  readerContractDetailInfo = () => {
    const { model: { compereInfo } } = this.props

    let contractInfoMap = compereInfo && compereInfo.compereInfoMap ? compereInfo.compereInfoMap : {}
    let cols = []
    for (let biz in contractInfoMap) {
      cols.push(<Col>
        <Card title={bizDescMap[biz] || biz} bordered>
          <pre>{JSON.stringify(contractInfoMap[biz], null, 2)}</pre>
        </Card>
      </Col>)
    }

    if (cols.length < 1) {
      return '没有数据'
    }

    return <div className='site-card-wrapper'>
      <Row gutter={16}>
        {cols}
      </Row>
    </div>
  }

  // 实名认证信息
  readerRealAuthInfo = () => {
    const { model: { compereInfo } } = this.props

    if (!compereInfo || !compereInfo.realAuthInfo) {
      return '没有数据'
    }

    return <pre>{JSON.stringify(compereInfo.realAuthInfo, null, 2)}</pre>
  }

  // 主持全量信息库信息
  readerCompereWholeInfo = () => {
    const { model: { compereInfo } } = this.props

    if (!compereInfo || !compereInfo.compereInfoWhole) {
      return '没有数据'
    }

    return <pre>{JSON.stringify(compereInfo.compereInfoWhole, null, 2)}</pre>
  }

  render () {
    const { route } = this.props
    return (
      <>
        <PageHeaderWrapper title={route.name}>
          <Row style={{ marginBottom: '1em' }}>
            <Form labelCol={{ span: 12 }} layout={'inline'} ref={form => {
              this.formRefQuery = form
            }}>
              <Form.Item name={'uid'} label={'主持UID'}>
                <Input placeholder='主持UID' allowClear />
              </Form.Item>
              <Button type='primary' onClick={this.onQuery}>查询</Button>
            </Form>
          </Row>

          <Collapse defaultActiveKey={['1']}>
            <Panel header='营收签约信息记录' key='1'>
              {this.readerZbaseContractInfo()}
            </Panel>
            <Panel header='普通&超级/独家主持信息' key='2'>
              {this.readerContractDetailInfo()}
            </Panel>
            <Panel header='主持全量信息库中的信息' key='3'>
              {this.readerCompereWholeInfo()}
            </Panel>
            <Panel header='实名认证信息' key='4'>
              {this.readerRealAuthInfo()}
            </Panel>
          </Collapse>

        </PageHeaderWrapper>
      </>
    )
  }
}

export default CompereDetailInfo
