import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Table, DatePicker, Input, Button, message, Popconfirm, Divider, Modal, Form, Select } from 'antd'
import { whiteListType, whiteListAmount, getAmountDisplay } from './common'

const namespace = 'helpGroup'

var moment = require('moment')
var dateFormat = 'YYYY-MM-DD'
var yyyymmddFormat = 'YYYYMMDD'

const Option = Select.Option

const { TextArea } = Input

const contractStatus = { 0: '否', 1: '是', 2: '否' }

@connect(({ helpGroup }) => ({
  model: helpGroup
}))

class SendWhiteList extends Component {
  columns = [
    { title: '添加时间', dataIndex: 'updateTime', align: 'center', render: (text, record) => (record.updateTime === 0 ? '' : moment.unix(record.updateTime / 1000).format(dateFormat)) },
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: 'YY号', dataIndex: 'yy', align: 'center' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: '是否签约', dataIndex: 'isContract', align: 'center', render: (text, record) => (contractStatus[record.isContract]) },
    { title: '签约频道', dataIndex: 'contractASid', align: 'center', render: (text, record) => (record.contractASid > 0 ? record.contractASid : '-') },
    { title: '月发放额度(元)', dataIndex: 'amount', align: 'center', render: (text, record) => (getAmountDisplay(record.amount)) },
    { title: '提交审批人', dataIndex: 'optUser', align: 'center' },
    { title: '操作', key: 'operation', align: 'center', render: (text, record) => (<span><a onClick={this.updateHandler(record)}>修改</a><Divider type='vertical' /><Popconfirm placement='bottom' title='确认删除?' okType='danger' okText='删除' cancelText='取消' onConfirm={this.deleteHandler(record)}><a>删除</a></Popconfirm></span>) }
  ]

  state = {
    visible: false,
    hadClickCheck: false,
    conformUidList: [],
    isUpdate: false
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  updateHandler = (record) => () => {
    console.log(record)
    if (this.formRef) {
      this.formRef.setFieldsValue({ type: whiteListType.send, uidList: String(record.uid), conformUid: String(record.uid), amount: record.amount })
    }
    let uidList = []
    uidList.push(String(record.uid))
    this.setState({ visible: true, isUpdate: true, hadClickCheck: true, conformUidList: uidList })
  }

  deleteHandler = (record) => () => {
    const { dispatch } = this.props
    let data = { uid: record.uid, type: whiteListType.send, amount: record.amount }
    console.log(data)
    dispatch({
      type: `${namespace}/deleteWhiteList`,
      payload: data
    })
  }

  componentDidMount () {
    this.loadData()
  }

  loadData = () => {
    const { searchUID, searchYY, searchCreateStartTime, searchCreateEndTime } = this.state
    const { dispatch } = this.props

    let searchCreateStartTimeTmp
    let searchCreateEndTimeTmp
    
    if (searchCreateStartTime) {
      searchCreateStartTimeTmp = moment(searchCreateStartTime).format(yyyymmddFormat)
    }
    if (searchCreateEndTime) {
      searchCreateEndTimeTmp = moment(searchCreateEndTime).add(1, 'days').format(yyyymmddFormat)
    }

    if (searchCreateStartTimeTmp !== '' && searchCreateEndTimeTmp !== '' && searchCreateStartTimeTmp > searchCreateEndTimeTmp) {
      message.warn('开始时间不能大于结束时间')
      return
    }
    console.log(searchCreateStartTimeTmp, searchCreateEndTimeTmp)
    let uid
    if (searchUID && searchUID !== '') {
      uid = Number(searchUID)
    }

    let yy
    if (searchYY && searchYY !== '') {
      yy = Number(searchYY)
    }

    // 1-发放白名单
    let data = { type: whiteListType.send, uid: uid, yy: yy, startDate: searchCreateStartTimeTmp, endDate: searchCreateEndTimeTmp }
    dispatch({
      type: `${namespace}/listSendWhiteList`,
      payload: data
    })
  }

  searchHandle = () => () => {
    this.loadData()
  }

  // ********************************************************
  // 检查uid
  checkWhiteListhandle = () => () => {
    const { inputUIDList } = this.state
    const { dispatch } = this.props
    if (inputUIDList === undefined || inputUIDList === '') {
      message.warning('请输入要修改的UID')
      return
    }

    let uidList = []
    if (inputUIDList && inputUIDList.length > 0) {
      let uidStrList = inputUIDList.split('\n')
      if (uidStrList && uidStrList.length > 0) {
        for (let index = 0; index < uidStrList.length; index++) {
          uidList.push(Number(uidStrList[index]))
        }
      }
    }

    let updateUIDInfo = (conformUid, unConformUid) => {
      if (this.formRef) {
        let conformUidStr = ''
        if (conformUid && conformUid.length > 0) {
          conformUidStr = conformUid.join('\n')
        }
        let unConformUidStr = ''
        if (unConformUid && unConformUid.length > 0) {
          unConformUidStr = unConformUid.join('\n')
        }
        this.formRef.setFieldsValue({ conformUid: conformUidStr, unConformUid: unConformUidStr })
        this.setState({ conformUidList: conformUid, hadClickCheck: true })
      }
    }

    dispatch({
      type: `${namespace}/checkWhiteList`,
      payload: { play: { type: whiteListType.send, uidList: uidList }, func: updateUIDInfo }
    })
  }

 // 添加
 addHandle = () => () => {
   this.setState({ visible: true, isUpdate: false })
 }

 saveFormRef = (formRef) => {
   this.formRef = formRef
 }

 hiddenModal = () => {
   this.setState({ visible: false, inputUIDList: '', conformUidList: [], hadClickCheck: false })
   if (this.formRef) {
     this.formRef.resetFields()
   }
 }

 handleCancel = e => {
   if (this.formRef) {
     this.formRef.resetFields()
   }
   this.setState({ visible: false })
 }

 handleSubmit = e => {
   if (this.formRef) {
     this.formRef.submit()
   }
 }

 onFinish = values => {
   const { conformUidList, hadClickCheck, isUpdate } = this.state

   if (!hadClickCheck) {
     message.warning('未检测结果, 请先检测结果')
     return
   }

   if (conformUidList === null || conformUidList === undefined || conformUidList.length === 0) {
     message.warning('符合条件的UID输入框为空，无法提交')
     return
   }

   let amount = Number(values.amount)

   let uidList = []
   for (let index = 0; index < conformUidList.length; index++) {
     uidList.push(Number(conformUidList[index]))
   }

   let data = { isAdd: !isUpdate, type: whiteListType.send, amount: amount, uidList: uidList }
   console.log(data)

   this.props.dispatch({
     type: `${namespace}/approvalWhiteList`,
     payload: data
   })
   this.hiddenModal()
 }
 // ********************************************************
 
 render () {
   const { model: { listSendWhiteList } } = this.props
   const { visible, isUpdate } = this.state

   const formItemLayout = {
     labelCol: {
       xs: { span: 5 },
       sm: { span: 10 }
     },
     wrapperCol: {
       xs: { span: 2 },
       sm: { span: 15 }
     }
   }

   return (
     <Card>
       <Button style={{ marginLeft: 5 }} type='primary' onClick={this.addHandle()}>添加</Button>
       <div style={{ marginTop: 20 }} />

       <span>UID</span>
       <Input allowClear placeholder='请输入uid' onChange={e => this.setState({ searchUID: e.target.value })} style={{ width: 150, marginLeft: 3 }} />

       <span style={{ marginLeft: 10 }}>YY</span>
       <Input allowClear placeholder='请输入yy' onChange={e => this.setState({ searchYY: e.target.value })} style={{ width: 150, marginLeft: 3 }} />

       <span style={{ marginLeft: 20 }}>添加时间</span>
       <DatePicker
         format='YYYY-MM-DD'
         placeholder='开始时间'
         onChange={(v) => this.setState({ searchCreateStartTime: v })}
         style={{ marginLeft: 10 }}
       />
       <span style={{ marginLeft: 3 }}>~</span>
       <DatePicker
         format='YYYY-MM-DD'
         placeholder='结束时间'
         onChange={(v) => this.setState({ searchCreateEndTime: v })}
         style={{ marginLeft: 3 }}
       />
       <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
       <Table style={{ marginTop: 10 }} rowKey='idx' pagination={this.defaultPageValue} columns={this.columns} dataSource={listSendWhiteList} />

       <Modal forceRender width={500} visible={visible} title='添加萌星帮帮团白名单' onCancel={this.handleCancel} onOk={this.handleSubmit} okText='提交审核'>
         <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
           <Form.Item label='白名单类型' name='type'>
             <Select disabled defaultValue={whiteListType.send} style={{ marginLeft: 5 }} allowClear placeholder='请选择' onChange={(v) => this.setState({ type: v })}>
               <Option value={whiteListType.send}>发送白名单</Option>
             </Select>
           </Form.Item>
           <Form.Item label='月额度上限(元)' name='amount' rules={[{ required: true }]}>
             <Select style={{ marginLeft: 5 }} allowClear placeholder='请选择'>
               { whiteListAmount[whiteListType.send].map((item, index) => (<Option key={index} value={item}>{getAmountDisplay(item)}</Option>))}
             </Select>
           </Form.Item>
           <Form.Item label='请输入要修改的UID' name='uidList' rules={[{ required: true }]}>
             <TextArea disabled={isUpdate} rows={4} placeholder='UID(回车换行)' onChange={e => this.setState({ inputUIDList: e.target.value })} />
           </Form.Item>
           <Button disabled={isUpdate} style={{ marginLeft: 365 }} type='primary' onClick={this.checkWhiteListhandle()}>检查结果</Button>
           <div style={{ marginTop: 10 }} />
           <Form.Item label='符合修改要求的UID' name='conformUid'>
             <TextArea disabled rows={4} />
           </Form.Item>
           <Form.Item label='不符合修改要求的UID' name='unConformUid'>
             <TextArea disabled rows={4} />
           </Form.Item>
         </Form>
       </Modal>
     </Card>
   )
 }
}

export default SendWhiteList
