import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Row, Col, Typography, Divider, Table, Button, Form, message, InputNumber, Select, TimePicker, Modal, Input, Space, Tooltip } from 'antd'
import { timeFormater, deepClone } from '@/utils/common'
import { CalculateAverage, CalculateMaxValue } from '../common'
import ApprovalButton from '@/components/ApprovalButton'
import DiffTable from '@/components/DiffTable'
import moment from 'moment'
import { columns3, broadcastOptionsJY } from '../../DropMain/components/list_common'
import { screenshotByID } from '@/utils/screenShot'
import DynamicCfgButton from '../../DropMain/components/dynamicCfgBtn'
import PrizeSelector from '../../DropMain/components/prizeSelector'
import { propTypeOptions } from '../../dropCommon'
const namespace = 'dropBoxWar'
const { Text } = Typography
const defaultAprInfo = { timestamp: 0, operator: 0, passport: '', opRemark: '(空)', aprUid: 0, aprRemark: '(空)', aprPassport: '' }
const bundlePoolID = 21000
const defaultPoolItem = { id: 1, appId: 2, prizeId: 101, prizeType: 1, propsId: 12, propsName: '爱心', hot: 0, propsType: 0, count: 1, value: 100, rate: 1, dailyLimit: -1, hoursLimit: -1, dailyCount: 0, valueLimit: 0, timeStart: 0, timeStop: 0, broadCastType: 0, boxWarSetting: { limitA: 0, limitB: 0, limitC: 0 } } // 注意这里的appID，不同业务要用不同的值！

@connect(({ dropBoxWar }) => ({
  model: dropBoxWar
}))

class BundleRewardTmp extends Component {
  state = {
    id: 20000,
    editing: false,
    poolEditing: false,
    submitComfirmVisible: false,
    prizeSelectorVisible: false,
    selectPoolIndex: 0,
    screenShot: '',
    updateRemark: '',
    diffTableKey: 0,
    poolConfigRaw: {},
    poolConfig: [], // 组合大礼道具池-生效中
    poolConfigTmp: [] // 组合大礼道具池-编辑中
  }

  componentDidMount = () => {
    this.getBundleReward()
    this.getPoolConfig(bundlePoolID)
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // =============== 获取数据 ======================

  // 获取组合大礼-礼盒配置
  getBundleReward = () => {
    const { id } = this.state
    const cbFunc = () => {
      setTimeout(() => {
        this.setState({ diffTableKey: Math.random() })
      }, 1000)
    }
    this.callModel('getBundleConfigTmp', { params: { id, isProd: false }, cbFunc })
    this.callModel('getBundleConfigProd', { params: { id, isProd: true }, cbFunc })
  }

  // 获取道具池配置
  getPoolConfig = (pid) => {
    this.setState({ poolConfig: [], poolConfigTmp: [], poolConfigRaw: {} })
    this.callModel('getPoolConfigByID', {
      params: { id: pid },
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg, list } = ret
        if (status !== 0) {
          message.error('获取道具池配置失败,请稍后再试' + msg)
          return
        }
        this.parsePoolConfig(list, pid)
      }
    })
  }

  // 道具池解析
  parsePoolConfig = (poolConfig, pid) => {
    let list = []
    let templist = []
    let content = poolConfig.temporary
    if (poolConfig !== undefined && poolConfig.content !== undefined) {
      try {
        list = JSON.parse(poolConfig.content)
        templist = JSON.parse(poolConfig.temporary.content)
      } catch (e) {
        message.error('json convert error ', e)
      }
    }
    delete content['content']
    list.forEach((item, index, ary) => { item.limitSetting.poolID = pid })
    templist.forEach((item, index, ary) => { item.limitSetting.poolID = pid })

    this.setState({ poolConfig: list, poolConfigTmp: templist, poolConfigRaw: poolConfig })
  }

  // =============== 组件状态变更 ======================

  // 开启编辑模式
  turnOnBundleEditMode = () => {
    this.setState({ editing: true })
  }

  // 放弃更新重置状态
  onResetBundle = () => {
    this.getBundleReward()
    this.setState({ editing: false })
  }

  // 放弃编辑道具池
  resetPool = () => {
    this.getPoolConfig(bundlePoolID)
    this.setState({ poolEditing: false })
  }

  // 切换礼物
  onSelectPropsId = (index) => {
    this.setState({ selectPoolIndex: index, prizeSelectorVisible: true })
  }

  // =============== 组合大礼礼盒 - 提交修改和审批 ========================

  // 检查配置合法性然后弹出提交审批确认框
  beforeSubmitBundleEditing = () => {
    let { bundleRewardTmp } = this.props.model
    let reason = this.checkSubmitBundleData(bundleRewardTmp)
    if (reason) {
      message.warn('配置不合法,请调整:' + reason)
      return
    }
    if (bundleRewardTmp.bundleA.dailyLimit < 0) {
      bundleRewardTmp.bundleA.limitSetting.isOpen = false
      bundleRewardTmp.bundleA.limitSetting.mode = 0
    }
    if (bundleRewardTmp.bundleB.dailyLimit < 0) {
      bundleRewardTmp.bundleB.limitSetting.isOpen = false
      bundleRewardTmp.bundleB.limitSetting.mode = 0
    }
    if (bundleRewardTmp.bundleC.dailyLimit < 0) {
      bundleRewardTmp.bundleC.limitSetting.isOpen = false
      bundleRewardTmp.bundleC.limitSetting.mode = 0
    }

    this.setState({ editing: false })
    setTimeout(() => {
      screenshotByID('screenShotDiv', (isOk, url) => {
        if (!isOk) {
          message.warn('截图失败~')
        }
        console.debug('url===>', url)
        this.setState({ submitComfirmVisible: true, screenShot: url })
      })
    }, 200)
  }

  // 检查道具池配置合法性, 返回原因
  checkSubmitBundleData = (value) => {
    if (value.bundleA.count <= 0 || value.bundleB.count <= 0 || value.bundleC.count <= 0) {
      return '连续参加次数不能小于1'
    }
    if (value.bundleA.rate < 0 || value.bundleB.rate < 0 || value.bundleC.rate < 0) {
      return '概率不能小于0'
    }
    if (value.bundleA.timeStart > value.bundleA.timeStop || value.bundleB.timeStart > value.bundleB.timeStop || value.bundleC.timeStart > value.bundleC.timeStop) {
      return '开始时间必须小于结束时间'
    }
    if (value.bundleA.dailyLimit >= 0 && value.bundleB.dailyLimit >= 0 && value.bundleC.dailyLimit >= 0) {
      return '不能全部礼盒都配置日上限'
    }
    return ''
  }

  // 向服务器提交修改
  onSubmitBundle = () => {
    const { updateRemark, screenShot } = this.state
    let { bundleRewardTmp } = this.props.model

    bundleRewardTmp.aprInfo.opRemark = updateRemark || '空'
    bundleRewardTmp.screenShot = screenShot

    this.callModel('editBundleRewardConfig', {
      params: { ...bundleRewardTmp },
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('提交失败,请稍后再试:' + msg)
          return
        }
        message.success('已提交审批~')
        this.setState({ submitComfirmVisible: false, editing: false })
        this.getBundleReward()
      }
    })
  }

  // ============= 组合大礼礼盒 - 编辑配置 ==========================

  // 转换配置结构
  parseBundleToList = (cfg, before) => {
    let { bundleA, bundleB, bundleC } = cfg
    if (bundleA === undefined) {
      return []
    }
    bundleA.key = 'bundleA'
    bundleB.key = 'bundleB'
    bundleC.key = 'bundleC'
    bundleA.limitSetting.poolID = 21000
    bundleB.limitSetting.poolID = 21000
    bundleC.limitSetting.poolID = 21000
    if (before) {
      bundleA.cmpLimitSetting = before.bundleA?.limitSetting
      bundleB.cmpLimitSetting = before.bundleB?.limitSetting
      bundleC.cmpLimitSetting = before.bundleC?.limitSetting
    }
    let list = [ bundleA, bundleB, bundleC ]
    return list
  }

  onInputNumberChange = (record, field) => value => {
    let { bundleRewardTmp } = this.props.model
    const { key: outerField } = record
    bundleRewardTmp[outerField][field] = value
    this.changeState('bundleRewardTmp', bundleRewardTmp)
  }

  onInputChange = (record, field) => event => {
    let { bundleRewardTmp } = this.props.model
    const { key: outerField } = record
    bundleRewardTmp[outerField][field] = event.target.value
    this.changeState('bundleRewardTmp', bundleRewardTmp)
  }

  onTimeChange = (record, field) => value => {
    let { bundleRewardTmp } = this.props.model
    const { key: outerField } = record
    bundleRewardTmp[outerField][field] = value ? value.unix() % 86400 : 0
    this.changeState('bundleRewardTmp', bundleRewardTmp)
  }

  // ============= 道具池 - 编辑配置 ==========================

  onPropChange = (prizeInfo) => {
    const { selectPoolIndex: index, poolConfigTmp } = this.state
    const { appId, id, name, url, price, prizeType } = prizeInfo
    let cp = [...poolConfigTmp]
    cp[index].appId = appId // appId
    cp[index].propsId = id // 礼物ID
    cp[index].propsName = name // 礼物名称
    cp[index].propsUrl = url // 礼物图片
    cp[index].value = price // 礼物加个
    cp[index].prizeType = prizeType // 礼物渠道
    this.setState({ poolConfigTmp: cp, prizeSelectorVisible: false })
  }

  // 数字字段更新
  onPoolInputChange = (index, field, value) => {
    const { poolConfigTmp } = this.state
    let cp = [...poolConfigTmp]
    cp[index][field] = value
    this.setState({ poolConfigTmp: cp })
  }

  // 组合上限字段更新
  onPoolBundleSettingChange = (index, field, value) => {
    const { poolConfigTmp } = this.state
    let cp = [...poolConfigTmp]
    cp[index].boxWarSetting[field] = value
    this.setState({ poolConfigTmp: cp })
  }

  // 删除礼物
  removePoolItem = (index) => {
    const { poolConfigTmp } = this.state
    let cp = [...poolConfigTmp]
    cp.splice(index, 1)
    this.setState({ poolConfigTmp: cp })
  }

  // 新增礼物
  addPoolItem = () => {
    const { poolConfigTmp } = this.state
    let cp = [...poolConfigTmp]
    let newItem = { ...defaultPoolItem }

    newItem.prizeId = bundlePoolID * 100 + newItem.prizeId
    if (Array.isArray(poolConfigTmp)) {
      poolConfigTmp.forEach(item => {
        if (item.id >= newItem.id) {
          newItem.id = item.id + 1
        }
        if (item.prizeId >= newItem.prizeId) {
          newItem.prizeId = item.prizeId + 1
        }
      })
    }
    cp.push(newItem)
    this.setState({ poolConfigTmp: cp })
  }

  // 检查配置合法性然后弹出提交审批确认框
  beforeSubmitPoolEditing = () => {
    let { poolConfigTmp } = this.state
    if (!this.checkSubmitPoolData(poolConfigTmp)) {
      return
    }

    this.setState({ poolEditing: false, screenShot: '' })
    setTimeout(() => {
      screenshotByID('screenShootPoolTable', (isOk, url) => {
        if (!isOk) {
          message.warn('截图失败~')
        }
        console.debug('url===>', url)
        this.setState({ updatePoolComfirmVisible: true, screenShot: url })
      })
    }, 200)
  }

  // 检查道具池配置合法性, 返回false表示不合法 TODO: 更多检查
  checkSubmitPoolData = (editingConfig) => {
    return true
  }

  // 提交道具池配置更新
  onSubmitPool = () => {
    const { updateRemark, screenShot, poolConfigTmp, poolConfigRaw } = this.state
    let temporary = { ...poolConfigRaw.temporary }
    let newConfig = { ...poolConfigRaw }
    // 数据检验
    if (!this.checkSubmitPoolData(poolConfigTmp)) {
      return
    }

    delete newConfig['list']
    temporary.content = JSON.stringify(poolConfigTmp)
    temporary.remark = updateRemark || '空'
    temporary.screenShot = screenShot
    newConfig.temporary = temporary

    this.callModel('editPoolConfig', {
      params: newConfig,
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('发生错误，请联系管理员:' + msg)
          return
        }
        message.success('已提交审批')
        this.setState({ poolEditing: false, updatePoolComfirmVisible: false })
        this.getPoolConfig(bundlePoolID)
      }
    })
  }
  // ============= 表头变换 ==========================

  // 组合大礼表头 编辑 or 查看
  renderEditBunddleColumn = (columns, editing) => {
    if (!editing) {
      return columns
    }
    let renderColumns = []
    let cp = deepClone(columns)

    for (let i = 0; i < cp.length; i++) {
      let column = cp[i]
      if (['count', 'rate', 'dailyLimit', 'hoursLimit', 'dailyCount', 'valueLimit'].indexOf(column.dataIndex) > -1) {
        column.render = (v, r, i) => {
          return <InputNumber onChange={this.onInputNumberChange(r, column.dataIndex)} defaultValue={v} />
        }
      }

      if (column.dataIndex === 'name') {
        column.render = (v, r, i) => {
          return <Input onChange={this.onInputChange(r, column.dataIndex)} defaultValue={v} style={{ width: '8em' }} />
        }
      }

      if (['timeStart', 'timeStop'].indexOf(column.dataIndex) > -1) {
        column.render = (v, r, i) => {
          return <TimePicker onChange={this.onTimeChange(r, column.dataIndex)} defaultValue={moment.unix(v)} />
        }
      }

      if (column.dataIndex === 'broadCastType') {
        column.render = (v, r, i) => {
          return <Select onChange={this.onInputNumberChange(r, column.dataIndex)} options={broadcastOptionsJY} defaultValue={v} />
        }
      }

      if (column.dataIndex === 'limitSetting') {
        column.render = (value, r, i) => {
          return <DynamicCfgButton key={value} value={value} record={r} isEdit globalModeOnly limit={r.dailyLimit} id={r.id} propsName={r.propsName} inComeType='交友物资大战全业务'
            onChange={(v) => {
              let { bundleRewardTmp } = this.props.model
              const { key: outerField } = r
              bundleRewardTmp[outerField].limitSetting = v
              this.changeState('bundleRewardTmp', bundleRewardTmp)
            }} />
        }
      }

      renderColumns.push(column)
    }

    return renderColumns
  }

  // 颜色概率表头 编辑 or 查看
  renderEditColorColumn = (columns, editing) => {
    if (!editing) {
      return columns
    }
    let renderColumns = []
    let cp = deepClone(columns)
    for (let i = 0; i < cp.length; i++) {
      let column = cp[i]

      if (column.dataIndex === 'count') {
        column.render = (v, r, i) => {
          return <InputNumber onChange={(newVal) => {
            let { bundleRewardTmp } = this.props.model
            bundleRewardTmp.difficulty[i] = newVal
            this.changeState('bundleRewardTmp', bundleRewardTmp)
          }} defaultValue={r} />
        }
      }

      renderColumns.push(column)
    }
    return renderColumns
  }

  // 颜色概率配置表头
  colorDicColumns = [
    { title: '房间颜色位置', dataIndex: 'index', render: (v, r, i) => { return i + 1 } },
    { title: '难度值(值越大,成功概率越低)', dataIndex: 'count', render: (v, r, i) => { return r } },
    { title: '成功概率', dataIndex: 'rate', render: (v, r, i) => { return `${(1 * 100.0 / r).toFixed(3)}%` } }
  ].map(item => {
    item.align = 'center'
    return item
  })

  // 道具池表头-查看
  poolColumnView = [
    { title: '奖励道具名称', dataIndex: 'propsName' },
    { title: '发放道具ID', dataIndex: 'propsId' },
    { title: '数量', dataIndex: 'count', render: (v, r) => { return countFormater(v, 'count') }, dft_getText: (v, r) => { return countGetText(v, 'count') } },
    { title: '价值/紫水晶', dataIndex: 'value', render: (v, r) => { return v ? (r.value * r.count).toLocaleString() : '' }, dft_getText: (v, r) => { return r ? (r.value * r.count).toLocaleString() : '' } },
    { title: '稀有度', align: 'center', dataIndex: 'propsType', render: v => { return propTypeOptions.find(item => item.value === v)?.label } },
    { title: '普通概率', dataIndex: 'rate', render: (v, r) => { return countFormater(v, 'rate') }, dft_getText: (v, r) => { return countGetText(v, 'rate') } },
    { title: '钻石上限', dataIndex: 'boxWarSettingA', render: (v, r) => { return r.boxWarSetting?.limitA } },
    { title: '至尊上限', dataIndex: 'boxWarSettingB', render: (v, r) => { return r.boxWarSetting?.limitB } },
    { title: '王者上限', dataIndex: 'boxWarSettingC', render: (v, r) => { return r.boxWarSetting?.limitC } }
  ].map(item => {
    item.align = 'center'
    return item
  })

  // 道具池表头-编辑
  poolColumnEdit = [
    { title: '奖励道具名称', dataIndex: 'propsName', render: (v, r) => { return <Tooltip title={r.prizeId}>{v}</Tooltip> } },
    { title: '发放道具ID', dataIndex: 'propsId', render: (v, r, i) => { return <Button onClick={() => this.onSelectPropsId(i, 'propsId')} type='link'>{v}</Button> } },
    { title: '数量', dataIndex: 'count', render: (v, r, i) => { return <InputNumber onChange={(nv) => this.onPoolInputChange(i, 'count', nv)} defaultValue={v} /> } },
    { title: '价值/紫水晶', dataIndex: 'value', render: (v, r) => { return v ? (r.value * r.count).toLocaleString() : '' } },
    { title: '稀有度', align: 'center', dataIndex: 'propsType', render: (v, r, i) => { return <Select onChange={(nv) => this.onPoolInputChange(i, 'propsType', nv)} options={propTypeOptions} defaultValue={v} /> } },
    { title: '普通概率', dataIndex: 'rate', render: (v, r, i) => { return <InputNumber onChange={(nv) => this.onPoolInputChange(i, 'rate', nv)} defaultValue={v} /> } },
    { title: '钻石上限', dataIndex: 'boxWarSetting', render: (v, r, i) => { return <InputNumber onChange={(nv) => this.onPoolBundleSettingChange(i, 'limitA', nv)} defaultValue={v.limitA} /> } },
    { title: '至尊上限', dataIndex: 'boxWarSetting', render: (v, r, i) => { return <InputNumber onChange={(nv) => this.onPoolBundleSettingChange(i, 'limitB', nv)} defaultValue={v.limitB} /> } },
    { title: '王者上限', dataIndex: 'boxWarSetting', render: (v, r, i) => { return <InputNumber onChange={(nv) => this.onPoolBundleSettingChange(i, 'limitC', nv)} defaultValue={v.limitC} /> } },
    { title: '操作', dataIndex: 'propsId', render: (v, r, i) => { return <Button onClick={() => this.removePoolItem(i)} type='link' danger>删除</Button> } }
  ].map(item => {
    item.align = 'center'
    return item
  })

  render () {
    const { isProd } = this.props
    const { editing, submitComfirmVisible, diffTableKey, poolConfig, poolConfigTmp, poolConfigRaw, poolEditing, prizeSelectorVisible, updatePoolComfirmVisible } = this.state
    const { bundleRewardTmp, bundleRewardProd, globalPrizeList } = this.props.model

    const bundleReward = isProd ? bundleRewardProd : bundleRewardTmp
    const { aprInfo, timestamp, status } = bundleReward
    const { aprInfo: aprInfoPool, temporary: temporaryPool } = poolConfigRaw

    const { aprId, passport, opRemark, aprUid, aprPassport, aprRemark, timestamp: aprTimestamp } = aprInfo || defaultAprInfo

    const oldDiffcult = countDifficultyByArray(bundleRewardProd.difficulty || [])
    const newDiffcult = countDifficultyByArray(bundleRewardTmp.difficulty || [])
    const oldTotalDif = countTotalDifficulty(bundleRewardProd.difficulty || [])
    const newTotalDif = countTotalDifficulty(bundleRewardTmp.difficulty || [])
    const h3Style = { fontSize: '1.2em', borderLeft: '5px solid', color: '#6f6969', paddingLeft: '1em' }

    return (
      <>
        {/* 道具池配置 */}
        <Text style={h3Style}>道具池配置</Text>
        <Card style={{ marginBottom: '2em' }}>
          {
            isProd
              ? ''
              : <Row gutter={8} style={{ marginBottom: '1em' }}>
                <Col span={6} hidden={!poolEditing}>
                  <Form.Item>
                    <Button style={{ marginRight: 20 }} onClick={() => this.addPoolItem()}>新增奖励道具</Button>
                    <Button style={{ marginRight: 20 }} onClick={this.resetPool}>放弃修改</Button>
                    <Button type='dash' danger onClick={() => this.beforeSubmitPoolEditing()} >提交修改</Button>
                  </Form.Item>
                </Col>
                <Col span={2} hidden={poolEditing}>
                  <Button type='primary' onClick={() => this.setState({ poolEditing: true })} >编辑道具池</Button>
                </Col>
                <Col span={2} hidden={poolEditing}>
                  <ApprovalButton aprId={temporaryPool?.aprId} >
                    <Button type='primary' disabled={temporaryPool?.aprId === '' || temporaryPool?.status !== 1} >审批</Button>
                  </ApprovalButton>
                </Col>
              </Row>
          }
          <Row>
            {
              isProd
                ? <Row>
                  <Col>
                    <Row>提交：<Text type='secondary'> {`${aprInfoPool?.passport}_(${timeFormater(aprInfoPool?.timestamp)})`} <Divider type='vertical' /> </Text></Row>
                    <Row>审批人：<Text type='secondary'> {aprInfoPool?.aprUid === 0 ? '系统自动审批' : aprInfoPool?.aprPassport}_({timeFormater(poolConfigRaw?.timestamp)}) <Divider type='vertical' /></Text> </Row>
                  </Col>
                  <Col>
                    <Row>申请理由: <Text type='secondary'> { aprInfoPool?.opRemark || '(空)'} <Divider type='vertical' /></Text></Row>
                    <Row>审批备注：<Text type='secondary'> { aprInfoPool?.aprRemark || '(空)'}</Text></Row>
                  </Col>
                </Row>
                : <Row>
                  <Col>
                    <Row>提交：<Text type='secondary'> {`${temporaryPool?.passport}_(${timeFormater(temporaryPool?.timestamp)})`} <Divider type='vertical' /> </Text></Row>
                    <Row hidden={temporaryPool?.status === 1}>审批：<Text type='secondary'> {temporaryPool?.aprUid === 0 ? '系统' : temporaryPool?.aprPassport}_({timeFormater(temporaryPool?.aprTimestamp)}) <Divider type='vertical' /></Text></Row>
                  </Col>
                  <Col>
                    <Row>申请理由：<Text type='secondary'> { temporaryPool?.opRemark || '(空)'} <Divider type='vertical' /></Text></Row>
                    <Row hidden={temporaryPool?.status === 1}>审批备注：<Text type='secondary'> { temporaryPool?.aprRemark || '(空)'} <Divider type='vertical' /></Text></Row>
                  </Col>
                  <Col>
                    <Row>审批状态：<Text type={['warning', 'warning', 'success', 'danger'][temporaryPool?.status]}> { ['未创建', '待审批', '已通过', '不通过'][temporaryPool?.status]} <Divider type='vertical' /></Text></Row>
                  </Col>
                </Row>
            }

            {/* 组合大礼价值描述 */}
            <div id='screenShootPoolTable' style={{ width: '100%', padding: '10px' }}>
              <Col span={24} style={{ marginTop: '1em' }}>
                <Text style={{ fontSize: '1.5em', color: '#2d9aff' }}>组合大礼道具池配置：</Text>
                {
                  isProd
                    ? genDiffAvgDiv(poolConfig, poolConfig, bundleReward, bundleReward)
                    : genDiffAvgDiv(poolConfigTmp, poolConfig, bundleRewardTmp, bundleRewardProd)
                }
                {
                  isProd
                    ? genMaxValueDiv(poolConfig, poolConfig, bundleReward, bundleReward)
                    : genMaxValueDiv(poolConfigTmp, poolConfig, bundleRewardTmp, bundleRewardProd)
                }
              </Col>

              {/* 道具池配置表格 */}
              <Col span={24}>

                {
                  isProd
                    ? <Table columns={this.poolColumnView} dataSource={poolConfig} pagination={false} size='small' rowKey='id' scroll={{ x: 'max-content' }} />
                    : poolEditing
                      ? <Table columns={this.poolColumnEdit} key={poolConfig} dataSource={poolConfigTmp} pagination={false} size='small' rowKey='id' scroll={{ x: 'max-content' }} />
                      : <DiffTable columns={this.poolColumnView} key={poolConfigTmp} after={poolConfigTmp} before={poolConfig} oldProps={{ pagination: false, size: 'small' }} />
                }
              </Col>
            </div>
          </Row>

          <PrizeSelector
            type='modal'
            appIDLimit={2}
            visible={prizeSelectorVisible} prizeList={globalPrizeList}
            onCancel={() => { this.setState({ prizeSelectorVisible: false }) }}
            onComfirm={(v) => { this.onPropChange(v) }}
          />

          {/* 提交修改确认模态框 */}
          <Modal visible={updatePoolComfirmVisible} title='确认提交审批么？' footer={null} onCancel={() => { this.setState({ updatePoolComfirmVisible: false, poolEditing: true }) }}>
            <Row style={{ marginBottom: '1em' }}>
              <Input placeholder='请输入备注信息 (选填)' onChange={(e) => { this.setState({ updateRemark: e.target.value }) }} />
            </Row>
            <Space>
              <Button onClick={() => { this.setState({ updatePoolComfirmVisible: false, poolEditing: true }) }}>再看看</Button>
              <Button danger type='primary' onClick={() => { this.onSubmitPool() }}>提交审批</Button>
            </Space>
          </Modal>

        </Card>

        {/* ====================================================================================================== */}

        {/* 组合大礼礼盒配置 */}
        <Text style={h3Style}>组合大礼配置</Text>
        <Card>
          <Row>
            {/* 审批相关按钮 */}
            {
              isProd
                ? ''
                : <><Col span={6} hidden={!editing}>
                  <Form.Item>
                    <Button onClick={this.onResetBundle} style={{ marginRight: 15 }}>放弃修改</Button>
                    <Button type='dash' danger onClick={() => { this.beforeSubmitBundleEditing() }}>提交修改</Button>
                  </Form.Item>
                </Col>

                  <Col span={2} hidden={editing}>
                    <Button type='primary' onClick={this.turnOnBundleEditMode}>编辑道具池</Button>
                  </Col>
                  <Col span={2} hidden={editing}>
                    <ApprovalButton aprId={aprId} >
                      <Button type='primary' disabled={aprId === '' || status !== 1} >审批</Button>
                    </ApprovalButton>
                  </Col></>
            }

            {/* 组合大礼配置信息 */}
            <Col>
              <Row>提交：<Text type='secondary'> {`${passport}_(${timeFormater(timestamp)})`} <Divider type='vertical' /> </Text></Row>
              {status > 1 ? <Row>审批人：<Text type='secondary'> {aprUid === 0 ? '系统自动审批' : aprPassport}_({timeFormater(aprTimestamp)}) <Divider type='vertical' /></Text> </Row> : ''}
            </Col>
            <Col>
              <Row>申请理由: <Text type='secondary'> { opRemark || '(空)'} <Divider type='vertical' /></Text></Row>
              {status > 1 ? <Row>审批备注：<Text type='secondary'> {aprRemark || '(空)'}</Text></Row> : ''}
            </Col>
            <Col hidden={isProd}>
              <Row>审批状态：<Text type={['warning', 'warning', 'success', 'danger'][status]}> { ['未创建', '待审批', '已通过', '不通过'][status]} <Divider type='vertical' /></Text></Row>
            </Col>

            <Divider />
            <Text style={{ fontSize: '1.5em', color: '#2d9aff' }}>组合大礼发放配置：</Text>
            {/* 组合大礼配置+颜色配置  */}
            <div id='screenShotDiv' style={{ padding: '10px', width: '100%' }}>
              <Col span={24}>
                { // 组合大礼配置表格
                  isProd || editing
                    ? <Table size={editing ? 'small' : 'middle'} columns={this.renderEditBunddleColumn(columns3, editing)} dataSource={this.parseBundleToList(bundleReward, bundleReward)} pagination={false} />
                    : <DiffTable key={diffTableKey} oldProps={{ size: 'small', pagination: false }} diffRender='Normal' columns={columns3} before={this.parseBundleToList(bundleRewardProd, bundleRewardTmp)} after={this.parseBundleToList(bundleRewardTmp, bundleRewardProd)} />
                }
              </Col>

              <Col span={24} style={{ marginTop: '2em' }}>
                <Text style={{ fontSize: '1.5em', color: '#2d9aff' }}>集齐房间颜色配置：</Text>
                集齐房间颜色总难度:{
                  isProd || newTotalDif === oldTotalDif
                    ? oldTotalDif
                    : <Text type='secondary'><Text type='danger'>{newTotalDif}</Text>({oldTotalDif})</Text>
                },
                总概率: {
                  isProd || oldDiffcult === newDiffcult
                    ? oldDiffcult
                    : <Text type='secondary'><Text type='danger'>{newDiffcult}</Text>({oldDiffcult})</Text>
                }%
              </Col>

              <Col span={12} >
                { // 颜色概率编辑查看
                  isProd || editing
                    ? <Table key={bundleReward.difficulty} size={editing ? 'small' : 'middle'} columns={this.renderEditColorColumn(this.colorDicColumns, editing)} dataSource={bundleReward.difficulty} pagination={false} />
                    : <DiffTable key={diffTableKey} oldProps={{ size: 'small', pagination: false }} diffRender='Normal' columns={this.colorDicColumns} before={bundleRewardProd.difficulty || []} after={bundleRewardTmp.difficulty || []} />
                }
              </Col>
            </div>

            {/* 提交修改确认模态框 */}
            <Modal visible={submitComfirmVisible} title='确认提交审批么？' footer={null} onCancel={() => { this.setState({ submitComfirmVisible: false, editing: true }) }}>
              <Row style={{ marginBottom: '1em' }}>
                <Input placeholder='请输入备注信息 (选填)' onChange={(e) => { this.setState({ updateRemark: e.target.value }) }} />
              </Row>
              <Space>
                <Button onClick={() => { this.setState({ submitComfirmVisible: false, editing: true }) }}>再看看</Button>
                <Button danger type='primary' onClick={() => { this.onSubmitBundle() }}>提交审批</Button>
              </Space>
            </Modal>

          </Row>
        </Card>

      </>
    )
  }
}

export default BundleRewardTmp

// 计算概率
const countDifficultyByArray = (ary) => {
  let totalVal = 0
  ary.forEach(v => {
    totalVal += v * 1.0
  })
  return (100.0 / totalVal).toFixed(3)
}

// 计算总难度
const countTotalDifficulty = (ary) => {
  let totalVal = 0
  ary.forEach(v => {
    totalVal += v
  })
  return totalVal
}

// 生成礼盒价值描述区域
const genDiffAvgDiv = (poolConfigA, poolConfigB, bundleRewardA, bundleRewardB) => {
  // console.debug('poolConfigA, bundleRewardA, bundleRewardB===>', poolConfigA, bundleRewardA, bundleRewardB)
  const avg1A = CalculateAverage(poolConfigA, bundleRewardA, 1)
  const avg2A = CalculateAverage(poolConfigA, bundleRewardA, 2)
  const avg3A = CalculateAverage(poolConfigA, bundleRewardA, 3)
  const avg1B = CalculateAverage(poolConfigB, bundleRewardB, 1)
  const avg2B = CalculateAverage(poolConfigB, bundleRewardB, 2)
  const avg3B = CalculateAverage(poolConfigB, bundleRewardB, 3)
  return (
    <Space size='small' style={{ marginRight: '2em' }}>
      <Text strong >平均价值:</Text>
      <Text>钻石：{genDiffContent(avg1A, avg1B)}</Text><Divider type='vertical' />
      <Text>至尊：{genDiffContent(avg2A, avg2B)}</Text><Divider type='vertical' />
      <Text>王者：{genDiffContent(avg3A, avg3B)}</Text>
    </Space>
  )
}

const genMaxValueDiv = (poolConfigA, poolConfigB, bundleRewardA, bundleRewardB) => {
  const max1A = CalculateMaxValue(poolConfigA, bundleRewardA, 1)
  const max2A = CalculateMaxValue(poolConfigA, bundleRewardA, 2)
  const max3A = CalculateMaxValue(poolConfigA, bundleRewardA, 3)
  const max1B = CalculateMaxValue(poolConfigB, bundleRewardB, 1)
  const max2B = CalculateMaxValue(poolConfigB, bundleRewardB, 2)
  const max3B = CalculateMaxValue(poolConfigB, bundleRewardB, 3)
  return (
    <Space size='small'>
      <Text strong >最大价值:</Text>
      <Text>钻石：{genDiffContent(max1A, max1B)}</Text><Divider type='vertical' />
      <Text>至尊：{genDiffContent(max2A, max2B)}</Text><Divider type='vertical' />
      <Text>王者：{genDiffContent(max3A, max3B)}</Text>
    </Space>
  )
}

const genDiffContent = (after, before) => {
  if (after === before) {
    return <Text>{after}</Text>
  }
  return <Text type='danger'>({after})<Text type='secondary'>{before}</Text></Text>
}

function countFormater (text, dataIndex) {
  return ['dailyLimit', 'hoursLimit', 'rate', 'extA'].indexOf(dataIndex) === -1 || text > 0 ? text // 上限或概率大于0 发放
    : <font color={text === 0 ? 'red' : ''}>{['无上限', '不发放'][text + 1]}</font>
}

function countGetText (text, dataIndex) {
  return ['dailyLimit', 'hoursLimit', 'rate', 'extA'].indexOf(dataIndex) === -1 || text > 0 ? text
    : ['无上限', '不发放'][text + 1]
}
