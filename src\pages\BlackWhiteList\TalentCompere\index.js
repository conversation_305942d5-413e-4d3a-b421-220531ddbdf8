import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Button, Divider, Form, Card, Modal, Input, Select, Popconfirm, InputNumber, Space } from 'antd'
import { connect } from 'dva'
const { TextArea } = Input
const namespace = 'talentCompere'
const FormItem = Form.Item
// const Option = Select.Option
var moment = require('moment')

// 业务类型

@connect(({ talentCompere }) => ({
  model: talentCompere
}))

class Index extends Component {
  // column structs.
  columns = [
    
    { title: 'UID', dataIndex: 'uid', key: 'uid', align: 'center' },
    { title: '主持昵称', dataIndex: 'nick', key: 'nick', align: 'center' },
    { title: 'YY号', dataIndex: 'imid', key: 'imid', align: 'center' },
    { title: '签约公会', dataIndex: 'asid', key: 'asid', align: 'center' },
    { title: '主持身份', dataIndex: 'compereTypeName', key: 'compereTypeName', align: 'center' },
    {
      title: '更新时间', dataIndex: 'updateTime', key: 'updateTime', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(record)}>编辑</a><Divider type='vertical' />
          <Popconfirm title={'确认要删除?'} type='primary' onConfirm={this.handleDelOne(record)} okText='是的' cancelText='暂不'>
            <a href=''>删除</a>
          </Popconfirm>
        </span>
      )
    }
  ]

  defaultPageValue = {
    defaultPageSize: 100,
    pageSizeOptions: ['100', '200', '500', '2000'],
    showSizeChanger: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  state = {
    visible: false,
    isUpdate: false,
    confirmVisible: false,
    deleteConfirmMsg: '',
    value: {}
  }

  // show modal
  showModal = (record) => () => {
    var title = '修改'
    var opType = 1
    var isUpdate = false
    var tagValue = []
    if (record == null) {
      title = '添加'
      isUpdate = false
      record = { opType: 1 }
    } else {
      opType = 3
      isUpdate = true
      record.tagInfo.forEach((v, i) => tagValue.push(v.tagID))
    }
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.opType = opType
      v.tagList = tagValue
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, title: title, isUpdate: isUpdate })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // add and update
  onFinish = values => {
    const { dispatch } = this.props
    if (values.opType === 1) {
      dispatch({
        type: `${namespace}/addItem`,
        payload: values
      })
    } else {
      dispatch({
        type: `${namespace}/updateItem`,
        payload: values
      })
    }

    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  handleDelOne = record => e => {
    const { dispatch } = this.props
    const data = { uid: record.uid }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = { type: this.state.type }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
    dispatch({
      type: `${namespace}/getTagList`,
      payload: data
    })
  }

  searchBy = (value) => {
    const { dispatch } = this.props
    const data = { uid: this.state.uid }
    console.log(data)
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  exportData = (value) => {
    const { dispatch } = this.props
    const data = { uid: this.state.uid, export: true }
    console.log(data)
    dispatch({
      type: `${namespace}/exportList`,
      payload: data
    })
  }

  handleCategoryChange = (value) => {
    const { dispatch } = this.props
    const data = { uid: this.state.searchUid, type: value.key }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
    console.log(value.key)

    this.setState({ type: value.key })
  }

  handleTagChange = (value) => {
    console.log(`selected ${value}`)
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  // content
  render () {
    const { route, model: { list, tagList } } = this.props
    const { visible, title, isUpdate } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }
    if (this.formRef) {
      console.info('this.formRef-->', this.formRef.getFieldValue())
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Button type='primary' onClick={this.showModal(null)}>添加主持</Button>
        <Card>
          <Form>
            <Space>
              UID:
              <InputNumber min={1} style={{ width: 200 }} onChange={(e) => this.setState({ uid: e })} />
              <Divider type='vertical' /> {/* 分割线 */}
              <Button type='primary' onClick={this.searchBy}>查询</Button>
              <Button type='primary' onClick={this.exportData}>导出</Button>
            </Space>
            
            <Table rowKey={(record, index) => index} dataSource={list} columns={this.columns} pagination={this.defaultPageValue} />
          </Form>
        </Card>

        <Modal visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit} forceRender>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem label='opType' name='opType' rules={[{ required: true }]} hidden='true' >
              <Input readOnly={isUpdate} defaultValue='1' />
            </FormItem>
            <FormItem label='UID' name='uid' rules={[{ required: isUpdate }]} hidden={!isUpdate} >
              <Input disabled={isUpdate} />
            </FormItem>
            <FormItem label='YY' name='imIDList' rules={[{ required: !isUpdate }]} hidden={isUpdate} >
              <TextArea disabled={isUpdate} rows={6} placeholder='一行一个YY号' />
            </FormItem>
            <FormItem label='标签' name='tagList' rules={[{ required: true }]}>
              <Select
                mode='multiple'
                allowClear
                style={{
                  width: '100%'
                }}
                placeholder='请选择'
                defaultValue={[]}
                onChange={this.handleTagChange}
                options={tagList}
              />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default Index
