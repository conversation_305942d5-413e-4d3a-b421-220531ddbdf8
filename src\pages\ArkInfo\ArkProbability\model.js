import { simpleRequire2 } from '@/utils/common'
import { message } from 'antd'
import {
  doApprovalFromServer,
  getProbabilitySetting,
  updateProbabilitySetting
} from './api'

export default {
  namespace: 'arkProbabilitySetting',

  state: {
    listen: null,
    dev: false, // 开发可以修改业务关联
    poolConfig: {}, // 单个道具数额汇总配置
    editingConfig: {}, // 编辑中的道具数额汇总
    toApproval: {}, // 待审批流程
    probabilitySetting: {} // 概率配置
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },

    listen (state, { payload }) { return { ...state, listen: payload } },
    updatePoolConfig (state, { payload, dev }) {
      if (state.listen) {
        state.listen(payload)
      }

      return { ...state, poolConfig: payload, dev }
    },

    updateProbabilitySetting (state, { payload, dev }) {
      return { ...state, probabilitySetting: payload, dev }
    }
  },

  effects: {

    // 道具数额汇总配置
    * getPool ({ payload }, { call, put }) {
      const { data: { list, dev } } = yield call(getProbabilitySetting, payload)
      yield put({
        type: 'updatePoolConfig',
        payload: list,
        dev
      })
    },
    // 获取编辑中的道具数额汇总配置
    * getEditingPool ({ payload }, { call, put }) {
      const { id, cbFunc } = payload
      const { data } = yield call(getProbabilitySetting, { id: id })
      const { list, dev } = data
      yield put({
        type: 'updateState',
        payload: { name: 'poolConfig', newValue: list }
      })
      yield put({
        type: 'updateState',
        payload: { name: 'dev', newValue: dev }
      })
      let content = list.temporary
      let templist = []
      let prodList = []
      try {
        prodList = JSON.parse(list.content) // 正式奖励列表
        templist = JSON.parse(content.content) // 暂存奖励列表
      } catch (e) {
        message.error('json convert error ' + e)
        console.error('list=', list)
      }

      delete content['content']
      content.list = templist
      yield put({
        type: 'updateState',
        payload: { name: 'editingConfig', newValue: content }
      })
      yield put({
        type: 'updateState',
        payload: { name: 'prodList', newValue: prodList }
      })
      if (cbFunc) {
        cbFunc()
      }
    },
    // 获取待审批信息
    * getToApproval ({ payload }, { call, put }) {
      const { params, cbFunc } = payload
      let resp = yield call(simpleRequire2, `/approval/admin/get_to_approval_byPID`, params, false)
      yield put({
        type: 'updateState',
        payload: { name: 'toApproval', newValue: resp }
      })
      cbFunc(resp)
    },
    // 配置审批
    * doApproval ({ payload }, { call }) {
      const { params, cbFunc } = payload
      const { data: { status, msg } } = yield call(doApprovalFromServer, params)
      if (status === 0) {
        cbFunc(true, '')
      } else {
        cbFunc(false, msg)
      }
    },
    * editPool ({ payload }, { call }) {
      const { cbFunc, params } = payload
      const { data: { status, msg } } = yield call(updateProbabilitySetting, params)
      if (status === 0) {
        cbFunc(true)
      } else {
        message.error(msg)
        cbFunc(false)
      }
    },

    * getProbabilitySetting ({ payload }, { call, put }) {
      const { data: { list, dev } } = yield call(getProbabilitySetting, payload)

      yield put({
        type: 'updateProbabilitySetting',
        payload: list,
        dev
      })
    },
    * updateProbabilitySetting ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updateProbabilitySetting, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getProbabilitySetting',
          payload: { id: payload.id }
        })
      } else {
        message.error('failed' + msg)
      }
    }
  }
}
