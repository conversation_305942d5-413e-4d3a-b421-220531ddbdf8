import request from '@/utils/request'
import { stringify } from 'qs'

export function getCompereListFromServer () {
  return request('/zhuiya_recommend/v2/')
}

// 获取列表 包含搜索
export function getList (params) {
  return request(`/new_compere_info/get_micro_cover_list?${stringify(params)}`)
}

// 删除、支持批量
export function deleteItem (params) {
  return request(`/new_compere_info/remove_micro_cover_list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

// 更新
export function updateItem (params) {
  return request(`/new_compere_info/update_micro_cover_list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
  // return request(`/zhuiya_recommend/v2/compere_library/update?${stringify(params)}`)
}
