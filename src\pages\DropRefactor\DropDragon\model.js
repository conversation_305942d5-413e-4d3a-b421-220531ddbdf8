import { getLists, add, remove } from './api'
import { message } from 'antd'
import { genGetRequireTemplate } from '@/utils/common'

const getAllPrizeList = genGetRequireTemplate('/drop/admin/query_prize_list', 'globalPrizeList')

export default {
  namespace: 'dropDragon',

  state: {
    listJY: [],
    listBB: [],
    listZW: [],
    globalPrizeList: [],
    propsList: [] // 营收礼物列表(准备废弃)
  },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return { ...state, list: payload }
    },
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },
    updateLocalPropsList (state, { payload }) { return { ...state, propsList: payload } }
  },

  effects: {
    getAllPrizeList,

    * getList ({ payload }, { call, put }) {
      const { channel } = payload
      let { data: { list } } = yield call(getLists, payload)
      if (!Array.isArray(list)) {
        list = []
      }
      console.debug('payload===>', payload)
      for (var i = 0; i < list.length; i++) {
        list[i].index = i + 1
      }
      let listName = ''
      if (channel === 100) {
        listName = 'listJY'
      }
      if (channel === 200) {
        listName = 'listBB'
      }
      if (channel === 300) {
        listName = 'listZW'
      }
      yield put({
        type: 'updateState',
        payload: { name: listName, newValue: list }
      })
    },

    * addItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(add, payload)
      if (status === 0) {
        message.success('add success')
        yield put({
          type: 'getList',
          payload: { channel: payload.channel }
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * updateItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(add, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getList',
          payload: { channel: payload.channel }
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * removeItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(remove, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getList',
          payload: { channel: payload.channel }
        })
      } else {
        message.error('failed' + msg)
      }
    }

  }
}
