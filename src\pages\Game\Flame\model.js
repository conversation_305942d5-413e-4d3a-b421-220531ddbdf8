import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const getFlameConfig = genGetRequireTemplate('/flame/boss/get_config')
const udpateFlameConfig = genUpdateTemplate('/flame/boss/update_config')
const getCrucialReport = genGetRequireTemplate('/aggregator/get_flame_crucial_info')
const getDetailReport = genGetRequireTemplate('/aggregator/get_flame_detail_list')

export default {
  namespace: 'flameGame',
  state: {},

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getFlameConfig,
    udpateFlameConfig,
    getCrucialReport,
    getDetailReport
  }
}
