/* eslint-disable no-template-curly-in-string */
import React from 'react'
import { DatePicker, message } from 'antd'
import moment from 'moment'

/* SimpleTimePicker 时间选择器, 参考以下示例：
<SimpleTimePicker value={this.startTime} showTime format='YYYY-MM-DD HH:mm' />
<SimpleTimePicker value={moment()} outputType='timestamp' />
<SimpleTimePicker value={1684981100} showTime format='YYYY-MM-DD HH' inputType='timestamp' outputType='string' outputFormat='YYYY-MM-DD HH:mm:ss' />
<SimpleTimePicker value='2023-06-01 13:00' showTime format='YYYY-MM-DD HH:mm' inputType='string' outputType='string' />
<SimpleTimePicker value='${now-30days}' format='YYYY-MM-DD' inputType='variable' outputType='moment' />
*/
export const SimpleTimePicker = (props) => {
  const {
    value = null,
    showTime = false, // fallse=>只选日期不选时分秒
    format = 'YYYY-MM-DD HH:mm:ss', // 显示时间的格式
    inputType = 'moment', // 输入方式 [moment | timestamp | variable | string]
    outputType = 'moment', // 输出方式 [moment | timestamp | string]
    style = {}, // 附加样式
    outputFormat = 'YYYY-MM-DD', // 输出格式,选填
    onChange = (v) => { console.debug('SimpleTimeRange onChange: ', v) },
    oldProps = {}
  } = props

  const fixInput = (before) => {
    switch (inputType) {
      case 'moment' || '':
        return before
      case 'timestamp':
        return moment.unix(parseInt(before))
      case 'variable':
        return parseTimeVariable(before)
      case 'string':
        return moment(before)
    }
    message.warn('SimpleTimePicker 用法错误: inputType=' + inputType)
    return null
  }

  const fixOutput = (before) => {
    switch (outputType) {
      case 'moment' || '':
        return before
      case 'timestamp':
        return before.unix()
      case 'string':
        return before.format(outputFormat)
    }
    message.warn('SimpleTimePicker 用法错误: outputType=' + outputType)
    return null
  }

  return <DatePicker
    value={fixInput(value)}
    format={format}
    showTime={showTime}
    onChange={(v) => { onChange(fixOutput(v)) }}
    showNow
    {...oldProps}
    {...style} />
}

/* SimpleTimeRangePicker 时间范围选择器, 参考以下使用示例
<SimpleTimeRangePicker value={[this.startTime, this.endTime]} inputType='moment[]' showTime format='YYYY-MM-DD HH:mm' outputType='moment[]' />
<SimpleTimeRangePicker value={[1683170282, 1684984682]} inputType='timestamp[]' showTime format='YYYY-MM-DD HH:mm:ss' outputType='timestamp[]' />
<SimpleTimeRangePicker value={['2023-01-01', '2023-06-01']} inputType='string[]' format='YYYY-MM-DD' outputFormat='YYYY-MM-DD' outputType='string[]' />
<SimpleTimeRangePicker value='${now-30days}~${now-2days}' inputType='variable' format='YYYY-MM-DD' outputFormat='YYYYMMDD' outputType='string' />
*/
export const SimpleTimeRangePicker = (props) => {
  const {
    value = null,
    showTime = false,
    format = 'YYYY-MM-DD HH:mm:ss',
    inputType = 'moment[]', // moment[] | timestamp[] | string[] | variable
    outputType = 'moment[]', // moment[] | timestamp[] | string[] | string
    outputFormat = 'YYYY-MM-DD', // 输出格式,选填
    style = {},
    onChange = (v) => { console.debug('SimpleTimeRangePicker onChange: ', v) },
    oldProps
  } = props

  const fixInput = (before) => {
    if (before === null) {
      return moment()
    }
    switch (inputType) {
      case 'moment[]' || '' :
        return before
      case 'timestamp[]':
        return [moment.unix(before[0]), moment.unix(before[1])]
      case 'string[]':
        return [moment(before[0]), moment(before[1])]
      case 'variable':
        let valuePaire = before.split('~')
        if (valuePaire.length === 2 && before.indexOf('$') >= 0) {
          return [parseTimeVariable(valuePaire[0]), parseTimeVariable(valuePaire[1])]
        }
        if (valuePaire.length === 2 && before.indexOf('$') < 0) {
          return [moment(valuePaire[0], outputFormat), moment(valuePaire[1], outputFormat)]
        }
        return []
    }
    message.warn('SimpleTimeRangePicker 用法错误: inputType=' + inputType)
    return null
  }

  const fixOutput = (before) => {
    switch (outputType) {
      case 'moment[]' || '':
        return before
      case 'timestamp[]':
        if (before === null) return []
        return [before[0].unix(), before[1].unix()]
      case 'string[]':
        if (before === null) return []
        return [before[0].format(outputFormat), before[1].format(outputFormat)]
      case 'string':
        if (before === null) return ''
        return `${before[0].format(outputFormat)}~${before[1].format(outputFormat)}`
    }
    message.warn('SimpleTimeRangePicker 用法错误: outputType=' + outputType)
    return before
  }

  if (value === null) {
    return '???'
  }

  // console.debug('temp===>', fixOutput(fixInput(value)))
  onChange(fixOutput(fixInput(value)))

  return <DatePicker.RangePicker
    value={fixInput(value)}
    format={format}
    showTime={showTime}
    onChange={(v) => { onChange(fixOutput(v)) }}
    showNow
    {...oldProps}
    {...style} />
}

// 解析时间,格式如 '${now-1days}'
const parseTimeVariable = (value) => {
  if (value === '${now}') {
    return moment()
  }
  const regexp = /^\$\{now(\+|-)(\d+)(minutes|hours|days|weeks|months)\}$/
  const matchResult = value.match(regexp)
  if (matchResult !== null && matchResult.length === 4) {
    const [, symble, count, unit] = matchResult
    if (symble === '-') {
      return moment().subtract(parseInt(count), unit)
    } else {
      return moment().add(parseInt(count), unit)
    }
  }

  return null
}
