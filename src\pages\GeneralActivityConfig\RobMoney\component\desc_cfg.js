import React, { Component } from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>, But<PERSON>, Modal, Form, Table, DatePicker, Popconfirm, message, InputNumber, Input } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import { connect } from 'dva'
import moment from 'moment'

const format = 'YYYY-MM-DD HH:mm:ss'
const namespace = 'robMoney' // model 的 namespace
const FormItem = Form.Item
const TextArea = Input.TextArea

@connect(({ rob<PERSON><PERSON> }) => ({ // model 的 namespace
  model: robMoney // model 的 namespace
}))
class DescCfgComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false
    }
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getDescCfgList`
    })
  }

  // 需要修改
  columns = [
    { title: 'ID', dataIndex: 'id', align: 'center' },
    { title: '开始时间', dataIndex: 'begin', align: 'center', render: text => moment.unix(text).format(format) },
    { title: '结束时间', dataIndex: 'end', align: 'center', render: text => moment.unix(text).format(format) },
    { title: '文案', dataIndex: 'desc', align: 'center' },
    { title: '操作',
      align: 'center',
      render: (text, record) => (
        <div>
          <a><DeleteOutlined style={{ marginRight: 10 }} onClick={this.showModal(true, record)} /></a>
          <Popconfirm onConfirm={this.handleRemove(record.id)} title='确认删除？'><a><DeleteOutlined style={{ color: 'red' }} type='delete' /></a></Popconfirm>
        </div>
      )
    }
  ]

  now = moment().unix()
  defaultValue = {
    begin: this.now,
    end: this.now
  }

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.begin = moment.unix(v.begin)
      v.end = moment.unix(v.end)
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // 隐藏弹窗，不需要修改
  hideModal = () => {
    this.setState({ visible: false })
  }

  onFinish = values => {
    const { dispatch, model: { tagList } } = this.props
    const { isUpdate } = this.state
    values.begin = values.begin.unix()
    values.end = values.end.unix()

    var list = tagList
    if (!isUpdate) {
      for (var i = 0; Array.isArray(list) && i < list.length; i++) {
        if (list[i].tagId === values.tagId) {
          message.error('period exist', 5)
          return
        }
      }
    }

    // console.log(values, list)
    dispatch({
      type: `${namespace}/upsetDescCfgList`,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 更新与删除
  handleSubmit = () => {
    this.formRef.submit()
  }

  handleRemove = id => () => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/removeDescCfgList`,
      payload: { id: id }
    })
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { descList } } = this.props
    const { visible, title, isUpdate } = this.state
    const formItemLayout = { // 不需要修改
      labelCol: {
        xs: { span: 4 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 20 },
        sm: { span: 20 }
      }
    }

    return (
      <Card>
        <Button onClick={this.showModal(false, this.defaultValue)}>添加</Button>
        <Divider />
        <Table rowKey={(record, index) => index} dataSource={descList} columns={this.columns} size='small' pagination={false} /> {/* 显示的列表 */}

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} ref={form => { this.formRef = form }} {...formItemLayout}>
            <FormItem label='ID' name='id' rules={[{ required: true }]}>
              <InputNumber min={1} readOnly={isUpdate} style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='开始时间' name='begin' rules={[{ required: true }]}>
              <DatePicker format={format} showTime style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='结束时间' name='end' rules={[{ required: true }]}>
              <DatePicker formort={format} showTime style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='文案' name='desc' rules={[{ required: true }]}>
              <TextArea rows={10} />
            </FormItem>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default DescCfgComponent
