import { connect } from 'dva'
import React, { Component } from 'react'
import { Button, Col, Divider, Form, Input, Modal, Row, Select, Spin, Table, message } from 'antd'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import ExportRemoteData from '@/components/ExportRemoteData'

const namespace = 'MongoTools'
const collectionInfoAction = 'collectionInfo'
const queryExplainAction = 'queryExplain'
const listAction = 'list'

const maxListPageSize = 500

@connect(({ MongoTools }) => ({
  model: MongoTools
}))

class MongoTools extends Component {
  state = {
    list: [], loadingVisible: false, currentPage: 1, perPage: 1000, currentFetch: 0, pagination: { current: 1, pageSize: 10 }
  }

  getQueryCondition = (action) => {
    let values = this.formRefQuery ? this.formRefQuery.getFieldsValue() : {}
    const { model: collections } = this.props
    if (action === listAction) {
      if (values.page && values.page < 1) {
        values.page = 1
      }
      values.page = parseInt(values.page)
      if (values.pageSize) {
        values.pageSize = parseInt(values.pageSize)
        if (values.pageSize < 1) {
          values.pageSize = 10
        } else if (values.pageSize > maxListPageSize) {
          message.warn('pageSize must <= ' + maxListPageSize)
          return false
        }
      }
    }

    if (!values.mongoURL || !values.col) {
      message.warn('MongoURL and collection both required')
      if (values.mongoURL && (!collections || collections.length < 1)) {
        this.refreshCollections()
      }
      return false
    }
    let query = {}
    if (action === listAction || action === queryExplainAction) {
      try {
        if (values.query) {
          query = JSON.parse(values.query)
        }
      } catch (e) {
        message.warn('invalid query:' + e)
        return false
      }
    }

    return {
      mongoURL: values.mongoURL, db: values.db || '', col: values.col, columns: values.columns || [], query: query, page: values.page, size: values.pageSize
    }
  }

  // 点击 查询集合信息按钮
  onCollectionInfoClick = () => {
    let params = this.getQueryCondition(collectionInfoAction)
    if (params === false) {
      return
    }
    params.query = {}

    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getCollectionInfo`,
      payload: params,
      callback: (info) => {
        this.setState({ action: collectionInfoAction })
      }
    })
  }

  // 点击 查询分析
  onQueryExplainClick = () => {
    let params = this.getQueryCondition(queryExplainAction)
    if (params === false) {
      return
    }
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getQueryExplain`,
      payload: params,
      callback: (info) => {
        this.setState({ action: queryExplainAction })
      }
    })
  }

  // 查询文档
  onQueryDocsClick = () => {
    let params = this.getQueryCondition(listAction)
    if (params === false) {
      return
    }
    console.log('params:', params)
    params.page = 1
    params.size = params.size || 10

    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getMongoDocsPage`,
      payload: params,
      callback: (info) => {
        this.setState({ action: listAction })
      }
    })
  }

  // 刷新集合列表
  refreshCollections = () => {
    const { dispatch } = this.props
    const { lastQueryMongoURL } = this.state

    let mongoURL = this.formRefQuery ? this.formRefQuery.getFieldValue('mongoURL') || lastQueryMongoURL || '' : ''
    dispatch({
      type: `${namespace}/listCollections`,
      payload: {
        mongoURL: mongoURL,
        clear: !mongoURL || mongoURL === lastQueryMongoURL
      }
    })
  }

  getExportColumns = (dataList) => {
    if (this.formRefQuery) {
      let columns = this.formRefQuery.getFieldValue('columns')
      if (!columns || columns.length < 1) {
        return []
      }
      let exportColumns = []
      columns.forEach(column => {
        exportColumns.push({
          title: column,
          dataIndex: column,
          align: 'left',
          ellipsis: true
        })
      })
    }
    // 返回空的，使用默认的
    return []
  }

  render () {
    const { route, model: { collections, collectionInfo, queryExplain, docList, docColumns } } = this.props
    const { loadingVisible, currentPage, perPage, currentFetch, action } = this.state

    let col = this.formRefQuery ? this.formRefQuery.getFieldValue('col') : ''
    let exportAllDisabled = !queryExplain || !queryExplain.executionStats || !col || !queryExplain.queryPlanner || !queryExplain.queryPlanner.namespace || !queryExplain.queryPlanner.namespace.endsWith(col)

    let columns = docColumns || []
    if (columns.length <= 0) {
      columns = collectionInfo.columns || []
    }

    return (
      <>
        <PageHeaderWrapper title={route.name}>
          <Row style={{ marginBottom: '1em' }}>
            <Form labelCol={{ span: 12 }} layout={'inline'} ref={form => {
              this.formRefQuery = form
            }}>
              <Form.Item name={'mongoURL'} label={'URL'} required>
                <Input style={{ width: 700 }} placeholder='MongoURL' allowClear />
              </Form.Item>
              <Form.Item name={'db'} label={'DB'}>
                <Input placeholder='DB' allowClear />
              </Form.Item>

              <Form.Item name={'col'} label={'collection'}>
                <Select style={{ width: 400 }} options={(collections || []).map(v => {
                  return { label: v, value: v }
                })} onFocus={this.refreshCollections} showSearch filterOption onChange={this.onCollectionInfoClick} />
              </Form.Item>

              <Divider type={'horizontal'} />

              <Form.Item name={'query'} label={'query'}>
                <Input.TextArea rows={3} style={{ minWidth: 600 }} placeholder='查询条件' allowClear />
              </Form.Item>

              <Form.Item name={'columns'} label={'columns'}>
                <Select mode='multiple' style={{ width: 300 }} options={(columns || []).map(v => {
                  return { label: v.title, value: v.title }
                })} showSearch filterOption />
              </Form.Item>

              <Form.Item name={'page'} label={'page'} initialValue={1} rules={[
                {
                  validator: (_, value) => {
                    if (value.length > 0) {
                      let v = parseInt(value)
                      if (v <= 0) {
                        return Promise.reject(new Error('required: page >= 1'))
                      }
                    }
                    return Promise.resolve()
                  }
                }
              ]}>
                <Input placeholder='page' allowClear style={{ width: 120 }} />
              </Form.Item>

              <Form.Item name={'pageSize'} label={'pageSize'} initialValue={10} rules={[
                {
                  validator: (_, value) => {
                    if (value.length > 0) {
                      let v = parseInt(value)
                      if (v <= 0 || v > maxListPageSize) {
                        return Promise.reject(new Error('required: 1 <= pageSize <= ' + maxListPageSize))
                      }
                    }
                    return Promise.resolve()
                  }
                }
              ]}>
                <Input placeholder='pageSize' allowClear style={{ width: 120 }} />
              </Form.Item>

              <Divider type={'horizontal'} />
              <Button type='primary' onClick={this.onCollectionInfoClick}>集合信息</Button>
              <Button type='primary' style={{ marginLeft: 5 }} onClick={this.onQueryExplainClick}>查询分析</Button>
              <Button type='primary' style={{ marginLeft: 5 }} onClick={this.onQueryDocsClick}>数据列表</Button>
              <ExportRemoteData disabled={!docList || docList.length < 1 || action !== listAction} title={'当前查询结果导出'} buttonStyle={{ marginLeft: 20 }} filename={this.formRefQuery ? this.formRefQuery.getFieldValue('col') : 'docs'} columns={this.getExportColumns} dataProvider={() => docList} />
              <ExportRemoteData
                disabled={exportAllDisabled}
                pageSize={maxListPageSize}
                title={'导出当前条件所有数据'}
                method={'POST'}
                buttonStyle={{ marginLeft: 20 }}
                filename={this.formRefQuery ? this.formRefQuery.getFieldValue('col') : 'docs'}
                columns={this.getExportColumns}
                uriBuilder={(page, size) => {
                  let params = this.getQueryCondition(listAction)
                  params.page = page
                  params.size = size
                  return { uri: `/assist_tools/mongo/list_collection_docs`, body: JSON.stringify(params) }
                }}
              />
            </Form>
          </Row>
          <Row hidden={action !== listAction} style={{ marginBottom: '1em' }}>
            <Col span={24}>
              <Table columns={docColumns || []}
                dataSource={docList}
                size='small'
                scroll={{ x: 'max-content' }}
                pagination={false}
                showSorterTooltip={false}
                rowKey={record => record['_id']}
                expandable={{
                  expandRowByClick: true,
                  expandedRowRender: record => <pre style={{ margin: 0 }}>{JSON.stringify(record, null, 4)}</pre>
                }}
              />
            </Col>
          </Row>

          <Row hidden={action !== collectionInfoAction}>
            <Col span={12} style={{ float: 'left' }}>
              <div>首行记录信息：</div>
              <pre>{collectionInfo && collectionInfo.firstRec ? JSON.stringify(collectionInfo.firstRec, null, 2) : ''}</pre>
            </Col>
            <Col span={12} style={{ float: 'right' }}>
              <div>索引信息</div>
              <pre>{collectionInfo && collectionInfo.indexes ? JSON.stringify(collectionInfo.indexes, null, 2) : ''}</pre>
            </Col>
          </Row>

          <Row hidden={action !== queryExplainAction}>
            <Col span={6} style={{ float: 'left' }}>
              <div>ExecutionStats：</div>
              <pre>{queryExplain && queryExplain.executionStats ? JSON.stringify(queryExplain.executionStats, null, 2) : ''}</pre>
            </Col>
            <Col span={10} style={{ float: 'right' }}>
              <div>QueryPlanner：</div>
              <pre>{queryExplain && queryExplain.queryPlanner ? JSON.stringify(queryExplain.queryPlanner, null, 2) : ''}</pre>
            </Col>
            <Col span={8} style={{ float: 'right' }}>
              <div>ServerInfo：</div>
              <pre>{queryExplain && queryExplain.serverInfo ? JSON.stringify(queryExplain.serverInfo, null, 2) : ''}</pre>
            </Col>
          </Row>

          <Modal visible={loadingVisible} footer={null} closable={false} centered>
            <Spin
              tip={'正在加载第[' + currentPage + ']页数据（每页[' + perPage + ']条）, 当前已加载[' + currentFetch + ']......'} />
          </Modal>
        </PageHeaderWrapper>
      </>
    )
  }
}

export default MongoTools
