import { simpleRequire2, genGetListTemplate, simpleRequireJson, genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'
import { message } from 'antd'
import {
  addRuleFromServer,
  updateRuleFromServer,
  removeRuleFromServer,
  getRuleListFromServer,
  addPoolFromServer,
  removePoolFromServer,
  updatePoolFromServer,
  editPoolFromServer,
  doApprovalFromServer,
  updatePoolDescFromServer,
  getPoolFromServer,
  getProgressFromServer,
  removeProgressFromServer,
  upsetProgressFromServer,
  getWarnFromServer,
  removeWarnFromServer,
  upsetWarnFromServer,
  getPoolEstimateFromServer,
  upsetPoolEstimateFromServer
} from './api'

const getNewUserRewardConfig = genGetListTemplate('/drop/admin/get_new_player_props_odds', 'newUserRewardConfig')
const getNewPlayerHitList = genGetListTemplate('/drop_stat/new_player_hit_list', 'newPlayerHitList', raw => {
  if (Array.isArray(raw)) {
    for (let i = 0; i < raw.length; i++) raw[i].index = i + 1
  }
  return raw
})

const queryPoolList = genGetRequireTemplate('/drop/admin/query_pool_list', 'poolListConfig')
const getSummaryListByPoolID = genGetListTemplate('/drop_stat/summary_list', 'summaryListGroup') // 空投配置-实时信息, sel=5
const queryChart = genGetRequireTemplate('/drop_stat/drop_chart', 'chartValue') // 空投配置-实时信息, sel=5
const getAllPrizeList = genGetRequireTemplate('/drop/admin/query_prize_list', 'globalPrizeList')
const getExtraActivityList = genGetRequireTemplate('/drop/admin/query_extra_activity_config', 'extraActivityList')
const updateExtraActivityConfig = genUpdateTemplate('/drop/admin/update_extra_activity_config')

export default {
  namespace: 'dropMain',

  state: {
    listen: null,
    dev: false, // 开发可以修改业务关联
    poolListConfig: [], // 道具池列表
    globalPrizeList: [], // 新的礼物列表(多地方使用)
    ruleList: [], // 渠道列表
    poolConfig: {}, // 单个道具池配置
    allPrizeList: [], // 所有礼物列表
    summaryListGroup: [], // 实时信息
    progressList: [], // 进度奖励
    warnList: [], // 预警配置
    chartValue: {}, // 统计图表
    poolEstimateList: [], // 道具池预估
    editingConfig: {}, // 编辑中的道具池
    prodConfig: {}, // 生效中的道具池
    toApproval: {}, // 待审批流程
    newUserRewardConfig: [], // 新用户奖励策略
    newUserList: [], // 新用户列表
    newPlayerHitList: [], // 新用户命中监控
    extraActivityList: [], // 翻倍活动配置

    // =========== 通用配置 ===========
    poolNameOptions: [], // 道具池ID列表
    businessNameOptions: [], // 业务名称选项
    appNameOptions: [] // 应用端名称选项
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },

    listen (state, { payload }) { return { ...state, listen: payload } },

    updateRuleList (state, { payload }) { return { ...state, ruleList: payload } },

    updatePoolConfig (state, { payload, dev }) {
      if (state.listen) {
        state.listen(payload)
      }

      return { ...state, poolConfig: payload, dev }
    },
    updateProgressList (state, { payload }) { return { ...state, progressList: payload } },
    updateWarnList (state, { payload }) { return { ...state, warnList: payload } },
    updatePoolEstimateList (state, { payload }) { return { ...state, poolEstimateList: payload } }
  },

  effects: {
    queryPoolList,
    getNewPlayerHitList,
    getNewUserRewardConfig,
    getSummaryListByPoolID,
    queryChart,
    getAllPrizeList,
    getExtraActivityList,
    updateExtraActivityConfig,

    // 渠道配置
    * getRule ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getRuleListFromServer)

      yield put({
        type: 'updateRuleList',
        payload: Array.isArray(list) ? list : []
      })
    },
    * updateRule ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updateRuleFromServer, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getRule'
        })
      } else {
        message.error('failed' + msg)
      }
    },
    * removeRule ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(removeRuleFromServer, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getRule'
        })
      } else {
        message.error('failed ' + msg)
      }
    },

    * addRule ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(addRuleFromServer, payload)
      if (status === 0) {
        message.success('add success')
        yield put({
          type: 'getRule'
        })
      } else {
        message.error(msg)
      }
    },

    // 道具池配置
    * getPool ({ payload }, { call, put }) {
      const { data: { list, dev } } = yield call(getPoolFromServer, payload)
      yield put({
        type: 'updatePoolConfig',
        payload: list,
        dev
      })
    },
    // 获取编辑中的道具池配置
    * getEditingPool ({ payload }, { call, put }) {
      const { id, cbFunc } = payload
      const { data } = yield call(getPoolFromServer, { id: id })
      const { list, dev } = data
      yield put({
        type: 'updateState',
        payload: { name: 'poolConfig', newValue: list }
      })
      yield put({
        type: 'updateState',
        payload: { name: 'dev', newValue: dev }
      })
      let content = list.temporary
      let templist = []
      let prodList = []
      try {
        prodList = JSON.parse(list.content) // 正式奖励列表
        templist = JSON.parse(list.temporary.content) // 暂存奖励列表
      } catch (e) {
        message.error('json convert error ' + e)
        console.error('list=', list)
      }
      delete content['content']
      prodList.forEach((item, index, ary) => { if (item.limitSetting) { item.limitSetting.poolID = id } })
      templist.forEach((item, index, ary) => { if (item.limitSetting) { item.limitSetting.poolID = id } })
      content.list = templist
      yield put({
        type: 'updateState',
        payload: { name: 'editingConfig', newValue: content }
      })
      yield put({
        type: 'updateState',
        payload: { name: 'prodList', newValue: prodList }
      })
      if (cbFunc) {
        cbFunc()
      }
    },
    // 获取待审批信息
    * getToApproval ({ payload }, { call, put }) {
      const { params, cbFunc } = payload
      let resp = yield call(simpleRequire2, `/approval/admin/get_to_approval_byPID`, params, false)
      yield put({
        type: 'updateState',
        payload: { name: 'toApproval', newValue: resp }
      })
      cbFunc(resp)
    },
    // 配置审批
    * doApproval ({ payload }, { call, put }) {
      const { params, cbFunc } = payload
      const { data: { status, msg } } = yield call(doApprovalFromServer, params)
      if (status === 0) {
        cbFunc(true, '')
      } else {
        cbFunc(false, msg)
      }
    },
    // 新用户策略-奖励概率更新
    * updateNewUserOdds ({ payload }, { call, put }) {
      const { params, cbFunc } = payload
      let resp = yield call(simpleRequireJson, `/drop/admin/update_new_player_props_odds`, params)
      console.debug('resp===>', resp)
      cbFunc(resp)
    },
    // 新用户策略-查询目标用户列表
    * getNewUserList ({ payload }, { call, put }) {
      const { startTime, endTime, cbFunc } = payload
      let resp = yield call(simpleRequire2, `/drop/admin/get_new_player_user_list?startTime=${startTime}&endTime=${endTime}`)
      if (Array.isArray(resp)) {
        for (let i = 0; i < resp.length; i++) resp[i].index = i + 1
      }
      cbFunc(resp)
      yield put({
        type: 'updateState',
        payload: { name: 'newUserList', newValue: resp === 1 ? [] : resp }
      })
    },
    // 新用户策略-导入新用户
    * importNewPlayer ({ payload }, { call, put }) {
      const { params, cbFunc } = payload
      let resp = yield call(simpleRequireJson, `/drop/admin/update_new_player_user_list`, params)
      cbFunc(resp)
    },
    // 新用户策略-强制失效
    * disableUID ({ payload }, { call, put }) {
      const { uid, cbFunc } = payload
      let resp = yield call(simpleRequire2, `/drop/admin/disable_new_player?uid=${uid}`)
      cbFunc(resp)
    },
    * updatePool ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updatePoolFromServer, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getPool',
          payload: { id: payload.id }
        })
      } else {
        message.error('failed' + msg)
      }
    },
    * editPool ({ payload }, { call, put }) {
      const { cbFunc, params } = payload
      const { data: { status, msg } } = yield call(editPoolFromServer, params)
      if (status === 0) {
        cbFunc(true)
      } else {
        message.error(msg)
        cbFunc(false)
      }
    },

    * updatePoolDesc ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updatePoolDescFromServer, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getPoolList',
          payload: { id: payload.id }
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * removePool ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(removePoolFromServer, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getPoolList'
        })
      } else {
        message.error('failed ' + msg)
      }
    },

    * addPool ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(addPoolFromServer, payload)
      if (status === 0) {
        message.success('add success')
        yield put({
          type: 'getPoolList'
        })
      } else {
        message.error(msg)
      }
    },

    * getProgressList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getProgressFromServer)

      yield put({
        type: `updateProgressList`,
        payload: Array.isArray(list) ? list : []
      })
    },

    * removeProgress ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(removeProgressFromServer, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getProgressList'
        })
      } else {
        message.error(msg)
      }
    },

    * upsetProgress ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(upsetProgressFromServer, payload)
      if (status === 0) {
        message.success('upset success')
        yield put({
          type: `getProgressList`
        })
      } else {
        message.error(msg)
      }
    },

    * getWarnList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getWarnFromServer)

      yield put({
        type: `updateWarnList`,
        payload: Array.isArray(list) ? list : []
      })
    },

    * removeWarn ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(removeWarnFromServer, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getWarnList'
        })
      } else {
        message.error(msg)
      }
    },

    * upsetWarn ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(upsetWarnFromServer, payload)
      if (status === 0) {
        message.success('upset success')
        yield put({
          type: `getWarnList`
        })
      } else {
        message.error(msg)
      }
    },

    * getPoolEstimateList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getPoolEstimateFromServer)

      yield put({
        type: `updatePoolEstimateList`,
        payload: Array.isArray(list) ? list : []
      })
    },

    * updatePoolEstimate ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(upsetPoolEstimateFromServer, payload)

      if (status !== 0) {
        message.warn(msg)
        return
      }

      yield put({
        type: `getPoolEstimateList`
      })
    }
  }
}
