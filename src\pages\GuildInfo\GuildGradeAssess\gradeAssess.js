import React, { Component } from 'react'
import { connect } from 'dva'
import { message, Input, Row, Col, InputNumber, Select, Button, Space, Table, Typography, Divider, Modal, Form, Tooltip } from 'antd'
import { deepClone, getCookie } from '@/utils/common'
import { tagOptions, MonthRangeStr, ReviewInfo, classValueFormater, methodFormater, defaultGuildInfo, genRemoveParams, SidDetailInfo } from './common' 

const { Link, Text } = Typography

const namespace = 'guildGradeAssess'

@connect(({ guildGradeAssess }) => ({
  model: guildGradeAssess
}))

class GuildGradeAssess extends Component {
  state = {
    queryParams: {
      queryAsid: null,
      querySid: null,
      queryOwYY: null,
      queryGrade: -1
    },
    reviewParams: {
      queryAsid: null,
      querySid: null,
      queryOwYY: null,
      queryGrade: -1
    },
    superGuildList: [],
    sidToAccountName: {},
    reviewList: [],
    updateGradeVisible: false,
    updateTagVisible: false,
    updateReviewVisible: false,
    reviewHistoryVisible: false,
    addSuperGuildVisible: false,
    updateSuperGuildVisible: false,
    removeVisible: false,
    editingItem: {},
    queryHistoryItem: {},
    removeReason: '',
    removeList: [],
    sidDetailInfo: null,
    sidDetailVisible: false
  }

  componentDidMount = () => {
    this.getTimeTable()
    this.querySuperGuildInfo() 
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // ================== 查询数据 ===================

  getTimeTable = () => {
    this.callModel('getTimeTable')
  }

  querySuperGuildInfo = () => {
    this.callModel('querySuperGuildInfo', {
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg, list } = ret
        if (status !== 0) {
          message.warn('查询数据失败: ' + msg)
          return
        }
        if (list.length === 0) {
          message.warn('数据为空～')
          return
        }
        let sidList = []
        this.setState({ superGuildList: list })
        list.forEach(item => {
          sidList.push(item.sid)
        })
        this.queryAccountName(sidList)
      }
    })
  }

  queryReviewInfo = () => {
    let reason = this.getIsTimeToReview()
    if (reason) {
      message.warn(reason)
      return
    }

    this.callModel('getReviewInfo', {
      params: {},
      isJsonMode: true,
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg, data } = ret
        if (status !== 0) {
          message.warn('获取列表失败:' + msg)
          return
        }
        let list = data.data.map(item => {
          if (!item.manualGrade) {
            item.manualGrade = item.systemGrade
          }
          return item
        })
        this.setState({ updateReviewVisible: true, reviewList: list })
      }
    })
  }

  queryReviewHistory = (sid, record) => {
    this.callModel('queryReviewHistory', {
      params: {
        sid: sid
      },
      isJsonMode: true,
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg, data } = ret
        if (status !== 0) {
          message.warn('获取列表失败:' + msg)
          return
        }
        this.setState({ reviewHistoryVisible: true, reviewHistoryList: data, queryHistoryItem: record })
      }
    })
  }

  queryAccountName = (list) => {
    this.callModel('queryUidToAccountName', {
      params: { sidList: list },
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg, list } = ret
        if (status !== 0) {
          message.warn('查询对公账户名失败: ' + msg)
          return
        }
        this.setState({ sidToAccountName: list })
      }
    })
  }

  // ================== 提交更新 ===================

  // 更新等级
  onSubmitUpdateGrade = (v) => {
    this.callModel('udpateGrade', {
      params: {
        sid: v.sid,
        grade: v.grade
      },
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('操作失败: ' + msg)
          return
        }
        message.success('已提交审批～')
        this.setState({ updateGradeVisible: false })
        this.getTimeTable()
        this.querySuperGuildInfo()
      }
    })
  }

  // 更新标签
  onSubmitUpdateTag = (v) => {
    const { sid, tag, notCheckPeriod } = v
    if (tag && !notCheckPeriod) {
      message.warn('新公会必须填不考核周期')
      return
    }
    if (!tag && notCheckPeriod) {
      message.warn('非公会不能填写不考核周期')
      return
    }

    this.callModel('updateTag', {
      params: {
        sid: sid,
        newTag: tag,
        notCheckPeriod: notCheckPeriod
      },
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('操作失败: ' + msg)
          return
        }
        message.success('已提交审批～')
        this.setState({ updateTagVisible: false })
        this.getTimeTable()
        this.querySuperGuildInfo()
      }
    })
  }

  // 提交复核审批
  onSubmitReview = (list) => {
    let myNick = getCookie('username')
    let newGrade = {}
    let itemCount = 0
    let fixedList = list.map(item => {
      return this.parseAprItem(item)
    })
    list.forEach(item => {
      if (this.IsNotCheck(item.notCheckPeriod)) {
        return
      }
      itemCount++
      newGrade[item.sid] = item.manualGrade
    })
    let aprData = JSON.stringify(fixedList)
    let params = {
      wcText: `${myNick} 申请复核超级水晶公会评级信息, 参与评级数量: ${itemCount}。`,
      aprData: aprData,
      newGrade: newGrade
    }
    this.callModel('gradeReview', {
      params: params,
      isJsonMode: true,
      isDetailMode: true,
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('操作失败: ' + msg)
          return
        }
        this.setState({ updateReviewVisible: false })
        message.success('送审成功～')
        this.getTimeTable()
        this.querySuperGuildInfo()
      }
    })
  }

  // 添加超级水晶公会信息 TODO: 此功能已下线
  onAddSuperGuildInfo = (v) => {
    this.callModel('addSuperGuildInfo', {
      params: v,
      isJsonMode: true,
      isDetailMode: true,
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('添加失败: ' + msg)
          return
        }
        message.success('添加成功～')
        this.setState({ addSuperGuildVisible: false })
        this.querySuperGuildInfo()
        this.getTimeTable()
      }
    })
  }

  // 更新超级水晶公会信息
  onUpdateSuperGuildInfo = (v) => {
    this.callModel('udpateSuperGuildInfo', {
      params: v,
      isJsonMode: true,
      isDetailMode: true,
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('更新失败: ' + msg)
          return
        }
        message.success('更新成功～')
        this.setState({ updateSuperGuildVisible: false })
        this.querySuperGuildInfo()
        this.getTimeTable()
      }
    })
  }

  // 批量移除
  onBatchRemoveSuperGuildInfo = (sidList, reason) => {
    if (sidList.length === 0) {
      message.warn('请选择需要移除的SID')
      return
    }
    if (!reason) {
      message.warn('请填写移除的原因')
      return
    }
    const uid = getCookie('yyuid')
    const nick = getCookie('username')
    if (!uid || !nick) {
      message.warn('获取uid失败~')
      return
    }
    const params = genRemoveParams(uid, nick, sidList, reason)
    this.callModel('batchRemoveSid', {
      params: params,
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('操作失败: ' + msg)
          return
        }
        message.success('操作成功~')
        this.setState({ removeVisible: false, removeList: [] })
      }
    })
  }

  // ================== update state ===================

  updateEditingItem = (field, value) => {
    const { editingItem } = this.state
    let cp = deepClone(editingItem)
    cp[field] = value
    if (field === 'tag' && value === '') {
      cp.notCheckPeriod = ''
    }
    this.setState({ editingItem: cp })
  }

  updateQueryParams = (field, value) => {
    const { queryParams } = this.state
    let cp = deepClone(queryParams)
    cp[field] = value
    this.setState({ queryParams: cp })
  }

  updateReviewParams = (field, value) => {
    const { reviewParams } = this.state
    let cp = deepClone(reviewParams)
    cp[field] = value
    this.setState({ reviewParams: cp })
  }

  updateReviewList = (id, newGrade) => {
    const { reviewList } = this.state
    let newList = reviewList.map(item => {
      if (item.id === id) {
        item.manualGrade = newGrade
      }
      return item
    })
    this.setState({ reviewList: newList })
  }

  // TODO: 此功能已下线
  beforeAddGuildInfo = () => {
    if (this.addGuildRef) {
      this.addGuildRef.setFieldsValue(defaultGuildInfo)
    }
    this.setState({ addSuperGuildVisible: true })
  }

  beforeUpdateGuildInfo = (r) => { // TODO: 可以去掉
    if (this.updateGuildRef) {
      this.updateGuildRef.setFieldsValue(r)
    }
    this.setState({ updateSuperGuildVisible: true })
  }

  beforeLoadSidDetail = (r) => {
    this.callModel('getSuperCrystalDetail', {
      params: { sid: r.sid },
      isDetailMode: true,
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg, data } = ret
        if (status !== 0) {
          message.warn('查询失败: ' + msg)
          return
        }
        this.setState({ sidDetailVisible: true, sidDetailInfo: data })
      }
    })
  }

  // ================== other ===================

  // 判断当前是否可进行复核
  getIsTimeToReview = () => {
    const { timetable } = this.props.model
    if (!timetable) {
      return '获取考核周期配置失败'
    }
    if (timetable.currentStep !== 2) {
      return '当前非考核阶段'
    }
    if (timetable.reviewStatus === 'OnGoing') {
      return '复核审批流程待审批'
    }
    if (timetable.reviewStatus === 'Passed') {
      return '复核审批流程已通过'
    }
    return ''
  }

  getGradeOptions = () => {
    const { timetable } = this.props.model
    const guildGradeList = timetable?.guildGradeList || []
    let options = [{ label: '所有', value: -1 }, { label: '无评级', value: 0 }]
    guildGradeList.forEach(item => {
      options.push({ label: item.gradeName, value: item.grade })
    })
    return options
  }

  getGradeOptionsV2 = () => {
    const { timetable } = this.props.model
    const guildGradeList = timetable?.guildGradeList || []
    let options = [{ label: '-', value: 0, disabled: true }]
    guildGradeList.forEach(item => {
      options.push({ label: item.gradeName, value: item.grade })
    })
    return options
  }

  gradeFormater = (v) => {
    const options = this.getGradeOptions()
    return options.find(item => { return item.value === v })?.label
  }

  manualGradeForamter = (systemGrade, manualGrade) => {
    let desc = this.gradeFormater(manualGrade)
    if (systemGrade === manualGrade) {
      return desc
    }
    return `<p style="color: red">${desc}</p>`
  }

  IsNotCheck = (notCheckPeriod) => {
    const match = notCheckPeriod.match(/^(\d{6})~(\d{6})$/)
    const { timetable } = this.props.model
    if (match && timetable && timetable.assessMonth) {
      const { assessMonth } = timetable
      if (assessMonth >= match[1] && assessMonth <= match[2]) {
        return true
      }
    }
    return false
  }

  tagFormater = (tag, notCheckPeriod) => {
    if (!tag) {
      return '-'
    }
    let isNotChecked = this.IsNotCheck(notCheckPeriod)
    return <div style={{ width: '5em', color: isNotChecked ? 'red' : '' }}>
      <Row>
        <Col span={24}>{tag}</Col>
        <Col span={24} style={{ fontSize: 'smaller' }}>({notCheckPeriod})</Col>
      </Row>
    </div>
  }

  filterReviewList = (before, params) => {
    let after = []
    before.forEach(item => {
      if (params.querySid > 0 && item.sid !== params.querySid) {
        return
      }
      if (params.queryAsid > 0 && item.asid !== params.queryAsid) {
        return
      }
      if (params.queryOwYY > 0 && item.owimid !== params.queryOwYY) {
        return
      }
      if (params.queryGrade > 0 && item.systemGrade !== params.queryGrade) {
        return
      }
      after.push(item)
    })
    return after
  }

  filterSuperGuildList = (before, params) => {
    let after = []
    before.forEach(item => {
      if (params.querySid > 0 && item.sid !== params.querySid) {
        return
      }
      if (params.queryAsid > 0 && item.asid !== params.queryAsid) {
        return
      }
      if (params.queryOwYY > 0 && item.owYy !== params.queryOwYY) {
        return
      }
      if (params.queryGrade >= 0 && item.grade !== params.queryGrade) {
        return
      }
      after.push(item)
    })
    return after
  }

  parseAprItem = (before) => {
    let isNotCheck = this.IsNotCheck(before.notCheckPeriod)
    let after = deepClone(before)
    after.giftFlow = classValueFormater(before.giftFlow)
    after.systemGrade = this.gradeFormater(before.systemGrade)
    after.manualGrade = this.manualGradeForamter(before.systemGrade, before.manualGrade)
    if (isNotCheck) {
      after.manualGrade = '-'
      after.systemGrade = '-'
    }
    return after
  }

  render () { 
    const { queryParams, reviewParams, editingItem, reviewList, reviewHistoryList, queryHistoryItem, superGuildList, sidToAccountName } = this.state
    const { updateGradeVisible, updateTagVisible, updateReviewVisible, reviewHistoryVisible, addSuperGuildVisible, updateSuperGuildVisible } = this.state
    const { removeReason, removeList, removeVisible } = this.state
    const { sidDetailInfo, sidDetailVisible } = this.state
    const { timetable } = this.props.model
    const canReview = (timetable?.currentStep === 2 && timetable?.reviewStatus !== 'Passed')

    const columns = [
      { dataIndex: 'asid', title: '短位ID' },
      { dataIndex: 'sid', title: '长位ID' },
      { dataIndex: 'company_name', title: '公司名称' },
      { dataIndex: 'crop', title: '法人' },
      { dataIndex: 'owYy', title: 'OW YY号', render: (v, r) => { return <Tooltip title={`uid=${r.ow_uid}`}>{v}</Tooltip> } },
      { dataIndex: 'ow_phone_number', title: 'OW 手机号' },
      { dataIndex: 'signed_time', title: '签约时间' },
      { dataIndex: 'contract_deadline', title: '合约期限' },
      { dataIndex: 'grade', title: '公会当前评级', render: (v) => { return this.gradeFormater(v) } },
      { dataIndex: 'tag', title: '公会当前标签', render: (v, r) => { return this.tagFormater(v, r.notCheckPeriod) } },
      { dataIndex: 'sid', title: '对公账户名', render: (v) => { return sidToAccountName[v] || '-' } },
      { dataIndex: '_',
        title: '操作',
        render: (_, r) => {
          return <Space>
            {/* <Link onClick={() => { this.beforeUpdateGuildInfo(r) }}>编辑</Link> */}
            <Link onClick={() => { this.beforeLoadSidDetail(r) }}>公会详情信息</Link>
            <Link disabled={canReview} onClick={() => { if (canReview) return; this.setState({ updateGradeVisible: true, editingItem: r }) }} >修改评级</Link>
            <Link disabled={canReview} onClick={() => { if (canReview) return; this.setState({ updateTagVisible: true, editingItem: r }) }}>修改标签</Link>
            <Link onClick={() => { this.queryReviewHistory(r.sid, r) }}>查看评级记录</Link>
            <Link type='danger' onClick={() => this.setState({ removeList: [r.sid], removeVisible: true })}>移除</Link>
          </Space>
        } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    const reviewColumn = [
      { dataIndex: 'month', title: '考核周期' },
      { dataIndex: 'asid', title: '短位ID' },
      { dataIndex: 'sid', title: '长位ID' },
      { dataIndex: 'owUid', title: 'OW uid' },
      { dataIndex: 'owimid', title: 'OW YY' },
      { dataIndex: 'giftFlow', title: '公会月礼物流水', render: (v, r) => { return classValueFormater(v) } },
      { dataIndex: 'packRate', title: '包裹礼物占比' },
      { dataIndex: 'validTing', title: '月有效经营厅数' },
      { dataIndex: 'systemGrade', title: '系统考核评级', render: (v, r) => { return this.IsNotCheck(r.notCheckPeriod) ? '-' : this.gradeFormater(v) } },
      { dataIndex: 'manualGrade',
        title: '人工修改评级',
        render: (v, r) => {
          let notCheck = this.IsNotCheck(r.notCheckPeriod)
          if (notCheck) {
            return '-'
          }
          let isDiff = r?.systemGrade !== v
          return <Select value={v} options={this.getGradeOptionsV2()} style={{ width: '5em', color: isDiff ? 'red' : '', fontWeight: isDiff ? 'bold' : '' }} onChange={(v) => { this.updateReviewList(r.id, v) }} />
        } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    const hisotoryColumn = [
      { dataIndex: 'month', title: '考核月份' },
      { dataIndex: 'grade', title: '评级等级', render: (v) => { return this.gradeFormater(v) } },
      { dataIndex: 'giftFlow', title: '月公会礼物流水(元)', render: (v, r) => { return r.method === 3 ? '-' : classValueFormater(v) } },
      { dataIndex: 'packRate', title: '包裹礼物流水占比', render: (v, r) => { return r.method === 3 ? '-' : v } },
      { dataIndex: 'validTing', title: '月有效经营厅数', render: (v, r) => { return r.method === 3 ? '-' : v } },
      { dataIndex: 'method', title: '评级类型', render: (v) => { return methodFormater(v) } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <div>
        <Row>
          <Col span={16}>
            <Space>
              <div>
                ASID: <InputNumber style={{ width: '10em' }} value={queryParams.queryAsid} onChange={v => { this.updateQueryParams('queryAsid', v) }} />
              </div>
              <div>
                SID: <InputNumber style={{ width: '10em' }} value={queryParams.querySid} onChange={v => { this.updateQueryParams('querySid', v) }} />
              </div>
              <div>
                OWYY号: <InputNumber style={{ width: '10em' }} value={queryParams.queryOwYY} onChange={v => { this.updateQueryParams('queryOwYY', v) }} />
              </div>
              <div>
                公会评级: <Select style={{ width: '6em' }} options={this.getGradeOptions()} value={queryParams.queryGrade} onChange={v => { this.updateQueryParams('queryGrade', v) }} />
              </div>
              <Button type='primary' onClick={() => this.querySuperGuildInfo()}>查询</Button> 
            </Space>
          </Col>
          <Col span={8}>
            <Space style={{ float: 'right' }}>
              <Button type='primary' disabled={!canReview} onClick={() => this.queryReviewInfo()}>复核</Button>
              {/* <Button onClick={() => this.beforeAddGuildInfo()}>添加</Button> */}
              <Button disabled={removeList.length === 0} onClick={() => { this.setState({ removeVisible: true }) }} >批量移除</Button>
            </Space>
          </Col>
          <Divider />
          <Col span={24}>
            <Table columns={columns} dataSource={this.filterSuperGuildList(superGuildList, queryParams)} 
              rowSelection={{       
                selectedRowKeys: removeList,
                type: 'checkbox',
                onChange: (idList, recordList) => { this.setState({ removeList: idList }) }  
              }}
              rowKey={r => { return r.sid }}
              pagination={{
                pageSizeOptions: [10, 20, 50, 100],
                showSizeChanger: true
              }} />
          </Col>
        </Row>

        <Modal visible={updateGradeVisible} title='修改公会评级' onCancel={() => { this.setState({ updateGradeVisible: false }) }} okText='确认修改' cancelText='取消' destroyOnClose
          onOk={() => this.onSubmitUpdateGrade(editingItem)}>
          <Form labelCol={{ span: 5 }} >
            <Form.Item label='sid'>
              <Input disabled value={editingItem?.sid} style={{ width: '10em' }} />
            </Form.Item>
            <Form.Item label='公司名'>
              <Input disabled value={editingItem?.company_name} style={{ width: '10em' }} />
            </Form.Item>
            <Form.Item label='公会评级'>
              <Select style={{ width: '8em' }} options={this.getGradeOptionsV2()} value={editingItem?.grade} onChange={(v) => this.updateEditingItem('grade', v)} />
              <Text type='danger' style={{ marginLeft: '1em' }}>修改后评级将在审批通过后次日生效</Text>
            </Form.Item>
          </Form>
        </Modal>

        <Modal visible={updateTagVisible} title='修改公会标签' onCancel={() => { this.setState({ updateTagVisible: false }) }} okText='确认修改' cancelText='取消' destroyOnClose
          onOk={() => this.onSubmitUpdateTag(editingItem)} >
          <Form labelCol={{ span: 5 }} >
            <Form.Item label='sid'>
              <Input disabled value={editingItem?.sid} style={{ width: '10em' }} />
            </Form.Item>
            <Form.Item label='公司名'>
              <Input disabled value={editingItem?.company_name} style={{ width: '10em' }} />
            </Form.Item>
            <Form.Item label='公会标签'>
              <Select style={{ width: '8em' }} options={tagOptions} value={editingItem?.tag} onChange={(v) => this.updateEditingItem('tag', v)} />
            </Form.Item>
            <Form.Item label='不考核周期'>
              <MonthRangeStr disabled={!editingItem?.tag} value={editingItem?.notCheckPeriod} onChange={v => { this.updateEditingItem('notCheckPeriod', v) }} />
              <br />
              <Text type='secondary'>例如选择1~3月，4月1日不考核3月数据，自4月开始考核数据</Text>
            </Form.Item>
          </Form>
        </Modal>

        <Modal visible={updateReviewVisible} title='公会评级复核' width='85vw' destroyOnClose
          onCancel={() => { this.setState({ updateReviewVisible: false }) }}
          okText='提交审核' cancelText='取消'
          onOk={() => this.onSubmitReview(reviewList)} >
          <Row>
            <Col span={24}>
              <Space>
                <div>
                  AID: <InputNumber style={{ width: '10em' }} value={reviewParams.queryAsid} onChange={v => { this.updateReviewParams('queryAsid', v) }} />
                </div>
                <div>
                  SID: <InputNumber style={{ width: '10em' }} value={reviewParams.querySid} onChange={v => { this.updateReviewParams('querySid', v) }} />
                </div>
                <div>
                  OWYY号: <InputNumber style={{ width: '10em' }} value={reviewParams.queryOwYY} onChange={v => { this.updateReviewParams('queryOwYY', v) }} />
                </div>
                <div>
                  系统评级: <Select style={{ width: '6em' }} options={this.getGradeOptions()} value={reviewParams.queryGrade} onChange={v => { this.updateReviewParams('queryGrade', v) }} />
                </div>
              </Space>
            </Col>
            <Col span={24}>
              <ReviewInfo data={reviewList} guildGradeList={timetable?.guildGradeList || []} />
            </Col>
            <Col span={24}>
              <Table columns={reviewColumn} dataSource={this.filterReviewList(reviewList, reviewParams)} />
            </Col>
          </Row>
        </Modal>

        <Modal visible={reviewHistoryVisible} title='公会评级历史记录' width='70em' destroyOnClose onCancel={() => { this.setState({ reviewHistoryVisible: false }) }} footer={false} >
          <Row>
            <Col span={24}>
              公会SID: {queryHistoryItem.sid}
            </Col>
            <Col span={24}>
              公司名: {queryHistoryItem.company_name}
            </Col>
            <Col span={24}>
              <Table columns={hisotoryColumn} dataSource={reviewHistoryList} />
            </Col>
          </Row>
        </Modal>

        {/* TODO: 此功能已下线  */}
        <Modal visible={addSuperGuildVisible} title='添加超级水晶公会信息' onCancel={() => { this.setState({ addSuperGuildVisible: false }) }} okText='添加' cancelText='取消'
          onOk={() => { this.addGuildRef.submit() }} forceRender >
          <Form labelCol={{ span: 5 }} ref={r => { this.addGuildRef = r }} onFinish={(v) => this.onAddSuperGuildInfo(v)} >
            <Form.Item label='SID' name='sid' rules={[{ required: true }]} >
              <InputNumber style={{ width: '15em' }} />
            </Form.Item>
            <Form.Item label='公司名' name='company_name' rules={[{ required: true }]}>
              <Input style={{ width: '15em' }} />
            </Form.Item>
            <Form.Item label='法人' name='crop' rules={[{ required: true }]}>
              <Input style={{ width: '15em' }} />
            </Form.Item>
            <Form.Item label='OW uid' name='ow_uid' rules={[{ required: true }]}>
              <InputNumber style={{ width: '15em' }} />
            </Form.Item>
            <Form.Item label='OW手机号码' name='ow_phone_number' rules={[{ required: true }]}>
              <Input style={{ width: '15em' }} />
            </Form.Item>
            <Form.Item label='签约时间' name='signed_time' rules={[{ required: true }]}>
              <Input style={{ width: '15em' }} placeholder='月-日-年' />
            </Form.Item>
            <Form.Item label='合约期限' name='contract_deadline' rules={[{ required: true }]}>
              <InputNumber style={{ width: '15em' }} />
            </Form.Item>
            <Form.Item label='对公账号' name='corporate_account' rules={[{ required: true }]}>
              <Input style={{ width: '15em' }} />
            </Form.Item>
          </Form>
        </Modal>
 
        <Modal width={800} visible={sidDetailVisible} title='公会详情信息' onCancel={() => { this.setState({ sidDetailVisible: false }) }} footer={null} forceRender >
          <SidDetailInfo record={sidDetailInfo} /> 
        </Modal>

        {/* TODO: 可以铲掉 */}
        <Modal visible={updateSuperGuildVisible} title='更新超级水晶公会信息' onCancel={() => { this.setState({ updateSuperGuildVisible: false }) }} okText='确认修改' cancelText='取消'
          onOk={() => { this.updateGuildRef.submit() }} forceRender >
          <Form labelCol={{ span: 5 }} ref={r => { this.updateGuildRef = r }} onFinish={(v) => this.onUpdateSuperGuildInfo(v)} >
            <Form.Item label='SID' name='sid' rules={[{ required: true }]} >
              <InputNumber style={{ width: '15em' }} />
            </Form.Item>
            <Form.Item label='公司名' name='company_name' rules={[{ required: true }]}>
              <Input style={{ width: '15em' }} />
            </Form.Item>
            <Form.Item label='法人' name='crop' rules={[{ required: true }]}>
              <Input style={{ width: '15em' }} />
            </Form.Item>
            <Form.Item label='OW uid' name='ow_uid' rules={[{ required: true }]}>
              <InputNumber style={{ width: '15em' }} />
            </Form.Item>
            <Form.Item label='OW手机号码' name='ow_phone_number' rules={[{ required: true }]}>
              <Input style={{ width: '15em' }} />
            </Form.Item>
            <Form.Item label='签约时间' name='signed_time' rules={[{ required: true }]}>
              <Input style={{ width: '15em' }} placeholder='月-日-年' />
            </Form.Item>
            <Form.Item label='合约期限' name='contract_deadline' rules={[{ required: true }]}>
              <InputNumber style={{ width: '15em' }} />
            </Form.Item>
            <Form.Item label='对公账号' name='corporate_account' rules={[{ required: true }]}>
              <Input style={{ width: '15em' }} />
            </Form.Item>
          </Form>
        </Modal>

        <Modal title='移除确认' visible={removeVisible} onCancel={() => this.setState({ removeVisible: false })} footer={false}>
          <Row>
            <Col span={24} style={{ marginBottom: '1em' }}>
              <Text>审批流通过后移除该公会的超级水晶公会身份，降为普通公会。<br />移除SID列表（共{removeList.length}个）: {removeList.join(', ')}</Text>
            </Col> 
            <Col span={24} style={{ marginBottom: '1em' }}>
              <Input placeholder='请填写移除原因(必填)' style={{ width: '100%' }} value={removeReason} onChange={(e) => { this.setState({ removeReason: e.target.value }) }} />
            </Col>
            <Col span={24}>
              <Button onClick={() => this.onBatchRemoveSuperGuildInfo(removeList, removeReason)}>提交审批</Button>
            </Col>
          </Row>
          
        </Modal>
      </div>
    )
  }
}

export default GuildGradeAssess
