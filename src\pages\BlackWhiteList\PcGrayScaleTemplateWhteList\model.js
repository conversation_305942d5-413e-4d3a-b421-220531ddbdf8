/* eslint-disable eqeqeq */
import { getLists, deleteWhiteList, addOrModifyWhiteList, getVersionListByUrl } from './api'
import { checkSid, checkPlay, checkSids, countVersionValue } from '@/utils/common'
import { Modal, message } from 'antd'

// 根据玩法和灰度过滤表格数据
function dataFilter (data, playId, grey) {
  if (data == undefined || playId == undefined || grey == undefined) {
    message.error('发生错误，请查看控制台')
    console.error('dataFilter() unexpect params: data=', data, 'play=', playId, 'grey=', grey, 'grey')
    return data
  }
  let result = []
  // 先按玩法过滤，再按灰度过滤
  if (playId >= 0) {
    result = data.filter((v) => { return v.play == playId })
  } else {
    result = data
  }
  if (grey >= 0) {
    if (grey == 0) { // 全量
      result = result.filter((v) => { return v.sid == 0 })
    } else { // 灰度
      result = result.filter((v) => { return v.sid != 0 })
    }
  }
  return result
}

export default {
  namespace: 'pcGrayScaleTemplateWhteList',
  state: {
    // 组件状态
    modifyModelVisibla: false,
    updating: false,
    // 表格参数
    allData: [],
    displayData: [],
    displayVersionList: [], // 下拉框显示的可选版本列表
    playVersionUrlList: [], // []{玩法，玩法编号，获取版本列表url}
    playVersionMap: new Map(), // 根据玩法编号获取版本列表
    currentPage: 1,
    currentSize: 0,
    currentPlay: -1, // 当前筛选的玩法，-1表示全部
    currentGrey: -1, // 当前筛选的灰度, -1全部，1全量，2灰度
    // selectedRowKeys: [], // 选中的列表
    // 修改或添加白名单
    newPlay: '',
    newVersion: '',
    newSids: '',
    remark: '',
    // 表格选中
    selectAsid: '',
    selectSid: '',
    selectPlay: '',
    selectVersion: '',
    // 版本修改
    updateVersion: '',
    updateRemark: ''
  },
  reducers: {
    // 更新单个state成员的值
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },
    // 更新data到灰度白名单数据列表(更新allData 和 displayData)
    displayList (state, { payload: data }) {
      if (!Array.isArray(data)) {
        console.error('unexpect table data type: data=', data)
        return
      }
      const { currentPlay, currentGrey } = state
      // 筛选玩法
      return ({
        ...state,
        allData: data,
        displayData: dataFilter(data, currentPlay, currentGrey)
      })
    },
    // 更新data到playVersionUrlList
    updataPlayVersionUrlList (state, { payload: data }) {
      if (!Array.isArray(data)) {
        console.error('unexpect table data type: data=', data)
        return
      }
      return {
        ...state,
        playVersionUrlList: data
      }
    },
    // 更新currentPlay并刷新需要显示的数据
    filterDataByPlayId (state, { payload: playId }) {
      const { allData, currentGrey, currentPlay } = state
      if (allData == []) {
        message.warn('数据为空')
        return { ...state }
      }
      if (playId == currentPlay) { // 无变化
        return { ...state }
      }
      return {
        ...state,
        displayData: dataFilter(allData, playId, currentGrey),
        currentPlay: playId
      }
    },
    // 更新currentGrey并刷新需要显示的数据
    filterDataByGrey (state, { payload: grey }) {
      const { allData, currentGrey, currentPlay } = state
      if (allData == []) {
        message.warn('数据为空')
        return { ...state }
      }
      if (grey == currentGrey) { // 无变化
        return { ...state }
      }
      return {
        ...state,
        displayData: dataFilter(allData, currentPlay, grey),
        currentGrey: grey
      }
    },
    updatePageAndSize (state, { payload: data }) {
      const { page, size } = data
      if (page === undefined || size == undefined) {
        console.error('unexpect params page or size:', page, size)
        return
      }
      return {
        ...state,
        currentSize: size,
        currentPage: page
      }
    },
    updateModifyVisible (state, { payload: visible }) {
      if (visible !== true && visible !== false) {
        console.error('unexpect params in onChangeModifyState(): visible=', visible)
        return
      }
      return {
        ...state,
        modifyModelVisibla: visible
      }
    },
    updateSelectData (state, { payload: data }) {
      const { asid, sid, play, version, remark } = data
      return {
        ...state,
        selectAsid: asid,
        selectSid: sid,
        selectPlay: play,
        selectVersion: version,
        updateRemark: remark
      }
    },
    // 设置‘添加标签页-添加’按钮的状态，true为正在处理中
    setBtnStatus (state, { payload: status }) {
      if (status !== true && status !== false) {
        console.error('unexpect argument in setBtnStatus: status=', status)
        Modal.error('发生错误，请查看控制台')
        return
      }
      return {
        ...state,
        updating: status
      }
    }
  },
  effects: {
    // 请求并刷新灰度白名单列表数据
    * getWhiteListData ({ params }, { select, call, put }) {
      let resp = yield call(getLists)
      const { data } = resp
      if (data == undefined) {
        Modal.error({ content: '获取灰度白名单数据失败，请检查控制台' })
        console.error('getWhiteListData() get data error: response=', resp)
        return
      }
      const { status, msg, payLoad } = data
      if (status != 0) {
        Modal.error({ content: '获取灰度白名单数据有误，请检查控制台' })
        console.error('getWhiteListData() status=' + status + ' msg=' + msg)
        return
      }
      if (payLoad === null) {
        message.warning('数据为空')
        yield put({
          type: 'displayList',
          payload: []
        })
        return
      }
      if (!Array.isArray(payLoad)) {
        Modal.error({ content: '灰度白名单数据异常，请检查控制台' })
        console.error('getWhiteListData() error: payload is not a array, payload=', payLoad)
        return
      }
      let sidList = payLoad
      sidList.sort(function (a, b) { return a.asid > b.asid ? 1 : -1 })
      for (let i = 0; i < sidList.length; i++) {
        sidList[i].key = i + 1
      }
      yield put({
        type: 'displayList',
        payload: sidList
      })
    },
    // 选中玩法后更新对应的displayVersionList
    // 若当前操作为修改，则通过func将版本号设置为正在使用的版本号，若操作为新增，则显示最新的版本号
    * updateVersionByPlay ({ payload }, { select, call, put }) {
      const { play, func, spec } = payload
      yield put({
        type: 'updateState',
        payload: { name: 'displayVersionList', newValue: [] }
      })
      if (!checkPlay(play)) {
        return
      }
      const { pcGrayScaleTemplateWhteList } = yield select(state => state)
      const { playVersionMap } = pcGrayScaleTemplateWhteList // 玩法对应的版本信息缓存
      if (playVersionMap === undefined) {
        message.error('发生错误，请检查控制台')
        console.error('unexpect playVersionMap,', playVersionMap)
        return
      }
      let versionList = playVersionMap.get(play)
      if (versionList === undefined || versionList === null) { // 获取数据并缓存到 playVersionMap
        const { playVersionUrlList } = pcGrayScaleTemplateWhteList
        if (playVersionUrlList === null || playVersionUrlList === undefined) {
          message.error('发生错误，请检查控制台')
          console.error('unexpect playVersionUrlList,', playVersionUrlList)
          return
        }
        let targetUrl = ''
        for (let i = 0; i < playVersionUrlList.length; i++) {
          if (playVersionUrlList[i].play == play) {
            targetUrl = playVersionUrlList[i].url
          }
        }
        if (targetUrl === '') {
          message.error('发生错误，请检查控制台')
          console.error('find target url fail: play=', play, 'playVersionUrlList=', playVersionUrlList)
          return
        }
        let resp = yield call(getVersionListByUrl, targetUrl)
        const { data } = resp
        if (!data || !data.payLoad) {
          console.error('获取版本信息失败，play=', play, ' url=', targetUrl, 'response=', resp)
          message.error('发生错误，请检查控制台')
          return
        }
        const { payLoad } = data
        payLoad.sort(function (a, b) { return countVersionValue(a) < countVersionValue(b) ? 1 : -1 })
        playVersionMap.set(play, payLoad)
        versionList = payLoad
      }
      // 设置下拉框显示的值
      let defaultVersion = -99
      if (spec != undefined && spec !== '') { // 使用指定的版本
        let isSafe = false
        for (let i = 0; i < versionList.length; i++) { // 检查指定的版本是否存在于可选列表
          if (versionList[i] === spec) {
            isSafe = true
            break
          }
        }
        if (!isSafe) {
          message.error('发生错误，请检查控制台')
          console.error('当前使用的版本不存在于版本列表： version: ', spec, ' list:', versionList)
        } else {
          defaultVersion = spec
        }
      } else if (versionList.length > 0) { // 使用最新的版本
        defaultVersion = versionList[0]
      }
      func(defaultVersion)
      yield put({
        type: 'updateState',
        payload: { name: 'displayVersionList', newValue: versionList }
      })
    },
    // 单个或批量修改白名单数据
    * singleModify ({ payload }, { select, call, put }) {
      const { play, version, sids } = payload
      let { remark } = payload
      if (!checkPlay(play) || !checkSids(sids)) {
        return
      }
      if (remark == '' || remark == undefined) {
        remark = 'none'
      }
      // 再次检查version是否在play对应的版本列表里面
      const { pcGrayScaleTemplateWhteList } = yield select(state => state)
      const { playVersionMap } = pcGrayScaleTemplateWhteList
      const tmpVersionList = playVersionMap.get(play)
      if (tmpVersionList == undefined) {
        message.error('发送错误，请检查控制台')
        console.error('unexpect version, the version list of ', play, 'is empty')
        return
      }
      let isSafe = false
      for (let i = 0; i < tmpVersionList.length; i++) {
        if (tmpVersionList[i] == version) {
          isSafe = true
          break
        }
      }
      if (!isSafe) {
        message.error('发送错误，请检查控制台')
        console.error('Error: version=', version, ' list=', tmpVersionList, ' version not found in list!')
        return
      }

      yield put({
        type: 'setBtnStatus',
        payload: true
      })
      let resp = yield call(addOrModifyWhiteList, play, version, sids, remark)
      const { data } = resp
      if (data == undefined) {
        Modal.error({ content: '发生错误，请检查控制台' })
        console.error('[添加PC模板玩法灰度白名单错误] response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status, msg } = data
      if (status === 0) {
        message.success('修改成功')
      } else {
        Modal.error({ content: '操作失败： status=' + status + ' msg=' + msg })
        console.error('[添加PC模板玩法灰度白名单失败] response=', resp)
      }
      yield put({
        type: 'setBtnStatus',
        payload: false
      })
      yield put({
        type: 'updateModifyVisible',
        payload: false
      })
      yield put({ // 更新列表
        type: 'getWhiteListData'
      })
    },
    // 单个删除灰度白名单
    * delWhiteList ({ payload }, { call, put }) {
      const { sid, play, version } = payload
      if (!checkSid(sid) || !checkPlay(play)) {
        return
      }
      let resp = yield call(deleteWhiteList, play, version, sid)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除PC模板玩法灰度白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status, msg } = data
      if (status === 0) {
        message.success('删除成功')
        yield put({ // 更新列表
          type: 'getWhiteListData'
        })
      } else {
        Modal.error({ content: '删除失败：status=' + status + ' msg=' + msg })
        console.error('delWhiteList() resp=', resp)
      }
    },
    // 批量删除白名单
    // 批量删除功能需要分多次完成，每次请求删除特定的play-version-sids
    * bashDelWhiteList ({ payload }, { call, put }) {
      const { bashDelList, callBackFunc } = payload
      if (!Array.isArray(bashDelList)) {
        console.error('unexpect bashDelList: ', bashDelList)
        return
      }
      let okTimes = 0
      for (let i = 0; i < bashDelList.length; i++) {
        let { play, version, sids } = bashDelList[i]
        if (!checkPlay(play) || !checkSids(sids)) {
          return
        }
        let resp = yield call(deleteWhiteList, play, version, sids)
        const { data } = resp
        if (data == undefined) {
          console.error('[批量删除白名单错误]: target=', bashDelList[i], ' response=', resp)
          break
        }
        const { status, msg } = data
        if (status !== 0) {
          console.error('[批量删除白名单错误]: target=', bashDelList[i], ' status=', resp, ' msg=', msg)
          break
        }
        console.info('[bashDelete OK] target=', bashDelList[i])
        okTimes++
      }
      if (okTimes !== bashDelList.length) { // 全部或部分删除失败
        Modal.warn({ content: '发生错误, 请检查控制台' })
      } else {
        Modal.info({ content: '删除成功' })
      }
      yield put({ // 更新列表
        type: 'getWhiteListData'
      })
      if (typeof callBackFunc !== 'function') {
        message.error('unexpect callBackFunc(), type=' + typeof callBackFunc)
      } else {
        callBackFunc()
      }
    }
  }
}
