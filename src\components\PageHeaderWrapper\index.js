import React, { useState } from 'react'
import Link from 'dva/router'
import PageHeader from '@/components/PageHeader'
import { connect } from 'dva'
import GridContent from './GridContent'
import styles from './index.module.less'
import MenuContext from '@/layouts/MenuContext'
import { ConfigProvider, message, Modal, Popconfirm, Table, Button, Row, Col, Typography } from 'antd'
import zhCN from 'antd/lib/locale/zh_CN'
import { LockTwoTone } from '@ant-design/icons'
import { getCookie, timeFormater } from '@/utils/common'
import request from '@/utils/request'
import initReloadReminder from '@/utils/reloadReminder.js'
import moment from 'moment'
const { Text, Title } = Typography

const PageHeaderWrapper = ({ children, contentWidth, wrapperClassName, top, ...restProps }) => {
  const [visible, setVisible] = useState(false)
  const [crashTipVisible, setCrashTipVisible] = useState(getCookie('crashURL') === window.location.toString())
  const [updateInfo, setUpdateInfo] = useState(null)
  const [selectedRowKeys, setSelectedRowKeys] = useState([])
  const [selectRecords, setSelectRecords] = useState([])
  const [dataSource, setDataSource] = useState([])
  const { title } = restProps
  window.currentHeaderTitle = title

  updateLocalStorage()

  initReloadReminder((info) => setUpdateInfo(info))

  const columns = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '通行证', dataIndex: 'passport', align: 'center' },
    { title: '昵称', dataIndex: 'nick', align: 'center', width: '6em', render: (v) => { return <Text>{v}</Text> } },
    { title: 'URI', dataIndex: 'uri', align: 'center', render: (v) => { return <Text>{v}</Text> } },
    { title: '操作', align: 'center', width: '4em', fixed: 'right', render: item => <Popconfirm title='确认回收单个权限么?' okText='确认' cancelText='取消' onConfirm={() => dropAuth(item)}><a>回收权限</a></Popconfirm> }
  ]

  const showModal = item => () => {
    // console.log(item)
    request(`/admin_menu/single_page_auth?uri=${item.uri}`).then(resp => {
      // console.log(resp)
      try {
        const { data: { status, msg, list } } = resp
        if (status !== 0) {
          message.warning(msg)
          return
        }

        setVisible(true)
        setDataSource(list?.map((item, index) => { item.index = index + 1; return item }))
      } catch (err) {
        message.warning(err)
      }
    })
  }

  const dropAuth = (item) => {
    if (!Array.isArray(item)) {
      item = [item]
    }
    request(`/admin_menu/drop_auth`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
      },
      body: JSON.stringify(item)
    }).then(resp => {
      const { data: { status, msg } } = resp
      if (status !== 0) {
        message.warning(msg)
        return
      }
      message.success('提交成功，请等待审批')
    })
  }

  const rowSelection = {
    selectedRowKeys,
    type: 'checkbox',
    onChange: (idList, recordList) => { setSelectedRowKeys(idList); setSelectRecords(recordList) }
  }

  return (
    <ConfigProvider locale={zhCN}>
      <div style={{ margin: '-24px -24px 0', height: '100%', display: 'flex', flexDirection: 'column' }} className={wrapperClassName}>
        {top}
        <MenuContext.Consumer>
          {value => (
            <PageHeader
              wide={contentWidth === 'Fixed'}
              home={<span>首页</span>}
              {...value}
              key='pageheader'
              {...restProps}
              linkElement={Link}
              itemRender={item => {
                return <span>{item.name}{item.children?.length > 0
                  ? ''
                  : <a onClick={showModal(item)}>
                    <LockTwoTone twoToneColor='#eb2f96' style={{ marginLeft: 10 }} />
                  </a>}</span>
              }}
            />
          )}
        </MenuContext.Consumer>

        {children
          ? <div style={{ flex: '1 1 auto' }} className={styles.content}>
            <GridContent>{children}</GridContent>
          </div>
          : null}

        <Modal title='页面权限' width={800} footer={null} visible={visible} onCancel={() => setVisible(false)}>
          <Row>
            <Col span={24}>
              <Table size='small' rowSelection={rowSelection} pagination={false} rowKey='uid' columns={columns} dataSource={dataSource} />
            </Col>
            <Col span={24}>
              <Button style={{ marginTop: '1em' }} type='primary' disabled={selectRecords.length === 0} onClick={() => { dropAuth(selectRecords) }}>批量回收权限</Button>
            </Col>
          </Row>
        </Modal>

        <Modal title='错误报告' visible={crashTipVisible} onCancel={() => setCrashTipVisible(false)} okButtonProps={{ hidden: true }} cancelText='好的'>
          <Title level={5}>😭 刚才页面似乎崩溃了 😭</Title>
          <Text>已将相关信息上报到问题采集系统，后台同学将尽快修复～</Text><br />
          <Text>如有其他疑问,请联系后台同学: 冼锦荣～</Text>
        </Modal>

        <Modal title='是否刷新页面?' visible={updateInfo !== null}
          onCancel={() => setUpdateInfo(null)}
          onOk={() => { window.location.reload() }} okText='马上刷新' cancelText='暂不刷新'>
          <Title level={5}>🌞 BOSS后台发布新版本啦！ 🌞</Title>
          <Text>发布时间: {timeFormater(updateInfo?.timestamp || 0)}</Text><br />
          <Text>更新人: {updateInfo?.auther}</Text><br />
          <Text>版本信息: {updateInfo?.message}</Text><br />
        </Modal>

      </div>
    </ConfigProvider>
  )
}

export default connect(({ global }) => ({
  contentWidth: global.contentWidth
}))(PageHeaderWrapper)

/* eslint-disable no-undef */
// 将经常访问的页面记录到缓存中
const updateLocalStorage = () => {
  let uri = `${window.location}`
  const favKey = 'myFavUri'
  var index = uri.indexOf('/n')
  if (index === -1) {
    return
  }
  uri = uri.slice(index + 2)

  let favRaw = localStorage.getItem(favKey)
  let timestamp = moment().unix()
  if (favRaw === null) {
    let initFav = {
      updateTime: timestamp,
      favUri: [uri]
    }
    localStorage.setItem(favKey, JSON.stringify(initFav))
    return
  }

  let fav = JSON.parse(favRaw)
  if (!fav || !fav.updateTime || !Array.isArray(fav.favUri)) {
    return
  }
  if (fav.updateTime === timestamp) {
    return
  }
  if (fav.favUri.indexOf(uri) >= 0) {
    return
  }
  let newFav = [uri, ...fav.favUri]
  if (newFav.length > 20) {
    newFav = newFav.slice(0, 20)
  }
  fav.favUri = newFav
  localStorage.setItem(favKey, JSON.stringify(fav))
}
