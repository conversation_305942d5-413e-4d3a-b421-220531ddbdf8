import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Table, InputNumber, Divider, Button, Select, Modal, Input, Popconfirm } from 'antd'
import { SearchOutlined } from '@ant-design/icons'
import { connect } from 'dva'
import moment from 'moment'

const Option = Select.Option
// var echarts = require('echarts')
const namespace = 'redpacketLotteryConfig'

@connect(({ redpacketLotteryConfig }) => ({
  model: redpacketLotteryConfig
}))
class RedpacketLotteryConfig extends Component {
  state = { editable: false, selectedRowKeys: [], selectedProps: null, editRow: null }

  columns = [
    { title: '编号',
      align: 'center',
      dataIndex: 'index',
      width: 70,
      render: (text, record) => text
    },
    { title: '营收ID',
      align: 'center',
      dataIndex: 'propsId',
      width: 100,
      render: (text, record) => <Button onClick={e => this.setState({ visible: true, editRow: record.index, selectedRowKeys: [] })}>{text}</Button>
    },
    { title: '奖品名称', align: 'center', dataIndex: 'propsName' },
    { title: '单价/紫水晶', align: 'center', dataIndex: 'price', render: (text, record) => record.price },
    { title: '数量',
      align: 'center',
      dataIndex: 'count',
      width: 100,
      render: (text, record) => this.state.editable ? <InputNumber style={{ borderColor: text === undefined || text === '' || text < 0 ? 'red' : '' }} min={0} value={text} onChange={this.handleChange(record.index, 'count')} /> : text
    },
    { title: '价值/紫水晶', align: 'center', dataIndex: 'value', render: (text, record) => (record.price * record.count).toLocaleString() },
    { title: '概率',
      align: 'center',
      dataIndex: 'probability',
      width: 100,
      render: (text, record) => this.state.editable ? <InputNumber min={0} value={text} onChange={this.handleChange(record.index, 'probability')} /> : text
    },
    { title: '播报等级',
      align: 'center',
      dataIndex: 'broadcastLevel',
      width: 100,
      render: (text, record) => this.state.editable ? <Select onChange={this.handleChange(record.index, 'broadcastLevel')} value={text}>{['无', 'LV1', 'LV2', 'LV3'].map((item, index) => <Option key={index} value={index}>{item}</Option>)}</Select> : ['无', 'LV1', 'LV2', 'LV3'][text]
    },
    { title: '广播类型',
      align: 'center',
      dataIndex: 'broadcastType',
      width: 120,
      render: (text, record) => this.state.editable ? <Select style={{ width: 80 }} onChange={this.handleChange(record.index, 'broadcastType')} value={text}>{['无', '子频道', '全频道', '全服'].map((item, index) => <Option key={index} value={index}>{item}</Option>)}</Select> : ['无', '子频道', '全频道', '全服'][text]
    },
    { title: '是否展示',
      align: 'center',
      dataIndex: 'isShow',
      width: 120,
      render: (text, record) => this.state.editable ? <Select style={{ width: 80 }} onChange={this.handleChange(record.index, 'isShow')} value={text}>{['否', '是'].map((item, index) => <Option key={index} value={index}>{item}</Option>)}</Select> : ['否', '是'][text]
    },
    { title: '操作',
      align: 'center',
      render: (text, record) => this.state.editable ? (
        <span>
          <Popconfirm title='确认删除?' onConfirm={this.handleDel(record.index)}>
            <a href=''>删除</a>
          </Popconfirm>
        </span>) : ''
    }
  ]

  roles = [
    { title: '编号',
      align: 'center',
      dataIndex: 'index',
      width: 70
    },
    { title: '角色名称', align: 'center', dataIndex: 'roleName' },
    { title: '概率',
      align: 'center',
      dataIndex: 'probability',
      width: 100,
      render: (text, record) => <InputNumber min={0} value={text} onChange={this.handleChangeRole(record.index, 'probability')} />
    }
  ]

  propsColumns = [
    { title: '道具ID',
      align: 'center',
      width: 100,
      dataIndex: 'id',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ width: 300, padding: 8, borderRadius: 6, background: '#FFF', boxShadow: '0 1px 6px' }}>
          <Input style={{ width: 140, marginRight: 10 }} value={selectedKeys[0]} onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={this.handleSearchPropsId(selectedKeys, confirm)} />
          <Button style={{ marginRight: 5 }} type='primary' onClick={this.handleSearchPropsId(selectedKeys, confirm)}>搜索</Button>
          <Button onClick={this.handleResetPropsId(clearFilters)}>重置</Button>
        </div>
      ),
      onFilter: (value, record) => record.id.toString().toLowerCase().includes((value.toLowerCase())),
      filterIcon: filtered => <SearchOutlined theme='outlined' style={{ color: filtered ? '#108ee9' : '#aaa' }} />,
      render: text => {
        text = text.toString()
        const { searchTextPropsId } = this.state
        return searchTextPropsId ? (
          <span>
            {text.split(new RegExp(`(?<=${searchTextPropsId})|(?=${searchTextPropsId})`, 'i')).map((fragment, i) => (
              fragment.toLowerCase() === searchTextPropsId.toLowerCase() ? <span key={i} style={{ color: '#f50' }}>{fragment}</span> : fragment
            ))}</span>) : text
      } },
    { title: '名称',
      align: 'center',
      width: 100,
      dataIndex: 'name',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ width: 300, padding: 8, borderRadius: 6, background: '#FFF', boxShadow: '0 1px 6px' }}>
          <Input style={{ width: 140, marginRight: 10 }} value={selectedKeys[0]} onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={this.handleSearch(selectedKeys, confirm)} />
          <Button style={{ marginRight: 5 }} type='primary' onClick={this.handleSearch(selectedKeys, confirm)}>搜索</Button>
          <Button onClick={this.handleReset(clearFilters)}>重置</Button>
        </div>
      ),
      onFilter: (value, record) => record.name.toLowerCase().includes((value.toLowerCase())),
      filterIcon: filtered => <SearchOutlined theme='outlined' style={{ color: filtered ? '#108ee9' : '#aaa' }} />,
      render: text => {
        const { searchText } = this.state
        return searchText ? (
          <span>
            {text.split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i')).map((fragment, i) => (
              fragment.toLowerCase() === searchText.toLowerCase() ? <span key={i} style={{ color: '#f50' }}>{fragment}</span> : fragment
            ))}</span>) : text
      }
    },
    { title: '单价', align: 'center', width: 100, dataIndex: 'price' },
    { title: '货币类型', align: 'center', width: 100, dataIndex: 'priceType' },
    { title: '道具类型', align: 'center', width: 150, dataIndex: 'type' },
    { title: '生效日期', align: 'center', width: 180, dataIndex: 'startTime', render: text => moment.unix(text / 1000).format('YYYY-MM-DD HH:mm:ss') },
    { title: '失效日期', align: 'center', dataIndex: 'endTime', render: text => moment.unix(text / 1000).format('YYYY-MM-DD HH:mm:ss') }
  ]

  // 删除
  handleDel = key => () => {
    var { model: { list } } = this.props
    console.log('before handleDel', list, key)
    for (var i = 0; i < list.length; i++) {
      if (list[i].index === key) {
        list.splice(i, 1)
        break
      }
    }
    for (i = 0; i < list.length; i++) {
      list[i].index = i
    }

    this.forceUpdate() // 强制刷新
  }

  // 本地搜索
  handleSearch = (selectedKeys, confirm) => () => {
    confirm()
    this.setState({ searchText: selectedKeys[0] })
  }

  // 重置搜索
  handleReset = clearFilters => () => {
    clearFilters()
    this.setState({ searchText: '' })
  }

    // 根据PropsId搜索
    handleSearchPropsId = (selectedKeys, confirm) => () => {
      confirm()
      this.setState({ searchTextPropsId: selectedKeys[0] })
    }

    // 重置 PropsId搜索条件
    handleResetPropsId = clearFilters => () => {
      clearFilters()
      this.setState({ searchTextPropsId: '' })
    }

  handleChange = (index, field) => value => {
    console.log('handleChange', index, value, field)
    var { model: { list } } = this.props
    if (value === undefined) {
      list[index][field] = value
      return
    }

    list[index][field] = value

    this.forceUpdate() // 强制刷新
  }

  handleChangeRole = (index, field) => value => {
    var { model: { roles } } = this.props
    console.log('handleChangeRole', index, value, field, roles)
    if (value === undefined) {
      roles[index][field] = value
      return
    }

    roles[index][field] = value

    this.forceUpdate() // 强制刷新
  }

  // 向服务器提交更新
  handleSave = e => {
    const { dispatch, model: { list } } = this.props
    console.log('handleSave: ', list)
    dispatch({
      type: `${namespace}/update`,
      payload: list
    })
    // console.log('saved: ', list)
    this.setState({ confirmVisible: false, editable: !this.state.editable })
  }

  // 向服务器提交更新
  handleSaveRole = e => {
    const { dispatch, model: { roles } } = this.props
    dispatch({
      type: `${namespace}/updateRole`,
      payload: roles
    })
  }

  handleSelectOnChange = (selectedRowKeys, record) => {
    console.log(selectedRowKeys, record)
    this.setState({ selectedRowKeys: selectedRowKeys, selectedProps: record[0] })
  }

  // 加载营收礼物列表 与 礼物配置列表
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getConfig`
    })
    dispatch({
      type: `${namespace}/getRole`
    })
  }

  // 保存修改按钮
  beforeSave = () => {
    this.setState({ confirmVisible: true })
  }

  toEdit = () => {
    this.setState({ editable: true })
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getPropsConfigList`
    })
  }

  toAddItem = (payload) => {
    var { model: { list } } = this.props

    payload.index = list.length
    list.push(payload)

    this.forceUpdate() // 强制刷新
  }

  render () {
    const { route, model: { list, propsList, roles } } = this.props
    const { visible, selectedRowKeys, confirmVisible } = this.state

    // console.log('render: ', list)
    // eslint-disable-next-line eqeqeq
    var columns = this.columns.map(i => { return i })
    // console.log(columns, group)

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <div>
            <label style={{ marginRight: 20 }}><font color='red'>红包雨抽奖</font>[特殊奖品，请联系后端同学开发]</label>
            {this.state.editable ? <Button style={{ marginRight: 20 }} onClick={this.beforeSave} type='danger'>保存修改</Button> : <Button onClick={this.toEdit} type='primary'>编辑</Button>}
            {this.state.editable ? <Button style={{ marginLeft: 20, marginRight: 20 }} onClick={e => { window.location.reload() }} >放弃修改</Button> : '' }
            {this.state.editable ? <Button style={{ marginLeft: 20, marginRight: 20 }}
              onClick={e => {
                this.toAddItem(
                  { propsId: 0, propsName: '无', price: 0, count: 1, value: 0, probability: 1, broadcastLevel: 0, broadcastType: 0 }
                )
              }} >新增空白奖品</Button> : '' }
          </div>
          <Divider />
          <Table pagination={false} size='small' dataSource={list} columns={columns} rowKey={(record, index) => index} />
        </Card>
        <div>
          <Modal onOk={this.handleSelect} okText='选中' cancelText='取消' okButtonProps={{ disabled: selectedRowKeys.length === 0 }} width={1100} title='选择数据' visible={visible} onCancel={e => this.setState({ visible: false })}>
            <Table bordered rowSelection={{ selectedRowKeys, onChange: this.handleSelectOnChange, type: 'radio' }} pagination={false} scroll={{ y: 340 }} dataSource={propsList} size='small' columns={this.propsColumns} rowKey={(record, index) => index} />
          </Modal>
          <Modal onOk={this.handleSave} title='确认修改？' onCancel={e => this.setState({ confirmVisible: false })} okText='确认修改' cancelText='取消' visible={confirmVisible}>
            <div align='center'>
              <br />
            </div>
          </Modal>
        </div>

        <Card>
          <div>
            <label style={{ marginRight: 20 }}><font color='red'>抽奖角色</font></label>
            <Button style={{ marginRight: 20 }} onClick={this.handleSaveRole} type='danger'>保存修改</Button>
          </div>
          <Divider />
          <Table pagination={false} size='small' dataSource={roles} columns={this.roles} rowKey={(record, index) => index} />
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default RedpacketLotteryConfig
