import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Button, Table, Card, Form, Divider, Input, Popconfirm, Modal } from 'antd'
import dateString from '@/utils/dateString'
import { connect } from 'dva'
import moment from 'moment'

const namespace = 'whitelistConfig'
const FormItem = Form.Item

@connect(({ whitelistConfig }) => ({
  model: whitelistConfig
}))

class WhitelistConfig extends Component {
  // 定义列表结构，
  columns = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: '频道', dataIndex: 'sid', align: 'center' },
    { title: '子频道(0表示全频道)', dataIndex: 'ssid', align: 'center' },
    { title: '添加时间', dataIndex: 'opTime', align: 'center', render: text => dateString(text) },
    { title: '添加人', dataIndex: 'opUsername', align: 'center' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (record) => (
        <span>
          <Popconfirm title='确认删除?' onConfirm={this.handleDel(record.id)}> <Button type='danger'>删除</Button> </Popconfirm>
        </span>),
      export: false
    }
  ]

  paginationProps = { showSizeChanger: true, pageSize: 10, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, sid: '', ssid: '', dataSource: [], searchDone: false }

  // 获取列表
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`
    })
  }

  // show modal
  showModal = (isUpdate, record) => () => {
    if (record == null) record = { uid: '' }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // add
  onFinish = values => {
    const { dispatch } = this.props
    var data = { sid: values.sid, ssid: values.ssid, createTime: moment().unix() }
    dispatch({
      type: `${namespace}/addItem`,
      payload: data
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { id: key }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // search
  handleSearch = e => {
    const { model: { list } } = this.props
    const { sid, ssid } = this.state

    var dataSource = list
    const shourtSid = parseInt(sid || 0, 10)
    if (isNaN(shourtSid)) {
      return
    }
    if (shourtSid > 0) {
      dataSource = dataSource.filter(data => data.sid === shourtSid)
    }

    const shortSsid = parseInt(ssid || 0, 10)
    if (isNaN(shortSsid)) {
      return
    }
    if (shortSsid > 0) {
      dataSource = dataSource.filter(data => data.ssid === shortSsid)
    }

    this.setState({ dataSource: dataSource, searchDone: true })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list } } = this.props
    const { sid, ssid, dataSource, searchDone, visible, isUpdate } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }
    let headers = []
    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.expand === undefined || !col.expand) {
        headers.push(col)
      }

      if (col.export === undefined || col.export) {
        exportHeader.push(col)
      }
    })

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form layout='inline'>
            <Form.Item label='频道' >
              <Input value={sid} onChange={e => this.setState({ sid: e.target.value })} />
            </Form.Item>
            <Form.Item label='子频道' >
              <Input value={ssid} onChange={e => this.setState({ ssid: e.target.value })} />
            </Form.Item>
            <Form.Item>
              <Button type='primary' onClick={this.handleSearch}>搜索</Button>
              <Button type='primary' style={{ marginLeft: 5 }} onClick={this.showModal(false)}>添加</Button>
            </Form.Item>
          </Form>
          <Divider />
          <Form>
            <Table
              dataSource={searchDone ? dataSource : list}
              columns={headers}
              rowKey={(record, index) => index}
              pagination={this.paginationProps}
              size='small'
            />
          </Form>
        </Card>

        <Modal visible={visible} title={'添加白名单'} onCancel={this.hideModal} onOk={this.handleSubmit} forceRender>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <FormItem label='频道' name='sid' rules={[{ required: true, message: 'sid can not be null' }]}>
              <Input placeholder='长位频道id' readOnly={isUpdate} />
            </FormItem>
            <FormItem label='子频道' name='ssid' rules={[{ required: true, message: 'ssid can not be null' }]}>
              <Input placeholder='长位子频道id(0表示全频道)' readOnly={isUpdate} />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default WhitelistConfig
