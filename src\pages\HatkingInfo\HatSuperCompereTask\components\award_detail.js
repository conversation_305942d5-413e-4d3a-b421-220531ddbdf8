import {
  Card,
  Form,
  Table,
  Divider, Button, Input, DatePicker
} from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import exportExcel from '@/utils/exportExcel'
import moment from 'moment'

import { getExportFinishDescDetail, getFinishDescDetail, prizeRuleMap } from './common'

const namespace = 'hatSuperCompereTask'
const getListUri = `${namespace}/listAwardDetail`

@connect(({ awardDetail }) => ({
  model: awardDetail
}))

class AwardDetail extends Component {
  constructor (props) {
    super(props)

    const { dataInfo } = props
    // console.log('constructor dataInfo', dataInfo)
    this.state = { list: dataInfo, searchIMID: 0, searchRewardIMID: 0, searchASID: 0, searchTaskMonth: '' }
  }

  componentDidMount () {
    const { dispatch } = this.props
    const { searchASID, searchTaskMonth } = this.state
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    let data = { asid: searchASID, month: searchMonthTmp }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  onClick = () => {
    const { dispatch } = this.props
    const { searchASID, searchTaskMonth } = this.state
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    let data = { asid: searchASID, month: searchMonthTmp }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  componentWillReceiveProps (nextProps) {
    const { dataInfo } = nextProps
    // console.log('componentWillReceiveProps', dataInfo)
    this.setState({ list: dataInfo })
  }

  state = {
    visible: false,
    isUpdate: false,
    isNotifyValue: true,
    searchInput: '',
    searchKeyword: '',
    channel: { key: -1 },
    gameType: { key: -1 }
  }

  tableInitValue = {}

  columns = [
    { title: '任务时间', width: 40, dataIndex: 'month' },
    { title: '短位ID', width: 50, dataIndex: 'asid' },
    { title: '主持YY号', width: 50, dataIndex: 'imid' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: '发奖YY号', width: 50, dataIndex: 'rewardIMID' },
    { title: '公会礼物流水/元', dataIndex: 'guildAmount', align: 'center' },
    { title: '主持盖章流水/元', width: 50, dataIndex: 'compereSealAmount' },
    {
      title: '基础任务',
      children: [
        {
          title: '任务完成情况',
          dataIndex: 'baseTaskStatus',
          align: 'center',
          render: (text, record) => (getFinishDescDetail(record.baseTask))
        },
        {
          title: '【系统结算】达标奖励/元',
          dataIndex: 'baseTaskReward',
          align: 'center',
          render: (text, record) => (record.baseTask.systemReward)
        }
      ]
    },
    {
      title: '进阶任务',
      children: [
        {
          title: '任务完成情况',
          dataIndex: 'advancedTaskStatus',
          align: 'center',
          render: (text, record) => (getFinishDescDetail(record.advancedTask))
        },
        {
          title: '【系统结算】达标奖励/元',
          dataIndex: 'advancedTaskReward',
          align: 'center',
          render: (text, record) => (record.advancedTask.systemReward)
        }
      ]
    },
    {
      title: '冲刺任务',
      children: [
        {
          title: '任务完成情况',
          dataIndex: 'sprintTaskStatus',
          align: 'center',
          render: (text, record) => (getFinishDescDetail(record.sprintTask))
        },
        {
          title: '【系统结算】达标奖励/元',
          dataIndex: 'sprintTaskReward',
          align: 'center',
          render: (text, record) => (record.sprintTask.systemReward)
        }
      ]
    },
    { title: '【配置】发奖规则', width: 50, dataIndex: 'prizeRule', render: (text, record) => (prizeRuleMap[record.prizeRule]) },
    { title: '【配置】达标奖励/元', width: 50, dataIndex: 'actReward', render: (text, record) => (record.actReward) },
    { title: '合计最终发奖规则', width: 50, dataIndex: 'finalRule', render: (text, record) => (prizeRuleMap[record.finalRule]) },
    { title: '合计最终发奖金额/元', width: 50, dataIndex: 'finalReward', render: (text, record) => (record.finalReward) },
    { title: '备注', width: 50, dataIndex: 'systemRemark', render: (text, record) => (text === '' ? '无' : text) }
  ].map(item => {
    item.align = 'center'
    item.ellipsis = true
    return item
  })

  pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  getFilterList = () => {
    const { list } = this.state
    const { searchIMID, searchASID, searchRewardIMID, searchTaskMonth } = this.state
    let filterList = list
    if (parseInt(searchIMID) > 0) {
      filterList = filterList.filter((v) => { return v.imid === parseInt(searchIMID) })
    }
    if (parseInt(searchASID) > 0) {
      filterList = filterList.filter((v) => { return v.asid === parseInt(searchASID) })
    }
    if (parseInt(searchRewardIMID) > 0) {
      filterList = filterList.filter((v) => { return v.rewardIMID === parseInt(searchRewardIMID) })
    }
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    if (parseInt(searchMonthTmp) > 0) {
      filterList = filterList.filter((v) => { return parseInt(v.month) === parseInt(searchMonthTmp) })
    }
    console.log('getFilterList', searchMonthTmp)
    return filterList
  }

  showModal = (isUpdate, record) => () => {
    if (record == null) {
      record = this.tableInitValue
    }
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(record)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate })
  }

  onExport = () => {
    let list = this.getFilterList()

    let exportData = list.map(item => {
      let v = $.extend(true, {}, item)

      v.baseTaskStatus = getExportFinishDescDetail(v.baseTask)
      v.baseTaskReward = v.baseTask.systemReward
      delete v.baseTask

      v.advancedTaskStatus = getExportFinishDescDetail(v.advancedTask)
      v.advancedTaskReward = v.advancedTask.systemReward
      delete v.advancedTask

      v.sprintTaskStatus = getExportFinishDescDetail(v.sprintTask)
      v.sprintTaskReward = v.sprintTask.systemReward
      delete v.sprintTask

      v.prizeRule = prizeRuleMap[v.prizeRule]
      v.finalRule = prizeRuleMap[v.finalRule]

      return v
    })
    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.export === undefined || col.export) {
        if (col.export === undefined || col.export) {
          if (Array.isArray(col.children)) {
            for (let i = 0; i < col.children.length; i++) {
              exportHeader.push({ key: col.children[i].dataIndex, header: col.title + '-' + col.children[i].title })
            }
          } else {
            exportHeader.push({ key: col.dataIndex, header: col.title })
          }
        }
      }
    })
    console.log('onExport', exportData)
    let fileName = '帽子超主流水激励任务发奖明细-' + moment().format('YYYYMMDD') + '.xlsx'
    exportExcel(exportHeader, exportData, fileName)
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  render () {
    return (
      <Card>
        <Form>
          <Divider type='vertical' />
          任务时间：
          <DatePicker
            format='YYYY-MM'
            picker='month'
            placeholder='任务时间'
            onChange={(v) => this.setState({ searchTaskMonth: v })}
            style={{ width: 100, marginRight: 10 }}
          />
          短位ID：
          <Input style={{ width: 100, marginRight: 10 }} placeholder='请输入' onChange={(e) => { this.setState({ 'searchASID': e.target.value }) }} />
          主持YY号：
          <Input style={{ width: 120, marginRight: 10 }} placeholder='请输入' onChange={(e) => { this.setState({ 'searchIMID': e.target.value }) }} />
          发奖YY号：
          <Input style={{ width: 120, marginRight: 10 }} placeholder='请输入' onChange={(e) => { this.setState({ 'searchRewardIMID': e.target.value }) }} />
          {/* <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button> */}
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onExport}>导出</Button>
          <Divider type='vertical' />
          <Table style={{ marginTop: 10 }} dataSource={this.getFilterList()} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} scroll={{ x: 'max-content' }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default AwardDetail
