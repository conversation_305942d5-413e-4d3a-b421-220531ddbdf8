// eslint-disable-next-line no-unused-vars
import { getHatkingComboInfo, getHatkingCrucialInfo, getHatkingDailyInfo, getHatkingModeStatsInfo, getHatkingUpgradeCrucialInfo } from './api'

export default {
  namespace: 'dragonSlaying',

  state: {
    crucialList: [],
    dailyList: [],
    comboList: [],
    modouList: [],
    modeStatsList: [],
    upgradeCrucialList: []
  },

  reducers: {
    updateCrucialInfoList (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      return {
        ...state,
        crucialList: payload
      }
    },
    updateDailyInfoList (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      return {
        ...state,
        dailyList: payload
      }
    },
    updateComboInfoList (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      return {
        ...state,
        comboList: payload
      }
    },
    updateModeStatsList (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      for (var i = 0; i < payload.length; i++) {
        payload[i].betRebateRatio1 = 0
        payload[i].betRebateRatio2 = 0
        payload[i].betRebateRatio3 = 0
        if (payload[i].betAmethyst1 > 0 && payload[i].betRewardAmethyst1 > 0) {
          payload[i].betRebateRatio1 = payload[i].betRewardAmethyst1 / payload[i].betAmethyst1 * 100
          payload[i].betRebateRatio1 = payload[i].betRebateRatio1.toFixed(2) + '%'
        }
        if (payload[i].betAmethyst2 > 0 && payload[i].betRewardAmethyst2 > 0) {
          payload[i].betRebateRatio2 = payload[i].betRewardAmethyst2 / payload[i].betAmethyst2 * 100
          payload[i].betRebateRatio2 = payload[i].betRebateRatio2.toFixed(2) + '%'
        }
        if (payload[i].betAmethyst3 > 0 && payload[i].betRewardAmethyst3 > 0) {
          payload[i].betRebateRatio3 = payload[i].betRewardAmethyst3 / payload[i].betAmethyst3 * 100
          payload[i].betRebateRatio3 = payload[i].betRebateRatio3.toFixed(2) + '%'
        }
      }
      return {
        ...state,
        modeStatsList: payload
      }
    },
    updateUpgradeCrucialList (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      for (var i = 0; i < payload.length; i++) {
        payload[i].rebateRatio = 0
        if (payload[i].betAmethyst > 0 && payload[i].betRewardAmethyst > 0) {
          payload[i].rebateRatio = payload[i].betRewardAmethyst / payload[i].betAmethyst * 100
          payload[i].rebateRatio = payload[i].rebateRatio.toFixed(2) + '%'
        }
      }
      return {
        ...state,
        upgradeCrucialList: payload
      }
    }
  },

  effects: {
    * getCrucialInfoList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getHatkingCrucialInfo, payload)
      yield put({
        type: 'updateCrucialInfoList',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getDailyInfoList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getHatkingDailyInfo, payload)
      yield put({
        type: 'updateDailyInfoList',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getComboInfoList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getHatkingComboInfo, payload)
      yield put({
        type: 'updateComboInfoList',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getModeStatsList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getHatkingModeStatsInfo, payload)
      yield put({
        type: 'updateModeStatsList',
        payload: Array.isArray(list) ? list : []
      })
    },
    * getUpgradeCrucialList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getHatkingUpgradeCrucialInfo, payload)
      yield put({
        type: 'updateUpgradeCrucialList',
        payload: Array.isArray(list) ? list : []
      })
    }
  }
}
