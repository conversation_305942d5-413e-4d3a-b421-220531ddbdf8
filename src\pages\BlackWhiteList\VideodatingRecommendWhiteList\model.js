/* eslint-disable eqeqeq */
import { getLists, whiteListAdd, whiteListDel } from './api'
import { Modal, message } from 'antd'
import { checkSsid, checkSid } from '@/utils/common'

export default {
  namespace: 'videoRecommendWhiteList',
  state: {
    updating: false,
    displayData: [],
    newSid: '',
    newSsid: ''
  },

  reducers: {
    // 更新data到多人视频推荐白名单数据列表
    displayList (state, { payload: data }) {
      if (!Array.isArray(data)) {
        console.error('unexpect data type: data=', data)
        return
      }
      return {
        ...state,
        displayData: data
      }
    },
    // 设置‘添加标签页-添加’按钮的状态，true为正在处理中
    setBtnStatus (state, { payload: status }) {
      if (status !== true && status !== false) {
        console.error('unexpect argument in setBtnStatus: status=', status)
        Modal.error('发生错误，请查看控制台')
        return
      }
      return {
        ...state,
        updating: status
      }
    }
  },

  effects: {
    // 请求并刷新多人视频推荐白名单列表数据
    * getWhiteListData ({ params }, { select, call, put }) {
      let resp = yield call(getLists)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[ 请求白名单列表数据错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      let { status, videodatingList } = data
      if (videodatingList === null) {
        message.warning('数据为空')
        yield put({
          type: 'displayList',
          payload: []
        })
        return
      }
      if (status !== 0 || !Array.isArray(videodatingList)) {
        console.error('getWhiteListData() get data error: response=', resp)
        Modal.error({ content: '获取多人视频推荐白名单数据失败，请检查控制台' })
        return
      }
      for (let i = 0; i < videodatingList.length; i++) {
        videodatingList[i].idx = i + 1
      }
      yield put({
        type: 'displayList',
        payload: videodatingList
      })
    },
    // 添加频道视频白名单
    * addWhiteListBySidSsid ({ payload }, { call, put }) {
      const { newSid, newSsid, callback } = payload
      if (!checkSid(newSid) || !checkSsid(newSsid)) {
        return
      }
      yield put({
        type: 'setBtnStatus',
        payload: true
      })
      let resp = yield call(whiteListAdd, newSid, newSsid)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[添加白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 0) {
        message.success('添加成功')
        if (callback) {
          callback()
        }
      } else {
        console.error('addWhiteListBySid()：[添加白名单] 返回结果为：', resp)
        Modal.warn({ content: '添加失败, 请检查控制台' })
      }
      yield put({
        type: 'setBtnStatus',
        payload: false
      })
    },
    // 删除频道视频白名单
    * delWhiteListBySidSsid ({ payload }, { call, put }) {
      const { sid, ssid } = payload
      if (!checkSid(sid) || !checkSsid(ssid)) {
        return
      }
      let resp = yield call(whiteListDel, sid, ssid)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 0) {
        message.success('删除成功')
        yield put({
          type: 'getWhiteListData'
        })
      } else {
        Modal.error({ content: '删除失败：status=' + status })
        console.error('delWhiteListBySidSsid() resp=', resp)
      }
    }
  }
}
