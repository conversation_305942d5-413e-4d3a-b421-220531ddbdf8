
import { simpleRequire3 } from '@/utils/common'

var channelMap = null // 抢空投全局配置
var poolMap = {} // poolID=>name
var groupType = ''

async function getMethodRequire (uri, params, cbFunc) {
  let resp = null
  resp = await simpleRequire3(uri, params)
  cbFunc(resp)
}

// 获取所有道具池选项列表
function getPoolNameOptions (poolMap) {
  if (!poolMap) {
    return []
  }
  let options = []
  for (let id in poolMap) {
    options.push({ label: poolMap[id], value: parseInt(id), key: parseInt(id) })
  }
  return options
}

// 获取所有业务选项
function getBusinessNameOptions (channelMap, poolMap) {
  if (!channelMap) {
    return []
  }
  let options = []
  let tmpSet = {}
  for (let id in channelMap) {
    let name = channelMap[id].BusinessName
    let key = channelMap[id].Business
    let pid = channelMap[id].Pool
    if (tmpSet[name]) {
      continue
    }
    if (!poolMap[pid]) {
      continue
    }
    options.push({ label: name, value: key, key: key })
    tmpSet[name] = 1
  }
  return options
}

// 获取所有产品端选项
function getAppOptions (channelMap) {
  if (!channelMap) {
    return []
  }
  let options = []
  let tmpSet = {}
  for (let id in channelMap) {
    let name = channelMap[id].AppName
    if (tmpSet[name]) {
      continue
    }
    // console.debug('tmpSet===>', tmpSet)
    options.push({ label: name, value: parseInt(id), key: parseInt(id) })
    tmpSet[name] = 1
  }
  return options
}

// =======================================

// 初始化配置
export function initGlobalBossConfig (setModalFunc, group = '') {
  if (channelMap && groupType === group) {
    return
  }
  getMethodRequire(`/drop/admin/get_global_config?group=${group}`, null, (ret) => {
    if (ret.status === 0) {
      channelMap = ret.list.channelMap
      poolMap = ret.list.poolMap
      groupType = group
      setModalFunc('poolNameOptions', getPoolNameOptions(ret.list.poolMap))
      setModalFunc('businessNameOptions', getBusinessNameOptions(ret.list.channelMap, poolMap))
      setModalFunc('appNameOptions', getAppOptions(ret.list.channelMap))
    }
  })
}

// =======================================

// 根据道具池id获取各种绑定的配置值
export function getFieldsValueByPoolID (poolId, fieldName) {
  // console.debug('channelMap==>', poolId, channelMap)
  if (!channelMap) {
    return '???'
  }
  for (let channelID in channelMap) {
    if (channelMap[channelID].Pool === poolId || channelMap[channelID].Crazy === poolId) {
      return channelMap[channelID][fieldName]
    }
  }
  return '???'
}

// 根据渠道获取各个字段的值
export function getFieldsValueByChannelID (channelID, fileName) {
  if (!channelMap) {
    return '???'
  }
  if (channelMap[channelID] && fileName) {
    return channelMap[channelID][fileName]
  }
  return '???'
}

// 根据道具池ID获取道具池名称
export function getPoolNameByPoolID (poolID) {
  if (!poolMap) {
    return `${poolID}?`
  }
  return poolMap[poolID] || `${poolID}?`
}
