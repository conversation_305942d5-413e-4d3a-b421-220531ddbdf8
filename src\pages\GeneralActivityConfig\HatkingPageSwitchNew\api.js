import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/rank/activity_config/pod_star_config_list?${stringify(params)}`)
}

export function update (params) {
  return request(`/rank/activity_config/update_pod_star_config`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
