import { getApprovalList, getConfigList, updateConfig } from './api'
import { message } from 'antd'

export default {
  namespace: 'appReviewConfig',

  state: {
    configList: [
      {
        'filterOn': 1,
        'platform': 3,
        'hostName': 'dreamer',
        'hostVersion': '4.6.0',
        'channelSource': ['hw'],
        'ignoreEntryList': [2, 8, 5, 11, 77, 1369, 83]
      },
      {
        'filterOn': 0,
        'platform': 3,
        'hostName': 'yomi',
        'hostVersion': '2.12.1',
        'channelSource': ['hw'],
        'ignoreEntryList': [2, 8, 81, 1213, 83, 1368, 1369, 5, 77, 1001, 125]
      }
    ],
    approvalList: [
      {
        'filterOn': 0,
        'platform': 3,
        'hostName': 'yomi',
        'hostVersion': '2.12.1',
        'channelSource': ['hw'],
        'ignoreEntryList': [2, 8, 81, 1213, 83, 1368, 1369, 5, 77, 1001, 125],
        'opUser': 'pengzhangjie'
      }
    ]
  },
  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },

    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        configList: payload
      }
    },

    updateApprovalList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        approvalList: payload
      }
    }
  },

  effects: {
    * getReviewList ({ payload }, { call, put }) {
      try {
        const { data: { list } } = yield call(getConfigList, payload)

        yield put({
          type: 'updateList',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        message.error('error:', e)
      }
    },
    * updateReviewConfig ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(updateConfig, payload)
        if (status !== 0) {
          message.error(msg)
        } else if (status === 0 && msg.length > 0) {
          message.success(msg)
        } else {
          message.success('操作成功')
        }
        yield put({
          type: 'getReviewList',
          payload: {}
        })
      } catch (e) {
        message.error('error:', e)
      }
    },
    * getApprovalList ({ payload }, { call, put }) {
      try {
        const { data: { list } } = yield call(getApprovalList, payload)

        yield put({
          type: 'updateApprovalList',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        message.error('error:', e)
      }
    }
  }
}
