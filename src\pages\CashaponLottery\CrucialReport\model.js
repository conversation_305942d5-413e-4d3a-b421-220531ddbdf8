import { getLists } from './api'
import { message } from 'antd'

export default {
  namespace: 'cashaponCrucialReport',

  state: {
    list: []
  },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { status, msg, list } } = yield call(getLists, payload)
      if (status === 0) {
        yield put({
          type: 'updateList',
          payload: Array.isArray(list) ? list : []
        })
      } else {
        message.error('failed' + msg)
      }
    }
  }
}
