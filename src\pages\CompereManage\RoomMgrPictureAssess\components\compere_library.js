import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Modal, Input, Select, message, Tooltip, Popconfirm } from 'antd'
import { connect } from 'dva'
import PopImage from '@/components/PopImage'
import { exportExcel } from 'xlsx-oc'
import { deepClone } from '../../../../utils/common'
// const { confirm } = Modal
const namespace = 'roomMgrLibrary'
const FormItem = Form.Item
const Option = Select.Option

const getListUri = `${namespace}/getList`
const updateItemUri = `${namespace}/updateItem`
const removeItemUri = `${namespace}/deleteItem`
const batchApprovalUri = `${namespace}/batchApproval`

const statusAuditing = 1 // 审核中

let from = 1
// let pagenum = 1 // todo
// let pagesize = 200 // todo

// 业务类型
// const BusinessTypeAll = 0 // 全部
// const BusinessTypeJY = 1 // 交友
// const BusinessTypePK = 2 // 约战
// const BusinessTypeBABY = 3 // 宝贝
// const BusinessTypeZW = 4 // 追玩
// const BusinessTypeZK = 5 // 追看
// const BusinessTypePW = 6 // 陪玩

@connect(({ roomMgrLibrary }) => ({
  model: roomMgrLibrary
}))

class RoomMgrLibrary extends Component {
  constructor (props) {
    super(props)
    const { roomMgrList } = props
    this.state = { selectedRow: '', list: roomMgrList, businessType: -1, pagenum: 0, pagesize: 1000, visible: false, isUpdate: false, value: {}, title: '', searchKey: '', searchResult: [], searchDone: false, searchUID: 0, searchASID: 0, searchSSID: 0, searchLiveType: 0, searchStatus: 0 }
  }
  // 取审核状态说明
  getStatusDesc = (status) => {
    switch (status) {
      case 0: return '未上传'
      case 1: return '审核中'
      case 2: return '审核通过'
      case 3: return '审核不通过'
      case 4: return '未上传'
    }
    return ''
  }
  // column structs.
  columns = [
    { title: '序号', width: 40, fixed: 'left', dataIndex: 'index', align: 'center' },
    { title: '签约sid', width: 60, fixed: 'left', dataIndex: 'contractSid', align: 'center' },
    { title: '经营sid', width: 60, fixed: 'left', dataIndex: 'sid', align: 'center' },
    { title: '经营ssid', width: 80, fixed: 'left', dataIndex: 'ssid', align: 'center' },
    { title: '经营asid', width: 60, fixed: 'left', dataIndex: 'asid', align: 'center' },
    // { title: '房管uid', width: 60, fixed: 'left', dataIndex: 'uid', align: 'center' },
    // { title: '房管YY号', width: 80, dataIndex: 'imid', align: 'center' },
    // { title: '房管昵称', width: 100, dataIndex: 'nick', align: 'center' },
    { title: '厅名称', width: 100, dataIndex: 'tingName', align: 'center' },
    // { title: '开播类型', width: 60, dataIndex: 'liveStatus', align: 'center' },
    // { title: '视频截图', width: 60, dataIndex: 'urlScreenshot', align: 'center', render: text => <PopImage value={text} /> },
    // { title: '推荐图(330*300)', width: 80, dataIndex: 'urlYYLiveRecomm', align: 'center', render: text => <PopImage value={text} /> },
    // { title: '状态', width: 60, dataIndex: 'statusYYLiveRecomm', align: 'center', render: (text, record) => this.getStatusDesc(text) },
    { title: '标题', width: 80, dataIndex: 'title', align: 'center' },
    { title: '状态',
      width: 100,
      dataIndex: 'statusTitle',
      align: 'center',
      render: (text, record) => (
        <Tooltip placement='top' title={this.statusDescHandler(record.statusDetail.title)}>
          <span>{this.getStatusDesc(text)}</span>
        </Tooltip>
      )
    },
    { title: '推荐图(1:1)', width: 80, dataIndex: 'urlDatingMatch', align: 'center', render: text => <PopImage value={text} /> },
    { title: '状态',
      width: 100,
      dataIndex: 'statusDatingMatch',
      align: 'center',
      render: (text, record) => (
        <Tooltip placement='top' title={this.statusDescHandler(record.statusDetail.datingMatch)}>
          <span>{this.getStatusDesc(text)}</span>
        </Tooltip>
      )
    },
    { title: '推荐图(4:3)', width: 80, dataIndex: 'urlSuperCompere', align: 'center', render: text => <PopImage value={text} /> },
    { title: '状态',
      width: 100,
      dataIndex: 'statusSuperCompere',
      align: 'center',
      render: (text, record) => (
        <Tooltip placement='top' title={this.statusDescHandler(record.statusDetail.superCompere)}>
          <span>{this.getStatusDesc(text)}</span>
        </Tooltip>
      )
    },
    { title: '推荐图(4:5)', width: 80, dataIndex: 'urlYYLiveRecomm45', align: 'center', render: text => <PopImage value={text} /> },
    { title: '状态',
      width: 100,
      dataIndex: 'statusYYLiveRecomm45',
      align: 'center',
      render: (text, record) => (
        <Tooltip placement='top' title={this.statusDescHandler(record.statusDetail.liveRecomm45)}>
          <span>{this.getStatusDesc(text)}</span>
        </Tooltip>
      )
    },
    { title: '操作',
      width: 80,
      align: 'center',
      export: false,
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(true, record)}>更新</a><Divider type='vertical' />
        </span>)
    }
  ]

  // defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  pagination = { pagesizeOptions: ['10', '50', '200', '1000'], showSizeChanger: true, pagesize: 10, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }
  defaultPageValue = {
    defaultPageSize: 10,
    pagesizeOptions: ['10', '50', '100', '500', '1000'],
    showSizeChanger: true,
    // onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { businessType: 0, pagenum: 0, pagesize: 1000, visible: false, isUpdate: false, value: {}, title: '', searchKey: '', searchResult: [], searchDone: false, searchIMID: 0, searchUID: 0, searchASID: -1, searchSSID: -1, searchLiveType: -1, searchStatus: -1 }

  statusDescHandler = (descList) => {
    console.log(descList)
    let content = null
    if (Array.isArray(descList) && descList.length > 0) {
      for (let i = 0; i < descList.length; i++) {
        content = <span>{content}<div className={'statusDesc'}>{descList[i]}</div></span>
      }
    }
    return <span>{content}</span>
  }

  // show modal
  showModal = (isUpdate, record) => () => {
    console.log(record)
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? '更新' : '添加' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  // update
  handleSubmit = () => {
    this.formRef.submit() // 提交表单 触发 onFinish 事件
  }

  // reset search info
  handleReset = clearFilters => () => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  // 添加 与 编辑
  onFinish = values => {
    const { dispatch } = this.props
    console.log(values)
    values.urlYYLiveRecommStatus45 = parseInt(values.statusYYLiveRecomm45)
    values.urlYYLiveRecommStatus = parseInt(values.statusYYLiveRecomm)
    values.urlNewPlayerStatus = parseInt(values.statusNewPlayer)
    values.urlSuperCompereStatus = parseInt(values.statusSuperCompere)
    values.urlDatingMatchStatus = parseInt(values.statusDatingMatch)
    values.urlStatusTitle = parseInt(values.statusTitle)
    // const { isUpdate } = this.state
    // let data = { values }
    dispatch({
      type: updateItemUri,
      payload: values
    }).then(res => {
      this.getList()
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  componentWillReceiveProps (nextProps) {
    const { configList } = nextProps
    this.setState({ list: configList })
  }

  getParam = () => {
    const { searchIMID, searchUID, searchSID, searchASID, searchSSID, searchLiveType, searchStatus } = this.state
    return { yy: searchIMID, uid: searchUID, sid: searchSID, asid: searchASID, ssid: searchSSID, livetype: searchLiveType, status: searchStatus }
  }

  getList () {
    const { dispatch } = this.props
    let data = this.getParam()
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  onSearch = () => {
    const { dispatch } = this.props
    let data = this.getParam()
    console.log('data:', data)
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { from: from, ids: key }
    dispatch({
      type: removeItemUri,
      payload: data
    }).then(res => {
      this.getList()
    })
  }

  // get list from server.
  componentDidMount () {
    const { dispatch, model: { list } } = this.props
    const { searchIMID, searchASID, searchSSID, searchLiveType, searchStatus } = this.state
    let data = { yy: searchIMID, asid: searchASID, ssid: searchSSID, livetype: searchLiveType, status: searchStatus }
    dispatch({
      type: getListUri,
      payload: data
    })
    if (Array.isArray(list) && list.length > 0) {
      this.setState({ list })
    }
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  onExport = () => {
    const { list } = this.state
    let business = { 1: '交友', 2: '约战', 3: '宝贝', 4: '打通房', 101: '派单房', 201: '游戏房', 301: '语音房' }
    let exportData = list.map(item => {
      item.businessType = business[item.businessType]
      return item
    })

    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.export === undefined || col.export) {
        exportHeader.push({ k: col.dataIndex, v: col.title })
      }
    })

    exportExcel(exportHeader, exportData)
  }

  onSelectChange = (selectedRowKeys, selectedRows) => {
    let value = selectedRows.map(item => item.uid).join(',')
    this.setState({ selectedRowKeys, selectedRows })
    this.setState({ selectedRow: value })
  }

  handlerBatchPassReject = (isPass) => () => {
    const { selectedRowKeys, selectedRows } = this.state
    console.log(selectedRowKeys, selectedRows)
    if (!Array.isArray(selectedRowKeys) || selectedRowKeys.length === 0) {
      message.warn('请选择记录')
      return
    }
    if (!Array.isArray(selectedRows)) {
      message.warn('请选择记录')
      return
    }

    let canApproval = true
    selectedRows.forEach(v => {
      if (v.statusDatingMatch !== statusAuditing && v.statusSuperCompere !== statusAuditing &&
        v.statusTitle !== statusAuditing && v.statusYYLiveRecomm45 !== statusAuditing) {
        canApproval = false
        message.warn('没有审核中的状态, 记录不能发起审批, 房管uid:' + v.uid)
      }
    })

    if (!canApproval) {
      return
    }

    const data = { pass: isPass, uids: selectedRowKeys }
    console.log(data)
    this.props.dispatch({
      type: batchApprovalUri,
      payload: data
    }).then(res => {
      this.getList()
    })
  }

  handlerExport = () => () => {
    const { selectedRows } = this.state
    if (!Array.isArray(selectedRows)) {
      message.warn('请选择记录')
      return
    }

    let data = deepClone(selectedRows)
    for (let i = 0; i < data.length; i++) {
      data[i].statusDatingMatch = this.getStatusDesc(data[i].statusDatingMatch)
      data[i].statusNewPlayer = this.getStatusDesc(data[i].statusNewPlayer)
      data[i].statusSuperCompere = this.getStatusDesc(data[i].statusSuperCompere)
      data[i].statusTitle = this.getStatusDesc(data[i].statusTitle)
      data[i].statusYYLiveRecomm = this.getStatusDesc(data[i].statusYYLiveRecomm)
      data[i].statusYYLiveRecomm45 = this.getStatusDesc(data[i].statusYYLiveRecomm45)
    }

    console.log(data)
    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.export === undefined || col.export) {
        exportHeader.push({ k: col.dataIndex, v: col.title })
      }
    })

    exportExcel(exportHeader, data, '房管推荐图封面审核.xlsx')
  }

  // delete
  handleBatchDel = () => {
    const { dispatch } = this.props
    if (this.state.selectedRow === '') {
      message.error('请先选中再操作！')
      return
    }
    const data = { from: from, ids: this.state.selectedRow }
    // confirm({
    //   title: '确认删除?',
    //   onOk () {
    //     dispatch({
    //       type: removeItemUri,
    //       payload: data
    //     }).then(res => {
    //       this.setState({ selectedRowKeys: null })
    //       this.getList()
    //     })
    //   }
    // })
    dispatch({
      type: removeItemUri,
      payload: data
    }).then(res => {
      // this.setState({ selectedRowKeys: null })
      this.getList()
    })
  }

  // 导入配置回调
  onUploadFileStatusChange = (info) => {
    const { status, name, response } = info.file
    let success = false
    if (status === 'uploading') {
      return
    }
    do {
      if (status === 'error') {
        console.error('upload failed: info=', info)
        break
      }
      if (!response) {
        console.error('unexpect response: response=', response)
        break
      }
      if (response.status !== 0) {
        console.error('unexpect response: response=', response)
        break
      }
      if (response.count !== response.success) {
        message.error(`${name}上传结果异常，请检查控制台,成功:'` + response.success + ',失败:' + response.fail)
        // console.error('unexpect respones: response=', response)
        break
      }
      success = true
    } while (false)

    if (success) {
      message.info(`${name} 成功导入:'` + response.success)
    }
    this.getList()
  }
  // content
  render () {
    const { value, searchResult, searchDone, visible, title, selectedRowKeys, list } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 10 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange
    }
    return (
      <PageHeaderWrapper >
        <p style={{ marginBlock: 0, color: 'red' }}> 说明：1:1-用于精彩世界-交友速配、Yo交友、Yo语音、YY交友官网推荐；4:5-用于手机YY推荐；4:3-用于精彩世界-热门直播&新手导航推荐 </p>
        <Card>
          <div>
            {/* 房管YY号： */}
            {/* <Input allowClear placeholder='房管YY号' onChange={e => this.setState({ searchIMID: e.target.value, searchDone: false })} style={{ width: 120 }} /> /!* 搜索按钮 *!/ */}
            {/* 房管UID： */}
            {/* <Input allowClear placeholder='房管UID' onChange={e => this.setState({ searchUID: e.target.value, searchDone: false })} style={{ width: 120 }} /> /!* 搜索按钮 *!/ */}
            {/* <Divider type='vertical' /> /!* 分割线 *!/ */}
            频道：
            <Input allowClear placeholder='搜索sid' onChange={e => this.setState({ searchSID: e.target.value, searchDone: false })} style={{ width: 120 }} /> {/* 搜索按钮 */}
            子频道：
            <Input allowClear placeholder='搜索ssid' onChange={e => this.setState({ searchSSID: e.target.value, searchDone: false })} style={{ width: 120 }} /> {/* 搜索按钮 */}
            短位频道：
            <Input allowClear placeholder='搜索asid' onChange={e => this.setState({ searchASID: e.target.value, searchDone: false })} style={{ width: 120 }} /> {/* 搜索按钮 */}
            <Divider type='vertical' />
            状态：
            <Select allowClear labelInValue defaultValue={{ key: '-1' }} style={{ width: 110 }} onChange={e => this.setState({ searchStatus: e.key })}>
              <Option value='-1'>全部</Option>
              <Option value='1'>审核中</Option>
              <Option value='2'>通过</Option>
              <Option value='3'>不通过</Option>
              <Option value='4'>未上传</Option>
            </Select>
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onSearch}>搜索</Button>
            <Popconfirm title='确认批量通过?' onConfirm={this.handlerBatchPassReject(true)} okText='是的' cancelText='暂不'>
              <Button style={{ marginLeft: 5 }} type='primary' className={'buttonStyle'} >批量通过</Button>
            </Popconfirm>
            <Popconfirm title='确认批量不通过?' onConfirm={this.handlerBatchPassReject(false)} okText='是的' cancelText='暂不'>
              <Button style={{ marginLeft: 5 }} type='primary' className={'buttonStyle'} >批量不通过</Button>
            </Popconfirm>
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.handlerExport()}>导出</Button>
          </div>
          <Divider type='horizontal' />

          <Table rowKey={(record, index) => record.uid} rowSelection={rowSelection} dataSource={searchDone ? searchResult : list} bordered columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 2540 }} />
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} {...formItemLayout} ref={form => { this.formRef = form }}>
            <FormItem hidden style={{ marginBottom: 10 }} label='sid' name='sid' >
              <Input disabled />
            </FormItem>
            <FormItem style={{ marginBottom: 10 }} label='ssid' name='ssid' >
              <Input disabled />
            </FormItem>
            <FormItem hidden style={{ marginBottom: 10 }} label='房管uid' name='uid' >
              <Input disabled />
            </FormItem>

            {/* <FormItem style={{ marginBottom: 10 }} label='推荐图(330*300)' name='urlYYLiveRecomm' >
              <PopImage />
            </FormItem>
            <FormItem style={{ marginBottom: 10 }} label='状态' name='statusYYLiveRecomm' >
              <Select disabled={!(value.statusYYLiveRecomm === 1)} defaultValue={1} placeholder='请选择'>
                <Option disabled value={1}>审核中</Option>
                <Option value={2}>通过</Option>
                <Option value={3}>审核不通过</Option>
                <Option disabled value={4}>未上传</Option>
              </Select>
            </FormItem> */}

            <FormItem style={{ marginBottom: 10 }} label='推荐图(1:1)' name='urlDatingMatch' >
              <PopImage />
            </FormItem>
            <FormItem style={{ marginBottom: 10 }} label='状态' name='statusDatingMatch' >
              <Select disabled={[3, 4].includes(value.statusDatingMatch)} defaultValue={1} placeholder='请选择'>
                <Option disabled value={1}>审核中</Option>
                <Option value={2}>通过</Option>
                <Option value={3}>审核不通过</Option>
                <Option disabled value={4}>未上传</Option>
              </Select>
            </FormItem>

            <FormItem style={{ marginBottom: 10 }} label='推荐图(4:3)' name='urlSuperCompere' >
              <PopImage />
            </FormItem>
            <FormItem style={{ marginBottom: 10 }} label='状态' name='statusSuperCompere' >
              <Select disabled={[3, 4].includes(value.statusSuperCompere)} defaultValue={1} placeholder='请选择'>
                <Option disabled value={1}>审核中</Option>
                <Option value={2}>通过</Option>
                <Option value={3}>审核不通过</Option>
                <Option disabled value={4}>未上传</Option>
              </Select>
            </FormItem>

            <FormItem style={{ marginBottom: 10 }} label='推荐图(4:5)' name='urlYYLiveRecomm45' >
              <PopImage />
            </FormItem>
            <FormItem style={{ marginBottom: 10 }} label='状态' name='statusYYLiveRecomm45' >
              <Select disabled={[3, 4].includes(value.statusYYLiveRecomm45)} defaultValue={1} placeholder='请选择'>
                <Option disabled value={1}>审核中</Option>
                <Option value={2}>通过</Option>
                <Option value={3}>审核不通过</Option>
                <Option disabled value={4}>未上传</Option>
              </Select>
            </FormItem>
            <FormItem label='标题' name='title'>
              <Input readOnly />
            </FormItem>
            <FormItem style={{ marginBottom: 10 }} label='状态' name='statusTitle' >
              <Select disabled={[3, 4].includes(value.statusTitle)} defaultValue={1} placeholder='请选择'>
                <Option disabled value={1}>审核中</Option>
                <Option value={2}>通过</Option>
                <Option value={3}>审核不通过</Option>
                <Option disabled value={4}>未上传</Option>
              </Select>
            </FormItem>

          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default RoomMgrLibrary
