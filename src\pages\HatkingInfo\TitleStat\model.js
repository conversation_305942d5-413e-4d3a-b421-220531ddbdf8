import { genGetRequireTemplate } from '@/utils/common'

const queryUserStat = genGetRequireTemplate('/pod_customer_boss/get_daily_stat', 'userList')
const queryTaskStat = genGetRequireTemplate('/pod_customer_boss/get_daily_stat', 'taskList')
const queryRewardStat = genGetRequireTemplate('/pod_customer_boss/get_daily_stat', 'rewardList')
const queryCurrentTitle = genGetRequireTemplate('/pod_customer_boss/get_current_title', 'currentTitle')

export default {
  namespace: 'hatkingInfoTitleInfo',
  state: {
    userList: [],
    taskList: [],
    rewardList: [],
    currentTitle: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    queryUserStat,
    queryTaskStat,
    queryRewardStat,
    queryCurrentTitle
  }
}
