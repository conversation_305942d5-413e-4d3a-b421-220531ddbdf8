import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Table, InputNumber, Select, DatePicker, Button, message, Modal, Form, Input, Row, Col, Divider, Upload } from 'antd'
import { UploadOutlined } from '@ant-design/icons'
import PopImage from '@/components/PopImage'

const Option = Select.Option

const namespace = 'newGuildSettleInV2'

const yesNoMap = [
  { label: '是', value: 1 },
  { label: '否', value: 2 }
]
// const yesNoMap2 = { 0: '全部', 1: '是', 2: '否' }

const reviewMap = [
  { label: '全部', value: 0 },
  { label: '待审核', value: 1 },
  { label: '审核通过', value: 2 },
  { label: '审核不通过', value: 3 }
]

const approvalMap = [
  { label: '全部', value: 0 },
  { label: '待审核', value: 1 },
  { label: '一审中', value: 2 },
  { label: '一审通过，二审中', value: 3 },
  { label: '审核通过', value: 4 },
  { label: '审核不通过', value: 5 }
]

const companyRealAuthMap = { '': '未审核', 'S0I': '待审核', 'S0S': '审核中', 'S0A': '审核通过', 'S0X': '审核不通过' }
const companyRealAuthList = [
  { label: '未审核', value: 'xxx' },
  { label: '待审核', value: 'S0I' },
  { label: '审核中', value: 'S0S' },
  { label: '审核通过', value: 'S0A' },
  { label: '审核不通过', value: 'S0X' }
]

const faildDescList = [
  { label: '公会合作协议填写有误', value: '公会合作协议填写有误' },
  { label: '三方协议填写有误', value: '三方协议填写有误' },
  { label: '乙方公会主要管理人员填写有误', value: '乙方公会主要管理人员填写有误' }
]

const companyTypeMap = { 0: '-', 1: '有限责任公司', 2: '股份有限公司' }
// const yesNoMap3 = { 0: '-', 1: '是', 2: '否' }

var moment = require('moment')
var dateFormat = 'YYYY-MM-DD'
var dateTimeFormat = 'YYYY-MM-DD HH:mm:ss'

@connect(({ newGuildSettleInV2 }) => ({
  model: newGuildSettleInV2
}))

class newGuildSettleInv2SuperCrysta extends Component {
  columns = [
    { title: '序号', dataIndex: 'idx', align: 'center', fixed: 'left' },
    { title: '频道ID', dataIndex: 'sid', align: 'center', fixed: 'left' },
    { title: '短位ID', dataIndex: 'asid', align: 'center', fixed: 'left' },
    { title: '企业认证审核状态', dataIndex: 'companyRealAuthStatus', align: 'center', render: (text, record) => (companyRealAuthMap[record.companyRealAuthStatus]) },
    { title: '创建频道时间', dataIndex: 'createSidTime', align: 'center', render: (text, record) => (record.createSidTime === 0 ? '' : moment.unix(record.createSidTime).format(dateTimeFormat)) },
    { title: '申请时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (record.timestamp === 0 ? '' : moment.unix(record.timestamp).format(dateTimeFormat)) },
    { title: '是否上传合同', dataIndex: 'reviewReason', align: 'center', render: (tect, record) => (record.uploadStatus ? '是' : '否') },
    { title: '运营审核状态', dataIndex: 'reviewStatus', align: 'center', render: v => reviewMap.find(i => i.value === v).label },
    { title: '业务审核状态', dataIndex: 'approvalStatus', align: 'center', render: v => approvalMap.find(i => i.value === v).label },
    { title: '历史申请记录', key: 'historyRecord', align: 'center', render: (text, record) => (<span><a onClick={this.showHistoryModal(record)}>查看</a></span>) },
    { title: '操作', key: 'operation', align: 'center', render: (text, record) => (<span><a onClick={this.showDetailModal(record)}>查看并审批</a> <Divider type='vertical' /> <a onClick={this.showUploadModal(record)}>上传合同</a></span>) }
  ]

  columnshistory = [
    { title: '日期', dataIndex: 'historyTimestamp', align: 'center', render: (text, record) => (record.historyTimestamp === 0 ? '' : moment.unix(record.historyTimestamp).format(dateTimeFormat)) },
    { title: '申请时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (record.timestamp === 0 ? '' : moment.unix(record.timestamp).format(dateTimeFormat)) },
    { title: '运营审核状态', dataIndex: 'reviewStatus', align: 'center', render: v => reviewMap.find(i => i.value === v).label },
    { title: '业务审核状态', dataIndex: 'approvalStatus', align: 'center', render: v => approvalMap.find(i => i.value === v).label },
    { title: '审核运营', dataIndex: 'reviewUser', align: 'center' },
    { title: '运营备注', dataIndex: 'reviewReason', align: 'center' },
    { title: '业务备注', dataIndex: 'approvalReason', align: 'center', render: (text, record) => (record.reviewStatus === 3 ? record.reviewReason : record.approvalReason) }
  ]

  state = {
    visible: false,
    uploadSuccess: false,
    approvalRejectReasonQuick: '',
    approvalRejectReason: '',
    inviteGuildModalSid: '',
    inviteGuildModalNickname: ''
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  componentDidMount () {
    this.loadData()
  }

  showHistoryModal = (record) => () => {
    this.setState({ visibleHistory: true, historySID: record.sid })
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/listSuperCrystalHistory`,
      payload: { sid: record.sid }
    })
  }

  showDetailModal = (record) => () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/detailSuperCrysta`,
      payload: { sid: record.sid }
    })

    this.setState({ curID: record.id, record: record, visibleSuperCrystalDetail: true })
  }

  showUploadModal = (record) => () => {
    if (this.formRef) {
      this.formRef.resetFields()
      console.log(record)
      this.formRef.setFieldsValue({ id: record.id })
      
      if (record.guildCooperationAgreementURL !== '') {
        let file = { uid: '1', name: record.guildCooperationAgreementName, status: 'done', url: record.guildCooperationAgreementURL, response: { msg: 'success!', status: 0, urls: [record.guildCooperationAgreementURL] } }
        this.formRef.setFieldsValue({ guildCooperationAgreementURL: { file: file, fileList: [file] } })
        this.setState({ guildCooperationAgreementURLList: [file] })
      }
      
      if (record.threePartiesAgreementURL !== '') {
        let file = { uid: '2', name: record.threePartiesAgreementName, status: 'done', url: record.threePartiesAgreementURL, response: { msg: 'success!', status: 0, urls: [record.threePartiesAgreementURL] } }
        this.formRef.setFieldsValue({ threePartiesAgreementURL: { file: file, fileList: [file] } })
        this.setState({ threePartiesAgreementURLList: [file] })
      }

      if (record.guildManagerURL !== '') {
        let file = { uid: '3', name: record.guildManagerName, status: 'done', url: record.guildManagerURL, response: { msg: 'success!', status: 0, urls: [record.guildManagerURL] } }
        this.formRef.setFieldsValue({ guildManagerURL: { file: file, fileList: [file] } })
        this.setState({ guildManagerURLList: [file] })
      }
    }
    this.setState({ curID: record.id, visibleUpload: true })
  }

  loadData = () => {
    const {
      searchSID,
      searchASID,
      searchReviewStatus,
      searchApprovalStatus,
      searchApplyStartTime, 
      searchApplyEndTime,
      searchCompanyRealAuthStatus,
      searchUploadContractStatus
    } = this.state
    const { dispatch } = this.props

    let searchApplyStartTimeTmp = 0
    let searchApplyEndTimeTmp = 0
    if (searchApplyStartTime) {
      searchApplyStartTimeTmp = moment(searchApplyStartTime).unix()
    }
    if (searchApplyEndTime) {
      searchApplyEndTimeTmp = moment(searchApplyEndTime).unix()
    }

    if (searchApplyStartTimeTmp !== 0 && searchApplyEndTimeTmp !== 0 && searchApplyStartTimeTmp >= searchApplyEndTimeTmp) {
      message.warn('开始时间不能大于结束时间')
      return
    }

    let data = { 
      sid: searchSID, 
      asid: searchASID,
      reviewStatus: searchReviewStatus,
      approvalStatus: searchApprovalStatus,
      applyStartTime: searchApplyStartTimeTmp,
      applyEndTime: searchApplyEndTimeTmp,
      companyRealAuthStatus: searchCompanyRealAuthStatus,
      uploadContractStatus: searchUploadContractStatus
    }
    dispatch({
      type: `${namespace}/listSuperCrystal`,
      payload: data
    })
  }

  searchHandle = () => () => {
    this.loadData()
  }

  handleCancelHistory = e => {
    this.setState({ visibleHistory: false })
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  handleCancelUpload = e => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.setState({ visibleUpload: false, guildCooperationAgreementURLList: [], threePartiesAgreementURLList: [], guildManagerURLList: [] })
  }

  handleSubmitUpload = e => {
    if (this.formRef) {
      this.formRef.submit()
    }
  }

  onFinishUpload = values => {
    const { uploadSuccess, curID } = this.state

    console.log(values)

    if (!uploadSuccess) {
      message.warn('上传文件格式不正确')
      return
    }

    if (values && (values.guildCooperationAgreementURL.fileList.length !== 1 || values.threePartiesAgreementURL.fileList.length !== 1 || values.guildManagerURL.fileList.length !== 1)) {
      message.warning('每个表单上传一个文件')
      console.log(values)
      return
    }

    let guildCooperationAgreementURL = ''
    let guildCooperationAgreementName = ''
    if (values && values.guildCooperationAgreementURL.fileList[0].status === 'done' && values.guildCooperationAgreementURL.fileList[0].response.status === 0 && values.guildCooperationAgreementURL.fileList[0].response.urls[0]) {
      guildCooperationAgreementURL = values.guildCooperationAgreementURL.fileList[0].response.urls[0]
      guildCooperationAgreementName = values.guildCooperationAgreementURL.fileList[0].name
    }

    let threePartiesAgreementURL = ''
    let threePartiesAgreementName = ''
    if (values && values.threePartiesAgreementURL.fileList[0].status === 'done' && values.threePartiesAgreementURL.fileList[0].response.status === 0 && values.threePartiesAgreementURL.fileList[0].response.urls[0]) {
      threePartiesAgreementURL = values.threePartiesAgreementURL.fileList[0].response.urls[0].replace('http://', 'https://')
      threePartiesAgreementName = values.threePartiesAgreementURL.fileList[0].name
    }

    let guildManagerURL = ''
    let guildManagerName = ''
    if (values && values.guildManagerURL.fileList[0].status === 'done' && values.guildManagerURL.fileList[0].response.status === 0 && values.guildManagerURL.fileList[0].response.urls[0]) {
      guildManagerURL = values.guildManagerURL.fileList[0].response.urls[0].replace('http://', 'https://')
      guildManagerName = values.guildManagerURL.fileList[0].name
    }

    if (guildCooperationAgreementURL === '' || threePartiesAgreementURL === '' || guildManagerURL === '') {
      message.warning('上传文件错误, 重新上传', guildCooperationAgreementURL, threePartiesAgreementURL, guildManagerURL)
      console.log(values)
      return
    }

    let data = { id: curID, guildCooperationAgreementURL: guildCooperationAgreementURL, guildCooperationAgreementName: guildCooperationAgreementName, threePartiesAgreementURL: threePartiesAgreementURL, threePartiesAgreementName: threePartiesAgreementName, guildManagerURL: guildManagerURL, guildManagerName: guildManagerName }

    console.log(data)
    this.props.dispatch({
      type: `${namespace}/uploadSuperSrysta`,
      payload: data
    })
    this.setState({ visibleUpload: false, guildCooperationAgreementURLList: [], threePartiesAgreementURLList: [], guildManagerURLList: [] })
  }

  showInviteGuildModal = () => () => {
    this.setState({ inviteGuildModalVisible: true })
  }

  inviteGuildSubmit = () => {
    if (this.formRefInviteGuild) {
      this.formRefInviteGuild.submit()
    }
  }

  onFinishInviteGuild = values => {
    this.props.dispatch({
      type: `${namespace}/inviteSuperCrysta`,
      payload: { sid: parseInt(values.sid) }
    })
    this.setState({ inviteGuildModalVisible: false })
  }

  inputInviteGuildSid = (value) => {
    console.log(value)
    this.props.dispatch({
      type: `${namespace}/getOwInfo`,
      payload: { sid: value },
      cbFn: (data) => {
        console.log(data)
        if (this.formRefInviteGuild) {
          this.formRefInviteGuild.setFieldsValue({ inviteGuildModalSid: data.yy, inviteGuildModalNickname: data.nick })
        }
      }
    })
  }

  handleCancelDetail = e => {
    this.setState({ curID: '', visibleSuperCrystalDetail: false })
  }

  cancelDetailHandle = () => () => {
    this.setState({ visibleSuperCrystalDetail: false })
  }

  UpLoadOnChange = info => {
    if (info.file.status !== 'done') {
      return
    }

    if (info.file.type === 'image/png' || info.file.type === 'image/jpg' || info.file.type === 'image/jpeg' || info.file.type === 'image/gif') {
      this.setState({ uploadSuccess: false })
      message.error(`${info.file.name} 不支持文档类型, 请重新上传`)
      return
    }

    if (info.file.response.status === 0) {
      this.setState({ uploadSuccess: true })
      message.success(`${info.file.name} uploaded successfully`)
    } else {
      message.error(info.file.response.msg)
    }
  }

  approvalPassHandle = () => () => {
    let detailHTML = document.getElementById('inner')?.innerHTML
    console.log(detailHTML)
    const { curID, approvalPassReason } = this.state
    let data = { id: curID, reviewStatus: 2, reviewReason: approvalPassReason, html: detailHTML }
    console.log(data)
    this.props.dispatch({
      type: `${namespace}/approvalSuperCrysta`,
      payload: data
    })
    this.hiddenApprovalPass()
    this.setState({ visibleSuperCrystalDetail: false })
    detailHTML = null
  }

  showApprovalPass = () => () => {
    this.setState({ approvalPassVisible: true })
  }

  hiddenApprovalPass = () => {
    this.setState({ approvalPassVisible: false, approvalPassReason: '' })
  }

  hiddenApprovalPass2 = () => () => {
    this.setState({ approvalPassVisible: false, approvalPassReason: '' })
  }

  showApprovalReject = () => () => {
    this.setState({ approvalRejectVisible: true })
  }

  hiddenApprovalReject = () => {
    this.setState({ approvalRejectVisible: false, approvalRejectReason: '', approvalRejectReasonQuick: '' })
  }

  hiddenApprovalReject2 = () => () => {
    this.setState({ approvalRejectVisible: false, approvalRejectReason: '', approvalRejectReasonQuick: '' })
  }

  approvalRejectHandle = () => () => {
    const { curID, approvalRejectReason, approvalRejectReasonQuick } = this.state
    console.log(approvalRejectReason, approvalRejectReasonQuick)
    if (approvalRejectReason && approvalRejectReasonQuick && approvalRejectReason !== '' && approvalRejectReasonQuick !== '') {
      message.warning('快速选择和手动输入只能选其一')
      return
    }
    let reviewReason = ''
    if (approvalRejectReason && approvalRejectReason !== '') {
      reviewReason = approvalRejectReason
    } else if (approvalRejectReasonQuick && approvalRejectReasonQuick !== '') {
      reviewReason = approvalRejectReasonQuick
    }

    if (reviewReason.length === 0) {
      message.warn('请输入审批意见')
      return
    }

    let data = { id: curID, reviewStatus: 3, reviewReason: reviewReason }
    console.log(data)

    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/approvalSuperCrysta`,
      payload: data
    })
    this.hiddenApprovalReject()
    this.setState({ visibleSuperCrystalDetail: false })
  }

  faildDescHandler = (value) => {
    this.setState({ approvalRejectReasonQuick: value })
  }

  displayScreenshot = () => {
    const { record } = this.state
    console.log(record)
    let len = record?.guildApply?.platformInfo?.screenshot?.length || 0
    if (len === 0) {
      return <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><font style={{ marginLeft: 10, fontSize: '16px' }}>公会流水截图凭证：</font></Row>
    }
    const screenshot = record?.guildApply?.platformInfo?.screenshot
    if (len === 1) {
      return <span><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><font style={{ marginLeft: 10, fontSize: '16px' }}>公会流水截图凭证：</font></Row><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><Col span={4}><PopImage value={screenshot[0]} /></Col></Row></span>
    }
    if (len === 2) {
      return <span><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><font style={{ marginLeft: 10, fontSize: '16px' }}>公会流水截图凭证：</font></Row><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><Col span={4}><PopImage value={screenshot[0]} /></Col><Col span={4}><PopImage value={screenshot[1]} /></Col></Row></span>
    }
    if (len === 3) {
      return <span><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><font style={{ marginLeft: 10, fontSize: '16px' }}>公会流水截图凭证：</font></Row><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><Col span={4}><PopImage value={screenshot[0]} /></Col><Col span={4}><PopImage value={screenshot[1]} /></Col><Col span={4}><PopImage value={screenshot[2]} /></Col></Row></span>
    }
    if (len === 4) {
      return <span><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><font style={{ marginLeft: 10, fontSize: '16px' }}>公会流水截图凭证：</font></Row><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><Col span={4}><PopImage value={screenshot[0]} /></Col><Col span={4}><PopImage value={screenshot[1]} /></Col><Col span={4}><PopImage value={screenshot[2]} /></Col><Col span={4}><PopImage value={screenshot[3]} /></Col></Row></span>
    }
    if (len === 5) {
      return <span><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><font style={{ marginLeft: 10, fontSize: '16px' }}>公会流水截图凭证：</font></Row><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><Col span={4}><PopImage value={screenshot[0]} /></Col><Col span={4}><PopImage value={screenshot[1]} /></Col><Col span={4}><PopImage value={screenshot[2]} /></Col><Col span={4}><PopImage value={screenshot[3]} /></Col><Col span={4}><PopImage value={screenshot[4]} /></Col></Row></span>
    }
  }

  noHtml = () => {
    return <font color='red'>否</font>
  }

  render () {
    const { 
      guildCooperationAgreementURLList,
      threePartiesAgreementURLList, 
      guildManagerURLList,
      visibleHistory, 
      historySID, 
      visibleUpload, 
      approvalRejectVisible,
      visibleSuperCrystalDetail, 
      approvalRejectReason, 
      approvalPassVisible,
      inviteGuildModalVisible,
      inviteGuildModalSid,
      inviteGuildModalNickname
    } = this.state
    const { model: { listSuperCrystal, listSuperCrystalHistory, tableLoadingSuperCrystalHistory, detailLoadingSuperCrysta, detailSuperCrysta } } = this.props

    const record = detailSuperCrysta

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 10 }
      },
      wrapperCol: {
        xs: { span: 2 },
        sm: { span: 15 }
      }
    }
    console.log(record)
    console.log(inviteGuildModalSid, inviteGuildModalNickname)
    return (
      <Card>
        <span>频道ID</span>
        <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchSID: e })} style={{ width: 100, marginLeft: 3 }} />
        <span style={{ marginLeft: 10 }}>短位ID</span>
        <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID: e })} style={{ width: 100, marginLeft: 3 }} />
        <span style={{ marginLeft: 10 }}>企业认证状态</span>
        <Select allowClear style={{ marginLeft: 5, width: 100 }} placeholder='请选择' onChange={(v) => this.setState({ searchCompanyRealAuthStatus: v })}>{ companyRealAuthList.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
        <span style={{ marginLeft: 10 }}>是否上传合同</span>
        <Select allowClear style={{ marginLeft: 5, width: 100 }} placeholder='请选择' onChange={(v) => this.setState({ searchUploadContractStatus: v })}>{ yesNoMap.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
        <span style={{ marginLeft: 10 }}>运营审核状态</span>
        <Select allowClear style={{ marginLeft: 5, width: 100 }} placeholder='请选择' onChange={(v) => this.setState({ searchReviewStatus: v })}>{ reviewMap.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
        <span style={{ marginLeft: 10 }}>业务审核状态</span>
        <Select allowClear style={{ marginLeft: 5, width: 140 }} placeholder='请选择' onChange={(v) => this.setState({ searchApprovalStatus: v })}>{ approvalMap.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
        <Button style={{ marginLeft: 20 }} type='primary' onClick={this.showInviteGuildModal()}>邀请公会入驻</Button>
        <div style={{ marginTop: 10 }} />
        <span>申请时间</span>
        <DatePicker
          format='YYYY-MM-DD'
          placeholder='开始时间'
          onChange={(v) => this.setState({ searchApplyStartTime: v })}
          style={{ marginLeft: 10 }}
        />
        <span style={{ marginLeft: 5 }}>~</span>
        <DatePicker
          format='YYYY-MM-DD'
          placeholder='结束时间'
          onChange={(v) => this.setState({ searchApplyEndTime: v })}
          style={{ marginLeft: 5 }}
        />
        <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
        <Table style={{ marginTop: 10 }} rowKey='idx' pagination={this.defaultPageValue} columns={this.columns} dataSource={listSuperCrystal} scroll={{ x: 'max-content' }} />

        {/* 历史记录 */}
        <Modal footer={null} forceRender width={1200} visible={visibleHistory} title='历史记录' onCancel={this.handleCancelHistory}>
          <Card>
            <span><font style={{ marginLeft: 5, fontSize: '20px' }}>频道ID:  </font><font style={{ fontSize: '20px' }}>{historySID}</font></span>
            <Table loading={tableLoadingSuperCrystalHistory} style={{ marginTop: 10 }} size='small' rowKey='idx' pagination={this.defaultPageValue} columns={this.columnshistory} dataSource={listSuperCrystalHistory} />
          </Card>
        </Modal>

        {/* 上传合同附件弹窗 */}
        <Modal loadData={detailLoadingSuperCrysta} forceRender width={600} visible={visibleUpload} title='上传合同附件' onCancel={this.handleCancelUpload} onOk={this.handleSubmitUpload}>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinishUpload}>
            <Form.Item name='id' hidden>
              <Input hidden />
            </Form.Item>
            <Form.Item label='公会合作协议' name='guildCooperationAgreementURL' rules={[{ required: true }]}>
              <Upload name='file' action='https://fts.yy.com/fs/uploadfiles' data={file => ({ bucket: 'makefriends', files: file })} onChange={this.UpLoadOnChange} multiple={false} accept='.pdf, .docx, .xlsx' defaultFileList={guildCooperationAgreementURLList}>
                <Button type='primary'>
                  <UploadOutlined /> 上传
                </Button>
              </Upload>
            </Form.Item>
            <Form.Item label='三方协议' name='threePartiesAgreementURL' rules={[{ required: true }]}>
              <Upload name='file' action='https://fts.yy.com/fs/uploadfiles' data={file => ({ bucket: 'makefriends', files: file })} onChange={this.UpLoadOnChange} multiple={false} accept='.pdf, .docx, .xlsx' defaultFileList={threePartiesAgreementURLList}>
                <Button type='primary'>
                  <UploadOutlined /> 上传
                </Button>
              </Upload>
            </Form.Item>
            <Form.Item label='附乙方公会主要管理人员' name='guildManagerURL' rules={[{ required: true }]}>
              <Upload name='file' action='https://fts.yy.com/fs/uploadfiles' data={file => ({ bucket: 'makefriends', files: file })} onChange={this.UpLoadOnChange} multiple={false} accept='.pdf, .docx, .xlsx' defaultFileList={guildManagerURLList}>
                <Button type='primary'>
                  <UploadOutlined /> 上传
                </Button>
              </Upload>
            </Form.Item>
          </Form>
          <div style={{ marginLeft: 100 }}>注：上传文件支持格式：word、excel、pdf</div>
        </Modal>

        {/* 查看详情并审批弹窗 */}
        <Modal footer={null} width={800} visible={visibleSuperCrystalDetail} title='申请超级水晶公会审批' onCancel={this.handleCancelDetail}>
          <div id='inner'>
            {/* 申请频道基本信息 */}
            <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px', fontWeight: '500' }}>申请频道基本信息</font></div>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>频道ID：{record?.asid || ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>申请时间：{moment.unix(record?.timestamp).format(dateFormat)}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公会名称：{record?.guildApply?.guildInfo?.name || ''}</font>
              </Col>
            </Row>
            <Divider />

            {/* 运营直播平台信息 */}
            <div><font style={{ marginLeft: 40, fontSize: '24px', fontWeight: '500' }}>运营直播平台信息</font></div>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>直播平台名称：{record?.guildApply?.platformInfo?.platformName || ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公会名称：{record?.guildApply?.platformInfo?.guildName || ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>签约主播数/人：{record?.guildApply?.platformInfo?.compereCount || ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公会ID：{record?.guildApply?.platformInfo?.guildId || ''}</font>
              </Col>
            </Row>
            {this.displayScreenshot()}
            <Divider />

            {/* 实名、资质及联系信息 */}
            <div><font style={{ marginLeft: 40, fontSize: '24px', fontWeight: '500' }}>实名、资质及联系信息</font></div>
            {/* <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>实名姓名：{record?.realName || ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>实名身份证号：{record?.realIdentity || ''}</font>
              </Col>
            </Row> */}
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              {/* <Col span={12}>
                <font style={{ fontSize: '16px' }}>实名手机号：{record?.realPhone || ''}</font>
              </Col> */}
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>法人真实姓名：{record?.guildApply?.qualificationInfo?.realName || ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>法人身份证号：{record?.guildApply?.qualificationInfo?.identity || ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>法人手机号：{record?.guildApply?.qualificationInfo?.phone || ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>联系人电话：{record?.guildApply?.guildInfo?.phone || ''}</font>
              </Col>
            </Row>

            {/* <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>姓名是否与实名一致：{yesNoMap3[record?.realNameStatus] === '否' ? this.noHtml() : '是' || ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>身份证号是否与实名一致：{yesNoMap3[record?.realIdentityStatus] === '否' ? this.noHtml() : '是' || ''}</font>
              </Col>
            </Row> */}
            {/* <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>手机号是否与实名一致：{yesNoMap3[record?.realPhoneStatus] === '否' ? this.noHtml() : '是' || ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>联系人电话：{record?.guildApply?.guildInfo?.phone || ''}</font>
              </Col>
            </Row> */}

            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>联系人邮箱：{record?.guildApply?.guildInfo?.email || ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>联系人地址：{record?.guildApply?.guildInfo?.address || ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公司全称：{record?.guildApply?.qualificationInfo?.companyName || ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>企业类型：{companyTypeMap[record?.guildApply?.qualificationInfo?.companyType] || ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>注册资金/元：{record?.guildApply?.qualificationInfo?.registerMoney || ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公司地址：{record?.guildApply?.qualificationInfo?.address || ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公司基本户开户银行：{record?.guildApply?.qualificationInfo?.bank || ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公司基本户账户：{record?.guildApply?.qualificationInfo?.account || ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <font style={{ marginLeft: 10, fontSize: '16px' }}>法人身份证正、反面：</font>
              <Col span={4}>
                <PopImage value={record?.guildApply?.qualificationInfo?.identityPicture?.[0] || ''} />
              </Col>
              <Col span={4}>
                <PopImage value={record?.guildApply?.qualificationInfo?.identityPicture?.[1] || ''} />
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <font style={{ marginLeft: 10, fontSize: '16px' }}>公司营业执照原件扫描件：</font>
              <Col span={12}>
                <PopImage value={record?.guildApply?.qualificationInfo?.companyPicture || ''} />
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <font style={{ marginLeft: 10, fontSize: '16px' }}>公司开户许可证原件扫描件：</font>
              <Col span={12}>
                <PopImage value={record?.guildApply?.qualificationInfo?.allowPicture || ''} />
              </Col>
            </Row>
            <Divider />

            {/* 企业认证 */}
            <div style={{ marginLeft: 42 }}><font color='red'>若审核不通过，可联系客服(钟淑玲)</font></div>
            <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px', fontWeight: '500' }}>企业认证</font></div>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>企业认证审核状态：{companyRealAuthMap[record?.companyRealAuthStatus] !== '审核通过' ? <font color='red'>{companyRealAuthMap[record?.companyRealAuthStatus]}</font> : companyRealAuthMap[record?.companyRealAuthStatus] || ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>审核描述：{record?.companyRealAuthDesc || ''}</font>
              </Col>
            </Row>
            <Divider />

            <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px', fontWeight: '500' }}>合同附件</font></div>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公会合作协议：</font><a href={record?.guildCooperationAgreementURL?.replace('http://', 'https://') || ''}>{record?.guildCooperationAgreementName || ''}</a>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>三方协议：</font><a href={record?.threePartiesAgreementURL?.replace('http://', 'https://') || ''}>{record?.threePartiesAgreementName || ''}</a>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>附乙方公会主要管理人员：</font><a href={record?.guildManagerURL?.replace('http://', 'https://') || ''}>{record?.guildManagerName || ''}</a>
              </Col>
            </Row>
          </div>
          <Divider />
          <Button disabled={record !== null && record !== undefined && (record.reviewStatus === 2 || record.reviewStatus === 3 || record.approvalStatus === 4 || record.approvalStatus === 5)} style={{ marginLeft: 230 }} type='primary' onClick={this.showApprovalPass()}>通过</Button>
          <Button disabled={record !== null && record !== undefined && (record.reviewStatus === 2 || record.reviewStatus === 3 || record.approvalStatus === 4 || record.approvalStatus === 5)} style={{ marginLeft: 20 }} type='primary' danger onClick={this.showApprovalReject()}>不通过</Button>
          <Button style={{ marginLeft: 20 }} type='default' onClick={this.cancelDetailHandle()}>取消</Button>
        </Modal>

        <Modal footer={null} visible={approvalRejectVisible} title='是否确认审批不通过？' onCancel={this.hiddenApprovalReject}>
          <span>失败原因快速选择</span>
          <Select style={{ marginLeft: 5, width: 230 }} placeholder='请选择' onChange={(v) => { this.faildDescHandler(v) }}>{ faildDescList.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
          <div style={{ marginTop: 10 }} />
          <span>手动输入原因</span>
          <Input placeholder='请输入' value={approvalRejectReason} onChange={e => this.setState({ approvalRejectReason: e.target.value })} style={{ width: 250, marginLeft: 10 }} />
          <div style={{ marginTop: 10 }}>注：原因选填，原因最多输入20字符。提交后，公会后台将提示审批不通过，并展示填写的原因。</div>
          <Divider />
          <Button style={{ marginTop: 10, marginLeft: 150 }} type='primary' onClick={this.approvalRejectHandle()}>确认</Button>
          <Button style={{ marginTop: 10, marginLeft: 30 }} type='default' onClick={this.hiddenApprovalReject2()}>取消</Button>
        </Modal>

        <Modal footer={null} visible={approvalPassVisible} title='是否确认审批通过？' onCancel={this.hiddenApprovalPass}>
          <span style={{ marginLeft: 24, marginTop: 20 }}>输入原因</span>
          <Input style={{ width: 230, marginLeft: 10 }} placeholder='请输入' onChange={e => this.setState({ approvalPassReason: e.target.value })} />
          <div style={{ marginTop: 10 }}>注：原因选填，原因最多输入20字符。</div>
          <Divider />
          <Button style={{ marginTop: 10, marginLeft: 150 }} type='primary' onClick={this.approvalPassHandle()}>确认</Button>
          <Button style={{ marginTop: 10, marginLeft: 30 }} type='default' onClick={this.hiddenApprovalPass2()}>取消</Button>
        </Modal>

        <Modal
          title='邀请公会入驻'
          width={400}
          forceRender
          destroyOnClose
          visible={inviteGuildModalVisible}
          onOk={this.inviteGuildSubmit}
          onCancel={() => this.setState({ inviteGuildModalVisible: false })}
        >
          <Form {...formItemLayout} ref={form => { this.formRefInviteGuild = form }} onFinish={this.onFinishInviteGuild}>
            <Form.Item label='公会sid' name='sid' rules={[{ required: true }]}>
              <Input width={200} placeholder='输入公会sid' onChange={e => this.inputInviteGuildSid(e.target.value)} />
            </Form.Item>
            <Form.Item label='OWYY号' name='inviteGuildModalSid'>
              <Input disabled width={200} />
            </Form.Item>
            <Form.Item label='OW昵称' name='inviteGuildModalNickname'>
              <Input disabled width={200} />
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default newGuildSettleInv2SuperCrysta
