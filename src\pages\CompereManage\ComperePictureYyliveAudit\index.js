import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Form, Card, Button, Tabs, Divider, Select, Input, message, DatePicker } from 'antd'
import PopImage from '@/components/PopImage'
import { connect } from 'dva'
const namespace = 'ComperePictureYyliveAudit'
const TabPane = Tabs.TabPane
var moment = require('moment')
const pass = 1
const notPass = 2
const { RangePicker } = DatePicker
const Option = Select.Option
const dateFormat = 'YYYY-MM-DD'
const waitingAudit = '0'
const audited = '1'

@connect(({ ComperePictureYyliveAudit }) => ({
  model: ComperePictureYyliveAudit
}))

// 测试环境审核 https://portal-nraq.yy.com/interfaceTest?mode=IMG&id=2400&tab=2

class Index extends Component {
  renderTodoContent = (value, record, index) => {
    if (record === undefined) {
      return ''
    }
    // if (record.uid !== undefined && record.uid === 50042292) {
    //   record.sid = 123
    //   record.ssid = 456
    //   record.source = 1
    //   record.size = 1
    // }
    let hiddenSID = true
    let target = '主播(uid)'
    if (record.sid !== undefined && record.sid > 0 && record.ssid > 0) {
      hiddenSID = false
      target = '厅(sid/ssid)'
    }
    let source = '上传'
    if (record.source !== undefined && record.source > 0) {
      source = '配置'
    }
    let picSize = '4:5'
    if (record.type !== undefined && record.type === 1) {
      picSize = '1:1'
    }
    return (
      <div>
        {
          record.id !== undefined ? <div>
            <PopImage value={record.cover} />
            <div><font>时间：{moment.unix(record.uploadTime).format('YYYY-MM-DD HH:mm:ss')}</font></div>
            <div hidden={!hiddenSID}>><font>昵称：{record.nick}</font></div>
            <div hidden={!hiddenSID}><font>uid: {record.uid}</font></div>
            <div hidden={hiddenSID}><font>sid: {record.sid}</font></div>
            <div hidden={hiddenSID}><font>ssid: {record.ssid}</font></div>
            <div><font color={'blue'}>来源：{target}{source} </font></div>
            <div><font>尺寸：{picSize} </font></div>
            <div>
              <Button type='primary' size='small' danger onClick={this.auditStatus(record.id, notPass)}>
                不通过
              </Button>
              <Divider type='vertical' />
              <Button type='primary' size='small' onClick={this.auditStatus(record.id, pass)}>
                通过
              </Button>
            </div>
          </div> : ''
        }
      </div>
    )
  }
  renderAuditContent = (value, record, index) => {
    if (record === undefined) {
      return ''
    }
    // if (record.uid !== undefined && record.uid === 50042292) {
    //   record.sid = 123
    //   record.ssid = 456
    //   record.source = 1
    //   record.size = 1
    // }
    let hiddenSID = true
    let target = '主播(uid)'
    if (record.sid !== undefined && record.sid > 0 && record.ssid > 0) {
      hiddenSID = false
      target = '厅(sid/ssid)'
    }
    let source = '上传'
    if (record.source !== undefined && record.source > 0) {
      source = '配置'
    }
    let picSize = '4:5'
    if (record.type !== undefined && record.type === 1) {
      picSize = '1:1'
    }
    return (
      <div>
        {
          record.id !== undefined ? <div>
            <PopImage value={record.cover} />
            <div><font>时间：{moment.unix(record.uploadTime).format('YYYY-MM-DD HH:mm:ss')}</font></div>
            <div hidden={!hiddenSID}>><font>昵称：{record.nick}</font></div>
            <div hidden={!hiddenSID}><font>uid: {record.uid}</font></div>
            <div hidden={hiddenSID}><font>sid: {record.sid}</font></div>
            <div hidden={hiddenSID}><font>ssid: {record.ssid}</font></div>
            <div><font color={'blue'}>来源：{target}{source} </font></div>
            <div><font>尺寸：{picSize} </font></div>
            <div>
              { record.status === pass ? <span><font>状态：</font><font color='green'>通过</font></span> : <font color='red'>不通过</font> }
            </div>
            <br />
            <div><font>操作人：{record.operator}</font></div>
            <div><font>操作时间：{moment.unix(record.auditTime).format('YYYY-MM-DD HH:mm:ss')}</font></div>
          </div> : ''
        }
      </div>
    )
  }
  // column structs.
  columns = [
    { title: '列1', align: 'center', width: 120, render: (text, record, index) => this.renderTodoContent(text, record[0], index) },
    { title: '列2', align: 'center', width: 120, render: (text, record, index) => this.renderTodoContent(text, record[1], index) },
    { title: '列3', align: 'center', width: 120, render: (text, record, index) => this.renderTodoContent(text, record[2], index) },
    { title: '列4', align: 'center', width: 120, render: (text, record, index) => this.renderTodoContent(text, record[3], index) },
    { title: '列5', align: 'center', width: 120, render: (text, record, index) => this.renderTodoContent(text, record[4], index) }
  ]

  auditColumns = [
    { title: '列1', align: 'center', width: 120, render: (text, record, index) => this.renderAuditContent(text, record[0], index) },
    { title: '列2', align: 'center', width: 120, render: (text, record, index) => this.renderAuditContent(text, record[1], index) },
    { title: '列3', align: 'center', width: 120, render: (text, record, index) => this.renderAuditContent(text, record[2], index) },
    { title: '列4', align: 'center', width: 120, render: (text, record, index) => this.renderAuditContent(text, record[3], index) },
    { title: '列5', align: 'center', width: 120, render: (text, record, index) => this.renderAuditContent(text, record[4], index) }
  ]

  defaultPageValue = {
    defaultPageSize: 10,
    pageSizeOptions: ['10', '100', '500', '2000'],
    showSizeChanger: true,
    isUpdate: false,
    onChange: (page, size) => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = {
    visible: false,
    confirmVisible: false,
    deleteConfirmMsg: '',
    selectedRowKeys: [],
    dateRange: [],
    auditedPageDateRange: [],
    // dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')],
    // auditedPageDateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')],
    value: {}
  }

  auditStatus = (id, status) => () => {
    const { dispatch } = this.props
    const data = { ids: id, status: status }
    const { dateRange, uid } = this.state

    var params = { uid: uid }
    if (dateRange && dateRange.length === 2) {
      params.start = moment(dateRange[0]).format(dateFormat)
      params.end = moment(dateRange[1]).format(dateFormat)
    }
    dispatch({
      type: `${namespace}/updateAuditStatus`,
      payload: data,
      getListParam: params
    })
  }

  batchUpdateAuditStatus = (status) => () => {
    const { dispatch } = this.props
    const data = { ids: this.state.ids, status: status }
    const { dateRange, uid } = this.state
    if (!data.ids || data.ids.length === 0) {
      message.error(`您还未选中图片`)
      return
    }
    var params = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat), uid }
    dispatch({
      type: `${namespace}/updateAuditStatus`,
      payload: data,
      getListParam: params
    })
  }

  buildSearchParams = (tab) => {
    const { dateRange, auditedPageDateRange, uid, auditedPageUid, status } = this.state
    console.log(dateRange)
    console.log(auditedPageDateRange)
    var data = { tab: tab }
    if (tab === audited) {
      data.status = status
      data.uid = auditedPageUid
      if (auditedPageDateRange && auditedPageDateRange.length === 2) {
        data.start = moment(auditedPageDateRange[0]).format(dateFormat)
        data.end = moment(auditedPageDateRange[1]).format(dateFormat)
      }
    } else {
      data.uid = uid
      if (dateRange && dateRange.length === 2) {
        data.start = moment(dateRange[0]).format(dateFormat)
        data.end = moment(dateRange[1]).format(dateFormat)
      }
    }
    return data
  }

  search = (tab) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`,
      payload: this.buildSearchParams(tab)
    })
  }

  handleSearch = (tab) => () => {
    this.search(tab)
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`
    })
  }
  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onSelectChange = (selectedRowKeys, selectedRows) => {
    var ids = ''
    for (let i = 0; i < selectedRows.length; ++i) {
      ids += selectedRows[i].map(item => item.id).join(',')
      if (i < selectedRows.length - 1) {
        ids += ','
      }
    }
    console.log(ids)
    this.setState({ selectedRowKeys })
    this.setState({ ids: ids })
  }

  onChange = (date, format) => {
    this.setState({ dateRange: date })
  }

  auditedPageOnChange = (date, format) => {
    this.setState({ auditedPageDateRange: date })
  }

  onAuditStatusChangeTab = tab => {
    this.search(tab)
  }

  handleExport = (tab) => {
    var data = this.buildSearchParams(tab)
    var params = Object.keys(data).map(function (key) {
      if (!data[key]) {
        data[key] = 0
      }
      return encodeURIComponent(key) + '=' + encodeURIComponent(data[key])
    }).join('&')
    return '/yylive_compere_picture/export_list?' + params
  }

  // content
  render () {
    const { route, model: { list } } = this.props
    const { dateRange, selectedRowKeys } = this.state
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card size={'small'}>
          <p style={{ color: 'red', marginBlock: 0 }}>说明：包含用户hgame页面上传4:5上传图、4:5和1:1配置图质审。流程：用户上传或运营配置=>安全审核(通过)=>质审(通过)=>app推荐图,可在"主播推荐信息库"查看状态</p>
        </Card>
        <Card>
          <Tabs defaultActiveKey={waitingAudit} onChange={this.onAuditStatusChangeTab}>
            <TabPane tab='待审核' key={waitingAudit}>
              <Form>
                时间范围
                <Divider type='vertical' /> {/* 分割线 */}
                <RangePicker defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
                <Divider type='vertical' /> {/* 分割线 */}
                <Input placeholder='搜索 uid' onChange={e => this.setState({ uid: e.target.value })} style={{ width: 100 }} /> {/* 搜索按钮 */}
                <Divider type='vertical' /> {/* 分割线 */}
                <Button type='primary' onClick={this.handleSearch(waitingAudit)}>搜索</Button>
                <Divider type='vertical' /> {/* 分割线 */}
                <Button type='primary' href={this.handleExport(waitingAudit)}>导出</Button>
                <br />
                <br />
                <Divider type='vertical' />
                <Button type='primary' danger onClick={this.batchUpdateAuditStatus(notPass)}>一键不通过</Button>
                <Divider type='vertical' /> {/* 分割线 */}
                <Button type='primary' onClick={this.batchUpdateAuditStatus(pass)}>一键通过</Button>
                <Table rowKey={(record, index) => index} rowSelection={rowSelection} dataSource={list} columns={this.columns} pagination={this.defaultPageValue} bordered />
              </Form>
            </TabPane>
            <TabPane tab='已审核' key={audited}>
              <Form>
                时间范围
                <Divider type='vertical' /> {/* 分割线 */}
                <RangePicker defaultValue={dateRange} format={dateFormat} onChange={this.auditedPageOnChange} />
                <Divider type='vertical' /> {/* 分割线 */}
                <Input placeholder='搜索 uid' onChange={e => this.setState({ auditedPageUid: e.target.value })} style={{ width: 100 }} /> {/* 搜索按钮 */}
                <Divider type='vertical' /> {/* 分割线 */}
                <Select labelInValue defaultValue={{ key: 0 }} style={{ width: 120 }} onChange={(v) => this.setState({ status: v.key })}>
                  <Option value={0}>全部</Option>
                  <Option value={pass}>通过</Option>
                  <Option value={notPass}>不通过</Option>
                </Select>
                <Button type='primary' onClick={this.handleSearch(audited)}>搜索</Button>
                <Divider type='vertical' /> {/* 分割线 */}
                <Button type='primary' href={this.handleExport(audited)}>导出</Button>
                <Table rowKey={(record, index) => index} dataSource={list} columns={this.auditColumns} pagination={this.defaultPageValue} bordered />
              </Form>
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default Index
