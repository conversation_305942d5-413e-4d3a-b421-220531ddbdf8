import request from '@/utils/request'
import { stringify } from 'qs'

export function getOneConfigLists (params) {
  return request(`/HatkingOneChannelPolicy/GetLists?${stringify(params)}`)
}

export function addOneConfigInfo (params) {
  return request(`/HatkingOneChannelPolicy/AddItem?${stringify(params)}`)
}

export function updateOneConfigInfo (params) {
  return request(`/HatkingOneChannelPolicy/UpdateItem?${stringify(params)}`)
}

export function deleteOneConfigInfo (params) {
  return request(`/HatkingOneChannelPolicy/DeleteItem?${stringify(params)}`)
}

export function getAllConfigLists (params) {
  return request(`/HatkingAllChannelPolicy/GetLists?${stringify(params)}`)
}

export function updateAllConfigInfo (params) {
  return request(`/HatkingAllChannelPolicy/UpdateItem?${stringify(params)}`)
}

export function getAllConfirmLists (params) {
  return request(`/HatkingAllChannelPolicyConfirm/GetLists?${stringify(params)}`)
}

export function getSinglePlayerLists (params) {
  return request(`/HatkingSinglePlayerPolicy/GetLists?${stringify(params)}`)
}

export function updateSinglePlayerInfo (params) {
  return request(`/HatkingSinglePlayerPolicy/UpdateItem?${stringify(params)}`)
}
