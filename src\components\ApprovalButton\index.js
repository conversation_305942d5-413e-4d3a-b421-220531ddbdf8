/* eslint-disable eqeqeq */
import { Button, Input, Space, Modal, message, Row } from 'antd'
import React, { Component } from 'react'
import { simpleRequire2, getCookie, simpleRequireJson } from '@/utils/common'

/* 公用组件-审批按钮
功能说明：执行审批公用组件，提供审批人、审批状态等检验， 并支持自定义组件的样式。

参数说明：
cbFunc: ()=>{}, 审批成功后调用, 若不传则刷新页面

使用示例：
  <ApprovalButton aprId={aprId} cbFunc={()=>{...}} >
       <Button hidden={editing}>审批</Button>
  </ApprovalButton>
*/

class ApprovalButton extends Component {
  state = {
    modalVisible: false,
    remark: '',
    approvalInfo: {}
  }

  componentDidUpdate () {}
  componentDidMount () {}

  // 检查是审批状态以及审批人
  onCheck = () => {
    const { aprId } = this.props
    if (!aprId) {
      message.warning('未创建审批流')
      return
    }
    getAprStatus(aprId, (resp) => {
      if (!Array.isArray(resp)) {
        message.warn('当前非待审批状态~')
        return
      }
      let info = resp[0]
      if (info == undefined || info.rule == undefined || info.rule.approvals == undefined) {
        message.warn('审批流信息异常，请稍后再试..')
        return
      }
      if (info.progress !== 0 && info.progress !== 1) {
        message.warn('审批流信息异常，请稍后再试..')
        return
      }
      const approvals = info.progress === 0 ? info.rule.approvals : info.rule.secondary
      if (!approvals) {
        message.warn(`审批流配置异常(未配置${info.progress + 1}级审批人)，请联系管理员...`)
        return
      }
      let uid = getCookie('yyuid')
      for (let i = 0; i < approvals.length; i++) {
        if (approvals[i].uid == uid) {
          this.setState({ modalVisible: true, approvalInfo: info })
          return
        }
      }
      message.warn('非指定审批人')
    })
  }
  // 提交审批
  onDoApproval = (pass) => {
    const { approvalInfo, reason } = this.state
    const { cbFunc } = this.props
    if (approvalInfo == undefined || approvalInfo.rule == undefined || approvalInfo.rule.approvals == undefined) {
      message.warn('审批流信息异常，请稍后再试..')
      return
    }
    approvalInfo.reason = reason || '空'
    if (pass) {
      approvalInfo.result = 'Passed'
    } else {
      approvalInfo.result = 'Rejected'
    }
    doApproval(approvalInfo, (ok) => {
      if (ok) {
        message.success('审批已提交')
        if (cbFunc) {
          cbFunc()
        } else {
          window.location.reload()
        }
      } else {
        message.error('审批提交失败,请联系管理员~')
      }
      this.setState({ modalVisible: false })
    })
  }

  render () {
    const material = this.props.children || <Button>审批</Button>
    return (
      <>
        <span onClick={() => this.onCheck()}>
          {material}
        </span>
        <Modal title='审批确认' visible={this.state.modalVisible} forceRender
          onCancel={() => this.setState({ modalVisible: false })} footer={null}>
          <Input placeholder='请输入审批备注或理由' style={{ marginBottom: '1em' }}
            onChange={(e) => { this.setState({ reason: e.target.value }) }} />
          <Row>
            <Space>
              <Button type='primary' onClick={e => this.onDoApproval(true)}>通过</Button>
              <Button danger onClick={e => this.onDoApproval(false)}>驳回</Button>
            </Space>
          </Row>
        </Modal>
      </>
    )
  }
}

export default ApprovalButton

async function getAprStatus (aprId, cbFunc) {
  let params = { pid: aprId }
  simpleRequire2(`/approval/admin/get_to_approval_byPID`, params, false).then(resp => {
    cbFunc(resp)
  })
}

async function doApproval (params, cbFunc) {
  simpleRequireJson(`/approval/admin/do_approval`, params, false).then(resp => {
    cbFunc(resp)
  })
}
