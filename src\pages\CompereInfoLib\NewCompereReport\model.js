import { message } from 'antd'
import * as api from './api'

export default {
  namespace: 'newCompereReport',

  state: {
    list: []
  },

  reducers: {
    displayList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * listSettlementReport ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listSettlementReport, payload)
      if (status !== 0) {
        message.warning(msg)
      }
      console.log(data)
      let dataNew = Array.isArray(data) ? data : []
      for (let i = 0; i < dataNew.length; i++) {
        dataNew[i].idx = i + 1
      }
      console.log(dataNew)
      yield put({
        type: 'displayList',
        payload: dataNew
      })
    },

    * deleteSettlementReport ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.deleteSettlementReport, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: '删除失败' })
      } else {
        message.success('删除成功')
      }
      yield put({
        type: 'listSettlementReport',
        payload: { business: payload.business }
      })
    }
  }
}
