import React, { Component } from 'react'
import { DatePicker } from 'antd'
import moment from 'moment'
import { DeleteOutlined } from '@ant-design/icons'

class DynamicDate extends Component {
  constructor (props) {
    super(props)

    this.state = {
      list: props.value || [],
      item: null
    }
  }

  componentWillReceiveProps (nextProps) {
    // 受控组件， 用于更新
    const { value } = nextProps
    this.setState({
      list: value || [],
      item: null
    })
  }

  handleOnChange = (value) => {
    // console.log(value, value.unix())
    this.setState({ item: value })
  }

  handleChange = (index) => () => {
    var { list, item } = this.state
    if (index !== undefined) {
      Array.splice(list, index, 1)
    } else {
      if (!item) {
        return
      }
      Array.push(list, item.unix())
    }

    if (this.props.onChange) {
      this.props.onChange(list) // update getFiledDecorator
    }

    // console.log(list)
    this.setState({ list: list, item: null })
  }

  render () {
    const { list, item } = this.state
    return (
      <div className='clearfix'>
        {list.map((i, index) => {
          return (
            <div>
              <span>{moment.unix(i).format('YYYY-MM-DD HH:mm:ss')}</span>
              <span style={{ marginLeft: 10 }}><a onClick={this.handleChange(index)}><DeleteOutlined style={{ color: 'red' }} type='delete' /></a></span>
            </div>
          )
        })}
        <div>
          <DatePicker value={item} defaultValue={null} allowClear showTime showToday onChange={this.handleOnChange} onOk={this.handleChange()} />
        </div>
      </div>
    )
  }
}

export default DynamicDate
