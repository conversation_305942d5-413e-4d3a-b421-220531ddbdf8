import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import DSComboInfoComponent from '../GameComponents/hatking_combo_info'
import DSCrucialInfoComponent from '../GameComponents/hatking_crucial_info'
import DSDailyInfoComponent from '../GameComponents/hatking_daily_info'
import DSDailyModeStatsComponent from '../GameComponents/hatking_daily_mode_stats'
import DSUpgradeCrucialInfoComponent from '../GameComponents/hatking_upgrade_crucial_info'

const namespace = 'dragonSlaying' // model 的 namespace
const TabPane = Tabs.TabPane

@connect(({ dragonSlaying }) => ({ // model 的 namespace
  model: dragonSlaying // model 的 namespace
}))
class DragonSlayingIndex extends Component { // 默认页面组件，不需要修改
  tabOnChange = type => activityKey => {
    console.log(type, activityKey)
    if (type !== undefined || type != null) {
      activityKey = type
    }
  }

  /** *******************************页面布局*************************************************************/
  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs onChange={this.tabOnChange()} type='card'>
          <TabPane tab='关键信息' key='1'>
            <DSCrucialInfoComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='每日玩法信息' key='2'>
            <DSDailyInfoComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='连中信息' key='3'>
            <DSComboInfoComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='总奖池返奖监控' key='4'>
            <DSDailyModeStatsComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='高倍场返奖监控' key='5'>
            <DSUpgradeCrucialInfoComponent modelName={namespace} model={this.props.model} />
          </TabPane>
        </Tabs>

      </PageHeaderWrapper>
    )
  }
}

export default DragonSlayingIndex // 保证唯一
