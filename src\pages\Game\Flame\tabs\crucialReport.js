import React, { Component } from 'react'
import { Table, Divider, But<PERSON>, Card, Form, DatePicker, message } from 'antd'
import { connect } from 'dva'
import exportExcel from '@/utils/exportExcel'
import formatNumberWithThousandsSeparators from '../common'

var moment = require('moment')
const { RangePicker } = DatePicker
const namespace = 'flameGame'
const dateFormat = 'YYYY-MM-DD'

@connect(({ flameGame }) => ({ // model 的 namespace
  model: flameGame // model 的 namespace
}))
class FlameCrucialReport extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      crucialList: [],
      dateRange: [moment().subtract(1, 'days'), moment().add(1, 'days')]
    }
  }

    componentDidMount = () => {
      this.getCrucialList()
    }
    // 调用 model 处理函数
    callModel = (funcName, params) => {
      const { dispatch } = this.props
      dispatch({
        type: `${namespace}/${funcName}`,
        payload: params
      })
    }

    // 获取关键日报
    getCrucialList = () => {
      const { dateRange } = this.state
      this.callModel('getCrucialReport', {
        params: { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) },
        isRawRespMode: true,
        cbFunc: (ret) => {
          const { status, msg, list } = ret
          if (status < 0) {
            message.warn('获取关键日报: ' + msg)
            return
          }

          list.sort((a, b) => b.date - a.date)
          this.setState({ crucialList: Array.isArray(list) ? list : [] })
        }
      })
    }
 
    columns = [
      { title: '日期', dataIndex: 'date', align: 'center' },
      { title: '升级总模拟流水', dataIndex: 'upgradeAmethyst', align: 'center', render: (text) => (formatNumberWithThousandsSeparators(text)) },
      { title: '成功总模拟流水', dataIndex: 'successAmethyst', align: 'center', render: (text) => (formatNumberWithThousandsSeparators(text)) },
      { title: '失败总模拟流水', dataIndex: 'failureAmethyst', align: 'center', render: (text) => (formatNumberWithThousandsSeparators(text)) },
      { title: '失败损耗度', dataIndex: 'failureDurability', align: 'center', render: (text) => (formatNumberWithThousandsSeparators(text)) },
      { title: '升级总用户数', dataIndex: 'upgradeUserNum', align: 'center' },
      { title: '成功总用户数', dataIndex: 'successUserNum', align: 'center' },
      { title: '失败总用户数', dataIndex: 'failureUserNum', align: 'center' }
    ]

    pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

    onClick = () => {
      this.getCrucialList()
    }

    onChange = (date, format) => {
      this.setState({ dateRange: date })
    }

    onExport = () => {
      let headers = []
      this.columns.forEach(function (item) {
        headers.push({ key: item.dataIndex, header: item.title })
      })

      const { crucialList } = this.state
      var exportData = crucialList.map(item => {
        let v = $.extend(true, {}, item)
        v.upgradeAmethyst = formatNumberWithThousandsSeparators(v.upgradeAmethyst)
        v.successAmethyst = formatNumberWithThousandsSeparators(v.successAmethyst)
        v.failureAmethyst = formatNumberWithThousandsSeparators(v.failureAmethyst)
        v.failureDurability = formatNumberWithThousandsSeparators(v.failureDurability)
        return v
      })

      exportExcel(headers, exportData)
    }

    /* *******************************页面布局***************************************************************/
    render () {
      const { dateRange, crucialList } = this.state

      return (
        <Card>
          <Form>
            时间范围:
            <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} onChange={this.onChange} />
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onClick}>查询</Button>
            <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
            <font style={{ marginLeft: 5 }} color='red'>流水单位-紫水晶(仅作模拟测算参考)</font>
            <Divider />
            <Table dataSource={crucialList} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
          </Form>
        </Card>
      )
    }
}

export default FlameCrucialReport
