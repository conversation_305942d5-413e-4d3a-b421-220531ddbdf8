import React, { Component } from 'react'
import { connect } from 'dva'
import MicroCover from './components/micro_cover'

@connect(({ microCover }) => ({ // model 的 namespace
  model: microCover // model 的 namespace
}))
class Index extends Component { // 默认页面组件，不需要修改
  /** *******************************页面布局*************************************************************/
  render () {
    const { model: { list } } = this.props

    return (
      <MicroCover configList={list} />
    )
  }
}

export default Index // 保证唯一
