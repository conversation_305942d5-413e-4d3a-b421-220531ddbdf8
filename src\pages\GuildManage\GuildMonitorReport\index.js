import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import {
  Table,
  Button,
  DatePicker,
  InputNumber, Tooltip, Select, Image
} from 'antd'
import { connect } from 'dva'
import moment from 'moment/moment'
import './style.css'
import { stringify } from 'qs'

const { RangePicker } = DatePicker

const namespace = 'guildMonitorReport'
const getReportDataListUri = `${namespace}/getReportDataList`

const dateFormat = 'YYYY-MM-DD'
const dateFormatMonth = 'YYYY-MM'

const orderRule = {
  'ascend': 1,
  'descend': -1
}

const tabTypeDaily = 1 // 按天
const tabTypeMonth = 2 // 按月

const defaultTimeRange = [moment().subtract(0, 'days'), moment().subtract(0, 'days')]
const defaultTimeRangeMonth = [moment().subtract(0, 'months'), moment().subtract(0, 'months')]

@connect(({ guildMonitorReport }) => ({
  model: guildMonitorReport
}))

class GuildMonitorReport extends Component {
  constructor (props) {
    super(props)
    this.tabType = tabTypeDaily
    this.refreshInfo()
  }

  state = {
    tabType: tabTypeDaily,
    searchRangeTime: defaultTimeRange,
    rangeTime: defaultTimeRange
  }

  columnsSort = [
    { title: '礼物总流水', dataIndex: 'TotalAmount', sorter: true, align: 'center' },
    { title: 'DAU', dataIndex: 'DAU', sorter: true, align: 'center' },
    { title: '有效经营厅', dataIndex: 'HighFlowHallsCount', sorter: true, align: 'center' },
    { title: '消费用户数', dataIndex: 'ConsumptionUsersCount', sorter: true, align: 'center' },
    { title: '公会活跃主持', dataIndex: 'ActiveCompereCount', sorter: true, align: 'center' },
    { title: '公会活跃嘉宾', dataIndex: 'ActiveGuestsCount', sorter: true, align: 'center' },
    { title: '公会盖章流水', dataIndex: 'SealFlow', sorter: true, align: 'center' },
    { title: '公会布料流水', dataIndex: 'FabricFlow', sorter: true, align: 'center' },
    { title: '包裹礼物流水', dataIndex: 'PackageGiftFlow', sorter: true, align: 'center' },
    { title: '即抽即送礼物流水', dataIndex: 'InstantGiftFlow', sorter: true, align: 'center' },
    { title: '弹幕礼物流水', dataIndex: 'DanmuGiftFlow', sorter: true, align: 'center' }
  ]

  columns = [
    { title: '-',
      width: 40,
      dataIndex: 'content',
      render: (text, record) => {
        return this.tabType === tabTypeDaily && (moment(this.state.rangeTime[0], dateFormat).format(dateFormat) === moment(this.state.rangeTime[1], dateFormat).format(dateFormat))
          ? this.dailyHTML(record) : this.monthHTML(record)
      } }
  ].map(item => {
    item.align = 'left'
    item.ellipsis = true
    return item
  })

  dailyHTML = (record) => {
    return <span className={'guildContent'}>
      <div className={'guildContent-wrap'}>
        <div className={'guildContent-card'}>
          <div className={'guildContent-card-pic'}>
            <div><Image width={100} height={100} marginLeft={1} src={record.guildAvatar.length > 0 ? record.guildAvatar : null} /></div>
            <div className={'guildContent-card-level'}>等级:<strong>&nbsp;{record.guildLevel}</strong></div>
          </div>
          <div className={'guildContent-card-info'}>
            <div className={'guildContent-card-guildTag-div'}>
              <Image src={record.guildTag} className={'guildContent-card-guildTag'} />
              <div className={'guildContent-card-guildName'}>
                <Tooltip title={record.guildName}>{record.guildName.length > 20 ? record.guildName.substring(0, 20) + '...' : record.guildName}</Tooltip>
              </div>
            </div>
            <div className={'guildContent-card-guildinfo'}>
              <div><font>SID:</font>&nbsp;&nbsp;{record.asid}</div>
              <div><font>签约主持数:</font>&nbsp;&nbsp;{record.signedCompereCount}</div>
              <div>
                <div><font>有效经营厅:</font>&nbsp;&nbsp;{record.highFlowHallsCount}</div>
                <div><font>(房管厅:</font>&nbsp;{record.highFlowHallsCountRoomMgr}&nbsp;<font>|</font>&nbsp;<font>多人厅:</font>&nbsp;{record.highFlowHallsCountMulti}<font>)</font></div>
              </div>
            </div>
          </div>
        </div>
        <div className={'guildContent-card'}>
          <div>
            <div><font>DAU:</font>&nbsp;&nbsp;{record.dau.toLocaleString()}</div>
            <div><font>(房管厅:</font>&nbsp;{record.dauRoomMgr}&nbsp;<font>|多人厅:</font>&nbsp;{record.dauMultiPerson}<font>)</font></div>
          </div>
          <div><font>消费用户数:</font>&nbsp;&nbsp;{record.consumptionUsersCount}</div>
        </div>
        <div className={'guildContent-card'}>
          <div><font>公会活跃主持:</font>&nbsp;&nbsp;{record.activeCompereCount}</div>
          <div><font>公会活跃嘉宾:</font>&nbsp;&nbsp;{record.activeGuestsCount}</div>
        </div>
        <div className={'guildContent-card'}>
          <div><font>礼物总流水/元:</font>&nbsp;&nbsp;{record.totalAmount.toLocaleString()}</div>
          <div><font>公会盖章流水/元:</font>&nbsp;&nbsp;{record.sealFlow.toLocaleString()}</div>
          <div><font>公会布料流水/元:</font>&nbsp;&nbsp;{record.fabricFlow.toLocaleString()}</div>
        </div>
        <div className={'guildContent-card'}>
          <div><font>包裹礼物流水/元(占比):</font>&nbsp;&nbsp;{record.packageGiftFlow.toLocaleString()}&nbsp;|&nbsp;{record.packageGiftRatio.toFixed(2)}%</div>
          <div><font>即抽即送礼物流水/元(占比):</font>&nbsp;&nbsp;{record.instantGiftFlow.toLocaleString()}&nbsp;|&nbsp;{record.instantGiftRatio.toFixed(2)}%</div>
          <div><font>弹幕礼物流水/元(占比):</font>&nbsp;&nbsp;{record.danmuGiftFlow.toLocaleString()}&nbsp;|&nbsp;{record.danmuGiftRatio.toFixed(2)}%</div>
        </div>
      </div>
    </span>
  }

  monthHTML = (record) => {
    return <span className={'guildContent'}>
      <div className={'guildContent-wrap'}>
        <div className={'guildContent-card'}>
          <div className={'guildContent-card-pic'}>
            <div><Image width={100} height={100} marginLeft={1} src={record.guildAvatar.length > 0 ? record.guildAvatar : null} /></div>
            <div className={'guildContent-card-level'}>等级:<strong>&nbsp;{record.guildLevel}</strong></div>
          </div>
          <div className={'guildContent-card-info'}>
            <div className={'guildContent-card-guildTag-div'}>
              <Image src={record.guildTag} className={'guildContent-card-guildTag'} />
              <div className={'guildContent-card-guildName'}>
                <Tooltip title={record.guildName}>{record.guildName.length > 20 ? record.guildName.substring(0, 20) + '...' : record.guildName}</Tooltip>
              </div>
            </div>
            <div className={'guildContent-card-guildinfo'}>
              <div><font>SID:</font>&nbsp;&nbsp;{record.asid}</div>
              <div><font>签约主持数:</font>&nbsp;&nbsp;{record.signedCompereCount}</div>
              <div>
                <div><font>日均有效经营厅:</font>&nbsp;&nbsp;{record.highFlowHallsCount}</div>
                <div><font>(房管厅:</font>&nbsp;{record.highFlowHallsCountRoomMgr}&nbsp;<font>|</font>&nbsp;<font>多人厅:</font>&nbsp;{record.highFlowHallsCountMulti}<font>)</font></div>
              </div>
            </div>
          </div>
        </div>
        <div className={'guildContent-card'}>
          <div>
            <div><font>日均DAU:</font>&nbsp;&nbsp;{record.dau.toLocaleString()}</div>
            <div><font>(房管厅:</font>&nbsp;{record.dauRoomMgr}&nbsp;<font>|多人厅:</font>&nbsp;{record.dauMultiPerson}<font>)</font></div>
          </div>
          <div><font>日均消费用户数:</font>&nbsp;&nbsp;{record.consumptionUsersCount}</div>
        </div>
        <div className={'guildContent-card'}>
          <div><font>日均活跃主持:</font>&nbsp;&nbsp;{record.activeCompereCount}</div>
          <div><font>日均活跃嘉宾:</font>&nbsp;&nbsp;{record.activeGuestsCount}</div>
        </div>
        <div className={'guildContent-card'}>
          <div><font>礼物总流水/元:</font>&nbsp;&nbsp;{record.totalAmount.toLocaleString()}</div>
          <div><font>公会盖章流水/元:</font>&nbsp;&nbsp;{record.sealFlow.toLocaleString()}</div>
          <div><font>公会布料流水/元:</font>&nbsp;&nbsp;{record.fabricFlow.toLocaleString()}</div>
        </div>
        <div className={'guildContent-card'}>
          <div><font>包裹礼物流水/元(占比):</font>&nbsp;&nbsp;{record.packageGiftFlow.toLocaleString()}&nbsp;|&nbsp;{record.packageGiftRatio.toFixed(2)}%</div>
          <div><font>即抽即送礼物流水/元(占比):</font>&nbsp;&nbsp;{record.instantGiftFlow.toLocaleString()}&nbsp;|&nbsp;{record.instantGiftRatio.toFixed(2)}%</div>
          <div><font>弹幕礼物流水/元(占比):</font>&nbsp;&nbsp;{record.danmuGiftFlow.toLocaleString()}&nbsp;|&nbsp;{record.danmuGiftRatio.toFixed(2)}%</div>
        </div>
      </div>
    </span>
  }

  pageValue = () => {
    return {
      defaultPageSize: 10,
      showSizeChanger: true,
      pageSizeOptions: ['10', '20', '30', '50', '100', '500', '1000'],
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    }
  }

  // --------------------------------------------------------------------------------//
  buildParam = () => {
    const { tabType, searchSID, searchRangeTime, sorter } = this.state

    let param = {}
    if (tabType && tabType > 0) {
      param['tabType'] = tabType
    }
    if (searchSID && searchSID > 0) {
      param['asid'] = searchSID
    }
    console.log(searchRangeTime)
    if (searchRangeTime && tabType === tabTypeDaily) {
      param['startDate'] = moment(searchRangeTime[0], dateFormat).format('YYYYMMDD')
      param['endDate'] = moment(searchRangeTime[1], dateFormat).format('YYYYMMDD')
    }
    if (searchRangeTime && tabType === tabTypeMonth) {
      param['startDate'] = moment(searchRangeTime[0], dateFormatMonth).format('YYYYMM')
      param['endDate'] = moment(searchRangeTime[1], dateFormatMonth).format('YYYYMM')
    }
    if (sorter) {
      param['orderTag'] = sorter.field
      param['orderRule'] = orderRule[sorter.order]
    }
    console.log(param)
    return param
  }

  refreshInfo = () => {
    let param = this.buildParam()
    this.props.dispatch({
      type: getReportDataListUri,
      payload: param
    })
  }

  sortHandler = (pagination, filters, sorter) => {
    console.log(sorter)
    if (sorter && sorter.field && sorter.order) {
      this.setState({ sorter: sorter })
    } else this.setState({ sorter: null })
  }

  searchHandler = () => () => {
    const { searchRangeTime, tabType } = this.state
    this.tabType = tabType
    this.setState({ rangeTime: searchRangeTime })
    this.refreshInfo()
  }

  exportUri = () => {
    let param = this.buildParam()
    param.export = true
    return `/aggregator/guild_report/get_data?${stringify(param)}`
  }

  rangePickerHTML= () => {
    const { tabType } = this.state
    return tabType === tabTypeDaily
      ? <RangePicker defaultValue={defaultTimeRange} style={{ marginLeft: 2 }} format={dateFormat} onChange={(v) => this.setState({ searchRangeTime: v })} />
      : <RangePicker defaultValue={defaultTimeRangeMonth} style={{ marginLeft: 2 }} picker='month' onChange={(v) => this.setState({ searchRangeTime: v })} />
  }

  render () {
    const { list } = this.props.model
    const { tabType } = this.state

    return (
      <PageHeaderWrapper title={'公会经营监控报表'}>
        <div>①按日查看，时间范围最多31天。按月查看，时间范围最多2个月</div>
        <div>②数据从24年9月开始展示</div>

        <div style={{ marginTop: 10 }}>
          <span>公会经营开始时间</span>
          <Select defaultValue={tabType} onChange={(v) => this.setState({ tabType: v })} style={{ width: 80, marginLeft: 5 }}>
            <Select.Option value={tabTypeDaily}>按天</Select.Option>
            <Select.Option value={tabTypeMonth}>按月</Select.Option>
          </Select>
          {this.rangePickerHTML()}

          <span style={{ marginLeft: 10 }}>公会SID：</span>
          <InputNumber placeholder='支持长位和短位频道' onChange={e => this.setState({ searchSID: e })} style={{ width: 160, marginLeft: 2 }} />

          <Button style={{ marginLeft: 15 }} type={'primary'} onClick={this.searchHandler()}>查询</Button>
          <Button style={{ marginLeft: 15 }} type={'primary'} href={this.exportUri()}>导出</Button>
        </div>
        <span className='sortBlock'>
          <Table style={{ marginTop: 10, width: 1500 }} className='sortTable' columns={this.columnsSort} onChange={this.sortHandler} pagination={false} size='small' />
          <Button className={'sortBtn'} type={'primary'} onClick={this.searchHandler()}>排序</Button>
        </span>
        <Table rowKey={(record, index) => record.sid} style={{ marginTop: 1 }} bordered showHeader={false} dataSource={list} columns={this.columns} pagination={this.pageValue()} />
      </PageHeaderWrapper>
    )
  }
}

export default GuildMonitorReport
