import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/dating_match_bosssvr/city_partner/get_recommend_items?${stringify(params)}`)
}

export function add (params) {
  return request(`/dating_match_bosssvr/city_partner/update_recommend_items?${stringify(params)}`)
}

export function remove (params) {
  return request(`/dating_match_bosssvr/city_partner/remove_recommend_items?${stringify(params)}`)
  // return request(`/dating_match/city_partner/remove_recommend_items`, {
  //   method: 'POST',
  //   headers: {
  //     'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
  //   },
  //   body: stringify(params)
  // })
}

export function update (params) {
  return request(`/dating_match_bosssvr/city_partner/update_recommend_items?${stringify(params)}`)
}
