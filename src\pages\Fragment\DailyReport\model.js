import { genGetRequireTemplate } from '@/utils/common'

const queryDailyReport = genGetRequireTemplate('/aggregator/frags/daily_summary', 'dailyReportList')
const queryProductList = genGetRequireTemplate('/aggregator/frags/play_type_product', 'productList')
const getFragmentConfig = genGetRequireTemplate('/fragment_mall/boss/get_search_elem', 'fragmentConfig')
const getExchangeList = genGetRequireTemplate('/fragment_mall/boss/display_exchange_records')
const downloadExchangeList = genGetRequireTemplate('/fragment_mall/boss/export_exchange_records')

export default {
  namespace: 'fragmentReport',
  state: {
    dailyReportList: [],
    productList: [],
    fragmentConfig: {},
    exchangeList: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    queryDailyReport,
    queryProductList,
    getFragmentConfig,
    getExchangeList,
    downloadExchangeList
  }
}
