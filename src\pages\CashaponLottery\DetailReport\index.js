import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
// import dateString from '@/utils/dateString'
import { Table, Divider, Button, Form, Card, DatePicker } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const namespace = 'cashaponDetailReport'
const getListUri = `${namespace}/getList`
// const FormItem = Form.Item

const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker

@connect(({ cashaponDetailReport }) => ({
  model: cashaponDetailReport
}))
class CashaponDetailReport extends Component {
  // column structs.
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '扭1次人数', dataIndex: 'betUser1', align: 'center' },
    { title: '扭1次流水', dataIndex: 'betAmethyst1', align: 'center' },
    { title: '扭10次人数', dataIndex: 'betUser2', align: 'center' },
    { title: '扭10次流水', dataIndex: 'betAmethyst2', align: 'center' },
    { title: '扭100次人数', dataIndex: 'betUser3', align: 'center' },
    { title: '扭100次流水', dataIndex: 'betAmethyst3', align: 'center' },
    { title: '扭500次人数', dataIndex: 'betUser4', align: 'center' },
    { title: '扭500次流水', dataIndex: 'betAmethyst4', align: 'center' }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, dateRange: [moment().subtract(7, 'days'), moment().subtract(-1, 'days')] }

  // get list from server.
  componentDidMount () {
    const { dispatch } = this.props
    const { dateRange } = this.state
    var data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  onChange = (date, format) => {
    this.setState({ dateRange: date })
  }

  onClick = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    var data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  // content
  render () {
    const { route, model: { list } } = this.props
    const { dateRange } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <div>
              日期范围
              <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
              <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
            </div>
            <Divider />
            <Table dataSource={list} columns={this.columns} rowKey={(record, index) => index} pagination={this.defaultPageValue} size='small' />
          </Form>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default CashaponDetailReport
