import { getLists, auditStatus } from './api'
import { message } from 'antd'

export default {
  namespace: 'MicroCoverAudit',

  state: {
    list: []
  },

  reducers: {
    updateList (state, { payload, tabListPayload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload,
        tabList: tabListPayload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getLists, payload)
      yield put({
        type: 'updateList',
        payload: Array.isArray(list) ? list : []
      })
    },
    * updateAuditStatus ({ payload, getListParam }, { call, put }) {
      const { data: { status, msg } } = yield call(auditStatus, payload)
      if (status === 0) {
        message.success('更新成功')
        yield put({
          type: 'getList',
          payload: getListParam
        })
      } else {
        message.error('失败：' + msg)
      }
    }
  }
}
