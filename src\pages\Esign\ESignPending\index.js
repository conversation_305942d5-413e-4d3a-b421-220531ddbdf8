import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, DatePicker, Input, Tooltip, Modal, Select, InputNumber } from 'antd'
import { connect } from 'dva'
import moment from 'moment'
import 'moment/locale/zh-cn'
import { QuestionCircleOutlined } from '@ant-design/icons'
moment.locale('zh-cn')

const namespace = 'ESignChange'
const FormItem = Form.Item
const Option = Select.Option

@connect(({ ESignChange }) => ({
  model: ESignChange
}))
class ESignChange extends Component {
  genTitle = (title, tips) => {
    return <span>
      <Tooltip title={tips}>
        <QuestionCircleOutlined style={{ marginRight: '0.25em' }} />
        {title}
      </Tooltip>
    </span>
  }

  // 需要修改
  ColumnPendingSign = [
    { title: '签约类型',
      dataIndex: 'type',
      align: 'center',
      width: 120,
      render: (text, record) => {
        switch (text) {
          case 1: return '正常签约'
          case 2: return '改签'
          case 3: return '厅管签约'
          case 4: return '续约'
        }
        return '未知'
      }
    },
    { title: '业务',
      dataIndex: 'btype',
      align: 'center',
      width: 80,
      render: (text, record) => {
        if (text === 0) {
          return '交友'
        } else if (text === 1) {
          return '约战'
        } else if (text === 2) {
          return '宝贝'
        }
        return ''
      }
    },
    { title: '电子签约频道', dataIndex: 'asid', width: 120, align: 'center' },
    { title: '主持UID', dataIndex: 'uid', width: 120, align: 'center' },
    { title: '主持昵称', dataIndex: 'nick', width: 160, align: 'center' },
    { title: '主持YY号', dataIndex: 'yy', width: 120, align: 'center' },
    { title: '真实姓名', dataIndex: 'rname', width: 120, align: 'center' },
    { title: '身份证号码', dataIndex: 'cardno', width: 160, align: 'center' },
    { title: '合同开始时间',
      dataIndex: 'cstarttime',
      key: 'cstarttime',
      width: 120,
      align: 'center',
      render: text => {
        if (!text) {
          return ''
        }
        return moment.unix(text).format('YYYY-MM-DD')
      }
    },
    { title: '合同结束时间',
      dataIndex: 'cendtime',
      key: 'cendtime',
      width: 120,
      align: 'center',
      render: text => {
        if (!text) {
          return ''
        }
        return moment.unix(text).format('YYYY-MM-DD')
      }
    },
    { title: '合同年限',
      dataIndex: 'contractYear',
      align: 'center',
      width: 80,
      render: (text, record) => {
        switch (text) {
          case 1:
            return '一年'
          case 2:
            return '两年'
          case 0:
          case 3:
            return '三年'
        } 
      }
    },
    { title: '签约状态',
      dataIndex: 'stage',
      align: 'center',
      width: 120,
      render: (text, record) => {
        switch (text) {
          case 0: return '待实名'
          case 9: return '待完善资料'
          case 1: return '待签署合同1'
          case 2: return '待签署合同2'
          case 3: return '待ow签署'
          case 4: return '待运营审批'
          case 5: return '待财务审批'
          case 6: return '已归档'
          case 7: return '运营已拒绝'
          case 8: return '财务已拒绝'
          case 10: return '已终止'
          case 11: return '待华多签约1'
          case 12: return '待华多签约2'
        }
        return '未知'
      }
    },
    { title: '添加类型',
      dataIndex: 'atype',
      align: 'center',
      width: 80,
      render: (text, record) => {
        if (text === 0) {
          return '系统'
        } else if (text === 1) {
          return '人工'
        }
        return ''
      }
    },
    { title: '操作',
      align: 'center',
      width: 160,
      render: (text, record) => (
        <div>
          {
            record.type !== 1 && record.type !== 3 ? <span><Divider type='vertical' /><a onClick={this.showModal(true, record)}>修改</a><Divider type='vertical' /><a onClick={this.deleteRecord(record.uid)}>删除</a></span>
              : <span><a onClick={this.deleteRecord(record.uid)}>删除</a></span>
          }
        </div>)
    }
  ]

  defaultPageValue = { pageSizeOptions: ['50', '60', '100', '1000'], showSizeChanger: true, pageSize: 50, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` } // 分页设置，可以不改

  // 需要修改
  state = {
    visible: false,
    isUpdate: false,
    tabid: '1',
    value: {},
    searchStage: -1,
    searchAsid: '0',
    searchText: '',
    type: 1,
    searchType: 0,
    searchBusinessType: -1
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = { tabid: this.state.tabid }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  deleteRecord = (uid) => e => {
    const { dispatch } = this.props
    const data = { uid: uid }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.businessType = v.btype == null ? 0 : v.btype
      if (v.cstarttime) {
        v.startTime = moment.unix(v.cstarttime)
      }
      if (v.cendtime) {
        v.endTime = moment.unix(v.cendtime)
      }
      if (v.contractYear === 0) {
        v.contractYear = 3
      }
      this.formRef.setFieldsValue(v)
    }
    if (isUpdate) {
      this.state.type = record.type
    } else {
      this.state.type = 1
      this.state.businessType = 0
    }
    console.log(v)
    this.setState({ value: v, isUpdate: isUpdate, visible: true, title: isUpdate ? '更新' : '添加' })
  }

  // hide total modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  saveAddFormRef = (formRef) => {
    this.addFormRef = formRef
  }

  handleAddSubmit = () => {
    this.formRef.submit()
  }

  // 添加
  onFinish = values => {
    const { dispatch } = this.props
    var getListParam = { stage: this.state.searchStage, asid: this.state.searchAsid, text: this.state.searchText, type: this.state.searchType }
    const isUpdate = this.state.isUpdate
    const url = isUpdate ? `${namespace}/updateItem` : `${namespace}/addItem`
    if (values.startTime) {
      values.startTime = values.startTime.unix()
    }
    if (values.endTime) {
      values.endTime = values.endTime.unix()
    }
    dispatch({
      type: url,
      payload: values,
      getListParam: getListParam
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  onChangeTab = tabid => {
    console.log('onChangeTab:', tabid)
    this.setState({ tabid: tabid })
    const { dispatch } = this.props
    var data = { tabid: tabid }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  onSearch = () => {
    const { dispatch } = this.props
    const data = { stage: this.state.searchStage, asid: this.state.searchAsid, text: this.state.searchText, businessType: this.state.searchBusinessType, type: this.state.searchType }
    console.log('onSearch:', data)
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  updateType = (t) => {
    console.log(t)
    if (this.formRef) {
      this.formRef.setFieldsValue({ type: t })
      this.setState({ type: t })
    }
  }

  updateBusinessType = (t) => {
    console.log(t)
    if (this.formRef) {
      this.setState({ businessType: t })
    }
  }

  isNormalESignType = (t) => {
    return t === 1
  }

  isChangeESignType = (t) => {
    return t === 2
  }

  isDisableContractYear= (t) => {
    console.log(t, this.state.businessType)
    return (t !== 1 && t !== 4) || this.state.businessType !== 0
  }

  // 实际的页面信息
  render () {
    const { route, model: { list } } = this.props // 基本不需要修改
    const { isUpdate, title, visible, type } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            签约类型
            <Divider type='vertical' />
            <Select labelInValue defaultValue={{ key: 0 }} style={{ width: 120 }} onChange={value => this.setState({ searchType: value.key })}>
              <Option value={0}>全部</Option>
              <Option value={1}>正常签约</Option>
              <Option value={2}>改签</Option>
              <Option value={3}>厅管签约</Option>
              <Option value={4}>续约</Option>
            </Select>
            <Divider type='vertical' />
            签约状态
            <Divider type='vertical' />
            <Select labelInValue defaultValue={{ key: -1 }} style={{ width: 120 }} onChange={value => this.setState({ searchStage: value.key })}>
              <Option value={-1}>全部</Option>
              <Option value={0}>待实名</Option>
              <Option value={9}>待完善资料</Option>
              <Option value={1}>待签署合同1</Option>
              <Option value={2}>待签署合同2</Option>
              <Option value={3}>待ow签署</Option>
              <Option value={7}>运营已拒绝</Option>
              <Option value={8}>财务已拒绝</Option>
              <Option value={11}>待华多签约1</Option>
              <Option value={12}>待华多签约2</Option>
            </Select>
            <Divider type='vertical' />
            业务
            <Divider type='vertical' />
            <Select labelInValue defaultValue={{ key: -1 }} style={{ width: 120 }} onChange={value => this.setState({ searchBusinessType: value.key })}>
              <Option value={-1}>全部</Option>
              <Option value={0}>交友</Option>
              <Option value={1}>约战</Option>
              <Option value={2}>宝贝</Option>
            </Select>
            <Divider type='vertical' /> {/* 分割线 */}
            <Input placeholder='搜索短位频道' onChange={e => this.setState({ searchAsid: e.target.value })} style={{ width: 150 }} />
            <Input placeholder='搜索内容' onChange={e => this.setState({ searchText: e.target.value })} style={{ width: 150 }} />
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onSearch}>搜索</Button>
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.showModal(false, null)}>添加</Button>
            <Divider />
            <Table dataSource={list} bordered columns={this.ColumnPendingSign} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} rowKey={(record, index) => index} /> {/* 显示的列表 */}
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleAddSubmit}>
          <Form onFinish={this.onFinish} {...formItemLayout} ref={form => { this.formRef = form }}>
            <FormItem label='UID' name='uid' rules={[{ required: true, message: 'uid不能为空' }]}>
              <InputNumber style={{ width: '100%' }} disabled={isUpdate} />
            </FormItem>
            <FormItem label='业务类型' name='businessType' rules={[{ required: true, message: '业务类型不能为空' }]}>
              <Select disabled={isUpdate} onChange={(v) => this.updateBusinessType(v)}>
                <Option value={0}>交友</Option>
                <Option value={1}>约战</Option>
                <Option value={2}>宝贝</Option>
              </Select>
            </FormItem>
            <FormItem label='签约类型' name='type' initialValue={1} rules={[{ required: true, message: '签约类型不能为空' }]}>
              <Select disabled={isUpdate} onChange={(v) => this.updateType(v)}>
                <Option value={1}>正常签约</Option>
                <Option value={2}>改签</Option>
                <Option value={4}>续约</Option>
              </Select>
            </FormItem>
            <FormItem label='真实姓名' name='rname' rules={[{ required: isUpdate }]}>
              <Input disabled={this.isNormalESignType(type)} />
            </FormItem>
            <FormItem label='身份证' name='cardno' rules={[{ required: isUpdate }]}>
              <Input disabled={this.isNormalESignType(type)} />
            </FormItem>
            <FormItem label='原合同开始时间' name='startTime' rules={[{ required: !this.isNormalESignType(type) }]}>
              <DatePicker disabled={this.isNormalESignType(type)} showTime='true' format='YYYY-MM-DD' />
            </FormItem>
            <FormItem label='原合同结束时间' name='endTime' rules={[{ required: !this.isNormalESignType(type) }]}>
              <DatePicker disabled={this.isNormalESignType(type)} showTime='true' format='YYYY-MM-DD' />
            </FormItem>
            <FormItem label='合同年限' name='contractYear' rules={[{ required: !this.isDisableContractYear(type), message: '合同年限不能为空' }]}>
              <Select disabled={this.isDisableContractYear(type)}>
                <Option value={1}>一年</Option>
                <Option value={2}>两年</Option>
                <Option value={3}>三年</Option>
              </Select>
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default ESignChange
