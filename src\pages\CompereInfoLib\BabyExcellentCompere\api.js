import request from '@/utils/request'
import { stringify } from 'qs'

export function listInfo (params) {
  return request(`/supercompere/boss/bbexcellentcompere/list?${stringify(params)}`)
}

export function addInfo (params) {
  return request(`/supercompere/boss/bbexcellentcompere/add?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

export function deleteInfo (params) {
  return request(`/supercompere/boss/bbexcellentcompere/delete?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

export function updateInfo (params) {
  return request(`/supercompere/boss/bbexcellentcompere/update?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}
