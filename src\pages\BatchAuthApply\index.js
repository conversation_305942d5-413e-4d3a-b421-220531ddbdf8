import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper' 
import { connect } from 'dva'
import { Card, Row, Col, Input, Select, Button, Typography, message } from 'antd'
import { Form } from '@ant-design/compatible' 
import { getCookie } from '@/utils/common'
const { Text } = Typography 
const Option = Select.Option

@connect(({ batchAuthApply }) => ({
  batchAuthApply
}))

@Form.create()
class BatchAuthApply extends Component {
  state={
    reason: '',
    days: 1
  }

  componentDidMount () { }

  handleSubmit = (uri, days, reason) => {
    if (!reason) {
      message.warn('原因必填~')
      return
    }
    const value = {
      uri: uri,
      days: days,
      reason: reason
    } 
    const { dispatch } = this.props 
    dispatch({
      type: 'batchAuthApply/submit',
      payload: value
    })
  }

  nameTip = () => {
    const passport = getCookie('username')
    if (passport.indexOf('dw_') === 0) {
      return ''
    }
    return (
      <Text type='danger'>注意: 当前登录帐号通行证非dw_开头，请在申请原因中注明姓名等信息</Text>
    )
  }

  render () {
    const { route } = this.props 
    const { days, reason } = this.state
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Row>
            <Col span={24} style={{ margin: '1em 0' }}>
              当前页面暂无权限，请先申请:
            </Col>
            <Col span={24} style={{ margin: '1em 0' }}>
              <Text style={{ display: 'inline-block', width: '8em' }}>申请原因:</Text>
              <Input placeholder='申请原因' style={{ width: '20em' }} value={reason} onChange={e => this.setState({ reason: e.target.value })} />
            </Col>
            <Col span={24} style={{ margin: '1em 0' }}>
              <Text style={{ display: 'inline-block', width: '8em' }}>请选择有效期:</Text>
              <Select style={{ width: '20em' }} value={days} onChange={v => this.setState({ days: v })}>
                <Option value={1}>一天</Option>
                <Option value={7}>一周</Option>
                <Option value={30}>一个月</Option>
                <Option value={90}>三个月</Option>
                <Option value={180}>半年</Option>
                <Option value={365}>一年</Option>
              </Select>
            </Col>
            <Col span={24}>
              <Button type='primary' onClick={() => { this.handleSubmit([route.uri], days, reason) }}>
                申请
              </Button>
            </Col>
          </Row>

        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default BatchAuthApply
