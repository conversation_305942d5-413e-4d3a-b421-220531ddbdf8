import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/esign_boss/op_esign_pending_get?${stringify(params)}`)
}

export function update (params) {
  return request(`/esign_boss/op_esign_pending_update?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function add (params) {
  return request(`/esign_boss/op_esign_pending_add?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function remove (params) {
  return request(`/esign_boss/op_esign_pending_remove?${stringify(params)}`)
}
