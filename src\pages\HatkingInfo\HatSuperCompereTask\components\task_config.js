import React, { Component } from 'react'
import { connect } from 'dva'
import { <PERSON><PERSON>, Card, DatePicker, Divider, Input, InputNumber, message, Modal, Select, Table, Tooltip } from 'antd'
import dateString from '@/utils/dateString'
import moment from 'moment/moment'
import { isModifyList } from './common'

const namespace = 'hatSuperCompereTask'

const Option = Select.Option
const { TextArea } = Input

const reviewMap = { 0: '-', 1: '待复核', 2: '已复核' }
const reviewStatusList = [
  { label: '待复核', value: 1 },
  { label: '已复核', value: 2 }
]

const approvaMap = { 0: '-', 1: '待审核', 2: '审批通过', 3: '审批不通过', 4: '一审中', 5: '二审中' }
const approvalStatusList = [
  { label: '待审核', value: 1 },
  { label: '审批通过', value: 2 },
  { label: '审批不通过', value: 3 },
  { label: '一审中', value: 4 },
  { label: '二审中', value: 5 }
]

@connect(({ hatSuperCompereTask }) => ({
  model: hatSuperCompereTask
}))

class TaskConfig extends Component {
  columns = [
    { title: '任务时间', dataIndex: 'month', align: 'center', fixed: 'left' },
    { title: '短位ID', dataIndex: 'asid', align: 'center', fixed: 'left' },
    { title: '主持UID', dataIndex: 'uid', align: 'center', fixed: 'left' },
    { title: '主持YY号', dataIndex: 'yy', align: 'center', fixed: 'left' },
    { title: '昵称', dataIndex: 'nick', align: 'center', fixed: 'left' },

    {
      title: '基础任务',
      children: [
        {
          title: '[系统生成]主持盖章流水/万元',
          dataIndex: 'systemSealWater',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.hatKingTaskBaseInfo.systemSealWater))
        },
        {
          title: '[系统生成]达标奖励/万元',
          dataIndex: 'systemReward',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.hatKingTaskBaseInfo.systemReward))
        },
        {
          title: '[配置]主持盖章流水/万元',
          dataIndex: 'configSealWater',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.hatKingTaskBaseInfo.configSealWater))
        },
        {
          title: '[配置]达标奖励/万元',
          dataIndex: 'configReward',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.hatKingTaskBaseInfo.configReward))
        }
      ]
    },

    {
      title: '进阶任务',
      children: [
        {
          title: '[系统生成]主持盖章流水/万元',
          dataIndex: 'systemSealWater',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.hatKingTaskAdvancedInfo.systemSealWater))
        },
        {
          title: '[系统生成]达标奖励/万元',
          dataIndex: 'systemReward',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.hatKingTaskAdvancedInfo.systemReward))
        },
        {
          title: '[配置]主持盖章流水/万元',
          dataIndex: 'configSealWater',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.hatKingTaskAdvancedInfo.configSealWater))
        },
        {
          title: '[配置]达标奖励/万元',
          dataIndex: 'configReward',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.hatKingTaskAdvancedInfo.configReward))
        }
      ]
    },

    {
      title: '冲刺任务',
      children: [
        {
          title: '[系统生成]主持盖章流水/万元',
          dataIndex: 'systemSealWater',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.hatKingTaskSprintInfo.systemSealWater))
        },
        {
          title: '[系统生成]达标奖励/万元',
          dataIndex: 'systemReward',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.hatKingTaskSprintInfo.systemReward))
        },
        {
          title: '[配置]主持盖章流水/万元',
          dataIndex: 'configSealWater',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.hatKingTaskSprintInfo.configSealWater))
        },
        {
          title: '[配置]达标奖励/万元',
          dataIndex: 'configReward',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.hatKingTaskSprintInfo.configReward))
        }
      ]
    },

    { title: '任务生成时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (dateString(record.timestamp)) },
    { title: '复核状态', dataIndex: 'reviewStatus', align: 'center', render: (text, record) => (reviewMap[record.approvalInfo.reviewStatus]) },
    { title: '审批状态', dataIndex: 'approvalStatus', align: 'center', render: (text, record) => (approvaMap[record.approvalInfo.approvalStatus]) },
    { title: '驳回原因', dataIndex: 'approvalReason', align: 'center', render: (text, record) => (record.approvalInfo.approvalReason) },
    { title: '操作人', dataIndex: 'optUid', align: 'center', render: (text, record) => (record.approvalInfo.optUid === 0 ? '' : record.approvalInfo.optUid) },
    { title: '是否生效', dataIndex: 'status', align: 'center', render: (text, record) => (record.status ? '生效中' : '未生效') },
    { title: '操作', key: 'operation', align: 'center', width: 200, fixed: 'right', render: (text, record) => (<span><a size='small' type='primary' onClick={this.showConfirmTaskWin(record)}>任务确认</a><Divider type='vertical' /><a size='small' type='primary' onClick={this.showTaskConfigInvalidWin(record)}>历史记录</a></span>)
    }
  ]

  columnsInvalidWin = [
    { title: '任务时间', dataIndex: 'month', align: 'center', fixed: 'left', render: (text, record) => (record.snapshot.month) },
    { title: '短位ID', dataIndex: 'asid', align: 'center', fixed: 'left', render: (text, record) => (record.asid) },
    { title: '主持YY号', dataIndex: 'yy', align: 'center', fixed: 'left', render: (text, record) => (record.yy) },
    { title: '昵称', dataIndex: 'nick', align: 'center', fixed: 'left', render: (text, record) => (record.nick) },

    {
      title: '基础任务',
      children: [
        {
          title: '[系统生成]主持盖章流水/万元',
          dataIndex: 'systemSealWater',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.snapshot.hatKingTaskBaseInfo.systemSealWater))
        },
        {
          title: '[系统生成]达标奖励/万元',
          dataIndex: 'systemReward',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.snapshot.hatKingTaskBaseInfo.systemReward))
        },
        {
          title: '[配置]主持盖章流水/万元',
          dataIndex: 'configSealWater',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.snapshot.hatKingTaskBaseInfo.configSealWater))
        },
        {
          title: '[配置]达标奖励/万元',
          dataIndex: 'configReward',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.snapshot.hatKingTaskBaseInfo.configReward))
        }
      ]
    },

    {
      title: '进阶任务',
      children: [
        {
          title: '[系统生成]主持盖章流水/万元',
          dataIndex: 'systemSealWater',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.snapshot.hatKingTaskAdvancedInfo.systemSealWater))
        },
        {
          title: '[系统生成]达标奖励/万元',
          dataIndex: 'systemReward',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.snapshot.hatKingTaskAdvancedInfo.systemReward))
        },
        {
          title: '[配置]主持盖章流水/万元',
          dataIndex: 'configSealWater',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.snapshot.hatKingTaskAdvancedInfo.configSealWater))
        },
        {
          title: '[配置]达标奖励/万元',
          dataIndex: 'configReward',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.snapshot.hatKingTaskAdvancedInfo.configReward))
        }
      ]
    },

    {
      title: '冲刺任务',
      children: [
        {
          title: '[系统生成]主持盖章流水/万元',
          dataIndex: 'systemSealWater',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.snapshot.hatKingTaskSprintInfo.systemSealWater))
        },
        {
          title: '[系统生成]达标奖励/万元',
          dataIndex: 'systemReward',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.snapshot.hatKingTaskSprintInfo.systemReward))
        },
        {
          title: '[配置]主持盖章流水/万元',
          dataIndex: 'configSealWater',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.snapshot.hatKingTaskSprintInfo.configSealWater))
        },
        {
          title: '[配置]达标奖励/万元',
          dataIndex: 'configReward',
          align: 'center',
          render: (text, record) => (this.yuanToWan(record.snapshot.hatKingTaskSprintInfo.configReward))
        }
      ]
    },

    { title: '任务生成时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (dateString(record.snapshot.timestamp)) },
    { title: '复核状态', dataIndex: 'reviewStatus', align: 'center', render: (text, record) => (reviewMap[record.snapshot.approvalInfo.reviewStatus]) },
    { title: '审批状态', dataIndex: 'approvalStatus', align: 'center', render: (text, record) => (approvaMap[record.snapshot.approvalInfo.approvalStatus]) },
    { title: '操作人', dataIndex: 'optUid', align: 'center', render: (text, record) => (record.snapshot.approvalInfo.optUid) },
    { title: '失效时间', dataIndex: 'optUid', align: 'center', render: (text, record) => (dateString(record.timestamp)) },
    { title: '备注', dataIndex: 'optUid', align: 'center', render: (text, record) => (record.remark) }
  ]

   columnsConfirmTaskSystem = [
     { title: '系统生成', dataIndex: 'title1', align: 'center' },
     { title: '盖章流水任务值/万元', dataIndex: 'systemSealWater', align: 'center', render: (text, record) => (this.yuanToWan(record.systemSealWater)) },
     { title: '达标奖励/万元', dataIndex: 'systemReward', align: 'center', render: (text, record) => (this.yuanToWan(record.systemReward)) },
     { title: ' ', dataIndex: ' ', align: 'center' },
     { title: '运营配置', dataIndex: 'title2', align: 'center' },
     { title: '盖章流水任务值/万元', dataIndex: 'configSealWater', align: 'center', render: (text, record) => (this.yuanToWan(record.configSealWater)) },
     { title: '达标奖励/万元', dataIndex: 'configReward', align: 'center', render: (text, record) => (this.yuanToWan(record.configReward)) }
   ]

  columnsConfirmTaskWin = [
    { title: '任务时间', dataIndex: 'month', width: 80, align: 'center', fixed: 'left', render: (text, record) => (record.month) },
    { title: '短位ID', dataIndex: 'asid', align: 'center', fixed: 'left', render: (text, record) => (record.asid) },
    { title: '主持UID', dataIndex: 'uid', align: 'center', fixed: 'left', render: (text, record) => (record.uid) },
    { title: '主持YY号', dataIndex: 'yy', align: 'center', fixed: 'left', render: (text, record) => (record.yy) },
    { title: '昵称', dataIndex: 'nick', align: 'center', fixed: 'left', render: (text, record) => (record.nick) },

    { title: '是否调整任务值',
      dataIndex: 'isModify',
      align: 'center',
      render: (text, record) => (<Select allowClear
        value={record.isModify}
        style={{ width: 200, marginLeft: 3 }}
        onChange={this.handleRowChangeSelect(record, 'isModify')}>
        {isModifyList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}
      </Select>)
    },

    {
      title: '基础任务',
      children: [
        {
          title: '[系统生成]主持盖章流水/万元',
          dataIndex: 'systemSealWaterBase',
          align: 'center',
          width: 220,
          render: (text, record) => (this.yuanToWan(record.hatKingTaskBaseInfo.systemSealWater))
        },
        {
          title: '[系统生成]达标奖励/万元',
          dataIndex: 'systemRewardBase',
          align: 'center',
          width: 220,
          render: (text, record) => (this.yuanToWan(record.hatKingTaskBaseInfo.systemReward))
        },
        {
          title: '[配置]主持盖章流水/万元',
          dataIndex: 'configSealWaterBase',
          align: 'center',
          width: 220,
          render: (text, record) => (
            <Tooltip title='请输入数字[0, +∞),支持一位小数'>
              <Input
                id={'input1'}
                placeholder='必须为[0, +∞)'
                style={{ width: 200 }}
                value={record.configSealWaterBase}
                onChange={(e) => {
                  let value = e.target.value
                  if (value === '' || value === null) value = 0
                  const match = /^[0-9]+\.?[0-9]?$/.test(value)
                  if (match) {
                    this.handleRowChangeString(record, 'configSealWaterBase')(e)
                  }
                }}
              // onChange={this.handleRowChangeString(record, 'configSealWaterBase')}
              // defaultValue={this.yuanToWan(record.hatKingTaskBaseInfo.configSealWater) !== 0 ? this.yuanToWan(record.hatKingTaskBaseInfo.configSealWater) : ''}
              /></Tooltip>)
        },
        {
          title: '[配置]达标奖励/万元',
          dataIndex: 'configRewardBase',
          align: 'center',
          width: 220,
          render: (text, record) => (<Tooltip title='请输入数字[0, 9),支持一位小数'>
            <Input
              id={'input2'}
              placeholder='必须为[0, 9)'
              style={{ width: 200 }}
              // onChange={this.handleRowChangeString(record, 'configRewardBase')}
              // defaultValue={this.yuanToWan(record.hatKingTaskBaseInfo.configReward) > 0 ? this.yuanToWan(record.hatKingTaskBaseInfo.configReward) : ''}
              value={record.configRewardBase}
              onChange={(e) => {
                let value = e.target.value
                if (value === '' || value === null) value = 0
                const match = /^0$|^0\.[1-9]?$|^[0-8](\.[1-9]?)?$/.test(value)
                // console.log(match, value)
                if (match) {
                  this.handleRowChangeString(record, 'configRewardBase')(e)
                }
              }}
            /></Tooltip>)
        }
      ]
    },

    {
      title: '进阶任务',
      children: [
        {
          title: '[系统生成]主持盖章流水/万元',
          dataIndex: 'systemSealWaterAdvanced',
          align: 'center',
          width: 220,
          render: (text, record) => (this.yuanToWan(record.hatKingTaskAdvancedInfo.systemSealWater))
        },
        {
          title: '[系统生成]达标奖励/万元',
          dataIndex: 'systemRewardAdvanced',
          align: 'center',
          width: 220,
          render: (text, record) => (this.yuanToWan(record.hatKingTaskAdvancedInfo.systemReward))
        },
        {
          title: '[配置]主持盖章流水/万元',
          dataIndex: 'configSealWaterAdvanced',
          align: 'center',
          width: 220,
          render: (text, record) => (
            <Tooltip title='请输入数字[0, +∞),支持一位小数'>
              <Input
                id={'input3'}
                placeholder='必须为[0, +∞)'
                style={{ width: 200 }}
                value={record.configSealWaterAdvanced}
                onChange={(e) => {
                  let value = e.target.value
                  if (value === '' || value === null) value = 0
                  const match = /^[0-9]+\.?[0-9]?$/.test(value)
                  if (match) {
                    this.handleRowChangeString(record, 'configSealWaterAdvanced')(e)
                  }
                }}
              // onChange={this.handleRowChangeString(record, 'configSealWaterAdvanced')}
              // defaultValue={this.yuanToWan(record.hatKingTaskAdvancedInfo.configSealWater) !== 0 ? this.yuanToWan(record.hatKingTaskAdvancedInfo.configSealWater) : ''}
              /></Tooltip>)
        },
        {
          title: '[配置]达标奖励/万元',
          dataIndex: 'configRewardAdvanced',
          align: 'center',
          width: 220,
          render: (text, record) => (<Tooltip title='请输入数字[0, 9),支持一位小数'>
            <Input
              id={'input4'}
              placeholder='必须为[0, 9)'
              style={{ width: 200 }}
              value={record.configRewardAdvanced}
              onChange={(e) => {
                let value = e.target.value
                if (value === '' || value === null) value = 0
                const match = /^0$|^0\.[1-9]?$|^[0-8](\.[1-9]?)?$/.test(value)
                if (match) {
                  this.handleRowChangeString(record, 'configRewardAdvanced')(e)
                }
              }}
            // onChange={this.handleRowChangeString(record, 'configRewardAdvanced')}
            // defaultValue={this.yuanToWan(record.hatKingTaskAdvancedInfo.configReward) !== 0 ? this.yuanToWan(record.hatKingTaskAdvancedInfo.configReward) : ''}
            /></Tooltip>)
        }
      ]
    },

    {
      title: '冲刺任务',
      children: [
        {
          title: '[系统生成]主持盖章流水/万元',
          dataIndex: 'systemSealWaterSprint',
          align: 'center',
          width: 220,
          render: (text, record) => (this.yuanToWan(record.hatKingTaskSprintInfo.systemSealWater))
        },
        {
          title: '[系统生成]达标奖励/万元',
          dataIndex: 'systemRewardSprint',
          align: 'center',
          width: 220,
          render: (text, record) => (this.yuanToWan(record.hatKingTaskSprintInfo.systemReward))
        },
        {
          title: '[配置]主持盖章流水/万元',
          dataIndex: 'configSealWaterSprint',
          align: 'center',
          width: 220,
          render: (text, record) => (
            <Tooltip title='请输入数字[0, +∞),支持一位小数'>
              <Input
                id={'input5'}
                placeholder='必须为[0, +∞)'
                style={{ width: 200 }}
                value={record.configSealWaterSprint}
                onChange={(e) => {
                  let value = e.target.value
                  if (value === '' || value === null) value = 0
                  const match = /^[0-9]+\.?[0-9]?$/.test(value)
                  if (match) {
                    this.handleRowChangeString(record, 'configSealWaterSprint')(e)
                  }
                }}
              // onChange={this.handleRowChangeString(record, 'configSealWaterSprint')}
              // defaultValue={this.yuanToWan(record.hatKingTaskSprintInfo.configSealWater) !== 0 ? this.yuanToWan(record.hatKingTaskSprintInfo.configSealWater) : ''}
              /></Tooltip>)
        },
        {
          title: '[配置]达标奖励/万元',
          dataIndex: 'configRewardSprint',
          align: 'center',
          width: 220,
          render: (text, record) => (<Tooltip title='请输入数字[0, 9),支持一位小数'>
            <Input
              id={'input6'}
              placeholder='必须为[0, +∞)'
              style={{ width: 200 }}
              value={record.configRewardSprint}
              onChange={(e) => {
                let value = e.target.value
                if (value === '' || value === null) value = 0
                const match = /^[0-9]+\.?[0-9]?$/.test(value)
                if (match) {
                  this.handleRowChangeString(record, 'configRewardSprint')(e)
                }
              }}
              // onChange={this.handleRowChangeString(record, 'configRewardSprint')}
              // defaultValue={this.yuanToWan(record.hatKingTaskSprintInfo.configReward) !== 0 ? this.yuanToWan(record.hatKingTaskSprintInfo.configReward) : ''}
            /></Tooltip>)
        }
      ]
    },

    { title: '备注', dataIndex: 'remark', align: 'center', render: (text, record) => (<Input style={{ width: 200 }} onChange={this.handleRowChangeString(record, 'remark')} defaultValue={record.remark} />) }
  ]

  state = {
    visible: false,
    searchASID: 0,
    searchMonth: '',
    confirmTaskConfigMap: {}
  }

  defaultPageValue = {
    defaultPageSize: 10,
    pageSizeOptions: ['10', '20', '30', '50', '80', '100'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  yuanToWan = (v) => {
    if (v === 0) {
      return 0
    }
    return (v / 10000).toFixed(1)
  }

  wanToYuan = (v) => {
    return v * 10000
  }

  componentDidMount () {
    this.loadData()
  }

  // 编辑复核信息
  handleRowChangeString = (record, field) => e => {
    let value = e.target.value
    let { confirmTaskConfigMap } = this.state

    if (confirmTaskConfigMap[record.uid] === null || confirmTaskConfigMap[record.uid] === undefined) {
      confirmTaskConfigMap[record.uid] = {
        configRewardAdvanced: '',
        configRewardBase: '',
        configRewardSprint: '',
        configSealWaterAdvanced: '',
        configSealWaterBase: '',
        configSealWaterSprint: '',
        remark: '',
        isModify: true
      }
    }

    console.log(value)

    confirmTaskConfigMap[record.uid][field] = value
    record[field] = value

    this.forceUpdate() // 强制刷新
  }

  handleRowChangeSelect = (record, field) => value => {
    let { confirmTaskConfigMap } = this.state

    console.log(field, value)

    if (confirmTaskConfigMap[record.uid] === null || confirmTaskConfigMap[record.uid] === undefined) {
      confirmTaskConfigMap[record.uid] = {
        configRewardAdvanced: '',
        configRewardBase: '',
        configRewardSprint: '',
        configSealWaterAdvanced: '',
        configSealWaterBase: '',
        configSealWaterSprint: '',
        remark: '',
        inputReviewRemark: '',
        isModify: true
      }
    }

    if (field === 'isModify' && !value) {
      confirmTaskConfigMap[record.uid]['configSealWaterBase'] = this.yuanToWan(record.hatKingTaskBaseInfo.systemSealWater)
      record['configSealWaterBase'] = this.yuanToWan(record.hatKingTaskBaseInfo.systemSealWater)
      confirmTaskConfigMap[record.uid]['configRewardBase'] = this.yuanToWan(record.hatKingTaskBaseInfo.systemReward)
      record['configRewardBase'] = this.yuanToWan(record.hatKingTaskBaseInfo.systemReward)

      confirmTaskConfigMap[record.uid]['configSealWaterAdvanced'] = this.yuanToWan(record.hatKingTaskAdvancedInfo.systemSealWater)
      record['configSealWaterAdvanced'] = this.yuanToWan(record.hatKingTaskAdvancedInfo.systemSealWater)
      confirmTaskConfigMap[record.uid]['configRewardAdvanced'] = this.yuanToWan(record.hatKingTaskAdvancedInfo.systemReward)
      record['configRewardAdvanced'] = this.yuanToWan(record.hatKingTaskAdvancedInfo.systemReward)

      confirmTaskConfigMap[record.uid]['configSealWaterSprint'] = this.yuanToWan(record.hatKingTaskSprintInfo.systemSealWater)
      record['configSealWaterSprint'] = this.yuanToWan(record.hatKingTaskSprintInfo.systemSealWater)
      confirmTaskConfigMap[record.uid]['configRewardSprint'] = this.yuanToWan(record.hatKingTaskSprintInfo.systemReward)
      record['configRewardSprint'] = this.yuanToWan(record.hatKingTaskSprintInfo.systemReward)
    }

    console.log(confirmTaskConfigMap)
    this.forceUpdate() // 强制刷新
  }

  showConfirmTaskWin = (record, all, isSystem) => () => {
    if (record !== null && record.approvalInfo.approvalStatus !== 1 && record.approvalInfo.approvalStatus !== 3) {
      message.warn('复核状态-未审核的记录才能任务确认')
      return
    }

    let callback = (isShow, data) => {
      console.log(data)
      this.setState({ visibleConfirmTaskWin: isShow, confirmTaskConfigMap: data })
    }

    if (all) {
      this.props.dispatch({
        type: `${namespace}/getAllConfirmTaskConfig`,
        payload: { isSystem: isSystem, callback: callback }
      })
    } else {
      this.props.dispatch({
        type: `${namespace}/getConfirmTaskConfig`,
        payload: { uid: record.uid, month: record.month, callback: callback }
      })
    }

    this.setState({ visibleConfirmTaskWin: true })
  }

  showTaskConfigInvalidWin = (record) => () => {
    this.props.dispatch({
      type: `${namespace}/geTaskConfigInvalid`,
      payload: { uid: record.uid }
    })
    this.setState({ visibleInvalidWin: true })
  }

  loadData = () => {
    const { searchASID, searchMonth, searchReviewStatus, searchApprovalStatus } = this.state
    const { dispatch } = this.props

    console.log(searchMonth)
    let data = { }
    if (searchASID > 0) {
      data['asid'] = searchASID
    }
    if (searchMonth) {
      data['month'] = moment(searchMonth, 'YYYY-MM-DD HH:mm:ss').format('YYYYMM')
    }
    if (searchReviewStatus > 0) {
      data['reviewStatus'] = searchReviewStatus
    }
    if (searchApprovalStatus > 0) {
      data['approvalStatus'] = searchApprovalStatus
    }
    dispatch({
      type: `${namespace}/listTaskConfig`,
      payload: data
    })
  }

  searchHandle = () => () => {
    this.loadData()
  }

  initSystemTaskConfigHandle = () => () => {
    this.props.dispatch({
      type: `${namespace}/debugInitSystemTaskConfig`,
      payload: {}
    })
  }

  // setVirtualTimeHandle = (v) => {
  //   this.props.dispatch({
  //     type: `${namespace}/debugHatKingTaskVirtualTime`,
  //     payload: { time: v }
  //   })
  // }

  hiddenModalConfirmTaskWin = () => {
    // setTimeout(() => {
    //   window.location.reload()
    //   this.setState({ visibleConfirmTaskWin: false, inputReviewRemark: '', confirmTaskConfigMap: {} })
    // }, 1000)
    // window.location.reload()
    if (document.getElementById('input1') !== null) document.getElementById('input1').value = ''
    if (document.getElementById('input2') !== null)document.getElementById('input2').value = ''
    if (document.getElementById('input3') !== null)document.getElementById('input3').value = ''
    if (document.getElementById('input4') !== null)document.getElementById('input4').value = ''
    if (document.getElementById('input5') !== null)document.getElementById('input5').value = ''
    if (document.getElementById('input6') !== null)document.getElementById('input6').value = ''
    this.setState({ visibleConfirmTaskWin: false, inputReviewRemark: '', confirmTaskConfigMap: {} })
  }

  handleCancelConfirmTaskWin = e => {
    this.hiddenModalConfirmTaskWin()
  }

  handleSubmitConfirmTaskWin = e => {
    const { isFlush, inputReviewRemark } = this.state

    if (!isFlush) {
      message.warn('未更新配置统计')
      return
    }

    let data = { remark: inputReviewRemark, list: [] }
    data.list = this.buildConfirmTaskConfig()
    if (data.length === 0) {
      message.error('配置任务值错误')
      return
    }

    this.props.dispatch({
      type: `${namespace}/approvalTaskConfig`,
      payload: data
    })

    this.hiddenModalConfirmTaskWin()
  }

  buildConfirmTaskConfig = () => {
    const { confirmTaskConfigMap } = this.state
    let list = []
    for (let k in confirmTaskConfigMap) {
      let value = confirmTaskConfigMap[k]

      // console.log(k, value, confirmTaskConfigMap)

      let configSealWaterBase = parseFloat(value.configSealWaterBase)
      if (value.configSealWaterBase === '' || isNaN(configSealWaterBase) || configSealWaterBase < 0 || this.wanToYuan(configSealWaterBase) % 100 > 0) {
        message.error('uid: ' + k + ' ' + '基础任务的任务值必须为[0, +∞), 支持一位小数')
        return []
      }

      let configRewardBase = parseFloat(value.configRewardBase)
      if (value.configRewardBase === '' || isNaN(configRewardBase) || configRewardBase < 0 || configRewardBase >= 9) {
        message.error('uid: ' + k + ' ' + '基础任务的奖励值必须为[0, 9), 支持一位小数')
        return []
      }

      let configSealWaterAdvanced = parseFloat(value.configSealWaterAdvanced)
      if (value.configSealWaterAdvanced === '' || isNaN(configSealWaterAdvanced) || configSealWaterAdvanced < 0) {
        message.error('uid: ' + k + ' ' + '进阶任务的任务值必须为[0, +∞), 支持一位小数')
        return []
      }

      let configRewardAdvanced = parseFloat(value.configRewardAdvanced)
      if (value.configRewardAdvanced === '' || isNaN(configRewardAdvanced) || configRewardAdvanced < 0 || configRewardAdvanced >= 9) {
        message.error('uid: ' + k + ' ' + '进阶任务的奖励值必须为[0, 9), 支持一位小数')
        return []
      }

      let configSealWaterSprint = parseFloat(value.configSealWaterSprint)
      if (value.configSealWaterSprint === '' || isNaN(configSealWaterSprint) || configSealWaterSprint < 0) {
        message.error('uid: ' + k + ' ' + '冲刺任务的任务值必须为[0, +∞), 支持一位小数')
        return []
      }

      let configRewardSprint = parseFloat(value.configRewardSprint)
      if (value.configRewardSprint === '' || isNaN(configRewardSprint) || configRewardSprint < 0) {
        message.error('uid: ' + k + ' ' + '冲刺任务的奖励值必须为[0, +∞), 支持一位小数')
        return []
      }

      let data = {
        uid: Number(k),
        configBase: { targetAmount: this.wanToYuan(configSealWaterBase), reward: this.wanToYuan(configRewardBase) },
        configAdvance: { targetAmount: this.wanToYuan(configSealWaterAdvanced), reward: this.wanToYuan(configRewardAdvanced) },
        configSprint: { targetAmount: this.wanToYuan(configSealWaterSprint), reward: this.wanToYuan(configRewardSprint) },
        remark: value.remark
      }

      // console.log(data)
      if (data.configAdvance.targetAmount > 0 && data.configAdvance.targetAmount < data.configBase.targetAmount) {
        message.error(data.uid + ' 进阶任务的任务值必须大于基础任务')
        return []
      }
      if (data.configSprint.targetAmount > 0 && data.configSprint.targetAmount < data.configAdvance.targetAmount) {
        message.error(data.uid + ' 冲刺任务的任务值必须大于进阶任务')
        return []
      }
      list.push(data)
    }
    return list
  }

  hiddenModalInvalidWin = () => {
    this.setState({ visibleInvalidWin: false })
  }

  handleCancelInvalidWin = e => {
    this.hiddenModalInvalidWin()
  }

  handleSubmitInvalidWin = e => {
    this.hiddenModalInvalidWin()
  }

  inputReviewRemarkHandle = () => e => {
    let value = e.target.value
    this.setState({ inputReviewRemark: value })
  }

  flushHandle = () => () => {
    let data = this.buildConfirmTaskConfig()
    if (data.length === 0) {
      message.error('配置任务值错误')
      return
    }

    this.props.dispatch({
      type: `${namespace}/flushConfirmTaskConfig`,
      payload: { list: data }
    })

    this.setState({ isFlush: true })
  }

  render () {
    const { visibleInvalidWin, visibleConfirmTaskWin, inputReviewRemark } = this.state
    const { model: { taskConfigList, taskConfigInvalidList, confirmTaskConfig, tableLoadingInvalidWin,
      tableLoadingConfirmTaskConfigWin, confirmTaskConfigInfo, confirmTaskConfigCal, totalTaskConfig, canJoinTaskConfig, hatKingTaskVirtualTime } } = this.props

    console.log(hatKingTaskVirtualTime)
    return (
      <Card>
        <span style={{ marginLeft: 15 }}>任务月份</span>
        <DatePicker picker='month' format='YYYY-MM' allowClear onChange={(v) => this.setState({ searchMonth: v })} style={{ marginLeft: 3 }} />

        <span style={{ marginLeft: 15 }}>短位ID</span>
        <InputNumber placeholder='请输入短位ID' onChange={e => this.setState({ searchASID: e })} style={{ width: 120, marginLeft: 3 }} />

        <span style={{ marginLeft: 15 }}>复核状态</span>
        <Select allowClear placeholder='请选择' style={{ width: 100, marginLeft: 3 }} onChange={(v) => this.setState({ searchReviewStatus: v })}>
          {reviewStatusList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}
        </Select>

        <span style={{ marginLeft: 15 }}>审批状态</span>
        <Select allowClear placeholder='请选择' style={{ width: 130, marginLeft: 3 }} onChange={(v) => this.setState({ searchApprovalStatus: v })}>
          {approvalStatusList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}
        </Select>

        <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
        <br />
        <Button style={{ marginTop: 10 }} type='primary' onClick={this.showConfirmTaskWin(null, true, true)}>配置[系统生成]</Button>
        <Button style={{ marginTop: 10, marginLeft: 20 }} type='primary' onClick={this.showConfirmTaskWin(null, true, false)}>配置[新授权]</Button>

        {/* <Popconfirm title='真的吗? 会清空当前所有任务配置' onConfirm={this.initSystemTaskConfigHandle()}> */}
        {/*  <Button style={{ marginTop: 10, marginLeft: 200 }} type='primary'>[测试]重新系统初始化任务配置</Button> */}
        {/* </Popconfirm> */}
        {/* <Search placeholder='[测试]输入虚拟时间, 格式:2006-01-02 15:04:05' style={{ marginTop: 10, width: 400, marginLeft: 10 }} onSearch={this.setVirtualTimeHandle} enterButton /> */}
        {/* <span style={{ marginTop: 10, marginLeft: 20 }}>当前虚拟时间: <font color='red'>{hatKingTaskVirtualTime !== null && hatKingTaskVirtualTime.length > 0 ? hatKingTaskVirtualTime : '使用当前时间'}</font></span> */}

        <Table bordered style={{ marginTop: 10 }} rowKey='idx' pagination={this.defaultPageValue} columns={this.columns} dataSource={taskConfigList} scroll={{ x: 'max-content' }} />

        <Modal keyboard={false} destroyOnClose forceRender width={1600} visible={visibleInvalidWin} title='历史记录' onCancel={this.handleCancelInvalidWin} onOk={this.handleSubmitInvalidWin}>
          <Card>
            <Table size='small' loading={tableLoadingInvalidWin} rowKey='idx' bordered dataSource={taskConfigInvalidList} columns={this.columnsInvalidWin} pagination={false} scroll={{ x: 'max-content' }} />
          </Card>
        </Modal>

        <Modal keyboard={false} destroyOnClose forceRender width={1600} visible={visibleConfirmTaskWin} title='任务确认' onCancel={this.handleCancelConfirmTaskWin} onOk={this.handleSubmitConfirmTaskWin}>
          <div><h1><font color='red'>{confirmTaskConfigInfo.title}</font></h1></div>
          <div>1. 整体情况: 授权帽子超级主持{totalTaskConfig}人, 获得任务主持 {canJoinTaskConfig} 人</div>
          <div>2. 任务整体情况: </div>
          <Table style={{ marginTop: 5, width: 1000 }} scroll={{ x: 'max-content' }} size='small' loading={tableLoadingConfirmTaskConfigWin} rowKey='idx' bordered dataSource={confirmTaskConfigCal} columns={this.columnsConfirmTaskSystem} pagination={false} />

          <div style={{ marginTop: 10 }} />
          <font color='red'>运营复核备注:</font>
          <TextArea placeholder='选填' autoSize={{ minRows: 2, maxRows: 4 }} style={{ height: 50, width: 500 }} value={inputReviewRemark} onChange={this.inputReviewRemarkHandle()} />

          <Button style={{ marginTop: 5, marginBottom: 5 }} type='primary' onClick={this.flushHandle()}>更新配置统计</Button>
          <Card>
            <Table size='small' loading={tableLoadingConfirmTaskConfigWin} rowKey='idx' bordered dataSource={confirmTaskConfig} columns={this.columnsConfirmTaskWin} pagination={false} scroll={{ x: 'max-content', y: 220 }} />
          </Card>
        </Modal>
      </Card>
    )
  }
}

export default TaskConfig
