import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/recommend_filter/op_get_zhuiya_recommend_black_list?${stringify(params)}`)
}

export function add (params) {
  return request(`/recommend_filter/op_add_zhuiya_recommend_black_list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function remove (params) {
  return request(`/recommend_filter/op_remove_zhuiya_recommend_black_list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function getExemptLists (params) {
  return request(`/recommend_filter/exempt_list?${stringify(params)}`)
}

export function addExempt (params) {
  return request(`/recommend_filter/add_exempt`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function removeExempt (params) {
  return request(`/recommend_filter/remove_exempt`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
