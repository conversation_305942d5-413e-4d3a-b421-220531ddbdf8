import { genGetListTemplate, genUpdateTemplate } from '@/utils/common'

const getCompensateConfig = genGetListTemplate('/claw_machine/admin/get_doll_config', 'configData')

const updateCompensateConfig = genUpdateTemplate('/claw_machine/admin/update_doll_config')
// const getAllPrizeList = genGetRequireTemplate('/claw_machine/admin/get_doll_config', 'globalPrizeList')

export default {
  namespace: 'clawMachinePrize',
  state: {
    configData: []
    // prizeList: [] // 准备废弃
    // globalPrizeList: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },

  effects: {
    getCompensateConfig,
    updateCompensateConfig
    // getAllPrizeList
  }
}
