import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Modal, Input, DatePicker, Select, Popconfirm } from 'antd'
import { connect } from 'dva'
import PopImage from '@/components/PopImage'
// import CSVExport from '@/components/CSVExport'
import MultiPicturesWall from '@/components/MultiPicturesWall'
import { exportExcel } from 'xlsx-oc'

var moment = require('moment')
const namespace = 'partnerGuild'
const FormItem = Form.Item
const Option = Select.Option

const getListUri = `${namespace}/getList`
const addItemUri = `${namespace}/addItem`
const updateItemUri = `${namespace}/updateItem`
const removeItemUri = `${namespace}/removeItem`
const searchItemUri = `${namespace}/searchItem`

@connect(({ partnerGuild }) => ({
  model: partnerGuild
}))

class PartnerGuild extends Component {
  // column structs.
  columns = [
    { title: '短位频道', dataIndex: 'asid', align: 'center' },
    { title: 'OW号', dataIndex: 'owId', align: 'center' },
    { title: 'OW昵称', dataIndex: 'owNick', align: 'center' },
    { title: '授权开始', dataIndex: 'authorizeStart', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD') },
    { title: '授权结束', dataIndex: 'authorizeEnd', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD') },
    { title: '合作区域', dataIndex: 'authorizeArea', align: 'center' },
    { title: '当前状态', dataIndex: 'confirmStatus', align: 'center', render: (text, record) => (text === 0 ? '未认证' : '已认证') },
    { title: '电子资料',
      dataIndex: 'imageList',
      align: 'center',
      export: false,
      render: (text, record) => (
        <div>
          {record.imageList.map((item, i) => <PopImage key={i} value={item} />)}
        </div>
      ) },
    { title: '操作人', dataIndex: 'operator', align: 'center' },
    { title: '操作时间', dataIndex: 'operateTime', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD HH:mm') },
    { title: '操作选项',
      align: 'center',
      export: false,
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(true, record)}>更新</a><Divider type='vertical' />
          <Popconfirm title={showDelTitle(record.asid)} onConfirm={this.handleDel(record.sid)} okText='是的' cancelText='暂不'>
            <a href=''>删除</a>
          </Popconfirm>
        </span>)
    }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  pagination = { pageSizeOptions: ['20', '50', '100'], showSizeChanger: true, pageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, title: '', searchKey: '' }
  defaultValue = { authorizeStart: moment().unix(), authorizeEnd: moment().unix(), confirmStatus: 1, imageList: [] }

  // show modal
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.authorizeStart = moment.unix(v.authorizeStart)
      v.authorizeEnd = moment.unix(v.authorizeEnd)
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // hide modal
  hideModal = () => {
    // const form = this.formRef.props.form
    // form.setFieldsValue({ imageList: [] })
    this.setState({ visible: false })
  }

  // update
  handleSubmit = () => {
    const form = this.formRef.props.form
    const { dispatch } = this.props
    form.validateFields((err, values) => {
      if (!err) {
        values.authorizeStart = values.authorizeStart.startOf('day').unix()
        values.authorizeEnd = values.authorizeEnd.endOf('day').unix()
        var url = this.state.isUpdate ? updateItemUri : addItemUri
        dispatch({
          type: url,
          payload: values
        })
        form.resetFields()
        this.setState({ visible: false })
      }
    })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { sid: key }
    dispatch({
      type: removeItemUri,
      payload: data
    })
  }

  // get list from server.
  componentDidMount () {
    const { dispatch, model: { list } } = this.props
    dispatch({
      type: getListUri
    })

    this.setState({ list })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  onExport = () => {
    const { model: { list } } = this.props
    var mp = { 0: '未认证', 1: '已认证' }
    var exportData = list.map(item => {
      item.confirmStatus = mp[item.confirmStatus]
      item.authorizeStart = moment.unix(item.authorizeStart).format('YYYY-MM-DD')
      item.authorizeEnd = moment.unix(item.authorizeEnd).format('YYYY-MM-DD')
      item.operateTime = moment.unix(item.operateTime).format('YYYY-MM-DD HH:mm')
      return item
    })

    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.export === undefined || col.export) {
        exportHeader.push({ k: col.dataIndex, v: col.title })
      }
    })

    exportExcel(exportHeader, exportData)
  }

  onSeach = () => {
    const { searchKey } = this.state
    const { dispatch } = this.props
    var data = { searchKey: searchKey }
    dispatch({
      type: searchItemUri,
      payload: data
    })
  }

  // content
  render () {
    const { route, model: { list } } = this.props
    const { visible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <div>
            <Input placeholder='输入内容搜索' onChange={e => this.setState({ searchKey: e.target.value })} style={{ width: 200 }} /> {/* 搜索按钮 */}
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onSeach}>查找</Button>
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.showModal(false, this.defaultValue)}>新增</Button>
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onExport}>导出</Button>
            <font style={{ marginLeft: 600 }} color='red'>注：状态为"已认证"的公会，可享有频道内及HGAME公会后台专属认证图标</font>
          </div>
          <Divider />
          <Table rowKey={(record, index) => index} dataSource={list} columns={this.columns} pagination={this.pagination} size='small' />
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} {...formItemLayout} ref={form => { this.formRef = form }}>
            <FormItem name='id'>
              <Input hidden />
            </FormItem>
            <FormItem label='短位频道' name='asid' rules={[{ required: true, message: '短位频道不能为空' }]}>
              <Input />
            </FormItem>
            <FormItem label='代表区域' name='authorizeArea' rules={[{ required: true, message: '合作区域不能为空' }]}>
              <Input placeholder='填写示例：华南+广东+广州' />
            </FormItem>
            <FormItem label='授权开始' name='authorizeStart' ruels={[{ required: true, message: '开始时间不能为空' }]}>
              <DatePicker format='YYYY-MM-DD' disabledDate={disabledDate} style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='授权结束' name='authorizeEnd' rules={[{ required: true, message: '结束时间不能为空' }]}>
              <DatePicker format='YYYY-MM-DD' disabledDate={disabledDate} style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='当前状态' name='confirmStatus'>
              <Select>
                <Option value={1}>已认证</Option>
                <Option value={0}>未认证</Option>
              </Select>
            </FormItem>
            <FormItem label='上传附件' name='imageList'>
              <MultiPicturesWall />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

function disabledDate (current) {
  // Can not select days before today
  return current && current < moment().subtract(1, 'days').endOf('day')
}

function showDelTitle (asid) {
  return '确定要取消' + String(asid) + '的城市合伙人认证图标吗？'
}

export default PartnerGuild
