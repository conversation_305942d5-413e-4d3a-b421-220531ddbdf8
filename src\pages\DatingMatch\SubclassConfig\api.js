import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/dating_match_bosssvr/v3/subclass_config_get?${stringify(params)}`)
}

export function add (params) {
  return request(`/dating_match_bosssvr/v3/subclass_config_add?${stringify(params)}`)
}

export function remove (params) {
  return request(`/dating_match_bosssvr/v3/subclass_config_remove?${stringify(params)}`)
}
