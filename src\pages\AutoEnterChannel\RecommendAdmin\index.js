import React, { Component } from 'react'
// import dateString from '@/utils/dateString'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Form, Card, Switch } from 'antd'
import { connect } from 'dva'
// import ReactDOM from 'react-dom'

// const Search = Input.Search
// const { Option } = Select
// var moment = require('moment')
const namespace = 'auctionHotPush'
const getListUri = `${namespace}/getList`
const updateItemUri = `${namespace}/updateItem`
// const RadioGroup = Radio.Group
@connect(({ auctionHotPush }) => ({
  model: auctionHotPush
}))
class AuctionHotPush extends Component {
  onChange = e => checked => {
    const { dispatch } = this.props
    console.log('check:', checked)
    console.log('e:', e)
    e.open = checked ? 1 : 0
    // e.id = e.id
    var url = updateItemUri
    dispatch({
      type: url,
      payload: e
    })
  }

  // 列表结构
  columns = [
    { title: '#',
      dataIndex: 'index',
      align: 'center',
      render: (text, record) => (
        text === 3 ? '单独开启' : text
      )
    },
    { title: '项目', dataIndex: 'title', align: 'center' },
    { title: '开关状态',
      dataIndex: 'open',
      align: 'center',
      render: (text, record) => (
        { ...<div>
          <Switch className={String(record.index)}
            key={Math.random()}
            defaultChecked={text === 1}
            checkedChildren='开'
            unCheckedChildren='关'
            onChange={this.onChange(record)} />
          <br />
        </div>
        }
      )
    }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  defaultValue = { url: null }

  state = { visible: false, isUpdate: false, value: {} }

  // 获取列表
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: getListUri
    })
    this.setState({})
  }

  // 添加 与 编辑
  handleSubmit = () => {
    const form = this.formRef.props.form
    const { dispatch } = this.props
    form.validateFields((err, values) => {
      if (!err) {
        // transfer Moment object to timestamp !!!
        var url = updateItemUri
        console.log('submit:', values)
        dispatch({
          type: url,
          payload: values
        })
        form.resetFields()
        this.setState({ visible: false })
      }
    })
  }

  saveFormRef = formRef => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list } } = this.props
    console.log('index list:', list)

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Divider />
            <Divider><span>注：推荐列表无法由多个项目同时控制，故只能控制单个开启状态。</span></Divider>
            <Table dataSource={list} columns={this.columns} rowKey={(record, index) => index} pagination={this.defaultPageValue} />
          </Form>
        </Card>
      </PageHeaderWrapper>
    )
  }
}
export default AuctionHotPush
