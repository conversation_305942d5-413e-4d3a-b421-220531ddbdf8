import request from '@/utils/request'
import { stringify } from 'qs'

export function getHatkingCrucialInfo (params) { // 关键信息
  return request(`/dragon_slaying/lottery_hatking_crucial_info?${stringify(params)}`, { jsonp: true })
}

export function getHatkingDailyInfo (params) { // 每日玩法信息
  return request(`/dragon_slaying/lottery_hatking_daily_info?${stringify(params)}`, { jsonp: true })
}

export function getHatkingComboInfo (params) { // 连中信息
  return request(`/dragon_slaying/lottery_hatking_combo_info?${stringify(params)}`, { jsonp: true })
}

export function getHatkingModeStatsInfo (params) { // 总奖池返奖监控
  return request(`/dragon_slaying/get_hatking_daily_mode_stats?${stringify(params)}`, { jsonp: true })
}

export function getHatkingUpgradeCrucialInfo (params) { // 高倍场返奖监控
  return request(`/dragon_slaying/get_hatking_upgrade_daily_crucial_info?${stringify(params)}`, { jsonp: true })
}
