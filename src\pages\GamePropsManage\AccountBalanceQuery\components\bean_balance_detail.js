import React, { Component } from 'react'
import { Table, Divider, But<PERSON>, Card, Form, Input } from 'antd'
import { connect } from 'dva'
import exportExcel from '@/utils/exportExcel'

@connect(({ gamePropsQuery, loading }) => ({ // model 的 namespace
  model: gamePropsQuery, // model 的 namespace
  loading: loading.effects['gamePropsQuery/getBeanDetailLists']
}))
class BeanBalanceDetailComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {}
    }
  }

  componentDidMount () {
    this.setState()
  }

  columns = [
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '布料数量', dataIndex: 'count', align: 'center' },
    { title: '活动布料数量', dataIndex: 'actCount', align: 'center' }
    // { title: '最后更新时间', dataIndex: 'lastUpdate', align: 'center', render: (text, record) => (text = moment.unix(record.lastUpdate).format('YYYY-MM-DD HH:MM:ss')) }
  ]

  pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  loadData = () => {
    const { dispatch } = this.props
    const { uid } = this.state
    const data = { uid: uid }
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getBeanDetailLists`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    this.setState({ dateRange: date })
  }

  onExport = () => {
    let headers = []
    this.columns.forEach(function (item) {
      headers.push({ key: item.dataIndex, header: item.title })
    })

    const { model: { beanDetailList } } = this.props
    var exportData = beanDetailList.map(item => {
      let v = $.extend(true, {}, item)
      return v
    })

    exportExcel(headers, exportData)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { beanDetailList }, loading } = this.props

    return (
      <Card>
        <Form>
          <Input.Group compact>
            <span style={{ marginTop: 5 }}>UID:</span>
            <Input.TextArea placeholder='批量查询请以逗号分隔，如:1,2' rows={1} onChange={e => this.setState({ uid: e.target.value })} style={{ marginLeft: 10, width: 150 }} /> {/* 搜索按钮 */}
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onClick}>查询</Button>
            <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
          </Input.Group>
          <Divider />
          <Table loading={loading} dataSource={beanDetailList} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
        </Form>
      </Card>
    )
  }
}

export default BeanBalanceDetailComponent
