import React, { Component } from 'react'
import <PERSON>Header<PERSON>rapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import { connect } from 'dva'
import AwardConfirm from './components/award_confirm.js'
import RewardSummaryReport from './components/reward_summary_report.js'
import TaskProgressReport from './components/task_progress_report.js'
import AwardDetail from './components/award_detail.js'

const TabPane = Tabs.TabPane

@connect(({ guildTaskRevision }) => ({ // model 的 namespace
  model: guildTaskRevision // model 的 namespace
}))
class Index extends Component { // 默认页面组件，不需要修改
  /** *******************************页面布局*************************************************************/
  render () {
    const { route, model: { rewardDetailList, taskProgressList, awardConfirmList, awardDetailList } } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs type='card' defaultActiveKey='1' >
          <TabPane tab='发奖确认' key='1'>
            <AwardConfirm dataInfo={awardConfirmList} navGroup={1} />
          </TabPane>
          <TabPane tab='任务进度报表' key='2'>
            <TaskProgressReport dataInfo={taskProgressList} navGroup={2} />
          </TabPane>
          <TabPane tab='发奖金额汇总' key='3'>
            <RewardSummaryReport dataInfo={rewardDetailList} navGroup={3} />
          </TabPane>
          <TabPane tab='发奖明细' key='4'>
            <AwardDetail dataInfo={awardDetailList} navGroup={4} />
          </TabPane>
        </Tabs>
      </PageHeaderWrapper>
    )
  }
}

export default Index // 保证唯一
