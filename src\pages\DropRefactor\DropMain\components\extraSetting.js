import React, { Component } from 'react'
import { Typo<PERSON>, Modal, Switch, Input, Form, Select, Divider, Button, Popover, InputNumber, message } from 'antd'
import { InfoBoard } from '@/components/SimpleComponents'
import { propTypeOptions } from '../../dropCommon'
import { broadcastOptionsAll } from '../components/list_common'

const { Link, Text } = Typography
const { Item } = Form
const defaultValue = { isOpen: false, rate: 0, propsType: 0, broadCastType: 0, dailyLimit: 0, hoursLimit: 0, valueLimit: 0 }

// 翻倍模式配置
class ExtraCfgButton extends Component {
  state = {
    editValue: { isOpen: false, rate: 0, propsType: 0, broadCastType: 0, dailyLimit: 0, hoursLimit: 0, valueLimit: 0 },
    modalVisible: false
  }

  componentDidMount = () => {
    const { value } = this.props
    this.formRef.setFieldsValue(value)
    this.fromRefMonitor(value)
  }

  // 弹出编辑模态框, 会重置数据
  showEditerModal = () => {
    const { value } = this.props
    this.fromRefMonitor(value || defaultValue)
    this.setState({ modalVisible: true })
  }

  // 关闭编辑模态框
  closeEditerModal = () => {
    this.setState({ modalVisible: false })
  }

  // 表单每次更新时调用, 维护临时配置值
  fromRefMonitor = (v) => {
    if (!v) return ''
    this.setState({ editValue: v })
    const cp = { ...v }
    this.formRef.setFieldsValue(cp)
  }

  // 更新外层配置值
  comfirmUpdateOuterValue = () => {
    const { onChange } = this.props
    const { editValue } = this.state
    let reason = this.checkFromValue(editValue)
    if (reason) {
      message.warn(reason)
      return
    }
    this.setState({ modalVisible: false })
    onChange(editValue)
  }

  // 检查参数
  checkFromValue = (v) => {
    if (v.rate < 0 || v.rate > 100) {
      return '概率不合法'
    }
    return ''
  }

  compereValue = (before, after) => {
    const { diffMode } = this.props
    if (!diffMode) return after
    if (before === undefined) before = ''
    if (before === after) return <Text>{after}</Text>
    return <Text type='danger'><Text type='secondary'>{`${before} => `}</Text>{after}</Text>
  }

  // 稀有度格式化
  propsTypeFormater = (v) => {
    return propTypeOptions.find(item => item.value === v)?.label
  }

  // 广播类型
  broadCastTypeFormater = (v) => {
    return broadcastOptionsAll.map(i => i.label)[v]
  }

  render () {
    const { value: initvalValue, cmpValue, isEdit, propsName, diffMode, broadcastOptions } = this.props
    const { modalVisible, editValue } = this.state
    const fixCmpVal = cmpValue || defaultValue
    const infoColumns = [
      { label: '礼物名称', dataIndex: 'name', span: 24, render: (v) => propsName },
      { label: '是否开启', dataIndex: 'isOpen', span: 24, render: (v) => this.compereValue(fixCmpVal?.isOpen ? '开启' : '关闭', v ? '开启' : '关闭') },
      { label: '翻倍概率', dataIndex: 'rate', span: 24, render: (v) => this.compereValue(`${fixCmpVal?.rate}%`, `${v}%`) },
      { label: '日投放上限', dataIndex: 'dailyLimit', span: '24', render: (v) => this.compereValue(fixCmpVal?.dailyLimit, v) },
      { label: '2H投放上限', dataIndex: 'hoursLimit', span: '24', render: (v) => this.compereValue(fixCmpVal?.hoursLimit, v) },
      { label: '金额限制', dataIndex: 'valueLimit', span: '24', render: (v) => this.compereValue(fixCmpVal?.valueLimit, v) },
      { label: '稀有度', dataIndex: 'propsType', span: 24, render: (v) => this.compereValue(this.propsTypeFormater(fixCmpVal?.propsType), this.propsTypeFormater(v)) },
      { label: '广播类型', dataIndex: 'broadCastType', span: 24, render: (v) => this.compereValue(this.broadCastTypeFormater(fixCmpVal?.broadCastType), this.broadCastTypeFormater(v)) }
    ]

    const infoBoxContent = <div style={{ width: '20em' }}><InfoBoard key={initvalValue} columns={infoColumns} dataSource={initvalValue || {}} divider={false} /></div>

    let placeholder = ''
    let isChangeTip = ''
    if (diffMode && JSON.stringify(initvalValue) !== JSON.stringify(cmpValue)) {
      isChangeTip = <Text type='danger'>*</Text>
    }

    if (!isEdit) { // 查看模式
      placeholder = <>{ initvalValue == null || initvalValue.isOpen === false
        ? <Popover placement='leftTop' trigger='hover' content={isChangeTip === '' ? '关闭' : infoBoxContent}>
          <Text type='secondary'>{isChangeTip}关闭</Text>
        </Popover>
        : <Popover placement='leftTop' trigger='hover' content={infoBoxContent}><Text type='success'>{isChangeTip}开启</Text></Popover>
      }</>
    }
    if (isEdit) { // 编辑模式
      placeholder = <>{ initvalValue && initvalValue.isOpen
        ? <Text>已开启, <Link onClick={() => this.showEditerModal()}>编辑</Link></Text>
        : <Button type='link' onClick={() => this.showEditerModal()}>开启</Button>}</>
    }

    return (
      <div>
        {placeholder}
        <Modal forceRender title='翻倍配置' visible={modalVisible}
          onCancel={() => this.closeEditerModal()}
          onOk={() => { this.comfirmUpdateOuterValue() }} >
          <Form labelCol={{ span: 9 }} forceRender ref={from => { this.formRef = from }} initialValues={defaultValue} onFinish={(v) => { console.debug(v) }} onValuesChange={(v, r) => this.fromRefMonitor(r)} >
            <Item label='是否开启翻倍' name='isOpen' getValueFromEvent={(v) => { return !!v }} valuePropName='checked' >
              <Switch checkedChildren='开启' unCheckedChildren='关闭' />
            </Item>
            <div hidden={editValue && editValue.isOpen === false}>
              <Divider />
              <Item label='礼物名称'>
                <Input disabled value={propsName} />
              </Item>
              <Item label='翻倍概率' name='rate' rules={[{ required: true }]} tooltip='当获得该礼物后，有N%的概率获得该礼物的翻倍'>
                <InputNumber />
              </Item>
              <Item label='日上限' name='dailyLimit' rules={[{ required: true }]} tooltip='该礼物可进行翻倍的每日上限；翻倍产出的礼物不占用该礼物现有的日上限'>
                <InputNumber />
              </Item>
              <Item label='2H上限' name='hoursLimit' rules={[{ required: true }]} tooltip='该礼物可进行翻倍的每2H上限'>
                <InputNumber />
              </Item>
              <Item label='金额限制' name='valueLimit' rules={[{ required: true }]} tooltip='用户在该业务空投中当日消费大于X元才能中该礼物的翻倍'>
                <InputNumber />
              </Item>
              <Item label='稀有度' name='propsType' rules={[{ required: true }]}>
                <Select options={propTypeOptions} />
              </Item>
              <Item label='广播类型' name='broadCastType' rules={[{ required: true }]}>
                <Select options={broadcastOptions} />
              </Item>
            </div>

          </Form>
        </Modal>
      </div>
    )
  }
}

export default ExtraCfgButton
