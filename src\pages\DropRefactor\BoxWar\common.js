import React from 'react'
import { Collapse } from 'antd'

export function BowWarTipDiv ({ isShow }) {
  if (!isShow) { // TODO: 临时隐藏使用提示,后面可能要还原
    return ''
  }
  return (<Collapse ghost>
    <Collapse.Panel header={<a>点击展开使用提示</a>} showArrow={false}>
      <div style={{ backgroundColor: 'aliceblue' }} >
        <div>2. 金额限制 用户今日抢空投消耗的金额(今日已空投次数*2000)不低于限制金额才能抽中当前礼物，单位：紫水晶/红钻/红贝</div>
        <div>3. 投放上限 -1表示无上限 0表示不发放 大于0表示最多发放数量</div>
        <div>4. A-超预估后出现的1个金额/元 B-超预估后新增的数量 C-今日已发放数量 D-今日剩余发放数量</div>
        <div>5. 非设限部分发放占比：日投放无上限的礼物价值×概率÷（总概率×单次抽取道具金额）×100% </div>
        <div>6. 设限部分总金额：日投放有上限礼物价值×日投放数量/1000（注：单位为元）</div>
        <div>7. 字段更新说明：红色为审核更新后，括号内为审核更新前</div>
        <div>8. 组合上限：含钻石/至尊/王者组合上限，表示各个奖励在单个组合中发出的上限数；且该组合上限与日上限不耦合</div>
      </div>
    </Collapse.Panel>
  </Collapse>)
}

// 计算组合大礼的平均价值
export function CalculateAverage (poolConfig, bundleConfig, bundleID) {
  if (!Array.isArray(poolConfig) || !bundleConfig) {
    return '???'
  }

  const { bundleA, bundleB, bundleC } = bundleConfig
  const bundleInfo = [bundleA, bundleB, bundleC][bundleID - 1]
  if (!bundleInfo) {
    return '???'
  }
  const { count: bundleCount } = bundleInfo // 多少抽

  const maxVal = 999999999
  let totalPrice = 0 // 所有礼物的价值
  let totalRate = 0 // 所有礼物普通概率之和
  let minPrice = maxVal// 最小的礼物价值

  poolConfig.forEach(item => {
    const { rate, value, count } = item
    totalRate += rate
    if (minPrice > value * count) {
      minPrice = value * count
    }
  })

  poolConfig.forEach(item => {
    const { boxWarSetting: { limitA, limitB, limitC }, rate, value, count } = item
    let limit = [limitA, limitB, limitC][bundleID - 1]
    if (limit < 0) {
      limit = bundleCount
    }

    let tmpRate = rate / totalRate // 当前礼物的概率
    let tmpCount = tmpRate * bundleCount // 忽略上限时当前的礼盒能出多少个当前奖励道具
    if (tmpCount <= limit) {
      totalPrice += tmpCount * (value * count)
      // console.info(`礼物名称=${item.propsName} 抽中${tmpCount.toFixed(2)}个 totalPrice=${totalPrice.toFixed(2)}`)
    } else {
      totalPrice += limit * (value * count)
      totalPrice += (tmpCount - limit) * minPrice
      // console.info(`礼物名称=${item.propsName} 抽中${limit}个 替换${(tmpCount - limit).toFixed(2)}个 totalPrice=${totalPrice.toFixed(2)}`)
    }
  })

  // console.info(`组合ID=${bundleID} 抽数=${bundleCount} 替换礼物价值=${minPrice} 最终总价值=${totalPrice.toFixed(2)}`)
  return `${(totalPrice / 1000).toFixed(0)}元`
}

// 计算组合大礼的最大价值
export function CalculateMaxValue (poolList, bundleConfig, bundleID) {
  const poolConfig = [...poolList]
  if (!Array.isArray(poolConfig) || !bundleConfig) {
    return '???'
  }
  const { bundleA, bundleB, bundleC } = bundleConfig
  const bundleInfo = [bundleA, bundleB, bundleC][bundleID - 1]
  if (!bundleInfo) {
    return '???'
  }

  const MAX = 999999999
  let unLimitValue = 0 // 无上限的礼物的价值
  let minValue = MAX // 道具池中价值最低的礼物价值
  const { count: bundleCount } = bundleInfo // 多少抽

  poolConfig.forEach((item) => {
    const { boxWarSetting: { limitA, limitB, limitC }, value, count } = item
    const limit = [limitA, limitB, limitC][bundleID - 1]
    if (limit < 0) {
      unLimitValue = value * count
    }
    if (value * count < minValue) {
      minValue = value * count
    }
  })

  poolConfig.sort((a, b) => { return b.value * b.count - a.value * a.count }) // 礼物价值降序排序

  let remain = bundleCount
  let maxValue = 0

  // 按照贪心算法,不断抽走最高价值的礼物
  for (let index in poolConfig) {
    const { boxWarSetting: { limitA, limitB, limitC }, value, count } = poolConfig[index]
    const limit = [limitA, limitB, limitC][bundleID - 1]
    if (limit < 0) {
      continue
    }
    if (limit >= remain) {
      maxValue += remain * value * count
      remain -= remain
      break
    }
    maxValue += limit * value * count
    remain -= limit
  }
  // 若仍有剩余,则用价值最低的礼物或无上限的礼物替代
  if (remain > 0 && unLimitValue) {
    maxValue += remain * unLimitValue
    remain = 0
  }
  if (remain > 0 && minValue !== MAX) {
    maxValue += remain * minValue
    remain = 0
  }

  console.debug(`bundleCount=${bundleCount} remain=${remain} minValue=${minValue} unLimitValue=${unLimitValue}`)

  return `${(maxValue / 1000).toFixed(0)}元`
}
