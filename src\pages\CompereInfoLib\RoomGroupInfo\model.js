import * as api from './api'
import { message } from 'antd'

export default {
  namespace: 'roomGroup',

  state: {
    list: []
  },

  reducers: {
    displayList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * listRoomGroup ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listRoomGroup, payload)
      console.log(status, msg)
      // let data = [{ id: '111', business: 1, uid: 11111, yy: 222, nickname: '123', sid: 12, asid: 3434, signStartTime: 1622432191, signEndTime: 1622518591, createRoomSum: 10, joinRoomCompereSum: 11, inboundTime: 1622432191, optUser: 'dw_liangjiefan' }]
      data = Array.isArray(data) ? data : []
      for (let i = 0; i < data.length; i++) {
        data[i].idx = i + 1
      }
      console.log(data)
      yield put({
        type: 'displayList',
        payload: data
      })
    },

    * removeRoomGroup ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.removeRoomGroup, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: '出库失败' })
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listRoomGroup',
        payload: {}
      })
    },

    * importRoomGroup ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.importRoomGroup, payload)
      console.log(status, msg)
      if (status !== 0) {
        if (status === 10001 || status === 10002 || status === 10003) {
          message.error({ content: msg })
          return
        }
        message.error({ content: '手工导入失败' })
      } else {
        message.success('导入成功')
      }
      yield put({
        type: 'listRoomGroup',
        payload: {}
      })
    }
  }
}
