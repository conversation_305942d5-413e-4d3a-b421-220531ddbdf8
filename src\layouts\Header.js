import React, { PureComponent } from 'react'
import { Layout } from 'antd'
import Animate from 'rc-animate'
import { connect } from 'dva'
import GlobalHeader from '@/components/GlobalHeader'
import styles from './Header.module.less'
import { logout } from '@yy/fr-auth'
// import Authorized from '@/utils/Authorized'

const { Header } = Layout

class HeaderView extends PureComponent {
  state = {
    visible: true
  };

  static getDerivedStateFromProps (props, state) {
    if (!props.autoHideHeader && !state.visible) {
      return {
        visible: true
      }
    }
    return null
  }

  componentDidMount () {
    document.addEventListener('scroll', this.handScroll, { passive: true })
  }

  componentWillUnmount () {
    document.removeEventListener('scroll', this.handScroll)
  }

  getHeadWidth = () => {
    const { collapsed, fixedHeader } = this.props
    if (!fixedHeader) {
      return '100%'
    }
    return collapsed ? 'calc(100% - 80px)' : 'calc(100% - 256px)'
  }

  handleMenuClick = ({ key }) => {
    // const { dispatch } = this.props

    if (key === 'logout') {
      // 这里如果是home的话，不会弹出登录框来，所以截取掉
      let afterLogoutUrl = window.location.href
      if (afterLogoutUrl.endsWith('/n/home')) {
        afterLogoutUrl = afterLogoutUrl.substring(0, afterLogoutUrl.length - '/n/home'.length)
      }
      logout(afterLogoutUrl)
    }
  };

  handScroll = () => {
    const { autoHideHeader } = this.props
    const { visible } = this.state
    if (!autoHideHeader) {
      return
    }
    const scrollTop = document.body.scrollTop + document.documentElement.scrollTop
    if (!this.ticking) {
      window.requestAnimationFrame(() => {
        if (this.oldScrollTop > scrollTop) {
          this.setState({
            visible: true
          })
          this.scrollTop = scrollTop
          return
        }
        if (scrollTop > 300 && visible) {
          this.setState({
            visible: false
          })
        }
        if (scrollTop < 300 && !visible) {
          this.setState({
            visible: true
          })
        }
        this.oldScrollTop = scrollTop
        this.ticking = false
      })
    }
    this.ticking = false
  };

  render () {
    const { handleMenuCollapse, fixedHeader } = this.props
    const { visible } = this.state
    const width = this.getHeadWidth()
    const HeaderDom = visible ? (
      <Header style={{ padding: 0, width }} className={fixedHeader ? styles.fixedHeader : ''}>
        <GlobalHeader
          onCollapse={handleMenuCollapse}
          onMenuClick={this.handleMenuClick}
          {...this.props}
        />
      </Header>
    ) : null
    return (
      <Animate component='' transitionName='fade'>
        {HeaderDom}
      </Animate>
    )
  }
}

export default connect(({ global }) => ({
  // currentUser: user.currentUser,
  ...global
}))(HeaderView)
