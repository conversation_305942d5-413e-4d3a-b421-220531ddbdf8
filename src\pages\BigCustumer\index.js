import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Modal, Input, DatePicker, Popconfirm, message } from 'antd'
import { connect } from 'dva'
import { DeleteOutlined } from '@ant-design/icons'
// import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
var moment = require('moment')
// const Search = Input.Search
const namespace = 'bigCustumer'
const FormItem = Form.Item
const TextArea = Input.TextArea
const RangePicker = DatePicker.RangePicker
// const Option = Select.Option

// const dateFormat = 'YYYY-MM-DD hh:mm:ss'
const approvalDescMap = {
  0: '审核中',
  1: '审核拒绝',
  2: '审核通过'
}
@connect(({ bigCustumer }) => ({
  model: bigCustumer
}))

class Index extends Component {
  columns = [
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: 'YY', dataIndex: 'imid', align: 'center' },
    { title: '发放原因', dataIndex: 'reason', align: 'center' },
    { title: '特权名称', dataIndex: 'privilegeName', align: 'center' },
    { title: '特权生效开始时间', dataIndex: 'startTime', align: 'center', render: text => this.dateString(text) },
    { title: '特权生效结束时间', dataIndex: 'endTime', align: 'center', render: text => this.dateString(text) },
    { title: '审核状态', dataIndex: 'approvalStatus', align: 'center', render: text => approvalDescMap[text] },
    // { title: '添加时间', dataIndex: 'addTime', align: 'center', render: text => this.dateString(text) },
    { title: '添加人', dataIndex: 'opUser', align: 'center' },
    { title: '最后修改时间', dataIndex: 'timestamp', align: 'center', render: text => this.dateString(text) },
    { title: '操作',
      align: 'center',
      render: (text, record) => (
        <span>
          {/* <a><EditOutlined style={{ marginRight: 10 }} onClick={this.showModal(true, record)} /></a> */}
          <Popconfirm onConfirm={this.handleDel(record.id)} title='确认删除？'><a><DeleteOutlined style={{ color: 'red' }} /></a></Popconfirm>
        </span>)
    }
  ]

  pagination = { pageSizeOptions: ['20', '50', '100'], showSizeChanger: true, pageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }
  state = { visible: false, isUpdate: false, value: {}, appStatus: 0, searchResult: [], searchDone: false }

  showModal = (isUpdate, record) => () => {
    if (record == null) record = { appId: 0, appStatus: 0 }

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? '更新' : '添加' })
  }

  hideModal = () => {
    this.setState({ visible: false })
  }

  dateString (timestamp) {
    if (timestamp === 0) {
      return '-'
    }
    return moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss')
  }

  handleSubmit = e => {
    this.formRef.submit()
    // this.setState({ visible: false })
  }

  onFinish = values => {
    const { dispatch } = this.props
    const { isUpdate } = this.state
    console.log('onFinish', values)
    if (values.reason === '') {
      return
    }
    // values.uid = parseInt(values.uid)
    var data = { startTime: moment(values.timeRange[0]).unix(), endTime: moment(values.timeRange[1]).unix(), reason: values.reason, uids: values.uids }
    dispatch({
      type: isUpdate ? `${namespace}/updateItem` : `${namespace}/addItem`,
      payload: data
    }).then(res => {
      this.getList()
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  getList () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`
    })
  }

  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { id: key }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  handleSearch = (selectedKeys, confirm) => () => {
    confirm()
    this.setState({ searchText: selectedKeys[0] })
  }

  handleReset = clearFilters => () => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`
    })
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  // onChange = (date, format) => {
  //   this.setState({ dateRange: date })
  // }

  onSelectChange = (selectedRowKeys, selectedRows) => {
    const value = selectedRows.map(item => item.uid).join(',')
    console.log('onSelectChange:', selectedRowKeys, value)
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  // 时间选择检查
  checkSelectTime = (tr) => {
    if (tr === null || tr === undefined || tr.length < 2) {
      message.warn('未选择时间')
    }
  }

  render () {
    const { route, model: { list } } = this.props
    const { searchResult, searchDone, visible, title, isUpdate } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Button type='primary' onClick={this.showModal(false)}>添加</Button>
            <font style={{ marginLeft: 10 }} color='red'>配置大客户VIP贵宾通道</font>
            <Divider />
            <Table dataSource={searchDone ? searchResult : list} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem label='UID' name='uids' rules={[{ required: true }]}>
              {/* <Input readOnly={isUpdate} /> */}
              <TextArea disabled={isUpdate} rows={12} placeholder='一行一个uid' />
            </FormItem>
            <FormItem label='发放原因' name='reason' rules={[{ required: true }]}>
              <Input />
            </FormItem>
            <Form.Item label='有效时间范围' name='timeRange' rules={[{ required: true }]}>
              <RangePicker format={'YYYY-MM-DD HH:mm:ss'} showTime={{ format: 'HH:mm:ss' }} onChange={(t) => this.checkSelectTime(t)} />
            </Form.Item>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default Index
