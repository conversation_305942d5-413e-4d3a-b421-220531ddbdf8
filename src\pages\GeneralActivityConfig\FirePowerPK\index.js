import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Row, Table, Divider, Typography, Tooltip, Button, message, DatePicker } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'

const namespace = 'firePowerPK'
var moment = require('moment')

@connect(({ firePowerPK }) => ({
  model: firePowerPK
}))

class FirePowerPK extends Component {
  state = {
    searchTimeRange: [moment().startOf('day').add(-7, 'd'), moment().startOf('day').add(-1, 'd')],
    searchTimeRange2: [moment().startOf('day').add(-14, 'd'), moment().startOf('day').add(-1, 'd')]
  }
  componentDidMount = () => {
    this.queryList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // 表头显示提示语
  genColumnTooltip = (title) => {
    return {
      filterDropdown: (<span />),
      filterIcon: (
        <Tooltip placement='top' title={title}>
          <QuestionCircleOutlined style={{ fontSize: '16px' }} />
        </Tooltip>
      )
    }
  }

  // 请求日报数据和周报数据
  queryList =(tag) => {
    this.queryDailyState()
    this.queryWeeklyState()
  }
  // 查询日报数据
  queryDailyState = () => {
    const { searchTimeRange } = this.state
    if (!searchTimeRange) {
      message.warn('请先设置时间范围')
      return
    }
    let startDate = searchTimeRange[0].format('YYYYMMDD')
    let endDate = searchTimeRange[1].format('YYYYMMDD')
    this.callModel('getDailyList', {
      params: { startDate: startDate, endDate: endDate, tag: 'DAILY' },
      cbFunc: (ok) => { ok ? message.success('获取玩法日报数据成功') : message.warn('获取玩法日报数据失败~') }
    })
  }
  // 查询周报数据
  queryWeeklyState = () => {
    const { searchTimeRange2 } = this.state
    if (!searchTimeRange2) {
      message.warn('请先设置时间范围')
      return
    }
    let startDate = searchTimeRange2[0].format('YYYYMMDD')
    let endDate = searchTimeRange2[1].format('YYYYMMDD')
    this.callModel('getWeeklyList', {
      params: { startDate: startDate, endDate: endDate, tag: 'WEEKLY' },
      cbFunc: (ok) => { ok ? message.success('获取营收周报数据成功') : message.warn('获取营收周报数据失败~') }
    })
  }

  render () {
    const { route } = this.props
    const { Title } = Typography
    const { dailyState, weeklyState } = this.props.model // 活动统计数据、营收统计数据
    const bonusToUnitYB = 0.01 // 100赏金值=1YB
    const crystalToUnitYB = 0.001 // 1000蓝水晶/1000黄水晶=1YB
    const actColumns = [
      { title: '日期', dataIndex: '', render: (v, r) => { return moment.unix(r.startTime).format('YYYY-MM-DD HH:mm') } },
      // { title: '日期', dataIndex: '', render: (v, r) => { return moment.unix(r.endTime).format('YYYY-MM-DD HH:mm') } },
      { title: '总场次', dataIndex: 'totalGame', ...this.genColumnTooltip('当日成功开启“火力大比拼”的次数') },
      { title: '主持数', dataIndex: 'totalJoin', ...this.genColumnTooltip('当日成功开启“火力大比拼”的主持uid去重') },
      { title: '有效场次', dataIndex: 'validGame', ...this.genColumnTooltip('当日成功开启“火力大比拼”，且PK结束时有发放道具的PK次数') },
      { title: '有效场次主持数', dataIndex: 'validJoin', ...this.genColumnTooltip('有效场次的主持uid去重') },
      { title: '有效场次占比', ...this.genColumnTooltip('有效场次/总场次'), render: (v, r) => { return `${r.totalGame === 0 ? 0 : (r.validGame * 100 / r.totalGame).toFixed(2)}%` } },
      { title: '总火力值', dataIndex: 'firePower', ...this.genColumnTooltip('“火力大比拼”产生的火力值总数') },
      { title: '总发放道具(元)', dataIndex: 'bonus', ...this.genColumnTooltip('火力大比拼”总发放道具金额，数值单位“元”'), render: (v) => { return (bonusToUnitYB * v).toFixed(2) } },
      { title: '任务总奖励(元)', dataIndex: 'taskBonus', ...this.genColumnTooltip('火力大比拼”完成任务发放的奖励总额，数值单位“元”'), render: (v) => { return (bonusToUnitYB * v).toFixed(2) } },
      { title: '宝箱总奖励(元)', dataIndex: 'overflowBonus', ...this.genColumnTooltip('火力大比拼”非任务部分的火力值奖励发放总额，数值单位“元”'), render: (v) => { return (bonusToUnitYB * v).toFixed(2) } },
      { title: '系统加成奖金金额(元)', dataIndex: 'systemBonus', ...this.genColumnTooltip('系统在“火力大比拼”宝箱投入总额，数值单位“元”'), render: (v) => { return (bonusToUnitYB * v).toFixed(2) } },
      { title: '专属礼物总流水(元)', dataIndex: 'totalGiftFlow', ...this.genColumnTooltip('交友业务产生的火力比拼专属礼物流水总额，数值单位“元”'), render: (v) => { return (crystalToUnitYB * v).toFixed(2) } }, // 产品说: 专属礼物的统计，应该是产生火力值的礼物，而非“蓝水晶礼物：，因为产生火力值的礼物来源不只是蓝水晶
      { title: 'PK专属礼物流水(元)', dataIndex: 'pkGiftFlow', ...this.genColumnTooltip('”火力大比拼”过程中产生火力值的礼物流水总额，数值单位“元”'), render: (v) => { return (crystalToUnitYB * v).toFixed(2) } } // 产品说: 专属礼物总流水，指火力PK和非火力PK，产生的这批指定礼物流水加和；PK专属礼物流水，指火力PK期间，产生的这批指定礼物流水
    ]
    const revenueColumns = [
      { title: '日期', dataIndex: '', ...this.genColumnTooltip('开始日期-结束日期'), render: (v, r) => { return `${moment.unix(r.startTime).format('YYYY-MM-DD')}~${moment.unix(r.endTime).format('YYYY-MM-DD')}` } },
      { title: '发放黄水晶总数', dataIndex: '', ...this.genColumnTooltip('奖励结算时，共计发放黄水晶数量（主持获得、ow获得、系统回收黄水晶之和）'), render: (v, r) => { return r.hostCitrine + r.owCitrine + r.recycleCitrine } },
      { title: '主持获得黄水晶总数', dataIndex: 'hostCitrine', ...this.genColumnTooltip('奖励结算时，主持总发放黄水晶数量') },
      { title: 'ow获得黄水晶总数', dataIndex: 'owCitrine', ...this.genColumnTooltip('奖励结算时，OW总发放黄水晶数量') },
      { title: '扣除奖励总金额(元)', dataIndex: 'totalDeduct', ...this.genColumnTooltip('奖励结算时，共计扣除用户账号奖励总额，数值单位“元”'), render: (v) => { return (crystalToUnitYB * v).toFixed(2) } }
    ]
    const tableStyle = {
      size: 'middle',
      bordered: true,
      pagination: { hideOnSinglePage: true }
    }
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Title level={4}>玩法日报</Title>
          <DatePicker.RangePicker format='YYYY-MM-DD'
            style={{ marginRight: 10, marginBottom: 5 }} defaultValue={this.state.searchTimeRange}
            onChange={(v) => { this.setState({ searchTimeRange: v }) }} />
          <Button type='primary'
            onClick={() => this.queryDailyState()}>
            刷新
          </Button>
          <Row>
            <Table columns={actColumns} dataSource={dailyState} {...tableStyle} scroll={{ x: 'max-content' }} />
          </Row>
          <Divider />
          <Title level={4}>奖励周报</Title>
          <DatePicker.RangePicker format='YYYY-MM-DD'
            style={{ marginRight: 10, marginBottom: 5 }} defaultValue={this.state.searchTimeRange2}
            onChange={(v) => { this.setState({ searchTimeRange2: v }) }} />
          <Button type='primary'
            onClick={() => this.queryWeeklyState()}>
            刷新
          </Button>
          <Row>
            <Table columns={revenueColumns} dataSource={weeklyState} {...tableStyle} scroll={{ x: 'max-content' }} />
          </Row>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default FirePowerPK
