import { getConfigList, add, update, remove } from './api'
import { message } from 'antd'

export default {
  namespace: 'sidAdvRecommend',

  state: {
    list: []
  },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { advList } } = yield call(getConfigList, payload)

      yield put({
        type: 'updateList',
        payload: Array.isArray(advList) ? advList : []
      })
    },

    * addItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(add, payload)
      if (status === 0) {
        message.success('add succes', 3)
        yield put({
          type: 'getList',
          payload: payload
        })
      } else {
        msg.error('failed: ' + msg, 5)
      }
    },

    * updateItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(update, payload)
      if (status === 0) {
        message.success('update sucess', 3)
        yield put({
          type: 'getList',
          payload: payload
        })
      } else {
        message.error('failed: ' + msg, 5)
      }
    },

    * removeItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(remove, payload)
      if (status === 0) {
        message.success('remove success', 3)
        yield put({
          type: 'getList',
          payload: payload
        })
      } else {
        message.error('faield: ', msg, 5)
      }
    }
  }
}
