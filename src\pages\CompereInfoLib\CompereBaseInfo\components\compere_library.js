import React, { Component } from 'react'
import { Table, Button, Card, Input, Select, DatePicker, Modal, Tooltip, message, Collapse } from 'antd'
import { connect } from 'dva'
import dateString from '@/utils/dateString'
import { checkUid } from '@/utils/common'
import ExportRemoteData from '@/components/ExportRemoteData'
import { stringify } from 'qs'
import CompereDetailInfo from '../../CompereDetailInfo'

const Option = Select.Option
const { Panel } = Collapse

const namespace = 'compereBaseInfo'

const compereRoleMap = { 1: '到期解约', 2: '互动超级(已废弃)', 3: '普通主持', 4: '超级主持', 5: '超级天团', 6: '厅天团', 7: '准超主', 10: '星光主持' }
// const identityMap = { 1: '普通个人', 2: '企业', 3: ' 工作室（工作室类型）', 4: '工作室（个体户类型）' }
const sexMap = { 0: '女', 1: '男', 2: '-' }
const yesNoMap = { 0: '-', 1: '是', 2: '否' }
const yesNo2Map = { 1: '是', 0: '否' }
const silenceTypeMap = {
  0: '-',
  1: '近期活跃',
  2: '轻度沉默',
  3: '中度沉默',
  4: '重度沉默'
}

var moment = require('moment')

function dateStringYMD (timestamp) {
  return moment.unix(timestamp).format('YYYY-MM-DD')
}

@connect(({ compereBaseInfo }) => ({
  model: compereBaseInfo
}))

class CompereLibrary extends Component {
  constructor (props) {
    super(props)
    this.state = {
      page: 1,
      currentIndex: 1,
      pageSize: 20
    }
  }

  compereRoleFormater = (compereRole, isCancelContract) => {
    if (isCancelContract && compereRole === 2) { // 已解约主持显示为帽子超主需要改成普通主持，因为线上存在这样的记录，应该需要改线上数据。
      return '普通主持'
    }
    if (compereRole === 2) {
      return '-'
    }
    return compereRoleMap[compereRole]
  }

  columns = [
    { title: 'UID', dataIndex: 'uid', align: 'center', fixed: 'left' },
    { title: 'YY号', dataIndex: 'yy', align: 'center', fixed: 'left' },
    { title: '昵称', dataIndex: 'nick', align: 'center', fixed: 'left' },
    { title: '短位ID', dataIndex: 'asid', align: 'center', fixed: 'left' },
    { title: '主持身份', dataIndex: 'compereRole', align: 'center', render: (text, r) => { return this.compereRoleFormater(r.compereRole, r.isCancelContract) } },
    { title: '是否有特权', dataIndex: 'hadPrivileged', align: 'center', render: (text, record) => (yesNoMap[record.hadPrivileged]) },
    { title: '是否解约', dataIndex: 'isCancelContract', align: 'center', render: (text, record) => (yesNo2Map[record.isCancelContract]) },
    { title: '沉默主持', dataIndex: 'silenceType', align: 'center', render: (text, record) => (silenceTypeMap[record.silenceType]) },
    { title: '公会抽成比例(%)', dataIndex: 'guildWeight', align: 'center' },
    { title: '签约开始时间', dataIndex: 'signStartTime', align: 'center', render: (text, record) => (dateStringYMD(record.signStartTime / 1000)) },
    { title: '签约结束时间', dataIndex: 'signEndTime', align: 'center', render: (text, record) => (dateStringYMD(record.signEndTime / 1000)) },
    { title: '实名性别', dataIndex: 'realSex', align: 'center', render: (text, record) => (sexMap[record.realSex]) },
    { title: '真实姓名', dataIndex: 'realName', align: 'center' },
    { title: '身份证号', dataIndex: 'idCard', align: 'center' },
    { title: '手机号', dataIndex: 'phone', align: 'center' },
    { title: '详细信息', key: 'operation', align: 'center', exportIgnore: true, render: (text, record) => (<span><a onClick={this.showDetailModal(record)}>查看</a></span>) },
    { title: '历史记录', key: 'operation', align: 'center', exportIgnore: true, render: (text, record) => (<span><a onClick={this.showHistoryModal(record)}>查看</a></span>) }
  ]

  columnsHistory = [
    { title: '日期', dataIndex: 'timestamp', align: 'center', render: (text, record) => (dateString(record.timestamp)) },
    { title: '操作人', dataIndex: 'optUser', align: 'center', render: (text, record) => (record.optUser === 'system' ? '系统' : record.optUser) },
    { title: '操作类型', dataIndex: 'optType', align: 'center' },
    { title: '描述', dataIndex: 'desc', align: 'center' }
  ]

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: (e) => { this.pageChange(e) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  pageValue = () => {
    return {
      pageSize: this.state.pageSize,
      total: this.props.model.total,
      current: this.state.currentIndex,

      onChange: (page, size) => {
        this.pageChange(page, size)
      },
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    }
  }

  defaultPageValueHistory = {
    defaultPageSize: 10,
    pageSizeOptions: ['10', '30', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: (e) => {
      this.setState({ page: e })
    },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  pageChange = (page, size) => {
    console.log('page:', page, 'size', size)
    this.loadData(page, size)
  }

  componentDidMount () {
    this.loadData()
  }

  getSearchCondition = () => {
    const { searchUID, searchYY, searchNick, searchASID, searchCompereRole, searchHadPrivileged, searchIsJoinTing, searchIsCancelContract, searchSilenceType, searchIdentity, searchSettlementType, searchIsPublic, searchSignStartTime, searchSignEndTime } = this.state
    let identity
    if (searchIdentity >= 0) {
      identity = Number(searchIdentity)
    }

    let signStartTimeTmp = 0
    let signEndTimeTmp = 0
    if (searchSignStartTime) {
      signStartTimeTmp = moment(searchSignStartTime).unix()
    }
    if (searchSignEndTime) {
      signEndTimeTmp = moment(searchSignEndTime).unix()
    }

    return { uidList: searchUID, yyList: searchYY, nick: searchNick, asid: searchASID, compereRole: searchCompereRole, hadPrivileged: searchHadPrivileged, isJoinTing: searchIsJoinTing, isCancelContract: searchIsCancelContract, silenceType: searchSilenceType, identity: identity, settlementType: searchSettlementType, isPublic: searchIsPublic, signStartTime: signStartTimeTmp, signEndTime: signEndTimeTmp }
  }

  loadData = (selectedPage, selectedPageSize) => {
    const { pageSize } = this.state
    if (!selectedPage) {
      selectedPage = 1
    }
    if (!selectedPageSize) {
      selectedPageSize = pageSize
    }
    this.setState({ currentIndex: selectedPage, pageSize: selectedPageSize })
    let payload = this.getSearchCondition()
    payload.page = selectedPage
    payload.pageSize = selectedPageSize

    this.props.dispatch({
      type: `${namespace}/lisCompereWholeLib`,
      payload: payload
    })
  }

  loadHistoryData = (curUIDParam) => {
    const { curUID, searchOptType, searchStartTime, searchEndTime } = this.state
    let startTimeTmp = 0
    let endTimeTmp = 0
    if (searchStartTime) {
      startTimeTmp = moment(searchStartTime).unix()
    }
    if (searchEndTime) {
      endTimeTmp = moment(searchEndTime).unix()
    }

    let uid = curUID
    if (uid === undefined || uid === null || uid === 0) {
      uid = curUIDParam
    }

    console.log(uid, searchOptType, searchStartTime, searchEndTime)
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/listCompereWholeLibHistory`,
      payload: { uid: uid, optType: searchOptType, startTime: startTimeTmp, endTime: endTimeTmp }
    })
  }

  searchHandle = () => {
    this.loadData()
  }

  searchHistoryHandle = () => {
    this.loadHistoryData()
  }

  showHistoryModal = (record) => () => {
    console.log(record)
    this.setState({ visibleHistory: true, curUID: record.uid })
    this.loadHistoryData(record.uid)
  }

  showDetailModal = (record) => () => {
    this.props.dispatch({
      type: `${namespace}/getCompereDetailInfo`,
      payload: { uid: record.uid, useSaveInfoIfNotContract: true }
    }).then(() => {
      this.setState({ visibleDetail: true })
    })
  }

  handleCancelHistory = e => {
    this.setState({ visibleHistory: false, curUID: 0 })
    // this.loadData()
  }

  handleCancelDetail = e => {
    this.setState({ visibleDetail: false })
    // this.loadData()
  }

  handleCancelDetailNew = (v) => {
    this.setState({ visibleDetail: v })
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      var value = selectedRows.map(item => item.Sid).join(',')
      this.setState({ removeKey: value })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User',
      name: record.name
    })
  }

  getExportQueryURI = (useCurrentQueryCondition, page, size) => {
    let params = {}
    if (useCurrentQueryCondition) {
      params = this.getSearchCondition()
    }
    params.page = page
    params.pageSize = size
    params.exportMode = true
    return `/compere_data_base/list_compere_whole_lib?${stringify(params)}`
  }

  // 批量输入完成
  onBatchInputComplete = () => {
    const { searchMultiUID } = this.state
    let tmpInput = searchMultiUID.trim()
    if (tmpInput === '') {
      message.warn('数据为空, 请检查')
      return
    }
    if (tmpInput.indexOf(',') >= 0) {
      message.warn('输入格式不正确，请使用换行副隔开每个元素')
      return
    }
    let result = tmpInput.replaceAll('\n', ',')
    let tmpSlice = result.split(',')
    if (tmpSlice.length > 100 || result.length > 2000) { // 限制批量查询数量,确保不超出url长度限制
      message.warn('批量查询数据量过大，请检查输入')
      return
    }
    for (let i = 0; i < tmpSlice.length; i++) {
      if (!checkUid(tmpSlice[i])) {
        return
      }
    }
    if (this.inputRef) { // 设置小输入框的值
      this.inputRef.setValue(result)
    }
    console.log(result)
    this.setState({ searchUID: result })
    this.setState({ modelVisable2: false }) // 隐藏模态框
  }

  // 批量输入完成
  onBatchInputCompleteYY = () => {
    const { searchMultiYY } = this.state
    let tmpInput = searchMultiYY.trim()
    if (tmpInput === '') {
      message.warn('数据为空, 请检查')
      return
    }
    if (tmpInput.indexOf(',') >= 0) {
      message.warn('输入格式不正确，请使用换行副隔开每个元素')
      return
    }
    let result = tmpInput.replaceAll('\n', ',')
    let tmpSlice = result.split(',')
    if (tmpSlice.length > 100 || result.length > 2000) { // 限制批量查询数量,确保不超出url长度限制
      message.warn('批量查询数据量过大，请检查输入')
      return
    }
    for (let i = 0; i < tmpSlice.length; i++) {
      if (!checkUid(tmpSlice[i])) {
        return
      }
    }
    if (this.inputRefYY) { // 设置小输入框的值
      this.inputRefYY.setValue(result)
    }
    console.log(result)
    this.setState({ searchYY: result })
    this.setState({ modelVisable: false }) // 隐藏模态框
  }

  render () {
    const { model: { listHistory, list, tableLoadingHistory, compereDetailInfo } } = this.props
    const { visibleHistory, visibleDetail } = this.state

    return (
      <Card>
        <div>
          <Tooltip title='批量输入请双击'>
            UID：
            <Input
              allowClear
              ref={(input) => { this.inputRef = input }}
              style={{ width: 120, marginLeft: 5, marginRight: 5 }}
              placeholder={'输入UID'}
              onChange={(e) => { this.setState({ searchUID: e.target.value }) }}
              onDoubleClick={(e) => { this.setState({ modelVisable2: true }) }}
            />
          </Tooltip>
          <Tooltip title='批量输入请双击'>
            YY号：
            <Input
              allowClear
              ref={(input) => { this.inputRefYY = input }}
              style={{ width: 120, marginLeft: 5, marginRight: 5 }}
              placeholder={'输入YY'}
              onChange={(e) => { this.setState({ searchYY: e.target.value }) }}
              onDoubleClick={(e) => { this.setState({ modelVisable: true }) }}
            />
          </Tooltip>
          昵称：
          <Input style={{ marginRight: 5, width: 120 }} placeholder='昵称' onChange={e => this.setState({ searchNick: e.target.value })} />
          短位ID：
          <Input style={{ marginRight: 5, width: 120 }} placeholder='短位ID' onChange={e => this.setState({ searchASID: e.target.value })} />
          主持身份：
          <Select style={{ marginRight: 5, width: 120 }} allowClear onChange={e => this.setState({ searchCompereRole: e })}>
            <Option key={1} value={1}>到期解约</Option>
            {/* <Option key={2} value={2}>互动超级</Option> */}
            <Option key={3} value={3}>普通主持</Option>
            <Option key={4} value={4}>超级主持</Option>
            <Option key={7} value={7}>准超主</Option>
            <Option key={10} value={10}>星光主持</Option>
          </Select>
          是否特权：
          <Select style={{ marginRight: 5, width: 80 }} allowClear onChange={e => this.setState({ searchHadPrivileged: e })}>
            <Option key={0} value={1}>是</Option>
            <Option key={1} value={2}>否</Option>
          </Select>
          是否解约：
          <Select style={{ marginRight: 5, width: 80 }} allowClear onChange={e => this.setState({ searchIsCancelContract: e })}>
            <Option key={1} value={2}>是</Option>
            <Option key={2} value={1}>否</Option>
          </Select>
          沉默主持：
          <Select style={{ marginRight: 5, width: 100 }} allowClear onChange={e => this.setState({ searchSilenceType: e })}>
            <Option key={0} value={0}>全部</Option>
            <Option key={1} value={1}>近期活跃</Option>
            <Option key={2} value={2}>轻度沉默</Option>
            <Option key={3} value={3}>中度沉默</Option>
            <Option key={4} value={4}>重度沉默</Option>
          </Select>
          <br />
          结算身份：
          <Select style={{ marginRight: 15, width: 120, marginTop: 10 }} allowClear onChange={e => this.setState({ searchIdentity: e })}>
            <Option key={0} value={1}>普通个人</Option>
            <Option key={1} value={2}>企业</Option>
            <Option key={2} value={3}>工作室</Option>
          </Select>
          是否半月提：
          <Select style={{ marginRight: 15, width: 120, marginTop: 10 }} allowClear onChange={e => this.setState({ searchSettlementType: e })}>
            <Option key={1} value={3}>是</Option>
            <Option key={2} value={111}>否</Option>
          </Select>
          是否对公：
          <Select style={{ marginRight: 15, width: 120 }} allowClear onChange={e => this.setState({ searchIsPublic: e })}>
            <Option key={1} value={1}>是</Option>
            <Option key={2} value={2}>否</Option>
          </Select>
          签约开始时间：
          <DatePicker
            format='YYYY-MM-DD'
            placeholder='开始时间'
            onChange={(v) => this.setState({ searchSignStartTime: v })}
            style={{ marginLeft: 5 }}
          />
          <span style={{ marginLeft: 5 }}>~</span>
          <DatePicker
            format='YYYY-MM-DD'
            placeholder='结束时间'
            onChange={(v) => this.setState({ searchSignEndTime: v })}
            style={{ marginLeft: 5 }}
          />
          <Button style={{ marginLeft: 5 }} type='primary' onClick={this.searchHandle}>查询</Button>
          <ExportRemoteData title={'选择导出'} buttonStyle={{ marginLeft: 20 }} filename={'主持全量信息库'} columns={this.columns} dataProvider={() => this.state.exportKey} />
          <ExportRemoteData title={'全量导出'} buttonStyle={{ marginLeft: 20 }} filename={'主持全量信息库'} columns={this.columns} uriBuilder={(page, size) => this.getExportQueryURI(false, page, size)} />
          <ExportRemoteData title={'当前条件导出'} buttonStyle={{ marginLeft: 20 }} filename={'主持全量信息库'} columns={this.columns} uriBuilder={(page, size) => this.getExportQueryURI(true, page, size)} />
        </div>
        <Collapse>
          <Panel header='说明'>
            <div>1. 入库/出库规则：完成个人实名且完成线上普通签约的主持，会自动入库。主持入库后，后续不再出库（注：完成企业实名的签约主持，暂无入库，比如签约的ow）</div>
            <div>2. 是否有特权：超级主持分 有特权和无特权。无特权指主持身份是超级，但是没有超主的特权（如超主分成比例/加成等）。应用场景是 超主兼职跳槽处罚时，会取消主持特权</div>
            <div>3. 沉默主持：</div>
            <div>①近期活跃：指最近60天内（含60天）上麦主持或多人视频下上座嘉宾位的主持</div>
            <div>②轻度沉默：指最近60天（不含60天）无上麦主持或多人视频下上座嘉宾位的主持；新签约主持，若签约后未开播，默认轻度沉默</div>
            <div>③中度沉默：指最近180天（不含180天）无上麦主持或多人视频下上座嘉宾位的主持</div>
            <div>④重度沉默：指最近360天（不含360天）无上麦主持或多人视频下上座嘉宾位的主持</div>
            <div>注：沉默类型每天早上8点更新。主持新签后，会默认显示-；沉默类型更新后，根据开播情况展示对应沉默类型</div>
            <div>4. 是否解约：若主持合约到期解约或申请强制解约，“是否解约”状态显示“是”</div>
          </Panel>
        </Collapse>
        <Table style={{ marginTop: 10 }} rowSelection={this.rowSelection} size='small' rowKey='idx' pagination={this.pageValue()} columns={this.columns} dataSource={list} scroll={{ x: 'max-content' }} />

        <Modal footer={null} forceRender width={1000} visible={visibleHistory} title='历史记录' onCancel={this.handleCancelHistory}>
          <Card>
            时间：
            <DatePicker
              format='YYYY-MM-DD'
              placeholder='开始时间'
              onChange={(v) => this.setState({ searchStartTime: v })}
              style={{ marginLeft: 5 }}
            />
            <span style={{ marginLeft: 5 }}>~</span>
            <DatePicker
              format='YYYY-MM-DD'
              placeholder='结束时间'
              onChange={(v) => this.setState({ searchEndTime: v })}
              style={{ marginLeft: 5 }}
            />
            <font style={{ marginLeft: 15 }}>类型：</font>
            <Select style={{ marginLeft: 5, width: 150 }} allowClear onChange={e => this.setState({ searchOptType: e })}>
              <Option key={1} value={1}>签约</Option>
              <Option key={2} value={2}>解约</Option>
              <Option key={3} value={3}>续约</Option>
              <Option key={4} value={4}>主持身份变更</Option>
              <Option key={5} value={5}>加入厅</Option>
              <Option key={6} value={6}>退出厅</Option>
              <Option key={7} value={7}>转会</Option>
              <Option key={8} value={8}>主持对公</Option>
              <Option key={9} value={9}>半月结</Option>
              <Option key={10} value={10}>结算身份变更</Option>
              <Option key={11} value={11}>电子签补充独家认证信息</Option>
            </Select>
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.searchHistoryHandle}>查询</Button>
            <Table loading={tableLoadingHistory} style={{ marginTop: 10 }} size='small' rowKey='idx' pagination={this.defaultPageValueHistory} columns={this.columnsHistory} dataSource={listHistory} />
          </Card>
        </Modal>

        <CompereDetailInfo visibleDetail={visibleDetail} record={compereDetailInfo} cancelHandler={this.handleCancelDetailNew} />

        <Modal
          title={'批量查询UID'}
          visible={this.state.modelVisable2}
          onCancel={() => { this.setState({ modelVisable2: false }) }}
          onOk={(v) => { this.onBatchInputComplete(v) }}
          centered
        >
          <Input.TextArea
            ref={(input) => { this.inputRef2 = input }}
            allowClear
            placeholder='tip:每项数据占一行, 限制100行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiUID: e.target.value }) }}
          />
        </Modal>

        <Modal
          title={'批量查询YY'}
          visible={this.state.modelVisable}
          onCancel={() => { this.setState({ modelVisable: false }) }}
          onOk={(v) => { this.onBatchInputCompleteYY(v) }}
          centered
        >
          <Input.TextArea
            ref={(input) => { this.inputRefYY2 = input }}
            allowClear
            placeholder='tip:每项数据占一行, 限制100行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiYY: e.target.value }) }}
          />
        </Modal>
      </Card>
    )
  }
}

export default CompereLibrary
