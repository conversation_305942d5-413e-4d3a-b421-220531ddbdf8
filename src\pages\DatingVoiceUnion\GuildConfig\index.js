import { connect } from 'dva'
import React, { Component } from 'react'
import { But<PERSON>, Col, Divider, Form, Input, InputNumber, message, Popconfirm, Row, Table, Tooltip } from 'antd'
import { formatTimestamp } from '../common'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import Modal from 'antd/es/modal/Modal'
import { parseStringToNumberList } from '@/utils/common'
import ContractCompere from './components/ContractCompere'
import ContractCompereSyncHistory from './components/ContractCompereSyncHistory'
import DatingYyfGuildChangeHistory from './components/DatingYyfGuildChangeHistory'

const namespace = 'GuildConfigManage'
const defaultPageSize = 20

@connect(({ GuildConfigManage }) => ({
  model: GuildConfigManage
}))

class GuildConfigManage extends Component { // 默认页面组件，不需要修改
  constructor (props) {
    super(props)
    this.initState()
    this.searchHandle()
  }

  initState = () => {
    this.state = {
      pagination: {
        pageSize: defaultPageSize,
        total: 0,
        current: 1,
        defaultCurrent: 1,
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        onChange: (page, pageSize) => {
          this.pageChange(page, pageSize)
        },
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      }
    }
  }

  updatePagination = (page, pageSize, total) => {
    const { pagination } = this.state
    pagination.current = page || 1
    pagination.pageSize = pageSize || defaultPageSize
    if (total !== undefined) {
      pagination.total = total
    }
    this.setState({ pagination: pagination })
    return pagination
  }

  // 分页信息变更
  pageChange = (page, pageSize, total) => {
    let pagination = this.updatePagination(page, pageSize, total)
    this.searchHandle(pagination.current, pagination.pageSize)
  }

  // 获取当前查询条件
  getQueryCondition = (page, size, cond) => {
    const { pagination } = this.state
    page = page || pagination.current || 1
    size = size || pagination.pageSize || defaultPageSize
    let pageInfo = {
      pageNo: page,
      pageSize: size
    }
    if (!cond) {
      return pageInfo
    }

    return Object.assign(cond, pageInfo)
  }

  // 处理查询事件
  searchHandle = (page, size, cond) => {
    const { dispatch } = this.props

    let query = this.getQueryCondition(page, size, cond)
    if (!query) {
      return
    }
    let self = this
    dispatch({
      type: `${namespace}/pageListGuildConfig`,
      payload: query,
      callback: (data) => {
        self.updatePagination(query.pageNo || 1, query.pageSize || defaultPageSize, data ? data.total : 0)
      }
    })
  }

  onQueryClick = (values) => {
    const { pagination } = this.state
    let size = pagination.pageSize || defaultPageSize
    this.searchHandle(1, size, values)
  }

  getShowColumns = () => {
    return [
      { title: '添加时间', dataIndex: 'createTime', width: 150, align: 'left', render: (v) => formatTimestamp(v) },
      { title: '交友公会频道', dataIndex: 'sid', align: 'left' },
      { title: '交友公会频道ASID', dataIndex: 'asid', align: 'left' },
      { title: '交友频道名称', dataIndex: 'channelName', align: 'left', ellipsis: true, render: (v) => { return <Tooltip title={v}>{v}</Tooltip> } },
      { title: '语音房家族ID', dataIndex: 'familyId', align: 'left' },
      { title: '语音房家族名称', dataIndex: 'familyName', align: 'left', ellipsis: true, render: (v) => { return <Tooltip title={v}>{v}</Tooltip> } },
      {
        title: '个播厅白名单ssid',
        dataIndex: 'ssidList',
        align: 'left',
        width: 400,
        ellipsis: true,
        render: (v) => {
          v = v || []
          return <Tooltip title={<Input.TextArea rows={10} value={v.join('\n')} />} placement={'leftBottom'}>{v.join(',')}</Tooltip>
        }
      },
      /* { title: '日流水额度/元', dataIndex: 'dailyLimit', align: 'left', render: (v) => (v + '').replace(/\B(?=(\d{3})+(?!\d))/g, ',') }, */
      /* { title: '月流水额度/元', dataIndex: 'monthLimit', align: 'left', render: (v) => (v + '').replace(/\B(?=(\d{3})+(?!\d))/g, ',') }, */
      /* { title: '更新时间', dataIndex: 'updateTime', width: 150, align: 'left', render: (v) => formatTimestamp(v) }, */
      /* { title: '操作人', dataIndex: 'operator', align: 'left' }, */
      {
        title: '操作',
        key: 'operation',
        align: 'left',
        width: 250,
        render:
          (text, item) => (
            <span>
              <a size='small' type='primary' onClick={() => this.showEditModal(item, 'edit')}>编辑</a>
              <Divider type='vertical' /> {/* 分割线 */}
              <Popconfirm title='确认删除该频道吗？' onConfirm={() => this.onDelete(item)}><a style={{ color: 'red' }}>删除</a></Popconfirm>
              <Divider type='vertical' /> {/* 分割线 */}
              <a size='small' type='primary' onClick={() => this.showContractCompereModal(item)}>旗下主持</a>
              <Divider type='vertical' /> {/* 分割线 */}
              <a size='small' type='primary' onClick={() => this.showContractCompereSyncHistoryModal(item)}>同步历史</a>
            </span>)
      }
    ]
  }

  showEditModal = (item, editMode) => {
    if (editMode === 'add') {
      if (this.formRefEdit) {
        this.formRefEdit.resetFields()
      }
    }
    let editItem = Object.assign({}, item || {})
    if (editItem.ssidList && editItem.ssidList.length > 0) {
      editItem.ssidListText = editItem.ssidList.join('\n')
    } else {
      editItem.ssidListText = ''
    }
    if (this.formRefEdit) {
      this.formRefEdit.setFieldsValue(editItem)
    }
    this.setState({ editModalVisible: true, editItem: editItem, editMode: editMode })
  }

  closeEditModal = () => {
    if (this.formRefEdit) {
      this.formRefEdit.resetFields()
    }
    this.setState({ editModalVisible: false })
  }

  onEditModalOk = () => {
    this.formRefEdit.submit()
  }

  onEditFormSubmit = (values) => {
    const { editItem, editMode } = this.state

    let submitItem = editItem || {}
    if (editMode === 'add') {
      submitItem.sid = values.sid
      submitItem.familyId = values.familyId
    }
    submitItem.ssidList = parseStringToNumberList(values.ssidListText)
    submitItem = {
      sid: submitItem.sid, familyId: submitItem.familyId, ssidList: submitItem.ssidList
    }
    console.log('待提交的数据: ', submitItem, values)
    const reqType = editMode === 'add' ? `${namespace}/addGuildConfig` : `${namespace}/updateGuildConfig`
    const { dispatch } = this.props
    let self = this
    let close = message.loading('执行中，请稍候......')
    dispatch({
      type: reqType,
      payload: submitItem,
      callback: (rsp) => {
        close()
        if (rsp && rsp.status === 0) {
          self.setState({ editModalVisible: false, editItem: undefined })
          let msg = rsp.msg || ''
          if (msg.indexOf('审批') >= 0 || msg.indexOf('审核') >= 0) {
            message.success('成功: ' + msg, 2)
            return
          }
          self.searchHandle(1, defaultPageSize)
          message.success('success')
        } else {
          message.error(editMode + ' 失败: ' + rsp.msg)
          if (rsp.data && rsp.data.invalidList && rsp.data.invalidList.length > 0) {
            let acceptList = rsp.data.acceptList || []
            self.formRefEdit.setFieldsValue({ ssidListText: acceptList.join('\n'), invalidSsidListText: rsp.data.invalidList.map(item => item.key + ': ' + item.remark).join('\n') })
          }
        }
      }
    })
  }

  onDelete = (item) => {
    console.log('准备删除：', item)
    let self = this
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/removeGuildConfig`,
      payload: { sids: [item.sid] },
      callback: (rsp) => {
        if (rsp && rsp.status === 0) {
          self.searchHandle()
        } else {
          message.error('删除失败: ' + rsp.msg)
        }
      }
    })
  }

  // 编辑规则数据对话框
  renderEditModal = () => {
    const { editModalVisible, editItem, editMode } = this.state
    return (
      <Modal width={500} title={editMode === 'add' || !editItem ? '添加' : '编辑 【' + editItem.sid + '】'} visible={editModalVisible} onCancel={this.closeEditModal} onOk={this.onEditModalOk}>
        <Form labelCol={{ span: 7 }} ref={(form) => {
          if (!this.formRefEdit) {
            form.setFieldsValue(editItem)
          }
          this.formRefEdit = form
        }} onFinish={this.onEditFormSubmit}>
          <Form.Item name='sid' label={'交友试点公会'} required>
            <InputNumber style={{ width: '300px' }} min={1} disabled={editMode === 'edit'} placeholder={'输入长号频道sid'} />
          </Form.Item>

          <Form.Item name='familyId' label={'语音房家族ID'} required>
            <InputNumber style={{ width: '300px' }} min={1} disabled={editMode === 'edit'} placeholder={'输入语音房家族ID'} />
          </Form.Item>

          <Divider type={'horizontal'} />
          <Form.Item name='ssidListText' label={'个播厅白名单ssid'}>
            <Input.TextArea rows={10} style={{ width: '300px' }} />
          </Form.Item>
          <Form.Item name='invalidSsidListText' label={'不符合要求ssid'}>
            <Input.TextArea rows={3} style={{ width: '300px' }} readOnly />
          </Form.Item>
        </Form>
      </Modal>
    )
  }

  // 展示签约主持信息对话框
  showContractCompereModal = (guild) => {
    this.setState({ contractCompereVisible: true, focusItem: guild })
  }

  // 签约主持信息对话框
  renderContractCompereModal = () => {
    const { contractCompereVisible, focusItem } = this.state
    let sid = focusItem ? focusItem.sid : 0
    let familyId = focusItem ? focusItem.familyId : 0
    return (
      <Modal width={'90%'} title={`交友工会【${sid}】 家族【${familyId}】下签约主持`}
        visible={contractCompereVisible}
        onCancel={() => this.setState({ contractCompereVisible: false })}
        onOk={() => this.setState({ contractCompereVisible: false })}>
        <ContractCompere sid={sid} />
      </Modal>
    )
  }

  // 展示签约主持信息对话框
  showContractCompereSyncHistoryModal = (guild) => {
    this.setState({ contractCompereHistoryVisible: true, focusItem: guild })
  }

  // 签约主持信息同步历史对话框
  renderContractCompereSyncHistoryModal = () => {
    const { contractCompereHistoryVisible, focusItem } = this.state
    let sid = focusItem ? focusItem.sid : 0
    let familyId = focusItem ? focusItem.familyId : 0
    return (
      <Modal width={'90%'} title={`交友工会【${sid}】 家族【${familyId}】下签约主持签约信息同步历史`}
        visible={contractCompereHistoryVisible}
        onCancel={() => this.setState({ contractCompereHistoryVisible: false })}
        onOk={() => this.setState({ contractCompereHistoryVisible: false })}>
        <ContractCompereSyncHistory sid={sid} />
      </Modal>
    )
  }

  showGuildChangeHistoryModal = () => {
    this.setState({ guildHistoryVisible: true })
  }

  // 签约主持信息对话框
  renderGuildChangeHistoryModal = () => {
    const { guildHistoryVisible } = this.state
    return (
      <Modal width={'90%'} title={`试点工会变更记录`}
        visible={guildHistoryVisible}
        onCancel={() => this.setState({ guildHistoryVisible: false })}
        onOk={() => this.setState({ guildHistoryVisible: false })}>
        <DatingYyfGuildChangeHistory />
      </Modal>
    )
  }

  showEditAllowSyncUIDModal = () => {
    const { dispatch } = this.props
    let self = this
    dispatch({
      type: `${namespace}/getAllowSyncUIDList`,
      callback: (data) => {
        let uidList = (data.list || []).map(item => item.uid)
        self.setState({ editAllowSyncUIDVisible: true, allowSyncUIDList: uidList })
        if (self.formRefEditAllow) {
          self.formRefEditAllow.setFieldsValue({ allowUIDListText: uidList.join('\n') })
        }
      }
    })
  }

  onEditAllowSyncUIDModalOK = () => {
    if (!this.formRefEditAllow) {
      this.setState({ editAllowSyncUIDVisible: false })
      return
    }
    let uidText = this.formRefEditAllow.getFieldValue('allowUIDListText')
    let uidList = parseStringToNumberList(uidText)
    const { dispatch } = this.props
    let self = this
    dispatch({
      type: `${namespace}/updateAllowSyncUIDList`,
      payload: { uidList: uidList },
      callback: (data) => {
        console.log(data)
        if (data && data.status === 0) {
          message.info('更新成功：' + (data.msg || '1分钟左右生效'), 1, () => {
            self.setState({ editAllowSyncUIDVisible: false })
          })
        } else {
          message.error('更新失败 ', data.msg || '')
        }
      }
    })
  }

  // 允许同步到语音房签约信息的主持uid白名单
  renderEditAllowSyncUIDModal = () => {
    const { editAllowSyncUIDVisible, allowSyncUIDList } = this.state
    let allowUIDListText = (allowSyncUIDList || []).join('\n')
    return (
      <Modal width={400} title={'允许同步到语音房签约信息的主持uid白名单'} visible={editAllowSyncUIDVisible} onCancel={() => this.setState({ editAllowSyncUIDVisible: false })} onOk={this.onEditAllowSyncUIDModalOK} okText={'更新'}>
        <Form ref={(form) => {
          if (!this.formRefEditAllow) {
            this.formRefEditAllow = form
            this.formRefEditAllow.setFieldsValue({ allowUIDListText: allowUIDListText })
          }
        }}>
          <Form.Item name='allowUIDListText'>
            <Input.TextArea rows={20} style={{ width: '300px' }} />
          </Form.Item>
        </Form>
      </Modal>
    )
  }

  // 渲染函数
  render () {
    const { route, model: { dataList } } = this.props
    const { pagination, contractCompereHistoryVisible, contractCompereVisible, guildHistoryVisible } = this.state
    const columns = this.getShowColumns()

    return (
      <>
        <PageHeaderWrapper title={route.name}>
          <Row style={{ marginBottom: '1em' }}>
            <Form layout={'inline'} ref={form => {
              this.formRefQuery = form
            }} onFinish={this.onQueryClick}>
              <Form.Item name={'channel'} label={'交友公会频道'}>
                <InputNumber placeholder='输入交友sid|asid' style={{ width: 150 }} allowClear />
              </Form.Item>
              <Form.Item name={'familyId'} label={'语音房家族ID'}>
                <InputNumber placeholder='输入语音房家族ID' style={{ width: 150 }} allowClear />
              </Form.Item>

              <Button type='primary' htmlType='submit'>查询</Button>
              <Divider type={'vertical'} />
              <Button type='primary' onClick={() => {
                this.formRefQuery.resetFields()
                this.formRefQuery.submit()
              }}>重置</Button>
              <Divider type={'vertical'} />
              <Button type={'primary'} onClick={() => this.showEditModal({}, 'add')}>添加</Button>
              <Divider type={'vertical'} />
              <Button type={'default'} onClick={() => this.showGuildChangeHistoryModal()}>变更历史</Button>
              <Divider type={'vertical'} />
              <Button type={'default'} onClick={() => this.showEditAllowSyncUIDModal()}>允许同步主持白名单</Button>
            </Form>
          </Row>
          <Row style={{ marginBottom: '1em' }}>
            <Col span={24}>
              <Table columns={columns}
                dataSource={dataList}
                size='small'
                pagination={pagination}
                showSorterTooltip={false}
                rowKey={record => record.sid}
              />
            </Col>
          </Row>

          {/* 渲染编辑对话框 */}
          {this.renderEditModal()}

          {/* 渲染可以同步语音房签约主持白名单对话框 */}
          {this.renderEditAllowSyncUIDModal()}

          {/* 试点工会下签约主持对话框 */}
          {contractCompereVisible ? this.renderContractCompereModal() : ''}

          {/* 试点工会下签约主持签约信息同步历史对话框 */}
          {contractCompereHistoryVisible ? this.renderContractCompereSyncHistoryModal() : ''}

          {/* 试点工会变更历史 */}
          {guildHistoryVisible ? this.renderGuildChangeHistoryModal() : ''}
        </PageHeaderWrapper>
      </>
    )
  }
}

export default GuildConfigManage
