import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import ActCfgComponent from './component/act_cfg'
import MoneyCfgComponent from './component/money_cfg'
import DescCfgComponent from './component/desc_cfg'
import ScoreRewardComponent from './component/score_reward'
import ActOpBlackCompereComponent from './component/black_compere'
import PkRewardDetailComponent from './component/pk_detail'

const TabPane = Tabs.TabPane
// 2022-01-19 TODO:已下线,可铲掉
class RobMoney extends Component { // 默认页面组件，不需要修改
  onTabChange = key => {
    this.setState({ key })
  }

  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs type='card'>
          <TabPane tab='周期配置' key='1'>
            <ActCfgComponent {...this.state} />
          </TabPane>

          <TabPane tab='带入金配置' key='2'>
            <MoneyCfgComponent {...this.state} />
          </TabPane>

          <TabPane tab='文案配置' key='3'>
            <DescCfgComponent {...this.state} />
          </TabPane>

          <TabPane tab='积分发放' key='4'>
            <ScoreRewardComponent {...this.state} />
          </TabPane>

          <TabPane tab='主持黑名单' key='5'>
            <ActOpBlackCompereComponent {...this.state} />
          </TabPane>

          <TabPane tab='赏金PK明细' key='6'>
            <PkRewardDetailComponent {...this.state} />
          </TabPane>
        </Tabs>
      </PageHeaderWrapper>
    )
  }
}

export default RobMoney // 保证唯一
