import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Table, Button, Form, Divider, Row, Col, DatePicker, Input } from 'antd'
import { getFieldsValueByChannelID } from '../../globalConfig'

const namespace = 'dropQuery'

@connect(({ dropQuery }) => ({
  model: dropQuery
}))

class DropQuerySignal extends Component {
  // 点击 添加标签页-添加按钮
  onFinish = values => {
    const { dispatch } = this.props

    let params = {}
    params.begin = values.range[0].unix()
    params.end = values.range[1].unix()
    params.uid = parseInt(values.uid)

    dispatch({
      type: `${namespace}/getSignal`,
      payload: params
    })
  }

  render () {
    const { model: { signalList } } = this.props
    const columns = [
      { title: '序号', align: 'center', render: (_, record, index) => index + 1 },
      { title: '序列号', dataIndex: 'seqID', align: 'center', width: '100px' },
      { title: '时间', dataIndex: 'time', align: 'center' },
      { title: 'UID', dataIndex: 'uid', align: 'center' },
      { title: '频道', dataIndex: 'sid', align: 'center' },
      { title: '子频道', dataIndex: 'ssid', align: 'center' },
      { title: '主持人UID', dataIndex: 'compereUid', align: 'center' },
      { title: '渠道', dataIndex: 'channel', align: 'center', render: v => getFieldsValueByChannelID(v, 'Name') },
      { title: '类型', dataIndex: 'history', align: 'center', render: v => ['空投助力', '盖章', '空投消耗'][v] },
      { title: '数量', dataIndex: 'count', align: 'center' },
      { title: '剩余总量', dataIndex: 'signal', align: 'center' }
    ]

    return (
      <Card>
        <Form onFinish={this.onFinish}>
          <Row gutter={4}>
            <Col>
              <Form.Item name='range' rules={[{ required: true, message: '时间区间不能为空' }]}>
                <DatePicker.RangePicker showTime style={{ width: '330px' }} />
              </Form.Item>
            </Col>
            <Col>
              <Form.Item name='uid' rules={[{ required: true, message: 'UID 不能为空' }]}>
                <Input placeholder='UID' />
              </Form.Item>
            </Col>
            <Form.Item>
              <Button type='primary' htmlType='submit'>查询</Button>
            </Form.Item>
            <Col />
          </Row>
        </Form>
        <Divider />
        <Table size='small' pagination={false} columns={columns} dataSource={signalList} rowKey={(record, index) => index} />
      </Card>
    )
  }
}

export default DropQuerySignal
