import { getList, updateItem, batchApproval } from './api'
import { message } from 'antd'
// import { message } from 'antd'

export default {
  namespace: 'roomMgrLibrary', // 全局唯一标识

  state: {
    roomMgrList: []
  },

  reducers: {
    updateRoomMgrList (state, { payload }) {
      for (let i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        roomMgrList: payload
      }
    },

    updateChannelList (state, { payload }) {
      for (let i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        channelInfoList: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      try {
        const { data: { recommendList } } = yield call(getList, payload)
        yield put({
          type: 'updateRoomMgrList',
          payload: Array.isArray(recommendList) ? recommendList : []
        })
      } catch (e) {
        message.error('exception', e)
      }
    },

    * updateItem ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(updateItem, payload)
        if (status === 0) {
          message.info('success!')
        } else {
          message.error(msg)
        }
      } catch (e) {
        message.error('exception', e)
      }
    },

    * batchApproval ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(batchApproval, payload)
        if (status === 0) {
          message.info('success!')
        } else {
          message.error(msg)
        }
      } catch (e) {
        message.error('exception', e)
      }
    }
  }
}
