import * as api from './api'

export default {
  namespace: 'tingSuperTeam',

  state: {
    list: [],
    countInfo: { tingMgrCount: 0, tingCompere: 0, superCompere: 0, tingsuperCompere: 0 }
  },

  reducers: {
    displayList (state, { payload, countInfo }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        list: payload,
        countInfo: countInfo
      }
    }
  },

  effects: {
    * listTingSuperTeam ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listTingSuperTeam, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []

      let tingMgrMap = new Map()
      let tingCompereMap = new Map()
      let countInfo = { tingMgrCount: 0, tingCompere: 0, superCompere: 0, tingsuperCompere: 0 }

      for (let i = 0; i < data.length; i++) {
        data[i].idx = i + 1

        if (tingMgrMap.get(data[i].tingMgrUid) === undefined) {
          countInfo.tingMgrCount++
          tingMgrMap.set(data[i].tingMgrUid, 1)
        }

        if (tingCompereMap.get(data[i].uid) === undefined) {
          countInfo.tingCompere++
          tingCompereMap.set(data[i].uid, 1)
        }

        if (data[i].role === 5) {
          countInfo.superCompere++
        }

        if (data[i].role === 2 || data[i].role === 11) {
          countInfo.tingsuperCompere++
        }
      }
      console.log(countInfo)
      yield put({
        type: 'displayList',
        payload: data,
        countInfo: countInfo
      })
    }
  }
}
