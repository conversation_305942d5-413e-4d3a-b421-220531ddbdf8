import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Modal, Input, Upload, message, Popconfirm, Drawer } from 'antd'
import { connect } from 'dva'
import dateString from '@/utils/dateString'
import LineWrap from '@/components/LineWrapper'

var moment = require('moment')
const namespace = 'activityValueSettle'
const FormItem = Form.Item
const TextArea = Input.TextArea
const getListUri = `${namespace}/getList`
const addItemUri = `${namespace}/addItem`
const updateItemUri = `${namespace}/updateItem`
const removeDetailItemUri = `${namespace}/removeItem`
const getDetailListUri = `${namespace}/getDetailList`
const publishDetailUri = `${namespace}/publishItem`

@connect(({ activityValueSettle }) => ({
  model: activityValueSettle
}))

class ActivityValueSettle extends Component {
  // column structs.
  columns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: '月份', dataIndex: 'monthId', align: 'center' },
    { title: '消息标题', dataIndex: 'title', align: 'center' },
    { title: '正文模版', dataIndex: 'message', align: 'center', render: text => (<LineWrap title={text} lineClampNum={1} />) },
    { title: '导入数量', dataIndex: 'importNum', align: 'center' },
    { title: '发布数量', dataIndex: 'publishNum', align: 'center' },
    { title: '操作时间', dataIndex: 'createAt', align: 'center', render: text => dateString(text) },
    { title: '操作', align: 'center', render: (text, record) => this.renderOpColumn(record) }
  ].map(item => {
    item.width = 200
    return item
  })

  pagination = { pageSizeOptions: ['20', '50', '100', '1000'], showSizeChanger: true, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, openDetail: false }

  messageTmpl = '经官方统计您10月31日0点-11月30日0点的活跃值为%.1f，将于今日扣除并于1号进行结算，其中活跃值结算时的20%%会结算给你签约公会'

  // show modal
  showModal = (isUpdate, record) => () => {
    if (record == null) record = { monthId: moment().format('YYYYMM'), title: '活跃值结算扣除通知', message: this.messageTmpl }

    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(record)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // update
  showDetailModal = (record) => () => {
    const { dispatch } = this.props
    var data = { monthId: record.monthId }
    dispatch({
      type: getDetailListUri,
      payload: data
    })

    this.setState({ openDetail: true, value: record, hasSent: record.completed !== 0 })
  }

  renderOpColumn = (record) => {
    if (record.completed !== 0 || record.importNum > 0) {
      return (
        <span>
          <a onClick={this.showDetailModal(record)}>查看详情</a>
        </span>)
    }

    return (
      <span>
        <a onClick={this.showModal(true, record)}>更新</a><Divider type='vertical' />
        <a onClick={this.showDetailModal(record)}>查看详情</a>
      </span>)
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  hideDetailModal = () => {
    const { dispatch } = this.props
    dispatch({
      type: getListUri
    })
    this.setState({ openDetail: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // update
  onFinish = values => {
    const { dispatch } = this.props
    var url = this.state.isUpdate ? updateItemUri : addItemUri
    dispatch({
      type: url,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // get list from server.
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: getListUri
    })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  disablePublish = () => {
    this.setState({ hasSent: true })
  }

  // content
  render () {
    const { route, model: { list, detailList } } = this.props
    const { visible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Button style={{ marginLeft: 5 }} type='primary' onClick={this.showModal(false)}>添加</Button>
          <Button style={{ marginLeft: 5 }} type='primary'>
            <a href='http://makefriends.bs2dl.yy.com/%E4%BA%A4%E5%8F%8B%E6%B4%BB%E8%B7%83%E5%80%BC%E7%BB%93%E7%AE%97%E6%A8%A1%E7%89%88.xlsx' type='primary' onClick={this.downLoad}>导入模板下载</a>
          </Button>
          <font style={{ marginLeft: 10 }} color='red'>tips: 请修改消息标题和模版，确认无误再导入详情数据</font>
          <Divider />
          <Table rowKey={(record, index) => index} dataSource={list} columns={this.columns} pagination={this.pagination} size='small' />
        </Card>
        <CompereActivityDetail disablePublish={this.disablePublish} {...this.props} list={detailList} {...this.state} onCancel={this.hideDetailModal} onSubmit={this.handleSubmit} /> {/* 弹窗 */}

        <Modal visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit} forceRender>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <FormItem label='月份' name='monthId' rules={[{ required: true, pattern: '^[23][0-9]{3}((0[1-9])|(1[0-2]))$', message: '月份格式不正确(200001-399912)' }]}>
              <Input />
            </FormItem>
            <FormItem label='标题' name='title' rules={[{ required: true, message: '标题不能为空' }]}>
              <Input />
            </FormItem>
            <FormItem label='消息模版' name='message' rules={[{ required: true, message: '消息模版不能为空' }]}>
              <TextArea rows={4} />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

class CompereActivityDetail extends Component {
  defaultPageValue = { defaultPageSize: 100, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` } // 分页设置，可以不改

  publishAlertMsg = '【警告】确认发布吗？一旦选择发布本次将扣除主持相应活跃值并发送通知！'
  // column structs.
  detailColumns = [
    { title: '#', dataIndex: 'index', key: 'index', align: 'center' },
    { title: '流水号', dataIndex: 'seqId', align: 'center' },
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '活跃值', dataIndex: 'score', align: 'center' },
    { title: '当前状态', dataIndex: 'status', align: 'center', render: (text, record) => text === 0 ? '待处理' : text === 1 ? '已推送' : text === 2 ? '已忽略' : '' },
    { title: '处理状态', dataIndex: 'processStatus', align: 'center', render: (text, record) => text === 0 ? '待处理' : text === 1 ? '扣除成功' : text === 2 ? '扣除失败' : '' },
    { title: '导入时间', dataIndex: 'createAt', align: 'center', render: (text, record) => dateString(text) },
    { title: '处理时间', dataIndex: 'publishAt', align: 'center', render: (text, record) => dateString(text) }
  ].map(item => {
    item.width = 200
    return item
  })

  confirms = type => e => {
    const { dispatch, disablePublish } = this.props
    var data = { monthId: type }

    if (disablePublish) {
      disablePublish()
    }

    dispatch({
      type: publishDetailUri,
      payload: data
    })
  }

  refreshDetails = type => e => {
    const { dispatch } = this.props
    var data = { monthId: type }
    dispatch({
      type: getDetailListUri,
      payload: data
    })
  }

  remove = type => e => {
    const { dispatch } = this.props
    const data = { monthId: type }
    dispatch({
      type: removeDetailItemUri,
      payload: data
    })
  }

  UpLoadOnChange = info => {
    if (info.file.status !== 'done') {
      return
    }
    if (info.file.response.status === 0) {
      message.success(`${info.file.name}导入成功`)
    } else {
      message.error(info.file.response.msg)
    }
    const { dispatch } = this.props
    const data = { monthId: this.props.value.monthId }
    dispatch({
      type: getDetailListUri,
      payload: data
    })
  }

  render () {
    const { openDetail, onCancel, list, value, hasSent } = this.props // 基本不需要修改
    return (
      <Drawer width='50%' visible={openDetail} onClose={onCancel} title='主持活跃值扣除列表'>
        {
          <div>
            <Button type='danger' onClick={this.remove(value.monthId)} disabled={hasSent}>删除</Button>
            <Upload action='/ActivityValueSettleDetail/ImportDetailItems' onChange={this.UpLoadOnChange} data={value}>
              <Button style={{ marginLeft: 5 }} type='primary' disabled={hasSent}>导入</Button>
            </Upload>
            <Divider type='horizontal' />
          </div>
        }
        <Table dataSource={list} columns={this.detailColumns} size='small' pagination={this.defaultPageValue} rowKey={(record, index) => index} /> {
          <div
            style={{
              borderTop: '1px solid #e9e9e9',
              padding: '10px 16px',
              textAlign: 'center'
            }}
          >
            <Popconfirm title={this.publishAlertMsg} onConfirm={this.confirms(value.monthId)} okText='Yes' cancelText='No'>
              <a href='#'><Button type='danger' disabled={hasSent}>发布</Button></a>
            </Popconfirm>
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.refreshDetails(value.monthId)}>刷新</Button>
          </div>
        }
      </Drawer>
    )
  }
}

export default ActivityValueSettle
