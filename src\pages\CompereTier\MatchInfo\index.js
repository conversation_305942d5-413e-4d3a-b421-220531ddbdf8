import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Form, Card, Input, DatePicker, Button } from 'antd'
import { connect } from 'dva'
import { CSVLink } from 'react-csv'
const namespace = 'MatchInfo'
var moment = require('moment')

@connect(({ MatchInfo }) => ({
  model: MatchInfo
}))

class Index extends Component {
  // column structs.
  columns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: 'uid', dataIndex: 'uid', key: 'uid', align: 'center' },
    { title: '昵称', dataIndex: 'nick', key: 'nick', align: 'center' },
    { title: '日期', dataIndex: 'date', key: 'date', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD') },
    { title: '每日乱斗场次', dataIndex: 'day_times', key: 'day_times', align: 'center' },
    { title: '随机匹配的场次', dataIndex: 'random_match', key: 'random_match', align: 'center' },
    { title: '随机匹配成功场次', dataIndex: 'random_match_success', key: 'random_match_success', align: 'center' },
    { title: '随机匹配同段场次', dataIndex: 'random_match_same_tier', key: 'random_match_same_tier', align: 'center' },
    { title: '随机匹配跨段场次', dataIndex: 'random_match_diff_tier', key: 'random_match_diff_tier', align: 'center' },
    { title: '邀请匹配的场次', dataIndex: 'invite_match', key: 'invite_match', align: 'center' },
    { title: '邀请匹配成功场次', dataIndex: 'invite_match_success', key: 'invite_match_success', align: 'center' },
    { title: '邀请匹配同段场次', dataIndex: 'invite_match_same_tier', key: 'invite_match_same_tier', align: 'center' },
    { title: '邀请匹配跨段场次', dataIndex: 'invite_match_diff_tier', key: 'invite_match_diff_tier', align: 'center' },
    { title: '系统匹配的场次', dataIndex: 'system_match', key: 'system_match', align: 'center' },
    { title: '系统匹配成功场次', dataIndex: 'system_match_success', key: 'system_match_success', align: 'center' },
    { title: '系统匹配同段场次', dataIndex: 'system_match_same_tier', key: 'system_match_same_tier', align: 'center' },
    { title: '系统匹配跨段场次', dataIndex: 'system_match_diff_tier', key: 'system_match_diff_tier', align: 'center' },
    { title: '同段匹配场次', dataIndex: 'same_tier', key: 'same_tier', align: 'center' },
    { title: '跨段匹配场次', dataIndex: 'diff_tier', key: 'diff_tier', align: 'center' }
  ]

  defaultPageValue = {
    defaultPageSize: 50,
    pageSizeOptions: ['50', '100', '500', '2000'],
    showSizeChanger: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = {
    value: {}
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = { type: this.state.type }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  searchByCompereUid = (value) => {
    const { dispatch } = this.props
    const data = { uid: this.state.uid }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  onDateChange = (date, dateString) => {
    this.setState({ date: dateString })
  }

  handleSearch = () => {
    const { dispatch } = this.props
    const data = { uid: this.state.uid, date: this.state.date }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // content
  render () {
    const { route, model: { list } } = this.props
    let headers = [
      { label: 'uid', key: 'uid' },
      { label: '日期', key: 'date' },
      { label: '每日乱斗场次', key: 'day_times' },
      { label: '随机匹配的场次', key: 'random_match' },
      { label: '随机匹配成功场次', key: 'random_match_success' },
      { label: '随机匹配同段场次', key: 'random_match_same_tier' },
      { label: '随机匹配跨段场次', key: 'random_match_diff_tier' },
      { label: '邀请匹配的场次', key: 'invite_match' },
      { label: '邀请匹配成功场次', key: 'invite_match_success' },
      { label: '邀请匹配同段场次', key: 'invite_match_same_tier' },
      { label: '邀请匹配跨段场次', key: 'invite_match_diff_tier' },
      { label: '系统匹配的场次', key: 'system_match' },
      { label: '系统匹配成功场次', key: 'system_match_success' },
      { label: '系统匹配同段场次', key: 'system_match_same_tier' },
      { label: '系统匹配跨段场次', key: 'system_match_diff_tier' },
      { label: '同段匹配场次', key: 'same_tier' },
      { label: '跨段匹配场次', key: 'diff_tier' }
    ]
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Input placeholder='搜索主持uid' onChange={e => this.setState({ uid: e.target.value })} style={{ width: 200 }} /> {/* 搜索按钮 */}
            <Divider type='vertical' /> {/* 分割线 */}
            日期
            <Divider type='vertical' /> {/* 分割线 */}
            <DatePicker onChange={this.onDateChange} />
            <Divider type='vertical' /> {/* 分割线 */}
            <Button type='primary' onClick={this.handleSearch}>搜索</Button>
            <Divider type='vertical' /> {/* 分割线 */}
            <CSVLink data={list} filename={'output.csv'} headers={headers}>导出</CSVLink>
            <Table rowKey={(record, index) => index} dataSource={list} columns={this.columns} pagination={this.defaultPageValue} />
          </Form>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default Index
