import { genGetListTemplate, genUpdateTemplate, genGetRequireTemplate } from '@/utils/common'

const getCompensateConfig = genGetListTemplate('/drop/admin/compensate_query', 'configData', (raw) => {
  if (raw.poolList == null) raw.poolList = []
  if (raw.version === 0) raw.version = 1
  return raw
})

const updateCompensateConfig = genUpdateTemplate('/drop/admin/compensate_update')
const getAllPrizeList = genGetRequireTemplate('/drop/admin/query_prize_list', 'globalPrizeList')

export default {
  namespace: 'dropCompensate',
  state: {
    configData: {},
    prizeList: [], // 准备废弃
    globalPrizeList: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },

  effects: {
    getCompensateConfig,
    updateCompensateConfig,
    getAllPrizeList
  }
}
