import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import { Card } from 'antd'
import DailyReportTab from './dailyReport'
import ProductReportTag from './productReport'
import ExchangeReport from './exchangeReport'

@connect(({ fragmentReport }) => ({
  model: fragmentReport
}))

class DailyReport extends Component {
  state = {}
  componentDidMount = () => {}

  render () {
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='1' type='card' size='large' >
            <TabPane tab='装扮商城日报' key='1'>
              <DailyReportTab />
            </TabPane>
            <TabPane tab='装扮商城兑换商品明细' key='2'>
              <ExchangeReport />
            </TabPane>
            <TabPane tab='获得装扮碎片日报' key='3'>
              <ProductReportTag />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default DailyReport
