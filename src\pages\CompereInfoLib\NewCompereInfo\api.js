import request from '@/utils/request'
import { stringify } from 'qs'

// 获取新主持信息列表
export function listCompereInfo (params) {
  return request(`/new_compere_info/list_new_compere_info?${stringify(params)}`)
}

// 手工出库
export function removeNewCompere (params) {
  return request(`/new_compere_info/remove_new_compere?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 手工导入
export function importNewCompere (params) {
  return request(`/new_compere_info/import_new_compere?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// approvalNewCompere 审批
export function approvalNewCompere (params) {
  return request(`/new_compere_info/approval_new_compere?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// batchApprovalNewCompere 批量审批
export function batchApprovalNewCompere (params) {
  return request(`/new_compere_info/batch_approval_new_compere?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 获取新主持入库各条件信息
export function getCompereInboundStatus (params) {
  return request(`/new_compere_info/get_new_compere_inbound_status?${stringify(params)}`)
}
