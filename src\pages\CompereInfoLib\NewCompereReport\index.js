import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Table, Tabs, Button, InputNumber, Select, DatePicker } from 'antd'
import { exportExcel } from 'xlsx-oc'

const namespace = 'newCompereReport'

var moment = require('moment')

const businessTypeJY = 1 // 交友
const businessTypeBABY = 3 // 宝贝

const contractTypeList = [
  { idx: 1, label: '普通主持' },
  { idx: 2, label: '超级主持' },
  { idx: 3, label: '超级天团' },
  { idx: 4, label: '帽子超级' },
  { idx: 5, label: '厅天团' },
  { idx: 6, label: '王牌主播' }
]
const contractTypeMap = { 1: '普通主持', 2: '超级主持', 3: '超级天团', 4: '帽子超级', 5: '厅天团', 6: '王牌主播' }

const supportPeriodList = [
  { idx: 0, label: '周期一' },
  { idx: 1, label: '周期二' },
  { idx: 2, label: '周期三' },
  { idx: 3, label: '周期四' },
  { idx: 4, label: '周期五' },
  { idx: 5, label: '周期六' }
]
const supportPeriodMap = { 0: '周期一', 1: '周期二', 2: '周期三', 3: '周期四', 4: '周期五', 5: '周期六' }

const reachTaskDescJYList = [
  { idx: '>=2k', label: '>=2k' },
  { idx: '>=5k', label: '>=5k' },
  { idx: '>=1W', label: '>=1W' },
  { idx: '未达标', label: '未达标' }
]
const reachTaskDescJYMap = { '>=2k': '>=2k', '>=5k': '>=5k', '>=1W': '>=1W', '未达标': '未达标' }

const reachTaskDescBabyList = [
  { idx: '>=100W', label: '>=100W' },
  { idx: '>=250W', label: '>=250W' },
  { idx: '>=500W', label: '>=500W' },
  { idx: '未达标', label: '未达标' }
]
const reachTaskDescBabyMap = { '>=100W': '>=100W', '>=250W': '>=250W', '>=500W': '>=500W', '未达标': '未达标' }

const settlementTypeList = [
  { idx: 0, label: '否' },
  { idx: 1, label: '是' }
]
const settlementTypeMap = { 0: '否', 1: '是' }

@connect(({ newCompereReport }) => ({
  model: newCompereReport
}))
// 2022-01-17, 应产品要求,将关键字"奖励"改成"扶持"
class NewCompereReport extends Component {
  constructor (props) {
    super(props)

    this.refreshInfo(businessTypeJY)
  }

  columns = [
    { title: 'YY号', dataIndex: 'yy', align: 'center' },
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: '短位id', dataIndex: 'asid', align: 'center' },
    {
      title: '主持身份',
      dataIndex: 'compereRole',
      align: 'center',
      render: (text, record) => {
        for (let i = 0; i < contractTypeList.length; i++) {
          const e = contractTypeList[i]
          if (e.idx === record.compereRole) {
            return e.label
          }
        }
      }
    },
    {
      title: '是否对公',
      dataIndex: 'settlementType',
      align: 'center',
      render: (text, record) => {
        for (let i = 0; i < settlementTypeList.length; i++) {
          const e = settlementTypeList[i]
          if (e.idx === record.settlementType) {
            return e.label
          }
        }
      }
    },
    {
      title: '扶持周期',
      dataIndex: 'supportPeriod',
      align: 'center',
      render: (text, record) => {
        for (let i = 0; i < supportPeriodList.length; i++) {
          const e = supportPeriodList[i]
          if (e.idx === record.supportPeriod) {
            return e.label
          }
        }
      }
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      align: 'center',
      render: (text, record) => {
        if (record.supportStartTime > 0 && record.supportEndTime > 0) {
          return moment.unix(record.supportStartTime).format('YYYY-MM-DD') + '至' + moment.unix(record.supportEndTime).format('YYYY-MM-DD') + ' 24点'
        }
      }
    },
    { title: '扶持周期内累计有效开播天数', dataIndex: 'validLiveDay', align: 'center' },
    { title: '扶持周期内累计礼物流水', dataIndex: 'props', align: 'center' },
    { title: '达标任务', dataIndex: 'reachTaskDesc', align: 'center', render: (text, record) => (record.reachTaskDesc === '未达标' ? <span><font color='red'>未达标</font></span> : <span><font>{record.reachTaskDesc}</font></span>) },
    { title: '发放扶持/元', dataIndex: 'reward', align: 'center' }
    // { title: '操作', key: 'operation', align: 'center', render: (text, record) => (<span><Button onClick={this.deleteHandle(record)} type='primary'>删除</Button></span>) }
  ]

  columns1 = [
    { title: 'YY号', dataIndex: 'yy', align: 'center' },
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: '短位id', dataIndex: 'asid', align: 'center' },
    {
      title: '主持身份',
      dataIndex: 'compereRole',
      align: 'center',
      render: (text, record) => {
        for (let i = 0; i < contractTypeList.length; i++) {
          const e = contractTypeList[i]
          if (e.idx === record.compereRole) {
            return e.label
          }
        }
      }
    },
    {
      title: '是否对公',
      dataIndex: 'settlementType',
      align: 'center',
      render: (text, record) => {
        for (let i = 0; i < settlementTypeList.length; i++) {
          const e = settlementTypeList[i]
          if (e.idx === record.settlementType) {
            return e.label
          }
        }
      }
    },
    {
      title: '扶持周期',
      dataIndex: 'supportPeriod',
      align: 'center',
      render: (text, record) => {
        for (let i = 0; i < supportPeriodList.length; i++) {
          const e = supportPeriodList[i]
          if (e.idx === record.supportPeriod) {
            return e.label
          }
        }
      }
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      align: 'center',
      render: (text, record) => {
        if (record.supportStartTime > 0 && record.supportEndTime > 0) {
          return moment.unix(record.supportStartTime).format('YYYY-MM-DD') + '至' + moment.unix(record.supportEndTime).format('YYYY-MM-DD') + ' 24点'
        }
      }
    },
    { title: '扶持周期内累计有效开播天数', dataIndex: 'validLiveDay', align: 'center' },
    { title: '扶持周期内累计礼物紫贝数', dataIndex: 'props', align: 'center' },
    { title: '达标任务', dataIndex: 'reachTaskDesc', align: 'center', render: (text, record) => (record.reachTaskDesc === '未达标' ? <span><font color='red'>未达标</font></span> : <span><font>{record.reachTaskDesc}</font></span>) },
    { title: '发放扶持/元', dataIndex: 'reward', align: 'center' }
    // { title: '操作', key: 'operation', align: 'center', render: (text, record) => (<span><Button onClick={this.deleteHandle(record)} type='primary'>删除</Button></span>) }
  ]

  state = {
    business: businessTypeJY
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      var value = selectedRows.map(item => item.Sid).join(',')
      this.setState({ removeKey: value })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User',
      name: record.name
    })
  }

  onTagChange = (idx) => {
    console.log(idx)
    if (idx === '1') {
      this.refreshInfo(businessTypeJY)
      this.setState({ business: businessTypeJY })
    } else if (idx === '2') {
      this.refreshInfo(businessTypeBABY)
      this.setState({ business: businessTypeBABY })
    }
  }

  refreshInfo = (business) => {
    let data = {}
    if (business === businessTypeJY) {
      const { searchUID1, searchYY1, searchASID1, searchCompereRole1, searchSupportPeriod1, searchSupportStartTime1, searchSupportEndTime1, searchReachTaskDesc1, searchSettlementType1 } = this.state

      let supportStartTime = ''
      let supportEndTime = ''
      if (searchSupportStartTime1) {
        supportStartTime = moment(searchSupportStartTime1).format('YYYY-MM-DD')
      }
      if (searchSupportEndTime1) {
        supportEndTime = moment(searchSupportEndTime1).format('YYYY-MM-DD')
      }

      data = { business: business, uid: searchUID1, yy: searchYY1, asid: searchASID1, compereRole: searchCompereRole1, supportPeriod: searchSupportPeriod1, supportStartTime: supportStartTime, supportEndTime: supportEndTime, reachTaskDesc: searchReachTaskDesc1, settlementType: searchSettlementType1 }
      console.log(data)
    } else {
      const { searchUID2, searchYY2, searchASID2, searchCompereRole2, searchSupportPeriod2, searchSupportStartTime2, searchSupportEndTime2, searchReachTaskDesc2, searchSettlementType2 } = this.state

      let supportStartTime = ''
      let supportEndTime = ''
      if (searchSupportStartTime2) {
        supportStartTime = moment(searchSupportStartTime2).format('YYYY-MM-DD')
      }
      if (searchSupportEndTime2) {
        supportEndTime = moment(searchSupportEndTime2).format('YYYY-MM-DD')
      }

      data = { business: business, uid: searchUID2, yy: searchYY2, asid: searchASID2, compereRole: searchCompereRole2, supportPeriod: searchSupportPeriod2, supportStartTime: supportStartTime, supportEndTime: supportEndTime, reachTaskDesc: searchReachTaskDesc2, settlementType: searchSettlementType2 }
    }

    console.log(data)
    this.props.dispatch({
      type: `${namespace}/listSettlementReport`,
      payload: data
    })
  }

  deleteHandle = (record) => () => {
    const { business } = this.state
    const { dispatch } = this.props
    let data = { id: record.id, business: business }
    console.log(data)
    dispatch({
      type: `${namespace}/deleteSettlementReport`,
      payload: data
    })
  }

  htmlJY = () => {
    const { model: { list } } = this.props
    return <span><span style={{ marginLeft: 15 }}>YY号</span>
      <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchYY1: e })} style={{ width: 120, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>UID</span>
      <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchUID1: e })} style={{ width: 120, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>短位ID</span>
      <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID1: e })} style={{ width: 120, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>主持身份</span>
      <Select style={{ width: 120, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchCompereRole1: v })} allowClear>
        {contractTypeList.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
      </Select>
      <span style={{ marginLeft: 15 }}>扶持周期</span>
      <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchSupportPeriod1: v })} allowClear>
        {supportPeriodList.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
      </Select>
      <span style={{ marginLeft: 15 }}>扶持结束时间</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='开始时间'
        onChange={(v) => this.setState({ searchSupportStartTime1: v })}
        style={{ marginLeft: 10 }}
      />
      <span style={{ marginLeft: 3 }}>~</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='结束时间'
        onChange={(v) => this.setState({ searchSupportEndTime1: v })}
        style={{ marginLeft: 3 }}
      />
      <div style={{ marginBottom: 10 }} />
      <span style={{ marginLeft: 15 }}>达标任务</span>
      <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchReachTaskDesc1: v })} allowClear>
        {reachTaskDescJYList.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
      </Select>
      <span style={{ marginLeft: 15 }}>是否对公</span>
      <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchSettlementType1: v })} allowClear>
        {settlementTypeList.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
      </Select>
      <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle1(businessTypeJY)}>查询</Button>
      <Button style={{ marginLeft: 20 }} type='primary' onClick={this.onExportHandle1()}>选择导出</Button>
      <Button style={{ marginLeft: 20 }} type='primary' onClick={this.onExport1()}>全量导出</Button>
      <div>
        <div>说明：</div>
        <div>1、新星主持数据源：boss_新主持名单-审批通过状态</div>
        <div>2、有效开播天数：①交友: 开播时长≥120min且礼物流水≥10元；②宝贝: 开播时长≥60min且紫贝流水≥5000</div>
      </div>
      <Table style={{ marginTop: 10 }} tabelLayout='fixed' rowSelection={this.rowSelection} rowKey={(record, index) => index} bordered dataSource={list} columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
    </span>
  }

  htmlBaby = () => {
    const { model: { list } } = this.props
    return <span><span style={{ marginLeft: 15 }}>YY号</span>
      <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchYY2: e })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>UID</span>
      <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchUID2: e })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>短位ID</span>
      <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID2: e })} style={{ width: 100, marginLeft: 3 }} />
      <span style={{ marginLeft: 15 }}>主持身份</span>
      <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchCompereRole2: v })} allowClear>
        {contractTypeList.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
      </Select>
      <span style={{ marginLeft: 15 }}>扶持周期</span>
      <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchSupportPeriod2: v })} allowClear>
        {supportPeriodList.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
      </Select>
      <span style={{ marginLeft: 15 }}>扶持结束时间</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='开始时间'
        onChange={(v) => this.setState({ searchSupportStartTime2: v })}
        style={{ marginLeft: 10 }}
      />
      <span style={{ marginLeft: 3 }}>~</span>
      <DatePicker
        format='YYYY-MM-DD'
        placeholder='结束时间'
        onChange={(v) => this.setState({ searchSupportEndTime2: v })}
        style={{ marginLeft: 3 }}
      />
      <div style={{ marginBottom: 10 }} />
      <span style={{ marginLeft: 15 }}>达标任务</span>
      <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchReachTaskDesc2: v })} allowClear>
        {reachTaskDescBabyList.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
      </Select>
      <span style={{ marginLeft: 15 }}>是否对公</span>
      <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchSettlementType2: v })} allowClear>
        {settlementTypeList.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
      </Select>
      <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle2(businessTypeBABY)}>查询</Button>
      <Button style={{ marginLeft: 20 }} type='primary' onClick={this.onExportHandle2()}>选择导出</Button>
      <Button type='primary' style={{ marginLeft: 20 }} onClick={this.onExport2()}>全量导出</Button>
      <div>
        <div>说明：</div>
        <div>1、新星主持数据源：boss_新主持名单-审批通过状态</div>
        <div>2、有效开播天数：①交友: 开播时长≥120min且礼物流水≥10元；②宝贝: 开播时长≥60min且紫贝流水≥5000</div>
      </div>
      <Table style={{ marginTop: 10 }} tabelLayout='fixed' rowSelection={this.rowSelection} rowKey={(record, index) => index} bordered dataSource={list} columns={this.columns1} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
    </span>
  }

  searchHandle1 = () => () => {
    this.refreshInfo(businessTypeJY)
  }

  searchHandle2 = () => () => {
    this.refreshInfo(businessTypeBABY)
  }

  onExportHandle1 = () => () => {
    let headers = []
    let columns = this.columns
    const { exportKey } = this.state
    columns.forEach(function (item) {
      if (item.title !== '操作') {
        if (item.title === '时间') {
          headers.push({ k: 'startTime', v: '扶持开始' })
          headers.push({ k: 'endTime', v: '扶持结束' })
        } else {
          headers.push({ k: item.dataIndex, v: item.title })
        }
      }
    })
    let list = []
    exportKey.forEach(function (item) {
      item.compereRole = contractTypeMap[item.compereRole]
      item.supportPeriod = supportPeriodMap[item.supportPeriod]
      item.settlementType = settlementTypeMap[item.settlementType]
      item.reachTaskDesc = reachTaskDescJYMap[item.reachTaskDesc]
      item.startTime = moment.unix(item.supportStartTime).format('YYYY-MM-DD')
      item.endTime = moment.unix(item.supportEndTime).format('YYYY-MM-DD') + ' 24点'
      list.push(item)
    })
    exportExcel(headers, list, '新星主持结算报表_交友.xlsx')
  }

  onExportHandle2 = () => () => {
    let headers = []
    let columns = this.columns1
    const { exportKey } = this.state
    columns.forEach(function (item) {
      if (item.title !== '操作') {
        if (item.title === '时间') {
          headers.push({ k: 'startTime', v: '扶持开始' })
          headers.push({ k: 'endTime', v: '扶持结束' })
        } else {
          headers.push({ k: item.dataIndex, v: item.title })
        }
      }
    })
    let list = []
    exportKey.forEach(function (item) {
      item.compereRole = contractTypeMap[item.compereRole]
      item.supportPeriod = supportPeriodMap[item.supportPeriod]
      item.settlementType = settlementTypeMap[item.settlementType]
      item.reachTaskDesc = reachTaskDescBabyMap[item.reachTaskDesc]
      item.startTime = moment.unix(item.supportStartTime).format('YYYY-MM-DD')
      item.endTime = moment.unix(item.supportEndTime).format('YYYY-MM-DD') + ' 24点'
      list.push(item)
    })
    exportExcel(headers, list, '新星主持结算报表_宝贝.xlsx')
  }

  onExport1 = () => () => {
    const { model: { list } } = this.props
    let headers = []
    let columns = this.columns
    columns.forEach(function (item) {
      if (item.title !== '操作') {
        if (item.title === '时间') {
          headers.push({ k: 'startTime', v: '扶持开始' })
          headers.push({ k: 'endTime', v: '扶持结束' })
        } else {
          headers.push({ k: item.dataIndex, v: item.title })
        }
      }
    })
    let listNew = []
    list.forEach(function (item) {
      item.compereRole = contractTypeMap[item.compereRole]
      item.supportPeriod = supportPeriodMap[item.supportPeriod]
      item.settlementType = settlementTypeMap[item.settlementType]
      item.reachTaskDesc = reachTaskDescJYMap[item.reachTaskDesc]
      item.startTime = moment.unix(item.supportStartTime).format('YYYY-MM-DD')
      item.endTime = moment.unix(item.supportEndTime).format('YYYY-MM-DD') + ' 24点'
      listNew.push(item)
    })
    exportExcel(headers, listNew, '新星主持结算报表_交友.xlsx')
  }

  onExport2 = () => () => {
    const { model: { list } } = this.props
    let headers = []
    let columns = this.columns1
    columns.forEach(function (item) {
      if (item.title !== '操作') {
        if (item.title === '时间') {
          headers.push({ k: 'startTime', v: '扶持开始' })
          headers.push({ k: 'endTime', v: '扶持结束' })
        } else {
          headers.push({ k: item.dataIndex, v: item.title })
        }
      }
    })
    let listNew = []
    list.forEach(function (item) {
      item.compereRole = contractTypeMap[item.compereRole]
      item.supportPeriod = supportPeriodMap[item.supportPeriod]
      item.settlementType = settlementTypeMap[item.settlementType]
      item.reachTaskDesc = reachTaskDescBabyMap[item.reachTaskDesc]
      item.startTime = moment.unix(item.supportStartTime).format('YYYY-MM-DD')
      item.endTime = moment.unix(item.supportEndTime).format('YYYY-MM-DD') + ' 24点'
      listNew.push(item)
    })
    exportExcel(headers, listNew, '新星主持结算报表_宝贝.xlsx')
  }

  render () {
    const { route } = this.props
    const { TabPane } = Tabs

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='1' onChange={(idx) => this.onTagChange(idx)} type='card' size='large'>
            <TabPane tab='交友' key='1'>
              {this.htmlJY()}
            </TabPane>
            <TabPane tab='宝贝' key='2'>
              { this.htmlBaby() }
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default NewCompereReport
