import React, { Component } from 'react'
// import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import { connect } from 'dva'
import CompereLibrary from './components/compere_library'

// const namespace = 'compereLibrary' // model 的 namespace
const TabPane = Tabs.TabPane

@connect(({ compereLibrary }) => ({ // model 的 namespace
  model: compereLibrary // model 的 namespace
}))
class Index extends Component { // 默认页面组件，不需要修改
  /** *******************************页面布局*************************************************************/
  render () {
    const { model: { compereList } } = this.props

    return (
      <Tabs>
        <TabPane>
          <CompereLibrary configList={compereList} />
        </TabPane>
      </Tabs>
    )
  }
}

export default Index // 保证唯一
