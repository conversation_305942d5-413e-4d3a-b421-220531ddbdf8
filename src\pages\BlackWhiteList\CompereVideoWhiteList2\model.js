/* eslint-disable eqeqeq */
import { getLists, videoCompereAdd, videoCompereDel } from './api'
import { Modal, message } from 'antd'
import { checkUid, checkSid } from '@/utils/common'

export default {
  namespace: 'compereVideoWhiteList2',
  state: {
    updating: false, // 添加白名单事件是否在处理中
    displayData: [], // 白名单列表显示的数据
    selectUid: 0, // 删除白名单选中的uid
    selectSid: 0, // 删除白名单选中的sid
    confirmMsg: '确认要删除吗？', // 删除白名单确认框提示信息
    newUid: '',
    newSid: '',
    addReason: '',
    isInit: false
  },

  reducers: {
    // 更新白名单数据列表
    displayList (state, { payload: data }) {
      if (!Array.isArray(data)) {
        console.error('unexpect data type: data=', data)
        return
      }
      return {
        ...state,
        displayData: data
      }
    },
    // 设置‘添加白名单-添加’按钮的状态，true为正在处理中
    setBtnStatus (state, { payload: status }) {
      if (status !== true && status !== false) {
        console.error('unexpect argument in setBtnStatus: status=', status)
        return
      }
      return {
        ...state,
        updating: status
      }
    }
  },

  effects: {
    // 请求并更新白名单列表数据
    * getVidioComperData ({ params }, { select, call, put }) {
      let respStr = yield call(getLists)
      let { data: { status, compereList } } = respStr
      if (status !== 0) {
        console.error('getVidioComperData fail, response=', respStr)
        Modal.error({ content: '获取表单数据失败，请检查控制台' })
        return
      }
      if (!Array.isArray(compereList)) {
        console.error('compereList ont a Array, compereList=', compereList)
        Modal.error({ content: '获取表单数据失败，请检查控制台' })
        return
      }
      for (let i = 0; i < compereList.length; i++) {
        compereList[i].idx = i + 1
      }
      yield put({
        type: 'displayList',
        payload: compereList
      })
    },
    // 添加白名单
    * addVidioComperData ({ payload }, { call, put }) {
      const { uid, sid, reason, callback } = payload
      if (!checkUid(uid) || !checkSid(sid)) {
        return
      }
      if (typeof reason !== 'string' || reason.trim() == '') {
        message.error('输入有误： reason=' + reason)
        return
      }
      yield put({
        type: 'setBtnStatus',
        payload: true
      })
      let resp = yield call(videoCompereAdd, uid, sid, reason.trim())
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[添加白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 1) {
        message.success('添加成功')
        if (callback) {
          callback()
        }
      } else {
        Modal.warn({ content: '添加失败, 请检查控制台' })
        console.error('addVidioComperData()：[添加白名单] 返回结果为：', resp, '提示: status=0 时可能更新了旧的白名单')
      }
      yield put({
        type: 'setBtnStatus',
        payload: false
      })
    },
    // 删除白名单
    * delVidioComperData ({ payload }, { call, put }) {
      const { uid, sid } = payload
      if (!checkUid(uid) || !checkSid(sid)) {
        return
      }
      let resp = yield call(videoCompereDel, uid, sid)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 1) {
        message.success('删除成功')
        yield put({ // 更新列表
          type: 'getVidioComperData'
        })
      } else {
        Modal.error({ content: '删除失败：status=' + status })
        console.error('delVidioComperData() resp=', resp)
      }
    }
  }
}

// =========== 备注 ==========
// 【前端变化】
// 获取白名单列表的get请求不加参数
// 新增白名单、删除白名单的POST请求表单不再使用test字段
// ‘添加白名单-添加到’ 选择框 没有实际用途，已移除
