/* eslint-disable eqeqeq */
import { getLists, whiteListAdd, whiteListDel } from './api'
import { Modal, message } from 'antd'
import { checkUid, checkSid } from '@/utils/common'

export default {
  namespace: 'compereLiveNotifyWhiteList',
  state: {
    updating: false,
    displayData: [],
    newUid: '',
    selectSid: '',
    newSid: ''
  },

  reducers: {
    // 更新data到白名单数据列表
    displayList (state, { payload: data }) {
      if (!Array.isArray(data)) {
        console.error('unexpect data type: data=', data)
        return
      }
      return {
        ...state,
        displayData: data
      }
    },
    // 设置‘添加标签页-添加’按钮的状态，true为正在处理中
    setBtnStatus (state, { payload: status }) {
      if (status !== true && status !== false) {
        console.error('unexpect argument in setBtnStatus: status=', status)
        Modal.error('发生错误，请查看控制台')
        return
      }
      return {
        ...state,
        updating: status
      }
    }
  },

  effects: {
    // 请求并刷新白名单列表数据
    * getWhiteListData ({ params }, { select, call, put }) {
      let resp = yield call(getLists)
      let { data: { status, compereList } } = resp
      if (compereList === null) {
        message.warning('数据为空')
        yield put({
          type: 'displayList',
          payload: []
        })
        return
      }
      if (status !== 0 || !Array.isArray(compereList)) {
        console.error('getWhiteListData() get data error: response=', resp)
        Modal.error({ content: '获取白名单数据失败，请检查控制台' })
        return
      }
      for (let i = 0; i < compereList.length; i++) {
        compereList[i].idx = i + 1
      }
      yield put({
        type: 'displayList',
        payload: compereList
      })
    },
    // 添加白名单
    * addWhiteListByUid ({ payload }, { call, put }) {
      const { newUid, newSid, callback } = payload
      if (!checkUid(newUid) || !checkSid(newSid)) {
        return
      }
      yield put({
        type: 'setBtnStatus',
        payload: true
      })
      let resp = yield call(whiteListAdd, newUid, newSid)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[添加白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 0) {
        message.success('添加成功')
        if (callback) {
          callback()
        }
      } else {
        console.error('addWhiteListByUid()：[添加白名单] 返回结果为：', resp)
        Modal.warn({ content: '添加失败, 请检查控制台' })
      }
      yield put({
        type: 'setBtnStatus',
        payload: false
      })
    },
    // 删除白名单
    * delWhiteListByUidSid ({ payload }, { call, put }) {
      const { uid, sid } = payload
      if (!checkUid(uid) || !checkSid(sid)) {
        return
      }
      let resp = yield call(whiteListDel, uid, sid)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 0) {
        message.success('删除成功')
        yield put({ // 更新列表
          type: 'getWhiteListData'
        })
      } else {
        Modal.error({ content: '删除失败：status=' + status })
        console.error('delWhiteListByUidSid() resp=', resp)
      }
    }
  }
}
