import { connect } from 'dva'
import React, { Component } from 'react'
import { Row, Col, Table } from 'antd'
import SearchParams from '@/components/SimpleComponents/searchParams'

const namespace = 'hgameTingTools'

@connect(({ hgameTingTools }) => ({
  model: hgameTingTools
}))

class StatExport extends Component {
  state = {
    paramsVal: {}
  }

  limitOptions = [
    { label: '20', value: 20 },
    { label: '50', value: 50 },
    { label: '100', value: 100 },
    { label: '200', value: 200 },
    { label: '500', value: 500 },
    { label: '1000', value: 100 },
    { label: '不限制', value: 0 }
  ]

  // 查询条件
  parmasColumn = [
    { label: '活动ID', fieldName: 'actId', inputType: 'Input', defaultValue: '', extProps: { style: { width: '7em' } } },
    { label: '频道ID', fieldName: 'channelId', inputType: 'InputNumber', defaultValue: null },
    { label: '开始时间', fieldName: 'startTime', formatTimeRange: true, inputType: 'DatePicker', defaultValue: null, extProps: { style: { width: '10em' } } },
    { label: '结束时间', fieldName: 'endTime', formatTimeRange: true, inputType: 'DatePicker', defaultValue: null, extProps: { style: { width: '10em' } } },
    { label: '数量限制', fieldName: 'limit', inputType: 'Selecter', defaultValue: 200, extProps: { style: { width: '7em' }, options: this.limitOptions } },
    { label: '查询', inputType: 'Button', extProps: { type: 'primary', onClick: () => { this.queryStatList(this.state.paramsVal, 'preview') } } },
    { label: '导出', inputType: 'Button', extProps: { onClick: () => { this.queryStatList(this.state.paramsVal, 'export') } } }
  ]

  componentDidMount = () => {
    this.queryStatList(this.state.paramsVal, 'preview')
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  queryStatList = (params, queryType) => {
    params.queryType = queryType
    params.actId = params.actId || ''
    params.startTime = params.startTime || 0
    params.endTime = params.endTime || 0
    params.channelId = params.channelId || 0
    params.limit = params.limit || 0
    console.debug('params==>', params)
    this.callModel('statPreviewOrExport', {
      params: params,
      isDownloadMode: (queryType === 'export')
    })
  }

  render () {
    const { statPreview } = this.props.model

    const columns = [
      { title: '日期', dataIndex: 'data' },
      { title: '公会ID', dataIndex: 'sid' },
      { title: '短位ID', dataIndex: 'asid' },
      { title: '厅战时间', dataIndex: 'startTime' },
      { title: '厅战时长', dataIndex: 'duration' },
      { title: '房管厅个数', dataIndex: 'tingCount' },
      { title: '厅战礼物流水（元）', dataIndex: 'totalFlow' },
      { title: 'yo直播厅战流水', dataIndex: 'yoFlow' },
      { title: 'yo语音厅战流水', dataIndex: 'zwFlow' },
      { title: '手Y厅战流水', dataIndex: 'yyFlow' },
      { title: '送礼用户数', dataIndex: 'sender' },
      { title: '房管厅收礼主持数', dataIndex: 'receiver' },
      { title: '公会奖励（元）', dataIndex: 'owReward' },
      { title: '平台奖励（元）', dataIndex: 'extReward' },
      { title: '总奖励（元）', dataIndex: 'totalReward' }
    ].map((item) => {
      item.align = 'center'
      return item
    })

    return (
      <Row>
        <Col>
          <SearchParams formatTimeRange columns={this.parmasColumn} onChange={(v) => { this.setState({ paramsVal: v }) }} />
        </Col>

        <Col span={24}>
          <Table columns={columns} dataSource={statPreview} size='small' pagination={{ pageSize: 50 }} />
        </Col>
      </Row>
    )
  }
}

export default StatExport
