import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const getConfigList = genGetRequireTemplate('/luxury_activity_boss/get_stage_info', 'sessionList')

const updateConfig = genUpdateTemplate('/luxury_activity_boss/update_stage_info')

export default {
  namespace: 'competitionSession',
  state: {
    sessionList: []
  },
  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getConfigList,
    updateConfig
  }
}
