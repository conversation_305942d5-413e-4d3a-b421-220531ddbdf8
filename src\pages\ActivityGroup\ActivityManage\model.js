import { addActivity, pageListActivity, pageListBusiness, pageListGroupResult, pageListRuleData, pushMessageFromServer, pushConfirmMessage, removeActivity, removeActivityRoleInfo, removeGroupResult, updateActivityBasicInfo, updateGroupResult, upsertActivityRoleInfo, upsertGroupResult } from '../common'
import { message } from 'antd'
import { uploadScreenshotFile } from '@/utils/common'

export default {
  namespace: 'ActivityManage',
  state: {
    dataList: [],
    businessList: []
  },

  reducers: {
    updateList (state, { payload }) {
      let obj = { ...state }
      obj[payload.name] = (payload.list || []).map((i, index) => {
        i.index = index + 1
        return i
      })
      return obj
    },

    updateState (state, { payload }) {
      let obj = { ...state }
      if (payload) {
        for (let name in payload) {
          obj[name] = payload[name]
        }
      }
      return obj
    }
  },

  // 数据变更
  effects: {
    // 分页查询规则函数列表
    * pageListActivity ({ payload, callback }, { call, put }) {
      const { data } = yield call(pageListActivity, payload)
      yield put({
        type: 'updateList',
        payload: {
          name: 'dataList',
          list: data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
        }
      })

      if (callback) {
        callback(data.data)
      }
    },

    // 分页查询分组结果列表
    * pageListGroupResult ({ payload, callback, exportMode }, { call, put }) {
      const { data } = yield call(pageListGroupResult, payload)

      if (!exportMode) {
        yield put({
          type: 'updateList',
          payload: {
            name: 'groupResultList',
            list: data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
          }
        })
      }

      if (callback) {
        callback(data.data)
      }
    },

    * listAllActivityOptions ({ payload }, { call, put }) {
      const { data } = yield call(pageListActivity, { pageNo: -1, pageSize: -1 })
      let list = data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
      let options = []
      let activityMap = {}
      list.forEach(item => {
        activityMap[item.id] = item
        options.push({ label: item.id + ' | ' + item.name, value: item.id })
      })
      yield put({
        type: 'updateState',
        payload: {
          activityOptions: options,
          activityMap: activityMap
        }
      })
    },

    * listAllBusiness ({ payload }, { call, put }) {
      const { data } = yield call(pageListBusiness, { pageNo: -1, pageSize: -1 })
      let businessList = data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
      let businessOptions = []
      let businessMap = {}
      businessList.forEach(item => {
        businessMap[item.id] = item
        businessOptions.push({ label: item.name, value: item.id })
      })
      yield put({
        type: 'updateList',
        payload: {
          name: 'businessList',
          list: businessList
        }
      })
      yield put({
        type: 'updateState',
        payload: {
          businessOptions: businessOptions,
          businessMap: businessMap
        }
      })
    },

    * listAllRuleData ({ payload }, { call, put }) {
      const { data } = yield call(pageListRuleData, { pageNo: -1, pageSize: -1 })
      let ruleDataList = data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
      let ruleDataMap = {}
      let ruleDataOptions = []
      ruleDataList.forEach(item => {
        ruleDataMap[item.id] = item
        ruleDataOptions.push({ label: item.name, value: item.id + ':' + item.name, data: item })
      })
      yield put({
        type: 'updateList',
        payload: {
          name: 'ruleDataList',
          list: ruleDataList
        }
      })
      yield put({
        type: 'updateState',
        payload: {
          ruleDataMap: ruleDataMap,
          ruleDataOptions: ruleDataOptions
        }
      })
    },

    * updateBasicInfo ({ payload, callback }, { call, put }) {
      const { data } = yield call(updateActivityBasicInfo, payload)
      if (callback) {
        callback(data)
      }
    },

    * addActivity ({ payload, callback }, { call, put }) {
      const { data } = yield call(addActivity, payload)
      if (callback) {
        callback(data)
      }
    },

    * removeActivity ({ payload, callback }, { call, put }) {
      const { data } = yield call(removeActivity, payload)
      if (callback) {
        callback(data)
      }
    },

    * upsertRoleInfo ({ payload, callback }, { call, put }) {
      const { data } = yield call(upsertActivityRoleInfo, payload)
      if (callback) {
        callback(data)
      }
    },

    * removeRoleInfo ({ payload, callback }, { call, put }) {
      const { data } = yield call(removeActivityRoleInfo, payload)
      if (callback) {
        callback(data)
      }
    },

    * upsertGroupResult ({ payload, callback }, { call, put }) {
      const { data } = yield call(upsertGroupResult, payload)
      if (callback) {
        callback(data)
      }
    },

    * updateGroupResult ({ payload, callback }, { call, put }) {
      const { data } = yield call(updateGroupResult, payload)
      if (callback) {
        callback(data)
      }
    },

    * removeGroupResult ({ payload, callback }, { call, put }) {
      const { data } = yield call(removeGroupResult, payload)
      if (callback) {
        callback(data)
      }
    },

    * pushMessage ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(pushMessageFromServer, payload)

      if (status !== 0) {
        message.error('push message ' + msg, 5)
      }
    },

    * pushConfirmMessage ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(pushConfirmMessage, payload)

      if (status !== 0) {
        message.error('push confirm message ' + msg, 5)
      }
    },

    // 上传文件
    * uploadFile ({ file, callback }, { call, put }) {
      if (!callback) {
        message.warn('callback is required')
        return
      }
      const url = yield call(uploadScreenshotFile, file)
      callback(url, 0, '')
    }

  }
}
