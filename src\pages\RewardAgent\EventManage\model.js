/* eslint-disable eqeqeq */
import { message } from 'antd'
import { getGroupInfo, delEvent, addUpdateEvent, readFiles, getRewardNotifyConfig, upsetRewardNotifyConfig, rewardNotifyConfigCommit, deleteUserAwardApply, updateUserAwardApply, notify<PERSON>ser<PERSON>wardApply, getAllRewardNeedConfigList, getAllHdztActList, getHdztRanksUserList, getHdztActRankConfigList } from './api'
import { checkResp, genGetRequireTemplate } from '@/utils/common'
import { getAllIntoTypeFromServer } from '../RewardMange/api'
import { mgetUserInfo, userListToMap } from '@/utils/userinfo'

const getActivityList = genGetRequireTemplate('/reward_agent/activity/list2', 'displayList2')
const getAllActivityList = genGetRequireTemplate('/reward_agent/activity/list', 'displayList')

export default {
  namespace: 'eventManage',
  state: {
    displayList: [],
    groupInfo: [], // 业务组数据
    rewardList: [], // 发奖列表
    commitStatus: 0, // 暂存状态
    displayList2: [] // 活动列表-重构版
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },

    updateRewardList (state, { payload }) { return { ...state, rewardList: payload.list, commitStatus: payload.commit } },

    // 用户输入类型
    updateInfoTypeList (state, { payload }) { return { ...state, infoTypeList: payload.list, infoTypeNameMap: payload.infoTypeNameMap } },

    // 奖品列表
    updatePrizeTypeList (state, { payload }) { return { ...state, prizeList: payload.list, prizeNameMap: payload.prizeNameMap } },

    // 活动中台活动列表
    updateHdztActList (state, { payload }) { return { ...state, hdztActList: payload.list } },

    // 活动中台活动榜单配置列表
    updateHdztActRankConfigList (state, { payload }) { return { ...state, currentActRankList: payload.list } }
  },

  effects: {
    getActivityList,
    getAllActivityList,
    // 获取所有用户输入类型
    * getAllInfoType ({ payload }, { call, put }) {
      const { data: { status, msg, list } } = yield call(getAllIntoTypeFromServer)

      if (status !== 0) {
        message.warn('get info type error:' + msg, 2)
        return
      }

      const infoTypeList = Array.isArray(list) ? list : []
      const infoTypeNameMap = {}
      infoTypeList.forEach((item) => {
        infoTypeNameMap[item.infoName] = item
      })

      yield put({
        type: 'updateInfoTypeList',
        payload: {
          list: infoTypeList,
          infoTypeNameMap: infoTypeNameMap
        }
      })
    },

    // 获取所有的奖品类型
    * getAllPrizeList ({ payload }, { call, put }) {
      const { data: { status, msg, list } } = yield call(getAllRewardNeedConfigList)

      if (status !== 0) {
        message.warn('get prize list error:' + msg, 2)
        return
      }

      const prizeList = Array.isArray(list) ? list : []
      const nameMap = {}
      prizeList.forEach((item) => {
        nameMap[item.name] = item
      })

      yield put({
        type: 'updatePrizeTypeList',
        payload: {
          list: prizeList,
          prizeNameMap: nameMap
        }
      })
    },

    // 获取所有的活动中台活动列表
    * getAllHdztActList ({ payload }, { call, put }) {
      const { data: { status, msg, list } } = yield call(getAllHdztActList)

      if (status !== 0) {
        message.warn('get hdzt act list error:' + msg, 2)
        return
      }

      const dataList = Array.isArray(list) ? list : []
      yield put({
        type: 'updateHdztActList',
        payload: {
          list: dataList
        }
      })
    },

    // 获取指定活动的榜单配置列表
    * getHdztActRankConfigList ({ payload, callback }, { call, put }) {
      const { data: { status, msg, list } } = yield call(getHdztActRankConfigList, payload)

      if (status !== 0) {
        message.warn('get hdzt act rank config list error:' + msg, 2)
        return
      }

      const dataList = Array.isArray(list) ? list : []
      yield put({
        type: 'updateHdztActRankConfigList',
        payload: {
          list: dataList
        }
      })

      if (callback) {
        callback(list, status, msg)
      }
    },

    // 获取活动中台制定排行榜用户列表
    * getHdztRanksUserList ({ actId, payload, callback }, { call, put }) {
      const { data: { status, msg, list } } = yield call(getHdztRanksUserList, { actId: actId, reqList: payload })

      if (status !== 0) {
        message.warn('get hdzt act rank user list error:' + msg, 2)
        return
      }

      const dataList = Array.isArray(list) ? list : []
      if (callback) {
        callback(dataList)
      }
    },
    // 查询业务组数据
    * queryGroupInfo ({ payload }, { select, call, put }) {
      let resp = yield call(getGroupInfo)
      if (!checkResp(resp)) {
        message.error('查询业务组失败')
        return
      }
      resp = resp.data
      if (resp.list == null) {
        message.warn('业务列表为空')
        return
      }
      for (let i = 0; i < resp.list.length; i++) {
        resp.list[i].key = i + 1
      }
      yield put({
        type: 'updateState',
        payload: { name: 'groupInfo', newValue: resp.list }
      })
    },
    // 删除活动
    * EventDelete ({ payload }, { select, call, put }) {
      const { params, cbFunc } = payload
      console.debug('add params=', params)
      let res = yield call(delEvent, params)
      if (!checkResp(res)) {
        cbFunc(false)
        return
      }
      cbFunc(true)
    },
    // 更新或添加活动
    * EventUpdateAdd ({ payload }, { select, call, put }) {
      const { params, cbFunc } = payload
      console.debug('update payload=', params)
      let res = yield call(addUpdateEvent, params)
      if (!checkResp(res)) {
        cbFunc(false)
        return
      }
      cbFunc(true)
    },

    // read file
    * readFile ({ payload }, { call, put }) {
      const { data: { list } } = yield call(readFiles, payload)

      yield put({
        type: `updateRewardList`,
        payload: { list: Array.isArray(list) ? list : [], commit: payload.commitStatus }
      })
    },

    * updateLocalAwardList ({ payload }, { call, put }) {
      yield put({
        type: `updateRewardList`,
        payload: { list: Array.isArray(payload.list) ? payload.list : [], commit: payload.commitStatus }
      })
    },

    * getRewardNotifyConfig ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getRewardNotifyConfig, payload)

      yield put({
        type: `updateRewardList`,
        payload: { list: Array.isArray(list) ? list : [], commit: (list || []).find(v => v.commitStatus > 0) !== undefined ? 1 : 0 }
      })
    },

    * upsetRewardNoitfyConfig ({ payload, callback }, { call, put }) {
      const { data: { status, msg } } = yield call(upsetRewardNotifyConfig, payload)

      if (status !== 0) {
        message.error('提交失败 ' + msg + ', 请重试.')
        return
      }

      message.success('更新成功')
      yield put({
        type: `getRewardNotifyConfig`,
        payload: { actId: payload.actId }
      })
    },

    * rewardNotifyCommit ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(rewardNotifyConfigCommit, payload)

      if (status !== 0) {
        message.error('提交失败 ' + msg + ',请重试')
        return
      }

      message.success('提交成功')
      yield put({
        type: `getRewardNotifyConfig`,
        payload: payload
      })
    },

    // 删除用户奖励
    * deleteUserAward ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(deleteUserAwardApply, payload)

      if (status === 0) {
        if (payload.callback) {
          payload.callback()
        }
        return
      }
      message.error('删除失败：' + msg)
    },

    // 删除用户奖励
    * updateUserAward ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updateUserAwardApply, payload.info)

      if (status === 0) {
        if (payload.callback) {
          payload.callback()
        }
        return
      }
      message.error('更新失败：' + msg)
    },

    // 通知用户填写奖励所需信息
    * notifyUserApply ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(notifyUserAwardApply, payload.info)

      if (status === 0) {
        if (payload.callback) {
          payload.callback()
        }
        return
      }
      message.error('更新失败：' + msg)
    },

    // 批量获取用户信息, payload int64[]
    * batchGetUserInfo ({ uidList, callback }, { call, put }) {
      let uRsp = yield call(mgetUserInfo, uidList)
      const { data } = uRsp
      const { status, list } = data
      let userInfoMap = {}
      if (status === 0 && list && list.length > 0) {
        userInfoMap = userListToMap(list)
      }
      if (callback) {
        callback(userInfoMap)
      }
    }

  }
}
