import React from 'react'
import PropTypes from 'prop-types'
import { Layout } from 'antd'
import DocumentTitle from 'react-document-title'
import isEqual from 'lodash/isEqual'
import memoizeOne from 'memoize-one'
import { connect } from 'dva'
import { ContainerQuery } from 'react-container-query'
import classNames from 'classnames'
import pathToRegexp from 'path-to-regexp'
// import { enquireScreen, unenquireScreen } from 'enquire-js'
// import { formatMessage } from 'umi/locale'
import SiderMenu from '@/components/SiderMenu'
// import Authorized from '@/utils/Authorized'
// // import SettingDrawer from '@/components/SettingDrawer' 
import Footer from './Footer'
import Header from './Header'
import Context from './MenuContext'
import { getUid, getUserName } from '@yy/fr-auth'
// import Exception403 from '../pages/Exception/403'

const { Content } = Layout

// Conversion router to menu.
function formatter (data, parentPath = '', parentAuthority) {
  return data.filter(item => item.name).map(item => {
    const result = {
      ...item,
      authority: item.authority || parentAuthority
    }
    if (item.routes) {
      const children = formatter(item.routes, `${parentPath}${item.path}/`, item.authority)
      // Reduce memory usage
      result.children = children
    }
    delete result.routes
    return result
  })
}

const query = {
  'screen-xs': {
    maxWidth: 575
  },
  'screen-sm': {
    minWidth: 576,
    maxWidth: 767
  },
  'screen-md': {
    minWidth: 768,
    maxWidth: 991
  },
  'screen-lg': {
    minWidth: 992,
    maxWidth: 1199
  },
  'screen-xl': {
    minWidth: 1200,
    maxWidth: 1599
  },
  'screen-xxl': {
    minWidth: 1600
  }
}

class BasicLayout extends React.PureComponent {
  constructor (props) {
    super(props)
    this.getPageTitle = memoizeOne(this.getPageTitle)
    this.getBreadcrumbNameMap = memoizeOne(this.getBreadcrumbNameMap, isEqual)
    this.breadcrumbNameMap = this.getBreadcrumbNameMap()
    this.matchParamsPath = memoizeOne(this.matchParamsPath, isEqual)
  }

  state = {
    rendering: true,
    isMobile: false
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: 'global/fetchCurrent',
      payload: {
        uid: getUid(),
        username: getUserName()
      }
    })

    dispatch({
      type: 'global/fetchSubscribeMenus'
    })

    dispatch({
      type: 'global/fetchOutURL'
    })

    this.renderRef = window.requestAnimationFrame(() => {
      this.setState({
        rendering: false
      })
    })
  }

  componentDidUpdate (preProps) {
    // After changing to phone mode,
    // if collapsed is true, you need to click twice to display
    this.breadcrumbNameMap = this.getBreadcrumbNameMap()
    // const { isMobile } = this.state
    // const { collapsed } = this.props
    // if (isMobile && !preProps.isMobile && !collapsed) {
    //   this.handleMenuCollapse(false)
    // }
  }

  componentWillUnmount () {
    window.cancelAnimationFrame(this.renderRef)
    // unenquireScreen(this.enquireHandler)
  }

  getContext () {
    const { location } = this.props
    return {
      location,
      breadcrumbNameMap: this.breadcrumbNameMap
    }
  }

  getMenuData () {
    // const {
    //   route: { routes }
    // } = this.props
    const { menus } = this.props
    // console.debug('basicLayout ---> getMenusData ---> props:', this.props)
    return formatter(menus)
  }

  /**
   * 获取面包屑映射
   * @param {Object} menuData 菜单配置
   */
  getBreadcrumbNameMap () {
    const routerMap = {}
    const mergeMenuAndRouter = data => {
      data.forEach(menuItem => {
        if (menuItem.children) {
          mergeMenuAndRouter(menuItem.children)
        }
        // Reduce memory usage
        routerMap[menuItem.path] = menuItem
      })
    }
    mergeMenuAndRouter(this.getMenuData())
    return routerMap
  }

  matchParamsPath = pathname => {
    const pathKey = Object.keys(this.breadcrumbNameMap).find(key =>
      pathToRegexp(key).test(pathname)
    )
    return this.breadcrumbNameMap[pathKey]
  }

  getPageTitle = pathname => {
    const currRouterData = this.matchParamsPath(pathname)

    if (!currRouterData || !currRouterData.name) {
      return '交友 Boss'
    }

    return `${currRouterData.name} - 交友 Boss`
  };

  getLayoutStyle = () => {
    const { fixSiderbar, collapsed, layout } = this.props
    if (fixSiderbar && layout !== 'topmenu') {
      return {
        paddingLeft: collapsed ? '80px' : '256px'
      }
    }
    return null
  };

  getContentStyle = () => {
    const { fixedHeader } = this.props
    return {
      margin: '24px 24px 0',
      paddingTop: fixedHeader ? 64 : 0
    }
  }

  handleMenuCollapse = collapsed => {
    const { dispatch } = this.props
    dispatch({
      type: 'global/changeLayoutCollapsed',
      payload: collapsed
    })
  };

  render () {
    const {
      navTheme,
      children,
      location: { pathname }
    } = this.props
    const menuData = this.getMenuData()
    // const routerConfig = this.matchParamsPath(pathname)
    // console.debug('menuData===>', menuData)
    const layout = (
      <Layout>
        <SiderMenu
          // Authorized={Authorized}
          theme={navTheme}
          onCollapse={this.handleMenuCollapse}
          menuData={menuData}
          {...this.props}
        />
        <Layout
          style={{
            ...this.getLayoutStyle(),
            minHeight: '100vh'
          }}
        >
          <Header
            handleMenuCollapse={this.handleMenuCollapse}
            {...this.props}
          />
          <Content style={this.getContentStyle()}>
            {/* <Authorized authority={routerConfig.authority} noMatch={<Exception403 />}> */}
            {children}
            {/* </Authorized> */}
          </Content>
          <Footer />
        </Layout>
      </Layout>
    )
    return (
      <React.Fragment>
        <DocumentTitle title={this.getPageTitle(pathname)}>
          <ContainerQuery query={query}>
            {params => (
              <Context.Provider value={this.getContext()}>
                <div className={classNames(params)}>{layout}</div>
              </Context.Provider>
            )}
          </ContainerQuery>
        </DocumentTitle>
      </React.Fragment>
    )
  }
}

BasicLayout.propTypes = {
  location: PropTypes.object.isRequired
}

export default connect(({ global }) => ({
  ...global
}))(BasicLayout)
