import React, { Component } from 'react'
import { connect } from 'dva'
import { Divider, Table, Input, message } from 'antd'
import { Tip, ConfigItem, viewColumns } from '../common'
import { AprInfoDesc } from '../../../../components/SimpleComponents'
const namespace = 'flameGame'

@connect(({ flameGame }) => ({
  model: flameGame
}))

class PoolView extends Component {
  state = {
    rawConfig: {}, // 对应PoolConfig结构
    poolConfig: {}
  }

  componentDidMount = () => {
    this.getPoolConifig()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 获取奖池配置
  getPoolConifig = () => {
    this.callModel('getFlameConfig', {
      params: { id: this.props.poolId },
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg, data } = ret
        if (status < 0) {
          message.warn('获取配置失败: ' + msg)
          return
        }
        const poolConfig = JSON.parse(data.content)
        this.setState({ rawConfig: data, poolConfig: poolConfig })
      }
    })
  }

  render () {
    const { rawConfig, poolConfig } = this.state
    const styleInput = { width: '10em', marginRight: '1em' }
    return (
      <div>
        <Tip />
        <div style={{ marginTop: '1em' }}>
          <AprInfoDesc value={rawConfig.aprInfo} />
        </div>
        <Divider />
        <div>
          <ConfigItem title='全平台日兑换紫水晶券总额度' tooltip='每日该玩法下,可产出紫水晶券的总上限'>
            <Input value={poolConfig.dailyLimitGlobal / 1000} style={styleInput} disabled />元
          </ConfigItem>
          <ConfigItem title='单UID紫水晶券总额度(耐久度)' tooltip='单日该玩法下，单人可产出紫水晶券的总上线'>
            <Input value={poolConfig.dailyLimitUser / 1000} style={styleInput} disabled />元
          </ConfigItem>
          <ConfigItem title='扣除损耗率' tooltip='单次升级失败扣除的损耗度'>
            <Input value={poolConfig.deductRate} style={styleInput} disabled />%
          </ConfigItem>
          <ConfigItem title='升级成功率' tooltip='单次升级的总价值区分对应获得礼物的概率，输入0%代表必失败，必获得紫水晶券'>
            <Table size='small' columns={viewColumns} pagination={false} dataSource={poolConfig.levelConfig} />
          </ConfigItem >
        </div>
      </div>
    )
  }
}

export default PoolView
