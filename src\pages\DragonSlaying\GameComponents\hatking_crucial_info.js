import { Button, Card, DatePicker, Divider, Form, Table } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
const chanMap = { all: 'ALL', pc: 'PC端', android: '手机交友', xiaomi: '小米渠道', zhuiwan: '追玩渠道', zhuikan: '追看渠道' }
// const namespace = 'hatkingPk' // model 的 namespace

@connect(({ dragonSlaying }) => ({ // model 的 namespace
  model1: dragonSlaying // model 的 namespace
}))

class DSCrucialInfoComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      list: [],
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
  }

  // 日期 渠道类型  押注总流水 付费用户数 剩余累计奖池  中奖总流水 中奖总人数 盈利人数  亏损人数  连中总发放金额 频道广播数量  全服广播数量
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '渠道类型', dataIndex: 'platform', align: 'center', render: (text, record, index) => { return chanMap[text] }, filters: [{ text: 'ALL', value: 'all' }, { text: 'PC端', value: 'pc' }, { text: '手机交友', value: 'android' }, { text: '小米渠道', value: 'xiaomi' }, { text: '追玩渠道', value: 'zhuiwan' }, { text: '追看渠道', value: 'zhuikan' }], onFilter: (value, record) => record.platform.includes(value) },
    { title: '押注总流水', dataIndex: 'incoming', align: 'center' },
    { title: '付费用户数', dataIndex: 'users_paid', align: 'center' },
    { title: '剩余累计奖池', dataIndex: 'jackpot_left', align: 'center' },
    { title: '中奖总流水', dataIndex: 'bet_rewarded', align: 'center' },
    { title: '中奖总人数', dataIndex: 'users_rewarded', align: 'center' },
    { title: '盈利人数', dataIndex: 'profit_user', align: 'center' },
    { title: '亏损人数', dataIndex: 'loss_user', align: 'center' },
    { title: '连中总发放金额', dataIndex: 'combo_rewarded', align: 'center' },
    { title: '频道广播数量', dataIndex: 'subsid_count', align: 'center' },
    { title: '全服广播数量', dataIndex: 'allsid_count', align: 'center' }
  ]

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getCrucialInfoList`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  handleSelectChange = (value) => {
    console.log(value)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { crucialList } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form>
          <span style={{ marginLeft: 10 }}>时间范围:</span>
          <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Divider />
          <Table dataSource={crucialList} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default DSCrucialInfoComponent
