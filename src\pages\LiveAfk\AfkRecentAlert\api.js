import request from '@/utils/request'

/**
 * @param options
 *  - begTime: 开始时间，单位：秒
 *  - endTime: 结束时间，单位：秒
 * @returns
 * {
 *   status: 0,
 *   msg: 'ok',
 *   data: [
 *     {
 *       date:    1632793470     // 日期, 单位： 秒
 *       liveCnt: 493,           // 当前交友开播总主持数
 *       canRecommendCnt: 295,   // 所有符合手Y推荐的主播
 *       liveHangUpCnt: 120      // 命中挂播主持数
 *     }
 *   ]
 * }
 */
export function getLists (options) {
  if (!options.page) {
    options.page = 1
  }
  if (!options.size) {
    options.size = 20
  }

  let urlParams = ''
  for (let k in options) {
    if (options[k] !== undefined) {
      urlParams += k + '=' + options[k] + '&'
    }
  }

  let url = '/live_afk/list_afk_recent?' + urlParams
  return request(url)
}
