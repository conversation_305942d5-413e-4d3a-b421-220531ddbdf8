import React, { Component } from 'react'
import { Card, Divider, But<PERSON>, Modal, Form, Table, Popconfirm, message, Input } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import { connect } from 'dva'
import PopImage from '../../../../components/PopImage'

const namespace = 'rob<PERSON>oney' // model 的 namespace
const FormItem = Form.Item

@connect(({ rob<PERSON><PERSON> }) => ({ // model 的 namespace
  model: robMoney // model 的 namespace
}))
class ActOpBlackCompereComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false
    }
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getBlackCompereList`
    })
  }

  // 需要修改
  columns = [
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: '头像', dataIndex: 'avatarInfo', align: 'center', render: text => <PopImage value={text} /> },
    { title: 'YY', dataIndex: 'imid', align: 'center' },
    { title: '签约频道', dataIndex: 'contractAsid', align: 'center' },
    { title: '操作',
      align: 'center',
      render: (text, record) => (
        <div>
          <Popconfirm onConfirm={this.handleRemove(record.uid)} title='确认删除？'><a><DeleteOutlined style={{ color: 'red' }} type='delete' /></a></Popconfirm>
        </div>
      )
    }
  ]

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // 隐藏弹窗，不需要修改
  hideModal = () => {
    this.setState({ visible: false })
  }

  onFinish = values => {
    const { dispatch, model: { tagList } } = this.props
    const { isUpdate } = this.state

    var list = tagList
    if (!isUpdate) {
      for (var i = 0; Array.isArray(list) && i < list.length; i++) {
        if (list[i].tagId === values.tagId) {
          message.error('period exist', 5)
          return
        }
      }
    }

    // console.log(values, list)
    dispatch({
      type: `${namespace}/upsetBlackCompereList`,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 更新与删除
  handleSubmit = () => {
    this.formRef.submit()
  }

  handleRemove = id => () => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/removeBlackCompereList`,
      payload: { uid: id }
    })
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { blackList } } = this.props
    const { visible, title, isUpdate } = this.state
    const formItemLayout = { // 不需要修改
      labelCol: {
        xs: { span: 4 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 20 },
        sm: { span: 20 }
      }
    }

    return (
      <Card>
        <Button onClick={this.showModal(false, this.defaultValue)}>添加</Button>
        <Divider />
        <Table rowKey={(record, index) => index} dataSource={blackList} columns={this.columns} size='small' pagination={false} /> {/* 显示的列表 */}

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} ref={form => { this.formRef = form }} {...formItemLayout}>
            <FormItem label='ID' name='uid' rules={[{ required: true }]}>
              <Input readOnly={isUpdate} style={{ width: '100%' }} />
            </FormItem>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default ActOpBlackCompereComponent
