import { getList, deleteItem, updateItem } from './api'
import { message } from 'antd'
// import { message } from 'antd'

export default {
  namespace: 'microCover', // 全局唯一标识

  state: {
    list: [
      // { index: 1, businessType: 4, sid: 87814665, ssid: 87814665, compereUID: 123, takeEffect: 1, featurePicture: '', currentFeaturePicture: 1, 'picture': '' },
      // { index: 1, id: '1', businessType: 101, asid: 753, sid: 87814665, ssid: 111, compereUID: 123, takeEffect: 1, featurePicture: '', currentFeaturePicture: 1, 'picture': 'http://s1.yy.com/guild/header/10001.jpg', pictureStatus: 1, title: '大傻叉', titleStatus: 2 },
      // { index: 2, id: '2', businessType: 1, asid: 489, sid: 123, ssid: 456, compereUID: 123, takeEffect: 1, featurePicture: '', currentFeaturePicture: 1, 'picture': 'http://s1.yy.com/guild/header/10001.jpg', pictureStatus: 3, title: 'abc', titleStatus: 1 }
    ]
  },

  reducers: {
    updateCompereList (state, { payload }) {
      for (let i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      try {
        const { data: { list } } = yield call(getList, payload)
        // yield put({
        //   type: 'updateCompereList',
        //   payload: Array.isArray(list) ? list : []
        // })
        yield put({
          type: 'updateCompereList',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        message.error('exception', e)
      }
    },

    * deleteItem ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(deleteItem, payload)
        if (status === 0) {
          message.info('删除成功')
          // yield put({
          //   type: `getList`,
          //   payload: { from: from }
          // })
        } else {
          message.error('删除失败:' + msg)
        }
      } catch (e) {
        message.error('exception', e)
      }
    },

    * updateItem ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(updateItem, payload)
        if (status === 0) {
          message.info('success!')
          // yield put({
          //   type: `getList`,
          //   payload: { from: from }
          // })
        } else {
          message.error(msg)
        }
      } catch (e) {
        message.error('exception', e)
      }
    }
  }
}
