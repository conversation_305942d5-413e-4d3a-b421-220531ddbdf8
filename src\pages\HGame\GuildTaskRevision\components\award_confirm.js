import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Table, Button, Modal, Select, InputNumber, message, Popconfirm, Input, Collapse, Divider } from 'antd'
import { deepClone } from '@/utils/common'
import { stringify } from 'qs'
import { yuanToWan } from '../../../HatkingInfo/HatSuperCompereTask/components/common'

const namespace = 'guildTaskRevision'

const { TextArea } = Input
const { Panel } = Collapse

const Option = Select.Option

// 发奖规则:按系统最终结算/下调奖励-固定值/按配置区间
const prizeRuleNone = 0
const prizeRuleSystem = 1
const prizeRuleDown = 2
const prizeRuleScale = 3
const prizeRule = { 0: '-', 1: '按系统最终结算', 2: '下调奖励-固定值', 3: '按配置区间' }
const prizeRuleList = [
  { label: '按系统最终结算', value: 1 },
  { label: '下调奖励-固定值', value: 2 },
  { label: '按配置区间', value: 3 }
]
const prizeRuleList2 = [
  { label: '按系统最终结算', value: 1 },
  { label: '下调奖励-固定值', value: 2 }
]

const reviewMap = { 0: '-', 1: '待复核', 2: '已复核' }
const reviewStatusList = [
  { label: '待复核', value: 1 },
  { label: '已复核', value: 2 }
]

const approvaMap = { 0: '-', 1: '待审核', 2: '审批通过', 3: '审批不通过', 4: '一审中', 5: '二审中' }
const approvalStatusList = [
  { label: '待审核', value: 1 },
  { label: '审批通过', value: 2 },
  { label: '审批不通过', value: 3 },
  { label: '一审中', value: 4 },
  { label: '二审中', value: 5 }
]

@connect(({ guildTaskRevision }) => ({
  model: guildTaskRevision
}))

class AwardConfirm extends Component {
  columns = [
    { title: '任务时间', dataIndex: 'month', align: 'center', className: 'replacecolor', fixed: 'left' },
    { title: '短位ID', dataIndex: 'asid', align: 'center', fixed: 'left' },
    {
      title: '流水任务',
      children: [
        { title: '公会礼物流水/元', dataIndex: 'turnover', align: 'center', render: (text, record) => (record.turnover.toLocaleString()) },
        { title: '[预结算]奖励金额/元', dataIndex: 'turnoverSettlement', align: 'center', render: (text, record) => (record.turnoverSettlement.toLocaleString()) },
        { title: '发奖规则', dataIndex: 'turnoverPrizeRule', align: 'center', render: (text, record) => (prizeRule[record.turnoverPrizeRule]) },
        { title: '[调整后]发奖金额/元',
          dataIndex: 'turnoverAdjustReward',
          align: 'center',
          render: (text, record) => {
            if (record.turnoverPrizeRule === prizeRuleNone) {
              return ''
            } else if (record.turnoverPrizeRule === prizeRuleSystem || record.turnoverPrizeRule === prizeRuleDown) {
              return <div>{record.turnoverAdjustRewardFix.toLocaleString()}</div>
            } else {
              let content = null
              if (record.turnoverAdjustRewardScale !== null && record.turnoverAdjustRewardScale.length > 0) {
                for (let i = 0; i < record.turnoverAdjustRewardScale.length; i++) {
                  const element = record.turnoverAdjustRewardScale[i]
                  content = <span>{content}<div>流水区间{element.start.toLocaleString()}-{element.end.toLocaleString()}，奖励({element.reward.toLocaleString()}元)</div></span>
                }
              }
              return content
            }
          }
        }
      ]
    },

    {
      title: '优质厅任务',
      children: [
        { title: '优质厅数/个', dataIndex: 'tingTotal', align: 'center' },
        { title: '各流水规模厅数',
          dataIndex: 'tingIntervalTingStr',
          align: 'center',
          render: (text, record) => {
            return this.buildTingNum(record.tingIntervalList)
          }
        },
        {
          title: '[预结算]奖励金额/元',
          dataIndex: 'tingSettlementStr',
          align: 'center',
          render: (text, record) => {
            return this.buildSettlement(record.tingIntervalList)
          }
        },
        {
          title: '发奖规则',
          dataIndex: 'tingPrizeRuleStr',
          align: 'center',
          render: (text, record) => {
            return this.buildPrizeRule(record.tingIntervalList)
          }
        },
        {
          title: '[调整后]发奖金额/元',
          dataIndex: 'tingAdjustRewardStr',
          align: 'center',
          render: (text, record) => {
            return this.buildFixReward(record.tingIntervalList)
          }
        },
        { title: '[预结算]总奖励金额/元', dataIndex: 'tingSettlementTotal', align: 'center', render: (text, record) => (record.tingSettlementTotal.toLocaleString()) },
        { title: '[调整后]总发奖金额/元', dataIndex: 'tingAdjustTotal', align: 'center', render: (text, record) => (record.tingAdjustTotal.toLocaleString()) }
      ]
    },

    { title: '[预结算]合计奖励金额/元', dataIndex: 'settlement', align: 'center', render: (text, record) => (record.settlement.toLocaleString()) },
    { title: '[调整后]合计发奖金额/元', dataIndex: 'adjustReward', align: 'center', render: (text, record) => (record.turnoverPrizeRule === prizeRuleNone ? '' : <font color='red'>{record.adjustReward}</font>) },

    { title: '复核状态', dataIndex: 'reviewStatus', align: 'center', render: (text, record) => (reviewMap[record.reviewStatus]) },
    { title: '审批状态', dataIndex: 'approvalStatus', align: 'center', render: (text, record) => (approvaMap[record.approvalStatus]) },
    { title: '驳回原因', dataIndex: 'approvalReason', align: 'center' },
    { title: '操作人', dataIndex: 'optUid', align: 'center', render: (text, record) => (record.optUid !== 0 ? record.optUid : '') }
  ]

  state = {
    visible: false,
    reviewList: {},
    settlement: 0,
    turnoverSettlement: 0,
    tingSettlement: 0,
    isFlush: false
  }

  defaultItem = {
    sid: 0,
    idx: 0,
    interval: '',
    reward: 0,
    turnoverNextReward: 0,
    turnoverMaxReward: 0
  }

  defaultPageValue = {
    defaultPageSize: 10,
    pageSizeOptions: ['10', '20', '30', '50', '80', '100'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  defaultPageValueReview = {
    defaultPageSize: 5,
    pageSizeOptions: ['5', '10', '20', '50', '100', '200'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  componentDidMount () {
    this.loadData()
  }

  loadData = (isExport) => {
    const { searchAsid, searchTurnoverPrizeRule, searchTingPrizeRule, searchReviewStatus, searchApprovalStatus } = this.state
    const { dispatch } = this.props

    let data = { export: isExport, prizeRuleGuildTask: searchTurnoverPrizeRule, prizeRuleHighGradeTingTask: searchTingPrizeRule, reviewStatus: searchReviewStatus, approvalStatus: searchApprovalStatus }
    if (searchAsid > 0) {
      data['asid'] = searchAsid
    }
    dispatch({
      type: `${namespace}/listAwardConfirm`,
      payload: data
    })

    dispatch({
      type: `${namespace}/listAwardConfirmAll`,
      payload: {}
    })
  }

  exportURL = () => {
    const { searchAsid, searchTurnoverPrizeRule, searchTingPrizeRule, searchReviewStatus, searchApprovalStatus } = this.state
    let data = { export: 1, prizeRuleGuildTask: searchTurnoverPrizeRule, prizeRuleHighGradeTingTask: searchTingPrizeRule, reviewStatus: searchReviewStatus, approvalStatus: searchApprovalStatus }
    if (searchAsid > 0) {
      data['asid'] = searchAsid
    }
    return `/fts_hgame/guild_task/v2/list_award_confirm?${stringify(data)}`
  }

  // 构造优质厅流水规模字段
  buildTingNum = (tingIntervalList) => {
    let content = null
    let lastIdx = tingIntervalList.length - 1
    for (let i = 0; i < tingIntervalList.length; i++) {
      const element = tingIntervalList[i]
      if (i !== lastIdx) {
        content = <span>{content}<div>[{yuanToWan(element.startInterval)}万-{yuanToWan(element.endInterval)}万): {element.ting}个</div><Divider type='horizontal' /></span>
      } else content = <span>{content}<div>[{yuanToWan(element.startInterval)}万+): {element.ting}个</div></span>
    }
    return content
  }

  buildSettlement = (tingIntervalList) => {
    let content = null
    let lastIdx = tingIntervalList.length - 1
    for (let i = 0; i < tingIntervalList.length; i++) {
      const element = tingIntervalList[i]
      if (i !== lastIdx) {
        content = <span>{content}<div>{element.settlement.toLocaleString()}</div><Divider type='horizontal' /></span>
      } else content = <span>{content}<div>{element.settlement.toLocaleString()}</div></span>
    }
    return content
  }

  buildPrizeRule = (tingIntervalList) => {
    let content = null
    let lastIdx = tingIntervalList.length - 1
    for (let i = 0; i < tingIntervalList.length; i++) {
      const element = tingIntervalList[i]
      if (i !== lastIdx) {
        content = <span>{content}<div>{prizeRule[element.prizeRule]}</div><Divider type='horizontal' /></span>
      } else content = <span>{content}<div>{prizeRule[element.prizeRule]}</div></span>
    }
    return content
  }

  buildFixReward = (tingIntervalList) => {
    let content = null
    let lastIdx = tingIntervalList.length - 1
    for (let i = 0; i < tingIntervalList.length; i++) {
      const element = tingIntervalList[i]
      if (i !== lastIdx) {
        content = <span>{content}<div>{element.adjustReward.toLocaleString()}</div><Divider type='horizontal' /></span>
      } else content = <span>{content}<div>{element.adjustReward.toLocaleString()}</div></span>
    }
    return content
  }

  // 复核页面
  buildPrizeRuleReview = (record) => {
    let content = null
    let lastIdx = record.tingIntervalList.length - 1
    for (let i = 0; i < record.tingIntervalList.length; i++) {
      const element = record.tingIntervalList[i]
      let key = 'tingPrizeRule' + '_' + record.sid + '_' + i
      content = <span>{content}<div><Select value={prizeRule[element.prizeRule]} style={{ width: 130 }} onChange={this.handleRowChange(record, key)}>{prizeRuleList2.map((item, index) => (<Option key={key} value={item.value}>{item.label}</Option>))}</Select></div>{i !== lastIdx ? <Divider type='horizontal' /> : null}</span>
    }
    return content
  }

  buildFixRewardReview = (record) => {
    let content = null
    let lastIdx = record.tingIntervalList.length - 1
    for (let i = 0; i < record.tingIntervalList.length; i++) {
      const element = record.tingIntervalList[i]
      console.log(element, record)
      let key = 'tingAdjustReward' + '_' + record.sid + '_' + i
      content = <span>{content}<div>{element.prizeRule === prizeRuleDown
        ? <InputNumber value={element.adjustReward} min={0} max={element.maxReward} onChange={this.handleRowChange(record, key)} />
        : element.adjustReward.toLocaleString()}</div>{i !== lastIdx ? <Divider type='horizontal' /> : null}</span>
    }
    return content
  }

//* ***************************编辑复核信息************************************************/
// 编辑复核信息
handleRowChange = (record, field) => value => {
  const { visible, reviewList, awardConfirmReviewList } = this.state
  if (!visible) {
    console.log('not update page')
    return
  }
  console.log(record)
  console.log(awardConfirmReviewList)

  let index = record.idx - 1
  if (index < 0) {
    return
  }
  console.log(index, field, value)

  awardConfirmReviewList[index][field] = value
  // record[field] = value

  // 因修改[发奖规则/调整奖励]而更新的固定显示

  // 流水任务
  if (field === 'turnoverPrizeRule') {
    // 修改 发奖规则
    if (value === prizeRuleSystem) {
      awardConfirmReviewList[index]['turnoverAdjustReward'] = record.turnoverSettlement
      // record['turnoverAdjustReward'] = record.turnoverSettlement
    }
    if (value === prizeRuleScale && record.turnoverNextReward === 0) {
      // 下阶段奖励为0, 表示最大达标了, 不能选择
      message.warn('已最大达标了, 不能选择')
      return
    }
  }

  // 优质厅任务
  if (field.indexOf('tingPrizeRule') !== -1) {
    let vals = field.split('_') // tingPrizeRule_sid_idx    // tingPrizeRule_87814665_0/tingPrizeRule_87814665_1/tingPrizeRule_87814665_2
    let idx = vals[2]

    awardConfirmReviewList[index]['tingIntervalList'][idx].prizeRule = value
    // record['tingIntervalList'][idx].prizeRule = value

    // 修改 发奖规则
    if (value === prizeRuleSystem) {
      // console.log(record.tingIntervalList[idx])
      awardConfirmReviewList[index]['tingIntervalList'][idx].adjustReward = awardConfirmReviewList[index].tingIntervalList[idx].settlement
      // record['tingIntervalList'][idx].adjustReward = record['tingIntervalList'][idx].settlement
    } else {
      awardConfirmReviewList[index]['tingIntervalList'][idx].adjustReward = 0
      // record['tingIntervalList'][idx].adjustReward = 0
    }
  }
  if (field.indexOf('tingAdjustReward') !== -1) {
    let vals = field.split('_') // tingAdjustReward_sid_idx    // tingAdjustReward_87814665_0/tingAdjustReward_87814665_1/tingAdjustReward_87814665_2
    let idx = vals[2]

    awardConfirmReviewList[index]['tingIntervalList'][idx].adjustReward = value
    // record['tingIntervalList'][idx].adjustReward = value

    if (awardConfirmReviewList[index]['tingIntervalList'][idx].prizeRule === prizeRuleDown) {
      awardConfirmReviewList[index]['tingIntervalList'][idx].adjustReward = value
      // record['tingIntervalList'][idx].adjustReward = value
    } else {
      awardConfirmReviewList[index]['tingIntervalList'][idx].adjustReward = awardConfirmReviewList[index].tingIntervalList[idx].settlement
      // record['tingIntervalList'][idx].adjustReward = record['tingIntervalList'][idx].settlement
    }
  }

  // 统计优质厅所有流水规模的调整后金额
  let tingAdjustReward = 0
  for (let i = 0; i < awardConfirmReviewList[index]['tingIntervalList'].length; i++) {
    tingAdjustReward += awardConfirmReviewList[index]['tingIntervalList'][i].adjustReward
  }

  awardConfirmReviewList[index]['tingAdjustTotal'] = tingAdjustReward

  if (awardConfirmReviewList[index].turnoverPrizeRule !== prizeRuleScale) {
    awardConfirmReviewList[index]['adjustReward'] = (awardConfirmReviewList[index]['turnoverAdjustReward'] + tingAdjustReward).toLocaleString()
  } else {
    // 分别找出最小区间, 最大区间的奖励
    if (!(reviewList[record.sid] === undefined || reviewList[record.sid] === null || reviewList[record.sid].length === 0)) {
      let count = reviewList[record.sid].length
      let turnoverMaxReward = this.calReward(reviewList[record.sid][count - 1].reward)
      let turnoverMinReward = this.calReward(reviewList[record.sid][0].reward)
      let min = turnoverMinReward + tingAdjustReward
      let max = turnoverMaxReward + tingAdjustReward
      awardConfirmReviewList[index]['adjustReward'] = '最低' + min.toLocaleString() + '~' + '最高' + max.toLocaleString()
    } else {
      awardConfirmReviewList[index]['adjustReward'] = awardConfirmReviewList[index]['turnoverAdjustReward'] + tingAdjustReward
    }
  }

  console.log(record)
  this.forceUpdate() // 强制刷新
}

// 刷新统计
flushHandle = () => () => {
  const { reviewList, inputReviewRemark, hadRead, awardConfirmReviewList } = this.state
  console.log(reviewList)
  console.log(awardConfirmReviewList)

  if (!hadRead) {
    message.warn('请先阅读重要提示')
    return
  }

  console.log(inputReviewRemark)
  if (inputReviewRemark === null || inputReviewRemark === '') {
    message.warn('运营备注必填')
    return
  }

  let count = awardConfirmReviewList.length
  for (let i = 0; i < count; i++) {
    const element = awardConfirmReviewList[i]

    if (element.turnoverPrizeRule === prizeRuleNone) {
      message.warn(element.asid + '[发奖规则]未配置')
      return
    }
    if (element.turnoverPrizeRule === prizeRuleSystem && element.turnoverAdjustReward !== element.turnoverSettlement) {
      message.warn(element.asid + '流水任务[按系统最终结算 调整金额]错误')
      return
    }
    if (element.turnoverPrizeRule === prizeRuleDown && element.turnoverAdjustReward > element.turnoverMaxReward) {
      message.warn(element.asid + '流水任务[下调奖励-固定值 调整金额]错误')
      return
    }
    if (element.turnoverPrizeRule === prizeRuleScale && (reviewList === null || reviewList[element.sid] === null || reviewList[element.sid] === undefined || reviewList[element.sid].length === 0)) {
      message.warn(element.asid + '流水任务[按配置区间 调整金额]错误')
      return
    }
  }

  let data = {}
  let turnoverRes = {}
  let tingRes = {}
  for (let i = 0; i < awardConfirmReviewList.length; i++) {
    const val = awardConfirmReviewList[i]
    let sid = val.sid
    let asid = val.asid

    data['month'] = val.month

    // 流水任务
    turnoverRes[sid] = { prizeRule: val.turnoverPrizeRule }
    if (val.turnoverPrizeRule === prizeRuleSystem || val.turnoverPrizeRule === prizeRuleDown) {
      turnoverRes[sid]['fixReward'] = val.turnoverAdjustReward
    } else {
      if (reviewList[sid] === undefined || reviewList[sid] === null || reviewList[sid].length === 0) {
        message.error(asid + '流水任务-按配置区间 没配置比例区间')
        return
      }
      let reward = []
      for (let index = 0; index < reviewList[sid].length; index++) {
        const element = reviewList[sid][index]
        let intervals = element.interval.split('-')
        if (intervals.length !== 2) {
          message.error(asid + '流水任务-按配置区间 比例区间配置错误')
          return
        }

        if (String(intervals[0]).indexOf('.') > -1) {
          message.error(asid + '流水任务-按配置区间 不能小数 ')
          return
        }
        if (String(intervals[1]).indexOf('.') > -1) {
          message.error(asid + '流水任务-按配置区间 不能小数')
          return
        }

        reward.push({ start: Number(intervals[0]), end: Number(intervals[1]), reward: element.reward })
      }
      turnoverRes[sid]['scaleReward'] = reward
    }

    // 优质厅任务
    tingRes[sid] = []
    for (let j = 0; j < val.tingIntervalList.length; j++) {
      let interval = val.tingIntervalList[j]

      if (interval.prizeRule === prizeRuleNone) {
        message.warn(asid + '[发奖规则]未配置')
        return
      }

      if (interval.prizeRule === prizeRuleSystem && interval.adjustReward !== interval.settlement) {
        message.warn(val.asid + '优质厅任务[按系统最终结算 调整金额]错误')
        return
      }
      if (interval.prizeRule === prizeRuleDown && interval.adjustReward > interval.maxReward) {
        message.warn(val.asid + '优质厅任务[下调奖励-固定值 调整金额]错误')
        return
      }

      let tingInterval = { startInterval: interval.startInterval, endInterval: interval.endInterval, prizeRule: interval.prizeRule, fixReward: interval.adjustReward }
      tingRes[sid].push(tingInterval)
    }
  }

  data['turnoverTaskRewardAdjust'] = turnoverRes
  data['tingTaskRewardAdjust'] = tingRes

  this.props.dispatch({
    type: `${namespace}/flushAwardConfirm`,
    payload: data
  })

  this.setState({ isFlush: true })
}

inputReviewRemarkHandle = () => e => {
  let value = e.target.value
  this.setState({ inputReviewRemark: value })
}

// 新增选项
handleAddItem = (record) => () => {
  const { reviewList } = this.state

  if (reviewList === undefined || reviewList[record.sid] === undefined || reviewList[record.sid] === null || reviewList[record.sid].length === 0) {
    reviewList[record.sid] = []
  }

  console.log(record, reviewList, reviewList[record.sid])
  let defaultItem = deepClone(this.defaultItem)
  defaultItem.idx = reviewList[record.sid].length
  defaultItem.sid = record.sid
  defaultItem.turnoverNextReward = record.turnoverNextReward
  defaultItem.turnoverMaxReward = record.turnoverMaxReward

  console.log(defaultItem)
  reviewList[record.sid].push(defaultItem)
  this.setState({ reviewList: deepClone(reviewList) })
}

// 删除选项
handleDel = (key, sid) => {
  const { reviewList } = this.state
  let newList = []
  let index = 0
  console.log(key, sid, reviewList)
  reviewList[sid].map(item => {
    console.log(key, item)
    if (item.idx !== key) {
      item.idx = index++
      newList.push(item)
    }
  })
  reviewList[sid] = newList
  this.setState({ reviewList: deepClone(reviewList) })
}

// 编辑复核比例信息
handleRowChangeScale = (record, field) => e => {
  let value = e.target.value
  const { reviewList } = this.state

  reviewList[record.sid][record.idx][field] = value
  record[field] = value

  console.log(field, value, record)
  this.forceUpdate() // 强制刷新
}

// 编辑复核奖励
  handleRowChangeScale2 = (record, field) => e => {
    let value = e
    const { reviewList } = this.state

    reviewList[record.sid][record.idx][field] = value
    record[field] = value

    console.log(field, value, record)
    this.forceUpdate() // 强制刷新
  }

  calReward = (reward) => {
    return reward
  }
  /** ************************************************************************** */

  reviewHandle = () => () => {
    const { model: { awardConfirmListAll } } = this.props

    let bak = JSON.parse(JSON.stringify(awardConfirmListAll))
    let reviewList = []
    for (let i = 0; i < bak.length; i++) {
      let one = {
        idx: bak[i].idx,
        sid: bak[i].sid,
        asid: bak[i].asid,
        month: bak[i].month,
        turnover: bak[i].turnover,
        turnoverSettlement: bak[i].turnoverSettlement,
        turnoverPrizeRule: 0,
        turnoverAdjustReward: 0,

        tingTotal: bak[i].tingTotal,
        tingIntervalList: [], // bak[i].tingIntervalList,
        tingSettlementTotal: bak[i].tingSettlementTotal,
        tingAdjustTotal: 0,

        turnoverNextStep: bak[i].turnoverNextStep,
        turnoverNextReward: bak[i].turnoverNextReward,
        turnoverMaxReward: bak[i].turnoverMaxReward,

        settlement: bak[i].settlement,
        adjustReward: ''
      }

      for (let j = 0; j < bak[i].tingIntervalList.length; j++) {
        one.tingIntervalList.push({
          startInterval: bak[i].tingIntervalList[j].startInterval,
          endInterval: bak[i].tingIntervalList[j].endInterval,
          ting: bak[i].tingIntervalList[j].ting,
          settlement: bak[i].tingIntervalList[j].settlement,
          maxReward: bak[i].tingIntervalList[j].maxReward,
          prizeRule: 0,
          adjustReward: 0
        })
      }

      reviewList.push(one)
    }

    this.setState({ visible: true, editing: true, hiddenCancelBotton: true, awardConfirmReviewList: reviewList })
  }

  searchHandle = () => () => {
    this.loadData()
  }

 hiddenModal = () => {
   this.props.dispatch({
     type: `${namespace}/cleanReviewInfo`,
     payload: {}
   })

   this.setState({ visible: false, isConfirm: false, editing: false, hiddenCancelBotton: false, inputReviewRemark: '', hadRead: false })
   this.loadData()
 }

 handleCancel = e => {
   this.props.dispatch({
     type: `${namespace}/cleanReviewInfo`,
     payload: {}
   })

   this.setState({ visible: false, isConfirm: false, editing: false, inputReviewRemark: '', hadRead: false })
   this.loadData()
 }

 handleSubmit = e => {
   this.setState({ hiddenCancelBotton: true })

   const { isFlush, reviewList, inputReviewRemark, hadRead, awardConfirmReviewList } = this.state
   var { model: { isCheckOk } } = this.props

   if (!hadRead) {
     message.warn('请先阅读重要提示')
     return
   }

   if (!isFlush) {
     message.warn('未更新概况文案')
     return
   }

   console.log(inputReviewRemark)
   if (inputReviewRemark === null || inputReviewRemark === '') {
     message.warn('运营备注必填')
     return
   }

   if (!isCheckOk) {
     message.warn('调整不合法, 请检查后点击刷新核对')
     return
   }

   let detailHTML = document.getElementById('inner')?.innerHTML
   console.log(detailHTML)

   let data = { isApproval: true, reviewReason: inputReviewRemark, html: detailHTML }

   let turnoverRes = {}
   let tingRes = {}
   for (let i = 0; i < awardConfirmReviewList.length; i++) {
     const val = awardConfirmReviewList[i]
     let sid = val.sid

     data['month'] = val.month

     // 流水任务
     turnoverRes[sid] = { prizeRule: val.turnoverPrizeRule }
     if (val.turnoverPrizeRule === prizeRuleSystem || val.turnoverPrizeRule === prizeRuleDown) {
       turnoverRes[sid]['fixReward'] = val.turnoverAdjustReward
     } else {
       if (reviewList[sid] === undefined || reviewList[sid] === null || reviewList[sid].length === 0) {
         message.error(sid + '流水任务-按配置区间 没配置比例区间')
         return
       }
       let reward = []
       for (let index = 0; index < reviewList[sid].length; index++) {
         const element = reviewList[sid][index]
         let intervals = element.interval.split('-')
         if (intervals.length !== 2) {
           message.error(sid + '流水任务-按配置区间 比例区间配置错误')
           return
         }
         reward.push({ start: Number(intervals[0]), end: Number(intervals[1]), reward: Number(element.reward) })
       }
       turnoverRes[sid]['scaleReward'] = reward
     }

     // 优质厅任务
     tingRes[sid] = []
     for (let j = 0; j < val.tingIntervalList.length; j++) {
       let interval = val.tingIntervalList[j]

       if (interval.prizeRule === prizeRuleSystem && interval.adjustReward !== interval.settlement) {
         message.warn(val.asid + '优质厅任务[按系统最终结算 调整金额]错误')
         return
       }
       if (interval.prizeRule === prizeRuleDown && interval.adjustReward > interval.maxReward) {
         message.warn(val.asid + '优质厅任务[下调奖励-固定值 调整金额]错误')
         return
       }

       let tingInterval = { startInterval: interval.startInterval, endInterval: interval.endInterval, prizeRule: interval.prizeRule, fixReward: interval.adjustReward }
       tingRes[sid].push(tingInterval)
     }
   }

   data['turnoverTaskRewardAdjust'] = turnoverRes
   data['tingTaskRewardAdjust'] = tingRes

   console.log(data)
   console.log(reviewList)

   this.props.dispatch({
     type: `${namespace}/approvalReward`,
     payload: data
   })
   this.hiddenModal()
 }

  getRowClassName = (record, index) => {
    return index % 2 === 0 ? 'guildTask' : '111'
  }

  collapseHandle = () => {
    this.setState({ hadRead: true })
  }

  render () {
    const { model: { reviewInfo, awardConfirmList, awardConfirmInfo } } = this.props
    const { visible, editing, reviewList, inputReviewRemark, awardConfirmReviewList } = this.state

    const tableLoading = false

    const columnsPrizeRuleScale = [
      { title: '区间', dataIndex: 'interval', width: 160, align: 'center', render: (text, record) => editing ? <span><Input onChange={this.handleRowChangeScale(record, 'interval')} /></span> : text },
      { title: '奖励元', dataIndex: 'reward', width: 160, align: 'center', render: (text, record) => editing ? <span><InputNumber min={0} max={record.turnoverMaxReward} onChange={this.handleRowChangeScale2(record, 'reward')} /></span> : text },
      { title: '操作', align: 'center', render: (text, record) => editing ? <span><Popconfirm title='确认删除?' onConfirm={() => this.handleDel(record.idx, record.sid)}><a href=''>删除</a></Popconfirm></span> : ''
      }
    ]

    const columnsReview = [
      { title: '任务时间', dataIndex: 'month', align: 'center' },
      { title: '短位ID', dataIndex: 'asid', align: 'center' },
      {
        title: '流水任务',
        children: [
          { title: '公会礼物流水/元', dataIndex: 'turnover', width: 100, align: 'center', render: (text, record) => (record.turnover.toLocaleString()) },
          { title: '[预结算]奖励金额/元', dataIndex: 'turnoverSettlement', width: 100, align: 'center', render: (text, record) => (record.turnoverSettlement.toLocaleString()) },
          { title: '发奖规则', dataIndex: 'turnoverPrizeRule', width: 200, align: 'center', render: (text, record) => editing ? record.turnoverNextReward !== 0 ? <Select onChange={this.handleRowChange(record, 'turnoverPrizeRule')} defaultValue={prizeRule[record.turnoverPrizeRule]} value={prizeRule[record.turnoverPrizeRule]} placeholder='请选择' style={{ width: 180 }}>{prizeRuleList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}</Select> : <Select onChange={this.handleRowChange(record, 'turnoverPrizeRule')} defaultValue={prizeRule[record.turnoverPrizeRule]} value={prizeRule[record.turnoverPrizeRule]} placeholder='请选择' style={{ width: 180 }}>{prizeRuleList2.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}</Select> : prizeRule[record.turnoverPrizeRule] },
          { title: '[调整后]发奖金额/元',
            dataIndex: 'turnoverAdjustReward',
            align: 'left',
            width: 400,
            render: (text, record) => {
              if (record.turnoverPrizeRule === prizeRuleSystem) {
                return <div>{record.turnoverSettlement.toLocaleString()}</div>
              } else if (record.turnoverPrizeRule === prizeRuleDown) {
                return editing ? <span><InputNumber style={{ width: 200 }} min={0} max={record.turnoverMaxReward} onChange={this.handleRowChange(record, 'turnoverAdjustReward')} /></span> : record.turnoverAdjustReward
              } else if (record.turnoverPrizeRule === prizeRuleScale) {
                return reviewList[record.sid] === null || reviewList[record.sid] === undefined || reviewList[record.sid].length === 0 ? <span><div><Button hidden={!editing} style={{ marginLeft: 20 }} size='small' type='primary' onClick={this.handleAddItem(record)}>+</Button></div><Table size='small' columns={columnsPrizeRuleScale} rowKey='idx' dataSource={[]} pagination={false} scroll={{ x: 120 }} /></span> : <span><div><Button hidden={!editing} style={{ marginLeft: 20 }} size='small' type='primary' onClick={this.handleAddItem(record)}>+</Button></div><Table size='small' columns={columnsPrizeRuleScale} rowKey='idx' dataSource={reviewList[record.sid]} pagination={false} scroll={{ x: 120 }} /></span>
              }
            }
          }
        ]
      },

      {
        title: '优质厅任务',
        children: [
          { title: '优质厅数/个', dataIndex: 'tingTotal', align: 'center' },

          { title: '各流水规模厅数',
            dataIndex: 'tingIntervalTingStr',
            align: 'center',
            width: 200,
            render: (text, record) => {
              return this.buildTingNum(record.tingIntervalList)
            }
          },
          {
            title: '[预结算]奖励金额/元',
            dataIndex: 'tingSettlementStr',
            align: 'center',
            width: 150,
            render: (text, record) => {
              return this.buildSettlement(record.tingIntervalList)
            }
          },
          {
            title: '发奖规则',
            dataIndex: 'tingPrizeRuleStr',
            align: 'center',
            width: 200,
            render: (text, record) => {
              return this.buildPrizeRuleReview(record)
            }
          },

          { title: '[调整后]发奖金额/元',
            dataIndex: 'tingAdjustReward',
            width: 150,
            render: (text, record) => {
              return this.buildFixRewardReview(record)
            }
          },
          { title: '[预结算]总奖励金额/元', dataIndex: 'tingSettlementTotal', align: 'center', render: (text, record) => (record.tingSettlementTotal.toLocaleString()) },
          { title: '[调整后]总发奖金额/元', dataIndex: 'tingAdjustTotal', align: 'center', render: (text, record) => (record.tingAdjustTotal.toLocaleString()) }
        ]
      },

      { title: '[预结算]合计奖励金额/元', dataIndex: 'settlement', align: 'center', render: (text, record) => (record.settlement.toLocaleString()) },
      { title: '[调整后]合计发奖金额/元', dataIndex: 'adjustReward', align: 'center' }
    ]

    console.log(reviewInfo)
    return (
      <Card>
        <span style={{ marginLeft: 15 }}>短位ID</span>
        <InputNumber placeholder='请输入短位ID' onChange={e => this.setState({ searchAsid: e })} style={{ width: 120, marginLeft: 3 }} />

        <span style={{ marginLeft: 15 }}>流水任务-发奖规则</span>
        <Select allowClear placeholder='请选择' style={{ width: 150, marginLeft: 3 }} onChange={(v) => this.setState({ searchTurnoverPrizeRule: v })}>
          {prizeRuleList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}
        </Select>

        <span style={{ marginLeft: 15 }}>优质厅任务-发奖规则</span>
        <Select allowClear placeholder='请选择' style={{ width: 150, marginLeft: 3 }} onChange={(v) => this.setState({ searchTingPrizeRule: v })}>
          {prizeRuleList2.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}
        </Select>

        <span style={{ marginLeft: 15 }}>复核状态</span>
        <Select allowClear placeholder='请选择' style={{ width: 100, marginLeft: 3 }} onChange={(v) => this.setState({ searchReviewStatus: v })}>
          {reviewStatusList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}
        </Select>

        <span style={{ marginLeft: 15 }}>审批状态</span>
        <Select allowClear placeholder='请选择' style={{ width: 130, marginLeft: 3 }} onChange={(v) => this.setState({ searchApprovalStatus: v })}>
          {approvalStatusList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}
        </Select>
        <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
        <Button style={{ marginLeft: 20 }} type='primary'><a href={this.exportURL()} target='_blank'>导出</a></Button>
        <div style={{ marginTop: 10 }} />
        <Button style={{ marginLeft: 20 }} type='primary' onClick={this.reviewHandle()}>复核</Button>
        <div style={{ marginLeft: 10, marginTop: 10 }}><font color='red'>运营备注:</font></div>
        <div style={{ marginLeft: 10, marginTop: 5 }}>{awardConfirmInfo.reviewRemark}</div>
        <Table bordered rowClassName={this.getRowClassName} style={{ marginTop: 10 }} rowKey='idx' pagination={this.defaultPageValue} columns={this.columns} dataSource={awardConfirmList} scroll={{ x: 'max-content' }} />

        <Modal keyboard={false} maskClosable={false} destroyOnClose forceRender width={2000} visible={visible} title='发奖复核确认' onCancel={this.handleCancel} onOk={this.handleSubmit} okText='提交审批'>
          <div id='inner'>
            <Collapse onChange={this.collapseHandle}>
              <Panel header='重要提醒！！ 填写前必读！！！'>
                <div><font color='red'>重要说明！！！</font></div>
                <div>1. 发奖规则：默认选中【按系统最终结算】，支持修改为【下调奖励-固定值】/【按配置区间】。</div>
                <div>① 若发奖规则选择“下调奖励-固定值”，仅支持输入固定数值</div>
                <div>② 若发奖规则为“按配置区间”：若最终结算时公会礼物流水 不在配置流水区间内，则会以【系统最终结算金额】来发奖。</div>
                <div><font color='red'>注：输入金额限制不能大于当前任务最高阶段金额</font></div>
                <div>2. 最终结算发奖时：</div>
                <div><font color='red'>{'① 若无审批通过的发奖规则，默认按照系统结算进行发奖：达标发奖，不达标不发奖'}</font></div>
                <div><font color='red'>{'② 发奖规则为“下调奖励-固定值”：若系统最终结算金额<运营填写的下调奖励金额, 则会以【系统最终结算金额】来发奖'}</font></div>
                <div><font color='red'>{'① 发奖规则为“按配置区间”：若最终结算时公会礼物流水 不在配置流水区间内，则会以【系统最终结算金额】来发奖'}</font></div>
                <div />
              </Panel>
            </Collapse>
            <div style={{ marginTop: 10 }}><font color='red'>{awardConfirmInfo.year}年{awardConfirmInfo.month}月（{awardConfirmInfo.dateRange}期间数据）</font></div>
            <div>1. 整体情况：参与任务公会 {awardConfirmInfo.guildCount}家，预结算任务达标发奖公会 {awardConfirmInfo.reachCount}家</div>
            <div>2. 发奖情况：</div>
            <div>①【预结算】 合计奖励金额<font color='red'>{reviewInfo.preSettlement.toLocaleString()}元</font>。流水任务<font color='red'>{reviewInfo.preTurnoverSettlement.toLocaleString()}元</font>，优质厅任务<font color='red'>{reviewInfo.preTingSettlement.toLocaleString()}元</font></div>
            <div>②【调整后】 合计奖励金额，<font color='red'>最高{reviewInfo.adjustMaxReward.toLocaleString()}元</font>，<font color='red'>最低{reviewInfo.adjustMinReward.toLocaleString()}元</font>。其中，流水任务<font color='red'>最高{reviewInfo.adjustTurnoverMaxReward.toLocaleString()}元</font>，<font color='red'>最低{reviewInfo.adjustTurnoverMinReward.toLocaleString()}元</font>；优质厅任务<font color='red'>{reviewInfo.adjustTingReward.toLocaleString()}元</font></div>
            <Button hidden={!editing} style={{ marginTop: 10, marginBottom: 10, marginLeft: 30 }} type='primary' onClick={this.flushHandle()}>更新发奖概况文案</Button>
            <div style={{ marginTop: 10 }} />
            <font color='red'>运营复核备注:</font>
            <TextArea placeholder='必填' autoSize={{ minRows: 2, maxRows: 4 }} style={{ height: 50, width: 500 }} value={inputReviewRemark} onChange={this.inputReviewRemarkHandle()} />

            <div style={{ marginTop: 10 }} />
            <Card>
              <Table size='small' loading={tableLoading} rowKey='idx' bordered dataSource={awardConfirmReviewList} columns={columnsReview} pagination={false} scroll={{ x: 2000, y: 400 }} />
            </Card>
          </div>
        </Modal>
      </Card>
    )
  }
}

export default AwardConfirm
