import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import { Table, Card, Popconfirm, Typography, message } from 'antd'
import { timeFormater } from '@/utils/common'
const { Link } = Typography

const namespace = 'flowRankData'

@connect(({ flowRankData }) => ({
  model: flowRankData
}))

class FlowRankData extends Component {
  state = {}
  componentDidMount = () => {
    this.getUserRank()
    this.getRoomRank()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  onTagChange = (v) => {
    // if (v === 'chan') {
    //   this.getRoomRank()
    // } else {
    //   this.getUserRank()
    // }
  }

  // 刷新用户榜单
  getUserRank = () => {
    this.callModel('getTopUserList')
  }

  // 刷新频道榜
  getRoomRank = () => {
    this.callModel('getTopRoomList')
  }

  // 删除房间榜
  onDeleteRoomRank = (key) => {
    this.callModel('deleteRoomItem', {
      params: { key: key },
      isSlientMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('操作失败:' + msg)
          return
        }
        this.getRoomRank()
        message.success('操作成功~')
      }
    })
  }
  // 删除用户榜
  onDeleteUserRank = (uid) => {
    this.callModel('deleteUserItem', {
      params: { uid: uid },
      isSlientMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('操作失败:' + msg)
          return
        }
        this.getUserRank()
        message.success('操作成功~')
      }
    })
  }

  render () {
    const { route } = this.props
    const { userRank, roomRank } = this.props.model || {}

    const columnsRoom = [
      { title: 'sid', dataIndex: 'sid' },
      { title: 'ssid', dataIndex: 'ssid' },
      { title: '厅名称', dataIndex: 'tingName' },
      { title: 'asid', dataIndex: 'asid' },
      { title: '开始时间', dataIndex: 'startTime', render: (v) => timeFormater(v) },
      { title: '结束时间', dataIndex: 'endTime', render: (v) => timeFormater(v) },
      { title: '操作',
        dataIndex: 'sid',
        render: (v, r) => {
          return <Popconfirm title='确认删除？' onConfirm={() => { this.onDeleteRoomRank(`${r.sid}:${r.ssid}`) }}>
            <Link>删除</Link>
          </Popconfirm>
        } }
    ].map(item => { item.align = 'center'; return item })

    const columnsUser = [
      { title: 'uid', dataIndex: 'uid' },
      { title: 'YY号', dataIndex: 'imid' },
      { title: 'YY昵称', dataIndex: 'nick' },
      { title: 'asid', dataIndex: 'asid' },
      { title: '开始时间', dataIndex: 'startTime', render: (v) => timeFormater(v) },
      { title: '结束时间', dataIndex: 'endTime', render: (v) => timeFormater(v) },
      { title: '操作',
        dataIndex: 'uid',
        render: (v, r) => {
          return <Popconfirm title='确认删除？' onConfirm={() => { this.onDeleteUserRank(`${v}`) }}>
            <Link>删除</Link>
          </Popconfirm>
        } }
    ].map(item => { item.align = 'center'; return item })

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='chan' type='card' size='large'
            onChange={(record) => this.onTagChange(record)}>
            <TabPane tab='sid&ssid' key='chan'>
              <Table columns={columnsRoom} dataSource={roomRank} />
            </TabPane>
            <TabPane tab='uid' key='user'>
              <Table columns={columnsUser} dataSource={userRank} />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default FlowRankData
