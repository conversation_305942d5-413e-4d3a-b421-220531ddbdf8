import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, DatePicker, Divider, Form, Input, Modal, Popconfirm, Table, TimePicker } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'

// var moment = require('moment')
const namespace = 'dsUpgradeConfigConfirm'
const getListUri = `${namespace}/getList`
const addItemUri = `${namespace}/addItem`
const confirmItemUri = `${namespace}/confirmItem`
const { RangePicker: DateRangePicker } = DatePicker
const { RangePicker: TimeRangePicker } = TimePicker
// const rmoveItem = `${namespace}/removeItem`
const FormItem = Form.Item

@connect(({ dsUpgradeConfigConfirm }) => ({
  model: dsUpgradeConfigConfirm
}))
class DSUpgradeConfigConfirm extends Component {
  // 列表结构
  columns = [
    { title: 'id', dataIndex: 'id', align: 'center' },
    { title: '提交时间', dataIndex: 'submitTime', align: 'center' },
    { title: '修改人', dataIndex: 'submitUser', align: 'center' },
    { title: '审批类型', dataIndex: 'confirmType', align: 'center' },
    { title: '审批内容', dataIndex: 'content', align: 'center' },
    { title: '操作',
      align: 'left',
      render: (text, record) => (
        record.status === 0 ? <div><Popconfirm onConfirm={this.handleRemove(record)} title='是否拒绝次申请?' ><a>拒绝</a></Popconfirm><Divider type='vertical' /><Popconfirm onConfirm={this.handleConfirm(record)} title='确认审批通过?' >
          <a>同意</a></Popconfirm></div> : record.status === 1 || record.status === 3 ? <div><font size='2' color='blue'>审批通过</font> <br /> 审批人:{record.confirmUser} <br /> 审批时间: {record.confirmTime} </div> : <div><font size='2' color='blue'>拒绝</font><br /> 审批人:{record.confirmUser}  <br /> 审批时间:{record.confirmTime} </div>
      )
    }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  defaultValue = { id: null }

  state = { visible: false, isUpdate: false, value: {} }

  // 获取列表
  componentDidMount () {
    this.getList()
  }

  getList () {
    const { dispatch } = this.props
    dispatch({
      type: getListUri,
      payload: { recordType: 2 }
    })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // 添加 与 编辑
  onFinish = values => {
    const { dispatch } = this.props
    // transfer Moment object to timestamp !!!
    // values.submitTime = values.submitTime.unix()
    console.log(values)
    var url = addItemUri
    dispatch({
      type: url,
      payload: values
    }).then(res => {
      this.getList()
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 显示弹窗
  showModal = (isUpdate, record) => e => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.bugYYLevel = v.buyYYLevel == null ? 1 : v.buyYYLevel
      v.safeStrategy = v.safeStrategy == null ? 1 : v.safeStrategy
      v.yyLevel = v.yyLevel == null ? 1 : v.yyLevel
      v.amountLimit = v.amountLimit == null ? 5000 : v.amountLimit
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, value: {}, isUpdate: isUpdate, title: isUpdate ? '更新' : '添加' })
  }

  // 关闭弹窗
  hidModal = () => {
    this.setState({ visible: false })
  }

  // 删除
  handleRemove = record => e => {
    const { dispatch } = this.props
    const data = { id: record.id }
    console.log(record.id)
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    }).then(res => {
      this.getList()
    })
  }

  // 添加 与 编辑
  handleConfirm = record => e => {
    const { dispatch } = this.props
    const data = { id: record.id }
    console.log(record.id)
    var url = confirmItemUri
    dispatch({
      type: url,
      payload: data
    }).then(res => {
      this.getList()
    })
  }

  // 更新
  handleUpdate = id => () => {
    const { dispatch } = this.props
    var data = { id: id }
    dispatch({
      type: `${namespace}/addItem`,
      payload: data
    })
  }

  saveFormRef = formRef => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list, extra } } = this.props
    const { visible, title } = this.state
    const formLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    console.log(this.props)
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <div>注意事项:</div><div> 1、请刷新页面</div><div> 2、请仔细检查数据准确性后审批</div>
            <br /><div><font color='red'>{extra}</font></div>
            <Divider />
            <Table dataSource={list} columns={this.columns} rowKey={(record, index) => index} pagination={this.defaultPageValue} />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hidModal} onOk={this.handleSubmit}>
          <Form {...formLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem label='开启日期' name='dateRange' rules={[{ type: 'array', required: true, message: 'Please select time!' }]}>
              <DateRangePicker style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='开启时段' name='timeRange' rules={[{ type: 'array', required: true, message: 'Please select time!' }]}>
              <TimeRangePicker style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='升级帽子数' name='hatNums' rules={[{ required: true, message: 'Please select time!' }]}>
              <Input />
            </FormItem>
            <FormItem label='返奖倍数' name='oddsList' rules={[{ required: true, message: 'Please select time!' }]}>
              <Input />
            </FormItem>
            <FormItem label='截断值' name='winOffset' rules={[{ required: true, message: 'Please select time!' }]}>
              <Input />
            </FormItem>
            <FormItem label='备注' name='comment'>
              <Input />
            </FormItem>
            <FormItem name='id' hidden>
              <Input hidden />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default DSUpgradeConfigConfirm
