import request from '@/utils/request'
import { stringify } from 'qs'

export function getPropsList () {
  return request(`/lottery/get_props_config_list`)
}

export function getLists (params) {
  return request(`/glory_hall_boss/get_props_wall_info?${stringify(params)}`)
}

export function update (params) {
  return request(`/glory_hall_boss/update_props_wall_info`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
