import React, { Component } from 'react'
import { connect } from 'dva'
import { But<PERSON>, message, Row, Col, Space, Tooltip, Divider, DatePicker, Typography } from 'antd'
import moment from 'moment'
import { timeFormater } from '@/utils/common'
const { Text } = Typography
const namespace = 'guildGradeAssess'

@connect(({ guildGradeAssess }) => ({
  model: guildGradeAssess
}))

class DebugPage extends Component {
  state = {
    timetable: {},
    stepA: null,
    stepB: null
  }

  componentDidMount = () => {
    this.getTimeTable()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  getTimeTable = () => {
    this.callModel('getTimeTableV2', {
      isJsonMode: true,
      isDetailMode: true,
      isRawRespMode: true,
      cbFunc: (ret) => {
        console.debug('ret===>', ret)
        const { status, msg, data } = ret
        if (status !== 0) {
          message.warn('查询数据失败:' + msg)
          return
        }

        this.setState({ timetable: data, stepA: this.parseDDHHmm(data.stepA), stepB: this.parseDDHHmm(data.stepB) })
      }
    })
  }

  onUpdateTimetable = (stepA, stepB) => {
    this.callModel('setTimeTable', {
      params: {
        stepA: stepA, // 格式DDHHmm
        stepB: stepB
      },
      isJsonMode: true,
      isDetailMode: true,
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('操作失败: ' + msg)
          return
        }
        message.success('操作成功～')
        this.getTimeTable()
      }
    })
  }

  debugTrriger = (tag, desc) => {
    this.callModel('debugTrriger', {
      params: {
        tag: tag
      },
      isJsonMode: true,
      isDetailMode: true,
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('操作失败: ' + msg)
          return
        }
        message.success('操作成功： ' + desc)
      }
    })
  }

  parseDDHHmm = (v) => {
    return moment(v, 'DDHHmm')
  }

  render () {
    const { timetable, stepA, stepB } = this.state
    const isProd = ENV_TAG === 'prod'
    return (
      <div>
        <Row>
          <Col span={24} style={{ marginBottom: '1em' }} hidden={isProd}>
            <Space>
              <Tooltip title='生成系统评级、开始运营审核阶段的时间，格式=DDHHMM'>
                StepA:
                <DatePicker format='DD-H:mm' value={stepA} showTime onChange={v => this.setState({ stepA: v })} />
              </Tooltip>
              <Tooltip title='确认评级结果的时间。若未完成审批，将采用系统评级结果，格式=DDHHMM'>
                StepB:
                <DatePicker format='DD-H:mm' value={stepB} showTime onChange={v => this.setState({ stepB: v })} />
              </Tooltip>
              <Button onClick={() => { this.onUpdateTimetable(stepA.format('DDHHmm'), stepB.format('DDHHmm')) }}>更新配置</Button>
            </Space>
          </Col>
          <Col span={24} style={{ marginBottom: '1em' }} hidden={isProd}>
            <Space>
              <Tooltip title='模擬0点时，系统自动让审批通过的更新等级请求、更新标签请求生效'>
                <Button onClick={() => { this.debugTrriger('TimerJobHandlePassedRequire', '已审批请求生效') }}>已审批请求生效</Button>
              </Tooltip>
              <Button onClick={() => { this.debugTrriger('timeA', '生成考核表') }}>生成考核表</Button>
              <Button onClick={() => { this.debugTrriger('timeB', '系统复核') }}>系统复核</Button>
              <Button onClick={() => { this.debugTrriger('timeAToManual', '人工复核推送如流') }}>人工复核推送如流</Button>
              <Button onClick={() => { this.debugTrriger('timeBToPre', '预考核') }}>预考核</Button>
            </Space>
          </Col>
          <Divider />
          <Col span={24}>
            <Text>
              【一些Debug信息】<br />
              StepA配置: {timetable.stepA} <br />
              StepB配置: {timetable.stepB} <br />
              考核月份: {timetable.assessMonth} <br />
              生成考核表时间: {timeFormater(timetable.timeA)} <br />
              系统考核时间: {timeFormater(timetable.timeB)} <br />
              当前所在阶段: {timetable.currentStep} <br />
              复核请求信息: 审批状态-{timetable.reviewStatus}；流程ID-{timetable.reviewAprId} <br />
              考核等级依据配置： {JSON.stringify(timetable.guildGradeList)}
            </Text>
          </Col>
        </Row>
      </div>
    )
  }
}

export default DebugPage
