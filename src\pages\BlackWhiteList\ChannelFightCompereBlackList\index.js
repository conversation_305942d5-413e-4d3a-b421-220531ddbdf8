﻿import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Input, Form, Row, Col, Button, Table, Popconfirm, Modal, message } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { checkIsNumber } from '@/utils/common'
var moment = require('moment')

const formItemLayout = {
  labelCol: {
    xs: { span: 5 },
    sm: { span: 5 }
  },
  wrapperCol: {
    xs: { span: 12 },
    sm: { span: 16 }
  }
}

const namespace = 'channelFightCompereBlackList'

@connect(({ channelFightCompereBlackList }) => ({
  model: channelFightCompereBlackList
}))

class CompereVideoBlackList2 extends Component {
  state = {
    modelVisable: false, // 修改配置模态框是否显示
    uidListStr: '',
    remark: ''
  }

  defaultPageValue = {
    defaultPageSize: 100,
    pageSizeOptions: ['100', '200', '500', '2000'],
    showSizeChanger: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  // 组件初始化
  componentDidMount = () => {
    this.refreshUidWhiteList()
    this.initForm()
  }

  // 获取/刷新黑名单列表数据
  refreshUidWhiteList = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getBlackListData`,
      payload: null
    })
  }

  // 清空输入表单
  initForm = () => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
  }

  // 点击 添加标签页-添加按钮
  onAddBtnClick = () => {
    const { uidListStr, remark } = this.state
    const { dispatch } = this.props

    if (uidListStr === '') {
      message.warn('输入内容为空,请检查!')
      return
    }
    let uidStrList = uidListStr.trim().split('\n')
    for (let i = 0; i < uidStrList.length; i++) {
      if (!checkIsNumber(uidStrList[i])) {
        message.warn(`第${i + 1}行: ${uidStrList[i]} 输入有误，请检查`)
        return
      }
    }
    let uidList = uidListStr ? uidListStr.trim().split('\n').map(i => parseInt(i))
      .filter((v) => { return v > 0 }) : []
    if (uidList.length === 0) {
      message.warn('请检查输入内容!')
      return
    }
    dispatch({
      type: `${namespace}/addBlackListByUid`,
      payload: { uidList: uidList, remark: remark, callback: this.refreshUidWhiteList }
    })
    this.setState({ modelVisable: false, uidListStr: '' })
  }

  // 确认删除选中的黑名单
  onComfirmDel = (uid, whiteListType) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/delBlackListByUid`,
      payload: { uidList: [uid], whiteListType: whiteListType }
    })
  }

  // 黑名单列表-删除操作html代码
  deleteBlackListHtml = (record) => {
    let tmpStr = `确定要删除黑名单 [uid=${record.uid}] 吗？`
    return (
      <Popconfirm placement='bottom' title={tmpStr}
        okType='danger' okText='删除' cancelText='取消' onConfirm={() => this.onComfirmDel(record.uid, record.whiteListType)}>
        <a href='#'><DeleteOutlined style={{ color: '#ff3535', fontSize: '1.2em' }} /></a>
      </Popconfirm>
    )
  }

  onAddConfig = () => {
    this.initForm()
    this.setState({ modelVisable: true })
  }

  render () {
    const { route } = this.props
    const { displayData } = this.props.model
    const columns = [
      { title: '#', dataIndex: 'idx' },
      { title: '主持uid', dataIndex: 'uid' },
      { title: '玩法', dataIndex: '-', render: () => '乱斗' },
      { title: '备注', dataIndex: 'remark' },
      { title: '最新操作人', dataIndex: 'opUser' },
      { title: '最新操作时间', dataIndex: 'updateTime', key: 'updateTime', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD HH:mm:ss') },
      { title: '操作', render: (record) => this.deleteBlackListHtml(record) }
    ]
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Row style={{ paddingBottom: '1em' }}>
            <Button type='primary' onClick={() => { this.onAddConfig() }}>添加</Button>
          </Row>
          <Row >
            <Col span={24}>
              <Table columns={columns} dataSource={displayData} size='small' pagination={this.defaultPageValue} />
            </Col>
          </Row>
          {/* 修改配置模态框 */}
          <Modal title='添加'
            forceRender
            visible={this.state.modelVisable}
            onCancel={() => { this.setState({ modelVisable: false }) }}
            onOk={this.onAddBtnClick} >
            <Row>
              <Col span={24}>
                <Form {...formItemLayout}
                  initialValues={{ uidListStr: '', remark: '' }}
                  ref={form => { this.formRef = form }}
                >
                  <Form.Item label='uid' name='uidListStr' tooltip={{ title: '支持多个uid换行分隔' }} rules={[{ required: true, message: 'uid为空' }]}>
                    <Input.TextArea placeholder='换行分割...' autoSize={{ minRows: 1 }} onChange={e => this.setState({ uidListStr: e.target.value })} />
                  </Form.Item>
                  <Form.Item label='备注' name='remark' >
                    <Input placeholder='输入需要备注的内容'
                      onChange={e => this.setState({ remark: e.target.value })}
                    />
                  </Form.Item>
                </Form>
              </Col>
            </Row>
          </Modal>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default CompereVideoBlackList2
