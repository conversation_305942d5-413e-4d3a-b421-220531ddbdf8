## 导出远程数据组件
本组件导出格式为 **.xlsx** 格式数据，适用于如下几个场景：

1. 直接导出固定|选中数据
2. 提供了查询待导出数据的接口，只要接口能查到所有需要导出数据即可，分页 or 查询全量接口均可
   - 这种实际上就是组件内部，分页调用接口，直到获取全量需要导出数据，然后进行导出
3. 导出的数据渲染，支持 **Table** 组件中的 **columns** 定义，可以根据此 columns 渲染导出

### 基本使用
- 引入组件
```javascript
import ExportRemoteData from '@/components/ExportRemoteData'
```

- 插入组件
```javascript
// 指定分页数据获取的uri地址构造器
<ExportRemoteData uriBuilder={(page, size) => `/user/list?page=${page}&pageSize=${size}`/>
```

```javascript
// 直接指定数据提供者，一般用在选择导出功能上, dataProvider 直接返回数据列表
<ExportRemoteData dataProvider={() => this.state.selectedDataList} />
```

> 以上是最简单的使用方式，并且使用了一些默认值，主要如下：
> - **title** 导出按钮的名称，默认就是："导出" 二字
> - **filename** 导出文件名，默认用的是 yyyyMMddHHmmss
> - **columns** 指定导出的表格列定义，默认表头就用数据列表的 属性名称，比如有 dataList = [{"uid": 1, "name": "Test""}], 那么最终表头就是两列， uid,name
> - **method** 请求方法，设置了 uriBuilder 才有效，默认是 GET 方法请求

### 导出固定|选中数据
```javascript
<ExportRemoteData
  filename={'导出测试'} // 待导出的文件名，最终的文件名会变成 “导出测试_yyyyMMddHHmmss.xlsx”
  dataProvider={() => this.state.selectedDataList}  // 返回选中的数据，或者固定数据列表，比如 this.state.selectedDataList = [{"uid": 1, "name": "Test""}]
  columns={[  // 导出的列定义，title 为导出的表头名称， dataIndex 表示数据列表中数据的属性名称，一般情况下，columns 可以直接取 Table 中的 columns 定义
    {title: 'UID', dataIndex: 'uid'},
    {title: '名称', dataIndex: 'name'}
  ]}
/>
```

### 导出具有查询全量数据的接口数据

假设已经有接口 **/user/listAll?uid=xxx**, 现在要导出这个接口的所有数据，那么只需要下面这样子即可：
```javascript
// 注意，此处实际上 /user/listAll 是GET接口
<ExportRemoteData
  filename={'全量用户'}
  uriBuilder= {(page, size) => `/user/listAll?uid=xx`}  // 因为无需分页，这里还可以带上指定条件参数
  columns={this.tableColumns} // 这里可以直接用 Table 展示数据时候用的columns
/>
```

上面的接口是 HTTP GET 的，那么如果提供的是 HTTP POST，使用方法如下：
```javascript
<ExportRemoteData
  filename={'全量用户'}
  method={'POST'}
  contentType="json"
  bodyBuilder={() => JSON.stringify({uid: 123})}
  uriBuilder= {() => `/user/listAll`}  // 因为无需分页，这里还可以带上指定条件参数
  columns={this.tableColumns} // 这里可以直接用 Table 展示数据时候用的columns
/>
```

注意，POST 模式下，我们多配置了几个字段：
- **method** 指定使用 POST 请求
- **contentType** 指定请求头 Content-Type, 可以用简写，json|form, 默认是form，会转成 **application/json;charset=UTF-8**
，**application/x-www-form-urlencoded;charset=UTF-8**
- **bodyBuilder** 请求参数构造，这个要根据，一般情况下，如 contentType = json, 那么使用 JSON.stringify(params) 转换参数，如过 contentType = form 那么使用 import { stringify } from 'qs' 中的 stringify(params) 转换

### 导出具有分页查询接口的数据

假设已经有接口 **/user/listAll?page=1&pageSize=10&uid=xxx**, 现在要导出这个接口的所有数据，那么只需要下面这样子即可：
```javascript
// 注意，此处实际上 /user/listAll 是GET接口
<ExportRemoteData
  filename={'全量用户'}
  uriBuilder= {(page, size) => `/user/listAll?page=${page}&pageSize=${size}&uid=xx`}  // 需要带上分页参数和查询条件
  columns={this.tableColumns} // 这里可以直接用 Table 展示数据时候用的columns
/>
```
上面的接口是 HTTP GET 的，那么如果提供的是 HTTP POST，使用方法如下：
```javascript
<ExportRemoteData
  filename={'全量用户'}
  method={'POST'}
  contentType="json"
  bodyBuilder={(page, size) => JSON.stringify({page: page, pageSize: size, uid: 123})}
  uriBuilder= {(page, size) => `/user/listAll`}  // 因为无需分页，这里还可以带上指定条件参数
  columns={this.tableColumns} // 这里可以直接用 Table 展示数据时候用的columns
/>
```

### 关于 columns 定义
columns 就是定义导出的文件表头和数据渲染规则，可以复用原本页面中 Table 中的 columns，在原有的 columns 基础上还进行了一些扩展，只要支持以下两个字段扩展：

- **exportIgnore** 导出的时候是否忽略，如为 **true** 那么导出的时候不会导出该字段
- **exportRender** 导出时候的渲染函数，需要提供 func (value, record) string|number.. 的函数实现，针对一些特殊的列渲染，因为可能原本的 columns 定义的render 可能返回的是 Antd 元素，不利于展示

```javascript
var columns = [{
 title: 'UID',
 dataIndex: 'uid',
 render: (fieldValue, record) => { return fieldValue },  // 渲染函数, 其中 fieldValue = record[dataIndex]
 exportRender: (fieldValue, record) => { return fieldValue }, // 渲染函数，优先于 render 使用
 exportIgnore: true | false // 是否需要忽略导出
}]
```

**这里要特别注意的是，如果设置了 exportRender， 那么就会直接使用 exportRender 进行导出渲染数据，否则会用 render， 如 render 也没有设置，就直接导出字段对应值**

### 导出时忽略某些标题

使用 **ignoreHeader** 可以指定一个字符串数组，用于忽略某些列的导出，匹配的规则是 只要 ignoreHeaders.containOneOf(columns[i].title, columns[i].dataIndex) 就不会被导出

还有另外一种方式，就是直接设置 columns[i].exportIgnore = true 也可以直接忽略该字段导出

另外， columns[i].title = “操作” 的字段也会直接被忽略导出

### dataFilter 待导出数据过滤
有些场景，比如后台的接口，本身只能查询全量数据，然后boss后台这里做了过滤功能，导出的时候，需要按照规则进行过滤，这个时候就可以使用这个函数来判定一个数据是否需要导出

```javascript
<ExportRemoteData
  dataFilter={(record) => {
    // 这里计算过滤逻辑, 返回 true 表示需要导出
    return true
  }}
  uriBuilder= {(page, size) => `/user/listAll`}  // 因为无需分页，这里还可以带上指定条件参数
/>
```

### 导出前数据预处理
有些场景，需要从接口获取到的数据中，添加一些虚拟的字段，比如加个序号，导出的时候也导出这个需要，那么就可以用这个属性

```javascript
<ExportRemoteData
  prepareHandle={(recordList) => {
    // 预处理，比如这里加上一个 index 序号
    recordList.forEach((item, index) => {
      item.index = index
    })
  }}
  uriBuilder= {(page, size) => `/user/listAll`}  // 因为无需分页，这里还可以带上指定条件参数
/>
```

### 设置导出按钮样式

目前只提供了三个属性 **buttonType, buttonStyle, buttonSize**, 分别对应原本 **Button** 中的 **type, style, size**

其中 **buttonType** 默认是 primary


### 接口数据转成数据列表
接口数据转成数据列表，默认提供了一些规则，基本上能满足大部分的场景了，如过默认的规则无法拿到响应中的数据列表，那么可以通过 **resConverter** 进行自定义，**如果响应有问题，那么请直接 throw new Error('xx')**

默认的取数据列表规则如下：
> 1. 扫描 res 第一层属性，如有 Array 类型的直接返回
> 2. 扫描 res 第二层属性，如有 Array 类型的直接返回
> 3. 扫描 res 第三层属性，如有 Array 类型的直接返回
> 4. 1-3 层属性都没有数据，不继续找了，查找第一层中的 status|state|code, 如 !=0 或者 != 200, 直接 throw new Error()

如果确实响应比较奇特，就通过自定义来获取：
```javascript
<ExportRemoteData
  uriBuilder= {(page, size) => `/user/listAll`}  // 因为无需分页，这里还可以带上指定条件参数
  resConverter={(res) => {
    if (res.status !== 0) {
      throw new Error('数据查询错误:' + res.status + ':' + res.msg)
    }
    // 返回数据列表
    return res.data.list
  }}
/>
```
