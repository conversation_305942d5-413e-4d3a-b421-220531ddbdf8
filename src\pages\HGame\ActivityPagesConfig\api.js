import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists () {
  return request('/hgamebackend/get_hgame_activity_page_list', { jsonp: true })
}

export function add (params) {
  return request(`/hgamebackend/add_hgame_activity_page_info?${stringify(params)}`, { jsonp: true })
}

export function remove (params) {
  return request(`/hgamebackend/del_hgame_activity_page_info?${stringify(params)}`, { jsonp: true })
}

export function update (params) {
  return request(`/hgamebackend/mod_hgame_activity_page_info?${stringify(params)}`, { jsonp: true })
}
