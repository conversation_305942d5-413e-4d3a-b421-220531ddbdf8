import { getLists, add, remove } from './api'
import { message } from 'antd'

export default {
  namespace: 'whitelistConfig',

  state: {
    list: []
  },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      try {
        const { data: { list } } = yield call(getLists)
        yield put({
          type: 'updateList',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        console.log('getList', e)
      }
    },

    * addItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(add, payload)
      if (status === 0) {
        message.success('添加成功')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('添加失败： ' + msg)
      }
    },

    * removeItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(remove, payload)
      if (status === 0) {
        message.success('删除成功')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('删除失败： ' + msg)
      }
    }
  }
}
