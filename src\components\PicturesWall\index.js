import React, { Component } from 'react'
import { Upload, Modal, Popover } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import { getUploadFilesURL, getUploadBusiness } from '@/utils/upload'
import PropTypes from 'prop-types'

class PicturesWall extends Component {
  static propTypes = {
    title: PropTypes.string,
    business: PropTypes.string,
    bucket: PropTypes.string,
    privacy: PropTypes.bool
  }
  constructor (props) {
    super(props)

    console.log('props:', props)

    this.state = {
      fileList: props.value ? [urlValue(props.value)] : [],
      previewImage: '',
      previewVisible: false
    }
  }

  componentWillReceiveProps (nextProps) {
    // 受控组件， 用于更新
    const { value } = nextProps
    this.setState({
      fileList: value ? [urlValue(value)] : []
    })
  }

  handlePreview = file => {
    this.setState({
      previewImage: file.url || file.thumbUrl,
      previewVisible: true
    })
  }

  handleCancel = () => this.setState({ previewVisible: false })

  handleChange = ({ fileList }) => {
    let onChange = this.props.onChange ?? function () {}
    if (fileList.length === 0) {
      onChange()
    }
    if (fileList.length > 0 && fileList[fileList.length - 1].status === 'done' && fileList[fileList.length - 1].response.status === 0) {
      var url = fileList[fileList.length - 1].response.urls[0]
      onChange(url) // update getFiledDecorator
    }
    this.setState({ fileList })
  }

  renderImage = () => {
    const { value } = this.props
    const content = (
      <div>
        <img src={value} style={{ maxHeight: 200, maxWidth: 200 }} />
      </div>
    )
    return (
      <div>
        <Popover placement='right' content={content} title={null}>
          <img width='102' height='102' src={value} />
        </Popover>
      </div>
    )
  }

  render () {
    const { title, business, bucket, privacy, beforeUpload } = this.props
    const { previewVisible, previewImage, fileList } = this.state
    const uploadButton = (
      <div>
        <PlusOutlined />
        <div className='ant-upload-text'>Upload</div>
      </div>
    )

    const uploadBusiness = getUploadBusiness(business)
    const uploadBucket = !bucket ? 'makefriends' : bucket
    const uploadAction = getUploadFilesURL(privacy)
    return (
      <div className='clearfix'>
        <Upload
          action={uploadAction}
          listType='picture-card'
          fileList={fileList}
          onPreview={this.handlePreview}
          onChange={this.handleChange}
          // showUploadList={false}
          data={file => ({ business: uploadBusiness, bucket: uploadBucket, files: file })}
          beforeUpload={(file, fileList) => { return beforeUpload ? beforeUpload(file, fileList) : true }}
        >
          {fileList.length > 0 ? null : uploadButton}
        </Upload>
        <Modal visible={previewVisible} footer={null} onCancel={this.handleCancel}>
          <img alt='example' style={{ width: '100%' }} src={previewImage} />
        </Modal>
        {title !== undefined && title.length > 0 ? <font color='blue' style={{ marginLeft: 35 }} >{title}</font> : null }
      </div>
    )
  }
}

function urlValue (url) {
  return { uid: -1, status: 'done', url: url, thumbUrl: url }
}

export default PicturesWall
