import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import DropMainPoolEditor from '@/pages/DropRefactor/DropMain/components/pool_editor'
import { message } from 'antd'
import { initGlobalBossConfig } from '../globalConfig'
import { isVoiceRoomPath } from '../dropCommon'
import { connect } from 'dva'
const namespace = 'dropMain'

@connect(({ dropMain }) => ({
  model: dropMain
}))

class DropPoolApproval extends Component {
  state = {
    groupType: isVoiceRoomPath(this.props.route.path) ? 'vr' : 'jy'
  }

  componentDidMount = () => {
    initGlobalBossConfig(this.changeState, this.state.groupType)
    this.getAllPrize()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 获取所有奖励列表
  getAllPrize = () => {
    this.callModel('getAllPrizeList', {
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('获取奖励列表失败: msg=' + msg)
        }
      }
    })
  }

  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name} >
        <DropMainPoolEditor groupType={this.state.groupType} />
      </PageHeaderWrapper>
    )
  }
}

export default DropPoolApproval // 要改
