import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists () {
  return request('/lottery/get_cashapon_prize_list_by_boss', { jsonp: true })
}

export function add (params) {
  return request(`/lottery/add_cashapon_prize_info_by_boss?${stringify(params)}`, { jsonp: true })
}

export function update (params) {
  return request(`/lottery/mod_cashapon_prize_info_by_boss?${stringify(params)}`, { jsonp: true })
}

export function remove (params) {
  return request(`/lottery/del_cashapon_prize_info_by_boss?${stringify(params)}`, { jsonp: true })
}
