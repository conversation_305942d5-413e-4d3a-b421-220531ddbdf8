const loadedScriptPromise = {}

function loadScript (src) {
  return new Promise((resolve, reject) => {
    const script = document.createElement('script')

    const remove = () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script)
      }
      script.onload = script.onabort = script.onerror = null
    }

    script.src = src
    // script.crossOrigin = 'anonymous'
    script.onload = () => {
      remove()
      resolve()
    }
    script.onabort = e => {
      remove()
      reject(e)
    }
    script.onerror = e => {
      remove()
      reject(e)
    }
    document.head.appendChild(script)
  })
}
async function loadShareModule (src, scopeName, module) {
  if (!loadedScriptPromise[src]) {
    loadedScriptPromise[src] = loadScript(src)
  }

  await loadedScriptPromise[src]

  const factory = await window[scopeName].get(module)
  return factory()
}

export default loadShareModule
