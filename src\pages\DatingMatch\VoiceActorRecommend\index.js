import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Modal, Input, Select, Upload, message } from 'antd'
import { connect } from 'dva'
import { exportExcel } from 'xlsx-oc'
import PopImage from '@/components/PopImage'
import { UploadOutlined } from '@ant-design/icons'

const namespace = 'VoiceActorRecommend'
const FormItem = Form.Item
const Search = Input.Search
const Option = Select.Option

@connect(({ VoiceActorRecommend }) => ({
  VoiceActorRecommend
}))

class Index extends Component {
  // column structs.
  columns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: '主持YY号', dataIndex: 'yy_number', key: 'yy_number', align: 'center' },
    { title: 'UID', dataIndex: 'uid', key: 'uid', align: 'center' },
    { title: '开播状态', dataIndex: 'live_state', key: 'live_state', align: 'center' },
    { title: '主持昵称', dataIndex: 'nick', key: 'nick', align: 'center' },
    { title: '配置频道', dataIndex: 'sid', key: 'sid', align: 'center' },
    { title: '配置子频道', dataIndex: 'ssid', key: 'ssid', align: 'center' },
    { title: '推荐标题', dataIndex: 'title', key: 'title', align: 'center' },
    { title: '推荐海报',
      dataIndex: 'cover',
      key: 'cover',
      align: 'center',
      render: (text, record) => (
        <PopImage value={record.cover} />
      )
    },
    { title: '权重', dataIndex: 'weight', key: 'weight', align: 'center' },
    { title: '推荐位置', dataIndex: 'zone', key: 'zone', align: 'center' },
    { title: '推荐时间', dataIndex: 'recomm_date', key: 'recomm_date', align: 'center' },
    { title: '推荐状态',
      dataIndex: 'state',
      key: 'state',
      align: 'center',
      render: (text, record) => (
        text === 0 ? '推荐' : '未推荐'
      )
    },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <Button style={{ marginRight: 10 }} size='small' type='primary' onClick={this.showModal(true, record)}>修改</Button>
          { record.state === 1 ? <Button size='small' type='primary' onClick={this.updateState(record.uid, 1)}>上推荐</Button> : <Button size='small' type='primary' onClick={this.updateState(record.uid, 2)}>下推荐</Button>}
        </span>)
    }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {} }

  // show modal
  showModal = (isUpdate, record) => () => {
    if (this.formRef) {
      this.formRef.setFieldsValue(record)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      var value = selectedRows.map(item => item.uid).join(',')
      this.setState({ removeKey: value })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name
    })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // add and update
  onFinish = values => {
    const form = this.formRef.props.form
    const { dispatch } = this.props
    const { isUpdate } = this.state
    const url = isUpdate ? `${namespace}/updateItem` : `${namespace}/addItem`
    dispatch({
      type: url,
      payload: values
    })
    form.resetFields()
    this.setState({ visible: false })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { uids: this.state.removeKey }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  handleDelOne = uid => e => {
    const { dispatch } = this.props
    const data = { uids: uid }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  updateState = (uid, recomm) => e => {
    const { dispatch } = this.props
    const data = { uid: uid, recomm: recomm }
    dispatch({
      type: `${namespace}/updateItem`,
      payload: data
    })
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = { type: this.state.type }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // reset search info
  handleReset = clearFilters => () => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  // get list from server.
  searchBy = (value) => {
    const { dispatch } = this.props
    const data = { yynum: this.state.searchYY, sid: this.state.searchSid, livestate: this.state.livestate }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  handleChange = (value) => {
    const { dispatch } = this.props
    const data = { livestate: value.key }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })

    this.setState({ livestate: value.key })
    // console.log(value) // { key: "lucy", label: "Lucy (101)" }
  }

  onExport = () => {
    let headers = []
    let columns = this.columns
    const { exportKey } = this.state
    columns.forEach(function (item) {
      headers.push({ k: item.dataIndex, v: item.title })
    })
    exportExcel(headers, exportKey)
  }

  UpLoadOnChange = info => {
    if (info.file.status !== 'done') {
      return
    }
    if (info.file.response.status === 0) {
      message.success(`${info.file.name} file uploaded successfully`)
    } else {
      message.error(info.file.response.msg)
    }
    const { dispatch } = this.props
    const data = {}
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // content
  render () {
    const { route, VoiceActorRecommend: { list } } = this.props
    const { visible, isUpdate, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Input placeholder='search by yy' onChange={e => this.setState({ searchYY: e.target.value })} style={{ width: 250 }} /> {/* 搜索按钮 */}
            <Search onSearch={value => this.searchBy(value)} placeholder='search by sid' onChange={e => this.setState({ searchSid: e.target.value })} style={{ width: 250 }} /> {/* 搜索按钮 */}
            <Divider type='vertical' /> {/* 分割线 */}
            开播状态
            <Divider type='vertical' /> {/* 分割线 */}
            <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 120 }} onChange={this.handleChange}>
              <Option value='0'>全部</Option>
              <Option value='1'>开播中</Option>
              <Option value='2'>未开播</Option>
            </Select>
            <Divider type='vertical' /> {/* 分割线 */}
            <Button type='primary'>
              <a href='http://makefriends.bs2dl.yy.com/1542101590_4135a76904a4ca3155f5a6031a24bd15.xlsx' type='primary' onClick={this.downLoad}>音频TAB导入模板</a>
            </Button>
            <Divider type='vertical' /> {/* 分割线 */}
            <Upload action='/VoiceActorRecommend/Insert' onChange={this.UpLoadOnChange} >
              <Button type='primary'>
                <UploadOutlined /> 批量导入
              </Button>
            </Upload>
            <Divider /> {/* 分割线 */}
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onExport}>导出</Button>
            <Divider type='vertical' /> {/* 分割线 */}
            <Button type='primary' onClick={this.handleDel(1)}>删除</Button>
            <Divider />`
            <Table rowKey={(record, index) => index} rowSelection={this.rowSelection} dataSource={list} columns={this.columns} pagination={this.defaultPageValue} />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem label='uid' name='uid' rules={[{ required: true }]}>
              <Input readOnly={isUpdate} />
            </FormItem>
            <FormItem label='权重' name='weight' rules={[{ required: true }]}>
              <Input />
            </FormItem>
            <FormItem label='推荐位置' name='zone' rules={[{ required: true }]}>
              <Input />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default Index
