import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Table, DatePicker, InputNumber, Button, message, Input, Modal, Form, Select, Space } from 'antd'

const namespace = 'newGuildSettleIn'

const greenChannelMap = [
  { label: '-', value: -1 },
  { label: '普通公会', value: 0 },
  { label: '水晶公会', value: 1 },
  { label: '超级水晶公会', value: 2 },
  { label: '-', value: 3 }
]

const statusYes = 1 // 有效
const statusNo = 2 // 失效
const statusMap = { 0: '-', 1: '生效中', 2: '已失效' }

const sidTypeLong = 1 // 长位ID
const sidTypeShort = 2 // 短位ID

var moment = require('moment')
var dateFormat = 'YYYY-MM-DD'

@connect(({ newGuildSettleIn }) => ({
  model: newGuildSettleIn
}))

class NewGuildSettleInWhite extends Component {
  columns = [
    { title: '序号', dataIndex: 'idx', align: 'center' },
    { title: '频道ID', dataIndex: 'sid', align: 'center' },
    { title: '添加时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (record.timestamp === 0 ? '' : moment.unix(record.timestamp).format(dateFormat)) },
    { title: '添加人', dataIndex: 'createUser', align: 'center' },
    { title: '开通绿色通道', dataIndex: 'greenChannel', align: 'center', render: v => greenChannelMap.find(i => i.value === v).label },
    { title: '当前状态', dataIndex: 'status', align: 'center', render: (text, record) => (record.status === statusYes ? <span><fond color='red'>{statusMap[record.status]}</fond></span> : <span><fond>{statusMap[record.status]}</fond></span>) },
    { title: '失效时间', dataIndex: 'invalidTime', align: 'center', render: (text, record) => (record.invalidTime === 0 ? '' : moment.unix(record.invalidTime).format(dateFormat)) },
    { title: '添加原因', dataIndex: 'addReason', align: 'center' }
  ]

  state = {
    visible: false
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  componentDidMount () {
    this.loadData()
  }

  loadData = () => {
    const { searchSID, searchStatus, searchCreateStartTime, searchCreateEndTime } = this.state
    const { dispatch } = this.props

    let searchCreateStartTimeTmp = 0
    let searchCreateEndTimeTmp = 0

    if (searchCreateStartTime) {
      searchCreateStartTimeTmp = moment(searchCreateStartTime).unix()
    }
    if (searchCreateEndTime) {
      searchCreateEndTimeTmp = moment(searchCreateEndTime).unix()
    }

    if (searchCreateStartTimeTmp !== 0 && searchCreateEndTimeTmp !== 0 && searchCreateStartTimeTmp >= searchCreateEndTimeTmp) {
      message.warn('开始时间不能大于结束时间')
      return
    }

    let data = { sid: searchSID, status: searchStatus, createStartTime: searchCreateStartTimeTmp, createEndTime: searchCreateEndTimeTmp }
    dispatch({
      type: `${namespace}/listGreenChannelWhite`,
      payload: data
    })
  }

  searchHandle = () => () => {
    this.loadData()
  }

  addHandle = () => () => {
    this.setState({ visible: true })
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  handleCancel = e => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.setState({ visible: false, guildRoleSid: 0, sidType: 0 })
  }

  handleSubmit = e => {
    if (this.formRef) {
      this.formRef.submit()
    }
  }

  onFinish = values => {
    const { dispatch } = this.props
    let sid = Number(values.sid)
    let data = { sidType: values.sidType, sid: sid, addReason: values.addReason }
    console.log(data)
    dispatch({
      type: `${namespace}/addGreenChannelWhite`,
      payload: data
    })
    this.setState({ visible: false, guildRoleSid: 0, sidType: 0 })
    if (this.formRef) {
      this.formRef.resetFields()
    }
  }

  getGuildRoleHandle = () => () => {
    const { guildRoleSid, sidType } = this.state
    const { dispatch } = this.props

    let updateGuildRoleStatus = (cur, next) => {
      if (this.formRef) {
        this.formRef.setFieldsValue({ curRole: greenChannelMap.find(i => i.value === cur).label, openRole: greenChannelMap.find(i => i.value === next).label })
      }
    }
    dispatch({
      type: `${namespace}/getGuildRole`,
      payload: { play: { sid: guildRoleSid, sidType: sidType }, func: updateGuildRoleStatus }
    })
  }

  render () {
    const { visible } = this.state
    const { model: { listWhite, guildRole } } = this.props

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 2 },
        sm: { span: 15 }
      }
    }
    console.log(guildRole)
    return (
      <Card>
        <span>频道ID</span>
        <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchSID: e })} style={{ width: 100, marginLeft: 3 }} />
        <span style={{ marginLeft: 20 }}>申请时间</span>
        <DatePicker
          format='YYYY-MM-DD'
          placeholder='开始时间'
          onChange={(v) => this.setState({ searchCreateStartTime: v })}
          style={{ marginLeft: 10 }}
        />
        <span style={{ marginLeft: 3 }}>~</span>
        <DatePicker
          format='YYYY-MM-DD'
          placeholder='结束时间'
          onChange={(v) => this.setState({ searchCreateEndTime: v })}
          style={{ marginLeft: 3 }}
        />

        <span style={{ marginLeft: 20 }}>当前状态</span>
        <Select allowClear style={{ marginLeft: 3, width: 110 }} onChange={(v) => this.setState({ searchStatus: v })}>
          <Select.Option value={statusYes}>生效中</Select.Option>
          <Select.Option value={statusNo}>已失效</Select.Option>
        </Select>

        <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
        <Button style={{ marginLeft: 20 }} type='primary' onClick={this.addHandle()}>添加</Button>
        <Table style={{ marginTop: 10 }} size='small' rowKey='idx' pagination={this.defaultPageValue} columns={this.columns} dataSource={listWhite} />

        <Modal forceRender width={450} visible={visible} title='添加绿色通道白名单' onCancel={this.handleCancel} onOk={this.handleSubmit}>
          <div style={{ marginLeft: 10 }}><font>注意: 选择频道类型, 输入频道后, 点击【获取】按钮，获取当前公会身份信息</font></div>
          <Form style={{ marginTop: 10 }} {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <Form.Item name='id' hidden>
              <Input hidden />
            </Form.Item>
            <Form.Item label='频道类型' name='sidType' rules={[{ required: true }]}>
              <Select onChange={(v) => this.setState({ sidType: v })}>
                <Select.Option value={sidTypeLong}>长位ID</Select.Option>
                <Select.Option value={sidTypeShort}>短位ID</Select.Option>
              </Select>
            </Form.Item>
            <Form.Item label='频道ID' name='sid' rules={[{ required: true }]}>
              <Input width={150} maxLength={20} onChange={e => this.setState({ guildRoleSid: e.target.value })} />
            </Form.Item>
            <Form.Item label='当前公会身份'>
              <Space align='start'>
                <Form.Item name='curRole' rules={[{ required: false }]}>
                  <Input disabled='true' />
                </Form.Item>
                <Button type='primary' onClick={this.getGuildRoleHandle()}>获取</Button>
              </Space>
            </Form.Item>
            <Form.Item label='开通绿色通道' name='openRole' rules={[{ required: false }]}>
              <Input disabled='true' />
            </Form.Item>
            <Form.Item label='添加原因' name='addReason' rules={[{ required: true }]}>
              <Input.TextArea rows={3} placeholder='必填， 最多输入20个字' />
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default NewGuildSettleInWhite
