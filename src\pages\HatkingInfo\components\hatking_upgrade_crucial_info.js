import React, { Component } from 'react'
import { Table, Divider, Button, Card, Form, DatePicker } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
const chanMap = { all: 'ALL', pc: 'PC端', zhuiwan: 'YO交友', yomi: 'YO语音' }
// const namespace = 'hatkingPk' // model 的 namespace

@connect(({ hatkingJy }) => ({ // model 的 namespace
  model1: hatkingJy // model 的 namespace
}))

class HatKingUpgradeCrucialInfoComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      list: [],
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
  }

  // 日期 渠道类型  参与总流水 付费用户数 剩余发放监控汇总  成功总流水 成功总人数 累计成功人数  累计失败人数  连胜总发放金额 频道广播数量  全服广播数量
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '渠道类型', dataIndex: 'platform', align: 'center', render: (text, record, index) => { return chanMap[text] }, filters: [{ text: 'ALL', value: 'all' }, { text: 'PC端', value: 'pc' }, { text: 'YO交友', value: 'zhuiwan' }, { text: 'YO语音', value: 'yomi' }], onFilter: (value, record) => record.platform.includes(value) },
    { title: '升级场双参与流水', dataIndex: 'betAmethyst', align: 'center' },
    { title: '升级场双参与成功金额', dataIndex: 'betRewardAmethyst', align: 'center' },
    { title: '升级场双参与人数', dataIndex: 'betUser', align: 'center' },
    { title: '成功总人数', dataIndex: 'betRewardUser', align: 'center' },
    { title: '累计成功人数', dataIndex: 'profitUser', align: 'center' },
    { title: '累计失败人数', dataIndex: 'lossUser', align: 'center' },
    { title: '升级场双参与发放道具比', dataIndex: 'rebateRatio', align: 'center' }
  ]

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getUpgradeCrucialList`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  handleSelectChange = (value) => {
    console.log(value)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { upgradeCrucialList } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form>
          <span style={{ marginLeft: 10 }}>时间范围:</span>
          <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Divider />
          <Table dataSource={upgradeCrucialList} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default HatKingUpgradeCrucialInfoComponent
