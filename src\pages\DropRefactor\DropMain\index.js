import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, message } from 'antd'
import DropPoolCfgComponent from './components/config'
import DropMainComponent from './components/list'
import { connect } from 'dva'
import DropChannelComponent from './components/rule'
// import DropSummaryComponent from './components/summary'
import ProgressConfigComponent from './components/progress'
import DropWarnComponent from './components/warn'
import NewUserReward from './components/new_user_reward'
import NewUserList from './components/new_user_list'
import NewUserMonitor from './components/new_user_monitor'
import ExtraActivityConfig from './components/extraActivityConfig'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import { initGlobalBossConfig } from '../globalConfig'
import { isVoiceRoomPath } from '../dropCommon'

const namespace = 'dropMain'

@connect(({ dropMain }) => ({ // model 的 namespace
  model: dropMain // model 的 namespace
}))

class DropMain extends Component { // 默认页面组件，不需要修改
  state = {
    groupType: isVoiceRoomPath(this.props.route.path) ? 'vr' : 'jy'
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getPropsConfigList` // 旧的奖励列表-准备废弃
    })
    initGlobalBossConfig(this.changeState, this.state.groupType) // 获取全局配置
    this.getAllPrize() // 获取奖励列表
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // 获取所有奖励列表
  getAllPrize = () => {
    this.callModel('getAllPrizeList', {
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('获取奖励列表失败: msg=' + msg)
        }
      }
    })
  }

  render () {
    const { route } = this.props
    const { groupType } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs id='drft' type='card' defaultActiveKey='2'>
            <TabPane tab='新增道具池' key='1'>
              <DropPoolCfgComponent />
            </TabPane>

            <TabPane tab='道具池查看' key='2'>
              <DropMainComponent groupType={groupType} />
            </TabPane>

            {/* <TabPane tab='实时信息' key='3'>
              <DropSummaryComponent groupType={groupType} />
            </TabPane> */}

            <TabPane tab='规则配置' key='4'>
              <DropChannelComponent />
            </TabPane>

            <TabPane tab='进度配置' key='5'>
              <ProgressConfigComponent groupType={groupType} />
            </TabPane>

            <TabPane tab='预警配置' key='6'>
              <DropWarnComponent />
            </TabPane>

            {
              groupType === 'vr'
                ? ''
                : <>
                  <TabPane tab='新用户策略' key='8' >
                    <NewUserReward />
                  </TabPane>

                  <TabPane tab='新用户UID配置' key='9'>
                    <NewUserList />
                  </TabPane>

                  <TabPane tab='新用户策略监控' key='10'>
                    <NewUserMonitor />
                  </TabPane>
                </>
            }

            <TabPane tab='翻倍活动配置' key='11'>
              <ExtraActivityConfig />
            </TabPane>

          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default DropMain
