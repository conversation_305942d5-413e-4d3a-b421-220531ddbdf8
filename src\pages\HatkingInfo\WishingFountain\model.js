﻿import { message } from 'antd'
import { getCurWishActConfig, delWishActConfig, addAupdateWishActConfig, getStatInfo, getAccountRemain } from './api'

export default {
  namespace: 'wishingFountain',
  state: {
    page: 0,
    size: 0,
    displayList: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },

  effects: {
    * queryList ({ payload }, { select, call, put }) {
      let resp = yield call(getCurWishActConfig)
      const { data: { status, data } } = resp
      if (status !== 0) {
        message.warn('请求数据失败, msg=', resp.data.msg)
        console.error('queryList get bad response: ', resp)
        return
      }
      const list = []
      if (data.actID !== '') {
        list[0] = data
      }
      yield put({
        type: 'updateState',
        payload: { name: 'displayList', newValue: list }
      })
    },
    // 新增或修改配置
    * modifyOrAddConfig ({ payload }, { select, call, put }) {
      const { callback, params } = payload
      params['roundProfit'] = parseInt(params['roundProfit'], 10)
      params['recycleLimit'] = parseInt(params['recycleLimit'], 10)
      params['dailyRecycleLimit'] = parseInt(params['dailyRecycleLimit'], 10)
      params['delayGetReword'] = parseInt(params['delayGetReword'], 10)

      let resp = yield call(addAupdateWishActConfig, params)
      console.log('modifyOrAddConfig', resp)
      const { data: { status } } = resp
      if (status !== 0) {
        message.error('修改配置失败，msg=' + resp.data.msg)
        console.error('modifyOrAddConfig() return bad response: params=', params, 'resp=', resp)
      } else {
        message.info('OK')
      }
      callback()
    },
    // 删除配置
    * deleteConfig ({ payload }, { select, call, put }) {
      const { actID, callback } = payload
      let resp = yield call(delWishActConfig, actID)
      const { data: { status } } = resp
      if (status !== 0) {
        message.error('删除配置失败， msg=' + resp.data.msg)
        console.error('deleteConfig() return bad response: resp=', resp)
      } else {
        message.info('删除成功')
        callback()
      }
    },
    // 查询统计信息
    * getStatInfo ({ payload }, { select, call, put }) {
      let resp = yield call(getStatInfo, payload)
      const { data: { status, data } } = resp
      if (status !== 0) {
        message.warn('请求数据失败, msg=', resp.data.msg)
        console.error('getStatInfo get bad response: ', resp)
        return
      }
      yield put({
        type: 'updateState',
        payload: { name: 'statList', newValue: data }
      })
    },
    // 查询所有官方账户剩余
    * getAccountRemain ({ payload }, { select, call, put }) {
      let resp = yield call(getAccountRemain, payload)
      const { data: { status, data } } = resp
      if (status !== 0) {
        message.warn('请求数据失败, msg=', resp.data.msg)
        console.error('getAccountRemain get bad response: ', resp)
        return
      }
      yield put({
        type: 'updateState',
        payload: { name: 'accountRemainList', newValue: data }
      })
    }
  }
}
