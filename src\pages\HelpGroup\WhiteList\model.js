import * as api from './api'
import { message } from 'antd'
import { whiteListType } from './components/common'

export default {
  namespace: 'helpGroup',

  state: {
    listSendWhiteList: [],
    listRecvWhiteList: [],
    checkData: {}
  },

  reducers: {
    displaySendWhiteList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        listSendWhiteList: payload
      }
    },

    displayRecvWhiteList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        listRecvWhiteList: payload
      }
    },

    updateCheckData (state, { payload }) {
      return {
        ...state,
        checkData: payload
      }
    }
  },

  effects: {
    * listSendWhiteList ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listSendWhiteList, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      for (let i = 0; i < data.length; i++) {
        data[i].idx = i + 1
      }
      yield put({
        type: 'displaySendWhiteList',
        payload: data
      })
    },

    * listRecvWhiteList ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listRecvWhiteList, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      for (let i = 0; i < data.length; i++) {
        data[i].idx = i + 1
      }
      yield put({
        type: 'displayRecvWhiteList',
        payload: data
      })
    },

    * deleteWhiteList ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.deleteWhiteList, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        message.success('删除成功')
        if (payload.type === whiteListType.send) {
          yield put({
            type: 'listSendWhiteList'
          })
        } else {
          yield put({
            type: 'listRecvWhiteList'
          })
        }
      }
    },
  
    * checkWhiteList ({ payload }, { call, put }) {
      if (payload === null || payload === undefined) {
        return
      }
      const { play, func } = payload
      let { data: { status, msg, data } } = yield call(api.checkWhiteList, play)
      if (status !== 0) {
        message.warning(msg)
        return
      }
      func(data.successUidList, data.failUidList)
    },

    * approvalWhiteList ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.approvalWhiteList, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        message.success('已成功提交审核，待运营人员审核完毕后生效')
      }
    }

  }
}
