import React, { Component } from 'react'
import { Card, Row, Col, Typo<PERSON>, Tooltip, Divider, Input, Button, Modal, message, Select, Space } from 'antd'
import { timeFormater, deepClone, getCookie } from '../../utils/common'
import { connect } from 'dva'
import moment from 'moment'
import AuthTree from './AuthTree'
import { Link } from 'dva/router'

const { Title, Text } = Typography
const { Option } = Select
const namespace = 'global'

@connect(({ global }) => ({
  model: global
}))

class HomePage extends Component {
  state = {
    myMenus: [],
    myFavMenus: [],
    expireMenus: [],
    keyword: '',
    authApplyVisable: false,
    menuVisible: false,
    authInfo: {},
    menuList: [],
    applyParams: {
      days: 1,
      reason: '',
      uri: []
    }
  }

  componentDidMount = () => {
    this.updateVisibleMenu()
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  updateVisibleMenu = () => {
    const { menus } = this.props
    // console.debug('menus======>', menus)
    if (!menus) {
      return
    }
    let fixMenus = this.iterateMenus(menus, '') 
    // console.debug('fixMenus===>', fixMenus)  
    let favMenus = this.getFavMenu(fixMenus)
    let expireMenus = this.getExpireMenus(fixMenus)

    this.setState({ myMenus: fixMenus, myFavMenus: favMenus, expireMenus: expireMenus })
  }

  getFavMenu = (all) => {
    let favMenu = []
    // eslint-disable-next-line no-undef
    let favRaw = localStorage.getItem('myFavUri')
    let fav = JSON.parse(favRaw)
    if (!fav || !Array.isArray(fav.favUri || fav.favUri.length === 0)) {
      return []
    }
    let myFavList = fav.favUri
    for (let uri of myFavList) {
      let detail = all.find(item => { return item.uri === uri })
      if (detail) {
        favMenu.push(detail)
      }
    }
    return favMenu
  }

  iterateMenus = (root, lastName) => {
    let nodes = []
    for (let child of root) {
      const { routes, auth, name, uri, path, expireAt } = child
      if (routes) {
        let subNodes = this.iterateMenus(routes, `${lastName}/${name}`)
        if (subNodes.length > 0) {
          nodes.push(...subNodes)
        }
        continue
      }
      if (auth !== 1) {
        continue
      } 
      nodes.push({
        href: path,
        name: name,
        expireAt: expireAt,
        uri: uri,
        path: `${lastName}/${name}`
      })
    }
    return nodes
  }

  // 过滤得到即将过期的页面
  getExpireMenus = (all) => {
    let timeNow = moment().unix()
    let timeExpire = moment().add(7, 'days').unix()
    return all.filter(item => { return item.expireAt > timeNow && item.expireAt < timeExpire })
  }

  menusFilter =(all, keyword) => {
    if (keyword.length === 0) {
      return all
    }
    return all.filter(item => { return item.name.includes(keyword) })
  }

  // 查询是否有权限查看菜单
  showModalBefore = () => {
    this.callModel('queryMenuAuth', {
      params: { uid: getCookie('yyuid'), app: 'jyboss' },
      isDetailMode: true,
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg, list } = ret
        if (status !== 0) {
          message.warn(msg)
          return
        }
       
        const { expireTime } = list
        const now = moment().unix()
        if (expireTime > now) { // 当前在有效期内
          this.showMenuModal()
          return
        }
        this.setState({ authInfo: list, authApplyVisable: true })
      }
    })
  }

  // 获取查看菜单权限
  applyMenuAuth = () => {
    const uid = getCookie('yyuid')
    if (!uid) {
      message.warn('获取uid失败')
      return
    }
    this.callModel('applyMenuAuth', {
      params: { uid: uid, app: 'jyboss', name: 'boss后台' },
      isDetailMode: true,
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn(msg)
          return
        }
        message.success('已提交审批～')
        this.setState({ authApplyVisable: false })
      }
    })
  }

  // 获取菜单列表
  showMenuModal = () => {
    this.callModel('queryUnAuthMenu', {
      isDetailMode: true,
      isRawRespMode: true,
      cbFunc: (ret) => { 
        const { status, msg, list } = ret
        if (status !== 0) {
          message.warn('查询菜单失败: ' + msg)
          return
        }
        this.setState({ menuVisible: true, menuList: this.fixMenuList(list) })
      }
    })
  }

  // 更新树状菜单列表
  fixMenuList = (list) => {
    if (!list) {
      return null
    }
    let after = []
    list.forEach(item => {
      if (item.children) {
        item.children = this.fixMenuList(item.children)
      }
      if ((!item.children || item.children.length === 0) && !item.uri) { // 没有子页面的父级菜单，隐藏
        return
      }
      if (item.removed === 1) { // 隐藏页面
        return
      }
      after.push(item)
    })
    return after
  }

  // 更新参数
  updateParams = (field, value) => {
    const { applyParams } = this.state 
    let newVal = deepClone(applyParams)
    newVal[field] = value
    this.setState({ applyParams: newVal })
  }

  // 申请页面权限
  submitAuthApply = (params) => {  
    if (!params.reason) {
      message.warn('请填写申请理由')
      return
    }
    this.callModel('bossAuthApply', {
      params: params,
      isDetailMode: true,
      isRawRespMode: true,
      isJsonMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('提交送审失败: ' + msg)
          return
        }
        message.success('已提交审批～')
        this.setState({ menuVisible: false }) 
      }
    })
  }

  getTitle = () => {
    const groupType = window.groupType
    if (groupType === 1) {
      return '欢迎使用交友产运后台'
    }
    if (groupType === 2) {
      return '欢迎使用交友技术后台'
    }
    return 'Boss后台开发'
  }

  render () {
    const { myMenus, myFavMenus, expireMenus, keyword, authApplyVisable, authInfo, menuVisible, menuList, applyParams } = this.state
    const { expireTime, aprInfo } = authInfo  
    return <Card>
      <Row>
        <Col span={24} style={{ textAlign: 'center' }}>
          <Title level={4} style={{ color: '#f9a44a' }}>🎉 {this.getTitle()} 🎉</Title>
        </Col>
        <Col span={24} style={{ textAlign: 'center' }}>
          <Input placeholder='搜索组件' onChange={e => this.setState({ keyword: e.target.value })} style={{ fontSize: '15px', width: '30em', height: '2.5em', borderRadius: '15px' }} />
        </Col>
        <div style={{ position: 'absolute', left: '1em', top: '1em' }}>
          <Button type='primary' onClick={() => { this.showModalBefore() }}>申请页面权限</Button>
        </div>
      </Row>
      <Divider style={{ color: '#f9a44a' }}>最近访问</Divider>
      <Row>
        {
          this.menusFilter(myFavMenus, keyword).map(item => {
            return <Col><MenuCard {...item} color='#e6d16526' /></Col>
          })
        }
      </Row>
      <Divider style={{ color: '#f9a44a' }}>即将过期</Divider>
      <Row>
        {
          this.menusFilter(expireMenus, keyword).map(item => {
            return <Col><MenuCard {...item} color='#ffc4c4' /></Col>
          })
        }
      </Row>
      <Divider style={{ color: '#f9a44a' }}>我的页面</Divider>
      <Row>
        {
          this.menusFilter(myMenus, keyword).map(item => {
            return <Col><MenuCard {...item} /></Col>
          })
        }
      </Row>

      {/* =================== 查看菜单权限 =======================  */}
      <Modal title='暂无权限查看菜单' visible={authApplyVisable} onCancel={() => this.setState({ authApplyVisable: false })} footer={null}>
        {
          moment().unix() > expireTime && aprInfo?.aprResult !== 'OnGoing' // 未申请权限
            ? <Row>
              <Col span={24} style={{ fontSize: 'larger', textAlign: 'center' }} >
                <Text type='warning'>
                  当前无权限查看菜单列表，是否申请？
                </Text>
              </Col>
              <Col span={24} style={{ textAlign: 'center', marginTop: '1em' }}>
                <Button type='primary' onClick={() => { this.applyMenuAuth() }}>申请查看菜单权限</Button></Col>
            </Row>
            : ''
        }
        {
          moment().unix() < aprInfo?.opTime + 1800 && aprInfo?.aprResult === 'OnGoing' // 申请提交30分钟内，无需再次申请
            ? <div>
              <Text type='warning' style={{ fontSize: 'larger', textAlign: 'center' }}>
                权限正在申请中，请申请通过后重新打开～
              </Text>
            </div>
            : ''
        }
        {
          moment().unix() > expireTime && aprInfo?.aprResult === 'OnGoing' && moment().unix() > aprInfo?.opTime + 1800 // 申请超过1天但未审批，允许重复发起
            ? <div style={{ margin: '1em', textAlign: 'center' }}>
              <Button type='primary' onClick={() => { this.applyMenuAuth() }} >重新发起申请</Button>
            </div>
            : ''
        }
      </Modal>
      {/* =================== 页面权限申请 =======================  */}
      <Modal title='申请页面权限' visible={menuVisible} onCancel={() => { this.setState({ menuVisible: false }) }} footer={null} width={'60em'}>
        <Row>
          <Col span={12}>
            <Text>请勾选您要申请的页面地址</Text> <br />
            <AuthTree treeData={menuList} selectURI={applyParams.uri} 
              onChange={(v) => { this.updateParams('uri', v.filter(item => { return item.indexOf('/') >= 0 })) }} />
          </Col>
          <Col span={10} push={1}>
            <Card>
              <Space direction='vertical' >
                <Text>已选择: {applyParams?.uri.length} 个页面 </Text> 
                <Text>请选择申请时长: </Text> 
                <Select style={{ width: '15em' }} value={applyParams.days} onChange={(v) => { this.updateParams('days', v) }} >
                  <Option value={1}>一天</Option>
                  <Option value={7}>一周</Option>
                  <Option value={30}>一个月</Option>
                  <Option value={90}>三个月</Option>
                  <Option value={180}>半年</Option>
                  <Option value={365}>一年</Option>
                </Select>
                <Text>请输入申请理由: </Text> 
                <Input placeholder='申请理由（必填）' style={{ width: '15em' }} value={applyParams.reason} onChange={(e) => { this.updateParams('reason', e.target.value) }} />
                <Button type='primary' disabled={applyParams?.uri.length === 0} onClick={() => { this.submitAuthApply(applyParams) }}>提交申请</Button>
              </Space>
            </Card>
          </Col>
        </Row>
      </Modal>
    </Card>
  }
}

const MenuCard = (props) => {
  const { name, uri, path, expireAt, color } = props
  return <Link to={uri}>
    <Card style={{ margin: '10px', cursor: 'pointer', minWidth: '170px', backgroundColor: color }}>
      <Tooltip title={path}>
        <Title level={4} style={{ color: genColorCode(uri) }} >{name}</Title>
      </Tooltip>
      <p>{uri}</p>
      <div style={tipStyle}>有效期:{ expireAt <= 0 ? '(已过期)' : timeFormater(expireAt)}</div>
    </Card>
  </Link>
}

const genColorCode = (str) => {
  var sum = 0
  for (var i = 0; i < str.length; i++) {
    sum += str.charCodeAt(i)
  }

  let colors = [ '#FFC300', '#FF5733', '#C70039', '#900C3F', '#4169E1', '#6dd8d8', '#409740', '#7FFF00', '#FF00FF', '#FF1493' ]
  return colors[sum % colors.length]
}

export default HomePage

const tipStyle = {
  color: '#d0883a',
  fontSize: 'smaller',
  position: 'absolute',
  right: '5px'
}
