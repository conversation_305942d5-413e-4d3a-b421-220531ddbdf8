import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const getTimeTable = genGetRequireTemplate('/guild/boss/guild_grade/debug/get_timetable', 'timetable')
const getTimeTableV2 = genGetRequireTemplate('/guild/boss/guild_grade/debug/get_timetable')
const querySuperGuildInfo = genGetRequireTemplate('/guild/boss/guild_grade/list')
const queryUidToAccountName = genUpdateTemplate('/guild/boss/guild_grade/query_cloud_user_account_name')
const addSuperGuildInfo = genUpdateTemplate('/guild/boss/guild_grade/add')
const udpateSuperGuildInfo = genUpdateTemplate('/guild/boss/guild_grade/update') 
const getReviewInfo = genGetRequireTemplate('/guild/boss/guild_grade/grade_assess/get_review_list')
const queryReviewHistory = genGetRequireTemplate('/guild/boss/guild_grade/grade_assess/query_review_history')
const gradeReview = genUpdateTemplate('/guild/boss/guild_grade/grade_assess/grade_review')
const udpateGrade = genUpdateTemplate('/guild/boss/guild_grade/grade_assess/update_grade')
const updateTag = genUpdateTemplate('/guild/boss/guild_grade/grade_assess/update_tag')
const batchRemoveSid = genUpdateTemplate('/guild/boss/guild_grade/batch_remove')
const setTimeTable = genUpdateTemplate('/guild/boss/guild_grade/debug/set_timetable')
const debugTrriger = genGetRequireTemplate('/guild/boss/guild_grade/debug/trigger')
const getSuperCrystalDetail = genGetRequireTemplate('/fts_hgame/boss/guild_settle_in/detail_super_crysta')  

export default {
  namespace: 'guildGradeAssess',
  state: {
    timetable: {}
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getSuperCrystalDetail,
    getTimeTable,
    getTimeTableV2,
    querySuperGuildInfo,
    addSuperGuildInfo,
    udpateSuperGuildInfo, 
    getReviewInfo,
    gradeReview,
    udpateGrade,
    updateTag,
    setTimeTable,
    debugTrriger,
    queryReviewHistory,
    batchRemoveSid,
    queryUidToAccountName
  }
}
