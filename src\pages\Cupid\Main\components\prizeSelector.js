import React, { Component } from 'react'
import { Table, Input, Modal, Typography, Tooltip } from 'antd'
import { SearchOutlined } from '@ant-design/icons'
import { ImagesList, SearchSelect } from '@/components/SimpleComponents'

const { Link } = Typography

class PrizeSelector extends Component {
  state = {
    keyword: ''
  }

  componentDidMount () {}

  prizeTypeOptions = { 0: '礼物', 1: '货币' }
  przeIDOptions = { 2: '交友', 34: '语音房', 36: '宝贝' }

  columns = [
    { title: 'ID', dataIndex: 'id' },
    { title: '名称', dataIndex: 'name' },
    { title: '图标', dataIndex: 'url', render: (v) => { return <ImagesList imgList={[v]} width={25} height={25} /> } },
    { title: '单价', dataIndex: 'price', render: (v) => { return parseInt(v).toLocaleString() } },
    { title: '类型', dataIndex: 'prizeType', render: (v) => { return this.prizeTypeOptions[v] } }, // 0-礼物, 1-货币
    { title: '所属业务', dataIndex: 'appId', render: (v) => { return this.przeIDOptions[v] } },
    { title: '道具类型', dataIndex: 'type' },
    { title: '操作',
      dataIndex: 'id',
      render: (v, r) => {
        return <Link onClick={() => this.onSelectPrize(r)}>选中</Link>
      } }
  ].map(item => {
    item.align = 'center'
    return item
  })

  // 列表过滤
  prizeListFilter = (before, appID, keyword) => {
    let after = before
    if (appID) {
      after = before.filter(item => {
        return item.appId === appID
      }
      )
    }
    if (keyword) {
      after = after.filter(item => {
        return `${item.id}${item.name}`.indexOf(keyword) >= 0
      })
    }
    return after
  }
  // 选项过滤
  optionsFilter = (before, appID) => {
    let after = before
    if (appID) {
      after = before.filter(item => {
        return item.appId === appID
      }
      )
    }
    return after.map(item => {
      return {
        label: `${item.name} (${item.id})`,
        value: item.id,
        key: item.id,
        raw: item
      }
    })
  }
  // 礼物选中
  onSelectPrize = (v) => {
    const { onComfirm } = this.props
    onComfirm(v)
  }

  render () {
    const { visible, onCancel, prizeList, appIDLimit, type, value, readOnly } = this.props
    const { keyword } = this.state
    const label = `选择${this.przeIDOptions[appIDLimit]}礼物`
    return (
      type === 'modal'
        ? <Modal title={label} visible={visible} footer={null} onCancel={onCancel} width={1100}>
          <Input placeholder='按ID或名字搜索' onChange={(e) => { this.setState({ keyword: e.target.value }) }} style={{ width: '20em', marginBottom: '1em' }} suffix={<SearchOutlined />} />
          <Table bordered columns={this.columns} dataSource={this.prizeListFilter(prizeList, appIDLimit, keyword)} size='small' rowKey={(record, index) => index} />
        </Modal>
        : <Tooltip title={label}>
          <SearchSelect style={{ width: '100%' }} readOnly={readOnly} value={value} options={this.optionsFilter(prizeList, appIDLimit)} onChange={(v, r) => this.onSelectPrize(r)} />
        </Tooltip>
    )
  }
}

export default PrizeSelector
