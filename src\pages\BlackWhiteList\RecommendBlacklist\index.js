import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card } from 'antd'
import RecommendCancelList from './component/cancel'
import RecommendExemptList from './component/exempt'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'

class RecommendBlacklist extends Component { // 默认页面组件，不需要修改
  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs id='cancel' type='card' defaultActiveKey='cancel' onTabClick={this.onTabClick}>
            <TabPane tab='取消推荐' key='cancel'>
              <RecommendCancelList />
            </TabPane>

            <TabPane tab='低质豁免' key='exempt'>
              <RecommendExemptList />
            </TabPane>

          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default RecommendBlacklist // 保证唯一
