import { getRetainLists, getWinnerLists } from './api'

export default {
  namespace: 'dsRetainMonitor',

  state: {
    list: []
  },

  reducers: {
    updateRetainList (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
        if (payload[i].retainRate1 > 0) {
          payload[i].retainRate1 = payload[i].retainRate1 * 100
          payload[i].retainRate1 = payload[i].retainRate1.toFixed(2) + '%'
        } else {
          payload[i].retainRate1 = 0
        }
        if (payload[i].retainRate2 > 0) {
          payload[i].retainRate2 = payload[i].retainRate2 * 100
          payload[i].retainRate2 = payload[i].retainRate2.toFixed(2) + '%'
        } else {
          payload[i].retainRate2 = 0
        }
        if (payload[i].retainRate3 > 0) {
          payload[i].retainRate3 = payload[i].retainRate3 * 100
          payload[i].retainRate3 = payload[i].retainRate3.toFixed(2) + '%'
        } else {
          payload[i].retainRate3 = 0
        }
        if (payload[i].retainRate4 > 0) {
          payload[i].retainRate4 = payload[i].retainRate4 * 100
          payload[i].retainRate4 = payload[i].retainRate4.toFixed(2) + '%'
        } else {
          payload[i].retainRate4 = 0
        }
        if (payload[i].retainRate5 > 0) {
          payload[i].retainRate5 = payload[i].retainRate5 * 100
          payload[i].retainRate5 = payload[i].retainRate5.toFixed(2) + '%'
        } else {
          payload[i].retainRate5 = 0
        }
        if (payload[i].retainRate6 > 0) {
          payload[i].retainRate6 = payload[i].retainRate6 * 100
          payload[i].retainRate6 = payload[i].retainRate6.toFixed(2) + '%'
        } else {
          payload[i].retainRate6 = 0
        }
      }

      return {
        ...state,
        list: payload
      }
    },
    updateWinnerList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
        payload[i].winRate = 0
        if (payload[i].betCount > 0) {
          payload[i].winRate = payload[i].winCount / payload[i].betCount * 100
          payload[i].winRate = payload[i].winRate.toFixed(2) + '%'
        }
      }

      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * getRetainList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getRetainLists, payload)
      yield put({
        type: 'updateRetainList',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getWinnerList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getWinnerLists, payload)
      yield put({
        type: 'updateWinnerList',
        payload: Array.isArray(ret) ? ret : []
      })
    }
  }
}
