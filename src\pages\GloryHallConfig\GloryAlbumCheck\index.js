import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import PopImage from '@/components/PopImage'
import { Table, Divider, Form, Card } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const namespace = 'photoCheck'
const getListUri = `${namespace}/getList`
// const TextArea = Input.TextArea
// const rmoveItem = `${namespace}/removeItem`
// const FormItem = Form.Item
// const RadioGroup = Radio.Group

@connect(({ photoCheck }) => ({
  model: photoCheck
}))
class photoCheck extends Component {
  // 列表结构
  columns = [
    { title: '', dataIndex: 'url1', align: 'center', render: text => <PopImage value={text} /> },
    { title: '', dataIndex: 'url2', align: 'center', render: text => <PopImage value={text} /> },
    { title: '', dataIndex: 'url3', align: 'center', render: text => <PopImage value={text} /> },
    { title: '', dataIndex: 'url4', align: 'center', render: text => <PopImage value={text} /> }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  defaultValue = { sealUrl: null }

  state = { visible: false, isUpdate: false, value: {} }

  // 获取列表
  componentDidMount () {
    this.getList()
  }

  getList () {
    const { dispatch } = this.props
    dispatch({
      type: getListUri
    })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // 显示弹窗
  showModal = (isUpdate, record) => e => {
    if (!isUpdate) {
      const { model: { list } } = this.props
      let max = 0
      list.forEach(i => { max = parseInt(i.activityId) > max ? parseInt(i.activityId) : max })
      max++
      record.activityId = max
    }

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.startTime = isUpdate ? moment.unix(v.startTime) : moment()
      v.endTime = isUpdate ? moment.unix(v.endTime) : moment()
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, value: record, isUpdate: isUpdate, title: isUpdate ? '更新印章配置' : '添加印章配置' })
  }

  // 关闭弹窗
  hidModal = () => {
    this.setState({ visible: false })
  }

  // 删除
  handleRemove = id => () => {
    const { dispatch } = this.props
    var data = { id: id }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    }).then(res => {
      this.getList()
    })
  }

  // 删除
  handleUpdate = record => () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/updateItem`,
      payload: record
    }).then(res => {
      this.getList()
    })
  }

  saveFormRef = formRef => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list } } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Divider />
            <Table dataSource={list} columns={this.columns} rowKey={(record, index) => index} pagination={this.defaultPageValue} />
          </Form>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default photoCheck
