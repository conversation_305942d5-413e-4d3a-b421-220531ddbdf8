import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import RoomMangeList from './component/roomMgrList'

const TabPane = Tabs.TabPane

class RoomMangeListComponent extends Component { // 默认页面组件，不需要修改
  state = { activeKey: '1' }
  onTabChange = key => {
    this.setState({ activeKey: key }) // render tab pane
  }

  render () {
    const { route } = this.props
    const { activeKey } = this.state
    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs onChange={this.onTabChange} type='card'>

          <TabPane tab='房管名单' key='1'>
            {activeKey === '1' ? <RoomMangeList {...this.state} /> : ''}
          </TabPane>

          {/* <TabPane tab='房管白名单' key='2'> */}
          {/*  {activeKey === '2' ? <RoomMgrWhitelist {...this.state} /> : ''} */}
          {/* </TabPane> */}

        </Tabs>
      </PageHeaderWrapper>
    )
  }
}

export default RoomMangeListComponent // 保证唯一
