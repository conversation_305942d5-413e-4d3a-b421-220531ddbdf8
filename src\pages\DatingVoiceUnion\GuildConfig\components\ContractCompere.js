import { connect } from 'dva'
import React, { Component } from 'react'
import { Button, Col, Divider, Input, message, Popconfirm, Row, Space, Table } from 'antd'
import { formatTimestamp } from '../../common'
import { SearchOutlined } from '@ant-design/icons'
import { formatOptions } from '@/utils/common'

const namespace = 'GuildConfigManage'
const defaultPageSize = 20

@connect(({ ContractCompere }) => ({
  model: ContractCompere
}))

class ContractCompere extends Component { // 默认页面组件，不需要修改
  constructor (props) {
    super(props)
    this.initState(props.sid)
    this.searchHandle()
  }

  initState = (sid) => {
    this.state = {
      sid: sid,
      remarkOptions: []
    }
  }

  // 处理查询事件
  searchHandle = (page, size, cond) => {
    const { dispatch } = this.props
    const { sid } = this.state

    let query = { sid: sid }
    let self = this
    dispatch({
      type: `${namespace}/pageContractCompereList`,
      payload: query,
      callback: (data) => {
        let remarkOptions = []
        let exists = {}
        let compereList = (data.list || [])
        compereList.forEach(item => {
          if (exists[item.remark]) {
            return
          }
          exists[item.remark] = true
          remarkOptions.push({ value: item.remark, label: item.remark })
        })
        self.setState({ compereList: data.list, remarkOptions: remarkOptions })
      }
    })
  }

  onQueryClick = (values) => {
    const { pagination } = this.state
    let size = pagination.pageSize || defaultPageSize
    this.searchHandle(1, size, values)
  }

  getShowColumns = () => {
    const { remarkOptions, sid } = this.state
    return [
      { title: 'UID', dataIndex: 'uid', align: 'left', width: 150 },
      { title: '交友签约信息',
        disabledSearch: true,
        width: 450,
        dataIndex: 'datingContract',
        align: 'left',
        render: (contract) => {
          if (!contract) {
            return '未签约'
          }
          let signTime = formatTimestamp(contract.signTime / 1000)
          let finishedTime = formatTimestamp(contract.finishTime / 1000)
          return <div><span style={{ color: 'red' }}>签约时间:</span>[{signTime} ~ {finishedTime}}] <span style={{ color: 'red' }}>分成比例:</span>{contract.weight}%</div>
        }
      },
      { title: '语音房签约信息',
        disabledSearch: true,
        width: 700,
        dataIndex: 'yyfContract',
        align: 'left',
        render: (contract) => {
          if (!contract) {
            return '未签约'
          }
          let signTime = formatTimestamp(contract.signTime / 1000)
          let finishedTime = formatTimestamp(contract.finishTime / 1000)
          let expand = contract.expand
          try {
            expand = JSON.stringify(JSON.parse(expand))
          } catch (e) {}
          return <div><span style={{ color: 'red' }}>签约时间:</span>[{signTime} ~ {finishedTime}}] <span style={{ color: 'red' }}>分成比例:</span>{contract.weight}% <span style={{ color: 'red' }}>家族ID:</span>{contract.familyId} <span style={{ color: 'red' }}>Expand:</span>{expand}</div>
        }
      },
      { title: '状态', dataIndex: 'remark', align: 'left', options: (remarkOptions || []) },
      {
        title: (<>操作 <Popconfirm title='确定要同步全部吗！！！' onConfirm={() => this.onContractSync(0, sid)}><a style={{ color: 'red' }}>同步全部</a></Popconfirm></>),
        disabledSearch: true,
        key: 'operation',
        align: 'left',
        render: (text, item) => {
          // 不能同步
          if (!item.canSync) {
            return ''
          }
          return (<span>
            <Divider type='vertical' /> {/* 分割线 */}
            <Popconfirm title='确定要同步吗！！！' onConfirm={() => this.onContractSync(item.uid, item)}><a style={{ color: 'red' }}>同步</a></Popconfirm>
          </span>)
        }
      }
    ]
  }

  renderColumns = (columns) => {
    columns = columns || this.getShowColumns()
    let getLocalFilter = (column) => {
      return (v, rec) => {
        return String(v).localeCompare(rec[column.dataIndex]) === 0
      }
    }
    let getOptionRender = (options) => {
      return (val, record) => formatOptions(val, options)
    }
    return columns.map(v => {
      if (v.disabledSearch) {
        return v
      }
      if (v.options) {
        v.options.forEach(opt => { opt.text = opt.label })
        v.filters = v.options
        v.onFilter = getLocalFilter(v)
        v.filterIcon = <SearchOutlined />
        v.filterMode = 'tree'
        v.filterSearch = true

        if (!v.render) {
          v.render = getOptionRender(v.options)
        }

        return v
      }
      v = Object.assign(this.getColumnSearchTextProps(v.dataIndex), v)
      return v
    })
  }

  // 搜索文本
  getColumnSearchTextProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={node => {
            this.searchInput = node
          }}
          placeholder={`Search ...`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => this.handleSearch(selectedKeys, confirm, dataIndex)}
          style={{ marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button size='small' onClick={() => this.handleReset(clearFilters)}>重置</Button>
          <Button type='primary' size='small' onClick={() => this.handleSearch(selectedKeys, confirm, dataIndex)}>确定</Button>
        </Space>
      </div>
    ),
    filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
        : ''
  })

  handleSearch = (selectedKeys, confirm, dataIndex, table) => {
    confirm()
    this.setState({
      searchText: '', searchedColumn: dataIndex
    })
  }

  handleReset = (clearFilters, table) => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  // 同步签约关系
  onContractSync = (uid, item) => {
    console.log('准备同步：', item)
    let self = this
    const { dispatch } = this.props
    const { sid } = this.state

    let uidList = []
    if (uid && uid > 0) {
      uidList.push(uid)
    }

    dispatch({
      type: `${namespace}/syncContract`,
      payload: { sid: sid, uidList: uidList },
      callback: (rsp) => {
        if (!rsp) {
          message.error('同步失败: 系统繁忙！')
          return
        }
        if (rsp && rsp.status === 0) {
          let successList = (rsp.data.successUIDList || [])
          let failedList = (rsp.data.failedList || [])

          if (uidList.length === 1) {
            if (failedList.length > 0) {
              message.error(<div>同步失败： {failedList[0].remark}</div>, 3)
            } else {
              message.info('同步成功！')
            }
          } else {
            let failedEltList = []
            failedList.forEach(item => {
              failedEltList.push(<div><span>{item.key + ': ' + item.remark}</span></div>)
            })
            if (failedList.length > 0) {
              message.info(<div>同步结果如下：<br /> 同步成功 {successList.length}<br />同步失败列表：<br />{failedEltList}</div>, 3)
            } else {
              message.info('同步成功！')
            }
          }
          self.searchHandle()
        } else {
          message.error('同步失败: ' + (rsp ? '系统繁忙' : rsp.msg))
        }
      }
    })
  }

  // 渲染函数
  render () {
    const { compereList } = this.state
    const columns = this.renderColumns(this.getShowColumns())

    return (
      <>
        <Row style={{ marginBottom: '1em' }}>
          <Col span={24}>
            <Table columns={columns}
              dataSource={compereList || []}
              size='small'
              showSorterTooltip={false}
              rowKey={record => record.uid}
            />
          </Col>
        </Row>
      </>
    )
  }
}

export default ContractCompere
