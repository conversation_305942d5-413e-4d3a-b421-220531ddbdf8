import React, { Component } from 'react'
import { Card, Table, Divider, Row, Col, Typography, message } from 'antd'
import { connect } from 'dva'
import { columns, PoolChangeDesc, countSURT, broadcastOptionsJY } from '../../DropMain/components/list_common'
import { timeFormater } from '@/utils/common'
import { BowWarTipDiv } from '../common'
const namespace = 'dropBoxWar'

const defaultChangeDataSource = { rta: 0, rtb: 0, sma: 0, smb: 0 }
const defaultPoolConfig = { arpUid: 0, aprPassport: '', aprTimestamp: 0, aprId: 0, changeInfo: defaultChangeDataSource }
const defaultAprInfo = { timestamp: 0, operator: 0, passport: '', opRemark: '(空)', aprUid: 0, aprRemark: '(空)', aprPassport: '' }

@connect(({ dropBoxWar }) => ({
  model: dropBoxWar
}))

class PoolListConfig extends Component {
  state = {
    list: [], // 道具池
    temporary: null,
    defaultPoolConfig: defaultPoolConfig,
    poolChangeDataSource: defaultChangeDataSource
  }

  componentDidMount () {
    this.getPoolConfig(20000)
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // 获取道具池配置
  getPoolConfig = (pid) => {
    this.callModel('getPoolConfigByID', {
      params: { id: pid },
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg, list } = ret
        if (status !== 0) {
          message.error('获取道具池配置失败,请稍后再试' + msg)
          return
        }
        this.parsePoolConfig(list, pid)
      }
    })
  }

  // 道具池解析
  parsePoolConfig = (poolConfig, pid) => {
    let list = []
    if (poolConfig !== undefined && poolConfig.content !== undefined) {
      try {
        list = JSON.parse(poolConfig.content)
        list.forEach((item, index, ary) => { item.limitSetting.poolID = pid })
      } catch (e) {
        message.error('json convert error ', e)
      }
    }
    this.setState({ list, poolConfig })
    this.updateChangeInfo(list, pid)
  }

  // 刷新实时计算数值
  updateChangeInfo = (list, pid) => {
    const result = countSURT(list, pid, 10)
    this.setState({
      poolChangeDataSource: { rta: result.rt, sma: result.sm }
    })
  }

  fixColumns = (before) => {
    let after = before.map(item => {
      if (item.dataIndex === 'broadCastType') {
        item.render = (v, r) => { return broadcastOptionsJY.map(i => i.label)[v] }
      }
      return item
    })
    return after
  }

  render () {
    const { list, poolConfig, poolChangeDataSource } = this.state
    const { timestamp, operator, aprInfo, changeInfo } = poolConfig || defaultPoolConfig
    const { passport, opRemark, aprUid, aprPassport, aprRemark, timestamp: aprTimestamp } = aprInfo || defaultAprInfo
    const { Text } = Typography

    return (
      <Card>
        <Row gutter={8}>

          <Col>
            <PoolChangeDesc rtb={changeInfo.rtb} smb={changeInfo.smb} rta={poolChangeDataSource.rta} sma={poolChangeDataSource.sma} isEdit={false} />
          </Col>
        </Row>

        <Row>
          <Col>
            <Row>提交：<Text type='secondary'> {`${passport}_(${timeFormater(timestamp)})`} <Divider type='vertical' /> </Text></Row>
            <Row>审批人：<Text type='secondary'> {aprUid === 0 ? '系统自动审批' : aprPassport}_({timeFormater(aprTimestamp)}) <Divider type='vertical' /></Text> </Row>
          </Col>
          <Col>
            <Row>申请理由: <Text type='secondary'> { opRemark || '(空)'} <Divider type='vertical' /></Text></Row>
            <Row>审批备注：<Text type='secondary'> {aprRemark || '(空)'}</Text></Row>
          </Col>
        </Row>

        <Row>
          <Text type='warning' hidden={operator !== 10101} >{`请注意: 该道具池最近触发过周星替换逻辑~ (${timeFormater(timestamp)})`}</Text>
          <BowWarTipDiv />
        </Row>
        <Divider />
        <Table key={list} columns={this.fixColumns(columns)} dataSource={list} pagination={false} size='small' rowKey='id' scroll={{ x: 'max-content' }} />
      </Card>
    )
  }
}

export default PoolListConfig
