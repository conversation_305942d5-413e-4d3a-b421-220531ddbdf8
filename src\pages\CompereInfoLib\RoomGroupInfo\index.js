import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Table, Modal, Form, Button, Upload, Popover, Input, InputNumber, DatePicker } from 'antd'
import { connect } from 'dva'
import { UploadOutlined } from '@ant-design/icons'
import { exportExcel } from 'xlsx-oc'

const namespace = 'roomGroup'
const listRoomGroupURL = `${namespace}/listRoomGroup`
const importRoomGroupURL = `${namespace}/importRoomGroup`
const removeRoomGroupURL = `${namespace}/removeRoomGroup`

// const Option = Select.Option

var moment = require('moment')
var dateFormat = 'YYYY-MM-DD'

// 业务类型
// const businessTypeAll = 0 // 全部
const businessTypeJY = 1 // 交友
// const businessTypePK = 2 // 约战
// const businessTypeBABY = 3 // 宝贝
// const businessTypeMap = { 0: '全部', 1: '交友', 2: '约战', 3: '宝贝' }

@connect(({ roomGroup }) => ({
  model: roomGroup
}))

class RoomGroup extends Component {
  constructor (props) {
    super(props)

    this.refreshRoomGroup()
  }

  columns = [
    { title: 'ID', dataIndex: 'idx', align: 'center', fixed: 'left' },
    // { title: '业务类型', dataIndex: 'business', align: 'center', fixed: 'left', render: (text, record) => (businessTypeMap[record.business]) },
    { title: 'UID', dataIndex: 'uid', align: 'center', fixed: 'left' },
    { title: 'YY号', dataIndex: 'yy', align: 'center' },
    { title: '昵称', dataIndex: 'nickname', align: 'center' },
    { title: '签约频道ID', dataIndex: 'sid', align: 'center' },
    { title: '签约频道短位', dataIndex: 'asid', align: 'center' },
    {
      title: '签约时间（开始-结束）',
      dataIndex: 'signTime',
      align: 'center',
      width: 200,
      render: (text, record) => {
        if (record.signStartTime === 0 || record.signEndTime === 0) {
          return ''
        }
        record.signTime = moment.unix(record.signStartTime).format(dateFormat) + '至' + moment.unix(record.signEndTime).format(dateFormat)
        return record.signTime
      }
    },
    { title: '创建厅数量', dataIndex: 'createRoomSum', align: 'center' },
    { title: '加入厅管主持总数', dataIndex: 'joinRoomCompereSum', align: 'center' },
    {
      title: '入库时间',
      dataIndex: 'inboundTime',
      align: 'center',
      render: (text, record) => {
        if (record.inboundTime === 0) {
          return ''
        }
        return moment.unix(record.inboundTime).format(dateFormat)
      }
    },
    { title: '操作人', dataIndex: 'importOptUser', align: 'center' },
    { title: '操作', key: 'operation', align: 'center', width: 80, render: (text, record) => (<span><Popover content={this.renderContent(record)} trigger='click'><a>出库</a></Popover></span>) }
  ]

  state = {
    visible: false
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  renderContent = (record) => {
    return (
      <div>
        <Input.TextArea onChange={this.onTextChange} row={4} placeholder={'选填，最多100字符'} />
        <Button onClick={this.removeRoomGroup(record)} style={{ marginLeft: 120, marginTop: 5 }} type='primary'>确定</Button>
      </div>
    )
  }

  onTextChange = e => {
    this.setState({ removeReason: e.target.value })
  }

  removeRoomGroup = (record) => () => {
    const { removeReason } = this.state
    this.props.dispatch({
      type: removeRoomGroupURL,
      payload: { id: record.id, uid: record.uid, business: record.business, removeReason: removeReason }
    })
  }

  refreshRoomGroup = () => {
    const { searchBusiness, searchUID, searchYY, searchNick, searchSID, searchASID, searchSignStartTime, searchSignEndTime, searchInboundTime } = this.state

    let searchInboundTimeTmp = ''
    if (searchInboundTime) {
      searchInboundTimeTmp = moment(searchInboundTime).format(dateFormat) + ' 00:00:00'
    }

    let signStartTimeTmp = 0
    let signEndTimeTmp = 0
    if (searchSignStartTime) {
      signStartTimeTmp = moment(searchSignStartTime).unix()
    }
    if (searchSignEndTime) {
      signEndTimeTmp = moment(searchSignEndTime).unix()
    }

    let data = { business: searchBusiness, uid: searchUID, yy: searchYY, nick: searchNick, sid: searchSID, asid: searchASID, signStartTime: signStartTimeTmp, signEndTime: signEndTimeTmp, inboundTime: searchInboundTimeTmp }
    console.log(data)
    this.props.dispatch({
      type: listRoomGroupURL,
      payload: data
    })
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      var value = selectedRows.map(item => item.Sid).join(',')
      this.setState({ removeKey: value })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name
    })
  }

  searchHandle = () => () => {
    this.refreshRoomGroup()
  }

  importHandle = () => () => {
    this.setState({ visible: true })
  }

  onExportHandle = () => () => {
    let headers = []
    let columns = this.columns
    const { exportKey } = this.state
    columns.forEach(function (item) {
      if (item.title !== '操作' && item.title !== '操作人') {
        headers.push({ k: item.dataIndex, v: item.title })
      }
    })
    let list = []
    exportKey.forEach(function (item) {
      // let business = businessTypeMap[item.business]
      let inboundTime = moment.unix(item.inboundTime).format('YYYY-MM-DD')
      // let one = { idx: item.idx, business: business, uid: item.uid, yy: item.yy, nickname: item.nickname, sid: item.sid, asid: item.asid, signTime: item.signTime, createRoomSum: item.createRoomSum, joinRoomCompereSum: item.joinRoomCompereSum, inboundTime: inboundTime }
      let one = { idx: item.idx, uid: item.uid, yy: item.yy, nickname: item.nickname, sid: item.sid, asid: item.asid, signTime: item.signTime, createRoomSum: item.createRoomSum, joinRoomCompereSum: item.joinRoomCompereSum, inboundTime: inboundTime }
      list.push(one)
    })
    exportExcel(headers, list, '厅管信息库.xlsx')
  }

  hiddenModal = () => {
    this.setState({ visible: false })
  }

  handleCancel = e => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.hiddenModal()
  }

  handleSubmit = e => {
    if (this.formRef) {
      this.formRef.submit()
    }
    this.hiddenModal()
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onFinish = values => {
    values.business = businessTypeJY
    console.log(values)
    const { dispatch } = this.props

    let fileURL = ''
    if (values && values.fileURL.file.response.urls[0]) {
      fileURL = values.fileURL.file.response.urls[0]
    }
    let data = { business: values.business, fileUrl: fileURL }

    dispatch({
      type: importRoomGroupURL,
      payload: data
    })

    this.hiddenModal()
    if (this.formRef) {
      this.formRef.resetFields()
    }
  }

  render () {
    const { visible } = this.state
    const { route, model: { list } } = this.props

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 2 },
        sm: { span: 15 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          {/* <span>业务</span>
          <Select style={{ marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchBusiness: v })}>
            <Option key={0} value={businessTypeAll}>全部</Option>
            <Option key={1} value={businessTypeJY}>交友</Option>
            <Option key={2} value={businessTypePK}>约战</Option>
            <Option key={3} value={businessTypeBABY}>宝贝</Option>
          </Select> */}
          <span style={{ marginLeft: 15 }}>UID</span>
          <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchUID: e })} style={{ width: 100, marginLeft: 3 }} />
          <span style={{ marginLeft: 15 }}>YY号</span>
          <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchYY: e })} style={{ width: 100, marginLeft: 3 }} />
          <span style={{ marginLeft: 15 }}>昵称</span>
          <Input style={{ marginRight: 15, width: 120, marginLeft: 3 }} placeholder='昵称' onChange={e => this.setState({ searchNick: e.target.value })} />
          <span style={{ marginLeft: 15 }}>签约频道ID</span>
          <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchSID: e })} style={{ width: 100, marginLeft: 3 }} />
          <span style={{ marginLeft: 15 }}>签约频道短位</span>
          <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID: e })} style={{ width: 100, marginLeft: 3 }} />
          <span style={{ marginLeft: 15 }}>签约时间</span>
          <DatePicker
            format='YYYY-MM-DD'
            placeholder='开始时间'
            onChange={(v) => this.setState({ searchSignStartTime: v })}
            style={{ marginLeft: 5 }}
          />
          <span style={{ marginLeft: 5 }}>~</span>
          <DatePicker
            format='YYYY-MM-DD'
            placeholder='结束时间'
            onChange={(v) => this.setState({ searchSignEndTime: v })}
            style={{ marginLeft: 5 }}
          />
          <div style={{ marginTop: 10 }} />
          <span>入库时间</span>
          <DatePicker onChange={(v) => this.setState({ searchInboundTime: v })} style={{ marginLeft: 3 }} />
          <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
          <Button style={{ marginLeft: 20 }} type='primary' onClick={this.importHandle()}>导入厅管</Button>
          <Button style={{ marginLeft: 20 }} type='primary' onClick={this.onExportHandle()}>导出名单</Button>
          <div>
            <div><font>注释：</font></div>
            <div><font>1、厅管信息库：指初始满足厅管入库规则的主持，组合成的信息库</font></div>
            <div><font>2、入库/出库规则：目前为运营手动操作，完成厅管入库/出库</font></div>
            <div><font>3、签约时间：指签约厅管，签订合约生效和终止时间（包含线上普通签约及与平台独家签约两部分合约）</font></div>
            <div><font>4、创建厅数量：指签约厅管UID下，创建的厅数量（统计厅管UID下TID创建个数），单位个</font></div>
            <div><font>5、加入厅管主持总数：指签约厅管UID下，加入厅管的交友签约主持总数，单位个</font></div>
            <div><font>6、表格排序：按照入库时间从最新到最久</font></div>
          </div>
          <Table rowSelection={this.rowSelection} rowKey={(record, index) => index} bordered dataSource={list} columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
        </Card>

        <Modal forceRender width={600} visible={visible} title='批量导入厅管' onCancel={this.handleCancel} onOk={this.handleSubmit}>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            {/* <Form.Item label='业务' name='business' rules={[{ required: true }]}>
              <Select style={{ marginLeft: 5 }} placeholder='请选择'>
                <Option value={businessTypeJY}>交友</Option>
                <Option value={businessTypePK}>约战</Option>
                <Option value={businessTypeBABY}>宝贝</Option>
              </Select>
            </Form.Item> */}
            <span>
              <Form.Item label='导入名单' name='fileURL' rules={[{ required: true }]}>
                <Upload name='file' action='https://fts.yy.com/fs/uploadfiles' data={file => ({ bucket: 'makefriends', files: file })} onChange={this.UpLoadOnChange} multiple={false}>
                  <Button type='primary'>
                    <UploadOutlined /> 上传
                  </Button>
                </Upload>
              </Form.Item>
              <Button type='primary' style={{ marginLeft: 135 }}>
                <a href='https://makefriends.bs2dl.yy.com/1622442131_b5358d77d7e4467edaabbb2ef008c68f.xlsx' onClick={this.downLoad}>下载模板</a>
              </Button>
            </span>
            <div style={{ marginLeft: 135 }}>
              <div><font>1、请下载模板，按照模板中字段要求，完成输入</font></div>
              <div><font>2、上传文件仅使用模板文件格式及字段，数量1个</font></div>
              <div><font>3、提交后，名单会自动导入厅管信息库</font></div>
            </div>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default RoomGroup
