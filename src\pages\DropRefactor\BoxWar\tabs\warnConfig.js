import React, { Component } from 'react'
import { Card, Divider, Modal, Form, Table, InputNumber, message } from 'antd'
import { connect } from 'dva'
import { getPoolNameByPoolID } from '../../globalConfig'

const namespace = 'dropBoxWar'

@connect(({ dropBoxWar }) => ({
  model: dropBoxWar
}))

class WarnConfig extends Component {
  state = {
    value: {},
    visible: false
  }

  componentDidMount () {
    setTimeout(() => {
      this.queryWarnList()
    }, 500)
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // 获取预警配置
  queryWarnList = () => {
    this.callModel('getWarnList')
  }

  // ==================================

  columns = [
    { title: '道具池ID', dataIndex: 'id' },
    { title: '道具池名称', dataIndex: 'id', align: 'center', render: v => { return getPoolNameByPoolID(v) } },
    { title: '每日支出阈值', dataIndex: 'dailyOut', align: 'center', render: v => v.toLocaleString() + ' YB' },
    { title: '大额礼物价值', dataIndex: 'bigPrize', align: 'center', render: v => v.toLocaleString() + ' YB' },
    { title: '操作', align: 'center', render: (text, record) => <a style={{ marginRight: 10 }} onClick={this.showModal(true, record)}>编辑</a> }
  ]

  showModal = (isUpdate, record) => () => {
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(record)
    }
    this.setState({ visible: true, isUpdate: isUpdate, title: isUpdate ? '更新规则' : '新建规则' })
  }

  // 隐藏弹窗，不需要修改
  hideModal = () => {
    this.setState({ visible: false })
  }

  // 提交更新
  onFinish = values => {
    this.callModel('updsertWarnConfig', {
      params: values,
      isDetailMode: true,
      isJsonMode: true,
      cbFunc: (ret) => {
        const { msg, status } = ret
        if (status !== 0) {
          message.error('更新失败,请稍后再试: ' + msg)
          return
        }
        message.success('更新成功～')
        this.formRef.resetFields()
        this.queryWarnList()
        this.setState({ visible: false })
      }
    })
  }

  handleSubmit = () => {
    this.formRef.submit()
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  listFilter = (before) => {
    const { poolNameOptions } = this.props.model
    const after = before.filter(item => {
      return poolNameOptions.some(entry => { return entry.value === item.id })
    })
    return after
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { warnList } } = this.props
    const { visible } = this.state

    const formItemLayout = {
      labelCol: {
        xs: { span: 8 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 16 },
        sm: { span: 16 }
      }
    }

    return (
      <Card>
        <div>
          <div>{'1. 每日支出 = 普通道具池支出 + 疯狂道具池支出 + 龙宫支出'}</div>
          <div>{'2. 触发条件 = 统计范围内支出 >= 预警阈值 '}</div>
        </div>
        <Divider />

        <Table rowKey={(record, index) => index} dataSource={this.listFilter(warnList)} columns={this.columns} pagination={false} />

        <Modal forceRender visible={visible} title='更新规则' onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} ref={form => { this.formRef = form }} {...formItemLayout}>
            <Form.Item name='id' label='道具池' hidden>
              <InputNumber />
            </Form.Item>
            <Form.Item label='每日支出阈值/YB' name='dailyOut' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>
            <Form.Item label='大额礼物价值/YB' name='bigPrize' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default WarnConfig
