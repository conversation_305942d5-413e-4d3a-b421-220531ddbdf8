import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists () {
  return request('/glory_hall_boss/get_props_class_config_list')
}

export function add (params) {
  return request(`/glory_hall_boss/set_props_class_config_info?${stringify(params)}`)
}

export function update (params) {
  return request(`/glory_hall_boss/set_props_class_config_info?${stringify(params)}`)
}

export function remove (params) {
  return request(`/glory_hall_boss/del_props_class_config_info?${stringify(params)}`)
}
