import request from '@/utils/request'
import { stringify } from 'qs'

// 公会信息列表
export function queryGuildInfoList (params) {
  return request(`/guild_manage/query_guild_info_list?${stringify(params)}`)
}

// 公会签约列表
export function queryGuildContractList (params) {
  return request(`/guild_manage/query_guild_contract_list?${stringify(params)}`)
}

// 新增签约信息
export function addContract (params) {
  return request(`/guild_manage/add_guild_info`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 修改签约信息
export function updateContract (params) {
  return request(`/guild_manage/update_guild_info`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 签约审批通过/打回
export function approveContract (params) {
  return request(`/guild_manage/approve_contract`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 导入公会信息
export function importGuildInfoManage (params) {
  return request(`/guild_manage/import_guild_info_manage`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 审核人一触发进入审核流程
export function approveOneStart (params) {
  return request(`/guild_manage/approve_one_start`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 获取登录用户的个人审批相关信息
export function getUserApproveInfo (params) {
  return request(`/guild_manage/get_user_approve_info?${stringify(params)}`)
}

// 获取文件名的私密链接
export function getPrivateFileToken (params) {
  return request(`/guild_manage/get_private_file_token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}
