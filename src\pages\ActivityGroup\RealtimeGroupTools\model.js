import { pageListActivity, pageListBusiness, pageListRuleData } from '../common'
import { newCommonReducers } from '@/utils/common'

// 分组规则示例
const commonRuleOptions = [
  {
    label: '区分天团男女&音视频主持',
    value: 'common_tt_sex_av_compere',
    groupOptions: '-1:未签约\n' +
      '0:-\n' + // 帽子主持(已废弃)
      '50010:交友女视频\n' +
      '50011:交友女音频\n' +
      '50012:交友男视频\n' +
      '50013:交友男音频\n' +
      '50014:交友天团',
    ruleProgram: '$[QUERY_SOURCE] == \'JY\' && $[JY_SIGNED] {\n' +
      '\t$[JY_SIGNED_COMPERE_ROLE] == \'SUPER_HAT\' {\n' +
      '\t\t{0} // 帽子主持\n' +
      '\t}\n' +
      '\t\n' +
      '\t$[JY_SIGNED_COMPERE_ROLE] IN (\'SUPER\', \'SUPER_PREPARE\') { // 超级主持，准超主\n' +
      '\t\t$[JY_SIGNED_COMPERE_GENDER] == \'FEMALE\' { // 女\n' +
      '\t\t\t$[JY_SIGNED_COMPERE_AV_TYPE] == \'VIDEO\' { // 视频\n' +
      '\t\t\t      {50010} // 交友女视频\n' +
      '\t\t         }\n' +
      '\t\t         {50011} // 交友女音频\n' +
      '\t\t}\n' +
      '\t\t$[JY_SIGNED_COMPERE_AV_TYPE] == \'VIDEO\' { // 视频\n' +
      '\t\t\t      {50012} // 交友男视频\n' +
      '\t\t}\n' +
      '\t\t{50013} // 交友男音频\n' +
      '\t}\n' +
      '\t{50014} // 其余的都划分到 交友天团\n' +
      '}\n' +
      '{-1}'
  },
  {
    label: '仅区分天团主持',
    value: 'common_only_tt_compere',
    groupOptions: '-1:未签约\n' +
      '0:-\n' + // 帽子主持(已废弃)
      '50001:交友主持\n' +
      '50014:交友天团',
    ruleProgram: '$[QUERY_SOURCE] == \'JY\' && $[JY_SIGNED]{\n' +
      '      $[JY_SIGNED_COMPERE_ROLE] == \'SUPER_HAT\'{ // 帽子主持，不分组\n' +
      '            {0}\n' +
      '      }\n' +
      '      $[JY_SIGNED_COMPERE_ROLE] in (\'SUPER\',\'SUPER_PREPARE\'){ // 交友主持分组\n' +
      '             {50001}\n' +
      '      }\n' +
      '\n' +
      '     {50014} // 其余划分到交友天团\n' +
      '}\n' +
      '{-1}'
  }
]

export default {
  namespace: 'RealtimeGroupTools',
  state: {
    dataList: [],
    businessList: [],
    ruleDataList: [],
    ruleOptions: [],
    defaultRuleOptions: commonRuleOptions
  },

  reducers: newCommonReducers(),

  // 数据变更
  effects: {
    * listAllBusiness ({ payload }, { call, put }) {
      const { data } = yield call(pageListBusiness, { pageNo: -1, pageSize: -1 })
      let businessList = data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
      let businessOptions = []
      let businessMap = {}
      businessList.forEach(item => {
        businessMap[item.id] = item
        businessOptions.push({ label: item.name, value: item.id })
      })
      yield put({
        type: 'updateState',
        payload: {
          businessList: businessList,
          businessOptions: businessOptions,
          businessMap: businessMap
        }
      })
    },

    * listAllRuleData ({ payload }, { call, put }) {
      const { data } = yield call(pageListRuleData, { pageNo: -1, pageSize: -1 })
      let ruleDataList = data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
      let ruleDataMap = {}
      let ruleDataOptions = []
      ruleDataList.forEach(item => {
        ruleDataMap[item.id] = item
        ruleDataOptions.push({ label: item.name, value: item.id + ':' + item.name, data: item })
      })
      yield put({
        type: 'updateState',
        payload: {
          ruleDataList: ruleDataList,
          ruleDataMap: ruleDataMap,
          ruleDataOptions: ruleDataOptions
        }
      })
    },

    * listAllRuleOptions ({ payload }, { call, put }) {
      const { data } = yield call(pageListActivity, { pageNo: -1, pageSize: -1 })
      let actList = data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
      let ruleOptions = [...commonRuleOptions]
      actList.forEach(act => {
        (act.roleList || []).forEach(role => {
          if (role.ruleProgram && role.ruleProgram.indexOf('ALIAS_ID') < 0) {
            let groupOptions = []
            let hadUnSigned
            (role.groupItems || []).forEach(group => {
              if (group.id === '-1') {
                hadUnSigned = true
              }
              groupOptions.push(group.id + ':' + group.name)
            })

            if (!hadUnSigned) {
              groupOptions = ['-1:未签约', ...groupOptions]
            }

            let option = {
              label: act.id + '(' + act.name + ')-' + role.name + '(' + role.id + ')',
              value: act.id + '_' + role.id,
              ruleProgram: role.ruleProgram,
              groupOptions: groupOptions.join('\n')
            }
            ruleOptions.push(option)
          }
        })
      })
      yield put({
        type: 'updateState',
        payload: {
          ruleOptions: ruleOptions
        }
      })
    }
  }
}
