import React, { Component } from 'react'
import { connect } from 'dva'
import { Button, Card, Input, InputNumber, message, Modal, Select, Table, Tooltip } from 'antd'

import {
  getFinishDesc,
  yuanToWan,
  wanToYuan,
  reviewMap,
  approvalMap,
  prizeRuleMap,
  prizeRuleList, prizeRuleSystem, prizeRuleDown, prizeRuleConfig, prizeRuleNone
} from './common'
import { deepClone } from '../../../../utils/common'
import { stringify } from 'qs'

const namespace = 'hatSuperCompereTask'

const Option = Select.Option
const { TextArea } = Input

@connect(({ hatSuperCompereTask }) => ({
  model: hatSuperCompereTask
}))

class RewardConfirm extends Component {
  columns = [
    { title: '任务时间', dataIndex: 'month', align: 'center', fixed: 'left' },
    { title: '短位ID', dataIndex: 'asid', align: 'center', fixed: 'left' },
    { title: '主持UID', dataIndex: 'uid', align: 'center', fixed: 'left' },
    { title: '主持YY号', dataIndex: 'yy', align: 'center', fixed: 'left' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: '公会礼物流水/元', dataIndex: 'guildSealWater', align: 'center', render: (text, record) => (record.guildSealWater.toLocaleString()) },
    { title: '主持盖章流水/元', dataIndex: 'compereSealWater', align: 'center', render: (text, record) => (record.compereSealWater.toLocaleString()) },

    {
      title: '基础任务',
      children: [
        {
          title: '任务完成情况',
          dataIndex: 'baseDetail',
          align: 'center',
          render: (text, record) => (getFinishDesc(record.baseDetail.guildSealWaterReach, record.baseDetail.targetAmountGuild, record.baseDetail.compereSealWaterReach, record.baseDetail.targetAmountSeal))
        },
        {
          title: '[系统结算]达标奖励/万元',
          dataIndex: 'baseSystemReward',
          align: 'center',
          render: (text, record) => (yuanToWan(record.baseDetail.systemReward) === 0 ? '-' : yuanToWan(record.baseDetail.systemReward))
        }
      ]
    },

    {
      title: '进阶任务',
      children: [
        {
          title: '任务完成情况',
          dataIndex: 'advancedDetail',
          align: 'center',
          render: (text, record) => (getFinishDesc(record.advancedDetail.guildSealWaterReach, record.advancedDetail.targetAmountGuild, record.advancedDetail.compereSealWaterReach, record.advancedDetail.targetAmountSeal))
        },
        {
          title: '[系统结算]达标奖励/万元',
          dataIndex: 'advancedSystemReward',
          align: 'center',
          render: (text, record) => (yuanToWan(record.advancedDetail.systemReward) === 0 ? '-' : yuanToWan(record.advancedDetail.systemReward))
        }
      ]
    },

    {
      title: '冲刺任务',
      children: [
        {
          title: '任务完成情况',
          dataIndex: 'sprintDetail',
          align: 'center',
          render: (text, record) => (getFinishDesc(record.sprintDetail.guildSealWaterReach, record.sprintDetail.targetAmountGuild, record.sprintDetail.compereSealWaterReach, record.sprintDetail.targetAmountSeal))
        },
        {
          title: '[系统结算]达标奖励/万元',
          dataIndex: 'sprintSystemReward',
          align: 'center',
          render: (text, record) => (yuanToWan(record.sprintDetail.systemReward) === 0 ? '-' : yuanToWan(record.sprintDetail.systemReward))
        }
      ]
    },

    { title: '发奖YY号', dataIndex: 'rewardYY', align: 'center', render: (text, record) => (record.rewardYY === 0 ? '' : record.rewardYY) },
    { title: '发奖账号昵称', dataIndex: 'rewardNick', align: 'center' },
    { title: '发奖规则', dataIndex: 'prizeRule', align: 'center', render: (text, record) => (prizeRuleMap[record.prizeRule]) },
    { title: '[调整后]达标奖励/万元', dataIndex: 'actReward', align: 'center', render: (text, record) => (record.prizeRule === prizeRuleNone && yuanToWan(record.actReward) === 0 ? '' : yuanToWan(record.actReward)) },
    { title: '运营备注', dataIndex: 'reviewRemark', align: 'center' },
    { title: '复核状态', dataIndex: 'reviewStatus', align: 'center', render: (text, record) => (reviewMap[record.reviewStatus]) },
    { title: '审批状态', dataIndex: 'approvalStatus', align: 'center', render: (text, record) => (approvalMap[record.approvalStatus]) },
    { title: '驳回原因', dataIndex: 'approvalRemark', align: 'center', render: (text, record) => (record.approvalRemark === '' ? '' : <span>{record.approvalRemark.split('|').map((item, index) => (<div>{item}</div>))}</span>) },
    { title: '操作人', dataIndex: 'optUid', align: 'center', render: (text, record) => (record.optUid === 0 ? '' : record.optUid) }
  ]

  columnsUpdate = [
    { title: '任务时间', dataIndex: 'month', align: 'center', fixed: 'left' },
    { title: '短位ID', dataIndex: 'asid', align: 'center', fixed: 'left' },
    { title: '主持UID', dataIndex: 'uid', align: 'center', fixed: 'left' },
    { title: '主持YY号', dataIndex: 'yy', align: 'center', fixed: 'left' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: '公会礼物流水/元', dataIndex: 'guildSealWater', align: 'center', render: (text, record) => (record.guildSealWater.toLocaleString()) },
    { title: '主持盖章流水/元', dataIndex: 'compereSealWater', align: 'center', render: (text, record) => (record.compereSealWater.toLocaleString()) },

    {
      title: '基础任务',
      children: [
        {
          title: '任务完成情况',
          dataIndex: 'baseDetail',
          align: 'center'
        },
        {
          title: '[系统结算]达标奖励/万元',
          dataIndex: 'baseSystemReward',
          align: 'center'
        }
      ]
    },

    {
      title: '进阶任务',
      children: [
        {
          title: '任务完成情况',
          dataIndex: 'advancedDetail',
          align: 'center'
        },
        {
          title: '[系统结算]达标奖励/万元',
          dataIndex: 'advancedSystemReward',
          align: 'center'
        }
      ]
    },

    {
      title: '冲刺任务',
      children: [
        {
          title: '任务完成情况',
          dataIndex: 'sprintDetail',
          align: 'center'
        },
        {
          title: '[系统结算]达标奖励/万元',
          dataIndex: 'sprintSystemReward',
          align: 'center'
        }
      ]
    },

    { title: '发奖YY号',
      dataIndex: 'rewardYY',
      align: 'center',
      render: (text, record) => (record.prizeRule !== prizeRuleNone && parseFloat(record.actReward) === 0 ? <div>无需发奖</div> : <Input
        style={{ width: 120 }}
        value={record.rewardYY}
        onChange={(e) => {
          let value = e.target.value
          if (value === '' || value === null) value = ''
          const match = /^[0-9]*$/.test(value)
          if (match) {
            this.handleRowChangeString(record, 'rewardYY')(e)
          }
        }}
      />)
    },
    { title: '发奖账号昵称', dataIndex: 'rewardNick', align: 'center', render: (text, record) => (record.prizeRule !== prizeRuleNone && parseFloat(record.actReward) === 0 ? '' : record.rewardNick) },
    { title: '发奖规则',
      dataIndex: 'prizeRule',
      align: 'center',
      render: (text, record) => (<Select allowClear
        placeholder='请选择'
        value={record.prizeRule}
        style={{ width: 200, marginLeft: 3 }}
        onChange={this.handleRowChange(record, 'prizeRule')}>
        {prizeRuleList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}
      </Select>)
    },
    { title: '[调整后]达标奖励/万元',
      dataIndex: 'actReward',
      align: 'center',
      render: (text, record) => {
        if (record.prizeRule === prizeRuleSystem) {
          return <div>{yuanToWan(record.systemReward)}</div>
        } else {
          return <Tooltip title='仅支持一位小数'>
            <InputNumber
              style={{ width: 120 }}
              min={record.prizeRule === prizeRuleDown ? 0 : yuanToWan(record.systemReward)}
              max={record.prizeRule === prizeRuleDown ? yuanToWan(record.systemReward) : 100}
              value={record.actReward}
              precision={1}
              onChange={(e) => {
                console.log(record.systemReward, e)
                this.handleRowChangeInt(record, 'actReward')(e)
              }}
            /></Tooltip>
        }
      }
    },
    { title: '运营备注',
      dataIndex: 'reviewReason',
      align: 'center',
      render: (text, record) => (<Input
        style={{ width: 150 }}
        value={record.reviewReason}
        onChange={(e) => {
          this.handleRowChangeString(record, 'reviewReason')(e)
        }}
      />)
    }
  ]

  state = {
    visible: false
  }

  defaultPageValue = {
    defaultPageSize: 10,
    pageSizeOptions: ['10', '20', '30', '50', '80', '100'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  componentDidMount () {
    this.loadData()
  }

  buildParam = () => {
    const { searchASID, searchYY, searchReviewStatus, searchApprovalStatus } = this.state

    let data = { }
    if (searchASID > 0) {
      data['asid'] = searchASID
    }
    if (searchYY > 0) {
      data['yy'] = searchYY
    }
    if (searchReviewStatus > 0) {
      data['reviewStatus'] = searchReviewStatus
    }
    if (searchApprovalStatus > 0) {
      data['approvalStatus'] = searchApprovalStatus
    }
    return data
  }

  loadData = () => {
    const { dispatch } = this.props

    let data = this.buildParam()
    dispatch({
      type: `${namespace}/listRewardConfirm`,
      payload: data
    })
  }

  searchHandle = () => () => {
    this.loadData()
  }

  exportURL = () => {
    let data = this.buildParam()
    data['export'] = 1
    return `/fts_hgame/hat_super_task/boss/list_reward_confirm?${stringify(data)}`
  }

  // 编辑复核信息
  handleRowChangeString = (record, field) => e => {
    let value = e.target.value
    let { rewardConfirmInfoList } = this.state

    console.log(value)

    rewardConfirmInfoList[record.idx][field] = value
    record[field] = value

    this.forceUpdate() // 强制刷新
  }

  handleRowChangeInt = (record, field) => e => {
    let value = e
    let { rewardConfirmInfoList } = this.state

    console.log(value)

    rewardConfirmInfoList[record.idx][field] = value
    record[field] = value

    this.forceUpdate() // 强制刷新
  }

  handleRowChange = (record, field) => value => {
    let { rewardConfirmInfoList } = this.state

    console.log(field, value)

    rewardConfirmInfoList[record.idx][field] = value
    record[field] = value

    if (field === 'prizeRule') {
      if (value === prizeRuleSystem) {
        rewardConfirmInfoList[record.idx]['actReward'] = yuanToWan(record.systemReward)
        record['actReward'] = yuanToWan(record.systemReward)
      } else {
        rewardConfirmInfoList[record.idx]['actReward'] = ''
        record['actReward'] = ''
      }
    }

    this.forceUpdate() // 强制刷新
  }

  inputReviewRemarkHandle = () => e => {
    let value = e.target.value
    this.setState({ inputReviewRemark: value })
  }

  hiddenModal = () => {
    this.setState({ visible: false, inputReviewRemark: '', rewardConfirmInfoList: [] })
    this.loadData()
  }

  handleCancel = e => {
    this.hiddenModal()
  }

  handleSubmit = e => {
    const { inputReviewRemark, rewardConfirmInfoList } = this.state

    if (rewardConfirmInfoList.length === 0) {
      message.warn('复核数据有误')
      return
    }

    console.log(rewardConfirmInfoList)

    let data = { remark: inputReviewRemark, infos: [] }
    for (let i = 0; i < rewardConfirmInfoList.length; i++) {
      let v = rewardConfirmInfoList[i]
      console.log(v, parseFloat(v.actReward), parseFloat(yuanToWan(v.systemReward)))

      if (v.prizeRule === prizeRuleSystem && parseFloat(v.actReward) !== parseFloat(yuanToWan(v.systemReward))) {
        message.warn('按系统结算发奖, 金额不一致, uid: ' + v.uid)
        return
      }

      if (v.prizeRule === prizeRuleDown && parseFloat(v.actReward) > parseFloat(yuanToWan(v.systemReward))) {
        message.warn('下调奖励-固定值, 调整金额比系统发奖金额大, uid: ' + v.uid)
        return
      }

      if (v.prizeRule === prizeRuleConfig && parseFloat(v.actReward) < parseFloat(yuanToWan(v.systemReward))) {
        message.warn('按配置金额, 调整金额比系统发奖金额小, uid: ' + v.uid)
        return
      }

      if (v.actReward > 0 && (v.rewardYY === '' || v.rewardYY === 0)) {
        const msg = '发奖yy空, uid: ' + v.uid
        message.warn(msg)
        return
      }

      if (v.prizeRule !== prizeRuleSystem && v.prizeRule !== prizeRuleDown && v.prizeRule !== prizeRuleConfig) {
        const msg = '发奖规则空, uid: ' + v.uid
        message.warn(msg)
        return
      }

      let one = {
        month: v.month.replace('-', ''),
        uid: v.uid,
        prizeRule: v.prizeRule,
        rewardYY: Number(v.rewardYY),
        actReward: wanToYuan(parseFloat(v.actReward)),
        remark: v.reviewReason
      }
      data.infos.push(one)
    }

    console.log(data)
    this.props.dispatch({
      type: `${namespace}/approvalRewardConfirm`,
      payload: data
    })

    this.hiddenModal()
  }

  showModal = () => () => {
    let callbackFn = (rewardConfirmInfo) => {
      let data = Array.isArray(rewardConfirmInfo.list) ? deepClone(rewardConfirmInfo.list) : []
      let infos = []
      for (let i = 0; i < data.length; i++) {
        let v = data[i]
        let one = {
          idx: i,
          month: v.month,
          asid: v.asid,
          uid: v.uid,
          yy: v.yy,
          nick: v.nick,
          guildSealWater: v.guildSealWater,
          compereSealWater: v.compereSealWater,
          baseDetail: getFinishDesc(v.baseDetail.guildSealWaterReach, v.baseDetail.targetAmountGuild, v.baseDetail.compereSealWaterReach, v.baseDetail.targetAmountSeal),
          baseSystemReward: yuanToWan(v.baseDetail.systemReward),
          advancedDetail: getFinishDesc(v.advancedDetail.guildSealWaterReach, v.advancedDetail.targetAmountGuild, v.advancedDetail.compereSealWaterReach, v.advancedDetail.targetAmountSeal),
          advancedSystemReward: yuanToWan(v.advancedDetail.systemReward),
          sprintDetail: getFinishDesc(v.sprintDetail.guildSealWaterReach, v.sprintDetail.targetAmountGuild, v.sprintDetail.compereSealWaterReach, v.sprintDetail.targetAmountSeal),
          sprintSystemReward: yuanToWan(v.sprintDetail.systemReward),
          systemReward: v.systemReward,
          rewardYY: v.rewardYY > 0 ? v.rewardYY : '',
          rewardNick: v.rewardNick,
          prizeRule: '',
          actReward: '',
          reviewReason: ''
        }

        infos.push(one)
      }
      console.log(infos)
      this.setState({ visible: true, rewardConfirmInfoList: infos })
    }

    this.props.dispatch({
      type: `${namespace}/approvalListRewardConfirm`,
      payload: { callback: callbackFn }
    })
  }

  setAllSystemRewardHandler = () => () => {
    let { rewardConfirmInfoList } = this.state
    const { model: { rewardConfirmInfo } } = this.props

    for (let i = 0; i < rewardConfirmInfoList.length; i++) {
      rewardConfirmInfoList[i]['prizeRule'] = prizeRuleSystem
      rewardConfirmInfo.list[i]['prizeRule'] = prizeRuleSystem

      rewardConfirmInfoList[i]['actReward'] = yuanToWan(rewardConfirmInfoList[i].systemReward)
      rewardConfirmInfo.list[i]['actReward'] = yuanToWan(rewardConfirmInfoList[i].systemReward)
    }

    this.forceUpdate() // 强制刷新
  }

  flushRewardNickHandler = () => () => {
    let callback = (data) => {
      // data yy-nick
      const { rewardConfirmInfoList } = this.state
      console.log(rewardConfirmInfoList)
      for (let i = 0; i < rewardConfirmInfoList.length; i++) {
        rewardConfirmInfoList[i].rewardNick = data[rewardConfirmInfoList[i].rewardYY]
      }
      this.setState({ rewardConfirmInfoList: rewardConfirmInfoList })
      this.forceUpdate() // 强制刷新
    }

    const { rewardConfirmInfoList } = this.state
    if (rewardConfirmInfoList.length === 0) return

    let yyList = []
    for (let i = 0; i < rewardConfirmInfoList.length; i++) {
      yyList.push(parseInt(rewardConfirmInfoList[i].rewardYY))
    }

    this.props.dispatch({
      type: `${namespace}/flushRewardConfirm`,
      payload: { yyList: yyList, callback: callback }
    })
  }

  render () {
    const { visible, inputReviewRemark, rewardConfirmInfoList } = this.state
    const { model: { rewardConfirmInfo, tableLoadingRewardConfirm } } = this.props

    let calTotalReward = 0
    if (Array.isArray(rewardConfirmInfoList)) {
      for (let i = 0; i < rewardConfirmInfoList.length; i++) {
        if (!isNaN(parseFloat(rewardConfirmInfoList[i].actReward))) calTotalReward += parseFloat(rewardConfirmInfoList[i].actReward)
      }
    }

    console.log(rewardConfirmInfoList)
    return (
      <Card>
        <span>短位ID</span>
        <InputNumber placeholder='请输入短位ID' onChange={e => this.setState({ searchASID: e })} style={{ width: 120, marginLeft: 3 }} />

        <span style={{ marginLeft: 15 }}>主持YY</span>
        <InputNumber placeholder='请输入主持yy' onChange={e => this.setState({ searchYY: e })} style={{ width: 150, marginLeft: 3 }} />

        {/* <span style={{ marginLeft: 15 }}>复核状态</span> */}
        {/* <Select allowClear placeholder='请选择' style={{ width: 100, marginLeft: 3 }} onChange={(v) => this.setState({ searchReviewStatus: v })}> */}
        {/*  {reviewStatusList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))} */}
        {/* </Select> */}

        {/* <span style={{ marginLeft: 15 }}>审批状态</span> */}
        {/* <Select allowClear placeholder='请选择' style={{ width: 130, marginLeft: 3 }} onChange={(v) => this.setState({ searchApprovalStatus: v })}> */}
        {/*  {approvalStatusList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))} */}
        {/* </Select> */}

        <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
        <Button style={{ marginLeft: 20 }} type='primary'><a href={this.exportURL()} target='_blank'>导出</a></Button>

        <div style={{ marginTop: 5 }}><font color='red'>运营复核备注:</font></div>
        <div>{rewardConfirmInfo.remark}</div>
        <Button style={{ marginTop: 5 }} type='primary' onClick={this.showModal()}>复核</Button>

        <Table bordered style={{ marginTop: 10 }} rowKey='idx' pagination={this.defaultPageValue} columns={this.columns} dataSource={rewardConfirmInfo.list} scroll={{ x: 'max-content' }} />

        <Modal keyboard={false} destroyOnClose forceRender width={1400} visible={visible} title='发奖复核' onCancel={this.handleCancel} onOk={this.handleSubmit}>
          <div><h1><font color='red'>{rewardConfirmInfo.title}</font></h1></div>
          <div>1. 整体情况: 授权帽子超级主持{rewardConfirmInfo.countCompere}人, 获得任务主持 {rewardConfirmInfo.hadCompere} 人，合计盖章流水{yuanToWan(rewardConfirmInfo.compereSealWater)}万元，其中{rewardConfirmInfo.reachCompere}人达标任务</div>
          <div>2. 发奖概况: 运营调整后, 合计发放达标奖励<font color='red'>{calTotalReward.toFixed(1)}万元</font></div>
          <div style={{ marginTop: 10 }} />
          <font color='red'>运营复核备注:</font>
          <TextArea placeholder='选填' autoSize={{ minRows: 2, maxRows: 4 }} style={{ height: 50, width: 500 }} value={inputReviewRemark} onChange={this.inputReviewRemarkHandle()} />

          <Button style={{ marginTop: 5 }} type='primary' onClick={this.flushRewardNickHandler()}>刷新发奖YY昵称</Button>
          {/* <Button style={{ marginTop: 5, marginLeft: 10 }} type='primary' onClick={this.setAllSystemRewardHandler()}>测试[一键设全部记录为按系统结算]</Button> */}
          <Table loading={tableLoadingRewardConfirm} size='small' rowKey='idx' bordered dataSource={rewardConfirmInfoList} columns={this.columnsUpdate} pagination={false} scroll={{ x: 'max-content' }} />
        </Modal>

      </Card>
    )
  }
}

export default RewardConfirm
