import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Card, Tabs, Popconfirm, TimePicker, InputNumber, Input, Modal } from 'antd'
import { Form } from '@ant-design/compatible'
import { connect } from 'dva'
import moment from 'moment'

const FormItem = Form.Item
const namespace = 'whiteIdsAdmin'
const TabPane = Tabs.TabPane
const format = 'HH:mm'

@connect(({ whiteIdsAdmin }) => ({
  model: whiteIdsAdmin
}))
class WhiteIdsAdmin extends Component {
  onChange = e => checked => {
    // const { dispatch } = this.props
    console.log('check:', checked)
    console.log('e:', e)
    e.open = checked ? 1 : 0
    // e.id = e.id
    /* var url = `${namespace}/updateItem`
    dispatch({
      type: url,
      payload: e
    }) */
  }
  // 需要修改
  columnsCompere = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: 'uid', dataIndex: 'uid', align: 'center' },
    { title: 'yy', dataIndex: 'yy_id', align: 'center' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: 'Asid', dataIndex: 'asid', align: 'center' },
    { title: 'Sid', dataIndex: 'sid', align: 'center' },
    { title: '超级类型',
      dataIndex: 'super_type',
      align: 'center',
      render: (text, record) => (
        text === 0 ? '否' : text === 1 ? '是' : '未知'
      )
    },
    { title: '推荐开始时间',
      dataIndex: 'beg_time',
      align: 'center',
      width: 80,
      render: (text, record) => <TimePicker key={Math.random()} onChange={this.handleChange(record, 'beg_time', '1')} defaultValue={moment(record.beg_time, format)} format={format} />
    },
    { title: '推荐结束时间',
      dataIndex: 'end_time',
      align: 'center',
      width: 80,
      render: (text, record) => <TimePicker key={Math.random()} onChange={this.handleChange(record, 'end_time', '1')} defaultValue={moment(record.end_time, format)} format={format} />
    },
    { title: '是否直播',
      dataIndex: 'live_status',
      align: 'center',
      render: (text, record) => (
        text === 0 ? '否' : text === 1 ? '是' : '未知'
      )
    },
    { title: '是否推荐',
      dataIndex: 'recommend_status',
      align: 'center',
      render: (text, record) => (
        text === 0 ? '否' : text === 1 ? '是' : '未知'
      )
    },
    { title: '权重',
      dataIndex: 'weight',
      align: 'center',
      width: 30,
      render: (text, record) => <InputNumber key={Math.random()} min={0} defaultValue={text} onChange={this.handleChange(record, 'weight', '1')} />
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => (
        <span>
          <Popconfirm title='Sure to delete?' onConfirm={this.handleDel(record.uid, '1')}>
            <a href=''>删除</a>
          </Popconfirm>
        </span>
      )
    }
  ]

  columnsWhiteIds = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: 'Asid', dataIndex: 'asid', align: 'center' },
    { title: 'Sid', dataIndex: 'sid', align: 'center' },
    { title: 'Ssid', dataIndex: 'ssid', align: 'center' },
    { title: '推荐开始时间',
      dataIndex: 'beg_time',
      align: 'center',
      width: 80,
      render: (text, record) => <TimePicker key={Math.random()} onChange={this.handleChange(record, 'beg_time', '2')} defaultValue={moment(record.beg_time, format)} format={format} />
    },
    { title: '推荐结束时间',
      dataIndex: 'end_time',
      align: 'center',
      width: 80,
      render: (text, record) => <TimePicker key={Math.random()} onChange={this.handleChange(record, 'end_time', '2')} defaultValue={moment(record.end_time, format)} format={format} />
    },
    { title: '权重',
      dataIndex: 'weight',
      align: 'center',
      width: 80,
      render: (text, record) => <InputNumber key={Math.random()} min={0} defaultValue={text} onChange={this.handleChange(record, 'weight', '2')} />
    },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => (
        <span>
          <Popconfirm title='Sure to delete?' onConfirm={this.handleDel(record.ssid, '2')}>
            <a href=''>删除</a>
          </Popconfirm>
        </span>
      )
    }
  ]

  columnsBlackIds = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: 'Asid', dataIndex: 'asid', align: 'center' },
    { title: 'Sid', dataIndex: 'sid', align: 'center' },
    { title: 'Ssid', dataIndex: 'ssid', align: 'center' },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => (
        <span>
          <Popconfirm title='Sure to delete?' onConfirm={this.handleDel(record.ssid, '3')}>
            <a href=''>删除</a>
          </Popconfirm>
        </span>
      )
    }
  ]

  defaultPageValue = { defaultPageSize: 100 } // 分页设置，可以不改
  defaultValue = { index: 0, uid: 0, beg_time: '00:00', end_time: '23:59' }

  state = { visible: false, active: '1', type: '1', value: {}, tabType: '1' }

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record, tabType) => e => {
    console.log('showModal:', tabType)
    this.setState({ tabType: tabType })
    if (isUpdate !== this.state.isUpdate) this.formRef.props.form.resetFields()
    this.setState({ visible: true, value: record, isUpdate: isUpdate, title: isUpdate ? '更新' : '新增', tabType: tabType })
  }

  // 隐藏弹窗，不需要修改
  hidModal = () => {
    this.setState({ visible: false })
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      var ssids = selectedRows.map(item => item.ssid).join(',')
      var uids = selectedRows.map(item => item.uid).join(',')
      this.setState({ removeKey: ssids, removeKey2: uids })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name
    })
  }

  // 删除，不需要修改
  handleDel = (id, type) => e => {
    console.log('handleDel:', id, type)
    console.log('handleDel:', e)
    const { dispatch } = this.props
    const data = { tabType: type, ssids: id, uids: id }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // 删除，不需要修改
  handleBatchDel = (type) => e => {
    console.log('handleDel:', type)
    console.log('handleDel:', e)
    const { dispatch } = this.props
    const data = { tabType: type, ssids: this.state.removeKey, uids: this.state.removeKey2 }
    console.log('keys1:', this.state.removeKey)
    console.log('keys2:', this.state.removeKey2)
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // 编辑信息
  handleChange = (record, field, type) => value => {
    console.log(record, value, field, type)
    var { dispatch } = this.props
    if (field === 'beg_time' || field === 'end_time') {
      console.log(value.format(format).toString())
      record[field] = value.format(format).toString()
    } else {
      console.log(value)
      record[field] = value
    }
    record.tabType = type

    dispatch({
      type: `${namespace}/updateItem`,
      payload: record
    })
    // this.forceUpdate() // 强制刷新
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = { tabType: this.state.type }
    console.log('componentDidMount:', data)
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  // 添加 与 编辑
  handleSubmit = () => e => {
    console.log('handleSubmit:', this.state.tabType)
    const form = this.formRef.props.form
    const { dispatch } = this.props
    form.validateFields((err, values) => {
      if (!err) {
        // transfer Moment object to timestamp !!!
        var url = this.state.isUpdate ? `${namespace}/updateItem` : `${namespace}/addItem`
        values.tabType = this.state.tabType
        // pvalues.tabType = tabType
        dispatch({
          type: url,
          payload: values
        })
        form.resetFields()
        this.setState({ visible: false })
      }
    })
  }
  onTabChange = type => {
    console.log(type)
    this.setState({ editable: false })
    this.setState({ key: type })
    const { dispatch } = this.props
    var data = { tabType: type }
    console.log('onchage:', data)
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // 实际的页面信息
  render () {
    const { route, model: { list } } = this.props // 基本不需要修改
    console.log('render props:', this.props)
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='1' onTabClick={this.onTabChange}>
            <TabPane tab='推荐主持' key='1'>
              <Form>
                {/* <Search onSearch={value => this.handleSearch(value, 1)} enterButton placeholder='search by asid' style={{ width: 250 }} />  搜索按钮 */}
                <Divider /> {/* 分割线 */}
                { <Button type='primary' htmlType='submit' onClick={this.handleBatchDel('1')}>删除</Button> }
                <Divider type='vertical' /> {/* 分割线 */}
                { <Button type='primary' onClick={this.showModal(false, this.defaultValue, '1')}>添加</Button> }
                <Table rowSelection={this.rowSelection} dataSource={list} columns={this.columnsCompere} pagination={this.defaultPageValue} rowKey={(record, index) => index} /> {/* 显示的列表 */}
                <ItemInfo wrappedComponentRef={this.saveFormRef} {...this.state} onCancel={this.hidModal} onSubmit={this.handleSubmit()} />
              </Form>
            </TabPane>
            <TabPane tab='公会白名单' key='2'>
              <Form>
                {/* <Search onSearch={value => this.handleSearch(value, 2)} enterButton placeholder='search by asid' style={{ width: 250 }} />  搜索按钮 */}
                <Divider /> {/* 分割线 */}
                { <Button type='primary' htmlType='submit' onClick={this.handleBatchDel('2')}>删除</Button> }
                <Divider type='vertical' /> {/* 分割线 */}
                { <Button type='primary' onClick={this.showModal(false, this.defaultValue, '2')}>添加</Button> }
                <Divider type='vertical' /> {/* 分割线 */}
                {/* this.state.editable ? <Button onClick={e => { this.setState({ editable: false }) }} type='primary' >查看</Button> : <Button onClick={() => this.setState({ editable: true })} type='primary'>编辑</Button> */}
                <Table rowSelection={this.rowSelection} dataSource={list} columns={this.columnsWhiteIds} pagination={this.defaultPageValue} rowKey={(record, index) => index} /> {/* 显示的列表 */}
                <ItemInfo wrappedComponentRef={this.saveFormRef} {...this.state} onCancel={this.hidModal} onSubmit={this.handleSubmit()} />
              </Form>
            </TabPane>
            <TabPane tab='公会黑名单' key='3'>
              <Form>
                {/* <Search onSearch={value => this.handleSearch(value, 3)} enterButton placeholder='search by asid' style={{ width: 250 }} />  搜索按钮 */}
                <Divider /> {/* 分割线 */}
                { <Button type='primary' htmlType='submit' onClick={this.handleBatchDel('3')}>删除</Button> }
                <Divider type='vertical' /> {/* 分割线 */}
                { <Button type='primary' onClick={this.showModal(false, this.defaultValue, '3')}>添加</Button> }
                <Table rowSelection={this.rowSelection} dataSource={list} columns={this.columnsBlackIds} pagination={this.defaultPageValue} rowKey={(record, index) => index} /> {/* 显示的列表 */}
                <ItemInfo wrappedComponentRef={this.saveFormRef} {...this.state} onCancel={this.hidModal} onSubmit={this.handleSubmit()} />
              </Form>
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

@connect(({ whiteIdsAdmin }) => ({
  model: whiteIdsAdmin
}))
@Form.create()
class ItemInfo extends Component {
  render () {
    const { value, visible, title, onCancel, onSubmit, form, tabType } = this.props
    const { getFieldDecorator } = form
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    if (tabType === '1') {
      return (
        <Modal visible={visible} title={title} onCancel={onCancel} onOk={onSubmit}>
          <Form >
            <FormItem {...formItemLayout} label='uid'>
              {getFieldDecorator('uid', {
                initialValue: value.uid,
                rules: [{ required: false, message: 'uid can not be null ' }]
              })(<Input />)}
            </FormItem>
            <FormItem {...formItemLayout} label='beg_time'>
              {getFieldDecorator('beg_time', {
                initialValue: value.beg_time,
                rules: [{ required: false, message: 'beg_time can not be null ' }]
              })(<Input />)}
            </FormItem>
            <FormItem {...formItemLayout} label='end_time'>
              {getFieldDecorator('end_time', {
                initialValue: value.end_time,
                rules: [{ required: false, message: 'end_time can not be null ' }]
              })(<Input />)}
            </FormItem>
            <FormItem {...formItemLayout} label='weight'>
              {getFieldDecorator('weight', {
                initialValue: value.weight,
                rules: [{ required: false, message: 'weight can not be null ' }]
              })(<Input />)}
            </FormItem>
          </Form>
        </Modal>
      )
    } else if (tabType === '2') {
      return (
        <Modal visible={visible} title={title} onCancel={onCancel} onOk={onSubmit}>
          <Form >
            <FormItem {...formItemLayout} label='sid'>
              {getFieldDecorator('sid', {
                initialValue: value.sid,
                rules: [{ required: false, message: 'sid can not be null ' }]
              })(<Input />)}
            </FormItem>
            <FormItem {...formItemLayout} label='ssid'>
              {getFieldDecorator('ssid', {
                initialValue: value.ssid,
                rules: [{ required: false, message: 'ssid can not be null ' }]
              })(<Input />)}
            </FormItem>
            <FormItem {...formItemLayout} label='beg_time'>
              {getFieldDecorator('beg_time', {
                initialValue: value.beg_time,
                rules: [{ required: false, message: 'beg_time can not be null ' }]
              })(<Input />)}
            </FormItem>
            <FormItem {...formItemLayout} label='end_time'>
              {getFieldDecorator('end_time', {
                initialValue: value.end_time,
                rules: [{ required: false, message: 'end_time can not be null ' }]
              })(<Input />)}
            </FormItem>
            <FormItem {...formItemLayout} label='weight'>
              {getFieldDecorator('weight', {
                initialValue: value.weight,
                rules: [{ required: false, message: 'index can not be null ' }]
              })(<Input />)}
            </FormItem>
          </Form>
        </Modal>
      )
    } else if (tabType === '3') {
      return (
        <Modal visible={visible} title={title} onCancel={onCancel} onOk={onSubmit}>
          <Form >
            <FormItem {...formItemLayout} label='sid'>
              {getFieldDecorator('sid', {
                initialValue: value.sid,
                rules: [{ required: false, message: 'sid can not be null ' }]
              })(<Input />)}
            </FormItem>
            <FormItem {...formItemLayout} label='ssid'>
              {getFieldDecorator('ssid', {
                initialValue: value.ssid,
                rules: [{ required: false, message: 'ssid can not be null ' }]
              })(<Input />)}
            </FormItem>
          </Form>
        </Modal>
      )
    }
  }
}

export default WhiteIdsAdmin
