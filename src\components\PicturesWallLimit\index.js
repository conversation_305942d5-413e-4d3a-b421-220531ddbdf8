import React, { Component } from 'react'
import { Upload, Modal, Popover, message } from 'antd'
import { PlusOutlined } from '@ant-design/icons'

class PicturesWall extends Component {
  constructor (props) {
    super(props)

    this.state = {
      fileList: props.value === null ? [] : [urlValue(props.value)],
      previewImage: '',
      previewVisible: false
    }
  }

  componentWillReceiveProps (nextProps) {
    // 受控组件， 用于更新
    const { value } = nextProps
    this.setState({
      fileList: value === null ? [] : [urlValue(value)]
    })
  }

  handlePreview = file => {
    this.setState({
      previewImage: file.url || file.thumbUrl,
      previewVisible: true
    })
  }

  handleCancel = () => this.setState({ previewVisible: false })

  handleChange = ({ fileList }) => {
    if (fileList.length === 0) {
      this.props.onChange()
    }
    if (fileList.length > 0 && fileList[fileList.length - 1].status === 'done' && fileList[fileList.length - 1].response.status === 0) {
      var url = fileList[fileList.length - 1].response.urls[0]
      this.props.onChange(url) // update getFiledDecorator
    }
    this.setState({ fileList })
  }

  beforeUpload1 (file) {
    const isLt2M = file.size < 40 * 1024
    if (!isLt2M) {
      message.error('图片要小于 40k!')
    }
    return isLt2M
  }

  renderImage = () => {
    const { value } = this.props
    const content = (
      <div>
        <img src={value} style={{ maxHeight: 200, maxWidth: 200 }} />
      </div>
    )
    return (
      <Popover placement='right' content={content} title={null}>
        <img width='102' height='102' src={value} />
        <i className='anticon-delete' />
      </Popover>
    )
  }

  render () {
    const { previewVisible, previewImage, fileList } = this.state
    const uploadButton = (
      <div>
        <PlusOutlined />
        <div className='ant-upload-text'>Upload</div>
      </div>
    )

    return (
      <div className='clearfix'>
        <Upload
          action='https://fts.yy.com/fs/uploadfiles'
          listType='picture-card'
          fileList={fileList}
          beforeUpload={this.beforeUpload1}
          onPreview={this.handlePreview}
          onChange={this.handleChange}
          showUploadList={false}
          data={file => ({ bucket: 'makefriends', files: file })}
        >
          {fileList.length > 0 ? this.renderImage() : uploadButton}
        </Upload>
        <Modal visible={previewVisible} footer={null} onCancel={this.handleCancel}>
          <img alt='example' style={{ width: '100%' }} src={previewImage} />
        </Modal>
      </div>
    )
  }
}

function urlValue (url) {
  return { uid: -1, status: 'done', url: url, thumbUrl: url }
}

export default PicturesWall
