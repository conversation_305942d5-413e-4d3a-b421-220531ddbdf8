import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import { connect } from 'dva'
import Compere from './components/Compere.js'
import User from './components/User.js'

const TabPane = Tabs.TabPane

@connect(({ levelInfo }) => ({ // model 的 namespace
  model: levelInfo // model 的 namespace
}))
class Index extends Component { // 默认页面组件，不需要修改
  /** *******************************页面布局*************************************************************/
  state = { activeKey: '1' }
  onTabClick = key => {
    this.setState({ activeKey: key })
  }

  render () {
    const { route } = this.props
    const { activeKey } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs type='card' defaultActiveKey='1' onTabClick={this.onTabClick} >
          <TabPane tab='主持' key='1' >
            {activeKey === '1' ? <Compere /> : null }
          </TabPane>
          <TabPane tab='用户' key='2'>
            {activeKey === '2' ? <User /> : null }
          </TabPane>
        </Tabs>
      </PageHeaderWrapper>
    )
  }
}

export default Index // 保证唯一
