import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import LineWrap from '@/components/LineWrapper'
import { Table, Divider, Button, Form, Card, Modal, Input, DatePicker, Select, message } from 'antd'
import { EditOutlined } from '@ant-design/icons'
import { connect } from 'dva'
var moment = require('moment')

const namespace = 'liveHelperNotify'
const Option = Select.Option
const FormItem = Form.Item
const dateFormat = 'YYYY-MM-DD HH:mm'
const { RangePicker } = DatePicker

// const TextArea = Input.TextArea

const showPlaceConfig = [
  { value: 0, label: '全部' },
  { value: 1, label: '默认首页' },
  { value: 2, label: '主持任务' }
]

const jumpTypeConfig = [
  { value: 0, label: '全部' },
  { value: 1, label: '不跳转' },
  { value: 2, label: '打开开播工具弹窗' },
  { value: 3, label: '模板内弹窗' },
  { value: 4, label: '浏览器打开链接' }
]

const stateConfig = [
  { value: 0, label: '全部' },
  { value: 1, label: '生效中' },
  { value: 2, label: '待生效' },
  { value: 3, label: '已失效' }
]

const operationConfig = [
  { value: 0, label: '全部' },
  { value: 1, label: '上架' },
  { value: 2, label: '下架' }
]

let showPlaceOptions = []
for (let i = 0; i < showPlaceConfig.length; i++) {
  showPlaceOptions.push(<Option key={i} value={showPlaceConfig[i].value}>{showPlaceConfig[i].label}</Option>)
}

let jumpTypeOptions = []
for (let i = 0; i < jumpTypeConfig.length; i++) {
  jumpTypeOptions.push(<Option key={i} value={jumpTypeConfig[i].value}>{jumpTypeConfig[i].label}</Option>)
}

let statusOptions = []
for (let i = 0; i < stateConfig.length; i++) {
  statusOptions.push(<Option key={i} value={stateConfig[i].value}>{stateConfig[i].label}</Option>)
}

let operationOptions = []
for (let i = 0; i < operationConfig.length; i++) {
  operationOptions.push(<Option key={i} value={operationConfig[i].value}>{operationConfig[i].label}</Option>)
}

@connect(({ liveHelperNotify }) => ({
  model: liveHelperNotify
}))

class liveHelperNotify extends Component {
  columns = [
    { title: '展示位置', dataIndex: 'showPlace', align: 'center', render: text => this.showPlace(text), width: 150 },
    { title: '通知文案', dataIndex: 'notifyMsg', align: 'center', width: 300 },
    { title: '通知时间', dataIndex: 'timeRange', align: 'center', width: 300 },
    { title: '跳转类型', dataIndex: 'jumpType', align: 'center', width: 150, render: text => this.showJumpType(text) },
    { title: '跳转链接', dataIndex: 'jumpURL', align: 'center', width: 300, render: text => (<LineWrap title={text} lineClampNum={1} />) },
    { title: '状态', dataIndex: 'status', align: 'center', width: 100, render: text => this.showStatus(text) },
    { title: '添加时间', dataIndex: 'updateTime', align: 'center', width: 150, render: text => moment.unix(text).format('YYYY-MM-DD HH:mm:ss') },
    { title: '操作',
      align: 'center',
      width: 150,
      render: (text, record) => (
        <span>
          <a><EditOutlined style={{ marginRight: 10 }} onClick={this.showModal(true, record)} /></a>
        </span>)
    }
  ]

  pagination = { pageSizeOptions: ['20', '50', '100'], showSizeChanger: true, pageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }
  state = { visible: false, isUpdate: false, value: {}, dateRange: [moment().subtract(1, 'days'), moment().subtract(0, 'days')], showPlace: 0, jumpType: 0, status: 0, startTime: 0, endTime: 0, operation: 1 }

  showModal = (isUpdate, record) => () => {
    if (record == null) {
      record = { startTime: moment().unix(), endTime: moment().unix() }
    }

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.startTime = moment.unix(v.startTime)
      v.endTime = moment.unix(v.endTime)
      this.formRef.setFieldsValue(v)
    }

    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  getDateFirstSecond = (timestamp) => {
    var date = new Date(timestamp * 1000)
    var unixTimestamp = Math.floor(date.getTime() / 1000)
    return unixTimestamp
  }

  onSeach = () => {
    const { dispatch } = this.props
    const data = { showPlace: this.state.showPlace, jumpType: this.state.jumpType, status: this.state.status, startTime: this.state.startTime, endTime: this.state.endTime }

    console.log('onSeach:', data)

    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }
  hideModal = () => {
    this.setState({ visible: false })
  }

  dateString (timestamp) {
    if (timestamp === 0) {
      return '-'
    }
    return moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss')
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  onFinish = values => {
    const { dispatch } = this.props
    const { isUpdate } = this.state

    values.startTime = this.getHourFirstSecond(values.startTime.unix())
    values.endTime = this.getHourFirstSecond(values.endTime.unix())

    console.log('value:isUpdate ', isUpdate)
    console.log('value:value values', values)

    if (values.showPlace === 1 && values.notifyMsg.length > 20) {
      message.error('默认首页的通知超过20个字')
      return
    }

    if (values.showPlace === 2 && values.notifyMsg.length > 25) {
      message.error('主持任务的通知超过25个字')
      return
    }

    const url = isUpdate ? `${namespace}/updateItem` : `${namespace}/addItem`
    dispatch({
      type: url,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { seqId: key }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  handleGameTypeChange = (value) => {
    console.log('handleGameTypeChange:', value.key)
    this.state.showPlace = value.key
    console.log('showPlace:', this.state.showPlace)
  }

  getHourFirstSecond = (timestamp) => {
    var date = new Date(timestamp * 1000)
    date.setSeconds(0)
    date.setMilliseconds(0)
    var unixTimestamp = Math.floor(date.getTime() / 1000)
    return unixTimestamp
  }

  handleTimeRangeChange = (value) => {
    // this.setState({ dateRange: value })

    var startTime = value[0].unix()
    var endTime = value[1].unix()
    this.state.startTime = startTime
    this.state.endTime = endTime
    console.log('startTime', this.state.startTime)
    console.log('endTime', this.state.endTime)
  }

  handleJumpTypeChange = (value) => {
    this.state.jumpType = value.key
    console.log('jumpType:', this.state.jumpType)
  }

  handleStatusChange = (value) => {
    this.state.status = value.key
    console.log('status:', this.state.status)
  }

  componentDidMount () {
    const { dispatch } = this.props
    const data = { jumpType: 0 }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  showJumpType (jumpType) {
    let jumpTypeName = ''
    for (let i = 0; i < jumpTypeConfig.length; i++) {
      if (jumpTypeConfig[i].value === jumpType) {
        jumpTypeName = jumpTypeConfig[i].label
        break
      }
    }

    return jumpTypeName
  }

  showPlace (gameType) {
    let showPlace = ''
    for (let i = 0; i < showPlaceConfig.length; i++) {
      if (showPlaceConfig[i].value === gameType) {
        showPlace = showPlaceConfig[i].label
        break
      }
    }

    return showPlace
  }

  showStatus (statusType) {
    let showStatus = ''
    for (let i = 0; i < stateConfig.length; i++) {
      if (stateConfig[i].value === statusType) {
        showStatus = stateConfig[i].label
        break
      }
    }

    return showStatus
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list } } = this.props
    const { visible, title } = this.state
    const { dateRange } = this.state
    const formLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    console.log('value:aaaaaaaaaaaaaa')
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form ref={form => { this.formRef = form }} {...formLayout}>
            <span style={{ marginLeft: 10 }}>展示位置  </span>
            <Select labelInValue defaultValue={{ key: 0 }} style={{ width: 200 }} onChange={this.handleGameTypeChange}>

              {showPlaceOptions}
            </Select>
            <Divider type='vertical' /> {/* 分割线 */}
            <span style={{ marginLeft: 10 }}>通知时间  </span>
            <RangePicker showTime style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.handleTimeRangeChange} />
            <Divider type='vertical' /> {/* 分割线 */}
            <span style={{ marginLeft: 10 }}>跳转类型  </span>
            <Select labelInValue defaultValue={{ key: 0 }} style={{ width: 120 }} onChange={this.handleJumpTypeChange}>
              { jumpTypeOptions }
            </Select>
            <span style={{ marginLeft: 10 }}>状态  </span>
            <Select labelInValue defaultValue={{ key: 0 }} style={{ width: 120 }} onChange={this.handleStatusChange}>
              { statusOptions }
            </Select>
            <span style={{ marginLeft: 10 }} />
            <Button type='primary' onClick={this.onSeach}>搜索</Button>
            <span style={{ marginLeft: 10 }} />
            <Button type='primary' onClick={this.showModal(false)}>添加</Button>
            <Divider />
            <Table dataSource={list} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit} width='40%'>
          <Form ref={this.saveFormRef} onFinish={this.onFinish} {...formLayout} >
            <FormItem label='展示位置' name='showPlace' rules={[{ required: true }]}>
              <Select style={{ width: '100%' }}>{showPlaceOptions}</Select>
            </FormItem>
            <FormItem label='通知文案' name='notifyMsg' rules={[{ required: true, message: '请输入功能地址!' }]}>
              <Input maxlength={25} />
            </FormItem>
            <FormItem label='通知开始时间' name='startTime' rules={[{ required: true, message: '请输入通知开始时间' }]}>
              <DatePicker showTime='true' format='YYYY-MM-DD HH' />
            </FormItem>
            <FormItem label='通知结束时间' name='endTime' rules={[{ required: true, message: '请输入通知结束时间' }]}>
              <DatePicker showTime='true' format='YYYY-MM-DD HH' />
            </FormItem>
            <FormItem label='跳转类型' name='jumpType'>
              <Select style={{ width: '100%' }}>{jumpTypeOptions}</Select>
            </FormItem>
            <FormItem label='页面地址' name='jumpURL' rules={[{ message: '请输入功能地址!' }]}>
              <Input />
            </FormItem>
            <FormItem label='操作' name='operation' rules={[{ required: true }]}>
              <Select style={{ width: '100%' }}>{operationOptions}</Select>
            </FormItem>
            <FormItem name='ID'>
              <Input hidden />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default liveHelperNotify
