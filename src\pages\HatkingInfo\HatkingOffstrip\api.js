import request from '@/utils/request'
import { stringify } from 'qs'

export function getPoolFromServer () {
  return request(`/lottery_hatking/admin/query_pool`)
}

export function getReleasePoolFromServer () {
  return request(`/lottery_hatking/admin/query_release_pool`)
}

// export function removePoolFromServer (params) {
//   return request(`/drop/admin/remove_pool?${stringify(params)}`)
// }

// export function getPoolListFromServer () {
//   return request(`/drop/admin/query_pool_list`)
// }

export function updatePoolFromServer (params) {
  return request(`/lottery_hatking/admin/update_pool`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

// export function updatePoolDescFromServer (params) {
//   return request(`/drop/admin/update_pool?desc=1`, {
//     method: 'POST',
//     headers: {
//       'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
//     },
//     body: JSON.stringify(params)
//   })
// }

// export function addPoolFromServer (params) {
//   return request(`/drop/admin/add_pool`, {
//     method: 'POST',
//     headers: {
//       'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
//     },
//     body: JSON.stringify(params)
//   })
// }

// 营收礼物列表
export function getPorpsList () {
  return request(`/lottery_hatking/admin/prize_list`)
}

export function getWarnFromServer () {
  return request(`/lottery_hatking/admin/warn_query`)
}

export function upsetWarnFromServer (params) {
  return request(`/lottery_hatking/admin/warn_upset`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function removeWarnFromServer (params) {
  return request(`/lottery_hatking/admin/warn_remove?${stringify(params)}`)
}
