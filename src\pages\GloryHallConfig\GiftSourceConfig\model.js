import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const getSouceConfigList = genGetRequireTemplate('/glory_hall_boss/get_yuyin_props_source', 'list')
const updateSourceConfig = genUpdateTemplate('/glory_hall_boss/update_yuyin_props_source')

export default {
  namespace: 'giftSourceConfig',
  state: {
    list: [],
    title: 'Bind success!'
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getSouceConfigList,
    updateSourceConfig
  }
}
