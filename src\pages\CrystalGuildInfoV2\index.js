import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Table, Divider, Form, Button, Input, DatePicker, Modal, message } from 'antd'
import { connect } from 'dva'
import { tableStyle, Inputlayout } from '@/utils/common'
import { exportExcel } from 'xlsx-oc'

var moment = require('moment')
const namespace = 'crystalGuildInfoV2'

@connect(({ crystalGuildInfoV2 }) => ({
  model: crystalGuildInfoV2
}))

class CrystalGuildInfoV2 extends Component {
  constructor (props) {
    super(props)
    this.refreshCrystalGuildList()
  }

  columns = [
    { title: '#', dataIndex: 'idx' },
    { title: '短位ID', dataIndex: 'asid' },
    { title: '长位ID', dataIndex: '_id' },
    { title: 'OW YY号', dataIndex: 'ow_yy' },
    { title: '公会群号', dataIndex: 'guild_group_id' },
    { title: '公会等级', dataIndex: 'level' },
    { title: '日均运营时长', dataIndex: 'last_month_op_time' },
    { title: 'dau', dataIndex: 'last_month_dau' },
    { title: '日均黄水晶(剔除盖章)', dataIndex: 'last_month_crystal' },
    { title: '日均盖章黄水晶', dataIndex: 'last_month_seal_crystal' },
    { title: '收入主持数', dataIndex: 'last_month_income_count' },
    { title: '操作人', dataIndex: 'last_op_uid' },
    { title: '操作时间', dataIndex: 'operate_time' },
    { title: '51-60级', dataIndex: 'between_51_60' },
    { title: '41-50级', dataIndex: 'between_41_50' },
    { title: '31-40级', dataIndex: 'between_31_40' },
    { title: '11-30级', dataIndex: 'between_11_30' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <Button style={{ marginRight: 10 }} size='small' type='primary' onClick={this.showModal(record)}>修改</Button>
        </span>)
    }
  ]

  state = { visible: false, isUpdate: false, value: {}, timeStart: '', timeEnd: '', searchSid: '-1', searchLevel: '-1' }

  // 标签页发生切换
  onTagChange = (record) => {
    console.log(record)
    if (record === '1') { // 切换到'添加标签页'
      this.initForm()
    }
    if (record === '2') { // 切换到'频道视频白名单标签页'
      this.refreshCrystalGuildList()
    }
  }

  initForm = () => {
    // this.setState({ timeStart: '', timeEnd: '', querySid: '-1', queryLevel: '-1' })
    if (this.formRef) {
      this.formRef.resetFields()
    }
  }

  // 获取/刷新水晶公会列表数据
  refreshCrystalGuildList = () => {
    this.props.dispatch({
      type: `${namespace}/getList`,
      payload: null
    })
  }

  // 搜索筛选水晶公会列表
  handleSearch = () => {
    const { dispatch } = this.props
    const { timeStart, timeEnd, searchSid, searchLevel } = this.state
    var data = { query_time_start: moment(timeStart).format('YYYY-MM-DD'), query_time_end: moment(timeEnd).format('YYYY-MM-DD'), query_sid: searchSid, query_level: searchLevel, isQuery: 1 }
    dispatch({
      type: `${namespace}/getQueryList`,
      payload: data
    })
  }

  onExport = () => {
    message.info('onExport')
    let headers = []
    let columns = this.columns
    const { exportKey } = this.state
    columns.forEach(function (item) {
      headers.push({ k: item.dataIndex, v: item.title })
    })

    exportExcel(headers, exportKey)
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  onStartChange = (value) => {
    this.onChange('timeStart', value)
  }

  onEndChange = (value) => {
    this.onChange('timeEnd', value)
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      var value = selectedRows.map(item => item.Sid).join(',')
      this.setState({ removeKey: value })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name
    })
  }

  // show modal
  showModal = (record) => () => {
    // if (record == null) record = { uid: '' }
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(record)
    }
    this.setState({ value: record, visible: true, title: '修改水晶公会' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onFinish = values => {
    const { dispatch, model: { displayData } } = this.props

    for (var i = 0; i < displayData.length; i++) {
      if (values.guild_group_id === displayData[i].guild_group_id && values.level === displayData[i].level) {
        message.info('无更新')
        return
      }
    }

    var data = { _id: values._id, guild_group_id: values.guild_group_id, level: values.level }
    dispatch({
      type: `${namespace}/updateItem`,
      payload: data
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 水晶公会展示列表标签页html代码
  displayCrystalGuildInfoListHtml = () => {
    const { displayData } = this.props.model

    return (
      <Form {...Inputlayout}
        initialValues={{ timeStart: '', timeEnd: '', querySid: '-1', queryLevel: '-1', isQuery: -1 }}
        ref={form => { this.formRef = form }}
      >
        长位id
        <Input placeholder='长位sid' onChange={e => this.setState({ searchSid: e.target.value === '' ? -1 : e.target.value })} style={{ width: 120, marginLeft: 10 }} />
        <Divider type='vertical' /> {/* 分割线 */}

        公会等级
        <Input placeholder='公会等级level' onChange={e => this.setState({ searchLevel: e.target.value === '' ? -1 : e.target.value })} style={{ width: 120, marginLeft: 10 }} />
        <Divider type='vertical' /> {/* 分割线 */}

        开始日期
        <DatePicker
          format='YYYY-MM-DD'
          defaultValue=''
          placeholder='开始日期'
          onChange={this.onStartChange}
          style={{ marginRight: 10, marginLeft: 10 }}
        />

        结束日期
        <DatePicker
          format='YYYY-MM-DD'
          defaultValue=''
          placeholder='开始日期'
          onChange={this.onEndChange}
          style={{ marginLeft: 10 }}
        />

        <Button style={{ marginLeft: 10 }} type='primary' onClick={this.handleSearch}>搜索</Button>
        <Form>
          <Table rowSelection={this.rowSelection} columns={this.columns} dataSource={displayData} rowKey={(record, index) => index} size='small' pagination={tableStyle} />
        </Form>
        <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
      </Form>
    )
  }

  render () {
    const { route } = this.props
    const { visible } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          {this.displayCrystalGuildInfoListHtml()}
        </Card>

        <Modal visible={visible} title={route.name} onCancel={this.hideModal} onOk={this.handleSubmit} forceRender>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <Form.Item label='短位ID' name='asid' rules={[{ required: true, message: '必须为数字' }]}>
              <Input placeholder='必须为数字' disabled='true' />
            </Form.Item>

            <Form.Item label='长位ID' name='_id' rules={[{ required: true, message: '必须为数字' }]}>
              <Input placeholder='必须为数字' disabled='true' />
            </Form.Item>

            <Form.Item label='ow yy号' name='ow_yy' rules={[{ required: true, message: 'ow yy号' }]}>
              <Input placeholder='ow yy号' disabled='true' />
            </Form.Item>

            <Form.Item label='公会群号' name='guild_group_id' rules={[{ required: true, message: '公会等级' }]}>
              <Input placeholder='公会群号' />
            </Form.Item>

            <Form.Item label='公会等级' name='level' rules={[{ required: true, message: '公会等级' }]}>
              <Input placeholder='公会等级' />
            </Form.Item>

            <Form.Item label='上月运营时长' name='last_month_op_time' rules={[{ required: true, message: '必须为数字' }]}>
              <Input placeholder='必须为数字' disabled='true' />
            </Form.Item>

            <Form.Item label='上月日均人气' name='last_month_dau' rules={[{ required: true, message: '必须为数字' }]}>
              <Input placeholder='必须为数字' disabled='true' />
            </Form.Item>

            <Form.Item label='最后操作人' name='last_op_uid'>
              <Input disabled='true' />
            </Form.Item>

            <Form.Item label='操作时间' name='operate_time' rules={[{ required: true, message: '必须为数字' }]}>
              <Input placeholder='必须为数字' disabled='true' />
            </Form.Item>

            <Form.Item label='上月黄水晶数' name='last_month_crystal' rules={[{ required: true, message: '必须为数字' }]}>
              <Input placeholder='必须为数字' disabled='true' />
            </Form.Item>

            <Form.Item label='收入主持数' name='last_month_income_count' rules={[{ required: true, message: '必须为数字' }]}>
              <Input placeholder='必须为数字' disabled='true' />
            </Form.Item>

            <Form.Item label='51-60级主持人数' name='between_51_60' rules={[{ required: true, message: '必须为数字' }]}>
              <Input placeholder='必须为数字' disabled='true' />
            </Form.Item>

            <Form.Item label='41-50级主持人数' name='between_41_50' rules={[{ required: true, message: '必须为数字' }]}>
              <Input placeholder='必须为数字' disabled='true' />
            </Form.Item>

            <Form.Item label='31-40级主持人数' name='between_31_40' rules={[{ required: true, message: '必须为数字' }]}>
              <Input placeholder='必须为数字' disabled='true' />
            </Form.Item>

            <Form.Item label='11-30级主持人数' name='between_11_30' rules={[{ required: true, message: '必须为数字' }]}>
              <Input placeholder='必须为数字' disabled='true' />
            </Form.Item>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default CrystalGuildInfoV2
