import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card } from 'antd'
import { connect } from 'dva'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import BrandActivityMagic from './components/magicTaskConfig.js'
import BrandActivityLive from './components/liveConfig.js'
import BrandActivityRoutine from './components/routineTaskConfig'
import BrandActivityYYMobile from './components/yymobileTaskConfig.js'

@connect(({ brandActLive }) => ({
  model: brandActLive
}))

class HelpGroup extends Component {
  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Card style={{ marginTop: 20 }}>
          <Tabs id='brandActivityLive' type='card' defaultActiveKey='1'>
            <TabPane tab='常规任务配置' key='1'>
              <BrandActivityRoutine />
            </TabPane>
            <TabPane tab='魔法任务配置' key='2'>
              <BrandActivityMagic />
            </TabPane>
            <TabPane tab='活动玩法配置' key='3'>
              <BrandActivityLive />
            </TabPane>
            <TabPane tab='手Y专属任务配置' key='4'>
              <BrandActivityYYMobile />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default HelpGroup
