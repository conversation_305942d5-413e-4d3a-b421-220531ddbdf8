
// 构造优质厅流水规模字段
import { yuanToWan } from '../../../HatkingInfo/HatSuperCompereTask/components/common'
import { Divider } from 'antd'
import React from 'react'

export const buildTingNum = (tingIntervalList) => {
  let content = null
  let lastIdx = tingIntervalList.length - 1
  for (let i = 0; i < tingIntervalList.length; i++) {
    const element = tingIntervalList[i]
    if (i !== lastIdx) {
      content = <span>{content}<div>[{yuanToWan(element.startInterval)}万-{yuanToWan(element.endInterval)}万): {element.ting}个</div><Divider type='horizontal' /></span>
    } else {
      if (element.type === 1) {
        // [10万-50万)
        content = <span>{content}<div>[{yuanToWan(element.startInterval)}万-{yuanTo<PERSON>an(element.endInterval)}万): {element.ting}个</div></span>
      } else {
        content = <span>{content}<div>[{yuanToWan(element.startInterval)}万+): {element.ting}个</div></span>
      }
    }
  }
  return content
}

export const buildTingNumSettlement = (tingIntervalList) => {
  let content = null
  let lastIdx = tingIntervalList.length - 1
  for (let i = 0; i < tingIntervalList.length; i++) {
    const element = tingIntervalList[i]
    if (i !== lastIdx) {
      content = <span>{content}<div>[{yuanToWan(element.startInterval)}万-{yuanToWan(element.endInterval)}万): {element.ting}个; {element.settlement}元</div><Divider type='horizontal' /></span>
    } else {
      if (element.type === 1) {
        // [10万-50万)
        content = <span>{content}<div>[{yuanToWan(element.startInterval)}万-{yuanToWan(element.endInterval)}万): {element.ting}个; {element.settlement}元</div></span>
      } else {
        content = <span>{content}<div>[{yuanToWan(element.startInterval)}万+): {element.ting}个; {element.settlement}元</div></span>
      }
    }
  }
  return content
}

export const buildTingNumExport = (tingIntervalList) => {
  let content = ''
  let lastIdx = tingIntervalList.length - 1
  for (let i = 0; i < tingIntervalList.length; i++) {
    const element = tingIntervalList[i]
    if (i !== lastIdx) {
      content += '[' + yuanToWan(element.startInterval) + '万-' + yuanToWan(element.endInterval) + '万): ' + element.ting + '个' + ';' + element.settlement + '元' + ' | '
    } else {
      if (element.type === 1) {
        // [10万-50万)
        content += '[' + yuanToWan(element.startInterval) + '万-' + yuanToWan(element.endInterval) + '万): ' + element.ting + '个' + ';' + element.settlement + '元'
      } else {
        content += '[' + yuanToWan(element.startInterval) + '万+): ' + element.ting + '个' + ';' + element.settlement + '元'
      }
    }
  }
  return content
}

export const buildTingPrizeRule = (tingIntervalList) => {
  let content = null
  let lastIdx = tingIntervalList.length - 1
  for (let i = 0; i < tingIntervalList.length; i++) {
    const element = tingIntervalList[i]
    if (i !== lastIdx) {
      content = <span>{content}<div>[{element.remark === '未确认发奖，默认按照系统结算' ? '' : yuanToWan(element.startInterval)}万-{yuanToWan(element.endInterval)}万): {element.prizeRuleStr}</div><Divider type='horizontal' /></span>
    } else {
      if (element.type === 1) {
        // [10万-50万)
        content = <span>{content}<div>[{element.remark === '未确认发奖，默认按照系统结算' ? '' : yuanToWan(element.startInterval)}万-{yuanToWan(element.endInterval)}万): {element.prizeRuleStr}</div></span>
      } else {
        content = <span>{content}<div>[{element.remark === '未确认发奖，默认按照系统结算' ? '' : yuanToWan(element.startInterval)}万+): {element.prizeRuleStr}</div></span>
      }
    }
  }
  return content
}

export const buildTingPrizeRuleStrExport = (tingIntervalList) => {
  let content = ''
  let lastIdx = tingIntervalList.length - 1
  for (let i = 0; i < tingIntervalList.length; i++) {
    const element = tingIntervalList[i]
    const prizeRuleStr = element.remark === '未确认发奖，默认按照系统结算' ? '' : element.prizeRuleStr
    if (i !== lastIdx) {
      content += '[' + yuanToWan(element.startInterval) + '万-' + yuanToWan(element.endInterval) + '万): ' + prizeRuleStr + ' | '
    } else {
      if (element.type === 1) {
        // [10万-50万)
        content += '[' + yuanToWan(element.startInterval) + '万-' + yuanToWan(element.endInterval) + '万): ' + prizeRuleStr
      } else {
        content += '[' + yuanToWan(element.startInterval) + '万+): ' + prizeRuleStr
      }
    }
  }
  return content
}
