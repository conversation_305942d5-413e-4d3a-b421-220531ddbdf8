import {
  Card,
  Form,
  Table,
  // Typography,
  // message,
  Divider, Button,
  // Input,
  DatePicker
} from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import exportExcel from '@/utils/exportExcel'
import moment from 'moment'
import { buildTingNum, buildTingNumExport } from './common'

const namespace = 'guildTaskRevision'
const getListUri = `${namespace}/getSummaryList`

@connect(({ rewardSummaryReport }) => ({
  model: rewardSummaryReport
}))

class RewardSummaryReport extends Component {
  constructor (props) {
    super(props)

    const { dataInfo } = props
    console.log('constructor dataInfo', dataInfo)
    this.state = { list: dataInfo, searchASID: 0, searchTaskMonth: '' }
  }

  componentDidMount () {
    const { dispatch } = this.props
    const { searchASID, searchTaskMonth } = this.state
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    let data = { asid: searchASID, month: searchMonthTmp }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  onClick = () => {
    const { dispatch } = this.props
    const { searchASID, searchTaskMonth } = this.state
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    let data = { asid: searchASID, month: searchMonthTmp }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  componentWillReceiveProps (nextProps) {
    const { dataInfo } = nextProps
    console.log('componentWillReceiveProps', dataInfo)
    this.setState({ list: dataInfo })
  }

  state = {
    visible: false,
    isUpdate: false,
    isNotifyValue: true,
    searchInput: '',
    searchKeyword: '',
    channel: { key: -1 },
    gameType: { key: -1 }
  }

  tableInitValue = {}

  columns = [
    { title: '任务时间', width: 50, dataIndex: 'month' },
    { title: '全月礼物流水/元', dataIndex: 'monthAmount' },
    { title: '公会任务-礼物流水/元', dataIndex: 'guildAmount' },
    { title: '公会任务-优质厅数/个', dataIndex: 'highGradeTingCount' },
    { title: '各流水规模优质厅数',
      dataIndex: 'tingIntervalList',
      render: (text, record) => {
        return buildTingNum(record.tingIntervalList !== null ? record.tingIntervalList : [])
      }
    },
    { title: '参与公会数', dataIndex: 'sidCount' },
    { title: '发奖公会数', width: 50, dataIndex: 'rewardSidCount' },
    { title: '流水任务最终发奖金额/元', dataIndex: 'revenueReward' },
    { title: '优质厅任务最终发奖金额/元', dataIndex: 'highGradeTingReward' },
    { title: '合计最终发奖金额/元', align: 'center', dataIndex: 'totalReward' }
  ].map(item => {
    item.align = 'center'
    item.ellipsis = true
    return item
  })

  pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  getFilterList = () => {
    const { list } = this.state
    // const { searchImid } = this.state
    let filterList = list
    // if (searchImid && searchImid.length > 0) {
    //   filterList = filterList.filter((v) => { return v.imid === parseInt(searchImid) })
    // }
    console.log('getFilterList', list)
    return filterList
  }

  showModal = (isUpdate, record) => () => {
    if (record == null) {
      record = this.tableInitValue
    }
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(record)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate })
  }

  onExport = () => {
    let list = this.getFilterList()
    let exportData = list.map(item => {
      let tingIntervalList = item.tingIntervalList
      if (tingIntervalList) {
        delete item.tingIntervalList
        item['tingIntervalList'] = buildTingNumExport(tingIntervalList)
      }
      let v = $.extend(true, {}, item)
      return v
    })
    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.export === undefined || col.export) {
        exportHeader.push({ key: col.dataIndex, header: col.title })
      }
    })
    let fileName = '公会任务发奖金额汇总-' + moment().format('YYYYMMDD') + '.xlsx'
    exportExcel(exportHeader, exportData, fileName)
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  render () {
    return (
      <Card>
        <Form>
          <Divider type='vertical' />
          任务时间：
          <DatePicker
            format='YYYY-MM'
            picker='month'
            placeholder='任务时间'
            onChange={(v) => this.setState({ searchTaskMonth: v })}
            style={{ width: 100, marginRight: 10 }}
          />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onExport}>导出</Button>
          <Divider type='vertical' />
          <Table style={{ marginTop: 10 }} dataSource={this.getFilterList()} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' scroll={{ x: 'max-content' }} />

        </Form>
      </Card>
    )
  }
}

export default RewardSummaryReport
