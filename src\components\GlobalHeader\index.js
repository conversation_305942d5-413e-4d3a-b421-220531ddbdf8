import React, { PureComponent } from 'react'
import Debounce from 'lodash-decorators/debounce'
import styles from './index.module.less'
import RightContent from './RightContent'
import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons'
import GlobalFavarate from './Favarate'

export default class GlobalHeader extends PureComponent {
  componentWillUnmount () {
    this.triggerResizeEvent.cancel()
  }
  /* eslint-disable*/
  @Debounce(600)
  triggerResizeEvent() {
    // eslint-disable-line
    const event = document.createEvent('HTMLEvents');
    event.initEvent('resize', true, false);
    window.dispatchEvent(event);
  }
  toggle = () => {
    const { collapsed, onCollapse } = this.props;
    onCollapse(!collapsed);
    this.triggerResizeEvent();
  };
  render() {
    const { collapsed } = this.props;
    return (
      <div className={styles.header}>
        {collapsed ? <MenuUnfoldOutlined className={styles.trigger} onClick={this.toggle} /> :
        <MenuFoldOutlined className={styles.trigger} onClick={this.toggle} />}
        <RightContent {...this.props} />
        <GlobalFavarate {...this.props} />
      </div>
    );
  }
}
