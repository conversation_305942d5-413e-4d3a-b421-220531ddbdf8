import React, { Component } from 'react'
// import dateString from '@/utils/dateString'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, DatePicker } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const namespace = 'bubbleDailyStatistics'
const getListUri = `${namespace}/getList`

const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker

@connect(({ bubbleDailyStatistics }) => ({
  model: bubbleDailyStatistics
}))
class BubbleDailyStatistics extends Component {
  // 列表结构
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '1级气泡数量', dataIndex: 'bubble_sum_1', align: 'center' },
    { title: '2级气泡数量', dataIndex: 'bubble_sum_2', align: 'center' },
    { title: '3级气泡数量', dataIndex: 'bubble_sum_3', align: 'center' },
    { title: '4级气泡数量', dataIndex: 'bubble_sum_4', align: 'center' },
    { title: '4级气泡点击跳转数量', dataIndex: 'bubble_sum_jump', align: 'center' }
  ]

  defaultPageValue = {
    defaultPageSize: 50,
    pageSizeOptions: ['50', '100', '500'],
    showSizeChanger: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, startValue: null, endValue: null, dateRange: [moment().subtract(7, 'days'), moment().subtract(0, 'days')] }

  // 获取列表
  componentDidMount () {
    const { dispatch } = this.props
    const { dateRange } = this.state
    var data = { startDate: moment(dateRange[0]).format(dateFormat), endDate: moment(dateRange[1]).format(dateFormat) }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  onClick = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    var data = { startDate: moment(dateRange[0]).format(dateFormat), endDate: moment(dateRange[1]).format(dateFormat) }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  render () {
    const { route, model: { list } } = this.props
    const { dateRange } = this.state
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <span style={{ marginLeft: 10 }}>时间范围:</span>
            <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
            <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
            <Divider />
            <Table dataSource={list} columns={this.columns} rowKey={(record, index) => index} pagination={this.defaultPageValue} size='small' />
          </Form>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default BubbleDailyStatistics
