// eslint-disable-next-line no-unused-vars
import { getArkCrucialInfo, getArkPaidRangeInfo, getArkPeriodRangeInfo, getArkPositionStatsInfo, getArkRetainStatsInfo, getArkTopNUserInfo } from './api'

export default {
  namespace: 'arkJy',

  state: {
    crucialList: [],
    paidRangeList: [],
    periodRangeList: [],
    positionStatsList: [],
    retainUserInfoList: [],
    topnUserInfoList: []
  },

  reducers: {
    updateCrucialInfoList (state, { payload, cbFunc }) {
      // console.info('payload==>', payload)
      payload.sort((a, b) => b.date - a.date)
      cbFunc()
      return {
        ...state,
        crucialList: payload
      }
    },
    updatePaidRangeList (state, { payload }) {
      payload.sort(function (a, b) {
        if (a.date === b.date) { return a.rangeId - b.rangeId }
        return b.date - a.date
      })
      for (var i = 0; i < payload.length; i++) {
        payload[i].betRewardRatio = 0
        if (payload[i].betAmethyst > 0 && payload[i].betRewardAmethyst > 0) {
          payload[i].betRewardRatio = payload[i].betRewardAmethyst / payload[i].betAmethyst * 100
          payload[i].betRewardRatio = payload[i].betRewardRatio.toFixed(2) + '%'
        }

        payload[i].betAmethyst = payload[i].betAmethyst / 1000
        payload[i].betRewardAmethyst = payload[i].betRewardAmethyst / 1000
      }

      return {
        ...state,
        paidRangeList: payload
      }
    },
    updatePeriodRangeList (state, { payload }) {
      payload.sort(function (a, b) {
        if (a.date === b.date) { return a.rangeId - b.rangeId }
        return b.date - a.date
      })
      for (var i = 0; i < payload.length; i++) {
        payload[i].betRewardRatio = 0
        if (payload[i].betAmethyst > 0 && payload[i].betRewardAmethyst > 0) {
          payload[i].betRewardRatio = payload[i].betRewardAmethyst / payload[i].betAmethyst * 100
          payload[i].betRewardRatio = payload[i].betRewardRatio.toFixed(2) + '%'
        }

        payload[i].betAmethyst = payload[i].betAmethyst / 1000
        payload[i].betRewardAmethyst = payload[i].betRewardAmethyst / 1000
      }
      return {
        ...state,
        periodRangeList: payload
      }
    },
    updatePositionStatsInfoList (state, { payload }) {
      payload.sort(function (a, b) {
        if (b.date === a.date) {
          return a.position - b.position
        }

        return b.date - a.date
      })

      for (var i = 0; i < payload.length; i++) {
        payload[i].betRewardRatio = 0
        if (payload[i].winAmethyst > 0 && payload[i].betAmethyst > 0) {
          payload[i].betRewardRatio = payload[i].winAmethyst / payload[i].betAmethyst * 100
          payload[i].betRewardRatio = payload[i].betRewardRatio.toFixed(2) + '%'
        }
        if (payload[i].winAmethyst > 0) {
          payload[i].winAmethyst /= 1000
        }
        if (payload[i].betAmethyst > 0) {
          payload[i].betAmethyst /= 1000
        }
      }

      return {
        ...state,
        positionStatsList: payload
      }
    },
    updateUserRetainInfoList (state, { payload }) {
      payload.sort(function (a, b) {
        if (b.date === a.date) {
          return a.position - b.position
        }

        return b.date - a.date
      })
      for (var i = 0; i < payload.length; i++) {
        payload[i].betRewardRatio = 0
        if (payload[i].betRewardAmethyst > 0 && payload[i].betAmethyst > 0) {
          payload[i].betRewardRatio = payload[i].betRewardAmethyst / payload[i].betAmethyst * 100
          payload[i].betRewardRatio = payload[i].betRewardRatio.toFixed(2) + '%'
        }
        if (payload[i].amethyst > 0) {
          payload[i].amethyst /= 1000
        }
      }
      return {
        ...state,
        retainUserInfoList: payload
      }
    },

    updateUserTopNInfoList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        if (payload[i].amethyst > 0) {
          payload[i].amethyst /= 1000
        }
        if (payload[i].sealAmount > 0) {
          payload[i].sealAmount /= 1000
        }
        if (payload[i].awardAmount > 0) {
          payload[i].awardAmount /= 1000
        }
      }
      return {
        ...state,
        topnUserInfoList: payload
      }
    }
  },

  effects: {
    * getCrucialInfoList ({ payload, cbFunc }, { call, put }) {
      const { data: { ret } } = yield call(getArkCrucialInfo, payload)
      yield put({
        type: 'updateCrucialInfoList',
        payload: Array.isArray(ret) ? ret : [],
        cbFunc: cbFunc
      })
    },
    * getPaidRangeList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getArkPaidRangeInfo, payload)
      yield put({
        type: 'updatePaidRangeList',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getPeriodRangeList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getArkPeriodRangeInfo, payload)
      yield put({
        type: 'updatePeriodRangeList',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getPositionStatsList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getArkPositionStatsInfo, payload)
      yield put({
        type: 'updatePositionStatsInfoList',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getRetainUserInfo ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getArkRetainStatsInfo, payload)
      yield put({
        type: 'updateUserRetainInfoList',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getTopNRangeList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getArkTopNUserInfo, payload)
      yield put({
        type: 'updateUserTopNInfoList',
        payload: Array.isArray(ret) ? ret : []
      })
    }
  }
}
