import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import SynthesisConfigComponent from './component/config'
import SynthesisConfigListComponent from './component/history' 

const TabPane = Tabs.TabPane

class SynthesisConfigComponentPagge extends Component { // 默认页面组件，不需要修改
  state = { activeKey: '1' }
  onTabChange = key => {
    this.setState({ activeKey: key }) // render tab pane
  }

  render () {
    const { route } = this.props
    const { activeKey } = this.state
    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs onChange={this.onTabChange} type='card'>
          <TabPane tab='当前生效配置' key='1'>
            {activeKey === '1' ? <SynthesisConfigComponent {...this.state} /> : ''}
          </TabPane>
          <TabPane tab='审批历史记录' key='3'>
            {activeKey === '3' ? <SynthesisConfigListComponent {...this.state} /> : ''}
          </TabPane>
        </Tabs>
      </PageHeaderWrapper>
    )
  }
}

export default SynthesisConfigComponentPagge // 保证唯一
