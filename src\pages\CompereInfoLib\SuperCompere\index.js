import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import ExportRemoteData from '@/components/ExportRemoteData'
import { Button, Card, Col, DatePicker, Divider, Form, Input, InputNumber, Modal, Popconfirm, Row, Select, Table, Tabs, message } from 'antd'
import { tableStyle } from '@/utils/common'
import { stringify } from 'qs'
import CompereDetailInfo from '../CompereDetailInfo'

const FormItem = Form.Item
const Option = Select.Option
const namespace = 'supperCompere'
const { TextArea } = Input

var moment = require('moment')

const businessTypeJY = 1 // 交友
const businessTypePK = 2 // 约战
const businessTypeBABY = 3 // 宝贝
const businessTypeReadySuperJY = 4 // 交友准超主

const sex = [
  { idx: -1, label: '全部' },
  { idx: 0, label: '女' },
  { idx: 1, label: '男' }
]

// const remarkSex = [
//   { idx: -1, label: '全部' },
//   { idx: 0, label: '女' },
//   { idx: 1, label: '男' },
//   { idx: 2, label: '无备注' }
// ]
//
// const contractType = [
//   { idx: -1, label: '全部' },
//   { idx: 0, label: '否' },
//   { idx: 1, label: '是' }
// ]

const privilegeType = [
  { idx: -1, label: '全部' },
  { idx: 0, label: '无' },
  { idx: 1, label: '有' }
]

const compereLevel = [
  { idx: 0, label: 'C' }, // 默认C
  { idx: 1, label: 'S' },
  { idx: 2, label: 'A' },
  { idx: 3, label: 'B' },
  { idx: 4, label: 'C' }
]

const jyCompereType = [
  { idx: '', label: '全部' },
  { idx: 'OrdinaryJyCompere', label: '普通主持' },
  // { idx: 'OrdinaryJyTtt', label: '厅天团' },
  { idx: 'SuperJyCompere', label: '超级主持' },
  // { idx: 'SuperJyTingGroup', label: '超级天团' },
  // { idx: 'SuperJyInteractive', label: '帽子超级' },
  { idx: 'SuperJyReady', label: '准超级' }
]

const pkBabyCompereType = [
  { idx: '', label: '全部' },
  { idx: 'OrdinaryYzBbAnchor', label: '普通主播' },
  { idx: 'SuperKing', label: '王牌主播' }
]

const mediaType = [
  { idx: '', label: '全部' },
  { idx: 'video', label: '视频' },
  { idx: 'audio', label: '音频' }
]

const silenceTypeMap = {
  0: '-',
  1: '近期活跃',
  2: '轻度沉默',
  3: '中度沉默',
  4: '重度沉默'
}

@connect(({ supperCompere }) => ({
  model: supperCompere
}))

class SupperCompere extends Component {
  constructor (props) {
    super(props)

    this.refreshInfo(businessTypeJY, 1)
  }

  momentToDayStartMillis = (t) => {
    if (t) {
      return new Date(t.format('YYYY-MM-DD') + ' 00:00:00').getTime()
    }
    return 0
  }

  momentToDayEndMillis = (t) => {
    if (t) {
      return new Date(t.format('YYYY-MM-DD') + ' 23:59:59').getTime()
    }
    return 0
  }

  // business: 1-交友，2-约战，3-宝贝
  getSignTimeQuery = (business) => {
    return {
      begSignStartTime: this.momentToDayStartMillis(this.state['begSignStartTime' + business]),
      endSignStartTime: this.momentToDayEndMillis(this.state['endSignStartTime' + business]),
      begSignEndTime: this.momentToDayStartMillis(this.state['begSignEndTime' + business]),
      endSignEndTime: this.momentToDayEndMillis(this.state['endSignEndTime' + business])
    }
  }

  wrapTableColumns = (columns) => {
    let tColumns = []
    // tColumns.push(
    //   {
    //     title: '序号',
    //     render: (text, record, index) => {
    //       let currentPage = this.state.currentIndex || 1
    //       let currentSize = this.state.pageSize || 10
    //       return ((currentPage - 1) * currentSize + index + 1)
    //     }
    //   })
    columns.forEach(function (item) {
      tColumns.push(item)
    })
    return tColumns
  }

  columnsJy = [
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: 'YY号', dataIndex: 'yyno', align: 'center' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: '短位id', dataIndex: 'asid', align: 'center' },
    {
      title: '主持身份',
      dataIndex: 'compereType',
      align: 'center',
      render: (text, record) => {
        if (record.compereType === 'OrdinaryJyCompere') {
          return '普通主持'
        } else if (record.compereType === 'OrdinaryJyTtt') {
          return '厅天团'
        } else if (record.compereType === 'SuperJyCompere') {
          return '超级主持'
        } else if (record.compereType === 'SuperJyTingGroup') {
          return '超级天团'
        } else if (record.compereType === 'SuperJyInteractive') {
          return '帽子超级(已废弃)'
        } else if (record.compereType === 'SuperJyReady') {
          return '准超级'
        }
      }
    },
    { title: '沉默主持', dataIndex: 'silenceType', align: 'center', render: (text, record) => (silenceTypeMap[record.silenceType]) },
    {
      title: '等级',
      dataIndex: 'superLevel',
      align: 'center',
      render: (text, record) => {
        if (text === undefined || text === 0) {
          return 'C'
        } else {
          return compereLevel[text].label
        }
      }
    },
    {
      title: '签约开始时间',
      dataIndex: 'signStartTime',
      align: 'center',
      render: (text, record) => {
        if (record.signStartTime > 0) {
          return moment.unix(record.signStartTime / 1000).format('YYYY-MM-DD')
        }
      }
    },
    {
      title: '签约结束时间',
      dataIndex: 'signEndTime',
      align: 'center',
      render: (text, record) => {
        if (record.signEndTime > 0) {
          return moment.unix(record.signEndTime / 1000).format('YYYY-MM-DD')
        }
      }
    },
    {
      title: '特权',
      dataIndex: 'privilege',
      align: 'center',
      render: (text, record) => {
        if (record.privilege === 1) {
          return '有'
        } else if (record.privilege === 0) {
          return '无'
        }
      }
    },
    {
      title: '实名性别',
      dataIndex: 'gender',
      align: 'center',
      render: (text, record) => {
        if (record.gender === 0) {
          return '女'
        } else if (record.gender === 1) {
          return '男'
        }
      }
    },
    { title: '运营备注昵称', dataIndex: 'remarkNick', align: 'center' },
    {
      title: '音视频',
      dataIndex: 'tendencyCompereType',
      align: 'center',
      render: (text, record) => {
        if (record.tendencyCompereType === 'video') {
          return '视频'
        } else if (record.tendencyCompereType === 'audio') {
          return '音频'
        }
      }
    },
    { title: '运营备注', dataIndex: 'remarkOpt', align: 'center' },
    { title: '操作人', dataIndex: 'lastOperator', align: 'center' },
    {
      title: '详细信息',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      exportIgnore: true,
      render: (text, record) => (<span><a onClick={this.showDetailModal(record)}>详情</a></span>)
    },
    {
      title: '历史记录',
      align: 'center',
      fixed: 'right',
      exportIgnore: true,
      render: (text, record) => (
        <span>
          <a onClick={this.showremarkHistoryModal(record)}>查询</a>
        </span>)
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      render: (text, record) => {
        if (record.privilege === 1) {
          return <span>
            <a onClick={this.onModSuperCompereWhite(record.uid, 0)}>去掉特权</a>
            <Divider type='vertical' />
            <a onClick={this.showCancelSuperModal(record)}>取消超主身份</a>
            <Divider type='vertical' />
            <a onClick={this.showremarkModal(record)}>备注</a>
            <Divider type='vertical' />
            <a onClick={() => this.onRefreshCompereInfoClick(record)}>刷新</a>
          </span>
        } else if (record.privilege === 0) {
          return <span>
            <a onClick={this.onModSuperCompereWhite(record.uid, 1)}>添加特权</a>
            <Divider type='vertical' />
            <a onClick={this.showCancelSuperModal(record)}>取消超主身份</a>
            <Divider type='vertical' />
            <a onClick={this.showremarkModal(record)}>备注</a>
            <Divider type='vertical' />
            <a onClick={() => this.onRefreshCompereInfoClick(record)}>刷新</a>
          </span>
        }
      }
    }
  ]

  remarkHistoryColumns = [
    {
      title: '日期',
      dataIndex: 'opTime',
      align: 'left',
      render: (text, record) => {
        if (record.opTime > 0) {
          return moment.unix(record.opTime).format('YYYY-MM-DD HH:mm')
        }
      }
    },
    { title: '操作人', dataIndex: 'opPassport', align: 'left' },
    {
      title: '变更字段',
      dataIndex: 'field',
      align: 'left',
      render: (text, record) => {
        let content = []
        let arr = text.split(/\s+/)
        arr.forEach(function (item) {
          content.push(<div>{item}</div>)
        })
        return (<>{content}</>)
      }
    },
    {
      title: '变更描述',
      dataIndex: 'desc',
      align: 'left',
      render: (text, record) => {
        let content = []
        let arr = text.split(/;\s*【/)
        arr.forEach(function (item, index) {
          if (index > 0) {
            item = '【' + item
          }
          item = item.substr(0, item.length - 1)
          let regexp = /(【[^】]+】)字段从:([^，]*)，变为:(.*)/
          if (regexp.test(item)) {
            item = item.replace(regexp, '$1 从 [$2] 变成 [$3]')
          }
          content.push(<div>{item}</div>)
        })
        return (<>{content}</>)
      }
    }
  ]

  columnsPKBaby = [
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: 'YY号', dataIndex: 'yyno', align: 'center' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: '短位id', dataIndex: 'asid', align: 'center' },
    {
      title: '主持身份',
      dataIndex: 'compereRole',
      align: 'center',
      render: (text, record) => {
        if (record.compereType === 'OrdinaryYzBbAnchor') {
          return '普通主播'
        } else if (record.compereType === 'SuperKing') {
          return '王牌主播'
        }
      }
    },
    {
      title: '签约开始时间',
      dataIndex: 'signStartTime',
      align: 'center',
      render: (text, record) => {
        if (record.signStartTime > 0) {
          return moment.unix(record.signStartTime / 1000).format('YYYY-MM-DD')
        }
      }
    },
    {
      title: '签约结束时间',
      dataIndex: 'signEndTime',
      align: 'center',
      render: (text, record) => {
        if (record.signEndTime > 0) {
          return moment.unix(record.signEndTime / 1000).format('YYYY-MM-DD')
        }
      }
    },
    {
      title: '实名性别',
      dataIndex: 'gender',
      align: 'center',
      render: (text, record) => {
        if (record.gender === 0) {
          return '女'
        } else if (record.gender === 1) {
          return '男'
        }
      }
    },
    { title: '运营备注昵称', dataIndex: 'remarkNick', align: 'center' },
    {
      title: '音视频',
      dataIndex: 'tendencyCompereType',
      align: 'center',
      render: (text, record) => {
        if (record.tendencyCompereType === 'video') {
          return '视频'
        } else if (record.tendencyCompereType === 'audio') {
          return '音频'
        }
      }
    },
    { title: '操作人', dataIndex: 'lastOperator', align: 'center' },
    {
      title: '历史记录',
      align: 'center',
      fixed: 'right',
      exportIgnore: true,
      render: (text, record) => (
        <span>
          <a onClick={this.showremarkHistoryModal(record)}>查询</a>
        </span>)
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      render: (text, record) => (
        <span>
          <a onClick={this.showremarkModal(record)}>备注</a>
          <Divider type='vertical' />
          <a onClick={() => this.onRefreshCompereInfoClick(record)}>刷新</a>
        </span>)
    }
  ]

  state = {
    business: businessTypeJY,
    pageSize: 10,
    remarkModalVisible: false,
    currentBusiness: 1,
    currentIndex: 1,
    remarkHistoryModalVisible: false,
    remarkHistoryCurrentUID: 0,
    remarkHistoryCurrentNick: '',
    readyOpUid: 0,
    searchResult: [],
    searchDone: false
  }

  showDetailModal = (record) => () => {
    this.props.dispatch({
      type: `${namespace}/getCompereDetailInfo`,
      payload: { uid: record.uid, useSaveInfoIfNotContract: false }
    }).then(() => {
      this.setState({ visibleDetail: true })
    })
  }

  initState = () => {
    this.state = { readyOpUid: 0, searchResult: [], searchDone: false }
  }

  pageValue = () => {
    return {
      pageSize: this.state.pageSize,
      total: this.props.model.totalSize,
      current: this.state.currentIndex,

      onChange: (page, size) => {
        this.pageChange(page, size)
      },
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    }
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100'],
    showSizeChanger: true,
    isUpdate: false,
    onChange: (page, size) => {
      this.setState({ selectedRowKeys: null })
    },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  pageChange = (page, pageSize) => {
    this.refreshInfo(this.state.currentBusiness, page, pageSize)
  }

  onTagChange = (idx) => {
    console.log(idx)
    if (idx === '1') {
      this.refreshInfo(businessTypeJY, 1)
      this.setState({ business: businessTypeJY, currentBusiness: businessTypeJY })
    } else if (idx === '2') {
      this.refreshInfo(businessTypePK, 1)
      this.setState({ business: businessTypePK, currentBusiness: businessTypePK })
    } else if (idx === '3') {
      this.refreshInfo(businessTypeBABY, 1)
      this.setState({ business: businessTypeBABY, currentBusiness: businessTypeBABY })
    } else if (idx === '4') {
      this.refreshH5WhiteList(true)
      this.setState({ business: businessTypeReadySuperJY, currentBusiness: businessTypeReadySuperJY })
    }
  }

  // 获取超级主持白名单列表数据
  refreshH5WhiteList = (readySuper = true) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getReadySuperCompereList`
    })
  }

  refreshInfo = (business, pageIndex, pageSize) => {
    if (!pageIndex) {
      pageIndex = 1
    }
    if (!pageSize) {
      pageSize = this.state.pageSize
    }
    this.setState({ currentIndex: pageIndex, pageSize: pageSize })
    let data = this.getQueryParams(business, pageIndex, pageSize)
    this.props.dispatch({
      type: `${namespace}/listCompere`,
      payload: data
    })
  }

  filterNumberArray = (value) => {
    if (!value) {
      return ''
    }
    value = value.replace(/([^\d]|[\s，, ])+/g, ',')
    if (value.endsWith(',')) {
      value = value.substring(0, value.length - 1)
    }
    return value
  }

  getQueryParams = (business, page, size) => {
    let params = {}

    if (business === businessTypeJY) {
      const { searchUID1, searchYY1, searchASID1, searchRealSex1, searchRemarkSex1, searchIfNewContract1, searchSilenceType, searchCompereType1, searchMediaType1, searchPrivilege1, searchJoinGroup1, searchNick1, searchSuperLevel } = this.state
      params = {
        ds: 'super_jy',
        page: page,
        size: size,
        uid: searchUID1,
        yyno: searchYY1,
        asid: searchASID1,
        gender: searchRealSex1,
        remarkGender: searchRemarkSex1,
        newCompere: searchIfNewContract1,
        silenceType: searchSilenceType,
        compereType: searchCompereType1,
        tendencyCompereType: searchMediaType1,
        privilege: searchPrivilege1,
        joinGroup: searchJoinGroup1,
        nick: searchNick1,
        superLevel: searchSuperLevel
      }
    } else if (business === businessTypePK) {
      const { searchUID2, searchYY2, searchASID2, searchRealSex2, searchRemarkSex2, searchIfNewContract2, searchCompereType2, searchMediaType2, searchNick2 } = this.state
      params = {
        ds: 'super_yz',
        page: page,
        size: size,
        uid: searchUID2,
        yyno: searchYY2,
        asid: searchASID2,
        gender: searchRealSex2,
        remarkGender: searchRemarkSex2,
        newCompere: searchIfNewContract2,
        compereType: searchCompereType2,
        tendencyCompereType: searchMediaType2,
        nick: searchNick2
      }
    } else if (business === businessTypeBABY) {
      const { searchUID3, searchYY3, searchASID3, searchRealSex3, searchRemarkSex3, searchIfNewContract3, searchCompereType3, searchMediaType3, searchNick3 } = this.state
      params = {
        ds: 'super_bb',
        page: page,
        size: size,
        uid: searchUID3,
        yyno: searchYY3,
        asid: searchASID3,
        gender: searchRealSex3,
        remarkGender: searchRemarkSex3,
        newCompere: searchIfNewContract3,
        compereType: searchCompereType3,
        tendencyCompereType: searchMediaType3,
        nick: searchNick3
      }
    }
    // 添加签约时间条件时间
    params = Object.assign(params, this.getSignTimeQuery(business))
    params.uid = this.filterNumberArray(params.uid)
    params.yyno = this.filterNumberArray(params.yyno)
    return params
  }

  getQueryURI = (page, size) => {
    let params = this.getQueryParams(this.state.currentBusiness, page, size)
    return `/compere_data_base/list_compere?${stringify(params)}`
  }

  hideremarkModal = () => {
    this.setState({ remarkModalVisible: false })
  }

  saveUpdateRemarkInfoFormRef = (info) => {
    this.formRefRemarkInfo = info
  }

  handleUpdateRemarkInfoSubmit = e => {
    this.formRefRemarkInfo.submit()
  }

  hideCancelSuperModal = () => {
    if (this.formRefCancelSuper) {
      this.formRefCancelSuper.resetFields()
    }
    this.setState({ cancelSuperModalVisible: false })
  }

  saveCancelSuperFormRef = (info) => {
    this.formRefCancelSuper = info
  }

  handleCancelSuperSubmit = e => {
    this.formRefCancelSuper.submit()
  }

  getDsType = (businessType) => {
    switch (businessType) {
      case '1':
        return 'super_jy'
      case '2':
        return 'super_yz'
      case '3':
        return 'super_bb'
      default:
        return 'super_jy'
    }
  }

  // 刷新信息
  onRefreshCompereInfoClick = (item) => {
    const { business } = this.state
    const { dispatch } = this.props
    let data = { uids: '' + item.uid, ds: this.getDsType(business) }
    console.log(data, item)
    dispatch({
      type: `${namespace}/refreshCompereList`,
      payload: data
    })
  }

  showCancelSuperModal = (record) => () => {
    if (this.formRefCancelSuper) this.formRefCancelSuper.setFieldsValue({ uids: [record.uid], count: 1 })
    this.setState({ cancelSuperModalVisible: true })
  }

  showCancelSuperSelectModal = () => () => {
    const { selectCancelSuperUids } = this.state
    console.log(selectCancelSuperUids)
    if (!Array.isArray(selectCancelSuperUids) || selectCancelSuperUids.length === 0) {
      message.warn('请选择要取消的uid')
      return
    }
    if (this.formRefCancelSuper) {
      this.formRefCancelSuper.setFieldsValue({ uids: selectCancelSuperUids, count: selectCancelSuperUids.length })
    }
    this.setState({ cancelSuperModalVisible: true })
  }

  showremarkModal = (record) => () => {
    console.log('======>  ', record)
    if (record == null) record = {}

    if (record.remarkGender !== 0 && record.remarkGender !== 1) {
      record.remarkGender = -1
    }
    if (record.remarkNewCompere !== 0 && record.remarkNewCompere !== 1) {
      record.remarkNewCompere = -1
    }
    if (record.superLevel === undefined || record.superLevel === 0) {
      record.superLevel = 4
    }
    this.formRefRemarkInfo.resetFields()
    this.formRefRemarkInfo.setFieldsValue(record)

    let self = this
    this.getRemarkInfo(record.uid, () => {
      self.setState({ remarkModalVisible: true })
    })
  }

  saveRemarkHistoryFormRef = (info) => {
    this.formRefRemarkHistory = info
  }

  hideremarkHistoryModal = () => {
    this.setState({ remarkHistoryModalVisible: false })
  }

  showremarkHistoryModal = (record) => () => {
    if (record == null) record = {}

    this.formRefRemarkHistory.resetFields()
    this.formRefRemarkHistory.setFieldsValue(record)
    this.getRemarkHistory(record.uid)
    this.setState({ remarkHistoryModalVisible: true, remarkHistoryCurrentUID: record.uid, remarkHistoryCurrentNick: record.nick })
  }

  getRemarkInfo = (uid, callback) => {
    const { dispatch } = this.props
    let cbFunc = (record) => {
      let info = {}
      if (record.id) {
        info = { remarkNick: record.nick, remarkOpt: record.remarkOpt, remarkGender: record.gender, remarkNewCompere: record.newCompere, remarkAVType: record.avType }
      }
      if (info.remarkGender !== 0 && info.remarkGender !== 1) {
        info.remarkGender = -1
      }
      if (info.remarkNewCompere !== 0 && info.remarkNewCompere !== 1) {
        info.remarkNewCompere = -1
      }
      this.formRefRemarkInfo.setFieldsValue(info)
      if (callback) {
        callback(info)
      }
    }

    let data = { from: 2, business: this.state.currentBusiness, uid: uid, cbFunc: cbFunc }
    dispatch({
      type: `${namespace}/getRemarkInfo`,
      payload: data
    })
  }

  setRemarkInfo = (v) => {
    const { dispatch } = this.props
    let cbFunc = () => {
      this.refreshInfo(this.state.currentBusiness, this.state.currentIndex)
    }

    var data = {
      uid: v.uid,
      business: this.state.currentBusiness,
      from: 2,
      gender: v.remarkGender,
      nick: v.remarkNick,
      remarkOpt: v.remarkOpt,
      avType: v.remarkAVType,
      newCompere: v.remarkNewCompere,
      superLevel: v.superLevel,
      cbFunc: cbFunc
    }

    console.log(data)
    dispatch({
      type: `${namespace}/updateRemarkInfo`,
      payload: data
    })
    this.setState({ remarkModalVisible: false })
  }

  cancelSuperHandler = (v) => {
    let data = { uids: v.uids, reason: v.reason }

    console.log(data)
    this.props.dispatch({
      type: `${namespace}/cancelSuperCompere`,
      payload: data
    })
    this.hideCancelSuperModal()
  }

  getRemarkHistory = (uid) => {
    const { dispatch } = this.props

    var data = { from: 2, business: this.state.currentBusiness, uid: uid }
    dispatch({
      type: `${namespace}/getRemarkHistory`,
      payload: data
    })
  }

  liveDaysFormat = (liveDays) => {
    if (liveDays > 90) {
      return '90+'
    }
    return '' + liveDays
  }

  // 清空输入表单(黑/白)
  initForm = (readySuper = true) => {
    this.initState()
  }

  /**
   * 修改特权
   */
  onModSuperCompereWhite = (uid, privilege) => () => {
    const { dispatch } = this.props
    let cbFunc = () => {
      this.refreshInfo(this.state.currentBusiness, this.state.currentIndex)
    }

    dispatch({
      type: `${namespace}/modSuperCompereWhite`,
      payload: { uid: uid, privilege: privilege, cbFunc: cbFunc }
    })
  }

  /**
   * 升级成正式的超级主持
   */
  onUpgradeAsRealCompere = (record) => {
    const { dispatch } = this.props
    const { uid } = record
    dispatch({
      type: `${namespace}/upgradeAsSuperCompere`,
      payload: { uid: uid, readySuper: true }
    })
  }

  // 确认删除选中的白名单(黑/白)
  onDelConfirm = (record, readySuper = false) => {
    const { dispatch } = this.props
    const { uid } = record
    const search = this.handleSearch
    dispatch({
      type: `${namespace}/delWhiteList`,
      payload: {
        uid: uid,
        readySuper: readySuper,
        callback: function () {
          setTimeout(function () {
            search()
          }, 1000)
        }
      }
    })
  }

  // 准超级主持白名单 操作渲染
  readerReadySuperWhiteListHtml = (record) => {
    return (
      <>
        <Popconfirm placement='bottom' title='确认升级吗？' okType='danger' okText='确定' cancelText='取消' onConfirm={() => this.onUpgradeAsRealCompere(record)}>
          <a href='#'><Button type='primary'>升级正式超主</Button></a>
        </Popconfirm>

        <Button type='link' danger onClick={() => window.open('/admin/super_compere_detail_info_list.html?uid=' + record.uid)}>{`前往编辑资料>>`}</Button>
        <Popconfirm placement='bottom' title='确认废了他吗？' okType='danger' okText='确定' cancelText='取消' onConfirm={() => this.onDelConfirm(record, true)}>
          <a href='#'><Button danger type='primary'>废了他</Button></a>
        </Popconfirm>
      </>
    )
  }

  // 点击添加准超级主持按钮
  onAddBtnClick = (readySuper = true) => {
    const { dispatch } = this.props
    const { readyOpUid } = this.state
    dispatch({
      type: `${namespace}/addWhiteList`,
      payload: {
        uid: readyOpUid,
        readySuper: readySuper,
        callback: this.initForm
      }
    })
  }

  // 修改state中某个成员到特定值
  changeState = (name, newValue) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/updateState`,
      payload: { name: name, newValue: newValue }
    })
  }

  pageChange1 = (pagination, readySuper = false) => {
    const { current, pageSize } = pagination

    this.changeState('currentPage2', current)
    this.changeState('currentSize2', pageSize)
  }

  // search
  handleSearch = (opts) => {
    const { model: { displayData } } = this.props
    const { keyword, liveState, privilegeState, labelType, asidKw } = Object.assign(this.state, opts)

    let iLiveState = parseInt(liveState || -1, 10)
    let iPrivilegeState = parseInt(privilegeState || -1, 10)
    let iAsidKw = parseInt(asidKw || 0, 10)
    // console.log('handleSearch, keyword:', keyword, ', liveState:', iLiveState, ', privilegeState:', iPrivilegeState, ', labelType:', labelType, ', asidKw:', iAsidKw)

    let dataSource = displayData
    if (!keyword && iLiveState === -1 && iPrivilegeState === -1 && labelType === 'all' && iAsidKw === 0) {
      this.setState({ searchDone: false })
      return
    }
    if (keyword) {
      dataSource = dataSource.filter(v => {
        let longStr = v.uid + '_' + v.asid + '_' + v.sid
        if (longStr.indexOf(keyword) >= 0) {
          return true
        }
        return false
      })
    }
    if (iLiveState !== -1) {
      let pIsNotLive = iLiveState === 0
      dataSource = dataSource.filter(v => {
        let notLive = !v['is_live']
        return notLive === pIsNotLive
      })
    }
    if (iAsidKw !== 0) {
      dataSource = dataSource.filter(v => v.asid === iAsidKw)
    }
    if (iPrivilegeState !== -1) {
      dataSource = dataSource.filter(v => {
        let privilege = v['privilege_stat']
        if (typeof privilege === 'number') {
          if (privilege === 1 && iPrivilegeState === 1) {
            return true
          }
          if ((privilege & 2) > 0 && iPrivilegeState === 2) {
            return true
          }
          return privilege === 0 && iPrivilegeState === 0
        }
        return iPrivilegeState === 0
      })
    }

    if (labelType !== 'all') {
      dataSource = dataSource.filter(v => {
        if (labelType === 'mz' && v.hatking === true) {
          return true
        }
        if (labelType === 'gy' && v.public === true) {
          return true
        }
        return false
      })
    }

    this.setState(Object.assign({ searchResult: dataSource, searchDone: true }, opts))
  }

  handleCancelDetailNew = (v) => {
    this.setState({ visibleDetail: v })
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(selectedRows)
      let uids = []
      selectedRows.forEach(v => {
        uids.push(v.uid)
      })
      this.setState({ selectCancelSuperUids: uids })
    }
  }

  htmlJY = () => {
    const { visibleDetail } = this.state
    const { model: { list, compereDetailInfo } } = this.props

    let pagination = this.pageValue()
    return (<>
      <Row style={{ marginBottom: '1em' }}>
        <Col offset={0}>
          <span>
            <div style={{ marginBottom: 5 }}>
              <span style={{ marginLeft: 15 }}>UID</span>
              <Input placeholder='可输入多个,如：50016575 1621064462' onChange={e => this.setState({ searchUID1: e.target.value })} style={{ width: 550, marginLeft: 3 }} allowClear />
              <span style={{ marginLeft: 15 }}>YY号</span>
              <Input placeholder='可输入多个,如：909015575 1836993309' onChange={e => this.setState({ searchYY1: e.target.value })} style={{ width: 400, marginLeft: 3 }} allowClear />
              <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle1(businessTypeJY)}>查询</Button>
              <ExportRemoteData buttonStyle={{ marginLeft: 20 }} filename={'超级/独家主持名单_交友'} columns={this.columnsJy} uriBuilder={this.getQueryURI} />

              <Button style={{ marginLeft: 20 }} type='primary' onClick={this.showCancelSuperSelectModal()}>批量取消超主身份</Button>
            </div>
            <span style={{ marginLeft: 15 }}>短位ID</span>
            <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID1: e })} style={{ width: 120, marginLeft: 3 }} />
            <span style={{ marginLeft: 15 }}>主持身份</span>
            <Select style={{ width: 120, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchCompereType1: v })} allowClear>
              {jyCompereType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
            </Select>
            <span style={{ marginLeft: 15 }}>实名性别</span>
            <Select style={{ width: 120, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchRealSex1: v })} allowClear>
              {sex.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
            </Select>
            {/* <span style={{ marginLeft: 15 }}>备注性别</span> */}
            {/* <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchRemarkSex1: v })} allowClear> */}
            {/*  {remarkSex.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))} */}
            {/* </Select> */}
            <span style={{ marginLeft: 15 }}>音视频</span>
            <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchMediaType1: v })} allowClear>
              {mediaType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
            </Select>
            {/* <span style={{ marginLeft: 15 }}>是否新签</span> */}
            {/* <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchIfNewContract1: v })} allowClear> */}
            {/*  {contractType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))} */}
            {/* </Select> */}
            <span style={{ marginLeft: 15 }}>沉默主持</span>
            <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchSilenceType: v })} allowClear>
              <Option key={0} value={0}>全部</Option>
              <Option key={1} value={1}>近期活跃</Option>
              <Option key={2} value={2}>轻度沉默</Option>
              <Option key={3} value={3}>中度沉默</Option>
              <Option key={4} value={4}>重度沉默</Option>
            </Select>
            <span style={{ marginLeft: 15 }}>等级</span>
            <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchSuperLevel: v })} allowClear>
              <Option value={1}>S</Option>
              <Option value={2}>A</Option>
              <Option value={3}>B</Option>
              <Option value={4}>C</Option>
            </Select>
            <div style={{ marginTop: 5 }}>
              <span style={{ marginLeft: 15 }}>昵称</span>
              <Input placeholder='请输入' onChange={e => this.setState({ searchNick1: e.target.value })} style={{ width: 120, marginLeft: 3 }} allowClear />

              <span style={{ marginLeft: 15 }}>有无特权</span>
              <Select style={{ width: 120, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchPrivilege1: v })} allowClear>
                {privilegeType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>
              {/* <span style={{ marginLeft: 15 }}>加入厅？</span> */}
              {/* <Select style={{ width: 120, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchJoinGroup1: v })} allowClear> */}
              {/*  {joinGroupType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))} */}
              {/* </Select> */}
              <span style={{ marginLeft: 15 }}>签约开始时间</span>
              <DatePicker
                format='YYYY-MM-DD'
                placeholder='开始时间'
                value={this.state.begSignStartTime1}
                onChange={(v) => {
                  this.setState({ begSignStartTime1: v })
                }}
                style={{ marginLeft: 5 }}
              />
              <span style={{ marginLeft: 5 }}>~</span>
              <DatePicker
                format='YYYY-MM-DD'
                placeholder='结束时间'
                value={this.state.endSignStartTime1}
                onChange={(v) => this.setState({ endSignStartTime1: v })}
                style={{ marginLeft: 5 }}
              />
              <span style={{ marginLeft: 15 }}>签约结束时间</span>
              <DatePicker
                format='YYYY-MM-DD'
                placeholder='开始时间'
                value={this.state.begSignEndTime1}
                onChange={(v) => {
                  this.setState({ begSignEndTime1: v })
                }}
                style={{ marginLeft: 5 }}
              />
              <span style={{ marginLeft: 5 }}>~</span>
              <DatePicker
                format='YYYY-MM-DD'
                placeholder='结束时间'
                value={this.state.endSignEndTime1}
                onChange={(v) => this.setState({ endSignEndTime1: v })}
                style={{ marginLeft: 5 }}
              />
            </div>
          </span>
        </Col>
      </Row>
      <Row style={{ marginBottom: '1em' }}>
        <Col offset={0}>
          <div>
            <div>说明：</div>
            <div>1、展示当前签约交友/约战/宝贝，且主持身份为超级的名单，每10min更新一次（数据源：交友-Boss_主持全量信息库、约战-Zbase-签约记录tab-业务VIPPK、宝贝-游戏宝贝运营管理后台-游戏宝贝tab-王牌主播）</div>
            <div>2、音视频：若主持在才艺主持名单，则为视频主持；否则为音频主持</div>
            <div>3、运营备注昵称：备注昵称用于精彩世界-热门直播/交友速配tab、hgame首页排行榜 的拉取名单，请谨慎填写</div>
          </div>
          <Table style={{ marginTop: 10 }} rowSelection={this.rowSelection} tabelLayout='fixed' rowKey={(record, index) => index} bordered dataSource={list} columns={this.wrapTableColumns(this.columnsJy)} pagination={pagination} scroll={{ x: 'max-content' }} />
        </Col>
      </Row>

      <CompereDetailInfo visibleDetail={visibleDetail} record={compereDetailInfo} cancelHandler={this.handleCancelDetailNew} />
    </>
    )
  }

  htmlPK = () => {
    const { model: { list } } = this.props

    let pagination = this.pageValue()
    return (
      <>
        <Row style={{ marginBottom: '1em' }}>
          <Col offset={0}>
            <span>
              <div style={{ marginBottom: 5 }}>
                <span style={{ marginLeft: 15 }}>UID</span>
                <Input placeholder='可输入多个,如：50016575 1621064462' onChange={e => this.setState({ searchUID2: e.target.value })} style={{ width: 550, marginLeft: 3 }} allowClear />
                <span style={{ marginLeft: 15 }}>YY号</span>
                <Input placeholder='可输入多个,如：909015575 1836993309' onChange={e => this.setState({ searchYY2: e.target.value })} style={{ width: 550, marginLeft: 3 }} allowClear />
                <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle2(businessTypePK)}>查询</Button>
                <ExportRemoteData buttonStyle={{ marginLeft: 20 }} filename={'超级/独家主持名单_约战'} columns={this.columnsPKBaby} uriBuilder={this.getQueryURI} />
              </div>
              <span style={{ marginLeft: 15 }}>短位ID</span>
              <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID2: e })} style={{ width: 120, marginLeft: 3 }} />
              <span style={{ marginLeft: 15 }}>主持身份</span>
              <Select style={{ width: 120, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchCompereType2: v })} allowClear>
                {pkBabyCompereType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>
              <span style={{ marginLeft: 15 }}>实名性别</span>
              <Select style={{ width: 120, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchRealSex2: v })} allowClear>
                {sex.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>
              {/* <span style={{ marginLeft: 15 }}>备注性别</span> */}
              {/* <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchRemarkSex2: v })} allowClear> */}
              {/*  {remarkSex.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))} */}
              {/* </Select> */}
              <span style={{ marginLeft: 15 }}>音视频</span>
              <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchMediaType2: v })} allowClear>
                {mediaType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>
              {/* <span style={{ marginLeft: 15 }}>是否新签</span> */}
              {/* <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchIfNewContract2: v })} allowClear> */}
              {/*  {contractType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))} */}
              {/* </Select> */}
              <div style={{ marginTop: 5 }}>
                <span style={{ marginLeft: 15 }}>昵称</span>
                <Input placeholder='请输入' onChange={e => this.setState({ searchNick2: e.target.value })} style={{ width: 120, marginLeft: 3 }} allowClear />

                <span style={{ marginLeft: 15 }}>签约开始时间</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='开始时间'
                  value={this.state.begSignStartTime2}
                  onChange={(v) => {
                    this.setState({ begSignStartTime2: v })
                  }}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 5 }}>~</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='结束时间'
                  value={this.state.endSignStartTime2}
                  onChange={(v) => this.setState({ endSignStartTime2: v })}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 15 }}>签约结束时间</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='开始时间'
                  value={this.state.begSignEndTime2}
                  onChange={(v) => {
                    this.setState({ begSignEndTime2: v })
                  }}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 5 }}>~</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='结束时间'
                  value={this.state.endSignEndTime2}
                  onChange={(v) => this.setState({ endSignEndTime2: v })}
                  style={{ marginLeft: 5 }}
                />
              </div>
            </span>
          </Col>
        </Row>
        <Row style={{ marginBottom: '1em' }}>
          <Col offset={0}>
            <div>
              <div>说明：</div>
              <div>1、展示当前签约交友/约战/宝贝，且主持身份为超级的名单，每10min更新一次（数据源：交友-Boss_主持全量信息库、约战-Zbase-签约记录tab-业务VIPPK、宝贝-游戏宝贝运营管理后台-游戏宝贝tab-王牌主播）</div>
              <div>2、音视频：若主持在才艺主持名单，则为视频主持；否则为音频主持</div>
              <div>3、运营备注昵称：备注昵称用于精彩世界-热门直播/交友速配tab、hgame首页排行榜 的拉取名单，请谨慎填写</div>
            </div>
            <Table style={{ marginTop: 10 }} tabelLayout='fixed' rowKey={(record, index) => index} bordered dataSource={list} columns={this.wrapTableColumns(this.columnsPKBaby)} pagination={pagination} scroll={{ x: 1800 }} />
          </Col>
        </Row>
      </>
    )
  }

  htmlBaby = () => {
    const { model: { list } } = this.props

    let pagination = this.pageValue()
    return (
      <>
        <Row style={{ marginBottom: '1em' }}>
          <Col offset={0}>
            <span>
              <div style={{ marginBottom: 5 }}>
                <span style={{ marginLeft: 15 }}>UID</span>
                <Input placeholder='可输入多个,如：50016575 1621064462' onChange={e => this.setState({ searchUID3: e.target.value })} style={{ width: 550, marginLeft: 3 }} allowClear />
                <span style={{ marginLeft: 15 }}>YY号</span>
                <Input placeholder='可输入多个,如：909015575 1836993309' onChange={e => this.setState({ searchYY3: e.target.value })} style={{ width: 550, marginLeft: 3 }} allowClear />
                <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle3(businessTypeBABY)}>查询</Button>
                <ExportRemoteData buttonStyle={{ marginLeft: 20 }} filename={'超级/独家主持名单_宝贝'} columns={this.columnsPKBaby} uriBuilder={this.getQueryURI} />
              </div>
              <span style={{ marginLeft: 15 }}>短位ID</span>
              <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID3: e })} style={{ width: 120, marginLeft: 3 }} />
              <span style={{ marginLeft: 15 }}>主持身份</span>
              <Select style={{ width: 120, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchCompereType3: v })} allowClear>
                {pkBabyCompereType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>
              <span style={{ marginLeft: 15 }}>实名性别</span>
              <Select style={{ width: 120, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchRealSex3: v })} allowClear>
                {sex.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>
              {/* <span style={{ marginLeft: 15 }}>备注性别</span> */}
              {/* <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchRemarkSex3: v })} allowClear> */}
              {/*  {remarkSex.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))} */}
              {/* </Select> */}
              <span style={{ marginLeft: 15 }}>音视频</span>
              <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchMediaType3: v })} allowClear>
                {mediaType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>
              {/* <span style={{ marginLeft: 15 }}>是否新签</span> */}
              {/* <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchIfNewContract3: v })} allowClear> */}
              {/*  {contractType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))} */}
              {/* </Select> */}
              <div style={{ marginTop: 5 }}>
                <span style={{ marginLeft: 15 }}>昵称</span>
                <Input placeholder='请输入' onChange={e => this.setState({ searchNick3: e.target.value })} style={{ width: 120, marginLeft: 3 }} allowClear />

                <span style={{ marginLeft: 15 }}>签约开始时间</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='开始时间'
                  value={this.state.begSignStartTime3}
                  onChange={(v) => {
                    this.setState({ begSignStartTime3: v })
                  }}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 5 }}>~</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='结束时间'
                  value={this.state.endSignStartTime3}
                  onChange={(v) => this.setState({ endSignStartTime3: v })}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 15 }}>签约结束时间</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='开始时间'
                  value={this.state.begSignEndTime3}
                  onChange={(v) => {
                    this.setState({ begSignEndTime3: v })
                  }}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 5 }}>~</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='结束时间'
                  value={this.state.endSignEndTime3}
                  onChange={(v) => this.setState({ endSignEndTime3: v })}
                  style={{ marginLeft: 5 }}
                />
              </div>
            </span>
          </Col>
        </Row>
        <Row style={{ marginBottom: '1em' }}>
          <Col offset={0}>
            <div>
              <div>说明：</div>
              <div>1、展示当前签约交友/约战/宝贝，且主持身份为超级的名单，每10min更新一次（数据源：交友-Boss_主持全量信息库、约战-Zbase-签约记录tab-业务VIPPK、宝贝-游戏宝贝运营管理后台-游戏宝贝tab-王牌主播）</div>
              <div>2、音视频：若主持在才艺主持名单，则为视频主持；否则为音频主持</div>
              <div>3、运营备注昵称：备注昵称用于精彩世界-热门直播/交友速配tab、hgame首页排行榜 的拉取名单，请谨慎填写</div>
            </div>
            <Table style={{ marginTop: 10 }} tabelLayout='fixed' rowKey={(record, index) => index} bordered dataSource={list} columns={this.wrapTableColumns(this.columnsPKBaby)} pagination={pagination} scroll={{ x: 1800 }} />
          </Col>
        </Row>
      </>
    )
  }

  // ‘准白名单展示列表标签页’html代码 : 用户uid,短位频道,签约频道,开播状态,特权,特权操作,帽子超级,公用号
  displayReadySuperWhiteListHtml = () => {
    const { currentPage2, currentSize2 } = this.props.model
    let currentPage = currentPage2
    let currentSize = currentSize2
    const columns = [
      {
        title: '#',
        render: (text, record, index) => ((currentPage - 1) * currentSize + index + 1)
      },
      {
        title: '用户UID',
        dataIndex: 'uid',
        sorter: { compare: (a, b) => a.uid - b.uid }
      },
      {
        title: 'YY号',
        dataIndex: 'yyno'
      },
      {
        title: '昵称',
        dataIndex: 'nick',
        render: (val) => val === 0 ? '*' : val
      },
      {
        title: '短位频道',
        dataIndex: 'asid'
      },
      {
        title: '签约频道',
        dataIndex: 'sid',
        render: (val) => val === 0 ? '*' : val
      },
      {
        title: '准超主天数',
        dataIndex: 'live_days',
        render: (val) => this.liveDaysFormat(val)
      },
      {
        title: '是否达到要求',
        dataIndex: 'qualified',
        render: (qualified) => {
          return qualified ? '是' : '否'
        }
      },
      { title: '沉默主持', dataIndex: 'silenceType', align: 'center', render: (text, record) => (silenceTypeMap[record.silenceType]) },
      // { title: '实名通过',
      //   dataIndex: 'realAuth',
      //   render: (v) => {
      //     return v ? '通过' : '未通过'
      //   }
      // },
      // { title: '当月流水',
      //   dataIndex: 'currentMonthCurrency'
      // },
      // { title: '近7天日均开播时长(分钟)',
      //   dataIndex: 'recently7DayLiveDuration',
      //   render: (v) => {
      //     return parseInt(v / (60 * 7))
      //   }
      // },
      { title: '操作', render: (record) => this.readerReadySuperWhiteListHtml(record) }
    ]
    const { readyDisplayData } = this.props.model
    // console.log(readyDisplayData)

    return (
      <>
        <Row style={{ marginBottom: '1em' }}>
          <Col>
            待添加的UID: <Input placeholder='待添加的UID' onChange={e => {
              this.setState({ readyOpUid: e.target.value })
            }} style={{ width: 200, marginLeft: 10 }} />
            <Button type='primary' style={{ marginLeft: 10 }} onClick={e => {
              this.onAddBtnClick(true)
            }}>添加准超级主持</Button>
          </Col>
        </Row>
        <Row>
          <Col span={24}>
            <Table columns={columns}
              dataSource={readyDisplayData}
              size='small'
              pagination={tableStyle}
              onChange={(pagination) => {
                this.pageChange1(pagination, true)
              }}
              showSorterTooltip={false}
              rowKey={record => record.uid}
            />
          </Col>
        </Row>
      </>
    )
  }

  searchHandle1 = () => () => {
    this.refreshInfo(businessTypeJY, 1)
  }

  searchHandle2 = () => () => {
    this.refreshInfo(businessTypePK, 1)
  }

  searchHandle3 = () => () => {
    this.refreshInfo(businessTypeBABY, 1)
  }

  render = () => {
    const { model: { totalSize, remarkHistoryRecords } } = this.props
    const { readyDisplayDataLength } = this.props.model
    const { remarkHistoryCurrentUID, remarkHistoryCurrentNick } = this.state
    const { route } = this.props
    const { TabPane } = Tabs

    let jyTab = '交友'
    if (this.state.currentBusiness === businessTypeJY) {
      jyTab = '交友（' + totalSize + ')'
    }

    let pkTab = '约战'
    if (this.state.currentBusiness === businessTypePK) {
      pkTab = '约战（' + totalSize + ')'
    }

    let babyTab = '宝贝'
    if (this.state.currentBusiness === businessTypeBABY) {
      babyTab = '宝贝（' + totalSize + ')'
    }

    // console.log(readyDisplayDataLength)
    let readySuperJyTab = '交友准超主'
    if (this.state.currentBusiness === businessTypeReadySuperJY) {
      readySuperJyTab = '交友准超主（' + readyDisplayDataLength + ')'
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='1' onChange={(idx) => this.onTagChange(idx)} type='card' size='large'>
            <TabPane tab={jyTab} key='1'>
              {this.htmlJY()}
            </TabPane>
            <TabPane tab={pkTab} key='2'>
              {this.htmlPK()}
            </TabPane>
            <TabPane tab={babyTab} key='3'>
              {this.htmlBaby()}
            </TabPane>
            <TabPane tab={readySuperJyTab} key='4'>
              {this.displayReadySuperWhiteListHtml()}
            </TabPane>
          </Tabs>
        </Card>

        <Modal forceRender width={300} visible={this.state.remarkModalVisible} title='修改备注' onCancel={this.hideremarkModal} onOk={this.handleUpdateRemarkInfoSubmit} okText='提交' cancelText='取消'>
          <Form ref={this.saveUpdateRemarkInfoFormRef} onFinish={(v) => {
            this.setRemarkInfo(v)
          }}>
            <FormItem label='YY  号' name='yyno'>
              <Input disabled='true' />
            </FormItem>
            <FormItem label='备注昵称' name='remarkNick'>
              <Input />
            </FormItem>
            <FormItem name='uid' hidden>
              <Input />
            </FormItem>
            <FormItem hidden={!(this.state.currentBusiness === businessTypeJY)} label='等级' name='superLevel'>
              <Select >
                <Option value={1}>S</Option>
                <Option value={2}>A</Option>
                <Option value={3}>B</Option>
                <Option value={4}>C</Option>
              </Select>
            </FormItem>
            <FormItem label='运营备注' name='remarkOpt'>
              <TextArea showCount allowClear />
            </FormItem>
          </Form>
        </Modal>

        <Modal forceRender footer={null} width={1300} visible={this.state.remarkHistoryModalVisible} title='历史记录' onCancel={this.hideremarkHistoryModal}>
          <Form ref={this.saveRemarkHistoryFormRef}>
            <span style={{ marginLeft: 15 }}>UID</span>
            <Input min={0} style={{ width: 120, marginLeft: 3 }} value={remarkHistoryCurrentUID} />
            <span style={{ marginLeft: 15 }}>昵称</span>
            <Input min={0} style={{ width: 240, marginLeft: 3 }} value={remarkHistoryCurrentNick} />

            <Table style={{ marginTop: 10 }} tabelLayout='fixed' rowKey={(record, index) => index} bordered dataSource={remarkHistoryRecords} columns={this.remarkHistoryColumns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
          </Form>
        </Modal>

        <Modal forceRender width={300} visible={this.state.cancelSuperModalVisible} title='取消超主身份' onCancel={this.hideCancelSuperModal} onOk={this.handleCancelSuperSubmit} okText='确认并提交' cancelText='取消'>
          <Form ref={this.saveCancelSuperFormRef} onFinish={(v) => {
            this.cancelSuperHandler(v)
          }}>
            <FormItem label='UID列表' name='uids' hidden >
              <Input disabled />
            </FormItem>
            <FormItem label='UID处理个数' name='count' >
              <Input disabled />
            </FormItem>
            <FormItem label='取消原因' name='reason' rules={[{ required: true }]}>
              <Input />
            </FormItem>
          </Form>
        </Modal>

      </PageHeaderWrapper>
    )
  }
}

export default SupperCompere
