import { getUnauthMenuAndItem, authApply } from './api'
import { message } from 'antd'

export default {
  namespace: 'batchAuthApply',

  state: {
    menuList: [],
    uriList: []
  },

  reducers: {
    saveMenuList (state, { payload }) {
      return {
        ...state,
        menuList: payload
      }
    },
    saveUriList (state, { payload }) {
      return {
        ...state,
        uriList: payload
      }
    }
  },

  effects: {
    * fetchMenuList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getUnauthMenuAndItem)
      // console.log(menuList)
      yield put({
        type: 'saveMenuList',
        payload: Array.isArray(list) ? list : []
      })
    },
    * submit ({ payload }, { call }) {
      const { data: { status, msg } } = yield call(authApply, payload)
      if (status === 0) {
        message.success('申请成功，请等待审核！')
        setTimeout(() => {
          window.location.reload()
        }, 1000)
      } else {
        message.error('提交失败！' + msg)
      }
    }
  }
}
