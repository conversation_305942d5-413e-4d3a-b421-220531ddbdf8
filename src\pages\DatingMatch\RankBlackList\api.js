import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/dating_match_bosssvr/op_get_rank_black_list?${stringify(params)}`)
}

export function add (params) {
  return request(`/dating_match_bosssvr/op_add_rank_black_list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function remove (params) {
  return request(`/dating_match_bosssvr/op_del_rank_black_list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
