import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
// import dateString from '@/utils/dateString'
import { Table, Form, Card, Modal, Input, Select } from 'antd'
import { connect } from 'dva'
const Option = Select.Option

var moment = require('moment')
const namespace = 'BeansPrizeConfig'
const FormItem = Form.Item

@connect(({ BeansPrizeConfig }) => ({
  PrizeConfig: BeansPrizeConfig
}))
class PrizeConfig extends Component {
  // column structs.
  columns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: '奖品名称', dataIndex: 'prizeName', key: 'prizeName', align: 'center' },
    { title: '营收ID', dataIndex: 'propsID', key: 'propsID', align: 'center' },
    { title: '礼物数量', dataIndex: 'count', key: 'count', align: 'center' },
    { title: '单价(紫水晶)', dataIndex: 'money', key: 'money', align: 'center' },
    { title: '概率', dataIndex: 'probability', key: 'probability', align: 'center' },
    { title: '首抽概率', dataIndex: 'firstDrawProbability', key: 'firstDrawProbability', align: 'center' },
    { title: '每日上限', dataIndex: 'limitCount', key: 'limitCount', align: 'center' },
    { title: '每日首抽上限', dataIndex: 'firstDrawLimitCount', key: 'firstDrawLimitCount', align: 'center' },
    { title: '大奖',
      dataIndex: 'isBigReward',
      key: 'isBigReward',
      align: 'center',
      render: (text, record) => {
        switch (text) {
          case false: return '否'
          case true: return '是'
        }
        return ''
      }
    },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(true, record)}>配置</a>
        </span>)
    }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {} }

  // show modal
  showModal = (isUpdate, record) => () => {
    var now = moment().unix()
    if (record == null) record = { key: '', desp: '', start: now, stop: now }

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.isBigReward = v.isBigReward == null ? false : v.isBigReward
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // add and update
  onFinish = values => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/updateItem`,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { key: key }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // search
  handleSearch = (selectedKeys, confirm) => () => {
    confirm()
    this.setState({ searchText: selectedKeys[0] })
  }

  // reset search info
  handleReset = clearFilters => () => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  // get list from server.
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`
    })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  // content
  render () {
    const { route, PrizeConfig: { list } } = this.props
    const { visible, isUpdate, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            (信号弹ID:10000001，谢谢参与ID:10000002) 配置新礼物请先与@吴熙确认是否支持，activityId=655
            <Table dataSource={list} rowKey={(record, index) => index} columns={this.columns} pagination={this.defaultPageValue} />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem label='编号' name='index' rules={[{ required: true }]}>
              <Input readOnly={isUpdate} />
            </FormItem>
            <FormItem label='奖品名称' name='prizeName'>
              <Input readOnly />
            </FormItem>
            <FormItem label='营收ID' name='propsID'>
              <Input />
            </FormItem>
            <FormItem label='礼物数量' name='count'>
              <Input />
            </FormItem>
            <FormItem label='概率' name='probability'>
              <Input />
            </FormItem>
            <FormItem label='每日首抽概率' name='firstDrawProbability'>
              <Input />
            </FormItem>
            <FormItem label='每日限额' name='limitCount'>
              <Input />
            </FormItem>
            <FormItem label='每日首抽限额' name='firstDrawLimitCount'>
              <Input />
            </FormItem>
            <FormItem label='是否是大奖' name='isBigReward' rules={[{ required: true }]}>
              <Select>
                <Option value>是</Option>
                <Option value={false}>否</Option>
              </Select>
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default PrizeConfig
