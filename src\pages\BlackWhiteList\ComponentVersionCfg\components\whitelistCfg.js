/* eslint-disable eqeqeq */
import React, { Component } from 'react'
import { connect } from 'dva'
import { timeFormater } from '@/utils/common'
import { Modal, Form, Input, Row, Col, Button, Table, Tooltip, message, Popconfirm, AutoComplete, Select, Alert } from 'antd'
import { SearchOutlined, FileAddOutlined, DeleteOutlined } from '@ant-design/icons'
import { SearchSelect } from '@/components/SimpleComponents'

const namespace = 'componentVersionCfg'

@connect(({ componentVersionCfg }) => ({
  model: componentVersionCfg
}))

class WhiteListCfg extends Component {
  state = {
    modelVisable: false,
    opTypeIsAdd: true,
    keyword: '',
    searchTKey: 'jy',
    searchCKey: '',
    searchSid: -1, // 小于0时表示全部
    searchEachPage: 200,
    searchType: -1,
    selectedRowKeys: [],
    warning: false
  }
  componentDidMount = () => {
    this.getWhitelistByFilter()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 修改、删除操作 html代码
  operationHtml = (record) => {
    const tipStr = `确认要删除这条白名单配置吗? (${record.appKey}:${record.cKey}:${record.version})`
    return (
      <>
        <a style={{ color: '#1890ff', fontSize: '1.2em', marginRight: '1em' }}
          onClick={() => {
            this.setState({ modelVisable: true, opTypeIsAdd: false, keyword: '' })
            setTimeout(() => {
              this.onSelectComponent(this.props.model.simpleComponentList, record.cKey, true)
              this.formRef.setFieldsValue(record)
            }, 500)
          }
          }>
          修改
        </a>
        <Tooltip title='删除'>
          <Popconfirm placement='bottom' title={tipStr}
            okType='danger' okText='删除' cancelText='取消' onConfirm={() => { this.submitDeleteRequire(record) }}>
            <a href='#' style={{ color: '#ff3535', fontSize: '1.2em' }}>删除</a>
          </Popconfirm>
        </Tooltip>
      </>
    )
  }
  // 提交新增或修改白名单请求
  submitAddOrUpdateRequire = (v) => {
    const { opTypeIsAdd } = this.state
    if (opTypeIsAdd) {
      v.opType = 'ADD'
      v.id = 0
    } else {
      v.opType = 'UPDATE'
    }
    v.key = v.cKey
    v.opUid = 0
    v.timestamp = 0
    if (!this.checkVersion(v)) {
      return
    }
    this.callModel('addOrUpdateWhiteListCfg', {
      params: v,
      cbFunc: (ok) => {
        if (ok) {
          message.success(opTypeIsAdd ? '新增成功' : '更新成功')
          this.formRef.resetFields()
          this.getWhitelistByFilter()
          this.setState({ modelVisable: false })
        } else {
          message.warn(opTypeIsAdd ? '新增' : '更新' + '失败，请稍后重试...')
        }
      } })
  }
  // 提交的限制条件，若组件中的版本url带有{ver}参数，则必须在新增白名单时指定版本
  checkVersion = (r) => {
    const { cKey, version } = r
    const { simpleComponentList } = this.props.model
    console.debug(cKey, version, simpleComponentList)
    let url = ''
    for (let i = 0; i < simpleComponentList.length; i++) {
      if (simpleComponentList[i].key == cKey) {
        url = simpleComponentList[i].url
        break
      }
    }
    if (url.indexOf('{ver}') >= 0 && version == '') {
      message.warn('组件地址包含版本变量“{ver}”, 白名单必需填写版本信息~')
      return false
    }
    return true
  }
  // 创建tooltip
  createTooltip = (v, width = 10) => {
    return !v ? '-' : <Tooltip title={v}>
      <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', maxWidth: `${width}em` }}>{v}</div>
    </Tooltip>
  }
  // 组件选择器数据源格式化
  optionsFormater = (list, keyword) => {
    // console.debug('list==>', list)
    if (!list) return []
    let newList = []
    for (let i = 0; i < list.length; i++) {
      newList[i] = { value: list[i].key, label: `${list[i].key} (${list[i].alias}) (${list[i].appKey}-${list[i].appName})`, disabled: !list[i].isValid }
    }
    if (typeof (keyword) == 'string' && keyword != '') {
      newList = newList.filter((v) => { return v.value.indexOf(keyword) >= 0 })
    }
    return newList.sort((a, b) => { return a.disabled ? 1 : -1 })
  }
  // 组件选择器选中组件后查询该组件的版本列表
  onSelectComponent = (list, keyword, isUpdate = false) => {
    let targetUrl = ''
    for (let i = 0; i < list.length; i++) {
      if (list[i].key == keyword) {
        targetUrl = list[i].versionUrl
        break
      }
    }
    if (targetUrl == '') {
      message.info('选中的组件没有指定获取版本的地址，请手动填写~')
    }
    this.callModel('getVersionListByVersionUrl', {
      params: { url: targetUrl },
      cbFunc: (v) => {
        if (isUpdate) return // 更新时不自动设置为最新版本
        this.formRef.setFieldsValue({ version: v })
      }
    })
  }
  // 将业务列表整理成自动完成下拉框所需的列表
  autoCompleteForamter = (list, keyword) => {
    if (!list) return []
    let newList = []
    for (let i = 0; i < list.length; i++) {
      newList[i] = { value: list[i].key, label: `${list[i].key} (${list[i].alias})` }
    }
    if (typeof (keyword) == 'string' && keyword != '') {
      newList = newList.filter((v) => { return v.value.indexOf(keyword) >= 0 })
    }
    return newList
  }
  // 根据输入的条件查询白名单列表
  getWhitelistByFilter = () => {
    let { searchTKey, searchCKey, searchSid, searchEachPage, searchType } = this.state
    if (searchSid == '') searchSid = -1
    if (searchCKey.length == 1) {
      message.warn('组件关键字最短两个字母~')
      return
    }
    this.callModel('selectWhiteListByCondition', {
      params: {
        appKey: searchTKey,
        cKeyword: searchCKey,
        sid: searchSid,
        limited: searchEachPage,
        type: searchType
      }
    })
  }
 // 提交单个删除白名单请求
 submitDeleteRequire = (v) => {
   this.callModel('batchDelteWhiteList', {
     params: { idList: `${v.key}` },
     cbFunc: (ok) => {
       if (ok) {
         message.success('删除成功')
         this.getWhitelistByFilter()
       } else {
         message.warn('删除失败，请稍后重试...')
       }
     } })
 }
// 批量删除白名单
onComfirmBatchDelete = () => {
  const { selectedRowKeys } = this.state
  let certain = window.confirm(`确认删除这${selectedRowKeys.length}条白名单吗?`)
  if (!certain) {
    return
  }
  this.callModel('batchDelteWhiteList', {
    params: { idList: selectedRowKeys.join(',') },
    cbFunc: (ok) => {
      if (ok) {
        message.success('删除成功')
        this.getWhitelistByFilter()
      } else {
        message.warn('删除失败，请稍后重试...')
      }
    } })
}

// 版本下拉框值发生变化
onVersionChange = (v) => {
  let warning = false
  if (ENV_TAG === 'prod' && /test/i.test(v)) {
    warning = true
  }
  this.setState({ warning })
}

render () {
  const { versionWhiteList, simpleComponentList, componentTypeList, verisonList } = this.props.model
  const { modelVisable, opTypeIsAdd, keyword, searchTKey, searchCKey, selectedRowKeys, warning } = this.state
  const { TextArea } = Input
  const { Option } = Select
  const rowSelection = {
    selectedRowKeys,
    onChange: (k) => {
      this.setState({ selectedRowKeys: k })
    }
  }
  const columns = [
    { title: '所属业务', dataIndex: 'tAlias', align: 'center', render: (v, r) => { return <Tooltip title={`${v} (key: ${r.tKey})`}>{v}</Tooltip> } },
    { title: '组件名称', dataIndex: 'cKey', align: 'center', render: (v, r) => { return <Tooltip title={`${v} (别名: ${r.CAlias})`}>{v}</Tooltip> } },
    { title: '组件版本', dataIndex: 'version', align: 'center', width: '13em', render: (v, r) => { return this.createTooltip(v, 12) } },
    { title: '频道白名单', dataIndex: 'sid', align: 'center', width: '14em', render: (v, r) => { return this.createTooltip(v, 13) } },
    { title: '备注', dataIndex: 'tag', align: 'center', width: '11em', render: (v, r) => { return this.createTooltip(v) } },
    { title: '更新人uid', dataIndex: 'opUid', align: 'center', width: '11em', render: (v, r) => { return this.createTooltip(v) } },
    { title: '更新时间', dataIndex: 'timestamp', align: 'center', render: (v) => { return timeFormater(v) } },
    { title: '操作', align: 'center', width: '10em', fix: 'right', render: (v, r) => { return this.operationHtml(r) } }
  ]

  return (
    <div>
      <Row style={{ marginBottom: '1em' }} >
        <Tooltip title='删除选中的白名单'>
          <Button danger disabled={selectedRowKeys.length === 0} onClick={(v) => { this.onComfirmBatchDelete() }}>
            <DeleteOutlined />批量删除{selectedRowKeys.length}项
          </Button>
        </Tooltip>
        <span style={{ marginLeft: '2em', marginRight: '2em', borderLeft: '1px #d7d7d7 solid' }} />
        <Tooltip title='根据指定业务筛选白名单 (完全匹配)'>
          <AutoComplete style={{ width: '12em', marginLeft: '1em' }} placeholder='选择指定业务'
            value={searchTKey}
            options={this.autoCompleteForamter(componentTypeList, searchTKey)}
            onChange={(v) => this.setState({ searchTKey: v })} />
        </Tooltip>
        <Tooltip title='根据组件筛选白名单 （模糊匹配）'>
          <AutoComplete style={{ width: '12em', marginLeft: '1em' }} placeholder='输入组件关键字'
            options={this.autoCompleteForamter(simpleComponentList, searchCKey)}
            onChange={(v) => this.setState({ searchCKey: v })} />
        </Tooltip>
        <Tooltip title='根据指定频道来筛选白名单 (完全匹配)'>
          <Input style={{ width: '12em', marginLeft: '1em' }} placeholder='输入需要查询的sid' onChange={(e) => { this.setState({ searchSid: e.target.value }) }} />
        </Tooltip>
        <Tooltip title='白名单类型'>
          <Select style={{ width: '8em', marginLeft: '1em' }} onChange={(v) => { this.setState({ searchType: v }) }} defaultValue={-1} >
            <Option key={-1} value={-1}>全部</Option>
            <Option key={0} value={0}>全量</Option>
            <Option key={1} value={1}>灰度</Option>
          </Select>
        </Tooltip>
        <Tooltip title='每页显示多少行'>
          <Select style={{ width: '8em', marginLeft: '1em' }} defaultValue={200} onChange={(v) => { this.setState({ searchEachPage: v }) }}>
            <Option key={20} value={20}>20</Option>
            <Option key={50} value={50}>50</Option>
            <Option key={100} value={100}>100</Option>
            <Option key={200} value={200}>200</Option>
            <Option key={0} value={0}>不限制</Option>
          </Select>
        </Tooltip>
        <Tooltip title='查询满足指定条件的白名单'>
          <Button type='primary' style={{ width: '6em', marginLeft: '1em' }} onClick={() => this.getWhitelistByFilter()}><SearchOutlined />搜索</Button>
        </Tooltip>
        <span style={{ marginLeft: '2em', marginRight: '2em', borderLeft: '1px #d7d7d7 solid' }} />
        <Button type='primary' onClick={() => {
          this.setState({ modelVisable: true, opTypeIsAdd: true, keyword: '' })
          setTimeout(() => { this.formRef.resetFields() }, 200)
        }} >
          <FileAddOutlined />新增白名单
        </Button>
      </Row>
      <Row>
        <Col span={24}>
          <Table rowSelection={rowSelection} columns={columns} dataSource={versionWhiteList} pagination={{ pageSize: 50 }} size='small' scroll={{ x: 'max-content' }} />
        </Col>
      </Row>

      {/* 新增或修改 */}
      <Modal visible={modelVisable} title={opTypeIsAdd ? '新增白名单' : '修改白名单'} okText={opTypeIsAdd ? '确认新增' : '确认修改'} cancelText='取消'
        onCancel={() => { this.setState({ modelVisable: false }) }} onOk={() => { this.formRef.submit() }} >
        <Form onFinish={(v) => { this.submitAddOrUpdateRequire(v) }} ref={(v) => { this.formRef = v }}
          labelCol={{ span: 6, align: 'left' }} initialValues={{ cKey: '', version: '', sid: '0', tag: '' }}>
          <Form.Item label='所属业务' name='tAlias' tooltip='当前组件所属的业务类型' hidden={opTypeIsAdd} >
            <Input disabled />
          </Form.Item>
          <Form.Item label='组件key' name='cKey' rules={[{ required: true, message: '业务key不能为空' }, { pattern: /^[a-zA-Z0-9][\w-_]{1,149}$/, message: '包含非法字符~' }]} tooltip='可由大小写字母、下划线、中划线组成'>
            <AutoComplete disabled={!opTypeIsAdd}
              options={this.optionsFormater(simpleComponentList, keyword)}
              onSelect={(v) => { this.onSelectComponent(simpleComponentList, v) }}
              onChange={(v) => { this.setState({ keyword: v }); this.callModel('clearVersionList'); this.formRef.setFieldsValue({ version: '' }) }}
            />
          </Form.Item>
          <Form.Item label='频道白名单' name='sid' rules={[{ required: true }]} tooltip='需要填写多个频道时请使用逗号分割,0表示全量'>
            {opTypeIsAdd ? <TextArea /> : <Input disabled />}
          </Form.Item>
          <Form.Item label='版本' name='version' rules={[{ required: false }, { pattern: /^^[\S]+$/, message: '不能包含空字符~' }]}>
            {
              verisonList.length == 0
                ? <Input placeholder='请手动填写版本~' onChange={e => this.onVersionChange(e.target.value)} />
                : <SearchSelect onChange={v => this.onVersionChange(v)} defaultValue={verisonList.length > 0 ? verisonList[0] : null} options={verisonList.map(item => { return { label: item, value: item } })} />
            }
          </Form.Item>
          <Form.Item label='修改备注' name='tag'>
            <Input />
          </Form.Item>
        </Form>
        {
          warning
            ? <div><Alert type='warning' message='当前为正式环境,选中的版本号可能存在风险,请谨慎操作' showIcon /></div>
            : ''
        }
      </Modal>

    </div>
  )
}
}

export default WhiteListCfg
