import React, { Component } from 'react'
import { Table, Divider, Button, Card, Form, DatePicker } from 'antd'
import exportExcel from '@/utils/exportExcel'
import { connect } from 'dva'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
const bizTypeMap = { 1: '魔豆', 2: '盖章', 3: '活动布料', 4: '活动布料盖章', 5: '布料', 6: '能量', 7: '常驻玩法盖章', 8: '活动玩法盖章' }

@connect(({ compereSummary }) => ({
  model: compereSummary
}))
class AllChannelTypeSummary extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
    this.setState()
  }

    columns = [
      { title: '日期', dataIndex: 'date', align: 'center' },
      { title: '模拟流水类型', dataIndex: 'kind', align: 'center', render: (text, record, index) => { return bizTypeMap[text] } },
      { title: '总模拟流水', dataIndex: 'totalAmount', align: 'center' },
      { title: '超级水晶公会流水占比', dataIndex: 'percent4', align: 'center' },
      { title: '水晶公会等级>=2公会流水占比', dataIndex: 'percent5', align: 'center' },
      { title: '水晶公会数量', dataIndex: 'guildTypeCount1', align: 'center' },
      { title: '水晶公会流水占比', dataIndex: 'percent1', align: 'center' },
      { title: '非水晶公会数量', dataIndex: 'guildTypeCount0', align: 'center' },
      { title: '非水晶公会流水占比', dataIndex: 'percent0', align: 'center' }
    ]

    pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

    loadData = () => {
      const { dispatch } = this.props
      const { dateRange } = this.state
      const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
      const { modelName } = this.props
      dispatch({
        type: `${modelName}/getChannelTypeSummaryList`,
        payload: data
      })
    }

    onQuery = () => {
      this.loadData()
    }

    onChange = (date, format) => {
      this.setState({ dateRange: date })
    }

    onStartChange = (value) => {
      this.onChange('startValue', value)
    }

    onEndChange = (value) => {
      this.onChange('endValue', value)
    }

    onExport = () => {
      const { model: { channelTypeSummaryList } } = this.props

      let headers = []
      this.columns.forEach(function (item) {
        headers.push({ key: item.dataIndex, header: item.title })
      })

      let fixList = this.fixDataSource(channelTypeSummaryList)

      var exportData = fixList.map(item => {
        let v = $.extend(true, {}, item)
        v.kind = bizTypeMap[v.kind]
        return v
      })

      exportExcel(headers, exportData)
    }

    fixDataSource = (before) => {
      if (!before) {
        return []
      }
      return before.filter(item => {
        return item.kind === 1
      })
    }

    /* *******************************页面布局***************************************************************/
    render () {
      const { model: { channelTypeSummaryList } } = this.props
      const { dateRange } = this.state
      return (
        <Card>
          <Form>
            <span style={{ marginLeft: 5 }}>时间范围:</span>
            <RangePicker style={{ marginLeft: 5 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onQuery}>查询</Button>
            <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
            <Divider />
            <Table dataSource={this.fixDataSource(channelTypeSummaryList)} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
          </Form>
        </Card>
      )
    }
}

export default AllChannelTypeSummary
