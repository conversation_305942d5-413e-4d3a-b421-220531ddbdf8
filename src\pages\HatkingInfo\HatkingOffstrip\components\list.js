/* eslint-disable no-unexpected-multiline */
import React, { Component } from 'react'
import { Card, Table, Form, message, Button, Popconfirm, Input, Space, InputNumber } from 'antd'
import { connect } from 'dva'
// import moment from 'moment'
import OffstripPropsComponent from './props'
// import DropQueryChart from './chart'

const namespace = 'offstripMain' // model 的 namespace

const defaultPropItem = { id: 1, prizeId: 101, prizeType: 1, propsId: 12, propsName: '爱心', count: 1, value: 100, probability1: 0, dailyLimit1: 0, probability2: 0, dailyLimit2: 0, probability3: 0, dailyLimit3: 0, probability4: 0, dailyLimit4: 0 }

@connect(({ offstripMain }) => ({ // model 的 namespace
  model: offstripMain // model 的 namespace
}))
class OffstripMainComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      pid: 1000,
      propVisible: false,
      visible: false, // 弹窗
      editing: false, // 编辑状态，奖池无法选择
      list: [] // 奖池
    }
  }

  /** ************************************组件初始化**************************************************/
  componentDidMount () {
    const { dispatch } = this.props
    const { pid } = this.state

    // 注册监听函数，在model的poolConfig发生更新时调用，用来更新当前组件的state的list
    dispatch({
      type: `${namespace}/listen`,
      payload: this.listenPoolConfigChange
    })

    // 刷新页面默认加载第一个奖池
    dispatch({
      type: `${namespace}/getPool`,
      payload: { id: pid }
    })
  }

  // model更新时，用于刷新当前state的list
  listenPoolConfigChange = poolConfig => {
    let list = []
    let totalLimit = 0
    let approvalDesc = ''
    let range1 = '[X1,X2)'; let range2 = '[X2,X3)'; let range3 = '[X3,X4)'; let range4 = '[X4,Infinity)'
    if (poolConfig !== undefined && poolConfig.content !== undefined) {
      try {
        if (poolConfig.content !== '') {
          list = JSON.parse(poolConfig.content)
          for (let i = 0; i < list.length; i++) {
            if (list[i].probability1 > 0) {
              list[i].probability1 /= 100
            }
            if (list[i].probability2 > 0) {
              list[i].probability2 /= 100
            }
            if (list[i].probability3 > 0) {
              list[i].probability3 /= 100
            }
            if (list[i].probability4 > 0) {
              list[i].probability4 /= 100
            }
          }
        }
      } catch (e) {
        message.error('content json convert error ', e)
      }
      approvalDesc = poolConfig.approvalDesc
      totalLimit = poolConfig.totalLimit
      range1 = '[' + poolConfig.rangeMin1 + ',' + poolConfig.rangeMax1 + ')'
      range2 = '[' + poolConfig.rangeMin2 + ',' + poolConfig.rangeMax2 + ')'
      range3 = '[' + poolConfig.rangeMin3 + ',' + poolConfig.rangeMax3 + ')'
      range4 = '[' + poolConfig.rangeMin4 + ',' + poolConfig.rangeMax4 + ')'
    }
    this.setState({ list: list, range1: range1, range2: range2, range3: range3, range4: range4, totalLimit: totalLimit, approvalDesc: approvalDesc })
  }

  /** ********************************Table定义************************************************* */
  columns = [
    {
      title: '奖池设定',
      dataIndex: 'propsInfo',
      children: [
        { title: '编号', align: 'center', dataIndex: 'id' },
        { title: '奖品ID', align: 'center', dataIndex: 'prizeId' },
        { title: '发奖ID', align: 'center', dataIndex: 'propsId' },
        { title: '奖品名称', align: 'center', dataIndex: 'propsName' },
        { title: '数量', align: 'center', dataIndex: 'count' },
        { title: '价值/紫水晶', align: 'center', dataIndex: 'value', render: (text, record) => (record.value * record.count).toLocaleString() }
      ]
    },
    {
      title: '[X1,X2)',
      dataIndex: 'range1',
      children: [
        { title: '中奖概率%', align: 'center', dataIndex: 'probability1' },
        { title: '投放上限/D', align: 'center', dataIndex: 'dailyLimit1' }
      ]
    },
    {
      title: '[X2,X3)',
      dataIndex: 'range2',
      children: [
        { title: '中奖概率%', align: 'center', dataIndex: 'probability2' },
        { title: '投放上限/D', align: 'center', dataIndex: 'dailyLimit2' }
      ]
    },
    {
      title: '[X3,X4)',
      dataIndex: 'range3',
      children: [
        { title: '中奖概率%', align: 'center', dataIndex: 'probability3' },
        { title: '投放上限/D', align: 'center', dataIndex: 'dailyLimit3' }
      ]
    },
    {
      title: '[X4,X5)',
      dataIndex: 'range4',
      children: [
        { title: '中奖概率%', align: 'center', dataIndex: 'probability4' },
        { title: '投放上限/D', align: 'center', dataIndex: 'dailyLimit4' }
      ]
    }
  ]

  renderMergeColumn (value, row, index) {
    const obj = {
      children: value,
      props: {}
    }

    if (row.rowSpan > 0) {
      obj.props.rowSpan = row.rowSpan
    } else {
      obj.props.rowSpan = 0 // merged into above cell
    }

    return obj
  }

  /** ************************************完成奖池编辑，提交到服务器************************************************ */
  onRangeInvalid = range => {
    if (range === '') {
      return false
    }
    return (!range.startsWith('\\[') || !range.endsWith('\\)'))
  }

  onSubmit = () => {
    const { model: { poolConfig }, dispatch } = this.props
    const { list, editing, totalLimit, range1, range2, range3, range4 } = this.state

    if (this.onRangeInvalid(range1) || this.onRangeInvalid(range2) ||
        this.onRangeInvalid(range3) || this.onRangeInvalid(range4)) {
      message.log('中奖金额区间输入有误！')
      return
    }

    let isStep1OK = true
    let isStep2OK = true
    let isStep3OK = true
    let isStep4OK = true
    let min1, max1
    let min2, max2
    let min3, max3
    let min4, max4
    let splits = range1.replace('[', '').replace(')', '').split(',')
    min1 = parseInt(splits[0])
    max1 = parseInt(splits[1])
    if (isNaN(min1) || isNaN(max1) || max1 === 0 || min1 > max1) {
      isStep1OK = false
    }

    splits = range2.replace('[', '').replace(')', '').split(',')
    min2 = parseInt(splits[0])
    max2 = parseInt(splits[1])
    if (isNaN(min2) || isNaN(max2) || min2 === 0 || max2 === 0 || min2 > max2) {
      isStep2OK = false
    }

    splits = range3.replace('[', '').replace(')', '').split(',')
    min3 = parseInt(splits[0])
    max3 = parseInt(splits[1])
    if (isNaN(min3) || isNaN(max3) || min3 === 0 || max3 === 0 || min3 > max2) {
      isStep3OK = false
    }

    splits = range4.replace('[', '').replace(')', '').split(',')
    min4 = parseInt(splits[0])
    max4 = parseInt(splits[1])
    if (isNaN(min4) || isNaN(max4) || min4 === 0 || max4 === 0 || min4 > max4) {
      isStep4OK = false
    }

    if (!isStep1OK && isStep2OK && isStep3OK && isStep4OK) {
      message.warn('阶段1配置错误')
      return
    }
    if ((!isStep1OK || !isStep2OK) && isStep3OK && isStep4OK) {
      message.warn('阶段1/2配置错误')
      return
    }
    if ((!isStep1OK || !isStep2OK || !isStep3OK) && isStep4OK) {
      message.warn('阶段1/2/3配置错误')
      return
    }

    if (max1 > min2 || max2 > min3 || max3 > min4) {
      message.warn('区间必须是递增')
      return
    }

    let uri = `${namespace}/updatePool`

    let probability1Total = 0
    let probability2Total = 0
    let probability3Total = 0
    let probability4Total = 0
    let ls = []
    list.forEach(item => {
      // 深拷贝
      let itemJson = JSON.stringify(item)
      let itemTmp = JSON.parse(itemJson)
      probability1Total += itemTmp.probability1
      probability2Total += itemTmp.probability2
      probability3Total += itemTmp.probability3
      probability4Total += itemTmp.probability4

      if (itemTmp.probability1 > 0) {
        itemTmp.probability1 *= 100
      }
      if (itemTmp.probability2 > 0) {
        itemTmp.probability2 *= 100
      }
      if (itemTmp.probability3 > 0) {
        itemTmp.probability3 *= 100
      }
      if (itemTmp.probability4 > 0) {
        itemTmp.probability4 *= 100
      }

      ls.push(itemTmp)
    })

    if (probability1Total > 100) {
      message.warn('第1阶段概率超过100%')
      return
    }
    if (probability2Total > 100) {
      message.warn('第2阶段概率超过100%')
      return
    }
    if (probability3Total > 100) {
      message.warn('第3阶段概率超过100%')
      return
    }
    if (probability4Total > 100) {
      message.warn('第4阶段概率超过100%')
      return
    }

    // parse to json string
    let v = $.extend({}, true, poolConfig)
    v.totalLimit = totalLimit
    v.rangeMin1 = min1
    v.rangeMax1 = max1
    v.rangeMin2 = min2
    v.rangeMax2 = max2
    v.rangeMin3 = min3
    v.rangeMax3 = max3
    v.rangeMin4 = min4
    v.rangeMax4 = max4
    v.content = JSON.stringify(ls) // to json

    console.log(v, list)

    dispatch({
      type: uri,
      payload: v
    })

    this.setState({ editing: !editing })
  }

  // 放弃更新
  onReset = () => {
    const { dispatch, model: { poolConfig } } = this.props
    dispatch({
      type: `${namespace}/getPool`,
      payload: { id: poolConfig.id }
    })

    this.editChange()
  }

  /** **********************************切换奖池******************************************************************/

  onSelectChange = cid => {
    // console.log(cid)
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getPool`,
      payload: { id: cid }
    })

    this.setState({ pid: cid })
  }

  editChange = () => {
    const { editing } = this.state
    this.setState({ editing: !editing })
  }

  /** **********************************list 增删改查******************************************************************/
  addItem = () => {
    let v = $.extend({}, true, defaultPropItem)
    // 自动生成奖品ID，递增
    const { model: { poolConfig } } = this.props
    let base = 1000
    if (poolConfig !== undefined && poolConfig.id !== undefined && poolConfig.id > 0) {
      base = poolConfig.id
    }
    v.prizeId = base * 100 + v.prizeId

    // 奖品ID 与 编号 自增
    const { list } = this.state
    list.forEach(item => {
      if (item.id >= v.id) {
        v.id = item.id + 1
      }

      if (item.prizeId >= v.prizeId) {
        v.prizeId = item.prizeId + 1
      }
    })

    // 覆盖旧数据
    let cl = $.extend([], true, list)
    cl.push(v)
    // console.log(cl)
    this.setState({ list: cl })
  }

  removeItem = id => () => {
    const { list } = this.state
    let cl = []
    list.forEach(item => {
      if (item.id !== id) {
        cl.push(item)
      }
    })

    this.setState({ list: cl })
  }
  /** **********************************list 增删改查结束******************************************************************/

  /** **********************************table 渲染******************************************************************/
  onInputTitleChange = (field) => e => {
    this.setState({ [field]: e.target.value })
  }

  onInputChange = (row, field) => value => {
    const { list } = this.state
    let v = $.extend([], true, list)
    v[row][field] = value
    this.forceUpdate() // 强制刷新，用于计算紫水晶价值
  }

  onInputRangeChange = (row, field) => value => {
    const { list } = this.state
    let v = $.extend([], true, list)
    v[row][field] = value
    this.forceUpdate() // 强制刷新，用于计算紫水晶价值
  }

  // 行选择
  onBtnClick = row => () => {
    this.setState({ row, propVisible: true }) // 当前正在编辑的行
  }

  // 选择营收礼物
  onPropChange = prop => {
    const { row, list } = this.state
    if (row === undefined) {
      message.error('unfined row' + row)
      return
    }

    // console.log(prop, row)

    let v = $.extend([], true, list)
    v[row].propsId = prop.id // 礼物ID
    v[row].propsName = prop.name // 礼物名称
    v[row].propsUrl = prop.url // 礼物图片
    v[row].value = prop.price // 礼物加个
    v[row].prizeType = prop.prizeType // 礼物渠道

    this.setState({ propVisible: false, list: v })
  }

  // 营收礼物列表隐藏
  onPropCancel = () => {
    this.setState({ propVisible: false })
  }

  renderColumn = () => {
    const { editing, range1, range2, range3, range4 } = this.state
    let renderColumns = []
    this.columns.forEach(column => {
      // 查找元素在数组中
      // console.log(column)
      // if (['count', 'rate', 'dailyLimit', 'hoursLimit', 'dailyCount', 'valueLimit'].indexOf(column.dataIndex) > -1) {
      //   column.render = (text, record, index) => {
      //     return editing ? <InputNumber onChange={this.onInputChange(index, column.dataIndex)} defaultValue={text} /> // 编辑模式
      //       : text
      //   }
      // }

      // if (['xRange1', 'xRange2', 'xRange3', 'xRange4'].indexOf(column.dataIndex) > -1) {
      //   column.render = (text, record, index) => {
      //     return editing ? <Input onChange={this.onInputRangeChange(index, column.dataIndex)} defaultValue={text} /> // 编辑模式
      //       : text
      //   }
      // }

      if (['range1'].indexOf(column.dataIndex) > -1) {
        column.title = () => { return editing ? <Input onChange={e => this.setState({ range1: e.target.value })} defaultValue={range1} /> : range1 }
        // column.title = () => { return editing ? <Input onChange={this.onInputTitleChange(range1)} defaultValue={range1} /> : range1 }
      }

      if (['range2'].indexOf(column.dataIndex) > -1) {
        column.title = () => { return editing ? <Input onChange={e => this.setState({ range2: e.target.value })} defaultValue={range2} /> : range2 }
      }

      if (['range3'].indexOf(column.dataIndex) > -1) {
        column.title = () => { return editing ? <Input onChange={e => this.setState({ range3: e.target.value })} defaultValue={range3} /> : range3 }
      }

      if (['range4'].indexOf(column.dataIndex) > -1) {
        column.title = () => { return editing ? <Input onChange={e => this.setState({ range4: e.target.value })} defaultValue={range4} /> : range4 }
      }

      if (['range1', 'range2', 'range3', 'range4'].indexOf(column.dataIndex) > -1) {
        column.children.forEach(item => {
          item.render = (text, record, index) => {
            return editing ? <InputNumber onChange={this.onInputRangeChange(index, item.dataIndex)} defaultValue={text} /> : text
          }
        })
      }

      if (column.dataIndex === 'propsInfo') {
        column.children.forEach(item => {
          if (item.dataIndex === 'propsId') {
            item.render = (text, record, index) => {
              return editing ? <Button onClick={this.onBtnClick(index, item.dataIndex)} type='link'>{text}</Button> : text
            }
          }
          if (item.dataIndex === 'count') {
            item.render = (text, record, index) => {
              return editing ? <InputNumber onChange={this.onInputChange(index, item.dataIndex)} defaultValue={text} /> : text
            }
          }
        })
      }

      renderColumns.push(column)
    })

    if (editing) {
      renderColumns.push({
        title: '编辑',
        align: 'center',
        render: (text, record) => (
          <div>
            <Button onClick={this.removeItem(record.id)} type='link' danger>删除</Button>
          </div>
        )
      })
    }

    return renderColumns
  }

  popConfirmTitle = () => {
    return <h3>确认修改奖池</h3>
  }
  /** ***********************************table 渲染结束********************************************************* */

  /* *******************************页面布局***************************************************************/
  render () {
    const { list, editing, propVisible, totalLimit, approvalDesc } = this.state

    return (
      <Card>
        <Space align='center'>
          <div hidden={!editing}>
            <Form.Item>
              <Button style={{ marginRight: 10 }} type='primary' onClick={this.addItem}>新增奖品</Button>
              <Button onClick={this.onReset} style={{ marginRight: 10 }}>放弃修改</Button>
              <Popconfirm onConfirm={this.onSubmit} cancelText='再看看' okText='提交' title={this.popConfirmTitle}><Button type='danger'>提交修改</Button></Popconfirm>
            </Form.Item>
          </div>
          <div span={2} hidden={editing}>
            <Form.Item>
              <Button type='primary' onClick={this.editChange}>编辑奖池</Button>
              <font style={{ marginLeft: 20 }} color='blue'>{approvalDesc}</font>
            </Form.Item>
          </div>
        </Space>
        <div>
          <Form>
            <Form.Item label='发奖上限（元/D）'>
              {editing ? <InputNumber defaultValue={totalLimit} onChange={e => this.setState({ totalLimit: e })} /> : totalLimit}
              <font color='red' style={{ marginLeft: 20 }}>(奖池支出达到发奖上限后, 当天将不再爆奖)</font>
            </Form.Item>
          </Form>
          <Form.Item>
            <div><font color='red'>1. "X"指用户参与豆荚玩法获得的返奖金额, X值落在哪个金额区间, 即按照对应区间的奖品概率进行爆奖</font></div>
            <div><font color='red'>2. 发放上限/D: -1, 视为不设限; 0或其他正整数, 按填写的数量进行限制</font></div>
            <div><font color='red'>3. 概率, 每个发奖金额（单位-元）区间内, 各奖品的出奖概率为独立事件</font></div>
          </Form.Item>
        </div>
        <Table bordered pagination={false} size='small' rowKey='id' columns={this.renderColumn()} dataSource={list} />
        <OffstripPropsComponent onCancel={this.onPropCancel} onChange={this.onPropChange} visible={propVisible} />
      </Card>
    )
  }
}

export default OffstripMainComponent
