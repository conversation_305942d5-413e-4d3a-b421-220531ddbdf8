import request from '@/utils/request'

export function getVideoUidBlackLists () {
  let url = `/white_list/get_uid_white_list?whiteListType=1`
  return request(url, { jsonp: true })
}

export function AddVideoUidBlack (params) {
  return request(`/white_list/add_uid_white_list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function DelVideoUidBlack (params) {
  return request(`/white_list/del_uid_white_list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}
