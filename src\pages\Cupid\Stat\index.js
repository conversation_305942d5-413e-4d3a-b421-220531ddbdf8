import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card } from 'antd'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import DataMonitor from './tabs/dataMonitor'
import HourMonitor from './tabs/hourMonitor'
import UserInMonitor from './tabs/userInMonitor'
import TopUserList from './tabs/topUserList'
import NewUserList from './tabs/newUserMonitor'

const namespace = 'cupidStat'
const cupitOriginID = 340235 // 丘比特礼物id

@connect(({ cupidStat }) => ({
  model: cupidStat
}))

class CupidStat extends Component {
  state = {
    targetOriginID: cupitOriginID // 入口礼物ID
  }

  componentDidMount = () => {}

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 标签页发生切换
  onTagChange = (record) => {}

  render () {
    const { route } = this.props
    const { targetOriginID } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='dataMonitor' type='card' onChange={(record) => this.onTagChange(record)}>
            <TabPane tab='数据监控' key='dataMonitor'>
              <DataMonitor originID={targetOriginID} />
            </TabPane>
            <TabPane tab='分时监控' key='hourMonitor'>
              <HourMonitor originID={targetOriginID} />
            </TabPane>
            <TabPane tab='用户投入区间' key='userIn'>
              <UserInMonitor originID={targetOriginID} />
            </TabPane>
            <TabPane tab='TOP10用户' key='topUser'>
              <TopUserList originID={targetOriginID} />
            </TabPane>
            <TabPane tab='用户留存' key='newUser'>
              <NewUserList originID={targetOriginID} />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default CupidStat
