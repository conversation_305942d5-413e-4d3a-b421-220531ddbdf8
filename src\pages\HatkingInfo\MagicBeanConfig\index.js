import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { checkIsNumber, checkSid } from '@/utils/common'
import dateString from '@/utils/dateString'
// import { QuestionCircleOutlined } from '@ant-design/icons'
// import { Button, Card, DatePicker, Divider, Form, Input, message, Modal, Popconfirm, Table, Tabs, Tooltip, Select } from 'antd'
import { Card, Divider, Form, Input, message, Modal, Popconfirm, Table, Tabs, Select } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'

const namespace = 'magicBeanControl'
const getOneConfigListUri = `${namespace}/getOneConfigList`
const addOneConfigInfoUri = `${namespace}/addOneConfigItem`
const updateOneConfigInfoUri = `${namespace}/updateOneConfigItem`
const deleteOneConfigInfoUri = `${namespace}/deleteOneConfigItem`
const getAllConfigListUri = `${namespace}/getAllConfigList`
const updateAllConfigInfoUri = `${namespace}/updateAllConfigItem`
const getAllConfirmListUri = `${namespace}/getAllConfirmList`
const getSinglePlayerListUri = `${namespace}/getSinglePlayerList`
const updateSinglePlayerInfoUri = `${namespace}/updateSinglePlayerItem`
const FormItem = Form.Item
const TabPane = Tabs.TabPane
const Option = Select.Option
// const { RangePicker: DateRangePicker } = DatePicker

var moment = require('moment')

@connect(({ magicBeanControl }) => ({
  model: magicBeanControl
}))
class MagicBeanControl extends Component {
  // 列表结构
  oneConfigColumns = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: 'sid', dataIndex: 'sid' },
    { title: 'asid', dataIndex: 'asid' },
    { title: '公会昵称', dataIndex: 'guildName' },
    { title: '开始时间', dataIndex: 'startTime' },
    { title: '结束时间', dataIndex: 'endTime' },
    { title: '每日上限(YB)', dataIndex: 'amethyst' },
    { title: '操作人', dataIndex: 'operator' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(true, record)}>更新</a><Divider type='vertical' />
          <Popconfirm title='Sure to delete?' onConfirm={this.handleDel(record.sid)}>
            <a>删除</a>
          </Popconfirm>
        </span>)
    }
  ]

    // 列表结构
    allConfigColumns = [
      { title: '配置ID', dataIndex: 'id', align: 'center' },
      { title: '管控策略', dataIndex: 'name', align: 'center', render: (v) => { return v ? v.replaceAll('魔豆流水', '魔豆模拟流水') : '' } },
      { title: '修改时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (text === 0 ? '无' : dateString(text)) },
      { title: '修改人', dataIndex: 'operator', align: 'center' },
      { title: '操作',
        key: 'operation',
        align: 'center',
        render: (text, record) => (
          <span>
            <a onClick={this.showModal(true, record)}>更新</a>
          </span>)
      }
    ]

    allConfirmColumns = [
      { title: '#', dataIndex: 'index', align: 'center' },
      { title: '配置ID', dataIndex: 'id', align: 'center' },
      { title: '管控策略', dataIndex: 'name', align: 'center', render: (v) => { return v ? v.replaceAll('魔豆流水', '魔豆模拟流水') : '' } },
      { title: '修改时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (text === 0 ? '无' : dateString(text)) },
      { title: '修改人', dataIndex: 'operator', align: 'center' }
    ]

  singlePlayerColumns = [
    { title: '配置ID', dataIndex: 'id', align: 'center' },
    { title: '单日参与玩法魔豆模拟限额时引导弹窗是否显示', dataIndex: 'showLimitPopups', align: 'center', render: text => (text === 1 ? '是' : '否') },
    { title: '单个uid每日可参与的魔豆模拟上限', dataIndex: 'dailyLimitCount', align: 'center' },
    { title: '修改时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (text === 0 ? '无' : dateString(text)) },
    { title: '修改人', dataIndex: 'operator', align: 'center' },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(true, record)}>更新</a>
        </span>)
    }
  ]

    pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, historyVisible: false, playerLimitVisible: false, isUpdate: false, value: {}, levelLimit: 0, activityKey: '1' }

  // 获取列表
  componentDidMount () {
    this.loadData()
  }

    loadData = (activityKey) => {
      if (activityKey === undefined) {
        activityKey = '1'
      }

      const { dispatch } = this.props
      dispatch({
        type: this.getSelectedQueryKey(activityKey)
      })

      if (activityKey === '1') {
        dispatch({
          type: getAllConfirmListUri
        })
      }
    }

    getSelectedQueryKey = (activityKey) => {
      let uri = ''
      switch (activityKey) {
        case '1':
          uri = getAllConfigListUri
          break
        case '2':
          uri = getOneConfigListUri
          break
        case '3':
          uri = getSinglePlayerListUri
          break
      }

      return uri
    }

    getSelectedUpdateKey = (activityKey, isUpdate) => {
      let uri = ''
      switch (activityKey) {
        case '1':
          uri = updateAllConfigInfoUri
          break
        case '2':
          if (isUpdate) {
            uri = updateOneConfigInfoUri
          } else {
            uri = addOneConfigInfoUri
          }
          break
        case '3':
          uri = updateSinglePlayerInfoUri
          break
      }

      return uri
    }

    handleDel = key => e => {
      const { dispatch } = this.props
      const data = { sid: key }
      dispatch({
        type: deleteOneConfigInfoUri,
        payload: data
      })
    }

  handleSubmit1 = e => {
    this.formRef1.submit()
  }

  handleSubmit2 = e => {
    this.formRef2.submit()
  }

  handleSubmit3 = e => {
    this.formRef3.submit()
  }

  onFinish3 = values => {
    const { dispatch } = this.props
    const { activityKey } = this.state
    if (parseInt(values.dailyLimitCount) <= 0) {
      message.error('每日魔豆上限需大于0')
      return
    }
    let isUpdate = this.state.isUpdate
    dispatch({
      type: this.getSelectedUpdateKey(activityKey, isUpdate),
      payload: values
    })
    this.formRef3.resetFields()
    this.setState({ playerLimitVisible: false })
  }

  // 添加 与 编辑
  onFinish2 = values => {
    const { dispatch } = this.props
    const { activityKey } = this.state

    if (!checkSid(values.sid)) {
      return
    }
    if (parseInt(values.sid) <= 0) {
      message.warn('sid不能为0,请重新填写')
      return
    }

    if (checkIsNumber(values.amethyst) && values.amethyst < 0) {
      message.warn('紫水晶数量填写有误,请检查: amethyst=' + values.amethyst)
      return
    }

    if (values.amethyst === undefined || values.amethyst === '') {
      values.amethyst = -1
    }

    if (values.timeRange[0].unix() >= values.timeRange[1].unix()) {
      message.warn('时间范围选择有误,请检查')
      return
    }

    values.startTime = values.timeRange[0].format('YYYY-MM-DD')
    values.endTime = values.timeRange[1].format('YYYY-MM-DD')
    values.timeRange = []

    let isUpdate = this.state.isUpdate
    dispatch({
      type: this.getSelectedUpdateKey(activityKey, isUpdate),
      payload: values
    })

    this.formRef2.resetFields()
    this.setState({ visible: false })
  }

  // 添加 与 编辑
  onFinish1 = values => {
    const { dispatch } = this.props
    const { activityKey } = this.state

    let channelDailyLimitX = parseInt(values.channelDailyLimitX)
    let channelDailyLimitY = parseInt(values.channelDailyLimitY)
    let channelDailyLimitZ = parseInt(values.channelDailyLimitZ)
    if (channelDailyLimitX >= channelDailyLimitY || channelDailyLimitY >= channelDailyLimitZ ||
      channelDailyLimitX >= channelDailyLimitZ) {
      message.error('配置有误，提交失败！')
      return
    }
    let isUpdate = this.state.isUpdate
    dispatch({
      type: this.getSelectedUpdateKey(activityKey, isUpdate),
      payload: values
    })
    this.formRef1.resetFields()
    this.setState({ historyVisible: false })
  }

  tabOnChange = type => activityKey => {
    if (type !== undefined || type != null) {
      activityKey = type
    }
    this.setState({ activityKey: activityKey })
    this.loadData(activityKey)
  }

  // 显示弹窗
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    switch (this.state.activityKey) {
      case '1':
        if (this.formRef1) {
          this.formRef1.resetFields()
          this.formRef1.setFieldsValue(v)
        }
        this.setState({ historyVisible: true, value: record, isUpdate: isUpdate, title: '全频道管控策略配置' })
        break
      case '2':
        if (isUpdate) {
          record.timeRange = [moment(record.startTime, 'YYYY-MM-DD'), moment(record.endTime, 'YYYY-MM-DD')]
        }

        if (this.formRef2) {
          this.formRef2.resetFields()
          this.formRef2.setFieldsValue(v)
        }
        this.setState({ visible: true, value: record, isUpdate: isUpdate, title: '单频道限制' })
        break
      case '3':
        if (this.formRef3) {
          this.formRef3.resetFields()
          this.formRef3.setFieldsValue(v)
        }
        this.setState({ playerLimitVisible: true, value: record, isUpdate: isUpdate, title: '用户参与限制' })
        break
    }
  }

  // 关闭弹窗
  hidModal = () => {
    switch (this.state.activityKey) {
      case '1':
        this.setState({ historyVisible: false })
        break
      case '2':
        this.setState({ visible: false })
        break
      case '3':
        this.setState({ playerLimitVisible: false })
        break
    }
  }

  render () {
    // const { route, model: { oneConfigList, allConfigList, allConfirmList, singlePlayerList } } = this.props
    // const { visible, historyVisible, playerLimitVisible, title, isUpdate } = this.state
    const { route, model: { allConfigList, allConfirmList, singlePlayerList } } = this.props
    const { historyVisible, playerLimitVisible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 4 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 10 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs onChange={this.tabOnChange()} type='card'>
            <TabPane tab='全频道管控配置' key='1'>
              <Form>
                <Table dataSource={allConfigList} columns={this.allConfigColumns} rowKey={(record, index) => index} pagination={false} size='small' />
                <Divider />
                <Table dataSource={allConfirmList} columns={this.allConfirmColumns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
              </Form>
              <Modal forceRender visible={historyVisible} title={title} onCancel={this.hidModal} onOk={this.handleSubmit1} width='30%'>
                <Form {...formItemLayout} ref={form => { this.formRef1 = form }} onFinish={this.onFinish1}>
                  <Divider plain dashed='true'>非超级水晶公会</Divider>
                  <FormItem label='魔豆流水限制/YB' name='channelDailyLimitX' rules={[{ required: true }]}>
                    <Input style={{ width: '100%' }} />
                  </FormItem>
                  <Divider plain dashed='true'>超级水晶公会</Divider>
                  <FormItem label='礼物流水X/YB' name='channelMonthlyAmount' rules={[{ required: true }]} >
                    <Input style={{ width: '100%' }} />
                  </FormItem>
                  <div>公会礼物流水未达标(上月礼物流水总额&lt;X)</div>
                  <FormItem label='魔豆流水限制/YB' name='channelDailyLimitY' rules={[{ required: true }]}>
                    <Input style={{ width: '100%' }} />
                  </FormItem>
                  <div>公会礼物流水达标(上月礼物流水总额&ge;X)</div>
                  <FormItem label='魔豆流水限制/YB' name='channelDailyLimitZ' rules={[{ required: true }]}>
                    <Input style={{ width: '100%' }} />
                  </FormItem>
                  <FormItem name='id'>
                    <Input hidden />
                  </FormItem>
                </Form>
              </Modal>
            </TabPane>
            <TabPane tab='用户参与限制' key='3'>
              <Table dataSource={singlePlayerList} columns={this.singlePlayerColumns} rowKey={(record, index) => index} pagination={false} size='small' />
              <Modal forceRender visible={playerLimitVisible} title={title} onCancel={this.hidModal} onOk={this.handleSubmit3} width='40%'>
                <Form {...formItemLayout} ref={form => { this.formRef3 = form }} onFinish={this.onFinish3}>
                  <FormItem label='配置ID' name='id'>
                    <Input style={{ width: '100%' }} disabled />
                  </FormItem>
                  <FormItem label='达到限额时显示引导弹窗' name='showLimitPopups' rules={[{ required: true }]}>
                    <Select><Option value={0}>否</Option><Option value={1}>是</Option></Select>
                  </FormItem>
                  <FormItem label='每日魔豆上限' name='dailyLimitCount' rules={[{ required: true }]}>
                    <Input style={{ width: '100%' }} />
                  </FormItem>
                </Form>
              </Modal>
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default MagicBeanControl
