import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Popconfirm, Modal, Input, Select } from 'antd'
import { connect } from 'dva'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
var moment = require('moment')

const namespace = 'propsClassConfig'
const FormItem = Form.Item
const Option = Select.Option

@connect(({ propsClassConfig }) => ({
  model: propsClassConfig
}))

class PropsClassConfig extends Component {
  columns = [
    { title: '分类ID', dataIndex: 'typeId', width: 100, align: 'center' },
    { title: '分类名称', dataIndex: 'typeName', width: 100, align: 'center' },
    { title: '权重', dataIndex: 'weight', width: 100, align: 'center' },
    { title: '标签', dataIndex: 'tag', width: 100, align: 'center', render: (text, record) => (text.length > 0 ? text : '无') },
    { title: '状态', dataIndex: 'status', width: 100, align: 'center', render: (text, record) => (text === 1 ? '可见' : '不可见') },
    { title: '修改人', dataIndex: 'operator', width: 100, align: 'center' },
    { title: '修改时间', dataIndex: 'timestamp', width: 100, align: 'center', render: text => this.dateString(text) },
    { title: '操作',
      width: 100,
      align: 'center',
      render: (text, record) => (
        <span>
          <a><EditOutlined style={{ marginRight: 10 }} onClick={this.showModal(true, record)} /></a>
          <Popconfirm onConfirm={this.handleDel(record.typeId)} title='确认删除？'><a><DeleteOutlined style={{ color: 'red' }} /></a></Popconfirm>
        </span>)
    }
  ]

  pagination = { pageSizeOptions: ['20', '50', '100'], showSizeChanger: true, pageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }
  state = { visible: false, isUpdate: false, value: {}, tag: '', status: 0 }

  showModal = (isUpdate, record) => () => {
    if (record == null) record = { weight: 0, tag: '', status: 0 }

    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(record)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  hideModal = () => {
    this.setState({ visible: false })
  }

  dateString (timestamp) {
    if (timestamp === 0) {
      return '-'
    }
    return moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss')
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  onFinish = values => {
    const { dispatch } = this.props
    const { isUpdate } = this.state
    const url = isUpdate ? `${namespace}/updateItem` : `${namespace}/addItem`
    dispatch({
      type: url,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { typeId: key }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  handleReset = clearFilters => () => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`
    })
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list } } = this.props
    const { searchResult, searchDone, visible, isUpdate, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Button type='primary' onClick={this.showModal(false)}>新增</Button>
            <Divider />
            <Table dataSource={searchDone ? searchResult : list} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
          </Form>
        </Card>

        <Modal visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit} forceRender>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <FormItem label='分类ID' name='typeId' rules={[{ required: true }]}>
              <Input readOnly={isUpdate} />
            </FormItem>
            <FormItem label='分类名称' name='typeName' rules={[{ required: true }]}>
              <Input />
            </FormItem>
            <FormItem label='权重' name='weight' rules={[{ required: true }]}>
              <Input />
            </FormItem>
            <FormItem label='标签' name='tag'>
              <Select><Option value=''>无</Option><Option value='NEW'>NEW</Option></Select>
            </FormItem>
            <FormItem label='状态' name='status'>
              <Select><Option value={0}>不可见</Option><Option value={1}>可见</Option></Select>
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default PropsClassConfig
