import React, { Component } from 'react'
import { connect } from 'dva'
import { tableStyle, timeFormater, checkSids } from '@/utils/common'
import { Input, Form, Row, Col, Button, Table, Popconfirm, Modal, message } from 'antd'
import { DeleteOutlined, UsergroupAddOutlined } from '@ant-design/icons'

// 监控目标UID配置页面
const namespace = 'channelEnterControl'

@connect(({ channelEnterControl }) => ({
  model: channelEnterControl
}))

class ChannelUidBlackList extends Component {
  state = {
    modalVisable: false,
    selectedRowKeys: []
  }
  componentDidMount = () => {
    this.refreshList()
  }
  refreshList = () => {
    this.callModel('getUIDBlackList')
  }
  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 翻页后更新表格相关参数
  pageChange (pagination) {
    const { current, pageSize } = pagination
    this.changeState('page', current)
    this.changeState('size', pageSize)
  }
  // 删除选中名单
  onComfirmBatchDelete = (uidList) => {
    const { selectedRowKeys } = this.state
    const f = { uidList: selectedRowKeys.join('\n'), op: 'DELETE' }
    this.callModel('updateUIDBlackList', {
      params: f,
      cbFunc: (ok) => {
        if (ok) {
          message.success('批量删除UID名单成功')
        } else {
          message.warn('批量删除失败，请稍后再试~')
        }
        this.refreshList()
      }
    })
  }
  // 提交批量新增
  onComfirmSubmit = (f) => {
    const { uidList } = f
    if (!checkSids(uidList)) {
      message.warn('uid列表格式不正确，请检查~')
    }
    f.uidList = f.uidList.replaceAll(',', '\n')
    f.uidList = f.uidList.replaceAll(' ', '')
    f.op = 'UPDATE'
    this.callModel('updateUIDBlackList', {
      params: f,
      cbFunc: (ok) => {
        if (ok) {
          message.success('更新UID名单成功')
        } else {
          message.warn('更新列表失败，请稍后再试~')
        }
        this.setState({ modalVisable: false })
        this.refreshList()
      }
    })
  }

  render () {
    const { selectedRowKeys, modalVisable } = this.state
    const { uidBlackList, currentPage, currentSize } = this.props.model
    const { TextArea } = Input

    const columns = [
      { title: '序号', render: (text, record, index) => ((currentPage - 1) * currentSize + index + 1) },
      { title: '目标UID', dataIndex: 'uid', sorter: { compare: (a, b) => a.uid - b.uid } },
      { title: '操作人UID', dataIndex: 'updateInfo', render: (v) => { return v.uid }, sorter: { compare: (a, b) => a.uid - b.uid } },
      { title: '添加时间', dataIndex: 'updateInfo', render: (v) => { return timeFormater(v.timestamp) } }
    ]
    const rowSelection = {
      selectedRowKeys,
      onChange: (uids) => { this.setState({ selectedRowKeys: uids }) }
    }

    return (
      <div>
        <text style={{ color: '#339999ba' }}> 简介：这里配置的UID，在交友模板上麦期间，手Y用户不能进入该UID所在的频道。请点击上方大标题的问号查看使用提示～”</text>
        <Row>
          <Col>
            <Popconfirm title='确认删除选名单中么？' onConfirm={this.onComfirmBatchDelete}>
              <Button danger disabled={selectedRowKeys.length === 0}>
                <DeleteOutlined />批量删除 {selectedRowKeys.length} 项
              </Button>
            </Popconfirm>
            <Button type='primary' style={{ marginLeft: '1em' }} onClick={() => this.formRef.resetFields() || this.setState({ modalVisable: true })}>
              <UsergroupAddOutlined /> 批量新增UID
            </Button>
          </Col>
        </Row>
        <br />
        <Row >
          <Col span={24}>
            <Table columns={columns} dataSource={uidBlackList} size='small' pagination={tableStyle}
              rowKey={record => record.uid} rowSelection={rowSelection} onChange={(pagination) => { this.pageChange(pagination) }} />
          </Col>
        </Row>
        <Modal title='批量添加UID' forceRender visible={modalVisable} cancelText='取消' okText='确认'
          onOk={() => this.formRef.submit()} onCancel={() => this.setState({ modalVisable: false })} >
          <Form onFinish={this.onComfirmSubmit} initialValues={{ uidList: '' }} ref={form => { this.formRef = form }}>
            <div>
              输入一到多个UID,使用逗号或换行符分隔
            </div>
            <Form.Item name='uidList'>
              <TextArea style={{ height: '10em' }} />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    )
  }
}

export default ChannelUidBlackList
