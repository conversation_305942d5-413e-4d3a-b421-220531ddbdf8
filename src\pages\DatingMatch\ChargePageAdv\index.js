import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import PopImage from '@/components/PopImage'
import { connect } from 'dva'
import { Card, Table, Popconfirm, Input, Form, Modal, DatePicker, Divider, Button } from 'antd'
import PicturesWall from '@/components/PicturesWall'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons'

var moment = require('moment')
const namespace = 'chargePageAdv'
const FormItem = Form.Item

@connect(({ chargePageAdv }) => ({
  model: chargePageAdv
}))
class ChargePageAdv extends Component {
  columns = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: '广告图', dataIndex: 'url', align: 'center', render: text => <PopImage value={text} /> },
    { title: '跳转链接', dataIndex: 'jumpUrl', align: 'center' },
    { title: '开始时间', dataIndex: 'beginTime', align: 'center' },
    { title: '结束时间', dataIndex: 'endTime', align: 'center' },
    { title: '权重', dataIndex: 'weight', align: 'center' },
    { title: '操作',
      align: 'center',
      render: record => (
        <div>
          <a><EditOutlined style={{ marginRight: 10 }} onClick={this.showModal(true, record)} /></a>
          <Popconfirm onConfirm={this.handleRemove(record.id)} title='确认删除？'><a><DeleteOutlined style={{ color: 'red' }} /></a></Popconfirm>
        </div>)
    }
  ]

  state = { visible: false, record: {} }
  defaultValue = { jumpUrl: '', url: null, weight: 0, beginTime: moment().format('YYYY-MM-DD HH:mm:ss'), endTime: moment().format('YYYY-MM-DD HH:mm:ss') }

  showModal = (update, record) => () => {
    record = update ? record : this.defaultValue

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.jump = v.jumpUrl
      v.starTime = moment(v.beginTime, 'YYYY-MM-DD HH:mm:ss')
      v.endTime = moment(v.endTime, 'YYYY-MM-DD HH:mm:ss')
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, update: update, record: record, title: update ? '修改' : '添加' })
  }

  handleRemove = id => () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/removeItem`,
      payload: { id: id, request: this.state.request }
    })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  onFinish = values => {
    const { dispatch } = this.props
    values.starTime = values.starTime.format('YYYY-MM-DD HH:mm:ss')
    values.endTime = values.endTime.format('YYYY-MM-DD HH:mm:ss')
    values.request = this.state.request
    values.id = this.state.record.id
    const url = this.state.update ? `${namespace}/updateItem` : `${namespace}/addItem`
    dispatch({
      type: url,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  hideModal = () => {
    this.setState({ visible: false })
  }

  componentDidMount () {
    const { match, dispatch } = this.props
    const request = { 'box': 0, 'screen': 1, 'charge': 2 }[match.path.split('-')[1]]
    this.setState({ request: request })

    dispatch({
      type: `${namespace}/getList`,
      payload: { request: request }
    })
  }

  saveFormRef = formRef => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list } } = this.props
    const { visible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper route={route}>
        <Card>
          <Button type='primary' onClick={this.showModal(false)}>添加</Button>
          <Divider />
          <Table rowKey={(_, index) => index} columns={this.columns} dataSource={list} pagination={{ pageSize: 100 }} />
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem label='跳转链接' name='jump' rules={[{ required: true }]}>
              <Input />
            </FormItem>
            <FormItem label='开始时间' name='starTime'>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </FormItem>
            <FormItem label='结束时间' name='endTime'>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </FormItem>
            <FormItem label='权重' name='weight' rules={[{ required: true }]}>
              <Input />
            </FormItem>
            <FormItem label='广告图' name='url' rules={[{ required: true }]}>
              <PicturesWall />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default ChargePageAdv
