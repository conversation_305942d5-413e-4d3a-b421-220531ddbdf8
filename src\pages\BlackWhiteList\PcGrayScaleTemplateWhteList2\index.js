import React, { Component } from 'react'
import { connect } from 'dva'
import { Tabs, Card, Input, Form, Row, Col, Button, Table, Popconfirm, Select, Modal, message, Tooltip, Empty, Descriptions } from 'antd'
import { tableStyle, Inputlayout, timeFormater, getH5ConfigsList } from '@/utils/common'
import { DeleteOutlined, FormOutlined } from '@ant-design/icons'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import TextArea from 'antd/lib/input/TextArea'

const namespace = 'pcGrayScaleTemplateWhteList2'

@connect(({ pcGrayScaleTemplateWhteList2 }) => ({
  model: pcGrayScaleTemplateWhteList2
}))

class PcGrayScaleTemplateWhteList2 extends Component {
  componentDidMount = () => {
    this.refreshH5WhiteList()
    this.initPlayInfo()
  }
  state = {
    selectedRowKeys: [], // 批量选择的行
    selectNumber: 0,
    historyModalVisable: false,
    historySelectItem: null,
    bashDeleteVisable: false // 批量删除按钮是否可用
  }

  // 修改model state中某个成员到特定值
  changeState = (name, newValue) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/updateState`,
      payload: { name: name, newValue: newValue }
    })
  }
  // 标签页发生切换
  onTagChange = (record) => {
    if (record === '1') { // 切换到'添加标签页'
      this.initModifyTagForm()
      this.changeState('displayVersionList', []) // 清空下拉框显示的版本列表
    }
    if (record === '2') { // 切换到'灰度白名单标签页'
      this.refreshH5WhiteList()
    }
    if (record === '3') {
      this.getUpdateRecord()
    }
  }
  // 初始化玩法配置信息
  // 备注：获取某个玩法的版本列表，需要先玩法配置中找到对应的url，再向后台请求数据
  initPlayInfo = () => {
    getH5ConfigsList(true).then(configs => {
      if (Array.isArray(configs) && configs.length > 0) {
        this.changeState('playVersionUrlList', configs)
      } else {
        message.error('获取玩法配置信息错误，请查看控制台')
      }
    })
  }
  // 翻页后更新表格相关参数，用于表格序号的显示
  pageChange (pagination) {
    const { current, pageSize } = pagination
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/updatePageAndSize`,
      payload: { page: current, size: pageSize }
    })
  }
  // 显示或隐藏修改白名单模态框
  onChangeModifyState = (visible) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/updateModifyVisible`,
      payload: visible
    })
  }
  // 更新选中列表的信息
  onSelectTableRow = (asid, sid, play, version, remark) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/updateSelectData`,
      payload: { asid: asid, sid: sid, play: play, version: version, remark: remark }
    })
    // updateVersion用于更新版本选择下拉框的显示值为value,调用时机为查询版本列表成功后
    let updateVersion = (value, ttl = 10) => {
      if (ttl < 0) {
        console.error('更新版本选择下拉框值失败...')
        return
      }
      if (this.formRef2) {
        this.formRef2.setFieldsValue({ version: value })
        this.formRef2.setFieldsValue({ remark: remark })
        return
      }
      setTimeout(() => { updateVersion(value, ttl - 1) }, 200)
    }
    dispatch({
      type: `${namespace}/updateVersionByPlay`,
      payload: { play: play, spec: version, func: updateVersion }
    })
  }
  // 获取/刷新灰度白名单列表数据
  refreshH5WhiteList = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getWhiteListData`,
      payload: null
    })
  }
  // 根据玩法过滤表格显示的数据
  refreshDataByPlayId = (playId) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/filterDataByPlayId`,
      payload: playId
    })
  }
  // 根据灰度过滤显示的数据
  refreshDataByGrey = (grey) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/filterDataByGrey`,
      payload: grey
    })
  }
  // 重置‘新增白名单’表单数据
  initModifyTagForm = () => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
  }
  // 重置‘新增白名单-版本信息选择框’
  initVersionSelecter = () => {
    // 下拉框显示默认值
    if (this.formRef) {
      this.formRef.resetFields(['version'])
    }
  }
  // 重置‘修改白名单’表单数据
  initModifyTagForm2 = () => {
    if (this.formRef2) {
      this.formRef2.resetFields()
    }
  }
  // 选定玩法后更新对应的版本列表
  updateVersionList = (play) => {
    const { dispatch } = this.props
    let updateVersion = (value) => {
      if (this.formRef) {
        this.formRef.setFieldsValue({ version: value })
      }
    }
    dispatch({
      type: `${namespace}/updateVersionByPlay`,
      payload: { play: play, func: updateVersion }
    })
  }
  // 点击 批量添加标签页-添加按钮
  onBatchAddBtnClick = () => {
    const { whiteListText } = this.state
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/batchModify`,
      payload: { whiteListText: whiteListText }
    })
  }
  // 格式化玩法名称
  playNameFormater = (playId) => {
    const { playVersionUrlList } = this.props.model
    if (!Array.isArray(playVersionUrlList)) {
      message.error('查询玩法名称错误，请检查控制台')
      console.error('unexpect playVersionUrlList: ', playVersionUrlList)
      return 'id=' + playId
    }
    for (let i = 0; i < playVersionUrlList.length; i++) {
      if (playVersionUrlList[i].play === playId) {
        return playVersionUrlList[i].name
      }
    }
    return 'id=' + playId + ' ?'
  }
  // 格式化删除白名单列表
  groupFormater = (deleteGroup) => {
    let tempGroup = []
    for (let i = 0; i < deleteGroup.length; i++) {
      tempGroup[i] = {
        'play': this.playNameFormater(deleteGroup[i].play),
        'version': deleteGroup[i].version,
        'sids': deleteGroup[i].sids
      }
    }
    return JSON.stringify(tempGroup, null, 3)
  }
  // 点击 ‘添加/修改标签页’-添加按钮
  onAddBtnClick = (value) => {
    const { play, version, sids, remark } = value
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/singleModify`,
      payload: { sids: sids, play: play, version: version, remark: remark }
    })
  }
  // ‘添加/修改标签页’HTML代码
  addOrModifyBlackListHtml = () => {
    const { Option } = Select
    const { TextArea } = Input
    const { updating, displayVersionList, playVersionUrlList } = this.props.model
    return (
      <div>
        <Row>
          <Col span={24}>
            <Form {...Inputlayout} name='addForm' onFinish={this.onAddBtnClick}
              initialValues={{ sids: '', play: '-99', version: '-99', remark: '' }}
              ref={form => { this.formRef = form }}
            >

              <Form.Item label='玩法 [play]' name='play' colon >
                <Select defaultValue='-99' onChange={(v) => { this.initVersionSelecter(); this.updateVersionList(v) }}>
                  <Option key='-99'>未选择</Option>
                  {playVersionUrlList.map((item, index) => (
                    <Option key={item.play} value={item.play}>{item.name}</Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item label='版本 [version]' name='version' colon>
                <Select defaultValue='-99'>
                  <Option key='-99'>未选择</Option>
                  {displayVersionList.map((item, index) => (
                    <Option key={index} value={item}>{item}</Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item label='频道号 [sid]' name='sids' colon>
                <TextArea rows={10} placeholder='可填写多个，每个sid之间使用逗号(,)隔开&#13;&#10;(0代表全部)' />
              </Form.Item>

              <Form.Item label='备注 [remark]' name='remark' colon>
                <Input placeholder='请填写备注信息(可选)' allowClear maxLength={200} />
              </Form.Item>

              <Form.Item>
                <Button type='primary' htmlType='submit' loading={updating} >
                  确定修改
                </Button>
              </Form.Item>

            </Form>
          </Col>
        </Row>
      </div>
    )
  }
  // 确认删除选中的白名单
  onComfirmDel = (record) => {
    const { dispatch } = this.props
    const { sid, play, version } = record
    dispatch({
      type: `${namespace}/delWhiteList`,
      payload: { sid: sid, play: play, version: version }
    })
  }
  // 确认批量删除选中的白名单
  onComfirmBashDel = (bashDelList) => {
    const { dispatch } = this.props
    // 回调函数：删除完成后清空选中列表数据，隐藏模态框
    let callBackFunc = () => {
      this.setState({
        selectedRowKeys: [],
        selectNumber: 0,
        bashDeleteVisable: false
      })
    }
    dispatch({
      type: `${namespace}/bashDelWhiteList`,
      payload: { bashDelList, callBackFunc }
    })
  }
  // 确认修改选中版本信息
  onModifPlay = (value) => {
    const { selectSid, selectPlay } = this.props.model
    const { version, remark } = value
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/singleModify`,
      payload: { play: selectPlay, version: version, sids: selectSid, remark: remark }
    })
  }
  // 选中需要删除的列表发生变化
  onSelectChange = (selectedRowKeys) => {
    let selectNumber = selectedRowKeys.length
    this.setState({ selectedRowKeys, selectNumber })
  }
  // 修改版本号模态框 html代码
  modifyVersionHtml = () => {
    const { modifyModelVisibla, selectAsid, selectSid, selectPlay, displayVersionList, updating } = this.props.model
    const { Option } = Select
    let layout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 18 }
    }

    return (
      <Modal title='白名单修改' footer={null} visible={modifyModelVisibla}
        onCancel={() => this.onChangeModifyState(false)}
      >
        <div>
          <Row>
            <Col span={24}>
              <Form {...layout}
                onFinish={this.onModifPlay}
                initialValues={{ version: '-99', remark: '' }}
                ref={form => { this.formRef2 = form }}>
                <Form.Item label='短位频道 [asid]:'>
                  <Input value={selectAsid} disabled />
                </Form.Item>
                <Form.Item label='频道号 [sid]:'>
                  <Input value={selectSid} disabled />
                </Form.Item>
                <Form.Item label='玩法 [play]:'>
                  <Input value={this.playNameFormater(selectPlay)} disabled />
                </Form.Item>
                <Form.Item label='版本 [version]:' name='version'>
                  <Select onChange={(v) => this.setState({ updateVersion: v })}>
                    <Option key='-99'>未选择</Option>
                    {displayVersionList.map((item, index) => (
                      <Option key={index} value={item}>{item}</Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item label='备注 [remark]:' name='remark'>
                  <Input onChange={e => this.setState({ updateRemark: e.target.value })}
                    placeholder='请填写备注(可选)'
                  />
                </Form.Item>
                <Form.Item>
                  <Button type='primary' htmlType='submit' loading={updating} >
                    确定修改
                  </Button>
                </Form.Item>
              </Form>
            </Col>
          </Row>
        </div>
      </Modal>
    )
  }
  // 批量删除确认模态框html代码
  bashDeleteHtml = () => {
    const { selectedRowKeys, selectNumber, bashDeleteVisable } = this.state
    const { allData } = this.props.model
    let deleteMenber = []
    for (let i = 0; i < selectedRowKeys.length; i++) {
      let temp = allData[selectedRowKeys[i] - 1]
      deleteMenber[i] = {
        'play': temp.play,
        'version': temp.version,
        'sid': temp.sid
      }
    }
    // 由于批量删除需要分批次完成，所以先将数据按照play-version进行分组
    deleteMenber.sort((a, b) => {
      if (a.play !== b.play) return a.play > b.play ? 1 : -1
      return a.version > b.version ? 1 : -1
    })
    let deleteGroup = []
    let tmpIdx = -1
    for (let i = 0; i < deleteMenber.length; i++) {
      if (tmpIdx === -1 || deleteMenber[i].play !== deleteGroup[tmpIdx].play || deleteMenber[i].version !== deleteGroup[tmpIdx].version) {
        tmpIdx++
        deleteGroup[tmpIdx] = {
          'play': deleteMenber[i].play,
          'version': deleteMenber[i].version,
          'sids': deleteMenber[i].sid
        }
        continue
      }
      deleteGroup[tmpIdx].sids += ',' + deleteMenber[i].sid
    }
    return (
      <Modal title='确认删除列表'
        visible={bashDeleteVisable}
        onCancel={() => this.setState({ bashDeleteVisable: false })}
        onOk={() => this.onComfirmBashDel(deleteGroup)}
        okText='确认删除'
        cancelText='取消'
      >
        <p>即将删除{selectNumber}条白名单记录：</p>
        <Row>
          <Col span={24}>
            <TextArea style={{ height: '20em' }} value={this.groupFormater(deleteGroup)} />
          </Col>
        </Row>
      </Modal>
    )
  }
  // 白名单列表-修改、删除操作 html代码
  operationHtml = (record) => {
    const { asid, sid, play, version, remark } = record
    let tmpTitle = `确定要将 [sid=${sid} play=${this.playNameFormater(play)} version=${version}] 从灰度白名单删除吗？`
    return (
      <>
        <a onClick={() => { // 修改操作按钮
          this.initModifyTagForm2()
          this.onSelectTableRow(asid, sid, play, version, remark)
          this.onChangeModifyState(true)
        }}>
          <FormOutlined style={{ color: '#1890ff', fontSize: '1.2em', marginRight: '1em' }} />
        </a>
        <Popconfirm placement='bottom' title={tmpTitle}
          okType='danger' okText='删除' cancelText='取消' onConfirm={() => this.onComfirmDel(record)}>
          <a href='#'><DeleteOutlined style={{ color: '#ff3535', fontSize: '1.2em' }} /></a>
        </Popconfirm>
      </>
    )
  }
  // ‘白名单展示列表标签页’html代码
  displayWhiteListHtml = () => {
    const { displayData, currentPage, currentSize, playVersionUrlList } = this.props.model
    const { selectedRowKeys, selectNumber } = this.state
    const { Option } = Select
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange
    }
    const columns = [
      {
        title: '#',
        render: (text, record, index) => ((currentPage - 1) * currentSize + index + 1),
        width: '5em'
      },
      {
        title: '短位频道',
        dataIndex: 'asid',
        sorter: { compare: (a, b) => a.asid - b.asid }
      },
      {
        title: '频道号',
        dataIndex: 'sid',
        sorter: { compare: (a, b) => a.sid - b.sid },
        render: (text) => { return (text === 0 ? '全部' : text) }
      },
      {
        title: '玩法',
        dataIndex: 'play',
        render: (id) => this.playNameFormater(id),
        sorter: { compare: (a, b) => a.play - b.play }
      },
      {
        title: '版本',
        dataIndex: 'version'
      },
      {
        title: '修改人',
        dataIndex: 'uid'
      },
      {
        title: '修改时间',
        dataIndex: 'timeStamp',
        render: (v) => timeFormater(v),
        sorter: { compare: (a, b) => a.timeStamp - b.timeStamp }
      },
      {
        title: '修改备注',
        dataIndex: 'remark',
        ellipsis: true
      },
      {
        title: '操作',
        align: 'center',
        render: (record) => this.operationHtml(record)
      }
    ]
    return (
      <>
        <Row style={{ marginBottom: '1em' }}>
          <Col>
            <Button danger disabled={selectNumber === 0} onClick={() => this.setState({ bashDeleteVisable: true })}>
              批量删除 {selectNumber} 项
            </Button>
          </Col>
          <Col offset={1}>
            <Tooltip title='根据玩法筛选数据'>
              <Select defaultValue='-1' style={{ width: '10em' }}
                onChange={(v) => this.refreshDataByPlayId(v)}>
                <Option key='-1'>全部</Option>
                {playVersionUrlList.map((item, index) => (
                  <Option key={item.play} value={item.play}>{item.name}</Option>
                ))}
              </Select>
            </Tooltip>
          </Col>
          <Col offset={1}>
            <Tooltip title='根据灰度筛选数据'>
              <Select defaultValue='-1' style={{ width: '10em' }}
                onChange={(v) => this.refreshDataByGrey(v)}>
                <Option key='-1' value='-1'>全部</Option>
                <Option key='0' value='0'>全量</Option>
                <Option key='1' value='1'>灰度</Option>
              </Select>
            </Tooltip>
          </Col>
        </Row>
        <Row >
          <Col span={24}>
            <Table columns={columns}
              rowSelection={rowSelection}
              dataSource={displayData}
              size='small'
              scroll={{ x: 'max-content' }}
              showSorterTooltip={false}
              pagination={tableStyle}
              onChange={(pagination) => { this.pageChange(pagination) }} />
          </Col>
        </Row>
      </>
    )
  }
  // 查询数据更新历史
  getUpdateRecord = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getUpdateRecord`,
      payload: null
    })
  }
  // 更新历史标签页代码
  updateHistoryTabHtml = () => {
    const { historyModalVisable, historySelectItem } = this.state
    const { updateHistory } = this.props.model
    const columns = [
      { title: '操作描述', dataIndex: 'tag', align: 'center' },
      { title: '执行时间', dataIndex: 'timestamp', align: 'center', render: (v) => { return timeFormater(v) } },
      { title: '操作人UID', dataIndex: 'opUid', align: 'center' },
      { title: '操作结果', dataIndex: 'resErr', align: 'center', render: (v) => { return v === '<nil>' ? '成功' : `结果异常: err=[${v}]` } },
      { title: '操作',
        align: 'center',
        render: (v, r) => {
          return <a onClick={() => { this.setState({ historyModalVisable: true, historySelectItem: r }) }}>查看详情</a>
        } }
    ]
    return (
      <div>
        <Row>
          <Col span={24} style={{ marginBottom: '1em' }}>
            <Button type='primary' onClick={() => { this.getUpdateRecord() }}>刷新</Button>
          </Col>
          <Col span={24}>
            <Table columns={columns} dataSource={updateHistory} size='small' scroll={{ x: 'max-content' }} pagination={{ pageSize: 25 }} />
          </Col>
        </Row>
        <Modal visible={historyModalVisable} title='操作详情' cancelText='关闭' width='60em'
          onCancel={() => { this.setState({ historyModalVisable: false }) }} okButtonProps={{ hidden: true }}>
          {
            historySelectItem === null
              ? <Empty />
              : <Descriptions span={24} bordered size='small'>
                <Descriptions.Item span={24} label='操作描述' style={{ width: '10em', whiteSpace: 'pre-line' }}>{historySelectItem.tag}</Descriptions.Item>
                <Descriptions.Item span={24} label='更新前'>
                  <pre>{JSON.stringify(JSON.parse(historySelectItem.before), null, 2)}</pre>
                </Descriptions.Item>
                <Descriptions.Item span={24} label='参数'>
                  <pre>{JSON.stringify(JSON.parse(historySelectItem.params), null, 2)}</pre>
                </Descriptions.Item>
                <Descriptions.Item span={24} label='更新后'>
                  <pre>{JSON.stringify(JSON.parse(historySelectItem.after), null, 2)}</pre>
                </Descriptions.Item>
              </Descriptions>
          }
        </Modal>
      </div>
    )
  }

  render () {
    const { TabPane } = Tabs
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='2' onChange={(record) => this.onTagChange(record)} type='card' size='large'>
            <TabPane tab='PC模板玩法灰度白名单' key='2'>
              {this.displayWhiteListHtml()}
            </TabPane>
            <TabPane tab='添加/修改' key='1'>
              {this.addOrModifyBlackListHtml()}
            </TabPane>
            <TabPane tab='更改历史' key='3'>
              {this.updateHistoryTabHtml()}
            </TabPane>
          </Tabs>
          {this.modifyVersionHtml()}
          {this.bashDeleteHtml()}
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default PcGrayScaleTemplateWhteList2
