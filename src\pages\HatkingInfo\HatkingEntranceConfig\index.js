import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import dateString from '@/utils/dateString'
// import { QuestionCircleOutlined } from '@ant-design/icons'
// import { Card, Form, Input, InputNumber, Modal, Select, Table, Tabs, Tooltip } from 'antd'
import { Card, Form, Input, Modal, Select, Table, Tabs } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'

const namespace = 'hatkingEntranceConfig'
const getLevelListUri = `${namespace}/getLevelList`
const updateLevelItemUri = `${namespace}/updateLevelItem`
const getHistoryListUri = `${namespace}/getHistoryList`
const updateHistoryItemUri = `${namespace}/updateHistoryItem`
const FormItem = Form.Item
const TabPane = Tabs.TabPane
const Option = Select.Option

const nobleMap = { 0: '无', 1: '富', 2: '剑士', 3: '骑士', 4: '将军', 5: '公爵', 6: '君王', 7: '帝皇', 8: '超神·帝皇' }

@connect(({ hatkingEntranceConfig }) => ({
  model: hatkingEntranceConfig
}))
class HatkingEntranceConfig extends Component {
  // 列表结构
  configColumns = [
    { title: '#', dataIndex: 'id', align: 'center' },
    { title: '功能名称', dataIndex: 'name', align: 'center' },
    { title: '用户YY等级', dataIndex: 'yyLevelLimit', align: 'center' },
    { title: '主持等级', dataIndex: 'compereLevelLimit', align: 'center' },
    { title: '水晶公会等级', dataIndex: 'guildLevelLimit', align: 'center' },
    { title: '用户超神贵族等级', dataIndex: 'ecologyNobleLevelLimit', align: 'center', render: (text, record, index) => { return nobleMap[text] } },
    { title: '修改时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (text === 0 ? '无' : dateString(text)) },
    { title: '修改人', dataIndex: 'opNick', align: 'center' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(true, record)}>更新</a>
        </span>)
    }
  ]

    // 列表结构
    historyColumns = [
      { title: '模块ID', dataIndex: 'id', align: 'center' },
      { title: '模块名称', dataIndex: 'name', align: 'center' },
      { title: '状态', dataIndex: 'status', align: 'center', render: (text, record) => (text === 1 ? '开启' : '暂停') },
      { title: '修改时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (text === 0 ? '无' : dateString(text)) },
      { title: '修改人', dataIndex: 'opNick', align: 'center' },
      { title: '操作',
        key: 'operation',
        align: 'center',
        render: (text, record) => (
          <span>
            <a onClick={this.showModal(true, record)}>更新</a>
          </span>)
      }
    ]

  defaultPageValue = { defaultPageSize: 10, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, historyVisible: false, isUpdate: false, value: {}, activityKey: '1' }

  // 获取列表
  componentDidMount () {
    this.loadData()
  }

    loadData = (activityKey) => {
      if (activityKey === undefined) {
        activityKey = '1'
      }

      const { dispatch } = this.props
      dispatch({
        type: this.getSelectedQueryKey(activityKey)
      })
    }

    getSelectedQueryKey = (activityKey) => {
      let uri = ''
      if (activityKey === '1') {
        uri = getLevelListUri
      } else if (activityKey === '2') {
        uri = getHistoryListUri
      }

      return uri
    }

    getSelectedUpdateKey = (activityKey) => {
      let uri = ''
      if (activityKey === '1') {
        uri = updateLevelItemUri
      } else if (activityKey === '2') {
        uri = updateHistoryItemUri
      }

      return uri
    }

  handleSubmit1 = e => {
    this.formRef1.submit()
  }

  handleSubmit2 = e => {
    this.formRef2.submit()
  }

  // 添加 与 编辑
  onFinish = values => {
    const { dispatch } = this.props
    const { activityKey } = this.state

    dispatch({
      type: this.getSelectedUpdateKey(activityKey),
      payload: values
    })

    if (this.state.activityKey === '1') {
      this.formRef1.resetFields()
      this.setState({ visible: false })
    } else {
      this.formRef2.resetFields()
      this.setState({ historyVisible: false })
    }
  }

  tabOnChange = type => activityKey => {
    if (type !== undefined || type != null) {
      activityKey = type
    }
    this.setState({ activityKey: activityKey })
    this.loadData(activityKey)
  }

  // 显示弹窗
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.state.activityKey === '1') {
      if (this.formRef1) {
        this.formRef1.resetFields()
        this.formRef1.setFieldsValue(v)
      }
      this.setState({ visible: true, value: record, isUpdate: isUpdate, title: '更新入口限制配置' })
    } else {
      if (this.formRef2) {
        this.formRef2.resetFields()
        this.formRef2.setFieldsValue(v)
      }
      this.setState({ historyVisible: true, value: record, isUpdate: isUpdate, title: '更新入口开关状态' })
    }
  }

  // 关闭弹窗
  hidModal = () => {
    if (this.state.activityKey === '1') {
      this.setState({ visible: false })
    } else {
      this.setState({ historyVisible: false })
    }
  }

  render () {
    const { route, model: { historyList } } = this.props
    const { historyVisible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 4 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 10 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs onChange={this.tabOnChange()} type='card'>
            <TabPane tab='帽子王入口限制配置' key='2'> 
              <Form>
                <Table dataSource={historyList} columns={this.historyColumns} rowKey={(record, index) => index} pagination={false} size='small' />
              </Form>
              <Modal forceRender visible={historyVisible} title={title} onCancel={this.hidModal} onOk={this.handleSubmit2}>
                <Form {...formItemLayout} ref={form => { this.formRef2 = form }} onFinish={this.onFinish}>
                  <FormItem label='模块名称' name='name'>
                    <Input readOnly disabled />
                  </FormItem>
                  <FormItem label='开关状态' name='status'>
                    <Select><Option value={0}>暂停</Option><Option value={1}>开启</Option></Select>
                  </FormItem>
                  <FormItem name='id'>
                    <Input hidden />
                  </FormItem>
                </Form>
              </Modal>
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default HatkingEntranceConfig
