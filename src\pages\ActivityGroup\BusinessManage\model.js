import { addBusiness, pageListBusiness, removeBusiness, updateBusiness } from '../common'

export default {
  namespace: 'BusinessManage',
  state: {
    dataList: []
  },

  reducers: {
    // 更新数据列表，usage:
    // yield put({
    //  type: 'updateList',
    //  payload: {
    //    name: 'configList', list: Array.isArray(data) ? data : []
    //  }
    // })
    updateList (state, { payload }) {
      let obj = { ...state }
      obj[payload.name] = (payload.list || []).map((i, index) => {
        i.index = index + 1
        return i
      })
      return obj
    }
  },

  // 数据变更
  effects: {
    // 分页查询规则函数列表
    * pageListBusiness ({ payload, callback }, { call, put }) {
      const { data } = yield call(pageListBusiness, payload)
      yield put({
        type: 'updateList',
        payload: {
          name: 'dataList',
          list: data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
        }
      })

      if (callback) {
        callback(data.data)
      }
    },

    * updateBusiness ({ payload, callback }, { call, put }) {
      const { data } = yield call(updateBusiness, payload)
      if (callback) {
        callback(data)
      }
    },

    * addBusiness ({ payload, callback }, { call, put }) {
      const { data } = yield call(addBusiness, payload)
      if (callback) {
        callback(data)
      }
    },

    * removeBusiness ({ payload, callback }, { call, put }) {
      const { data } = yield call(removeBusiness, payload)
      if (callback) {
        callback(data)
      }
    }

  }
}
