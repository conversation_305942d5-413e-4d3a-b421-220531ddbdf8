import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists () {
  return request('/ActivityValueSettleSummary/GetLists')
}

export function add (params) {
  return request(`/ActivityValueSettleSummary/AddItem?${stringify(params)}`)
}

export function update (params) {
  return request(`/ActivityValueSettleSummary/UpdateItem?${stringify(params)}`)
}

export function getDetailLists (params) {
  return request(`/ActivityValueSettleDetail/GetDetailLists?${stringify(params)}`)
}

export function publish (params) {
  return request(`/ActivityValueSettleDetail/PublishDetailItems?${stringify(params)}`)
}

export function removeDetailLists (params) {
  return request(`/ActivityValueSettleDetail/RemoveDetailItems?${stringify(params)}`)
}
