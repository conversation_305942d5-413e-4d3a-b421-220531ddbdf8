import request from '@/utils/request'
import { stringify } from 'qs'

export function getCompereLevelInfo (params) {
  return request(`/grade/boss/operate/getCompereLevelInfo?${stringify(params)}`)
}

export function setCompereLevel (params) {
  return request(`/grade/boss/operate/setCompereLevel`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function getUserLevelInfo (params) {
  return request(`/grade/boss/operate/getUserLevelInfo?${stringify(params)}`)
}

export function setUserLevel (params) {
  return request(`/grade/boss/operate/setUserLevel`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function getCompereLevelOptHistory (params) {
  return request(`/grade/boss/operate/getCompereLevelOptHistory/${params.uid}`)
}

export function getUserLevelOptHistory (params) {
  return request(`/grade/boss/operate/getUserLevelOptHistory/${params.uid}`)
}
