import request from '@/utils/request'
import { stringify } from 'qs'

const uri = `/fts_hgame/roommgr_seal_task/boss/advance`

// ------------------------任务配置-----------------------------------//
// 获取任务配置列表
export function listTaskConfig (params) {
  return request(`${uri}/list_task_config?${stringify(params)}`)
}

// 删除任务配置
export function deleteTaskConfig (params) {
  return request(`${uri}/delete_task_config?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// ------------------------任务配置-----------------------------------//

// ------------------------发奖确认-----------------------------------//
// 发奖确认列表
export function listRewardConfirm (params) {
  return request(`${uri}/list_reward_confirm?${stringify(params)}`)
}

// 发奖确认提交审批
export function approvalRewardConfirm (params) {
  return request(`${uri}/approval_reward_confirm?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 发奖确认刷新
export function flushRewardConfirm (params) {
  return request(`${uri}/flush_reward_confirm?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}
// ------------------------发奖确认-----------------------------------//

//  任务完成进度报表
export function listTaskProgress (params) {
  return request(`${uri}/list_task_progress?${stringify(params)}`)
}

// 发奖明细
export function listRewardHistory (params) {
  return request(`${uri}/list_reward_history?${stringify(params)}`)
}
