import * as api from './api'
import { message } from 'antd'

export default {
  namespace: 'hgameAnnouncement',

  state: {
    list: []
  },

  reducers: {
    displayList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        list: payload
      }
    }

  },

  effects: {
    * listAnnouncement ({ payload }, { call, put }) {
      let { data } = yield call(api.listAnnouncement, payload)

      let list = Array.isArray(data.message_list) ? data.message_list : []

      for (let i = 0; i < list.length; i++) {
        list[i].idx = i + 1
      }
      console.log(list)
      yield put({
        type: 'displayList',
        payload: list
      })
    },

    * addAnnouncement ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.addAnnouncement, payload)
      if (status !== 0) {
        if (msg) message.error({ content: msg })
        else message.error({ content: '系统错误' })
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listAnnouncement',
        payload: {}
      })
    },

    * updateAnnouncement ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.updateAnnouncement, payload)
      if (status !== 0) {
        if (msg) message.error({ content: msg })
        else message.error({ content: '系统错误' })
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listAnnouncement',
        payload: {}
      })
    },

    * deleteAnnouncement ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.deleteAnnouncement, payload)
      if (status !== 0) {
        if (msg) message.error({ content: msg })
        else message.error({ content: '系统错误' })
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listAnnouncement',
        payload: {}
      })
    }
  }
}
