import { Button, Card, DatePicker, Divider, Form, Table } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import exportExcel from '@/utils/exportExcel'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
// const gameMap = { 0: 'ALL', 6: '超能运动会' }
// const chanMap = { all: 'ALL', pc: 'PC端', dreamer: 'YO交友', yomi: 'YO语音' }

@connect(({ arkJy }) => ({ // model 的 namespace
  model: arkJy // model 的 namespace
}))

class ArkDailyBetTop10InfoComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      list: [],
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
  }

    // 日期 渠道类型  魔豆模拟流水 付费用户数 剩余发放监控汇总  成功总流水 成功总人数 累计成功人数  累计失败人数
    columns = [
      { title: '日期', dataIndex: 'date', align: 'center' },
      {
        title: 'uid',
        dataIndex: 'uid',
        key: 'uid',
        align: 'center',
        render: (text, record) => {
          switch (text) {
            case 0: return 'all'
            default: return text
          }
        }
      },
      { title: '昵称',
        dataIndex: 'nick',
        align: 'center',
        render: (text, record) => {
          switch (record.uid) {
            case 0: return '-'
            default: return text
          }
        }
      },
      { title: '盖章流水(元)', dataIndex: 'sealAmount', align: 'center' },
      { title: '参与流水(元)', dataIndex: 'amethyst', align: 'center' },
      { title: '发放流水(元)', dataIndex: 'awardAmount', align: 'center' },
      { title: '发放占比', dataIndex: 'awardPro', align: 'center' },
      { title: '参与次数', dataIndex: 'count', align: 'center' },
      { title: '成功次数', dataIndex: 'awardCount', align: 'center' },
      { title: '偏好位置', dataIndex: 'positionStr', align: 'center' },
      { title: '参与次数比', dataIndex: 'positionPro', align: 'center' },
      { title: '流水占比', dataIndex: 'positionAmountPro', align: 'center' },
      { title: '参与流水最高频道', dataIndex: 'sidListStr', align: 'center' },
      { title: '流水占比', dataIndex: 'sidAmountPro', align: 'center' },
      { title: '参与流水最高子频道', dataIndex: 'ssidListStr', align: 'center' },
      { title: '流水占比', dataIndex: 'ssidAmountPro', align: 'center' }
    ]

    loadData = () => {
      const { dispatch } = this.props
      const { dateRange } = this.state
      const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
      const { modelName } = this.props
      dispatch({
        type: `${modelName}/getTopNRangeList`,
        payload: data
      })
    }

    onClick = () => {
      this.loadData()
    }

    onChange = (date, format) => {
      console.log('date', date)
      this.setState({ dateRange: date })
    }

    onStartChange = (value) => {
      this.onChange('startValue', value)
    }

    onEndChange = (value) => {
      this.onChange('endValue', value)
    }

    handleSelectChange = (value) => {
      console.log(value)
    }

    onExport = () => {
      let headers = []
      let columns = this.columns
      columns.forEach(function (item) {
        headers.push({ key: item.dataIndex, header: item.title })
      })

      const { model: { topnUserInfoList } } = this.props
      var exportData = topnUserInfoList.map(item => {
        let v = $.extend(true, {}, item)
        if (v.uid === 0) {
          v.nick = '-'
        }
        return v
      })

      exportExcel(headers, exportData)
    }

    /* *******************************页面布局***************************************************************/
    render () {
      const { model: { topnUserInfoList } } = this.props
      const { dateRange } = this.state
      return (
        <Card>
          <Form>
            <span style={{ marginLeft: 10 }}>时间范围:</span>
            <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
            <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
            <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
            <Divider />
            <Table dataSource={topnUserInfoList} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
          </Form>
        </Card>
      )
    }
}

export default ArkDailyBetTop10InfoComponent
