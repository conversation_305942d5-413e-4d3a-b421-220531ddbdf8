import { connect } from 'dva'
import React, { Component } from 'react'
import Page<PERSON>eaderWrapper from '@/components/PageHeaderWrapper'
import { copyToClip, fetchAllRemoteData, formatOptions, parseOptionsFromMultiLineString } from '@/utils/common'
import { CopyTwoTone, SearchOutlined } from '@ant-design/icons'
import ExportRemoteData from '@/components/ExportRemoteData'
import { configTypeOptions, elementTypeWithoutObjectOptions, ruleDataTypeOptions } from '../common'
import { getRuleTokensError, Kind, parseRuleSourceTokens } from '../ruleengine'
import { Button, Card, Col, Form, Input, message, Modal, Row, Select, Space, Spin, Table } from 'antd'

const namespace = 'RealtimeGroupTools'

const querySourceOptions = [
  { value: 'JY', label: '交友' },
  { value: 'PK', label: '约战' },
  { value: 'BABY', label: '宝贝' },
  { value: 'PW', label: '陪玩' },
  { value: 'ZW', label: '追玩' }
]

@connect(({ RealtimeGroupTools }) => ({
  model: RealtimeGroupTools
}))

class RealtimeGroupTools extends Component {
  constructor (props) {
    super(props)
    this.loadCommonSelectOptions()
  }

  state = {
    list: [], loadingVisible: false, currentPage: 1, perPage: 1000, currentFetch: 0, pagination: { current: 1, pageSize: 10 }
  }

  loadCommonSelectOptions = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/listAllBusiness`
    })
    dispatch({
      type: `${namespace}/listAllRuleData`
    })
    dispatch({
      type: `${namespace}/listAllRuleOptions`
    })
  }

  getColumns = () => {
    let groupOptions = this.getGroupOptions(null, true)
    let groupOptions2 = this.getGroupOptions(null, true, true)
    return [
      { title: 'index', dataIndex: 'index', align: 'left', ellipsis: true },
      { title: 'member', dataIndex: 'member', align: 'left', ellipsis: true },
      { title: 'groupID', dataIndex: 'group', align: 'left', ellipsis: true, options: groupOptions2 },
      { title: 'groupDesc', dataIndex: 'group', align: 'left', ellipsis: true, options: groupOptions }
    ]
  }

  exportRenderColumns = (columns) => {
    return columns
  }

  renderColumns = (columns) => {
    columns = columns || this.columns
    let getLocalFilter = (column) => {
      return (v, rec) => {
        return String(v).localeCompare(rec[column.dataIndex]) === 0
      }
    }
    let getOptionRender = (options) => {
      return (val, record) => formatOptions(val, options)
    }
    return columns.map(v => {
      if (v.disabledSearch) {
        return v
      }
      if (v.options) {
        v.options.forEach(opt => {
          opt.text = opt.label
        })
        v.filters = v.options
        v.onFilter = getLocalFilter(v)
        v.filterIcon = <SearchOutlined />
        v.filterMode = 'tree'
        v.filterSearch = true

        if (!v.render) {
          v.render = getOptionRender(v.options)
        }

        return v
      }
      v = Object.assign(this.getColumnSearchTextProps(v.dataIndex), v)
      return v
    })
  }

  // 搜索文本
  getColumnSearchTextProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={node => {
            this.searchInput = node
          }}
          placeholder={`Search ...`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => this.handleSearch(selectedKeys, confirm, dataIndex)}
          style={{ marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button size='small' onClick={() => this.handleReset(clearFilters)}>重置</Button>
          <Button type='primary' size='small' onClick={() => this.handleSearch(selectedKeys, confirm, dataIndex)}>确定</Button>
        </Space>
      </div>
    ),
    filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
        : ''
  })

  handleSearch = (selectedKeys, confirm, dataIndex, table) => {
    confirm()
    this.setState({
      searchText: '', searchedColumn: dataIndex
    })
  }

  handleReset = (clearFilters, table) => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  // 点击查询
  onQueryClick = (values) => {
    // 换行符 切割字符串
    let members = (values.members || '').split(/[\r\n]+/)
    if (!values.members || !members || members.length < 1) {
      message.warn('当前没有输入待分组成员')
      return false
    }

    if (!this.formRefEditRule || !this.formRefEditRule.getFieldValue('ruleProgram')) {
      message.warn('还没有填写分组规则')
      this.showEditRuleModal()
      return false
    }

    const ruleProgram = this.formRefEditRule.getFieldValue('ruleProgram')
    const self = this
    fetchAllRemoteData({
      pageSize: 50,
      showLoading: (page, pageSize, total) => {
        self.setState({ loadingVisible: true, currentPage: page, perPage: pageSize, currentFetch: total })
      },
      closeLoading: (page, pageSize, total) => {
        self.setState({ loadingVisible: false })
      },
      uriBuilder: (page, pageSize) => {
        let startIndex = (page - 1) * pageSize
        let endIndex = startIndex + pageSize
        if (endIndex > members.length) {
          endIndex = members.length
        }
        let subList = []
        for (let i = startIndex; i < endIndex; i++) {
          subList.push(members[i])
        }
        return {
          fetchURI: `/group_svr/activity/compute_group_result`,
          fetchBody: JSON.stringify({
            business: values.business || 'JY',
            members: subList,
            ruleProgram: ruleProgram
          })
        }
      },
      method: 'POST',
      callback: (dataList) => {
        dataList.forEach((item, index) => {
          item.index = index
        })
        self.setState({ list: dataList })
      }
    })
  }

  ruleProgramValidator = (ruleObject, value) => {
    const { model: { ruleDataMap, businessMap } } = this.props
    if (value) {
      value = value.trim()
    }
    if (!value) {
      return Promise.resolve()
    }
    try {
      let tokens = parseRuleSourceTokens(value)
      let len = tokens.length
      let errMsg = getRuleTokensError(tokens, (token, index) => {
        // 数字，字符串，bool，变量，检查前后 是不是 {}, 是的话，按照分组结果来检查
        if (token.kind === Kind.BOOL || token.kind === Kind.VAR || token.kind === Kind.NUMBER) {
          let group = '' + token.value
          let clauseMatched = false
          if (group && group !== '0') {
            if (index - 1 >= 0 && index + 1 < len) {
              let pre = tokens[index - 1]
              let next = tokens[index + 1]
              if (pre.kind === Kind.CLAUSE && pre.value === '{' && next.kind === Kind.CLAUSE && next.value === '}') {
                clauseMatched = true
              }
            }
          }
          if (group !== '0' && token.kind === Kind.VAR && !clauseMatched) {
            return '非法变量: ' + token.value
          }
        }

        if (token.kind === Kind.REF) { // 引用类型
          let ruleData = ruleDataMap ? ruleDataMap[token.value] : null
          if (!ruleData) {
            return '不识别的数据引用 $[' + token.value + ']'
          }
          if (index + 1 >= len) { // 没有下一个 token
            return
          }
          // 获取下一个token，看看是不是比较类型
          let nextToken = tokens[index + 1]
          // 下一个是 逻辑操作， 或者 {, 那么验证该数据必须是 bool
          if (nextToken.kind === Kind.LOGIC || (nextToken.kind === Kind.CLAUSE && nextToken.value === '{')) {
            if (ruleData.dataType !== 'bool') {
              return '数据引用 $[' + token.value + ']放在表达式后面必须是 bool 类型'
            }
            return
          }
          // 下一个是 ==, 判断是否有 options
          if (nextToken.kind === Kind.COMPARE && nextToken.value === '==') {
            // 继续找下一个 token，值类型， NUMBER， string，bool，timestamp
            if (index + 2 >= len) {
              return '数据引用 $[' + token.value + '] 等值比较缺少右值'
            }
            let vToken = tokens[index + 2]
            if (vToken.kind !== Kind.STRING && vToken.kind !== Kind.NUMBER && vToken.kind !== Kind.BOOL) {
              return '数据引用 $[' + token.value + '] 等值比较缺少右值'
            }
            if (ruleData.dataType === 'bool' && vToken.kind !== Kind.BOOL) {
              return '数据引用 $[' + token.value + ']放在表达式后面必须是 bool 类型'
            }

            if (ruleData.id === 'QUERY_SOURCE') {
              if (!businessMap[vToken.value]) {
                return '数据引用 $[' + token.value + '] 值 [' + vToken.value + ']不是合法的业务ID'
              }
            }
            let options = ruleData.options || []
            if (options.length > 0) {
              if (formatOptions(vToken.value, options) === '') {
                console.debug(vToken.value, options)
                return '数据引用 $[' + token.value + '] 值 [' + vToken.value + ']非法'
              }
            }

            return
          }
          // IN ，判断IN 后面的值
          if (nextToken.kind === Kind.IN) {
            for (let i = index + 2; i < len; i++) {
              let vToken = tokens[i]
              if (vToken.kind === Kind.CLAUSE && vToken.value === ')') {
                return
              }
              if (vToken.kind === Kind.SEP || (vToken.kind === Kind.CLAUSE && vToken.value === '(')) {
                continue
              }
              if (ruleData.id === 'QUERY_SOURCE') {
                if (!businessMap[vToken.value]) {
                  return '数据引用 $[' + token.value + '] 值 [' + vToken.value + ']不是合法的业务ID'
                }
              }
              let options = ruleData.options || []
              if (options.length > 0) {
                if (formatOptions(vToken.value, options) === '') {
                  console.debug(vToken.value, options)
                  return '数据引用 $[' + token.value + '] 值 [' + vToken.value + ']非法'
                }
              }
            }
          }
        }
      })
      if (!errMsg) {
        return Promise.resolve()
      }
      console.debug(tokens)
      return Promise.reject(new Error(errMsg))
    } catch (e) {
      return Promise.reject(e)
    }
  }

  getGroupOptions = (value, ignoreEmpty, useValueAsDesc) => {
    if (value === null) {
      if (this.formRefEditRule) {
        value = this.formRefEditRule.getFieldValue('groupOptions')
      }
    }
    let options = []
    if (value) {
      let opts = parseOptionsFromMultiLineString(value)
      if (opts && opts.length > 0) {
        options = options.concat(opts)
      } else if (!ignoreEmpty) {
        return false
      }
    } else if (!ignoreEmpty) {
      return false
    }

    let exists = {}
    options.forEach(v => { exists[v.value] = v.label })

    const { list } = this.state
    if (list && list.length > 0) {
      list.forEach(item => {
        if (!exists[item.group]) {
          exists[item.group] = item.group
          options.push({ label: item.group, value: item.group })
        }
      })
    }
    if (!exists['-1']) {
      options = [{ value: '-1', label: '无分组' }].concat(options)
    }

    if (useValueAsDesc) {
      options.forEach(item => { item.label = item.value })
    }

    return options
  }

  showTranslateRuleProgram = (ruleProgram) => {
    const { model: { ruleDataMap, businessMap } } = this.props
    let tokens = parseRuleSourceTokens(ruleProgram, false)
    let contents = []
    let skipBlank = (i, justFilter) => {
      i++
      for (; i < tokens.length; i++) {
        let token = tokens[i]
        if (token.kind !== Kind.BLANK) {
          return i
        }
        if (!justFilter) {
          contents.push(token.value)
        }
      }
      return i
    }
    for (let i = 0; i < tokens.length; i++) {
      let token = tokens[i]
      switch (token.kind) {
        case Kind.REF:
          let ruleData = ruleDataMap[token.value]
          if (ruleData) {
            contents.push(ruleData.name)
            if (ruleData.id === 'QUERY_SOURCE' || (ruleData.options && ruleData.options.length > 0)) {
              i = skipBlank(i)
              // 查找后续比较值
              let next = i < tokens.length ? tokens[i] : null
              if (!next || (next.kind !== Kind.IN && next.kind !== Kind.COMPARE)) {
                i--
                break
              }
              contents.push(next.value)
              i = skipBlank(i)
              for (; i < tokens.length; i++) {
                let vToken = tokens[i]
                if (vToken.kind === Kind.CLAUSE && vToken.value === ')') {
                  i--
                  break
                }
                if (vToken.kind === Kind.STRING || vToken.kind === Kind.NUMBER || vToken.kind === Kind.BOOL) {
                  let content = vToken.value
                  if (ruleData.id === 'QUERY_SOURCE') {
                    let business = businessMap[vToken.value]
                    if (business) {
                      content = business.name
                    }
                  }
                  if (ruleData.options && ruleData.options.length > 0) {
                    for (let k = 0; k < ruleData.options.length; k++) {
                      let o = ruleData.options[k]
                      if (o.value === vToken.value) {
                        content = o.label
                        break
                      }
                    }
                  }
                  contents.push(content)
                  if (next.kind !== Kind.IN) {
                    break
                  }
                }
                contents.push(vToken.value)
              }
            }
          }
          break
        case Kind.CLAUSE: // 子句 {}
          if (token.value === '{') {
            let nIndex = skipBlank(i, true)
            if (nIndex < tokens.length) {
              let next = tokens[nIndex]
              if (next.kind === Kind.STRING || next.kind === Kind.NUMBER || next.kind === Kind.BOOL || next.kind === Kind.VAR) {
                let nnIndex = skipBlank(nIndex, true)
                if (nnIndex < tokens.length) {
                  let close = tokens[nnIndex]
                  if (close.kind === Kind.CLAUSE && close.value === '}') {
                    contents.push('返回分组 ==> [' + next.value + ']')
                    i = nnIndex
                    break
                  }
                }
              }
            }
          }
          contents.push(token.value)
          break
        default:
          contents.push(token.value)
      }
    }
    ruleProgram = contents.join('')
    Modal.info({
      title: '规则翻译',
      content: <Input.TextArea rows={15} style={{ width: '100%', height: '100%' }} defaultValue={ruleProgram} readOnly />,
      centered: true,
      width: '50%'
    })
  }

  getRenderContentForCompareDesc = (ruleData) => {
    let ref = '$[' + ruleData.id + ']'
    switch (ruleData.dataType) {
      case 'string':
        return (<>
          <div>== : 等值比较， 如 {ref} == 'A'</div>
          <div>in : 判断给定值在某个范围内，如 {ref} in ('a', 'b')</div>
        </>
        )
      case 'timestamp':
        return (<>
          <div>==, >, >=, &lt;, &lt;= : 等值比较，如 {ref} > '2021-01-02 00:00:00'</div>
        </>
        )
      case 'number':
        return (<>
          <div>==, >, >=, &lt;, &lt;= : 如 {ref} == 1， {ref} > 1</div>
        </>
        )
      case 'bool':
        return (<>
          <div>== : 等值比较，如 {ref} == true， {ref} == false</div>
        </>
        )
      default:
        return ''
    }
  }

  onEditRuleFormSubmit = () => {
    let groupOptions = this.getGroupOptions(null, true)
    this.setState({ editRuleModalVisible: false, groupOptions: groupOptions })
  }

  showEditRuleModal = () => {
    this.setState({ editRuleModalVisible: true })
  }

  closeEditRuleModal = () => {
    this.setState({ editRuleModalVisible: false })
  }

  onEditRuleModalOk = () => {
    this.formRefEditRule.submit()
  }

  onDemoRuleClick = (demo) => {
    if (!this.formRefEditRule) {
      return
    }
    this.formRefEditRule.setFieldsValue({
      groupOptions: demo.groupOptions,
      ruleProgram: demo.ruleProgram
    })
  }

  // 编辑规则对话框
  renderEditRuleModal = () => {
    const { editRuleModalVisible, selectedRuleData } = this.state
    const { model: { businessList, ruleDataList, ruleOptions, defaultRuleOptions } } = this.props
    const ruleDataOptions = (ruleDataList || []).filter(item => item.id.indexOf('ALIAS_ID') < 0).map(item => {
      let business = formatOptions(item.businessID, businessList, '-', 'id', 'name')
      return {
        label: business + ' | ' + item.type + ' | ' + item.name,
        value: business + ':' + item.type + ' | ' + item.id + ':' + item.name,
        data: item
      }
    })

    const firstRule = defaultRuleOptions[0]

    return (
      <Modal width={'100%'} title={'编辑分组规则'} visible={editRuleModalVisible} onCancel={this.closeEditRuleModal} onOk={this.onEditRuleModalOk}>
        <Form labelCol={{ span: 4 }} ref={(form) => {
          this.formRefEditRule = form
        }} onFinish={this.onEditRuleFormSubmit}>
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item labelCol={{ span: 7 }} name={'ruleID'} label={'常用分组规则'} initialValue={'common_tt_sex_av_compere'}>
                <Select
                  options={(ruleOptions || []).map(v => {
                    return { label: v.label, value: v.value, opt: v }
                  })}
                  onChange={(v, opt) => { this.onDemoRuleClick(opt.opt) }}
                  optionFilterProp='label'
                  showSearch
                  filterOption={(input, option) => {
                    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }} />
              </Form.Item>

              <Form.Item labelCol={{ span: 7 }} name={'groupOptions'} initialValue={firstRule.groupOptions} label={'分组映射'} tooltip={{ placement: 'rightTop', title: '格式：分组ID:分组名称，多个的话换行分隔' }} rules={[{
                validator: (_, value) => {
                  if (!value) {
                    return Promise.resolve()
                  }
                  let opts = this.getGroupOptions(value, false)
                  if (opts === false || opts.length < 1) {
                    return Promise.reject(new Error('分组映射不能为空，格式为：分组ID:分组名称，多个的话换行分隔'))
                  }
                  return Promise.resolve()
                }
              }]}>
                <Input.TextArea rows={15} placeholder='输入分组映射' />
              </Form.Item>
            </Col>
            <Col span={11}>
              <Form.Item labelCol={{ span: 2 }} name={'ruleProgram'} initialValue={firstRule.ruleProgram} label={'规则'} tooltip={{ placement: 'leftTop', title: '使用 $[规则数据引用ID] 来引用特定的数据，具体可用数据在右侧查询' }} rules={[{
                validator: this.ruleProgramValidator
              }]}>
                <Input.TextArea rows={22} placeholder='输入分组规则代码' />
              </Form.Item>
              <Button style={{ float: 'right' }} size={'small'} type={'default'}
                onClick={() => this.showTranslateRuleProgram(this.formRefEditRule.getFieldValue('ruleProgram'))}>翻译规则</Button>
            </Col>
            <Col span={7}>
              <label style={{ width: 200, marginRight: 10 }}>可用数据</label>
              <Select placeholder={'搜索可用数据'}
                style={{ width: '80%' }}
                options={ruleDataOptions}
                showSearch
                onChange={(item, v) => {
                  if (v.data.id === 'QUERY_SOURCE') {
                    v.data.options = businessList
                  }
                  this.setState({ selectedRuleData: v.data })
                }} />
              <div style={{ marginLeft: 70, marginTop: 5, color: 'red' }}>如果没有对应数据，请联系开发</div>

              {
                !selectedRuleData || !selectedRuleData.id
                  ? ''
                  : <div style={{ marginTop: 20 }}>
                    <Card style={{ width: '100%' }}>
                      <Row style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>引用方式：</label>
                        <div style={{ float: 'right' }}>
                          {'$[' + selectedRuleData.id + ']'} <CopyTwoTone size={'small'} onClick={() => copyToClip('$[' + selectedRuleData.id + ']', '$[' + selectedRuleData.id + ']')} />
                        </div>
                      </Row>
                      <Row style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>归属业务：</label>
                        <div style={{ float: 'right' }}>{formatOptions(selectedRuleData.businessID, businessList, '-', 'id', 'name')}</div>
                      </Row>
                      <Row style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>数据说明：</label>
                        <div style={{ float: 'right' }}>{selectedRuleData.desc || selectedRuleData.name}</div>
                      </Row>
                      <Row style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>配置类型：</label>
                        <div style={{ float: 'right' }}>{formatOptions(selectedRuleData.configType, configTypeOptions)}</div>
                      </Row>
                      <Row style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>数据类型：</label>
                        <div style={{ float: 'right' }}>{formatOptions(selectedRuleData.dataType, elementTypeWithoutObjectOptions)}</div>
                      </Row>
                      <Row style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>比较类型：</label>
                        <div style={{ float: 'right' }}>{this.getRenderContentForCompareDesc(selectedRuleData)}</div>
                      </Row>
                      <Row style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>来源类型：</label>
                        <div style={{ float: 'right' }}>{formatOptions(selectedRuleData.type, ruleDataTypeOptions)}</div>
                      </Row>
                      <Row hidden={!selectedRuleData.options || selectedRuleData.options.length < 1} style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>可选项：</label>
                        <div style={{ float: 'right' }}>{selectedRuleData.options.map((item, index) => {
                          let value = item.value || item.id
                          return <div key={selectedRuleData.id + '_' + value + '_' + index}>{(item.label || item.name) + ' : ' + value}
                            <CopyTwoTone size={'small'} style={{ marginLeft: 5 }} onClick={() => copyToClip(value, value)} />
                          </div>
                        })}</div>
                      </Row>
                    </Card>
                  </div>
              }
            </Col>
          </Row>
        </Form>
      </Modal>
    )
  }

  render () {
    const { route } = this.props
    const { loadingVisible, currentPage, perPage, currentFetch, list } = this.state

    const pagination = {
      pageSize: this.state.pagination.pageSize,
      current: this.state.pagination.current,
      pageSizeOptions: [10, 20, 50, 100],
      showSizeChanger: true,
      onChange: (page, pageSize) => {
        this.setState({ pagination: { current: page, pageSize: pageSize } })
      },
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    }

    const srcColumns = this.getColumns()
    const columns = this.renderColumns(srcColumns)
    const exportColumns = this.exportRenderColumns(srcColumns)

    return (
      <>
        <PageHeaderWrapper title={route.name}>
          <Row style={{ marginBottom: '1em' }}>
            <Form labelCol={{ span: 12 }} layout={'inline'} ref={form => {
              this.formRefQuery = form
            }} onFinish={this.onQueryClick}>
              <Form.Item name={'members'} label={'members'} required>
                <Input.TextArea style={{ height: 100 }} placeholder='members, 每行一个' allowClear />
              </Form.Item>

              <Form.Item name={'business'} label={'business'} required initialValue={'JY'}>
                <Select options={querySourceOptions} />
              </Form.Item>

              <Button type='primary' onClick={() => {
                this.setState({ editRuleModalVisible: true })
              }}>编辑分组规则</Button>
              <Button disabled={!this.formRefEditRule || !this.formRefEditRule.getFieldValue('ruleProgram')} type='primary' style={{ marginLeft: 20 }} htmlType='submit'>查询</Button>
              <ExportRemoteData disabled={!list || list.length < 1} buttonStyle={{ marginLeft: 20 }} filename={'group_result'} columns={exportColumns} dataProvider={() => list} />
            </Form>
          </Row>
          <Row style={{ marginBottom: '1em' }}>
            <Col span={24}>
              <Table columns={columns}
                dataSource={list}
                size='small'
                scroll={{ x: 'max-content' }}
                pagination={pagination}
                showSorterTooltip={false}
                rowKey={record => record.index}
              />
            </Col>
          </Row>

          <Modal visible={loadingVisible} footer={null} closable={false} centered>
            <Spin
              tip={'正在加载第[' + currentPage + ']页数据（每页[' + perPage + ']条）, 当前已加载[' + currentFetch + ']......'} />
          </Modal>

          {this.renderEditRuleModal()}
        </PageHeaderWrapper>
      </>
    )
  }
}

export default RealtimeGroupTools
