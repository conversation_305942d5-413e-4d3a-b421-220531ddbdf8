import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Col, Row, Table, Typography } from 'antd'
const { Text } = Typography

const namespace = 'appReviewConfig'

@connect(({ appReviewConfig }) => ({
  model: appReviewConfig
}))

// 审批记录
class AppReviewApprovalList extends Component {
  componentDidMount = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getApprovalList`
    })
  }

  retStatusDesc = (val) => {
    switch (val) {
      case 0:
        return <Text type='strong'>审批中</Text>
      case 1:
        return <Text type='success'>已通过</Text>
      case 2:
        return <Text type='secondary'>已驳回</Text>
    }
    return '-'
  }

  columns = [
    { title: '序号', dataIndex: 'index' },
    { title: 'app', dataIndex: 'hostNameDesc' },
    { title: '是否生效', dataIndex: 'filterOnDesc' },
    { title: '平台', dataIndex: 'platformDesc' },
    { title: '版本', dataIndex: 'hostVersion' },
    { title: 'app渠道来源', dataIndex: 'channelSourceDesc' },
    { title: '应用ID', dataIndex: 'ignoreEntryListDesc' },
    { title: '审批状态', dataIndex: 'status', render: val => this.retStatusDesc(val) },
    { title: '提交人', dataIndex: 'opUser' },
    { title: '时间', dataIndex: 'updateDate' }
  ].map(raw => {
    raw.align = 'center'
    return raw
  })

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '100', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  render () {
    const { model: { approvalList } } = this.props
    return (
      <Card>
        <Row>
          <Col span={24}>
            <Table columns={this.columns}
              pagination={this.defaultPageValue}
              dataSource={approvalList} />
          </Col>
        </Row>
      </Card>
    )
  }
}
export default AppReviewApprovalList
