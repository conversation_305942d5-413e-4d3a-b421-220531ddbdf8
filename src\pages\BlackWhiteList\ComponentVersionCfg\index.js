import React, { Component } from 'react'
import { connect } from 'dva'
import <PERSON>HeaderWrapper from '@/components/PageHeaderWrapper'
import { Card } from 'antd'
import WhiteListCfg from './components/whitelistCfg'
import ComponentCfg from './components/componentCfg'
import ComponentTypeCfg from './components/componentTypeCfg'
import UpdateHistory from './components/updateHistory'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
const namespace = 'componentVersionCfg'

@connect(({ componentVersionCfg }) => ({
  model: componentVersionCfg
}))

class ComponentVersionCfg extends Component { // 要改
  state = {}

  componentDidMount = () => {
    this.callModel('getAllSimpleComponent')
    this.callModel('getAllComponentTypeList')
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 标签页发生切换
  onTagChange = (record) => {
    if (record === '4') {
      this.callModel('getOpHistory')
    }
    this.callModel('getAllSimpleComponent')
    this.callModel('getAllComponentTypeList')
  }

  render () {
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs id='cvc' defaultActiveKey='1' type='card' size='large' onChange={(record) => this.onTagChange(record)}>
            <TabPane tab='白名单配置' key='1'>
              <WhiteListCfg />
            </TabPane>
            <TabPane tab='组件管理' key='2'>
              <ComponentCfg />
            </TabPane>
            <TabPane tab='业务管理' key='3'>
              <ComponentTypeCfg />
            </TabPane>
            <TabPane tab='更改历史' key='4'>
              <UpdateHistory />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default ComponentVersionCfg
