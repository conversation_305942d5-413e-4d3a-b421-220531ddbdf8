import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const getWhiteList = genGetRequireTemplate('/ting_tool_boss/get_pk_whitelist', 'whitelist')
const getTingToolConfig = genGetRequireTemplate('/ting_tool_boss/get_ting_tool_config', 'pkConfig')
const addWhiteList = genUpdateTemplate('/ting_tool_boss/update_pk_whitelist')

const getApprovalList = genGetRequireTemplate('/ting_tool_boss/get_activity_by_tag?tag=approval', 'approvalList', (raw) => {
  return raw.sort((a, b) => {
    if (a.statusDesc !== b.statusDesc) return a.statusDesc < b.statusDesc ? 1 : -1
    return b.submitTime - a.submitTime
  })
})
const getRewardList = genGetRequireTemplate('/ting_tool_boss/get_activity_by_tag?tag=reward', 'rewardList', (raw) => {
  return raw.sort((a, b) => {
    return b.submitTime - a.submitTime
  })
})

const getApprovalDetail = genGetRequireTemplate('/ting_tool_boss/get_approval_detail_by_id', 'approvalDetail', (raw) => {
  console.debug('approvalDetail==>', raw)
  let { rewardConfig } = raw
  rewardConfig.forEach(item => {
    item.extraReward = item.extraReward / 100
  })
  raw.rewardConfig = rewardConfig
  return raw
})

const getRewardDetail = genGetRequireTemplate('/ting_tool_boss/get_reward_detail_by_id', 'rewardDetail')

const activityApproval = genUpdateTemplate('/ting_tool_boss/activity_approval')

const getEventLogList = genGetRequireTemplate('/ting_tool_boss/get_event_log', 'eventLogs')

const statPreviewOrExport = genGetRequireTemplate('/ting_tool_boss/summary_export', 'statPreview')

export default {
  namespace: 'hgameTingTools',
  state: {
    pkConfig: {},
    whitelist: [],
    approvalList: [],
    rewardList: [],
    approvalDetail: {},
    rewardDetail: {},
    eventLogs: [],
    statPreview: []
  },
  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getEventLogList,
    getTingToolConfig,
    statPreviewOrExport,
    getWhiteList,
    addWhiteList,
    getApprovalList,
    getApprovalDetail,
    getRewardDetail,
    getRewardList,
    activityApproval
  }

}
