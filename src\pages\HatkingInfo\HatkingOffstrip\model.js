import {
  // addPoolFromServer,
  // removePoolFromServer,
  updatePoolFromServer,
  getPoolFromServer,
  getPorpsList,
  getWarnFromServer,
  removeWarnFromServer,
  upsetWarnFromServer,
  getReleasePoolFromServer
} from './api'
import { message } from 'antd'

export default {
  namespace: 'offstripMain',

  state: {
    listen: null,
    poolList: [], // 奖池列表
    poolConfig: {}, // 单个奖池配置
    propsList: [], // 营收礼物列表
    localPropsList: [], // 本地搜索列表
    warnList: [] // 预警配置
  },

  reducers: {

    listen (state, { payload }) { return { ...state, listen: payload } },

    updatePoolList (state, { payload, dev }) { return { ...state, dev, poolList: payload } },

    updatePoolConfig (state, { payload, dev }) {
      if (state.listen) {
        console.log(payload)
        state.listen(payload)
      }

      return { ...state, poolConfig: payload, dev }
    },

    updatePropsList (state, { payload }) { return { ...state, propsList: payload } },
    updateLocalPropsList (state, { payload }) { return { ...state, propsList: payload, localPropsList: payload } },
    updateWarnList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return { ...state, warnList: payload }
    }
  },

  effects: {

    // 奖池配置
    * getPool ({ payload }, { call, put }) {
      const { data: { list, dev } } = yield call(getPoolFromServer, payload)

      yield put({
        type: 'updatePoolConfig',
        payload: list,
        dev
      })
    },

    // 奖池线上配置
    * getReleasePool ({ payload }, { call, put }) {
      const { data: { list, dev } } = yield call(getReleasePoolFromServer, payload)
      yield put({
        type: 'updatePoolConfig',
        payload: list,
        dev
      })
    },

    * updatePool ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updatePoolFromServer, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getPool',
          payload: { id: payload.id }
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * getPropsConfigList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getPorpsList)

      yield put({
        type: 'updateLocalPropsList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * getWarnList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getWarnFromServer)

      yield put({
        type: `updateWarnList`,
        payload: Array.isArray(list) ? list : []
      })
    },

    * removeWarn ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(removeWarnFromServer, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getWarnList'
        })
      } else {
        message.error(msg)
      }
    },

    * upsetWarn ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(upsetWarnFromServer, payload)
      if (status === 0) {
        message.success('upset success')
        yield put({
          type: `getWarnList`
        })
      } else {
        message.error(msg)
      }
    }
  }
}
