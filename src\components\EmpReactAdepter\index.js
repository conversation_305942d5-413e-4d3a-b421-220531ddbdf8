import React, { Component } from 'react'
import { withRouter } from 'dva/router'
import PropTypes from 'prop-types'
const { ReactAdapter } = window.EMP_ADAPTER_LIB

const ReactAsyncAdapter = ReactAdapter.adapter()

class EmpReactAdepter extends Component {
  getRenderProps = () => {
    const { push, replace, goBack } = this.props.history
    const { params, path } = this.props.match
    const urlSearch = new URLSearchParams(this.props.location.search)
    const search = {}
    urlSearch.forEach((value, key) => {
      search[key] = value
    })

    const router = {
      path,
      push,
      replace,
      goBack,
      search,
      params
    }

    return router
  }

  render () {
    const { remote, ...rest } = this.props
    const router = this.getRenderProps()
    console.log('wcytest', remote, rest)
    return <ReactAsyncAdapter remote={remote} {...rest} router={router} />
  }
}

EmpReactAdepter.propTypes = {
  remote: PropTypes.string
}

export default withRouter(EmpReactAdepter)
