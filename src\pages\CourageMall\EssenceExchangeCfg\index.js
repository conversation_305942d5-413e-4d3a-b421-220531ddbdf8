/* eslint-disable eqeqeq */
import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Row, Col, Input, message, Button, Descriptions } from 'antd'
import { checkIsNumber } from '@/utils/common'

const namespace = 'essenceExchangeCfg'

@connect(({ essenceExchangeCfg }) => ({
  model: essenceExchangeCfg
}))

class EssenceExchangeCfg extends Component {
  state = {
    editingRecord: null,
    totalLimitValue: 0,
    totalLimitValueOld: 0,
    virtOrTeamLimitValue: 0,
    virtOrTeamLimitValueOld: 0,
    essenceLimitEditable: false
  }
  componentDidMount = () => {
    this.queryPodCfg()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 更新豆荚限制配置
  queryPodCfg = () => {
    this.callModel('queryPodCfg', {
      cbFunc: (v, v1) => {
        this.setState({
          totalLimitValue: v,
          totalLimitValueOld: v,
          virtOrTeamLimitValue: v1,
          virtOrTeamLimitValueOld: v1
        })
      }
    })
  }
  // 生成table中的编辑框
  createTableCollEditer = (rawValue, record, dataIndex) => {
    let { editingRecord } = this.state
    if (editingRecord == null || editingRecord.key != record.key) {
      return rawValue
    }
    return <Input width='15em' defaultValue={editingRecord[dataIndex]} onChange={(e) => { editingRecord[dataIndex] = e.target.value; this.setState({ editingRecord: editingRecord }) }} />
  }
  // 提交入口限制修改请求
  onSubmitAccessCfg = (params) => {
    const { editingRecord: { id, yyLevelLimit, beanLimit } } = this.state

    if (!checkIsNumber(yyLevelLimit) || !checkIsNumber(beanLimit)) {
      message.warn('输入格式有误，请检查：' + yyLevelLimit + ',' + beanLimit)
      return
    }
    this.callModel('submitAccessCfg', {
      params: { id: id, yyLevelLimit: yyLevelLimit, beanLimit: beanLimit },
      cbFunc: (success) => {
        if (success) {
          message.success('已提交Boss审核，请等待管理员审批', 10)
          this.callModel('queryAccessCfg')
          this.setState({ editingRecord: null })
        } else {
          message.error('更新失败，请检查控制台')
        }
      }
    })
  }
  // 提交豆荚限制修改请求
  onSubmitPodLimitCfg = () => {
    const { totalLimitValue, totalLimitValueOld, virtOrTeamLimitValue, virtOrTeamLimitValueOld } = this.state
    if (!checkIsNumber(totalLimitValue) || !checkIsNumber(virtOrTeamLimitValue) || totalLimitValue < -1 || virtOrTeamLimitValue < -1) {
      message.warn('限制金额输入有误，请检查：' + totalLimitValue + ',' + virtOrTeamLimitValue)
      return
    }

    if (parseInt(virtOrTeamLimitValue) > parseInt(totalLimitValue) && parseInt(totalLimitValue) != -1) {
      message.warn('兑换紫水晶券/队友金限额不能超过总限额：' + totalLimitValue + ',' + virtOrTeamLimitValue)
      return
    }

    if (totalLimitValue === totalLimitValueOld && virtOrTeamLimitValue === virtOrTeamLimitValueOld) {
      message.warn('配置没有改变')
      return
    }

    this.callModel('submitPodLimitValue', {
      params: { totalLimitValue: totalLimitValue, virtOrTeamLimitValue: virtOrTeamLimitValue },
      cbFunc: (success) => {
        if (success) {
          message.success('已提交boss审核，请等待管理员审批', 10)
          this.setState({ essenceLimitEditable: false })
          this.queryPodCfg()
        } else {
          message.warn('配置修改失败，请检查控制台')
        }
      }
    })
  }

  creteTableOperateHtml = (r) => {
    const { editingRecord } = this.state
    if (editingRecord == null || editingRecord.key != r.key) {
      return <a onClick={() => this.setState({ editingRecord: r })}>修改</a>
    }
    return <>
      <a style={{ marginRight: '1em' }} onClick={() => { this.onSubmitAccessCfg() }}>保存</a>
      <a onClick={() => { this.setState({ editingRecord: null }); this.callModel('queryAccessCfg') }}>取消</a>
    </>
  }

  render () {
    const { totalLimitValue, essenceLimitEditable, totalLimitValueOld, virtOrTeamLimitValue, virtOrTeamLimitValueOld } = this.state
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Row>
            <Col span={12} style={{ marginTop: '5px' }}>
              <Descriptions
                bordered
                title='精粹兑换道具配置'
                size='default'
                column={{ sm: 2 }}
                extra={!essenceLimitEditable
                  ? <Button type='default' onClick={() => { this.setState({ essenceLimitEditable: true }) }}>修改</Button>
                  : <> <Button type='primary' disabled={!essenceLimitEditable} onClick={() => this.onSubmitPodLimitCfg()}>保存</Button>
                    <Button style={{ marginLeft: 5 }} danger onClick={() => { this.setState({ essenceLimitEditable: false, totalLimitValue: totalLimitValueOld, virtOrTeamLimitValue: virtOrTeamLimitValueOld }) }}>取消</Button></>
                }
              >
                <Descriptions.Item label='兑换总限额'>
                  <Input key={essenceLimitEditable + totalLimitValueOld} disabled={!essenceLimitEditable} defaultValue={totalLimitValue} onChange={(e) => { this.setState({ totalLimitValue: e.target.value }) }} />
                </Descriptions.Item>
                <Descriptions.Item label='兑换紫水晶券限额'>
                  <Input key={essenceLimitEditable + virtOrTeamLimitValueOld} disabled={!essenceLimitEditable} defaultValue={virtOrTeamLimitValue} onChange={(e) => { this.setState({ virtOrTeamLimitValue: e.target.value }) }} />
                </Descriptions.Item>
                <Descriptions.Item label='说明' span={2}>用户当日使用精粹兑换礼物限制金额/紫水晶, 输入-1表示不限制。 (1精粹=100紫水晶)</Descriptions.Item>
              </Descriptions>
            </Col>
          </Row>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default EssenceExchangeCfg
