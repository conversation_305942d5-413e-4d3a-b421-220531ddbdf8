import { connect } from 'dva'
import React, { Component } from 'react'
import { <PERSON><PERSON>, <PERSON>, Col, DatePicker, Form, Input, InputNumber, message, Modal, Row, Table } from 'antd'
import { timeFormater } from '@/utils/common'
import PopImage from '@/components/PopImage'
const FormItem = Form.Item
const namespace = 'medalConfig'
const defaultSelectItem = {
  isActive: true,
  upgradeType: 1,
  position: 0,
  levelList: []
}
@connect(({ medalConfig }) => ({
  model: medalConfig
}))

class IssueMedal extends Component {
  state = {
    uid: 0,
    selectItem: {},
    opType: '',
    detailVisible: false,
    imgInputRefreshKey: 0
  }

  componentDidMount = () => {
    this.refreshList()
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // 更新列表
  refreshList =() => {
    this.setState({ detailVisible: false, selectItem: defaultSelectItem })
    this.callModel('getIssueList')
  }

  // 新增配置
  addItem = () => {
    this.setState({ selectItem: defaultSelectItem, detailVisible: true, opType: 'add' })
    this.refreshInputNumber()
  }

  // 刷新InputNumber组件
  refreshInputNumber = () => {
    this.setState({ imgInputRefreshKey: Math.random() })
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '500'],
    showSizeChanger: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  queryIssueMedal = (uid) => {
    if (!uid || uid <= 0) {
      return
    }
    this.setState({ issueList: null })
    this.callModel('getIssueList', {
      params: { uid: uid },
      isDetailMode: true,
      isJsonMode: true,
      isSlientMode: true,
      cbFunc: (ret) => {
        const { status, msg, list } = ret
        if (status !== 0) {
          message.warn('查询失败, 请稍后重试: ' + msg)
          return
        }
        let retList = Array.isArray(list) ? list : []
        for (let i = 0; i < retList.length; i++) {
          retList[i].index = i + 1
        }
        this.setState({ issueList: retList })
      }
    })
  }

  hiddenModal = () => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.setState({ detailVisible: false })
  }

  onFinish = values => {
    // const { dispatch } = this.props
    if (Number(values.uid) === 0) {
      message.warn(`uid错误`)
      return
    }
    if (Number(values.privilegeID) === 0) {
      message.warn(`勋章ID错误`)
      return
    }
    let days = Number(values.days) || 0
    let expireTime = 0
    if (values.expireTime != null) {
      expireTime = values.expireTime.unix()
    }
    if (days === 0 && expireTime === 0) {
      message.warn(`发放天数和过期时间不能同时为0`)
      return
    }
    let data = { uid: Number(values.uid),
      privilegeID: Number(values.privilegeID),
      days: days,
      expireTime: expireTime,
      remark: values.remark != null ? values.remark : '' }
    console.log(data)

    this.callModel('issueUserMedal', {
      params: data,
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ok) => {
        if (ok) {
          message.success('操作成功！')
          this.refreshList()
        } else {
          message.warn('操作失败，请检查！')
        }
      }
    })

    this.hiddenModal()
  }

  submitRefuseReason = () => {
    this.submitRefuseFormRef.submit()
    this.setState({ fillRefuseVisible: false })
  }

  handleAddSubmit = () => {
    this.formRef.submit()
  }

  getApprovalDesc = (val) => {
    if (val === 0) {
      return '-'
    }
    if (val === 1) {
      return '审批中'
    }
    if (val === 2) {
      return '审批通过'
    }
    if (val === 3) {
      return '审批拒绝'
    }
    return '-'
  }

  render () {
    const { issueList } = this.props.model
    const { uid, detailVisible } = this.state
    let columns = [
      { title: '序号', dataIndex: 'index' },
      { title: '昵称', dataIndex: 'nick' },
      { title: 'YY', dataIndex: 'imid' },
      { title: 'uid', dataIndex: 'uid' },
      { title: '勋章名称', dataIndex: 'medalName' },
      { title: '勋章ID', dataIndex: 'medalID' },
      { title: '特权ID', dataIndex: 'privilegeID' },
      { title: '发放天数', dataIndex: 'issueDays' },
      // { title: '发放到', dataIndex: 'privilegeID' },
      { title: '失效时间', dataIndex: 'expireTime', render: (v) => v === 0 ? '-' : timeFormater(v, 1) },
      { title: '更新时间', dataIndex: 'updateTime', render: (v) => v === 0 ? '-' : timeFormater(v, 1) },
      { title: '送礼图标', dataIndex: 'sendURL', render: (v) => v === '' ? '无' : <PopImage value={v} /> },
      { title: '发言图标', dataIndex: 'speakingURL', render: (v) => v === '' ? '无' : <PopImage value={v} /> },
      { title: 'tips', dataIndex: 'tips' },
      { title: '状态', dataIndex: 'statusDesc' },
      { title: '备注', dataIndex: 'remark' }
    ].map(item => { item.align = 'center'; return item })

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 12 }
      }
    }

    return (
      <Card>
        <Row style={{ marginBottom: '1em' }}>
          <InputNumber style={{ width: '15em', marginRight: '1em' }}
            placeholder='输入查询的用户uid, 单个查询'
            onChange={v => this.setState({ uid: v })} />
          <Button type='primary' onClick={() => this.queryIssueMedal(uid)}>发放历史查询</Button>
          <Button style={{ marginLeft: 10 }} type='primary' onClick={() => { this.addItem() }}>发勋章</Button>
        </Row>
        <Row>
          <Col span={24}>
            <Table columns={columns} dataSource={issueList} pagination={this.defaultPageValue} />
          </Col>
        </Row>

        <Modal width={350} title='发放勋章' forceRender visible={detailVisible}
          onOk={this.handleAddSubmit} onCancel={() => { this.setState({ detailVisible: false }) }}>
          <Form onFinish={this.onFinish} {...formItemLayout} ref={form => { this.formRef = form }}>
            <FormItem label='UID' name='uid' rules={[{ required: true, message: 'uid不能为空' }]}>
              <InputNumber style={{ width: '120%' }} />
            </FormItem>

            <FormItem label='特权ID' name='privilegeID' rules={[{ required: true, message: '特权ID不能为空' }]}>
              <InputNumber style={{ width: '120%' }} />
            </FormItem>

            <FormItem label='天数' name='days' >
              <InputNumber style={{ width: '120%' }} />
            </FormItem>

            <FormItem label='过期时间' name='expireTime' >
              <DatePicker style={{ width: '120%' }} showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </FormItem>

            <FormItem label='备注' name='remark' rules={[{ required: true, message: '备注不能为空' }]}>
              <Input style={{ width: '120%' }} />
            </FormItem>
          </Form>

        </Modal>

      </Card>
    )
  }
}

export default IssueMedal
