import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const getPoolConfigByID = genGetRequireTemplate('/drop/admin/query_pool', '')
const getToApproval = genGetRequireTemplate('/approval/admin/get_to_approval_byPID', 'toApproval')
const getAllPrizeList = genGetRequireTemplate('/drop/admin/query_prize_list', 'globalPrizeList')

const getBundleConfigTmp = genGetRequireTemplate('/drop/admin/get_bundle_reward_config', 'bundleRewardTmp')
const getBundleConfigProd = genGetRequireTemplate('/drop/admin/get_bundle_reward_config', 'bundleRewardProd')

const getWarnList = genGetRequireTemplate('/drop/admin/warn_query', 'warnList')
const updsertWarnConfig = genUpdateTemplate('/drop/admin/warn_upset')

const editPoolConfig = genUpdateTemplate('/drop/admin/edit_pool')
const editBundleRewardConfig = genUpdateTemplate('/drop/admin/update_bundle_reward_config')
const doApproval = genUpdateTemplate('/approval/admin/do_approval')

export default {
  namespace: 'dropBoxWar',
  state: {
    editingConfig: {}, // 编辑中的道具池
    prodConfig: {}, // 生效中的道具池
    globalPrizeList: [], // 礼物列表
    toApproval: {}, // 待审批流程
    poolConfig: {}, // 道具池完整配置
    bundleRewardTmp: {}, // 待审批组合配置
    bundleRewardProd: {}, // 生效中的组合配置
    warnList: [] // 预警配置
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }

  },
  effects: {
    getPoolConfigByID,
    getToApproval,
    doApproval,
    getAllPrizeList,
    editPoolConfig,
    getBundleConfigTmp,
    getBundleConfigProd,
    editBundleRewardConfig,
    getWarnList,
    updsertWarnConfig
  }
}
