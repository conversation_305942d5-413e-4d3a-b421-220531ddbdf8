import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Select, Tooltip, Modal, Popconfirm } from 'antd'
import { connect } from 'dva'
import moment from 'moment'
import 'moment/locale/zh-cn'
import { QuestionCircleOutlined } from '@ant-design/icons'
moment.locale('zh-cn')

const namespace = 'SubclassConfig'
const FormItem = Form.Item
const Option = Select.Option

@connect(({ SubclassConfig }) => ({
  model: SubclassConfig
}))
class SubclassConfig extends Component {
  genTitle = (title, tips) => {
    return <span>
      <Tooltip title={tips}>
        <QuestionCircleOutlined style={{ marginRight: '0.25em' }} />
        {title}
      </Tooltip>
    </span>
  }

  // 需要修改
  ColumnSubclassConfig = [
    { title: '分类区域',
      dataIndex: 'topclass',
      align: 'center',
      render: (text, record) => {
        switch (text) {
          case 'compere': return '特色主播'
          case 'play': return '特色玩法'
          case 'exclusive': return '独家主持'
        }
        return ''
      }
    },
    { title: '标签名称',
      dataIndex: 'subclass',
      align: 'center',
      render: (text, record) => {
        switch (text) {
          case 'voiceactor': return '声音恋人'
          case 'facevalue': return '颜值在线'
          case 'newcomer': return '新人可撩'
          case 'group': return '派对'
          case 'wonderful': return '精彩直播'
          case 'teamfight': return '激情团战'
          case 'makefriends': return '特色交友'
          case 'dating': return '相亲约会'
        }
        return ''
      }
    },
    { title: '操作',
      align: 'center',
      render: (text, record) => {
        return <Popconfirm title='确认删除?' type='primary' onConfirm={this.removeItem(record.id, record.topclass, record.subclass)} okText='确认' cancelText='取消'><a href=''>删除</a></Popconfirm>
      }
    }
  ]

  defaultPageValue = { pageSizeOptions: ['50', '60', '100', '1000'], showSizeChanger: true, pageSize: 50, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` } // 分页设置，可以不改

  // 需要修改
  state = {
    visible: false,
    addVisible: false,
    value: {}
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = { }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  removeItem = (id, topclass, subclass) => () => {
    var getListParam = { }
    const { dispatch } = this.props
    var payload = { id: id, topclass: topclass, subclass: subclass }
    dispatch({
      type: `${namespace}/remove`,
      payload: payload,
      getListParam: getListParam
    })
  }

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      v.topclass = v.topclass == null ? 'compere' : v.topclass
      v.subclass = v.subclass == null ? 'voiceactor' : v.subclass
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, addVisible: true, title: '添加' })
  }

  // hide total modal
  hideModal = () => {
    this.setState({ addVisible: false })
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  saveAddFormRef = (formRef) => {
    this.addFormRef = formRef
  }

  handleAddSubmit = e => {
    this.formRef.submit()
    this.setState({ addVisible: false })
  }

  // 添加
  onFinish = values => {
    const form = this.addFormRef.props.form
    const { dispatch } = this.props
    var getListParam = { }
    form.validateFields((err, values) => {
      if (!err) {
        const url = `${namespace}/addItem`
        dispatch({
          type: url,
          payload: values,
          getListParam: getListParam
        })
        form.resetFields()
        this.setState({ addVisible: false })
      }
    })
  }

  // 实际的页面信息
  render () {
    const { route, model: { list } } = this.props // 基本不需要修改
    const { addVisible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Button type='primary' onClick={this.showModal(false, null)}>新增</Button>
            <Divider /> {/* 分割线 */}
            <Table dataSource={list} columns={this.ColumnSubclassConfig} pagination={this.defaultPageValue} rowKey={(record, index) => index} /> {/* 显示的列表 */}
          </Form>
        </Card>

        <Modal visible={addVisible} title={title} onCancel={this.hideModal} onOk={this.handleAddSubmit}>
          <Form >
            <FormItem {...formItemLayout} label='子分类区域' name='topclass' rules={[{ required: true }]}>
              <Select>
                <Option value='compere'>特色主播</Option>
                <Option value='play'>特色玩法</Option>
                <Option value='exclusive'>独家主持</Option>
              </Select>)}
            </FormItem>
            <FormItem {...formItemLayout} label='标签名称' name='subclass' rules={[{ required: true }]}>
              <Select>
                <Option value='voiceactor'>声音恋人</Option>
                <Option value='facevalue'>颜值在线</Option>
                <Option value='newcomer'> 新人可撩</Option>
                <Option value='group'>  派对</Option>
                <Option value='wonderful'> 精彩直播</Option>
                <Option value='teamfight'> 激情团战</Option>
                <Option value='makefriends'> 特色交友</Option>
                <Option value='dating'> 相亲约会</Option>
              </Select>
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default SubclassConfig
