/* eslint-disable eqeqeq */
import { message } from 'antd'
import React, { Component } from 'react'

var echarts = require('echarts')

/* 公用组件-统计图表-折线图、使用示例：
let tags = ['01-02', '01-03', '01-04', '01-05']
let dataSource = [
  {name: '苹果', data: [11, 12, 13, 14]},
  {name: '香蕉', data: [13, 14, 11, 12]},
]
<LineChart title='价值趋势图' xAxisTag={tags} dataSource={dataSource} />
*/

class LineChart extends Component {
  componentDidUpdate () {
    this.initCharts()
  }
  componentDidMount () {
    this.initCharts()
  }

  initCharts () {
    let { dataSource, isSmooth, xAxisTag } = this.props
    if (!dataSource || dataSource.length === 0) {
      console.warn('图表传参有误, 请为dataSource赋值: dataSource=', dataSource)
      dataSource = []
    }
    if (xAxisTag == null) {
      console.warn('图表传参有误, 请为xAsisTag赋值: xasisTag=', xAxisTag)
      xAxisTag = []
    }
    if (dataSource.legend + xAxisTag.length === 0) {
      message.warn('数据为空')
    }

    let series = []
    dataSource.map((v, i) => {
      series.push({
        name: v.name,
        data: v.data,
        smooth: isSmooth == null ? true : isSmooth,
        type: 'line'
      })
    })
    const { title } = this.props
    let option = {
      title: { x: 'center', y: 'bottom', text: title || '' },
      legend: {},
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisTag
      },
      yAxis: { type: 'value' },
      series: series,
      toolbox: {
        feature: {
          // saveAsImage: {}
        }
      },
      tooltip: {
        trigger: 'axis',
        showContent: true,
        axisPointer: {
          type: 'cross' // 显示十字交叉指示器
        },
        position: function (pos, params, el, elReact, size) {
          let obj = { top: 10 }
          obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30
          return obj
        }
      }
    }
    this.renderCharts(option, title)
  }

  renderCharts (optioin, idName) {
    var chart = echarts.getInstanceByDom(document.getElementById(idName))
    if (chart !== undefined) {
      chart.clear() // 先清除掉旧的画板，防止冲突
    }
    if (chart === undefined) {
      chart = echarts.init(document.getElementById(idName))
    }
    chart.setOption(optioin)
  }

  render () {
    const style = this.props.style ? this.props.style : { width: '100%', height: 400 }
    const { title } = this.props

    return (
      <div id={title} style={style} />
    )
  }
}

export default LineChart
