import {
  Card,
  Form,
  Table,
  Divider, Input, message, Modal, Button, Checkbox, DatePicker, Select
} from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import dateString from '@/utils/dateString'
import { deepClone } from '../../../../utils/common'
import { idxDesc, tingSealStr, yuanToWan, approvaMap, approvalStatusList, reviewMap, reviewStatusList } from './common'
import './style.css'

const CheckboxGroup = Checkbox.Group

const { TextArea } = Input
const Option = Select.Option

const namespace = 'hatMonthTask'
const getListUri = `${namespace}/listTaskConfig`

@connect(({ taskConfig }) => ({
  model: taskConfig
}))

class TaskConfig extends Component {
  constructor (props) {
    super(props)

    const { dataInfo } = props
    this.state = { list: dataInfo.list, taskConfig: dataInfo.taskConfig, searchASID: 0, searchMonth: '', reviewStatus: 0, approvalStatus: 0 }
  }

  componentDidMount () {
    const { dispatch } = this.props
    const { searchASID, searchTaskMonth, searchReviewStatus, searchApprovalStatus } = this.state
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    let data = { asid: searchASID, month: searchMonthTmp, reviewStatus: searchReviewStatus, approvalStatus: searchApprovalStatus }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  onClick = () => {
    const { dispatch } = this.props
    const { searchASID } = this.state
    let data = { asid: searchASID }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  componentWillReceiveProps (nextProps) {
    const { dataInfo } = nextProps
    this.setState({ list: dataInfo.list, taskConfig: dataInfo.taskConfig })
  }

  columns = [
    { title: '任务时间', width: 40, dataIndex: 'month' },
    { title: '公会短位ID', width: 50, dataIndex: 'asid' },
    { title: '可参与任务阶段',
      dataIndex: 'taskStepStr',
      align: 'center',
      render: (text, record) => {
        if (Array.isArray(record.taskStepStr)) {
          let content = null
          for (let i = 0; i < record.taskStepStr.length; i++) {
            content = <span>{content}<div>{record.taskStepStr[i]}</div></span>
          }
          return content
        }
      }
    },
    { title: '复核状态', dataIndex: 'reviewStatus', align: 'center', render: (text, record) => (reviewMap[record.reviewStatus]) },
    { title: '审核状态', width: 50, dataIndex: 'approvalStatus', render: (text, record) => (approvaMap[record.approvalStatus]) },
    { title: '驳回原因', width: 50, dataIndex: 'approvalRemark' },
    { title: '操作人', width: 50, dataIndex: 'optUser', render: (text, record) => (record.optUser === 0 ? '' : record.optUser) },
    { title: '操作时间', width: 50, dataIndex: 'optTime', render: (text, record) => (dateString(record.timestamp)) },
    { title: '是否生效', width: 50, dataIndex: 'isValid', render: (text, record) => (record.isValid ? '是' : '否') },
    { title: '操作', key: 'operation', align: 'center', render: (text, record) => (<span><a onClick={this.showConfirmTask(record)}>任务确认</a></span>) }
  ].map(item => {
    item.align = 'center'
    item.ellipsis = true
    return item
  })

  ruleDesc = [
    { title: '任务阶段', width: 40, dataIndex: 'idx', render: (text, record) => (idxDesc[record.idx]) },
    { title: '厅盖章流水/万', width: 50, dataIndex: 'tingSeal' },
    { title: '频道礼物流水/万', width: 50, dataIndex: 'guildGift', render: (text, record) => (yuanToWan(record.guildGift)) },
    { title: '奖励金额', width: 50, dataIndex: 'reward' }
  ]

  columnsConfirmTask = [
    { title: '任务时间', width: 40, dataIndex: 'month' },
    { title: '公会短位ID', width: 50, dataIndex: 'asid' },
    { title: '当前可参与任务阶段', dataIndex: 'curJoinStep', align: 'center', render: (text, record) => (this.curJoinStepDisplay(record)) },
    { title: '选择阶段', dataIndex: 'selectStep', align: 'center', width: 400, render: (text, record) => (this.selectJoinStep(record)) }
  ]

  pagination = {
    pageSizeOptions: ['10', '20', '50', '100'],
    showSizeChanger: true,
    defaultPageSize: 20,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  curJoinStepDisplay = (record) => {
    if (!Array.isArray(record.selectStep)) return

    let content = null
    for (let i = 0; i < record.selectStep.length; i++) {
      content = <span>{content}<div>{record.selectStep[i].label}</div></span>
    }
    return <div className={'joinStep'}>{content}</div>
  }

  selectJoinStep = (record) => {
    const { taskConfig } = this.state
    if (!Array.isArray(taskConfig)) return

    // 所有阶段
    let list = []
    for (const item of taskConfig) {
      list.push({ label: this.stepDesc(item.idx, item.tingSeal, item.guildGift), value: item.idx })
    }

    // 已选阶段
    let joinIdx = []
    if (Array.isArray(record.selectStep)) {
      for (let i = 0; i < record.selectStep.length; i++) {
        joinIdx.push(record.selectStep[i].value)
      }
    }

    return <CheckboxGroup className={'joinStep'} options={list} defaultValue={joinIdx} onChange={this.selectJoinStepChange(record)} />
  }

  selectJoinStepChange = (record) => e => {
    const { taskConfig } = this.state
    if (!Array.isArray(taskConfig)) return

    if (Array.isArray(e)) {
      let select = {}
      e.forEach(item => {
        select[item] = true
      })
      record.selectStep = []
      for (const item of taskConfig) {
        if (!select[item.idx]) continue
        record.selectStep.push({ label: this.stepDesc(item.idx, item.tingSeal, item.guildGift), value: item.idx })
      }
    }
    this.forceUpdate() // 强制刷新
  }

  // 单个记录任务确认
  showConfirmTask = (record) => () => {
    if (!record.canAllConfirm) {
      message.warn('当月且在操作时间内的未复核单才能发起确认审批')
      return
    }

    let info = deepClone(record)
    let list = []

    // 已选阶段
    let data = { sid: info.sid, asid: info.asid, month: info.month, selectStep: [] }
    if (Array.isArray(info.taskStep)) {
      for (let i = 0; i < info.taskStep.length; i++) {
        let desc = this.stepDesc(info.taskStep[i].idx, tingSealStr(info.taskStep[i].tingSealBegin, info.taskStep[i].tingSealEnd), info.taskStep[i].guildGift)
        data.selectStep.push({ label: desc, value: info.taskStep[i].idx })
      }
    }
    list.push(data)

    this.setState({ visibleConfirmTask: true, waitUpdateList: list })
  }

  // 批量任务确认
  confirmAllHandle =() => () => {
    const { list } = this.state

    let filterList = deepClone(list)
    filterList = filterList.filter((v) => { return v.canAllConfirm })

    if (filterList.length === 0) {
      message.warn('无符合审批的单(当月且在操作时间内的未复核单才能发起确认审批)')
      return
    }

    let infos = []
    filterList.forEach(info => {
      // 已选阶段
      let data = { sid: info.sid, asid: info.asid, month: info.month, selectStep: [] }
      if (Array.isArray(info.taskStep)) {
        for (let i = 0; i < info.taskStep.length; i++) {
          let desc = this.stepDesc(info.taskStep[i].idx, tingSealStr(info.taskStep[i].tingSealBegin, info.taskStep[i].tingSealEnd), info.taskStep[i].guildGift)
          data.selectStep.push({ label: desc, value: info.taskStep[i].idx })
        }
      }
      infos.push(data)
    })

    this.setState({ visibleConfirmTask: true, waitUpdateList: infos })
  }

  stepDesc = (idx, tingSeal, guildGift) => {
    return idxDesc[idx] + ': ' + '盖章流水' + tingSeal + '万元, ' + '礼物' + yuanToWan(guildGift) + '万元'
  }

  getFilterList = () => {
    const { list } = this.state
    const { searchASID, searchTaskMonth, searchReviewStatus, searchApprovalStatus } = this.state
    let filterList = list
    if (parseInt(searchASID) > 0) {
      filterList = filterList.filter((v) => { return v.asid === parseInt(searchASID) })
    }
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    if (parseInt(searchMonthTmp) > 0) {
      filterList = filterList.filter((v) => { return parseInt(v.month) === parseInt(searchMonthTmp) })
    }
    if (searchReviewStatus > 0) {
      filterList = filterList.filter((v) => { return v.reviewStatus === searchReviewStatus })
    }
    if (searchApprovalStatus > 0) {
      filterList = filterList.filter((v) => { return v.approvalStatus === searchApprovalStatus })
    }
    return filterList
  }

  inputReviewRemarkHandle = () => e => {
    let value = e.target.value
    this.setState({ inputReviewRemark: value })
  }

  flushHandle = () => () => {
    const { waitUpdateList } = this.state
    if (!Array.isArray(waitUpdateList)) return

    let pass = true
    waitUpdateList.forEach(v => {
      if (Array.isArray(v.selectStep)) {
        for (let i = 0; i < v.selectStep.length - 1; i++) {
          if (v.selectStep[i].value + 1 !== v.selectStep[i + 1].value) {
            pass = false
            message.warn('配置错误, 阶段必须连续, asid:' + v.asid)
          }
        }
      }
    })

    if (!pass) return

    let sidSelectIdx = {}
    waitUpdateList.forEach(v => {
      let idxList = []
      for (let i = 0; i < v.selectStep.length; i++) {
        idxList.push(v.selectStep[i].value)
      }
      sidSelectIdx[v.sid] = idxList
    })

    console.log(sidSelectIdx)
    this.props.dispatch({
      type: `${namespace}/flushTaskConfig`,
      payload: { list: sidSelectIdx }
    })

    this.setState({ isFlush: true })
  }

  hiddenModalConfirmTask = () => {
    this.setState({ visibleConfirmTask: false, inputReviewRemark: '', waitUpdateList: null, isFlush: false })
  }

  handleCancelConfirmTask = e => {
    this.hiddenModalConfirmTask()
  }

  handleSubmitConfirmTask = e => {
    const { isFlush, inputReviewRemark, waitUpdateList } = this.state

    if (!isFlush) {
      message.warn('未更新配置统计')
      return
    }

    let sidSelectIdx = {}
    waitUpdateList.forEach(v => {
      let idxList = []
      for (let i = 0; i < v.selectStep.length; i++) {
        idxList.push(v.selectStep[i].value)
      }
      sidSelectIdx[v.sid] = idxList
    })

    let data = { remark: inputReviewRemark, list: sidSelectIdx }
    this.props.dispatch({
      type: `${namespace}/approvalTaskConfig`,
      payload: data
    })

    this.hiddenModalConfirmTask()
  }

  render () {
    const { visibleConfirmTask, waitUpdateList, inputReviewRemark, taskConfig } = this.state
    return (
      <Card>
        <Form>
          任务时间：
          <DatePicker
            format='YYYY-MM'
            picker='month'
            placeholder='任务时间'
            onChange={(v) => this.setState({ searchTaskMonth: v })}
            style={{ width: 100, marginRight: 10 }}
          />
          <Divider type='vertical' />

          短位ID：
          <Input style={{ width: 100, marginRight: 10 }} placeholder='请输入' onChange={(e) => { this.setState({ 'searchASID': e.target.value }) }} />
          <Divider type='vertical' />

          复核状态
          <Select allowClear placeholder='请选择' style={{ width: 100, marginLeft: 3 }} onChange={(v) => this.setState({ searchReviewStatus: v })}>
            {reviewStatusList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}
          </Select>
          <Divider type='vertical' />

          审批状态
          <Select allowClear placeholder='请选择' style={{ width: 130, marginLeft: 3 }} onChange={(v) => this.setState({ searchApprovalStatus: v })}>
            {approvalStatusList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}
          </Select>

          <div />
          <Button style={{ marginTop: 5, marginBottom: 5 }} type='primary' onClick={this.confirmAllHandle()}>任务确认</Button>
          <Table style={{ marginTop: 10 }} dataSource={this.getFilterList()} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} scroll={{ x: 'max-content' }} size='small' />
        </Form>

        <Modal keyboard={false} destroyOnClose forceRender width={1000} visible={visibleConfirmTask} title='任务确认' onCancel={this.handleCancelConfirmTask} onOk={this.handleSubmitConfirmTask}>
          <Table size='small' style={{ width: 500 }} dataSource={taskConfig} columns={this.ruleDesc} pagination={false} />
          <div style={{ marginTop: 5 }} />
          <font color='red'>运营复核备注:</font>
          <TextArea placeholder='选填' autoSize={{ minRows: 2, maxRows: 4 }} style={{ height: 50, width: 500 }} value={inputReviewRemark} onChange={this.inputReviewRemarkHandle()} />
          <Button style={{ marginTop: 5, marginBottom: 5 }} type='primary' onClick={this.flushHandle()}>更新配置统计</Button>
          <Table bordered dataSource={waitUpdateList} columns={this.columnsConfirmTask} pagination={false} />
        </Modal>
      </Card>
    )
  }
}

export default TaskConfig
