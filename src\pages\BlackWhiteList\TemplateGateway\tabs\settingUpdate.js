import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Input, Button, Table, message, Typography, Row, Col, Popconfirm } from 'antd'
const { Link } = Typography
const { TextArea } = Input

const namespace = 'templateGateWay'
const defaultVal = `5180550_2808712951,5180550_2808713015`

@connect(({ templateGateWay }) => ({
  model: templateGateWay
}))

// 已下线的Tab,可以铲掉了
class SettingUpdate extends Component {
  state = {
    tempVal: defaultVal,
    settingList: []
  }

  componentDidMount = () => {
    this.refreshData()
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  refreshData = () => {
    const { tempVal } = this.state
    let list = tempVal.replace(' ', '').split(',')
    this.callModel('getChanDetail', {
      params: {
        idList: list
      },
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('查询失败: ' + msg)
          return
        }
        this.setState({ settingList: ret.list })
      }
    })
  }

  // 更新开关设置
  updateSetting = (sid, ssid, templateType) => {
    this.callModel('udpateChanSetting', {
      params: {
        templateType: templateType,
        sid: sid,
        ssid: ssid
      },
      isDetailMode: true,
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('更新失败: ' + msg)
          return
        }
        message.success('更新成功')
        setTimeout(() => {
          this.refreshData()
        }, 1000)
      }
    })
  }

  getDesc = (r) => {
    const { isNotSet, isDefault, isNewOnly, isOldOnly } = r
    let desc = ''
    if (isNotSet) {
      desc = '默认使用A类'
    }
    if (isDefault) {
      desc = '默认新模板'
    }
    if (isNewOnly) {
      desc = '主动使用新模板'
    }
    if (isOldOnly) {
      desc = '主动使用旧模板'
    }
    return desc
  }

  render () {
    const { tempVal, settingList } = this.state
    const columns = [
      { dataIndex: 'sid', title: 'sid' },
      { dataIndex: 'ssid', title: 'ssid' },
      { dataIndex: 'isNew', title: '使用模板类型', render: (v) => { return v ? '新' : '旧' } },
      { dataIndex: '_', title: '匹配方式', render: (v, r) => { return this.getDesc(r) } },
      { dataIndex: 'note', title: '备注', render: (v) => { return v || '-' } },
      { dataIndex: '_',
        title: '操作',
        render: (v, r) => {
          return <Popconfirm title={`确认更新到${r.isNew ? '旧' : '新'}模板么？`} onConfirm={() => { this.updateSetting(r.sid, r.ssid, r.isNew ? 1 : 2) }}>
            <Link type={r.isNew ? 'warning' : ''} >切到{r.isNew ? '旧' : '新'}模板</Link>
          </Popconfirm>
        } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <Card>
        <Row>
          <Col span={24} style={{ marginBottom: '1em' }}>
            需要查询的频道：<Button onClick={() => this.refreshData()} type='primary'>查询</Button>
          </Col>
          <Col span={24} style={{ marginBottom: '1em' }}>
            <TextArea value={tempVal} placeholder='格式=sid_ssid, 批量输入时用英文逗号隔开' onChange={e => this.setState({ tempVal: e.target.value })} />
          </Col>
          <Col span={24} style={{ marginBottom: '1em' }}>
            <Table size='small' columns={columns} dataSource={settingList} pagination={false} />
          </Col>
        </Row>
      </Card>
    )
  }
}

export default SettingUpdate
