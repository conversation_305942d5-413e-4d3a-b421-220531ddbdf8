import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const getActivityConfigList = genGetRequireTemplate('/rank/boss/activity_config_list', 'configList')
const getActivityApprovalList = genGetRequireTemplate('/rank/boss/activity_approval_list', 'approvalList')
const updateConfigList = genUpdateTemplate('/rank/boss/UPDATE/update_config_list')
const addConfigList = genUpdateTemplate('/rank/boss/ADD/update_config_list')
const delConfigList = genUpdateTemplate('/rank/boss/DEL/update_config_list')

export default {
  namespace: 'nobleActivityConfig',
  state: {
    list: [],
    title: 'Bind success!',
    configList: [],
    approvalList: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getActivityConfigList,
    getActivityApprovalList,
    updateConfigList,
    addConfigList,
    delConfigList
  }
}
