import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Table, Button, InputNumber, DatePicker, Input } from 'antd'
import { connect } from 'dva'
import { exportExcel } from 'xlsx-oc'

const namespace = 'tingSuperTeam'
const listTingSuperTeamURL = `${namespace}/listTingSuperTeam`

var moment = require('moment')
var dateFormat = 'YYYY-MM-DD'

const roleMap = { 2: '超级天团', 5: '超级主持', 11: '普通主持' }

@connect(({ tingSuperTeam }) => ({
  model: tingSuperTeam
}))

class TingSuperTeam extends Component {
  constructor (props) {
    super(props)

    this.refreshTingSuperTeam()
  }

  columns = [
    { title: 'UID', dataIndex: 'uid', align: 'center', fixed: 'left' },
    { title: 'YY号', dataIndex: 'yy', align: 'center' },
    { title: '昵称', dataIndex: 'nickname', align: 'center' },
    { title: '主持身份', dataIndex: 'role', align: 'center', render: (text, record) => (record.role === 0 ? '' : record.role === 2 || record.role === 11 ? '厅天团' : roleMap[record.role]) },
    // { title: '签约频道', dataIndex: 'sid', align: 'center' },
    { title: '短位ID', dataIndex: 'asid', align: 'center' },
    { title: '公会抽成比例（%）', dataIndex: 'weight', align: 'center' },
    {
      title: '签约时间（开始-结束）',
      dataIndex: 'signTime',
      align: 'center',
      width: 200,
      render: (text, record) => {
        if (record.signStartTime === 0 || record.signEndTime === 0) {
          return ''
        }
        record.signTime = moment.unix(record.signStartTime).format(dateFormat) + '至' + moment.unix(record.signEndTime).format(dateFormat)
        return record.signTime
      }
    },
    {
      title: '加入TID时间',
      dataIndex: 'joinTingTime',
      align: 'center',
      render: (text, record) => {
        if (record.joinTingTime === 0) {
          return ''
        }
        return moment.unix(record.joinTingTime).format(dateFormat)
      }
    },
    { title: '所属TID', dataIndex: 'tingId', align: 'center' },
    { title: '厅管UID', dataIndex: 'tingMgrUid', align: 'center' },
    { title: '厅管昵称', dataIndex: 'tingMgrNick', align: 'center' },
    { title: 'TID-厅管抽成比例（%）', dataIndex: 'tingWeight', align: 'center' }
  ]

  state = {

  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  refreshTingSuperTeam = () => {
    const { searchUID, searchYY, searchNick, searchASID, searchSignStartTime, searchSignEndTime, searchJoinTingTime, searchTingMgrUID, searchTID } = this.state

    let searchJoinTingTimeTmp = 0
    if (searchJoinTingTime) {
      searchJoinTingTimeTmp = moment(searchJoinTingTime).unix()
    }

    let signStartTimeTmp = 0
    let signEndTimeTmp = 0
    if (searchSignStartTime) {
      signStartTimeTmp = moment(searchSignStartTime).unix()
    }
    if (searchSignEndTime) {
      signEndTimeTmp = moment(searchSignEndTime).unix()
    }

    // let data = { uid: searchUID, yy: searchYY, sid: searchSID, asid: searchASID, signStartTime: signStartTimeTmp, signEndTime: signEndTimeTmp, joinTingTime: searchJoinTingTimeTmp, tid: searchTID, tingMgrUid: searchTingMgrUID }
    let data = { uid: searchUID, yy: searchYY, nick: searchNick, asid: searchASID, signStartTime: signStartTimeTmp, signEndTime: signEndTimeTmp, joinTingTime: searchJoinTingTimeTmp, tid: searchTID, tingMgrUid: searchTingMgrUID }
    console.log(data)
    this.props.dispatch({
      type: listTingSuperTeamURL,
      payload: data
    })
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      var value = selectedRows.map(item => item.Sid).join(',')
      this.setState({ removeKey: value })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name
    })
  }

  searchHandle = () => () => {
    this.refreshTingSuperTeam()
  }

  onExportHandle = () => () => {
    let headers = []
    let columns = this.columns
    const { exportKey } = this.state
    columns.forEach(function (item) {
      headers.push({ k: item.dataIndex, v: item.title })
    })
    let list = []
    exportKey.forEach(function (item) {
      let joinTingTime = moment.unix(item.inboundTime).format('YYYY-MM-DD')
      let role = item.role === 2 || item.role === 11 ? '厅天团' : roleMap[item.role]
      // let one = { uid: item.uid, yy: item.yy, nickname: item.nickname, role: role, sid: item.sid, asid: item.asid, weight: item.weight, signTime: item.signTime, joinTingTime: joinTingTime, tingId: item.tingId, tingMgrUid: item.tingMgrUid, tingMgrNick: item.tingMgrNick, tingWeight: item.tingWeight }
      let one = { uid: item.uid, yy: item.yy, nickname: item.nickname, role: role, asid: item.asid, weight: item.weight, signTime: item.signTime, joinTingTime: joinTingTime, tingId: item.tingId, tingMgrUid: item.tingMgrUid, tingMgrNick: item.tingMgrNick, tingWeight: item.tingWeight }
      list.push(one)
    })
    exportExcel(headers, list, '厅主持名单.xlsx')
  }

  render () {
    const { route, model: { list, countInfo } } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <span>UID</span>
          <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchUID: e })} style={{ width: 100, marginLeft: 3 }} />
          <span style={{ marginLeft: 15 }}>YY号</span>
          <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchYY: e })} style={{ width: 100, marginLeft: 3 }} />
          <span style={{ marginLeft: 15 }}>昵称</span>
          <Input style={{ marginRight: 15, width: 120, marginLeft: 3 }} placeholder='昵称' onChange={e => this.setState({ searchNick: e.target.value })} />
          <span style={{ marginLeft: 15 }}>短位ID</span>
          <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID: e })} style={{ width: 100, marginLeft: 3 }} />
          <span style={{ marginLeft: 10 }}>厅管UID</span>
          <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchTingMgrUID: e })} style={{ width: 100, marginLeft: 3 }} />
          <span style={{ marginLeft: 10 }}>TID</span>
          <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchTID: e })} style={{ width: 100, marginLeft: 3 }} />
          <span style={{ marginLeft: 15 }}>签约时间</span>
          <DatePicker
            format='YYYY-MM-DD'
            placeholder='开始时间'
            onChange={(v) => this.setState({ searchSignStartTime: v })}
            style={{ marginLeft: 5 }}
          />
          <span style={{ marginLeft: 5 }}>~</span>
          <DatePicker
            format='YYYY-MM-DD'
            placeholder='结束时间'
            onChange={(v) => this.setState({ searchSignEndTime: v })}
            style={{ marginLeft: 5 }}
          />
          <div style={{ marginTop: 10 }} />
          <span>加入TID时间</span>
          <DatePicker onChange={(v) => this.setState({ searchJoinTingTime: v })} style={{ marginLeft: 3 }} />
          <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
          <Button style={{ marginLeft: 20 }} type='primary' onClick={this.onExportHandle()}>导出名单</Button>
          <div style={{ marginTop: 10 }}>
            <div><font>1、入库规则：加入TID的普通主持或超级主持</font></div>
            <div><font>2、表格排序：按照入库时间从最新到最久</font></div>
          </div>
          <div style={{ marginTop: 5 }}><font color='red'>签约中厅管{countInfo.tingMgrCount}人，加入厅主持{countInfo.tingCompere}人（超级主持{countInfo.superCompere}人、厅天团{countInfo.tingsuperCompere}人）</font></div>
          <Table style={{ marginTop: 10 }} rowSelection={this.rowSelection} rowKey={(record, index) => index} bordered dataSource={list} columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default TingSuperTeam
