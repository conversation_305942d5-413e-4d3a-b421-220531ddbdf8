import React, { Component } from 'react'
import { Row, Col, Typography, Tooltip, Input, message } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
const { Text } = Typography

export class Tip extends Component {
  render () {
    return (
      <Row>
        <Col span={24}>
          1、用户当日升级礼物，升级失败扣除手续费后，获得紫水晶券;
        </Col>
        <Col span={24}>
          2、输入-1代表不设限，用户升级守护礼物价值=兑换紫水晶券+手续费;
        </Col>
      </Row>
    )
  }
}

export class ConfigItem extends Component {
  render () {
    const { title, tooltip, children } = this.props
    return (
      <Row style={{ marginTop: '1em' }}>
        <Col span={24}>
          <Text style={{ marginRight: '1em' }}>{title}: </Text>
          <Tooltip title={tooltip}>
            <QuestionCircleOutlined />
          </Tooltip>
        </Col>
        <Col span={24}>
          {children}
        </Col>
      </Row>
    )
  }
}

// 整数编辑器
export class MyNumberInput extends Component {
  fixedValue = () => {
    const { value, base } = this.props
    return Number(value) / Number(base)
  }

  fixedOnChnage = (value) => {
    const { base, onChange } = this.props
    if (value === '-') {
      value = '-1'
    }
    const fixedValue = Number.parseInt(value)
    if (`${fixedValue}` !== value) {
      message.warn('请确保输入整数~')
      return
    }
    onChange(fixedValue * base)
  }

  render () {
    const { readOnly, style } = this.props
    return (
      <Input readOnly={readOnly} style={style} value={this.fixedValue()} onChange={(e) => { this.fixedOnChnage(e.target.value) }} />
    )
  }
}

export const viewColumns = [
  { title: '等级', dataIndex: 'level' },
  { title: '概率设置(元)', dataIndex: '_', render: (_, r) => { return `[${r.startPoint / 1000}, ${r.endPoint / 1000})` } },
  { title: '冰霜之心', dataIndex: 'rateA' },
  { title: '凤凰火焰戒', dataIndex: 'rateB' },
  { title: '升级失败', dataIndex: 'rateC' },
  { title: '失败概率', dataIndex: '_', render: (_, r) => { return `${(r.rateC * 100.0 / r.rateTotal).toFixed(2)}%` } }
].map(item => {
  item.align = 'center'
  return item
})

export default function formatNumberWithThousandsSeparators (number) {
  let n = number
  let r = ''
  let temp = ''
  let mod
  do {
    // 求模的值， 用于获取高三位，这里可能有小数
    mod = n % 1000
    // 值是不是大于1，是继续的条件
    n = n / 1000
    // 高三位
    temp = ~~mod
    // 1.填充: n > 1 循环未结束， 就要填充为比如 1 => 001
    // 不然temp = ~~mod的时候, 1 001， 就会变成 "11"
    // 2.拼接“,”
    r = (n >= 1 ? `${temp}`.padStart(3, '0') : temp) + (r ? ',' + r : '')
  } while (n >= 1)
  const strNumber = number + ''
  let index = strNumber.indexOf('.')
  // 拼接小数部分
  if (index >= 0) {
    r += strNumber.substring(index)
  }
  return r
}
