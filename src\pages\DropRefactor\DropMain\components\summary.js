import React, { Component } from 'react'
import { Card, Table, DatePicker, Divider, Row, Col, Button } from 'antd'
import { connect } from 'dva'
import moment from 'moment'
import { getPoolNameByPoolID } from '../../globalConfig'

const namespace = 'dropMain'

@connect(({ dropMain }) => ({
  model: dropMain
}))

class DropSummaryComponent extends Component {
  componentDidMount () {
    setTimeout(() => {
      this.refreshListByDate('')
    }, 1000)
  }

  state = { today: moment() }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 需要修改
  columns = [
    { title: '道具池', dataIndex: 'pid', align: 'center', render: v => { return this.getPoolName(v) } },
    { title: '普通收入', dataIndex: 'normalIn', align: 'center', render: v => v.toLocaleString() },
    { title: '普通支出', dataIndex: 'normalOut', align: 'center', render: v => v.toLocaleString() },
    { title: '普通人数', dataIndex: 'normalCount', align: 'center', render: v => v.toLocaleString() },
    { title: '疯狂收入', dataIndex: 'crazyIn', align: 'center', render: v => v.toLocaleString() },
    { title: '疯狂支出', dataIndex: 'crazyOut', align: 'center', render: v => v.toLocaleString() },
    { title: '疯狂人数', dataIndex: 'crazyCount', align: 'center', render: v => v.toLocaleString() },
    { title: '收入', dataIndex: 'userOut', align: 'center', render: v => v.toLocaleString() },
    { title: '支出', dataIndex: 'poolOut', align: 'center', render: v => v.toLocaleString() },
    { title: '抽取道具人数', dataIndex: 'poolOut', align: 'center', render: (_, record) => (record.normalCount + record.crazyCount).toLocaleString() },
    { title: '抽取道具偏移', dataIndex: 'offset', align: 'center', render: v => v.toLocaleString() },
    { title: '支出', dataIndex: 'dragonOut', align: 'center', render: v => v.toLocaleString() },
    { title: '龙宫开启次数', dataIndex: 'dragonTimes', align: 'center', render: v => v ? v.toLocaleString() : 0 },
    { title: '发放物资支出', dataIndex: 'flowOut', align: 'center', render: v => v.toLocaleString() },
    { title: '总偏移', dataIndex: 'totalOffset', align: 'center', render: v => v.toLocaleString() }
  ]

  getPoolName = (poolID) => {
    if (poolID === 0) return '汇总'
    return getPoolNameByPoolID(poolID, '')
  }

  onClick = day => () => {
    const { today } = this.state

    today.add(day, 'days')

    const todayStr = today.format('YYYYMMDD')
    this.refreshListByDate(todayStr)

    let v = $.extend({}, true, today) // 深拷贝
    this.setState({ today: v })
  }

  refreshListByDate = (date) => {
    const { poolNameOptions } = this.props.model
    const poolID = poolNameOptions.map(item => { return item.value }).join(',')
    this.callModel('getSummaryListByPoolID', {
      params: { sel: 5, date: date, pid: poolID }
    })
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { summaryListGroup } } = this.props
    const { today } = this.state

    return (
      <Card>
        <Row gutter={6}>
          <Col>
            <DatePicker value={today} disabled />
          </Col>
          <Col>
            <Button onClick={this.onClick(-1)}>前一天</Button>
          </Col>
          <Col>
            <Button onClick={this.onClick(1)}>后一天</Button>
          </Col>
        </Row>
        <Divider />
        <Table rowKey={(record, index) => index} dataSource={summaryListGroup} columns={this.columns} pagination={false} /> {/* 显示的列表 */}
        <div>
          <div>备注：</div>
          <div>普通/疯狂 收入：用户参与【普通/疯狂】抽取道具投入的金额合计</div>
          <div>普通/疯狂 支出：用户参与【普通/疯狂】抽取道具获得的礼物价值合计</div>
          <div>普通/疯狂 人数：参与【普通/疯狂】抽取道具的用户数去重</div>
          <div><font color='red'>收入：用户参与【普通&疯狂】抽取道具投入的金额合计（计算：普通收入+疯狂收入），即空投流水</font></div>
          <div>支出：用户参与【普通&疯狂】抽取道具获得的礼物价值合计（计算：普通支出+疯狂支出）</div>
          <div>抽取道具人数：参与空投玩法的用户数去重</div>
          <div>抽取道具偏移：系统在【普通&疯狂】道具池的累计失败情况（大于0为累计成功状态），计算：收入-支出</div>
          <div>支出：龙宫发放道具礼物价值合计</div>
          <div>龙宫开启次数：龙宫开启次数统计（龙宫进度100%用户领奖次数）</div>
          <div>物资支出：按照物资流水发放金额合计</div>
          <div><font color='red'>总偏移：空投道具池的总累计失败情况（大于0为累计成功状态），计算：收入-支出-龙宫支出-发放物资支出</font></div>
        </div>
      </Card>
    )
  }
}

export default DropSummaryComponent
