import { addRuleFunction, pageListBusiness, pageListRuleFunction, removeRuleFunction, updateRuleFunction } from '../common'

export default {
  namespace: 'RuleFunctionManage',
  state: {
    dataList: [],
    businessList: []
  },

  reducers: {
    // 更新数据列表，usage:
    // yield put({
    //  type: 'updateList',
    //  payload: {
    //    name: 'configList', list: Array.isArray(data) ? data : []
    //  }
    // })
    updateList (state, { payload }) {
      let obj = { ...state }
      obj[payload.name] = (payload.list || []).map((i, index) => {
        i.index = index + 1
        return i
      })
      return obj
    }
  },

  // 数据变更
  effects: {
    // 分页查询规则函数列表
    * pageListRuleFunction ({ payload, callback }, { call, put }) {
      const { data } = yield call(pageListRuleFunction, payload)
      yield put({
        type: 'updateList',
        payload: {
          name: 'dataList',
          list: data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
        }
      })
      if (callback) {
        callback(data.data)
      }
    },
    * listAllBusiness ({ payload }, { call, put }) {
      const { data } = yield call(pageListBusiness, { pageNo: -1, pageSize: -1 })
      yield put({
        type: 'updateList',
        payload: {
          name: 'businessList',
          list: data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
        }
      })
    },

    // 更新规则函数
    * updateRuleFunction ({ payload, callback }, { call, put }) {
      const { data } = yield call(updateRuleFunction, payload)
      if (callback) {
        callback(data)
      }
    },

    // 添加规则函数
    * addRuleFunction ({ payload, callback }, { call, put }) {
      const { data } = yield call(addRuleFunction, payload)
      if (callback) {
        callback(data)
      }
    },

    // 删除规则函数
    * removeRuleFunction ({ payload, callback }, { call, put }) {
      const { data } = yield call(removeRuleFunction, payload)
      if (callback) {
        callback(data)
      }
    }

  }
}
