import request from '@/utils/request'

export function getLists () {
  let url = `/white_list/video_compere_list`
  return request(url, { jsonp: true })
}

export function videoCompereAdd (uid, sid, reason) {
  let form = 'uid=' + uid + '&sid=' + sid + '&reason=' + reason
  return request(`/white_list/video_compere_add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: form
  })
}

export function videoCompereDel (uid, sid) {
  let form = 'uid=' + uid + '&sid=' + sid
  return request(`/white_list/video_compere_del`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: form
  })
}
