import { message } from 'antd'
import { getMagicConfigList, updateMagicConfig, deleteMagicConfig } from './api'

export default {
  namespace: 'brandActRoutine',

  state: {
    configList: []
  },

  reducers: {
    updateMagicConfigList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      for (let i = 0; i < payload.length; i++) {
        payload[i].idx = i + 1
      }
      return {
        ...state,
        configList: payload
      }
    }

  },

  effects: {
    * getMagicConfigList ({ payload }, { call, put }) {
      try {
        const { data: { status, msg, list } } = yield call(getMagicConfigList, payload)
        if (status === 0) {
          yield put({
            type: 'updateMagicConfigList',
            payload: Array.isArray(list) ? list : []
          })
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('exception', e)
        console.log('getMagicConfigList', e)
      }
    },

    * updateMagicConfig ({ payload }, { call, put }) {
      try {
        let { data: { status, msg } } = yield call(updateMagicConfig, payload)
        console.log(status, msg)
        if (status !== 0) {
          message.error('操作失败:' + msg)
          return
        }
        message.success('操作成功~')
      } catch (e) {
        message.error('exception', e)
      }
    },

    * deleteMagicConfig ({ payload }, { call, put }) {
      try {
        let { data: { status, msg } } = yield call(deleteMagicConfig, payload)
        console.log(status, msg)
        if (status !== 0) {
          message.error('操作失败:' + msg)
          return
        }
        message.success('操作成功~')
      } catch (e) {
        message.error('exception', e)
      }
    }

  }
}
