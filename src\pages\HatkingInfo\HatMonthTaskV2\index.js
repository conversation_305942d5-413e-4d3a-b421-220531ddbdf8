import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { connect } from 'dva'
import TaskConfig from './components/task_config.js'
import RewardConfirm from './components/reward_confirm.js'
import TaskProgress from './components/task_progress.js'
import RewardHistory from './components/reward_history.js'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'

@connect(({ hatMonthTaskv2 }) => ({
  model: hatMonthTaskv2
}))
class HatMonthTaskv2 extends Component {
  render () {
    const { route, model: { taskConfigInfo, rewardHistoryList, taskProgressInfo, rewardConfirmInfo } } = this.props
    console.log(rewardConfirmInfo)
    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs id='task' defaultActiveKey='task' type='card' >
          <TabPane tab='任务配置' key='task'>
            <TaskConfig dataInfo={taskConfigInfo} navGroup={1} />
          </TabPane>
          <TabPane tab='发奖确认' key='reward'>
            <RewardConfirm dataInfo={rewardConfirmInfo.list} canAllConfirm={rewardConfirmInfo.canAllConfirm} guildSum={rewardConfirmInfo.guildSum} navGroup={1} />
          </TabPane>
          <TabPane tab='任务完成进度报表' key='progress'>
            <TaskProgress dataInfo={taskProgressInfo.list} progressSummaryInfo={taskProgressInfo.info} navGroup={1} />
          </TabPane>
          <TabPane tab='发奖明细' key='history'>
            <RewardHistory dataInfo={rewardHistoryList} navGroup={1} />
          </TabPane>
        </Tabs>
      </PageHeaderWrapper>
    )
  }
}

export default HatMonthTaskv2
