import request from '@/utils/request'
import { stringify } from 'qs'

export function getLevelLists (params) {
  return request(`/HatkingLevelSwitch/GetLists?${stringify(params)}`)
}

export function updateLevelLimitInfo (params) {
  return request(`/HatkingLevelSwitch/UpdateItem?${stringify(params)}`)
}

export function getHistorySwitchLists (params) {
  return request(`/HatkingHistorySwitch/GetLists?${stringify(params)}`)
}

export function updateHistorySwitchInfo (params) {
  return request(`/HatkingHistorySwitch/UpdateItem?${stringify(params)}`)
}
