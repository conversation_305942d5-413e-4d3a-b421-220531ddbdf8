import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  let url = `/compere_data_base/list_talent_compere_info?${stringify(params)}`
  return request(url, { jsonp: true })
}

export function exportLists (params) {
  let url = `/compere_data_base/list_talent_compere_info?${stringify(params)}`
  window.open(url)
}

export function whiteListAdd (params) {
  var req = {}
  var imidList = params['imIDList'].split('\n')
  req['imIdList'] = imidList.map(Number)
  req['tagList'] = params['tagList']
  for (var i = 0; i < params['tagList'].length; i++) {
    console.log('-->tagID:', params['tagList'][i])
  }
  console.log('--->', req)
  let url = '/compere_data_base/manage_talent_compere_info'
  req['op'] = 'add'
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(req)
  })
}

export function whiteListUpdate (params) {
  let url = '/compere_data_base/manage_talent_compere_info'
  params['op'] = 'update'
  params['uid'] = parseInt(params['uid'], 10)
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function whiteListDel (params) {
  let url = '/compere_data_base/manage_talent_compere_info'
  params['op'] = 'remove'
  params['uid'] = parseInt(params['uid'], 10)
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function getTagLists (params) {
  let url = `/compere_data_base/list_talent_tag_info?${stringify(params)}`
  return request(url, { jsonp: true })
}
