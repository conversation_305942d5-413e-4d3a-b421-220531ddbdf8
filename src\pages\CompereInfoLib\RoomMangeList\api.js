import request from '@/utils/request'
import { stringify } from 'qs'

export const doPost = function (apiPath, params) {
  return request(apiPath, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
export function roomMgrList (params) {
  return request(`/room_manager/boss/room_mgr_list?${stringify(params)}`)
}

export function whiteList (params) {
  return request(`/room_manager/boss/whitelist?${stringify(params)}`)
}

export function addWhiteList (params) {
  return request(`/room_manager/boss/add_whitelist?${stringify(params)}`)
}

export function updateRoomMgr (params) {
  return doPost(`/room_manager/boss/update_room_mgr`, params)
}

export function deleteRoomMgr (params) {
  return doPost(`/room_manager/boss/delete_room_mgr`, params)
}

// 房管厅等级评级考核
export function updateRoomMgrGrade (params) {
  return doPost(`/room_manager/boss/update_room_grade`, params)
}

// 房管厅等级考核历史
export function getAssessHistory (params) {
  return doPost(`/room_manager/boss/grade_assess_history`, params)
}
