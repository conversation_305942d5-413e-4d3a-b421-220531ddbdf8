import request from '@/utils/request'

// 获取全部白名单数据
export function getLists () {
  let url = `/white_list/pc_template_list`
  return request(url)
}

// 根据url获取版本列表
export function getVersionListByUrl (targetUrl) {
  // console.log('you can get url list by: ', targetUrl + 'main/vers.txt')
  let form = 'targetUrl=' + targetUrl + '&numbers=1000'
  let url = '/white_list/get_h5_version_by_url'
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: form
  })
}

// 修改白名单数据，支持批量操作
function whiteListModifyOrDelete (operator, params) {
  if (operator !== 'MODIFY' && operator !== 'DELETE') {
    console.error('unexpect params: operator=' + operator)
    return
  }
  const form = 'operator=' + operator + '&' + params
  const url = '/white_list/pc_template_modify_delete'
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: form
  })
}

// 单个或批量修改白名单
// 若 play-version 记录不存在，这回新增新的记录
export function addOrModifyWhiteList (play, version, sids, remark) {
  let form = 'play=' + play + '&version=' + version + '&sids=' + sids + '&remark=' + remark
  return whiteListModifyOrDelete('MODIFY', form)
}

// 单个或批量删除白名单
export function deleteWhiteList (play, version, sids) {
  let remark = '[delete' + play + '-' + version + '-' + sids + '(auto fill)]'
  let form = 'play=' + play + '&version=' + version + '&sids=' + sids + '&remark=' + remark
  return whiteListModifyOrDelete('DELETE', form)
}
