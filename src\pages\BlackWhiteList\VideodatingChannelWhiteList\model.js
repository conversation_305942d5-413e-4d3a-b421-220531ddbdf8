/* eslint-disable eqeqeq */
import { getGameTypeWhite<PERSON><PERSON>, addGameTypeWhiteList, delGameTypeWhiteList, 
  getGameTypeBlackLists, addGameTypeBlackList, delGameTypeBlackList } from './api'
import { Modal, message } from 'antd'
import { checkSids, checkSid, checkSsid } from '@/utils/common'

export default {
  namespace: 'videodatingChannelWhiteList',
  state: {
    updating: false,
    gameTypeWhiteList: [],
    gameTypeBlackList: [],
    selectSid: '0',
    selectSsid: '0',
    selectPlay: 'duoteamfight',
    currentPlay: 'duoteamfight' // 频道玩法
  },

  reducers: {
    // 更新data到白名单数据列表
    displayGameTypeWhiteList (state, { payload: { data, currentPlay } }) {
      if (!Array.isArray(data)) {
        console.error('unexpect data type: data=', data)
        return
      }
      if (currentPlay == undefined || currentPlay == '') {
        console.error('unexpect currentPlay: ', currentPlay)
        return
      }
      return {
        ...state,
        gameTypeWhiteList: data,
        currentPlay: currentPlay
      }
    },
    displayGameTypeBlackList (state, { payload: { data, currentPlay } }) {
      if (!Array.isArray(data)) {
        console.error('unexpect data type: data=', data)
        return
      }
      if (currentPlay == undefined || currentPlay == '') {
        console.error('unexpect currentPlay: ', currentPlay)
        return
      }
      return {
        ...state,
        gameTypeBlackList: data,
        currentPlay: currentPlay
      }
    }
  },

  effects: {
    // 请求并刷新白名单列表数据
    * getWhiteListData ({ payload }, { select, call, put }) {
      const { currentPlay } = payload
      if (currentPlay == undefined || currentPlay.length == 0) {
        console.error('getWhiteListData() get unexpect params: currentPlay=' + currentPlay)
        Modal.error({ content: '获取多人玩法频道白名单数据失败，请检查控制台' })
        return
      }
      let resp = yield call(getGameTypeWhiteLists, currentPlay)
      let { data: { status, videodatingList, play } } = resp
      if (videodatingList === null) {
        message.warning('数据为空')
        yield put({
          type: 'displayGameTypeWhiteList',
          payload: { data: [], currentPlay: play }
        })
        return
      }
      if (status !== 0 || !Array.isArray(videodatingList)) {
        console.error('getWhiteListData() get data error: response=', resp)
        Modal.error({ content: '获取多人玩法频道白名单数据失败，请检查控制台' })
        return
      }
      if (play != currentPlay) {
        console.error('getWhiteListData() abnormal: request play type is ' + currentPlay + ' but return data is' + resp)
        Modal.warning({ content: '获取多人玩法频道白名单数据异常，返回的类型与请求的类型不一致，请检查控制台' })
      }
      for (let i = 0; i < videodatingList.length; i++) {
        videodatingList[i].idx = i + 1
        videodatingList[i].play = play
        if (videodatingList[i].ssid == 0) {
          videodatingList[i].ssid = '*'
        }
      }
      yield put({
        type: 'displayGameTypeWhiteList',
        payload: { data: videodatingList, currentPlay: play }
      })
    },
    // 添加白名单
    * addWhiteList ({ payload }, { call, put }) {
      let { selectSid, selectSsid, selectPlay } = payload
      if (selectSid == undefined || selectSid.length === 0) {
        Modal.error({ content: '添加失败，未完整输入信息' })
        return
      }
      if (selectSsid == undefined || selectSsid.length === 0) {
        Modal.error({ content: '添加失败，未完整输入信息' })
        return
      }
      if (!checkSid(selectSid) || !checkSids(selectSsid)) {
        return
      }
      if (selectPlay == undefined || selectPlay == 'undefined') {
        Modal.warn({ content: '请选择添加白名单的类别' })
        return
      }
      if (selectPlay == '') {
        console.error('unexpect play:', selectPlay)
        message.error('数据不合规范，请检查控制台或修改输入后提交')
        return
      }
      var params = {
        sid: parseInt(selectSid),
        ssidList: selectSsid,
        play: selectPlay
      }
      let resp = yield call(addGameTypeWhiteList, params)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发送错误, 请检查控制台' })
        console.error('[添加白名单错误]: response=', resp)
        return
      }
      const { status, msg } = data
      if (status === 0) {
        message.success('添加成功')
        yield put({
          type: 'getWhiteListData',
          payload: { currentPlay: selectPlay }
        })
      } else {
        console.error('[添加白名单] 返回结果为：', resp)
        Modal.warn({ content: msg })
      }
    },
    // 删除白名单
    * delWhiteList ({ payload }, { call, put }) {
      let { sid, ssid, currentPlay } = payload
      if (!checkSid(sid)) {
        return
      }
      if (ssid === '*') {
        ssid = '0'
      } else if (!checkSsid(ssid)) {
        return
      }
      if (currentPlay == undefined || currentPlay == 'undefined' || currentPlay == '') {
        console.error('delWhiteListByUid(): unexpect currentPlay=', currentPlay)
        Modal.error({ content: '删除失败，请检查控制台' })
        return
      }
      let resp = yield call(delGameTypeWhiteList, sid, ssid, currentPlay)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除白名单错误]: response=', resp)
        return
      }
      const { status } = data
      if (status === 0) {
        message.success('删除成功')
        yield put({
          type: 'getWhiteListData',
          payload: { currentPlay: currentPlay }
        })
      } else {
        Modal.error({ content: '删除失败：status=' + status })
        console.error('delWhiteListByUid() resp=', resp)
      }
    },

    // 请求并刷新白名单列表数据
    * getBlackListData ({ payload }, { select, call, put }) {
      const { currentPlay } = payload
      if (currentPlay == undefined || currentPlay.length == 0) {
        console.error('getWhiteListData() get unexpect params: currentPlay=' + currentPlay)
        Modal.error({ content: '获取多人玩法频道白名单数据失败，请检查控制台' })
        return
      }
      let resp = yield call(getGameTypeBlackLists, currentPlay)
      let { data: { status, videodatingList, play } } = resp
      if (videodatingList === null) {
        message.warning('数据为空')
        yield put({
          type: 'displayList',
          payload: { data: [], currentPlay: play }
        })
        return
      }
      if (status !== 0 || !Array.isArray(videodatingList)) {
        console.error('getWhiteListData() get data error: response=', resp)
        Modal.error({ content: '获取多人玩法频道白名单数据失败，请检查控制台' })
        return
      }
      if (play != currentPlay) {
        console.error('getWhiteListData() abnormal: request play type is ' + currentPlay + ' but return data is' + resp)
        Modal.warning({ content: '获取多人玩法频道白名单数据异常，返回的类型与请求的类型不一致，请检查控制台' })
      }
      for (let i = 0; i < videodatingList.length; i++) {
        videodatingList[i].idx = i + 1
        videodatingList[i].play = play
        if (videodatingList[i].ssid == 0) {
          videodatingList[i].ssid = '*'
        }
      }
      yield put({
        type: 'displayGameTypeBlackList',
        payload: { data: videodatingList, currentPlay: play }
      })
    },
    // 添加白名单
    * addGameTypeBlackList ({ payload }, { call, put }) {
      let { selectSid, selectSsid, selectPlay } = payload
      if (selectSid == undefined || selectSid.length === 0) {
        Modal.error({ content: '添加失败，未完整输入信息' })
        return
      }
      if (selectSsid == undefined || selectSsid.length === 0) {
        Modal.error({ content: '添加失败，未完整输入信息' })
        return
      }
      if (!checkSid(selectSid) || !checkSids(selectSsid)) {
        return
      }
      if (selectPlay == undefined || selectPlay == 'undefined') {
        Modal.warn({ content: '请选择添加白名单的类别' })
        return
      }
      if (selectPlay == '') {
        console.error('unexpect play:', selectPlay)
        message.error('数据不合规范，请检查控制台或修改输入后提交')
        return
      }
      var params = {
        sid: parseInt(selectSid),
        ssidList: selectSsid,
        play: selectPlay
      }
      let resp = yield call(addGameTypeBlackList, params)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发送错误, 请检查控制台' })
        console.error('[添加白名单错误]: response=', resp)
        return
      }
      const { status, msg } = data
      if (status === 0) {
        message.success('添加成功')
        yield put({
          type: 'getBlackListData',
          payload: { currentPlay: selectPlay }
        })
      } else {
        console.error('[添加白名单] 返回结果为：', resp)
        Modal.warn({ content: msg })
      }
    },
    // 删除白名单
    * delGameTypeBlackList ({ payload }, { call, put }) {
      let { sid, ssid, currentPlay } = payload
      if (!checkSid(sid)) {
        return
      }
      if (ssid === '*') {
        ssid = '0'
      } else if (!checkSsid(ssid)) {
        return
      }
      if (currentPlay == undefined || currentPlay == 'undefined' || currentPlay == '') {
        console.error('delWhiteListByUid(): unexpect currentPlay=', currentPlay)
        Modal.error({ content: '删除失败，请检查控制台' })
        return
      }
      let resp = yield call(delGameTypeBlackList, sid, ssid, currentPlay)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除白名单错误]: response=', resp)
        return
      }
      const { status } = data
      if (status === 0) {
        message.success('删除成功')
        yield put({
          type: 'getBlackListData',
          payload: { currentPlay: currentPlay }
        })
      } else {
        Modal.error({ content: '删除失败：status=' + status })
        console.error('delWhiteListByUid() resp=', resp)
      }
    }
  }
}
