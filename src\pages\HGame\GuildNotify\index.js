import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Table, Form, Button, Input, Modal, Divider, Popconfirm, Select, DatePicker, message } from 'antd'
import { connect } from 'dva'
import PicturesWall from '@/components/PicturesWall'
import PopImage from '@/components/PopImage'

const Option = Select.Option
const { RangePicker } = DatePicker
const { TextArea } = Input

const namespace = 'guildNotify'
const getGuildNotifyListURL = `${namespace}/getGuildNotifyList`
const addGuildNotifyURL = `${namespace}/addGuildNotify`
const updateGuildNotifyURL = `${namespace}/updateGuildNotify`
const deleteGuildNotifyURL = `${namespace}/deleteGuildNotify`

const addPage = 1
const updatePage = 2

const ALL = 0
const NO = 1
const YES = 2

// 通知渠道
const channelTypeHGame = 1

// 通知对象
const notifyTypeGuild = 1

const displayPosFirstMap = { 1: '后台页面顶部栏', 2: '左侧导航栏气泡', 3: '页面弹窗', 4: '公会任务右侧栏' }
const displayPosSecondMap = { 1: '公会经营', 2: '数据报表', 3: '主播管理' }

const displayPosFirstList = [ { index: 1, name: '后台页面顶部栏' }, { index: 2, name: '左侧导航栏气泡' }, { index: 3, name: '页面弹窗' }, { index: 4, name: '公会任务右侧栏' } ]
const displayPosSecondList = {
  2: [{ index: 1, name: '公会经营' }, { index: 2, name: '数据报表' }, { index: 3, name: '主播管理' }],
  3: [{ index: 1, name: '公会经营' }, { index: 2, name: '数据报表' }, { index: 3, name: '主播管理' }]
}

const notifyHzTypeOnce = 1
const notifyHzTypeTimer = 2
const notifyHzTypeList = [{ index: 1, name: '单次' }, { index: 2, name: '定时' }]
const notifyHzTriggerMap = { 1: '首次打开', 2: '每次打开', 3: '一直展示' }
const notifyHzWeekMap = { 1: '周日', 2: '周一', 3: '周二', 4: '周三', 5: '周四', 6: '周五', 7: '周六' }

const notifyHzTimerDay = 1
const notifyHzTimerWeek = 2
const notifyHzTimerMonth = 3
const notifyHzTimerList = [{ index: 1, name: '每天' }, { index: 2, name: '每周' }, { index: 3, name: '每月' }]
const notifyHzTriggerList = [{ index: 1, name: '首次打开' }, { index: 2, name: '每次打开' }, { index: 3, name: '一直展示' }]
const notifyHzWeekList = [{ index: 1, name: '周日' }, { index: 2, name: '周一' }, { index: 3, name: '周二' }, { index: 4, name: '周三' }, { index: 5, name: '周四' }, { index: 6, name: '周五' }, { index: 7, name: '周六' }]

var moment = require('moment')
var dateFormat = 'YYYY-MM-DD hh:mm:ss'

@connect(({ guildNotify }) => ({
  model: guildNotify
}))

class GuildNotify extends Component {
  constructor (props) {
    super(props)
    this.refreshGuildNotifyList()
  }

  configColumns = [
    { title: 'ID', dataIndex: 'idx', align: 'center' },
    {
      title: '通知渠道',
      dataIndex: 'channel',
      align: 'center',
      render: (text, record) => {
        switch (record.channel) {
          case channelTypeHGame: return 'Hgame后台'
        }
        return '-'
      }
    },
    {
      title: '通知对象',
      dataIndex: 'notifyType',
      align: 'center',
      render: (text, record) => {
        switch (record.notifyType) {
          case notifyTypeGuild: return '公会'
        }
        return '-'
      }
    },
    {
      title: 'ID(频道ID/YY号)',
      dataIndex: 'notifyObj',
      align: 'center',
      render: (text, record) => {
        if (text === null || text === undefined || text === '') {
          return ''
        }
        let content = null
        if (text.length === 1 && text[0] === '1') {
          content = null
        } else {
          for (let i = 0; i < text.length; i++) {
            const element = text[i]
            if (i === 5) {
              content = <span>{content}<div>......</div></span>
              break
            }
            content = <span>{content}<div>{element}</div></span>
          }
        }
        return content
      }
    },
    {
      title: '展示位置',
      dataIndex: 'displayPos',
      align: 'center',
      render: (text, record) => {
        if (record.displayPosFirst === 0 && record.displayPosSecond === 0) {
          return '-'
        }
        let content = ''
        if (record.displayPosFirst !== 0) {
          content = displayPosFirstMap[record.displayPosFirst]
        }
        if (record.displayPosSecond !== 0) {
          content = content + '-' + displayPosSecondMap[record.displayPosSecond]
        }
        return content
      }
    },
    { title: '标题', dataIndex: 'title', align: 'center' },
    { title: '简述', dataIndex: 'desc', align: 'center' },
    {
      title: '通知频率',
      dataIndex: 'notifyHz',
      align: 'center',
      render: (text, record) => {
        switch (record.notifyHzType) {
          case notifyHzTypeOnce:
            return '单次' + ' | ' + this.unixGetDate(record.notifyHzOnceTime)
          case notifyHzTypeTimer:
            if (record.notifyHzTimer === notifyHzTimerDay) {
              return '定时' + ' | ' + '每天' + ' | ' + notifyHzTriggerMap[record.notifyTrigger]
            } else if (record.notifyHzTimer === notifyHzTimerWeek) {
              return '定时' + ' | ' + '每周' + ' | ' + notifyHzWeekMap[record.notifyHzWeekTime] + ' | ' + notifyHzTriggerMap[record.notifyTrigger]
            } else if (record.notifyHzTimer === notifyHzTimerMonth) {
              return '定时' + ' | ' + '每月' + ' | ' + this.unixGetDate(record.notifyHzMonthStartTime) + '至' + this.unixGetDate(record.notifyHzMonthEndTime) + ' | ' + notifyHzTriggerMap[record.notifyTrigger]
            }
            return ''
          default:
            return ''
        }
      }
    },
    { title: '跳转链接', dataIndex: 'jumpUrl', align: 'center', render: (text, record) => (record.jumpUrl !== '' ? <span><a target='_blank' href={record.jumpUrl}>跳转</a></span> : '') },
    { title: '配图', dataIndex: 'imageUrl', align: 'center', render: text => text && text.length > 0 ? <PopImage value={text} /> : '' },
    {
      title: '发布时间',
      dataIndex: 'publishTime',
      align: 'center',
      render: (text, record) => {
        if (record.publishStartTime > 0 && record.publishEndTime > 0) {
          let curTIme = moment().unix()
          if (curTIme >= record.publishStartTime && curTIme <= record.publishEndTime) {
            return this.unixGetDate(record.publishStartTime) + '至' + this.unixGetDate(record.publishEndTime)
          }
          return <font color='red'>{this.unixGetDate(record.publishStartTime) + '至' + this.unixGetDate(record.publishEndTime)}</font>
        }
        return ''
      }
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      align: 'center',
      render: (text, record) => {
        if (record.updateTime > 0) {
          return moment(record.updateTime * 1000).format(dateFormat)
        }
        return ''
      }
    },
    {
      title: '是否上线',
      dataIndex: 'isPublish',
      align: 'center',
      render: (text, record) => {
        if (record.isPublish === YES) {
          return '是'
        }
        return '否'
      }
    },
    { title: '发布者', dataIndex: 'publishPerson', align: 'center' },
    { title: '操作', key: 'operation', align: 'center', width: 120, render: (text, record) => (<span><a size='small' type='primary' onClick={this.showModal(record, updatePage)}>修改</a><Divider type='vertical' /><Popconfirm title='确认删除?' onConfirm={this.deleteGuildNotify(record.id)}><a >删除</a></Popconfirm></span>)
    }
  ]

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  state = {
    visible: false,
    channelType: channelTypeHGame,
    notifyType: notifyTypeGuild,
    displayPosFirst: 1,
    notifyHzType: notifyHzTypeOnce,
    notifyHzTimer: notifyHzTimerDay
  }

  unixGetDate = (time) => {
    if (time <= 0) {
      return ''
    }
    var tmp = new Date(parseInt(time) * 1000 + 8 * 3600 * 1000)
    return tmp.toJSON().substr(0, 19).replace('T', ' ')
  }

  refreshGuildNotifyList = () => {
    this.props.dispatch({
      type: getGuildNotifyListURL,
      payload: {}
    })
  }

  showModal = (record, switchPage) => () => {
    if (this.formRef && switchPage === updatePage && record !== null) {
      console.log(record)
      let notifyObj = ''
      if (record.notifyObj !== null && record.notifyObj.length === 1 && record.notifyObj[0] === '1') {
        notifyObj = ''
      } else {
        if (record.notifyObj !== null && record.notifyObj.length > 0) {
          for (let i = 0; i < record.notifyObj.length; i++) {
            const element = record.notifyObj[i]
            if (i === record.notifyObj.length - 1) {
              notifyObj = notifyObj + element
            } else {
              notifyObj = notifyObj + element + '\n'
            }
          }
        }
      }
      record.notify = { notifyType: record.notifyType, notifyObj: notifyObj }
      record.displayPos = { displayPosFirst: record.displayPosFirst, displayPosSecond: record.displayPosSecond }
      record.publishTime = [moment(record.publishStartTime * 1000), moment(record.publishEndTime * 1000)]

      if (record.notifyHzWeekTime === 0) {
        record.notifyHzWeekTime = 1
      }

      let notifyHzMonthTime = []
      if (record.notifyHzMonthStartTime === 0 && record.notifyHzMonthEndTime === 0) {
        notifyHzMonthTime = [moment().subtract(3, 'days'), moment().subtract(0, 'days')]
      } else {
        notifyHzMonthTime = [moment(record.notifyHzMonthStartTime * 1000), moment(record.notifyHzMonthEndTime * 1000)]
      }

      record.notifyHzAll = { notifyHzType: record.notifyHzType, notifyHzOnceTime: moment(record.notifyHzOnceTime * 1000), notifyHzTimer: record.notifyHzTimer, notifyHzWeekTime: record.notifyHzWeekTime, notifyHzMonthTime: notifyHzMonthTime, notifyTrigger: record.notifyTrigger }

      this.formRef.setFieldsValue(record)

      this.setState({ displayPosFirst: record.displayPosFirst, notifyHzType: record.notifyHzType, notifyHzTimer: record.notifyHzTimer })
    }
    this.setState({ visible: true, switchPage: switchPage })
  }

  hiddenModal = () => {
    this.setState({ visible: false, displayPosFirst: 0, notifyHzType: 0, notifyHzTimer: 0 })
  }

  deleteGuildNotify = (value) => () => {
    if (value !== null && value !== undefined) {
      this.props.dispatch({
        type: deleteGuildNotifyURL,
        payload: { id: value }
      })
    }
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  handleCancel = e => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.hiddenModal()
  }

  onFinish = values => {
    const { switchPage } = this.state
    const { dispatch } = this.props
    let url = ''
    if (switchPage === addPage) {
      url = addGuildNotifyURL
    } else if (switchPage === updatePage) {
      url = updateGuildNotifyURL
    }

    if (url === '') {
      console.log('url empty')
      return
    }

    let data = values
    data.notifyType = values.notify.notifyType
    data.notifyObj = values.notify.notifyObj === undefined ? [] : String(values.notify.notifyObj).split('\n')
    data.displayPosFirst = values.displayPos.displayPosFirst
    data.displayPosSecond = values.displayPos.displayPosSecond
    data.isPublish = values.isPublish
    data.publishStartTime = values.publishTime === undefined ? 0 : values.publishTime[0].unix()
    data.publishEndTime = values.publishTime === undefined ? 0 : values.publishTime[1].unix()
    data.notifyHzType = values.notifyHzAll.notifyHzType // 单次/定时
    data.notifyHzTimer = values.notifyHzAll.notifyHzTimer // 定时(每天/每周/每月)
    data.notifyTrigger = values.notifyHzAll.notifyTrigger // (每次打开/首次打开/一直展示)
    // 单次
    data.notifyHzOnceTime = values.notifyHzAll.notifyHzOnceTime === undefined ? 0 : values.notifyHzAll.notifyHzOnceTime.unix()
    // 定时+每天
    data.notifyHzDayStartTime = values.notifyHzAll.notifyHzDayTime === undefined ? 0 : values.notifyHzAll.notifyHzDayTime[0].unix()
    data.notifyHzDayEndTime = values.notifyHzAll.notifyHzDayTime === undefined ? 0 : values.notifyHzAll.notifyHzDayTime[1].unix()
    // 定时+每周(周一~周日)
    data.notifyHzWeekTime = values.notifyHzAll.notifyHzWeekTime
    // 定时+每月
    data.notifyHzMonthStartTime = values.notifyHzAll.notifyHzMonthTime === undefined ? 0 : values.notifyHzAll.notifyHzMonthTime[0].unix()
    data.notifyHzMonthEndTime = values.notifyHzAll.notifyHzMonthTime === undefined ? 0 : values.notifyHzAll.notifyHzMonthTime[1].unix()
    delete data.notify
    delete data.displayPos
    delete data.publishTime
    delete data.notifyHzAll
    console.log(data)

    dispatch({
      type: url,
      payload: values
    })

    if (this.formRef) {
      this.formRef.resetFields()
      this.hiddenModal()
    }
  }

  handleSubmit = e => {
    if (this.formRef) {
      this.formRef.submit()
    }
  }

  displayPosSecondHtml = () => {
    const { switchPage, displayPosFirst } = this.state
    if (displayPosFirst === undefined || displayPosSecondList[displayPosFirst] === undefined) {
      return
    }
    return <Select style={{ width: 137 }} placeholder='请选择' disabled={!displayPosFirst && switchPage === addPage}>{displayPosSecondList[displayPosFirst].map((item, index) => (<Option key={index} value={item.index}>{item.name}</Option>))}</Select>
  }

  // 通知类型(单次/定时)
  notifyHzTypeHtml = () => {
    return <Select style={{ width: 82 }} placeholder='请选择' onChange={(v) => this.setState({ notifyHzType: v })}>{notifyHzTypeList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}</Select>
  }

  // 单次
  notifyHzOnceHtml = () => {
    const { notifyHzType } = this.state
    if (notifyHzType === notifyHzTypeOnce) {
      return <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
    }
  }

  // 定时(每天/每周/每月)
  notifyHzTimerHtml = () => {
    const { notifyHzType } = this.state
    if (notifyHzType === 0 || notifyHzType === notifyHzTypeOnce) {
      // 单次
      return
    }
    return <Select style={{ width: 80 }} placeholder='请选择' onChange={(v) => this.setState({ notifyHzTimer: v })}> {notifyHzTimerList.map((item, index) => (<Option key={index} value={item.index}>{item.name}</Option>))}</Select>
  }

  // 定时(每周)
  notifyHzTimeWeekHtml = () => {
    const { notifyHzType, notifyHzTimer } = this.state

    if (notifyHzType === notifyHzTypeTimer && notifyHzTimer === notifyHzTimerWeek) {
      return <Select style={{ width: 80 }} placeholder='请选择' onChange={(v) => this.setState({ notifyHzTime: v })}>{notifyHzWeekList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}</Select>
    }
  }

  // 定时(每月)
  notifyHzTimeMonthHtml = () => {
    const { notifyHzType, notifyHzTimer } = this.state
    if (notifyHzType === notifyHzTypeTimer && notifyHzTimer === notifyHzTimerMonth) {
      return <RangePicker defaultValue={[moment().subtract(3, 'days'), moment().subtract(0, 'days')]} showTime format='YYYY-MM-DD HH:mm:ss' />
    }
  }

  // 每次打开/首次打开/一直展示
  notifyTriggerHtml = () => {
    const { notifyHzType, notifyHzTimer } = this.state
    // 单次
    if (notifyHzType === undefined || notifyHzType === 0 || notifyHzTimer === undefined || notifyHzType === notifyHzTypeOnce) {
      return
    }

    return <Select placeholder='请选择' onChange={(v) => this.setState({ notifyHzTrigger: v })}> {notifyHzTriggerList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}</Select>
  }

  onChangeInfoDataRange = (dateRange) => {
    console.log(dateRange)
    if (dateRange === null) {
      this.setState({ searchPublishStartTime: 0, searchPublishEndTime: 0 })
      return
    }
    if (dateRange[0] !== null) {
      this.setState({ searchPublishStartTime: dateRange[0].unix() })
    } else {
      message.error('起始时间不能为空')
      return
    }
    if (dateRange[1] !== null) {
      this.setState({ searchPublishEndTime: dateRange[1].unix() })
    }
  }

  isDisplayPosFirstBubble = () => {
    const { displayPosFirst } = this.state
    console.log(displayPosFirst)
    if (displayPosFirst === 2) {
      return true
    }
    return false
  }

  // 查询
  searchHandle = () => () => {
    const { searchChannelType, searchNotifyObj, searchDisplayPosFirst, searchPublishStartTime, searchPublishEndTime, searchIsPublish } = this.state
    let data = { searchChannelType: searchChannelType, searchNotifyObj: searchNotifyObj, searchDisplayPosFirst: searchDisplayPosFirst, searchPublishStartTime: searchPublishStartTime, searchPublishEndTime: searchPublishEndTime, searchIsPublish: searchIsPublish }
    console.log(data)
    this.props.dispatch({
      type: getGuildNotifyListURL,
      payload: data
    })
  }

  render () {
    const { route, model: { list } } = this.props
    const { visible } = this.state

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 2 },
        sm: { span: 15 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <span style={{ marginLeft: 5 }}>通知渠道</span>
          <Select placeholder='全部' onChange={(v) => this.setState({ searchChannelType: v })} style={{ width: 100, marginLeft: 3 }}>
            <Option value={ALL}>全部</Option>
            <Option value={channelTypeHGame}>Hgame后台</Option>
          </Select>
          <span style={{ marginLeft: 10 }}>ID</span>
          <Input min={0} placeholder='输入YY号或长位ID' onChange={e => this.setState({ searchNotifyObj: e.target.value })} style={{ width: 150, marginLeft: 3 }} />
          <span style={{ marginLeft: 10 }}>展示位置</span>
          <Select placeholder='全部' onChange={(v) => this.setState({ searchDisplayPosFirst: v })} style={{ width: 150, marginLeft: 3 }}>
            <Option value={ALL}>全部</Option>
            {displayPosFirstList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}
          </Select>
          <span style={{ marginLeft: 10 }}>发布时间</span>
          <RangePicker allowEmpty={['false', 'true']} format={'YYYY-MM-DD'} onChange={this.onChangeInfoDataRange} style={{ marginLeft: 3 }} />
          <span style={{ marginLeft: 10 }}>是否上线</span>
          <Select placeholder='请选择' onChange={(v) => this.setState({ searchIsPublish: v })} style={{ width: 100, marginLeft: 3 }}>
            <Option value={ALL}>全部</Option>
            <Option value={YES}>是</Option>
            <Option value={NO}>否</Option>
          </Select>
          <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.showModal(null, addPage)}>新增</Button>
          <Table rowKey={(record, index) => index} bordered dataSource={list} columns={this.configColumns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
        </Card>

        <Modal forceRender width={800} visible={visible} title='配置弹窗' onCancel={this.handleCancel} onOk={this.handleSubmit} okText='提交'>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <Form.Item name='id' hidden>
              <Input hidden />
            </Form.Item>
            <div style={{ marginLeft: 20 }}><b><big>通知要求</big></b></div>
            <Form.Item label='通知渠道' name='channel' rules={[{ required: true, message: '请选择' }]}>
              <Select style={{ width: 120 }} placeholder='请选择'>
                <Option value={channelTypeHGame}>Hgame后台</Option>
              </Select>
            </Form.Item>
            <div style={{ marginLeft: 305 }}>1、选填。不填写默认全部公会</div>
            <div style={{ marginLeft: 305 }}>2、支持同时输入多个频道<font color='red'>长位ID</font>，回车分割</div>
            <div style={{ marginLeft: 305 }}><font color='red'>3、通过频道ID，给频道发送消息</font></div>
            <Form.Item label='通知对象' rules={[{ required: true, message: '请选择' }]}>
              <Input.Group compact>
                <Form.Item name={['notify', 'notifyType']} rules={[{ required: true, message: '请选择' }]}>
                  <Select style={{ width: 120 }} placeholder='请选择'>
                    <Option value={notifyTypeGuild}>公会</Option>
                  </Select>
                </Form.Item>
                <Form.Item name={['notify', 'notifyObj']}>
                  <TextArea rows={3} placeholder='支持输入多个频道长位ID，回车分割' />
                </Form.Item>
              </Input.Group>
            </Form.Item>
            <Form.Item label='展示位置' rules={[{ required: true, message: '请选择' }]}>
              <Input.Group compact>
                <Form.Item name={['displayPos', 'displayPosFirst']} rules={[{ required: true }]}>
                  <Select placeholder='请选择' style={{ width: 150 }} onChange={(v) => this.setState({ displayPosFirst: v })}>
                    {displayPosFirstList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}
                  </Select>
                </Form.Item>
                <Form.Item name={['displayPos', 'displayPosSecond']}>
                  {this.displayPosSecondHtml()}
                </Form.Item>
              </Input.Group>
            </Form.Item>
            <Form.Item label='发布时间' name='publishTime' rules={[{ required: true, message: '请选择' }]}>
              <RangePicker showTime />
            </Form.Item>

            <Form.Item label='通知频率' rules={[{ required: true, message: '请选择' }]}>
              <Input.Group compact>
                <Form.Item name={['notifyHzAll', 'notifyHzType']}>
                  {this.notifyHzTypeHtml()}
                </Form.Item>
                <Form.Item name={['notifyHzAll', 'notifyHzOnceTime']}>
                  {this.notifyHzOnceHtml()}
                </Form.Item>
                <Form.Item name={['notifyHzAll', 'notifyHzTimer']}>
                  {this.notifyHzTimerHtml()}
                </Form.Item>
                <Form.Item name={['notifyHzAll', 'notifyHzWeekTime']}>
                  {this.notifyHzTimeWeekHtml()}
                </Form.Item>
                <Form.Item name={['notifyHzAll', 'notifyHzMonthTime']}>
                  {this.notifyHzTimeMonthHtml()}
                </Form.Item>
                <Form.Item name={['notifyHzAll', 'notifyTrigger']}>
                  {this.notifyTriggerHtml()}
                </Form.Item>
              </Input.Group>
            </Form.Item>

            <Form.Item label='是否上线' name='isPublish' rules={[{ required: true, message: '请选择' }]}>
              <Select style={{ width: 120 }} placeholder='请选择'>
                <Option value={YES}>是</Option>
                <Option value={NO}>否</Option>
              </Select>
            </Form.Item>
            <Divider />
            <div style={{ marginLeft: 20 }}><b><big>通知详情</big></b></div>
            <Form.Item label='标题' name='title' rules={[{ required: true, message: '请输入' }]}>
              <Input maxLength={13} />
            </Form.Item>
            <Form.Item label='概述' name='desc'>
              {/* 左侧导航栏气泡 不输入概述 */}
              <Input maxLength={20} disabled={this.isDisplayPosFirstBubble()} />
            </Form.Item>
            <Form.Item label='点击跳转链接' name='jumpUrl'>
              <Input />
            </Form.Item>
            <span style={{ marginLeft: 190 }}>要求：数量1张，大小≦3M，格式jpg或png，尺寸414*292</span>
            <Form.Item label='配图' name='imageUrl'>
              <PicturesWall />
            </Form.Item>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default GuildNotify
