import { DatePicker, Typography, Space, Row, Col, Divider } from 'antd'
import React, { Component } from 'react'
import { timeFormater } from '@/utils/common'
import moment from 'moment' 
import PopImage from '@/components/PopImage'
const { Text } = Typography

export const tagOptions = [
  { label: '新公会', value: '新公会' },
  { label: '无', value: '' }
]

export const methodOptions = [
  { label: '系统自动复核', value: 1 },
  { label: '复核审批完成', value: 2 },
  { label: '手动修改', value: 3 }
]

export const defaultGuildInfo = {
  sid: 0,
  company_name: '',
  crop: '',
  ow_uid: 0,
  ow_phone_number: '',
  signed_time: moment().format('YYYY-MM-DD'),
  corporate_account: '',
  contract_deadline: 36
}

export const methodFormater = (v) => {
  return methodOptions.find(item => { return item.value === v })?.label
}

export const packRateFormater = (giftFlow, packFlow) => {
  if (packFlow === 0 || giftFlow === 0) {
    return '0%'
  }
  return `${(packFlow * 100 / giftFlow).toFixed(2)}%`
}

// 日期范围选择器
// 输入输出格式: YYYYMM~YYYYMM 或 空字符串
export class MonthRangeStr extends Component {
  getFixedTimeRange = () => {
    const { value } = this.props
    if (!value) {
      return [null, null]
    }
    const match = value.match(/^(\d{6})~(\d{6})$/)
    if (!match) {
      return [null, null]
    }
    const startTime = moment(match[1], 'YYYYMM')
    const endTime = moment(match[2], 'YYYYMM')
    return [startTime, endTime]
  }

  changeValue = (v) => {
    let [start, end] = v
    const { onChange } = this.props
    if (!start && !end) {
      onChange('')
      return
    }
    // const fixedValue = `${start.format('YYYYMM')}~${end.format('YYYYMM')}`
    const fixedStart = moment().add(-1, 'month') // 开始时间固定为上个月
    if (fixedStart.isAfter(end)) {
      end = fixedStart
    }
    const fixedValue = `${fixedStart.format('YYYYMM')}~${end.format('YYYYMM')}`
    onChange(fixedValue)
  }

  render () {
    const fixedValue = this.getFixedTimeRange()
    const { style, disabled } = this.props
    return (
      <DatePicker.RangePicker picker='month' format='YYYY-MM' value={fixedValue} disabled={disabled} onChange={v => { this.changeValue(v) }} style={style} />
    )
  }
}

export class ReviewInfo extends Component {
  getDataList = () => {
    const { data, guildGradeList } = this.props
    let dataList = [
      [<Text>调整前:</Text>, <Text>调整后：</Text>]
    ]

    let beforeList = []
    let afterList = []
    guildGradeList.forEach(item => {
      beforeList[item.grade] = 0
      afterList[item.grade] = 0
    })
    data.forEach(item => {
      afterList[item.manualGrade]++
      beforeList[item.systemGrade]++
    })
    guildGradeList.forEach(item => {
      const { grade, gradeName } = item
      let descA = `${gradeName}级公会数: ${beforeList[grade]}; `
      let descB = `${gradeName}级公会数: ${afterList[grade]}; `
      dataList.push([descA, descB])
    })
    return dataList
  }

  render () {
    const dataList = this.getDataList()
    return (
      <Space style={{ marginTop: '1em', marginBottom: '1em' }}>
        {
          dataList.map(item => {
            return (
              <Space direction='vertical'>
                {item[0]}
                {item[1]}
              </Space>
            )
          })
        }
      </Space>
    )
  }
}

export const classValueFormater = (v) => {
  return (
    Number(v / 1000.0).toFixed(2)
  )
}

export const packRateForamter = (gift, pack) => {
  return (
    <Text>{Number(pack * 100.0 / gift + pack).toFixed(2)}%</Text>
  )
}

export const genRemoveParams = (uid, nick, sidList, reason) => {
  let wcText = `${nick} (uid=${uid}), 申请移除超级水晶公会身份。\n
  移除数量: ${sidList.length}; \n
  移除原因: ${reason} \n
  移除列表: ${sidList.join(', ')} 
  `
  const aprData = {
    nick: nick,
    time: moment().format('YYYY-MM-DD HH:mm'),
    nRemove: sidList.length,
    sidList: sidList.join(', '),
    reason: reason
  } 
  return {
    sidList: sidList,
    reason: reason,
    wcText: wcText,
    aprData: JSON.stringify(aprData)
  }
}

// const yesNoMap3 = { 0: '-', 1: '是', 2: '否' }
const companyTypeMap = { 0: '-', 1: '有限责任公司', 2: '股份有限公司' }
const companyRealAuthMap = { '': '未审核', 'S0I': '待审核', 'S0S': '审核中', 'S0A': '审核通过', 'S0X': '审核不通过' }

const genCol = (title, value) => {
  return <Col span={12}>
    <font style={{ fontSize: '16px' }}>{`${title}: ${value}`}</font>
  </Col>
}

export const SidDetailInfo = (props) => {
  const { record } = props
  if (!record) {
    return <div>
      暂无信息
    </div>
  }

  return (
    <>
      {/* 申请频道基本信息 */}
      <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px', fontWeight: '500' }}>申请频道基本信息</font></div>
      <Row style={{ marginLeft: 30, marginTop: 10 }}>
        {genCol('频道ID', record?.asid || '')}
        {genCol('申请时间', timeFormater(record?.timestamp, 9))}
      </Row>
      {/* <Row style={{ marginLeft: 30, marginTop: 10 }}>
        {genCol('申请前7天礼物流水/元', 'todo')}
        {genCol('申请前7天日均有流水主持数/人', 'todo')}
      </Row>
      <Row style={{ marginLeft: 30, marginTop: 10 }}>
        {genCol('是否达到申请条件', 'todo')}
        {genCol('是否绿色通道被白名单', 'todo')}
      </Row> */}
      <Row style={{ marginLeft: 30, marginTop: 10 }}>
        {genCol('公会名称', record?.guildApply?.guildInfo?.name || '')} 
      </Row>

      <Divider />

      {/* 运营直播平台信息 */}
      <div><font style={{ marginLeft: 40, fontSize: '24px', fontWeight: '500' }}>运营直播平台信息</font></div>
      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        {/* {genCol('是否运营交友或其他直播平台', 'TODO')} */}
        {genCol('直播平台名称', record?.guildApply?.platformInfo?.platformName || '')}
      </Row>
      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        {genCol('公会名称', record?.guildApply?.platformInfo?.guildName || '')}
        {genCol('签约主播数/人', record?.guildApply?.platformInfo?.compereCount || '')}
      </Row>
      {/* <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        {genCol('近3个自然月流水/元', 'TODO')}
        {genCol('开播链接', 'TODO')}
      </Row> */}
      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        {genCol('公会ID', record?.guildApply?.platformInfo?.guildId || '')} 
      </Row>
      {
        displayScreenshot(record)
      }
      <Divider />

      {/* 实名、资质及联系信息 */}
      <div><font style={{ marginLeft: 40, fontSize: '24px', fontWeight: '500' }}>实名、资质及联系信息</font></div>
      {/* <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        {genCol('实名姓名', record?.realName || '')}
        {genCol('实名身份证号', record?.realIdentity || '')}
      </Row> */}
      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        {/* {genCol('实名手机号', record?.realPhone || '')} */}
        {genCol('法人真实姓名', record?.guildApply?.qualificationInfo?.realName || '')}
      </Row>
      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        {genCol('法人身份证号', record?.guildApply?.qualificationInfo?.identity || '')}
        {genCol('法人手机号', record?.guildApply?.qualificationInfo?.phone || '')}
      </Row>

      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        {/* {genCol('姓名是否与实名一致', yesNoMap3[record?.realNameStatus] || '')} */}
        {/* {genCol('身份证号是否与实名一致', yesNoMap3[record?.realIdentityStatus] || '')} */}
      </Row>
      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        {/* {genCol('手机号是否与实名一致', yesNoMap3[record?.realPhoneStatus] || '')} */}
        {genCol('联系人电话', record?.guildApply?.guildInfo?.phone || '')}
      </Row>

      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        {genCol('联系人邮箱', record?.guildApply?.guildInfo?.email || '')}
        {genCol('联系人地址', record?.guildApply?.guildInfo?.address || '')}
      </Row>
      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        {genCol('公司全称', record?.guildApply?.qualificationInfo?.companyName || '')}
        {genCol('企业类型', companyTypeMap[record?.guildApply?.qualificationInfo?.companyType] || '')}
      </Row>
      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        {genCol('注册资金/元', record?.guildApply?.qualificationInfo?.registerMoney || '')}
        {genCol('公司地址', record?.guildApply?.qualificationInfo?.address || '')}
      </Row>
      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        {genCol('公司基本户开户银行', record?.guildApply?.qualificationInfo?.bank || '')}
        {genCol('公司基本户账户', record?.guildApply?.qualificationInfo?.account || '')}
      </Row>
      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        <font style={{ marginLeft: 10, fontSize: '16px' }}>法人身份证正、反面：</font>
        <Col span={4}>
          <PopImage value={record?.guildApply?.qualificationInfo?.identityPicture?.[0] || ''} />
        </Col>
        <Col span={4}>
          <PopImage value={record?.guildApply?.qualificationInfo?.identityPicture?.[1] || ''} />
        </Col>
      </Row>
      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        <font style={{ marginLeft: 10, fontSize: '16px' }}>公司营业执照原件扫描件：</font>
        <Col span={12}>
          <PopImage value={record?.guildApply?.qualificationInfo?.companyPicture || ''} />
        </Col>
      </Row>
      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        <font style={{ marginLeft: 10, fontSize: '16px' }}>公司开户许可证原件扫描件：</font>
        <Col span={12}>
          <PopImage value={record?.guildApply?.qualificationInfo?.allowPicture || ''} />
        </Col>
      </Row>
      <Divider />

      {/* 企业认证 */}
      <div style={{ marginLeft: 42 }}><font color='red'>若审核不通过，可联系客服(钟淑玲)</font></div>
      <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px', fontWeight: '500' }}>企业认证</font></div>
      <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
        <Col span={12}>
          <font style={{ fontSize: '16px' }}>企业认证审核状态：{companyRealAuthMap[record?.companyRealAuthStatus] || ''}</font>
        </Col>
        <Col span={12}>
          <font style={{ fontSize: '16px' }}>审核描述：{record?.companyRealAuthDesc || ''}</font>
        </Col>
      </Row>
    </>
  )
} 

const displayScreenshot = (record) => { 
  if (!record?.guildApply?.platformInfo) {
    return ''
  }
  return <>
    <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><font style={{ marginLeft: 10, fontSize: '16px' }}>公会流水截图凭证：</font></Row>
    <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
      {
        record.guildApply.platformInfo?.screenshot?.map(item => {
          return <Col span={4}><PopImage value={item} /></Col>
        })        
      }
    </Row>
  </>
}
