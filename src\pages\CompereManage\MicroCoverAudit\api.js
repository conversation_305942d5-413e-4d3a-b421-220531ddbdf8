import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/new_compere_info/get_audit_micro_cover_list?${stringify(params)}`)
}

export function auditStatus (params) {
  return request(`/new_compere_info/update_audit_status_micro_cover`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
