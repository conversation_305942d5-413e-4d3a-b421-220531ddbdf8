import React, { Component } from 'react'
import { Table, Divider, But<PERSON>, Card, Form, DatePicker, Input, message, Select } from 'antd'
import { connect } from 'dva'
import exportExcel from '@/utils/exportExcel'
import formatNumberWithThousandsSeparators from '../common'

var moment = require('moment')
const { RangePicker } = DatePicker
const Option = Select.Option
const namespace = 'flameGame'
const dateFormat = 'YYYY-MM-DD'

@connect(({ flameGame }) => ({ // model 的 namespace
  model: flameGame // model 的 namespace
}))
class FlameDetailReport extends Component {
  constructor (props) {
    super(props)

    this.state = {
      detailList: [],
      dateRange: [moment().subtract(1, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.getDetailList()
  }

    // 调用 model 处理函数
    callModel = (funcName, params) => {
      const { dispatch } = this.props
      dispatch({
        type: `${namespace}/${funcName}`,
        payload: params
      })
    }

    // 获取详细信息
    getDetailList = () => {
      const { uid, upgradeResult, dateRange } = this.state
      this.callModel('getDetailReport', {
        params: { uid: uid, upgradeResult: upgradeResult, start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) },
        isRawRespMode: true,
        cbFunc: (ret) => {
          let { status, msg, list } = ret
          if (status < 0) {
            message.warn('获取详细信息: ' + msg)
            return
          }
          if (list) {
            list.sort((a, b) => b.timestamp - a.timestamp)
          }
          this.setState({ detailList: Array.isArray(list) ? list : [] })
        }
      })
    }

    // 日期（格式：YYYY-MM-DD HH：MM：SS）、用户UID、用户昵称、升级内容、升级价值/元、升级结果、升级产出、扣除损耗度/元、所在频道、频道主持UID、主持签约频道签约
    columns = [
      { title: '日期', dataIndex: 'index', align: 'center', render: (text, record) => (text = moment.unix(record.timestamp).format('YYYY-MM-DD HH:mm:ss')) },
      { title: '用户UID', dataIndex: 'uid', align: 'center' },
      { title: '用户昵称', dataIndex: 'nick', align: 'center' },
      { title: '升级内容', dataIndex: 'upgradeDetail', align: 'center', render: (_, record) => this.formatProps(record.usedPropsName, record.usedPropsCount) },
      { title: '升级价值', dataIndex: 'usedAmethyst', align: 'center', render: (text) => formatNumberWithThousandsSeparators(text) },
      { title: '升级结果', dataIndex: 'upgradeResult', align: 'center', render: text => (text === 1 ? '升级成功' : text === 2 ? '升级失败' : '未知') },
      { title: '升级产出', dataIndex: 'upgradeOutput', align: 'center', render: (_, record) => this.formatProps(record.gainPropsName, record.gainPropsCount) },
      { title: '扣除损耗度', dataIndex: 'lossDurability', align: 'center', render: (text, record) => (text === 0 ? 0 : formatNumberWithThousandsSeparators(record.usedAmethyst - record.lossDurability)) },
      { title: '所在频道', dataIndex: 'sid', align: 'center' },
      { title: '频道主持UID', dataIndex: 'compereUid', align: 'center' },
      { title: '主持签约频道', dataIndex: 'compereSignSid', align: 'center' }
    ]

    pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

    formatProps = (propsName, propsCount) => {
      return propsName + '*' + propsCount
    }

    onClick = () => {
      this.getDetailList()
    }

    onChange = (date, format) => {
      this.setState({ dateRange: date })
    }

    onUpgradeResultChange = (value) => {
      this.setState({ upgradeResult: value.key })
    }

    onExport = () => {
      let headers = []
      this.columns.forEach(function (item) {
        headers.push({ key: item.dataIndex, header: item.title })
      })

      const { detailList } = this.state
      var exportData = detailList.map(item => {
        let v = $.extend(true, {}, item)
        v.index = moment.unix(v.timestamp).format('YYYY-MM-DD HH:mm:ss')
        v.upgradeDetail = this.formatProps(v.usedPropsName, v.usedPropsCount)
        v.usedAmethyst = formatNumberWithThousandsSeparators(v.usedAmethyst)
        v.upgradeResult = v.upgradeResult === 1 ? '升级成功' : v.upgradeResult === 2 ? '升级失败' : '未知'
        v.upgradeOutput = this.formatProps(v.gainPropsName, v.gainPropsCount)
        v.lossDurability = v.lossDurability === 0 ? 0 : formatNumberWithThousandsSeparators(v.usedAmethyst - v.lossDurability)
        return v
      })

      exportExcel(headers, exportData)
    }

    /* *******************************页面布局***************************************************************/
    render () {
      const { dateRange, detailList } = this.state

      return (
        <Card>
          <Form>
            UID:
            <Input onChange={e => this.setState({ uid: e.target.value })} style={{ marginLeft: 10, width: 150 }} /> {/* 搜索按钮 */}
            <span style={{ marginLeft: 10 }}>时间范围:</span>
            <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} onChange={this.onChange} />
            <Select labelInValue defaultValue={{ key: 0 }} style={{ marginLeft: 10, width: 100 }} onChange={this.onUpgradeResultChange}>
              <Option value={0}>全部</Option>
              <Option value={1}>升级成功</Option>
              <Option value={2}>升级失败</Option>
            </Select>
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onClick}>查询</Button>
            <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
            <font style={{ marginLeft: 5 }} color='red'>流水单位-紫水晶(仅作模拟测算参考)</font>
            <Divider />
            <Table dataSource={detailList} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
          </Form>
        </Card>
      )
    }
}

export default FlameDetailReport
