import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { <PERSON><PERSON>, Card, DatePicker, Divider, Form, Input, Table, Tabs } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import { CSVLink } from 'react-csv'

var moment = require('moment')
const namespace = 'dsRetainMonitor'
const getRetainListUri = `${namespace}/getRetainList`
const getWinnerListUri = `${namespace}/getWinnerList`
const { TabPane } = Tabs
const userTypeMap = { all: 'ALL', win: '中奖用户', loss: '亏损用户', profit: '盈利用户' }

// const dateFormat = 'YYYY-MM-DD'
// const { RangePicker } = DatePicker

@connect(({ dsRetainMonitor }) => ({
  model: dsRetainMonitor
}))
class DSRetainMonitor extends Component {
  // 定义列表结构，
  retainColumns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '用户范围', dataIndex: 'userType', align: 'center', render: (text, record, index) => { return userTypeMap[text] }, filters: [{ text: 'ALL', value: 'all' }, { text: '中奖用户', value: 'win' }, { text: '亏损用户', value: 'loss' }, { text: '盈利用户', value: 'profit' }], onFilter: (value, record) => record.platform.includes(value) },
    { title: '押注用户数', dataIndex: 'userCount', align: 'center' },
    { title: '押注金额', dataIndex: 'betAmethyst', align: 'center' },
    { title: '次日存留', dataIndex: 'retainRate1', align: 'center' },
    { title: '次日押注金额', dataIndex: 'betAmethyst1', align: 'center' },
    { title: '3日存留', dataIndex: 'retainRate2', align: 'center' },
    { title: '3日押注金额', dataIndex: 'betAmethyst2', align: 'center' },
    { title: '4日存留', dataIndex: 'retainRate3', align: 'center' },
    { title: '4日押注金额', dataIndex: 'betAmethyst3', align: 'center' },
    { title: '5日存留', dataIndex: 'retainRate4', align: 'center' },
    { title: '5日押注金额', dataIndex: 'betAmethyst4', align: 'center' },
    { title: '6日存留', dataIndex: 'retainRate5', align: 'center' },
    { title: '6日押注金额', dataIndex: 'betAmethyst5', align: 'center' },
    { title: '7日存留', dataIndex: 'retainRate6', align: 'center' },
    { title: '7日押注金额', dataIndex: 'betAmethyst6', align: 'center' }
  ]

  winnerColumns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '用户身份', dataIndex: 'role', align: 'center', render: (text) => text === '' ? '未知' : text },
    { title: '用户等级', dataIndex: 'level', align: 'center' },
    { title: '押注金额', dataIndex: 'betAmethyst', align: 'center' },
    { title: '盖章金额/紫水晶', dataIndex: 'sealAmount1', align: 'center' },
    { title: '盖章金额/紫水晶券', dataIndex: 'sealAmount2', align: 'center' },
    { title: '盖章金额/Y币', dataIndex: 'sealAmount3', align: 'center' },
    { title: '押注次数', dataIndex: 'betCount', align: 'center' },
    { title: '中奖金额', dataIndex: 'winAmethyst', align: 'center' },
    { title: '中奖率', dataIndex: 'winRate', align: 'center' },
    { title: '是否盈利', dataIndex: 'isProfit', align: 'center', render: (text) => text === 1 ? '盈利用户' : text === 2 ? '亏损用户' : '持平' },
    { title: '是否中奖', dataIndex: 'isWinner', align: 'center', render: (text) => text === 1 ? '中奖用户' : '未中奖' }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  pagination = { pageSizeOptions: ['20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, startValue: moment().subtract(7, 'days'), endValue: moment().add(1, 'days'), uid: 0 }

  // 获取列表
  componentDidMount () {
    this.loadData()
  }

  loadData = (activityKey) => {
    if (activityKey === undefined) {
      activityKey = '1'
    }

    const { dispatch } = this.props
    const { startValue, endValue, uid } = this.state
    var data = { start: moment(startValue).format('YYYY-MM-DD'), end: moment(endValue).format('YYYY-MM-DD'), query_uid: uid }
    dispatch({
      type: this.getSelectedKey(activityKey),
      payload: data
    })
  }

  disabledStartDate = (startValue) => {
    const endValue = this.state.endValue
    if (!startValue || !endValue) {
      return false
    }
    return startValue.valueOf() > endValue.valueOf()
  }

  disabledEndDate = (endValue) => {
    const startValue = this.state.startValue
    if (!endValue || !startValue) {
      return false
    }
    return endValue.valueOf() <= startValue.valueOf()
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  onRetainClick = () => {
    const { dispatch } = this.props
    const { startValue, endValue } = this.state
    var data = { start: moment(startValue).format('YYYY-MM-DD'), end: moment(endValue).format('YYYY-MM-DD') }
    dispatch({
      type: getRetainListUri,
      payload: data
    })
  }

  onWinnerClick = () => {
    const { dispatch } = this.props
    const { startValue, endValue, uid } = this.state
    var data = { start: moment(startValue).format('YYYY-MM-DD'), end: moment(endValue).format('YYYY-MM-DD'), query_uid: uid }
    dispatch({
      type: getWinnerListUri,
      payload: data
    })
  }

  getSelectedKey = (activityKey) => {
    let uri
    if (activityKey === '1') {
      uri = getRetainListUri
    } else if (activityKey === '2') {
      uri = getWinnerListUri
    }

    return uri
  }

  tabOnChange = type => activityKey => {
    if (type !== undefined || type != null) {
      activityKey = type
    }

    // this.setState({ activityKey: activityKey })
    this.loadData(activityKey)
  }

  render () {
    const { route, model: { list } } = this.props
    const { startValue, endValue } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs onChange={this.tabOnChange()} type='card'>
          <TabPane tab='用户留存数据' key='1'>
            <Card>
              <div>
                日期范围
                <DatePicker
                  disabledDate={this.disabledStartDate}
                  format='YYYY-MM-DD'
                  defaultValue={startValue}
                  placeholder='开始日期'
                  onChange={this.onStartChange}
                  style={{ marginLeft: 10 }}
                />
                <span style={{ marginLeft: 10 }}>~</span>
                <DatePicker
                  disabledDate={this.disabledEndDate}
                  format='YYYY-MM-DD'
                  defaultValue={endValue}
                  placeholder='结束日期'
                  onChange={this.onEndChange}
                  style={{ marginLeft: 10 }}
                />
                <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onRetainClick}>查询</Button>
                <CSVLink data={list} target='_blank'><Button type='primary' style={{ marginLeft: 10 }}>导出</Button></CSVLink>
              </div>
              <Divider />
              <Form>
                <Table dataSource={list} columns={this.retainColumns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
              </Form>
            </Card>
          </TabPane>
          <TabPane tab='用户中奖数据' key='2'>
            <Card>
              <div>
                UID
                <Input onChange={e => this.setState({ uid: e.target.value })} style={{ marginLeft: 10, width: 150 }} /> {/* 搜索按钮 */}
                <span style={{ marginLeft: 10 }}>日期范围</span>
                <DatePicker
                  disabledDate={this.disabledStartDate}
                  format='YYYY-MM-DD'
                  defaultValue={startValue}
                  placeholder='开始日期'
                  onChange={this.onStartChange}
                  style={{ marginLeft: 10 }}
                />
                <span style={{ marginLeft: 10 }}>~</span>
                <DatePicker
                  disabledDate={this.disabledEndDate}
                  format='YYYY-MM-DD'
                  defaultValue={endValue}
                  placeholder='结束日期'
                  onChange={this.onEndChange}
                  style={{ marginLeft: 10 }}
                />
                <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onWinnerClick}>查询</Button>
                <CSVLink data={list} target='_blank'><Button type='primary' style={{ marginLeft: 10 }}>导出</Button></CSVLink>
              </div>
              <Divider />
              <Form>
                <Table dataSource={list} columns={this.winnerColumns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
              </Form>
            </Card>
          </TabPane>
        </Tabs>
      </PageHeaderWrapper>
    )
  }
}
export default DSRetainMonitor
