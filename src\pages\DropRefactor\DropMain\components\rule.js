import React, { Component } from 'react'
import { Card, Divider, Button, Modal, Form, Table, Select, InputNumber, Input, Tooltip } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { connect } from 'dva'

const namespace = 'dropMain' // model 的 namespace
const FormItem = Form.Item

@connect(({ dropMain }) => ({ // model 的 namespace
  model: dropMain // model 的 namespace
}))
class DropRuleComponent extends Component {
  state = {
    value: {},
    visible: false
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getRule`
    })
  }
 // 表头显示提示语
 genColumnTooltip = (title) => {
   return {
     filterDropdown: (<span />),
     filterIcon: (
       <Tooltip placement='top' title={title}>
         <QuestionCircleOutlined style={{ fontSize: '16px' }} />
       </Tooltip>
     )
   }
 }
// 创建tooltip
createTooltip = (v, width = 10) => {
  return !v ? '-' : <Tooltip title={v}>
    <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', width: `${width}em` }}>{v}</div>
  </Tooltip>
}

  columns = [
    { title: '关联道具池ID', dataIndex: 'id', align: 'center' },
    { title: '配置名', dataIndex: 'name', align: 'center' },
    { title: '开启超级模式阈值/紫水晶', dataIndex: 'thresholdB', align: 'center', render: v => v.toLocaleString(), ...this.genColumnTooltip('空投:开启超级模式阈值/紫水晶; \n幸运小狗：满进度需要的投食次数') },
    { title: '开启医疗包需要的空投次数', dataIndex: 'roomCountR', align: 'center' },
    { title: '超级模式持续时长/秒', dataIndex: 'crazyDurationTime', align: 'center' },
    { title: '龙宫冷却时长/秒', dataIndex: 'dragonCoolDown', align: 'center' },
    { title: '龙宫过期时长/秒', dataIndex: 'dragonExpireSecs', align: 'center' },
    { title: '操作', align: 'center', render: (text, record) => <a style={{ marginRight: 10 }} onClick={this.showModal(true, record)}>编辑</a> }
  ]

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()

      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, isUpdate: isUpdate, title: isUpdate ? '更新规则' : '新建规则' })
  }

  // 隐藏弹窗，不需要修改
  hideModal = () => {
    this.setState({ visible: false })
  }

  onFinish = values => {
    const { dispatch } = this.props
    const { isUpdate } = this.state

    let uri = isUpdate ? `${namespace}/updateRule` : `${namespace}/addRule`

    console.log(values)

    dispatch({
      type: uri,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 更新与删除
  handleSubmit = () => {
    this.formRef.submit()
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  // 列表过滤
  listFilter = (before) => {
    const { poolNameOptions } = this.props.model
    const offlinePoolID = [11000]
    const after = before.filter(item => {
      if (offlinePoolID.indexOf(item.id) >= 0) {
        return false
      }
      return poolNameOptions.some(entry => { return entry.value === item.id })
    })
    return after
  }

  // 选项过滤,隐藏已有的
  optionsFilter = (options, rule) => {
    if (!Array.isArray(options)) {
      return []
    }
    return options.filter(item => {
      return rule.findIndex((entry) => { return entry.id === item.value }) === -1
    })
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { ruleList, poolNameOptions } } = this.props
    const { visible, title, isUpdate } = this.state
    const formItemLayout = { // 不需要修改
      labelCol: {
        xs: { span: 10 },
        sm: { span: 10 }
      },
      wrapperCol: {
        xs: { span: 14 },
        sm: { span: 14 }
      }
    }

    return (
      <Card>
        <Button onClick={this.showModal(false, this.defaultValue)}>新建规则</Button>
        <Divider />
        <Table rowKey={(record, index) => index} dataSource={this.listFilter(ruleList)} columns={this.columns} pagination={false} />

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} ref={form => { this.formRef = form }} {...formItemLayout}>
            <FormItem name='id' label='道具池ID'>
              <Select options={this.optionsFilter(poolNameOptions, ruleList)} readOnly={isUpdate} />
            </FormItem>
            <FormItem name='name' label='规则名称'>
              <Input />
            </FormItem>
            <FormItem label='每日底池/紫水晶' name='dailyPriceN' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='开启超级模式阈值' name='thresholdB' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='开启医疗包需要空投次数' name='roomCountR' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='超级模式持续时长' name='crazyDurationTime' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='龙宫冷却时长' name='dragonCoolDown' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='龙宫过期时长' name='dragonExpireSecs' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </FormItem>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default DropRuleComponent
