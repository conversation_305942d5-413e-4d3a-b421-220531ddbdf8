/* eslint-disable no-unexpected-multiline */
import React, { Component } from 'react'
import { Card, Table, Form, message, Button, Input, InputNumber } from 'antd'
import { connect } from 'dva'
// import moment from 'moment'
import OffstripPropsComponent from './props'
// import DropQueryChart from './chart'

const namespace = 'offstripMain' // model 的 namespace

@connect(({ offstripMain }) => ({ // model 的 namespace
  model: offstripMain // model 的 namespace
}))
class OffstripReleaseMainComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      pid: 1000,
      propVisible: false,
      visible: false, // 弹窗
      editing: false, // 编辑状态，奖池无法选择
      list: [] // 奖池
    }
  }

  /** ************************************组件初始化**************************************************/
  componentDidMount () {
    const { dispatch } = this.props
    const { pid } = this.state

    // 注册监听函数，在model的poolConfig发生更新时调用，用来更新当前组件的state的list
    dispatch({
      type: `${namespace}/listen`,
      payload: this.listenPoolConfigChange
    })

    // 刷新页面默认加载第一个奖池
    dispatch({
      type: `${namespace}/getReleasePool`,
      payload: { id: pid }
    })
  }

  // model更新时，用于刷新当前state的list
  listenPoolConfigChange = poolConfig => {
    let list = []
    let totalLimit = 0
    let approvalDesc = ''
    let range1 = '[X1,X2)'; let range2 = '[X2,X3)'; let range3 = '[X3,X4)'; let range4 = '[X4,Infinity)'
    if (poolConfig !== undefined && poolConfig.content !== undefined) {
      try {
        if (poolConfig.content !== '') {
          list = JSON.parse(poolConfig.content)
          for (let i = 0; i < list.length; i++) {
            if (list[i].probability1 > 0) {
              list[i].probability1 /= 100
            }
            if (list[i].probability2 > 0) {
              list[i].probability2 /= 100
            }
            if (list[i].probability3 > 0) {
              list[i].probability3 /= 100
            }
            if (list[i].probability4 > 0) {
              list[i].probability4 /= 100
            }
          }
        }
      } catch (e) {
        message.error('content json convert error ', e)
      }
      approvalDesc = poolConfig.approvalDesc
      totalLimit = poolConfig.totalLimit
      range1 = '[' + poolConfig.rangeMin1 + ',' + poolConfig.rangeMax1 + ')'
      range2 = '[' + poolConfig.rangeMin2 + ',' + poolConfig.rangeMax2 + ')'
      range3 = '[' + poolConfig.rangeMin3 + ',' + poolConfig.rangeMax3 + ')'
      range4 = '[' + poolConfig.rangeMin4 + ',' + poolConfig.rangeMax4 + ')'
    }
    this.setState({ list: list, range1: range1, range2: range2, range3: range3, range4: range4, totalLimit: totalLimit, approvalDesc: approvalDesc })
  }

  /** ********************************Table定义************************************************* */
  columns = [
    {
      title: '奖池设定',
      dataIndex: 'propsInfo',
      children: [
        { title: '编号', align: 'center', dataIndex: 'id' },
        { title: '奖品ID', align: 'center', dataIndex: 'prizeId' },
        { title: '发奖ID', align: 'center', dataIndex: 'propsId' },
        { title: '奖品名称', align: 'center', dataIndex: 'propsName' },
        { title: '数量', align: 'center', dataIndex: 'count' },
        { title: '价值/紫水晶', align: 'center', dataIndex: 'value', render: (text, record) => (record.value * record.count).toLocaleString() }
      ]
    },
    {
      title: '[X1,X2)',
      dataIndex: 'range1',
      children: [
        { title: '中奖概率%', align: 'center', dataIndex: 'probability1' },
        { title: '投放上限/D', align: 'center', dataIndex: 'dailyLimit1' }
      ]
    },
    {
      title: '[X2,X3)',
      dataIndex: 'range2',
      children: [
        { title: '中奖概率%', align: 'center', dataIndex: 'probability2' },
        { title: '投放上限/D', align: 'center', dataIndex: 'dailyLimit2' }
      ]
    },
    {
      title: '[X3,X4)',
      dataIndex: 'range3',
      children: [
        { title: '中奖概率%', align: 'center', dataIndex: 'probability3' },
        { title: '投放上限/D', align: 'center', dataIndex: 'dailyLimit3' }
      ]
    },
    {
      title: '[X4,X5)',
      dataIndex: 'range4',
      children: [
        { title: '中奖概率%', align: 'center', dataIndex: 'probability4' },
        { title: '投放上限/D', align: 'center', dataIndex: 'dailyLimit4' }
      ]
    }
  ]

  renderMergeColumn (value, row, index) {
    const obj = {
      children: value,
      props: {}
    }

    if (row.rowSpan > 0) {
      obj.props.rowSpan = row.rowSpan
    } else {
      obj.props.rowSpan = 0 // merged into above cell
    }

    return obj
  }

  /** **********************************切换奖池******************************************************************/

  onSelectChange = cid => {
    // console.log(cid)
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getReleasePool`,
      payload: { id: cid }
    })

    this.setState({ pid: cid })
  }

  /** **********************************table 渲染******************************************************************/
  onInputTitleChange = (field) => e => {
    this.setState({ [field]: e.target.value })
  }

  onInputChange = (row, field) => value => {
    const { list } = this.state
    let v = $.extend([], true, list)
    v[row][field] = value
    this.forceUpdate() // 强制刷新，用于计算紫水晶价值
  }

  onInputRangeChange = (row, field) => value => {
    const { list } = this.state
    let v = $.extend([], true, list)
    v[row][field] = value
    this.forceUpdate() // 强制刷新，用于计算紫水晶价值
  }

  // 行选择
  onBtnClick = row => () => {
    this.setState({ row, propVisible: true }) // 当前正在编辑的行
  }

  // 选择营收礼物
  onPropChange = prop => {
    const { row, list } = this.state
    if (row === undefined) {
      message.error('unfined row' + row)
      return
    }

    // console.log(prop, row)

    let v = $.extend([], true, list)
    v[row].propsId = prop.id // 礼物ID
    v[row].propsName = prop.name // 礼物名称
    v[row].propsUrl = prop.url // 礼物图片
    v[row].value = prop.price // 礼物加个
    v[row].prizeType = prop.prizeType // 礼物渠道

    this.setState({ propVisible: false, list: v })
  }

  // 营收礼物列表隐藏
  onPropCancel = () => {
    this.setState({ propVisible: false })
  }

  renderColumn = () => {
    const { editing, range1, range2, range3, range4 } = this.state
    let renderColumns = []
    this.columns.forEach(column => {
      if (['range1'].indexOf(column.dataIndex) > -1) {
        column.title = () => { return editing ? <Input onChange={e => this.setState({ range1: e.target.value })} defaultValue={range1} /> : range1 }
        // column.title = () => { return editing ? <Input onChange={this.onInputTitleChange(range1)} defaultValue={range1} /> : range1 }
      }

      if (['range2'].indexOf(column.dataIndex) > -1) {
        column.title = () => { return editing ? <Input onChange={e => this.setState({ range2: e.target.value })} defaultValue={range2} /> : range2 }
      }

      if (['range3'].indexOf(column.dataIndex) > -1) {
        column.title = () => { return editing ? <Input onChange={e => this.setState({ range3: e.target.value })} defaultValue={range3} /> : range3 }
      }

      if (['range4'].indexOf(column.dataIndex) > -1) {
        column.title = () => { return editing ? <Input onChange={e => this.setState({ range4: e.target.value })} defaultValue={range4} /> : range4 }
      }

      if (['range1', 'range2', 'range3', 'range4'].indexOf(column.dataIndex) > -1) {
        column.children.forEach(item => {
          item.render = (text, record, index) => {
            return editing ? <InputNumber onChange={this.onInputRangeChange(index, item.dataIndex)} defaultValue={text} /> : text
          }
        })
      }

      if (column.dataIndex === 'propsInfo') {
        column.children.forEach(item => {
          if (item.dataIndex === 'propsId') {
            item.render = (text, record, index) => {
              return editing ? <Button onClick={this.onBtnClick(index, item.dataIndex)} type='link'>{text}</Button> : text
            }
          }
          if (item.dataIndex === 'count') {
            item.render = (text, record, index) => {
              return editing ? <InputNumber onChange={this.onInputChange(index, item.dataIndex)} defaultValue={text} /> : text
            }
          }
        })
      }

      renderColumns.push(column)
    })

    return renderColumns
  }

  /** ***********************************table 渲染结束********************************************************* */

  /* *******************************页面布局***************************************************************/
  render () {
    const { list, editing, propVisible, totalLimit } = this.state

    return (
      <Card>
        <div>
          <Form>
            <Form.Item label='发奖上限（元/D）'>
              {editing ? <InputNumber defaultValue={totalLimit} onChange={e => this.setState({ totalLimit: e })} /> : totalLimit}
              <font color='red' style={{ marginLeft: 20 }}>(奖池支出达到发奖上限后, 当天将不再爆奖)</font>
            </Form.Item>
          </Form>
          <Form.Item>
            <div><font color='red'>1. "X"指用户参与豆荚玩法获得的返奖金额, X值落在哪个金额区间, 即按照对应区间的奖品概率进行爆奖</font></div>
            <div><font color='red'>2. 发放上限/D: -1, 视为不设限; 0或其他正整数, 按填写的数量进行限制</font></div>
            <div><font color='red'>3. 概率, 每个发奖金额（单位-元）区间内, 各奖品的出奖概率为独立事件</font></div>
          </Form.Item>
        </div>
        <Table bordered pagination={false} size='small' rowKey='id' columns={this.renderColumn()} dataSource={list} />
        <OffstripPropsComponent onCancel={this.onPropCancel} onChange={this.onPropChange} visible={propVisible} />
      </Card>
    )
  }
}

export default OffstripReleaseMainComponent
