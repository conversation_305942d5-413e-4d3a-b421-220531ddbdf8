import React, { Component } from 'react'
import { Editor } from '@tinymce/tinymce-react'

// 对富文本编辑器再进行一层封装，减少一些
// 可传参数：onChange, value

// const TinymceSrc = 'https://cdn.staticfile.org/tinymce/5.4.1/tinymce.min.js'
const TinymceSrc = `https://jy${ENV_TAG !== 'prod' ? '-test' : ''}.yystatic.com/jy/jy-boss-201809-feat-pc/tinymce/5.4.1/tinymce.min.js`

// 文件服务器地址
const fileServerHost = 'https://fts.yy.com/fs/uploadfiles'

// 默认配置
const editorConfiguration = {
  menubar: true,
  branding: false,
  statusbar: false,
  height: 400,
  elementpath: false,
  selector: '#tinydemo',
  plugins: 'table paste autolink link hr nonbreaking insertdatetime lists noneditable preview code image',
  toolbar: `bold italic underline strikethrough| forecolor backcolor removeformat | numlist bullist alignleft aligncenter alignright alignjustify outdent indent | link image table | preview code | fontsizeselect fontselect`,
  toolbar_drawer: 'sliding',
  automatic_uploads: true,
  paste_data_images: true,
  images_reuse_filename: true,
  fontsize_formats: '12px 14px 16px 18px 24px 36px 48px 56px 72px',
  // 图片上传的实现
  images_upload_handler: function (file, success, failure, progress) {
    var xhr, formData

    // eslint-disable-next-line no-undef
    xhr = new XMLHttpRequest()
    xhr.withCredentials = false
    xhr.open('POST', fileServerHost)
    xhr.upload.onprogress = function (e) {
      progress(e.loaded / e.total * 100)
    }
    xhr.onload = function () {
      if (xhr.status < 200 || xhr.status >= 300) {
        failure('HTTP Error: ' + xhr.status)
        return
      }
      var json = JSON.parse(xhr.responseText)
      if (!json) {
        failure('Invalid JSON: ' + xhr.responseText)
        return
      }
      json.location = json.urls[0]
      success(json.location)
    }
    xhr.onerror = function () {
      failure('Image upload failed due to a XHR Transport error. Code: ' + xhr.status)
    }
    // eslint-disable-next-line no-undef
    formData = new FormData()
    formData.append('files', file.blob(), file.filename())
    formData.append('bucket', 'makefriends')
    xhr.send(formData)
  }
}

class TinymceEditer extends Component {
  componentDidMount = () => {
    console.debug('TinymceEditer prods=', this.props)
  }

  state = {}
  render () {
    return (
      <Editor
        value={this.props.value}
        tinymceScriptSrc={TinymceSrc}
        init={editorConfiguration}
        onChange={this.props.onChange}
      />
    )
  }
}

export default TinymceEditer
