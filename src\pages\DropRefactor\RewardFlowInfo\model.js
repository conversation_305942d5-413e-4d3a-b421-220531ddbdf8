import {
  getListFromServer,
  removeListFromServer,
  upsetListFromServer,
  getConfigListFromServer,
  approvalConfigFromServer,
  getReportListFromServer
} from './api'
import { message } from 'antd'

export default {
  namespace: 'RFIConfig',

  state: {
    author: 0,
    list: [],
    operaotrList: [],
    reportList: []
  },

  reducers: {
    updateList (state, { payload, author }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload,
        author
      }
    },

    updateConfigList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        operaotrList: payload
      }
    },

    updateReportList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        reportList: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      try {
        const { data: { list, author } } = yield call(getListFromServer)

        yield put({
          type: 'updateList',
          payload: Array.isArray(list) ? list : [],
          author: author
        })
      } catch (e) {
        message.error('err:' + e)
      }
    },

    * upsetList ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(upsetListFromServer, payload)
      if (status === 0) {
        if (msg.length === 0) {
          message.success('add success')
        } else {
          message.success(msg)
        }
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * approvalList ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(approvalConfigFromServer, payload)
      if (status === 0) {
        message.success('add success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * removeList ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(removeListFromServer, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed ' + msg)
      }
    },

    * getConfigList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getConfigListFromServer)

      yield put({
        type: 'updateConfigList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * getReportList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getReportListFromServer, payload)

      yield put({
        type: 'updateReportList',
        payload: Array.isArray(list) ? list : []
      })
    }
  }
}
