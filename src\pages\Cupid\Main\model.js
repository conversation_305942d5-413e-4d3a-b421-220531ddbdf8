import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const getAllPrizeList = genGetRequireTemplate('/drop/admin/query_prize_list', 'globalPrizeList') // 营收礼物列表

const getNormalPool = genGetRequireTemplate('/cupid/boss/get_pool_config/normal') // 普通配置
const getCrazyPool = genGetRequireTemplate('/cupid/boss/get_pool_config/crazy') // 狂欢配置
const updateNoramlPool = genUpdateTemplate('/cupid/boss/update_pool_config/normal') // 更新普通配置
const updateCrazyPool = genUpdateTemplate('/cupid/boss/update_pool_config/crazy') // 更新狂欢配置

const getToApproval = genGetRequireTemplate('/approval/admin/get_to_approval_byPID') // 审批状态获取
const doApproval = genUpdateTemplate('/approval/admin/do_approval') // 送审

export default {
  namespace: 'cupid',
  state: {
    list: [],
    globalPrizeList: [],
    title: 'Bind success!'
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },

  effects: {
    getNormalPool,
    getCrazyPool,
    updateNoramlPool,
    updateCrazyPool,
    getAllPrizeList,
    getToApproval,
    doApproval
  }
}
