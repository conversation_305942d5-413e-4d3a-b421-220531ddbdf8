import React, { Component } from 'react'
import { connect } from 'dva'
import {
  Card,
  Table,
  Input,
  Button,
  Modal,
  Form, DatePicker, InputNumber, Divider, Popconfirm, message, Typography
} from 'antd'

import moment from 'moment'
import 'moment/locale/zh-cn'
import PopImage from '@/components/PopImage'

const { Text } = Typography
moment.locale('zh-cn')
const namespace = 'brandActLive'
const TextArea = Input.TextArea

@connect(({ brandActLive }) => ({
  model: brandActLive
}))

class BrandActivityRoutine extends Component {
  constructor (props) {
    super(props)
    const { routineConfigList } = props
    this.state = {
      selectedRowKeys: [],
      selectedRow: '',
      list: routineConfigList,
      visible: false,
      isUpdate: false,
      value: {}
    }
  }
  columns = [
    { title: '序号', dataIndex: 'idx', align: 'center' },
    { title: '活动ID', dataIndex: 'id', align: 'center' },
    { title: '活动标题', dataIndex: 'titleName', align: 'center' },
    {
      title: '常规礼物列表',
      dataIndex: 'propsDescList',
      align: 'center',
      render: text => Array.isArray(text) ? text.join(',') : ''
    },
    { title: '生效时间（红色为当前生效配置）',
      dataIndex: 'startTime',
      align: 'center',
      width: 300,
      render: (text, record) => {
        if (!text) {
          return ''
        }
        return this.getActTimeDisplay(record)
      }
    },
    { title: '主持人奖励轮次', dataIndex: 'round', align: 'center' },
    {
      title: '背景图',
      dataIndex: 'bgUrl',
      align: 'center',
      render: text => <PopImage value={text} />
    },
    {
      title: '更新人',
      key: 'opInfo',
      align: 'center',
      render: (text, record) => {
        if (!text) {
          return ''
        }
        return record.opInfo.updatePassport.length > 0 ? record.opInfo.updatePassport : '系统'
      }
    },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      width: 120,
      render: (text, record) => (
        <span>
          <a size='small' type='primary' onClick={this.showModal(true, record)}>修改</a>
          <Divider type='vertical' />
          <Popconfirm title='确认删除常规任务配置?' onConfirm={this.deleteItem(record.id)}><a>删除</a></Popconfirm>
        </span>
      )
    }
  ]

  getActTimeDisplay = (record, width = 100) => {
    if (record.startTime === undefined || record.endTime === undefined) {
      return ''
    }
    let startDate = moment.unix(record.startTime).format('YYYY-MM-DD HH:mm:ss')
    let endDate = moment.unix(record.endTime).format('YYYY-MM-DD HH:mm:ss')
    let timeNow = moment().unix()
    if (record.startTime <= timeNow && timeNow <= record.endTime) {
      return <div style={{ color: 'red' }}>{startDate}~{endDate}</div>
    }
    return <div style={{ color: '#bfbfbf' }}>{startDate}~{endDate}</div>
  }

  // show modal
  showModal = (isUpdate, record) => () => {
    if (record == null) record = { uid: '' }

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.startTime = moment.unix(v.startTime)
      v.endTime = moment.unix(v.endTime)
      v.propsIdList = v.propsIdList.join('\n')
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  deleteItem = (id) => () => {
    const { dispatch } = this.props
    console.log('deleteItem', id)
    dispatch({
      type: `${namespace}/deleteRoutineConfig`,
      payload: { id: id }
    }).then(res => {
      this.loadData()
    })
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  componentDidMount () {
    this.loadData()
  }

  loadData = () => {
    const { dispatch } = this.props
    let data = { }
    dispatch({
      type: `${namespace}/getRoutineConfigList`,
      payload: data
    })
  }

  searchHandle = () => () => {
    this.loadData()
  }

 // 添加
 addHandle = () => () => {
   this.setState({ visible: true, isUpdate: false })
 }

 saveFormRef = (formRef) => {
   this.formRef = formRef
 }

 hiddenModal = () => {
   this.setState({ visible: false, conformUidList: [] })
   if (this.formRef) {
     this.formRef.resetFields()
   }
 }

 handleCancel = e => {
   if (this.formRef) {
     this.formRef.resetFields()
   }
   this.setState({ visible: false })
 }

 handleSubmit = e => {
   if (this.formRef) {
     this.formRef.submit()
   }
 }

  onAddConfig = () => {
    this.initForm()
    this.setState({ visible: true, isUpdate: false })
  }

  initForm = () => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
  }

 onFinish = values => {
   const { isUpdate } = this.state
   const { model: { routineConfigList } } = this.props
   if (!values.id || values.id.length === 0) {
     message.error('活动id有误!')
     return
   }
   let startTime = 0
   let endTime = 0
   if (values.startTime) {
     startTime = values.startTime.unix()
   }
   if (values.endTime) {
     endTime = values.endTime.unix()
   }
   if (startTime === 0 || endTime === 0 || startTime >= endTime) {
     message.error('开始/结束时间有误!')
     return
   }

   const compareList = isUpdate ? routineConfigList.filter(data => data.id !== values.id) : routineConfigList
   console.log('compareList', compareList)
   for (let item of compareList) {
     if (item.id === values.id) {
       message.error('活动id重复!' + item.id)
       return
     }
     // if (item.titleName === values.titleName) {
     //   message.error('活动标题重复!' + item.titleName)
     //   return
     // }
     let startDate = moment.unix(item.startTime).format('YYYY-MM-DD HH:mm:ss')
     let endDate = moment.unix(item.endTime).format('YYYY-MM-DD HH:mm:ss')
     let timeRange = startDate + '~' + endDate
     let conflictMsg = '与"' + item.titleName + '"生效时间(' + timeRange + ')冲突！'
     if (item.startTime <= startTime && startTime < item.endTime) {
       console.log('conflictMsg1', conflictMsg)
       message.error(conflictMsg)
       return
     }
     if (item.startTime < endTime && endTime < item.endTime) {
       console.log('conflictMsg2', conflictMsg)
       message.error(conflictMsg)
       return
     }
     if (startTime <= item.startTime && item.endTime < endTime) {
       console.log('conflictMsg3', conflictMsg)
       message.error(conflictMsg)
       return
     }
   }

   let propsIdList = values.propsIdList ? values.propsIdList.trim().split('\n') : []
   if (propsIdList.length === 0) {
     message.error('常规礼物列表不能为空！')
     return
   }
   let commitPropsList = []
   for (let i = 0; i < propsIdList.length; i++) {
     let propsID = parseInt(propsIdList[i].trim()) || 0
     let lineNo = i + 1
     if (propsID === 0) {
       message.error('非法的常规礼物:第' + lineNo + '行(' + propsIdList[i] + ')')
       return
     }
     if (commitPropsList.includes(propsID)) {
       message.error('重复的常规礼物:第' + lineNo + '行(' + propsIdList[i] + ')')
       return
     }
     commitPropsList.push(propsID)
   }
   if (commitPropsList.length === 0) {
     message.error('常规礼物列表不能为空！')
     return
   }
   let configItem = {
     id: values.id,
     titleName: values.titleName,
     round: values.round,
     startTime: startTime,
     endTime: endTime,
     propsIdList: commitPropsList,
     bgUrl: values.bgUrl
   }
   let data = { isUpdate: isUpdate, configItem: configItem }
   console.log(data)

   this.props.dispatch({
     type: `${namespace}/updateRoutineConfig`,
     payload: data
   }).then(res => {
     this.loadData()
   })
   this.hiddenModal()
 }
 // ********************************************************

 render () {
   const { model: { routineConfigList } } = this.props
   const { visible, isUpdate } = this.state

   const formItemLayout = {
     labelCol: {
       xs: { span: 5 },
       sm: { span: 6 }
     },
     wrapperCol: {
       xs: { span: 2 },
       sm: { span: 15 }
     }
   }

   return (

     <Card>
       <Button style={{ marginLeft: 20 }} type='primary' onClick={() => { this.onAddConfig() }}>添加常规任务配置</Button>
       <Text style={{ marginLeft: 20, color: 'red' }}>注意：活动id必须保证唯一，不能复用过期活动id，新活动时段需要新增配置 <br /> </Text>
       <Table style={{ marginTop: 10 }} rowKey='idx' pagination={this.defaultPageValue} columns={this.columns} dataSource={routineConfigList} />

       <Modal forceRender visible={visible} title={isUpdate ? '更新常规任务配置' : '添加常规任务配置'}
         onCancel={this.handleCancel} onOk={this.handleSubmit} okText='提交'>
         <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>

           <Form.Item label='活动id' name='id' rules={[{ required: true }]}>
             <Input disabled={isUpdate} placeholder='请输入活动id' />
           </Form.Item>
           <Form.Item label='活动标题' name='titleName' rules={[{ required: true }]}>
             <Input placeholder='请输入活动标题' />
           </Form.Item>

           <Form.Item label='常规礼物列表' name='propsIdList' rules={[{ required: true }]}>
             <TextArea rows={3} placeholder='常规礼物ID,一行一个id' />
           </Form.Item>

           <Form.Item label='开始时间' name='startTime' rules={[{ required: true }]}>
             <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' style={{ width: '100%' }} />
           </Form.Item>
           <Form.Item label='结束时间' name='endTime' rules={[{ required: true }]}>
             <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' style={{ width: '100%' }} />
           </Form.Item>

           <Form.Item label='主持人奖励轮次' name='round' rules={[{ required: true }]}>
             <InputNumber style={{ width: '100%' }} placeholder='奖励轮次' />
           </Form.Item>

           <Form.Item label='背景图' name='bgUrl' rules={[{ required: true }]}>
             <Input rows={12} placeholder='请输入背景图url' />
           </Form.Item>

         </Form>
       </Modal>
     </Card>
   )
 }
}

export default BrandActivityRoutine
