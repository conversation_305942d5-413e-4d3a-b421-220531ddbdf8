import {
  Card,
  Form,
  Table,
  Divider, Button, Input, DatePicker
} from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import exportExcel from '@/utils/exportExcel'
import moment from 'moment'
import { buildTingNum, buildTingNumExport, buildTingPrizeRule, buildTingPrizeRuleStrExport } from './common'

const namespace = 'guildTaskRevision'
const getListUri = `${namespace}/listAwardDetail`

@connect(({ awardDetail }) => ({
  model: awardDetail
}))

class AwardDetail extends Component {
  constructor (props) {
    super(props)

    const { dataInfo } = props
    // console.log('constructor dataInfo', dataInfo)
    this.state = { list: dataInfo, searchASID: 0, searchTaskMonth: '' }
  }

  componentDidMount () {
    const { dispatch } = this.props
    const { searchASID, searchTaskMonth } = this.state
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    let data = { asid: searchASID, month: searchMonthTmp }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  onClick = () => {
    const { dispatch } = this.props
    const { searchASID, searchTaskMonth } = this.state
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    let data = { asid: searchASID, month: searchMonthTmp }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  componentWillReceiveProps (nextProps) {
    const { dataInfo } = nextProps
    // console.log('componentWillReceiveProps', dataInfo)
    this.setState({ list: dataInfo })
  }

  state = {
    visible: false,
    isUpdate: false,
    isNotifyValue: true,
    searchInput: '',
    searchKeyword: '',
    channel: { key: -1 },
    gameType: { key: -1 }
  }

  tableInitValue = {}

  columns = [
    { title: '任务时间', width: 40, dataIndex: 'month' },
    { title: '短位ID', width: 50, dataIndex: 'asid' },
    { title: '公会任务礼物流水/元', width: 40, dataIndex: 'turnoverAmount' },
    { title: '优质厅数/个', width: 30, dataIndex: 'tingCount' },
    { title: '各流水规模优质厅数',
      dataIndex: 'tingIntervalList',
      render: (text, record) => {
        return buildTingNum(record.tingIntervalList !== null && record.tingIntervalList !== undefined ? record.tingIntervalList : [])
      }
    },
    { title: '流水任务最终发奖金额/元', width: 50, dataIndex: 'finallyTurnoverReward' },
    { title: '配置发奖规则', width: 50, dataIndex: 'configTurnoverRule', render: (v, item) => item.revenueRemark === '未确认发奖，默认按照系统结算' ? '' : v },
    { title: '最终发奖规则', width: 50, dataIndex: 'finallyTurnoverRule' },
    { title: '优质厅任务最终发奖金额/元', width: 50, dataIndex: 'finallyTingReward' },
    // { title: '配置发奖规则', width: 50, align: 'center', dataIndex: 'configTingRule', render: (v, item) => item.tingRemark === '未确认发奖，默认按照系统结算' ? '' : v },
    // { title: '最终发奖规则', width: 50, dataIndex: 'finallyTingRule' },
    { title: '配置发奖规则', width: 50, align: 'center', dataIndex: 'tingConfigTingRuleList', render: (v, item) => (buildTingPrizeRule(item.tingConfigTingRuleList !== null && item.tingConfigTingRuleList !== undefined ? item.tingConfigTingRuleList : [])) },
    { title: '最终发奖规则', width: 50, dataIndex: 'tingFinallyTingRuleList', render: (v, item) => (buildTingPrizeRule(item.tingFinallyTingRuleList !== null && item.tingFinallyTingRuleList !== undefined ? item.tingFinallyTingRuleList : [])) },
    { title: '合计最终发奖金额/元', width: 50, dataIndex: 'finallyTotalReward' },
    { title: '流水任务-备注', width: 50, dataIndex: 'revenueRemark' },
    { title: '优质厅任务-备注',
      width: 50,
      dataIndex: 'tingRemark',
      render: (text, record) => {
        if (record.tingRemark.length > 0) {
          let tingRemarks = record.tingRemark.split('|')
          let content = null
          for (let i = 0; i < tingRemarks.length; i++) {
            const element = tingRemarks[i]
            content = <span>{content}<div>{element}</div></span>
          }
          return content
        }
      }
    }
  ].map(item => {
    item.align = 'center'
    item.ellipsis = true
    return item
  })

  pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  getFilterList = () => {
    const { list } = this.state
    // const { searchImid } = this.state
    let filterList = list
    // if (searchImid && searchImid.length > 0) {
    //   filterList = filterList.filter((v) => { return v.imid === parseInt(searchImid) })
    // }
    console.log('getFilterList', list)
    return filterList
  }

  showModal = (isUpdate, record) => () => {
    if (record == null) {
      record = this.tableInitValue
    }
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(record)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate })
  }

  onExport = () => {
    let list = this.getFilterList()

    let exportData = list.map(item => {
      let tingIntervalList = item.tingIntervalList
      if (tingIntervalList) {
        delete item.tingIntervalList
        item['tingIntervalList'] = buildTingNumExport(tingIntervalList)
      }

      let tingConfigTingRuleList = item.tingConfigTingRuleList
      if (tingConfigTingRuleList) {
        delete item.tingConfigTingRuleList
        item['tingConfigTingRuleList'] = buildTingPrizeRuleStrExport(tingConfigTingRuleList)
      }

      let tingFinallyTingRuleList = item.tingFinallyTingRuleList
      if (tingFinallyTingRuleList) {
        delete item.tingFinallyTingRuleList
        item['tingFinallyTingRuleList'] = buildTingPrizeRuleStrExport(tingFinallyTingRuleList)
      }

      let v = $.extend(true, {}, item)
      return v
    })
    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.export === undefined || col.export) {
        exportHeader.push({ key: col.dataIndex, header: col.title })
      }
    })
    let fileName = '公会任务发奖明细-' + moment().format('YYYYMMDD') + '.xlsx'
    exportExcel(exportHeader, exportData, fileName)
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  render () {
    return (
      <Card>
        <Form>
          <Divider type='vertical' />
          任务时间：
          <DatePicker
            format='YYYY-MM'
            picker='month'
            placeholder='任务时间'
            onChange={(v) => this.setState({ searchTaskMonth: v })}
            style={{ width: 100, marginRight: 10 }}
          />
          短位ID：
          <Input style={{ width: 100, marginLeft: 0 }} placeholder='请输入' onChange={(e) => { this.setState({ 'searchASID': e.target.value }) }} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onExport}>导出</Button>
          <Divider type='vertical' />
          <Table style={{ marginTop: 10 }} dataSource={this.getFilterList()} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' scroll={{ x: 'max-content' }} />
        </Form>
      </Card>
    )
  }
}

export default AwardDetail
