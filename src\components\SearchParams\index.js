import React, { Component } from 'react'
import { Space, Form, message, Input, InputNumber, TimePicker, DatePicker, Button, Select } from 'antd'
import { SearchOutlined, FileDoneOutlined } from '@ant-design/icons'
import moment from 'moment'

/*
=============== 传参说明 =============
config=[]        // 搜索栏配置
onChange={v=>{}} // 必填, 参数改变时的回调 v:查询参数
formProps={}     // 传给搜索栏表单的属性,选填
onQuery={v=>{}}  // 点击查询按钮的回调
onExport={v=>{}}  // 点击导出按钮的回调

=============== config 配置参考 =============
    let config = [
      { label: '时间选择器_字符串', field: 'startTime', type: 'timePicker', valType: 'string', format: 'hh:mm:ss', toolTip: '提示语', defaultVal: '1669410367' },
      { label: '时间选择器_数字', field: 'startTimeStr', type: 'timePicker', valType: 'number', toolTip: '提示语', defaultVal: '1669410367' },
      { label: '时间范围_数字', field: 'timestamp1&timestamp2', type: 'timeRange', valType: 'number', defaultVal: '${now}&${now}' },
      { label: '时间范围_字符串', field: 'timeStr1&timeStr2', type: 'timeRange', format: 'h:m', valType: 'string', defaultVal: '1669392003&1669406706' },
      { label: '日期选择器_数字', field: 'startDate', type: 'datePicker', valType: 'string', format: 'YYYYMMDD', defaultVal: '1669410367' },
      { label: '日期选择器_字符串', field: 'startDateStr', type: 'datePicker', valType: 'number', showTime: true, defaultVal: '1669410367' },
      { label: '日期范围选择器_数字', field: 'date1&date2', type: 'dateRange', valType: 'number', format: 'YYYY-MM-DD HH:mm', showTime: true, defaultVal: '1669392003&1669406706' },
      { label: '日期范围选择器_字符串', field: 'dateStr1&dateStr2', type: 'dateRange', valType: 'string', format: 'YYYY-MM-DD', defaultVal: '1669392003&1669406706' },
      { label: '数字输入框', field: 'number', type: 'inputNumber', defaultVal: '123' },
      { label: '字符串输入框', field: 'string', type: 'input', defaultVal: 'keyword' },
      { label: '选项下拉框', field: 'select', type: 'select', defaultVal: '1', valType: 'number', options:'A=>1,b=>10,c=>30'  }
    ]

=============== 时间格式说明 =============
默认传unix时间戳, 组件需要两个值时使用符号&隔开, 当前时间用 ${now} 表示,
可以使用变量表示相对时间, 例如7天后可用 ${now+7days} 表示, 7天前可用 ${now-7days} 表示; 可用单位：[minutes|hours|days|weeks|months]
*/

class SimplePageParams extends Component {
    state = {
      params: {},
      dataList: []
    }
    componentDidMount = () => {
      const { config, onQuery, onChange } = this.props
      if (!onChange) {
        message.error('SearchParams 组件使用错误, onChange 参数必填～')
        return
      }
      if (!onQuery) {
        message.error('SearchParams 组件使用错误, onQuery 参数必填～')
        return
      }
      const fromValue = this.parseConfigToFromValue(config)
      this.formRef.setFieldsValue(fromValue)
      this.changeValue(fromValue)
      onQuery(this.parseValueToParams(fromValue))
    }

    changeValue = (v) => {
      const { onChange } = this.props
      this.setState({ params: this.parseValueToParams(v) })
      onChange(this.parseValueToParams(v))
    }

    // 解析时间
    parseTimeValue = (value) => {
      if (value === null) {
        return null
      }
      if (/^\d{10}$/.test(value)) {
        return moment.unix(parseInt(value))
      }
      if (/^\d{13}$/.test(value)) {
        return moment.unix(parseInt(value) / 1000)
      }
      // eslint-disable-next-line no-template-curly-in-string
      if (value === '${now}') {
        return moment()
      }

      const regexp = /^\$\{now(\+|-)(\d+)(minutes|hours|days|weeks|months)\}$/
      const matchResult = value.match(regexp)
      if (matchResult !== null && matchResult.length === 4) {
        const [, symble, count, unit] = matchResult
        if (symble === '-') {
          return moment().subtract(parseInt(count), unit)
        } else {
          return moment().add(parseInt(count), unit)
        }
      }

      return null
    }

    // 将配置解析到表单的值
    parseConfigToFromValue = (config) => {
      let fromValue = {}
      config.forEach((item) => {
        const { type, field, defaultVal = '' } = item
        if (type === 'timePicker' || type === 'datePicker') {
          fromValue[field] = this.parseTimeValue(defaultVal)
        }
        if (type === 'timeRange' || type === 'dateRange') {
          let valuePaire = defaultVal.split('&')
          let defaultRange = null
          if (valuePaire.length === 2) {
            defaultRange = [this.parseTimeValue(valuePaire[0]), this.parseTimeValue(valuePaire[1])]
          }
          fromValue[field] = defaultRange
        }
        if (type === 'input' || type === 'select') {
          fromValue[field] = defaultVal
        }
        if (type === 'inputNumber') {
          fromValue[field] = defaultVal !== null ? parseFloat(defaultVal) : null
        }
      })

      return fromValue
    }

    // 将表单的值转换到查询所需的结构
    parseValueToParams = (formVal) => {
      const { config } = this.props
      let params = { ...formVal }
      config.forEach(item => { // 对特殊类型的输入进行处理
        const { type, field, valType, format = 'YYYY-MM-DD HH:mm:ss' } = item
        const val = formVal[field]
        // 时间选择器
        if (type === 'timePicker' || type === 'datePicker') {
          if (valType === 'string') {
            params[field] = val ? val.format(format) : ''
          } else {
            params[field] = val ? val.unix() : 0
          }
        }
        // 时间范围选择器
        if (type === 'timeRange' || type === 'dateRange') {
          let filePaire = field.split('&')
          if (filePaire.length !== 2) {
            filePaire = [`${field}1`, `${field}2`]
          }
          if (valType === 'string') {
            params[filePaire[0]] = val ? val[0].format(format) : ''
            params[filePaire[1]] = val ? val[1].format(format) : ''
          } else {
            params[filePaire[0]] = val ? val[0].unix() : 0
            params[filePaire[1]] = val ? val[1].unix() : 0
          }
          delete params[field]
        }
        // 下拉框
        if (type === 'select') {
          if (valType === 'number') {
            params[field] = parseFloat(val)
          }
        }
      })

      return params
    }

    // 解析options配置,示例: a=>1,b=>2,c=>3
    parseOptionsConfig = (value) => {
      let options = []
      let items = value.split(',')
      options = items.map((item, index) => {
        let pair = item.split('=>')
        if (pair.length !== 2) {
          return
        }
        return { label: pair[0], value: pair[1], key: index }
      })
      return options
    }

    ButtonRender = (label, extProps) => {
      return (
        <Form.Item>
          <Button {...extProps}>{label}</Button>
        </Form.Item>
      )
    }

    // ==============================

    TimePickerRender = (name, label, ext) => {
      const {
        isRange = false,
        toolTip = null,
        format = 'hh:mm:ss'
      } = ext
      return (
        <Form.Item name={name} label={label} style={this.formStyle} tooltip={toolTip}>
          {
            isRange
              ? <TimePicker.RangePicker format={format} showNow />
              : <TimePicker format={format} />
          }
        </Form.Item>
      )
    }

    DatePickerRender = (name, label, ext) => {
      const {
        isRange = false,
        toolTip = null,
        format = 'YYYY-MM-DD HH:mm:ss',
        showTime = false
      } = ext

      return (
        <Form.Item name={name} label={label} style={this.formStyle} tooltip={toolTip}>
          {
            isRange
              ? <DatePicker.RangePicker format={format} showTime={showTime} />
              : <DatePicker format={format} showTime={showTime} />
          }
        </Form.Item>
      )
    }

    InputNumberRender = (name, label, ext) => {
      const { toolTip, placeholder, style } = ext
      return (
        <Form.Item name={name} label={label} style={this.formStyle} tooltip={toolTip}>
          <InputNumber placeholder={placeholder} style={style} />
        </Form.Item>
      )
    }

    InputRender = (name, label, ext) => {
      const { toolTip, placeholder, style } = ext
      return (
        <Form.Item name={name} label={label} style={this.formStyle} tooltip={toolTip}>
          <Input placeholder={placeholder} style={style} />
        </Form.Item>
      )
    }

    SelecterRender = (name, label, options, ext) => {
      const { toolTip } = ext
      return (
        <Form.Item name={name} label={label} style={this.formStyle} tooltip={toolTip}>
          <Select options={this.parseOptionsConfig(options)} />
        </Form.Item>
      )
    }

    render () {
      const { params } = this.state
      const { config, formProps, onQuery, onExport } = this.props

      return (
        <Form ref={(r) => { this.formRef = r }} onFinish={this.changeValue} onValuesChange={() => { this.formRef.submit() }} {...formProps} >
          <Space align='center' size='large'>
            {
              config.map(item => {
                const { type, field, label, toolTip, format, options, showTime, valType, placeholder, style } = item
                switch (type) {
                  case 'timePicker':
                    return this.TimePickerRender(field, label, { isRange: false, toolTip, format })
                  case 'timeRange':
                    return this.TimePickerRender(field, label, { isRange: true, toolTip, format })
                  case 'datePicker':
                    return this.DatePickerRender(field, label, { isRange: false, toolTip, format, showTime })
                  case 'dateRange':
                    return this.DatePickerRender(field, label, { isRange: true, toolTip, format, showTime })
                  case 'inputNumber':
                    return this.InputNumberRender(field, label, { toolTip, placeholder, style })
                  case 'input':
                    return this.InputRender(field, label, { toolTip, placeholder, style })
                  case 'select':
                    return this.SelecterRender(field, label, options, { toolTip, valType })
                }
                message.error(`使用方法错误,请检查代码: (SearchParams_${type})`)
                return ''
              })
            }
            {
              onQuery
                ? <Form.Item>
                  <Button onClick={() => onQuery(params)} type='primary' ><SearchOutlined />查询</Button>
                </Form.Item>
                : ''
            }
            {
              onExport
                ? <Form.Item type='submit'>
                  <Button onClick={() => onExport(params)} type='primary'><FileDoneOutlined />导出</Button>
                </Form.Item>
                : ''
            }
          </Space>
        </Form>
      )
    }
}

export default SimplePageParams
