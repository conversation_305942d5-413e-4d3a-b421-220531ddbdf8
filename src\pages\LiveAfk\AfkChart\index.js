import React, { Component } from 'react'
import { connect } from 'dva'
import { <PERSON><PERSON>, Card, Col, DatePicker, Row, Tabs } from 'antd'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Line } from '@ant-design/charts'
import { timeFormater } from '@/utils/common'
import moment from 'moment'

const namespace = 'afkChart'

@connect(({ afkChart }) => ({
  model: afkChart
}))

class AfkChart extends Component {
  constructor (props) {
    super(props)
    this.state = { begTime: parseInt(new Date().getTime() / 1000) - 7 * 24 * 60 * 60, endTime: parseInt(new Date().getTime() / 1000) }
    this.refreshAfkChartData()
  }

  refreshAfkChartData = () => {
    this.searchHandle()
  }

  // 处理查询事件
  searchHandle = () => {
    const { dispatch } = this.props
    const { begTime, endTime } = this.state
    dispatch({
      type: `${namespace}/getAfkChartData`,
      payload: { begTime: begTime, endTime: endTime }
    })
  }
  // 展示 报表
  displayAfkChart = () => {
    const { displayData } = this.props.model
    const data = displayData
    const config = {
      title: {
        visible: true,
        text: '多折线图'
      },
      description: {
        visible: true,
        text: '将数据按照某一字段进行分组\uFF0C用于比对不同类型数据的趋势\u3002'
      },
      padding: 'auto',
      forceFit: true,
      data,
      xField: 'date', // 分组字段
      yField: 'value', // 值字段
      seriesField: 'type', // 类型字段
      yAxis: {
        label: {
          formatter: (v) => {
            return `${v}`.replace(/\d{1,3}(?=(\d{3})+$)/g, (s) => `${s},`)
          }
        }
      },
      legend: { position: 'right-top' },
      responsive: true,
      // point: {
      //   visible: true,
      //   size: 2,
      //   shape: 'circle', // 'diamond',
      //   style: {
      //     fill: 'white',
      //     stroke: '#2593fc',
      //     lineWidth: 2
      //   }
      // },
      tooltip: {
        visible: true,
        shared: true,
        showCrosshairs: true,
        crosshairs: {
          type: 'y'
        },
        offset: 20,
        fields: ['value', 'type'],
        formatter: function (v) { // 自定义tooltip, v 参数就是一个对象，包含 fields 中指定的字段， 返回值的话，name表示左侧名称，value 表示对应值
          return { name: v.type, 'value': v.value }
        }
      }
      // 事件处理
      // onReady: (plot) => {
      //   console.log('Plot: ', plot)
      //   plot.on('line:mousemove', (evt) => {
      //     console.log('mousemove: ', evt)
      //   })
      // }
    }
    return <Line {...config} />
  }

  // 开始时间禁用
  disabledBegTime = (current) => {
    let now = new Date().getTime()
    if (now <= current._d.getTime()) {
      return true
    }
    const { endTime } = this.state
    // 填写了 endTime
    if (endTime > 0 && endTime * 1000 <= current._d.getTime()) {
      return true
    }
    return false
  }

  // 开始时间禁用
  disabledEndTime = (current) => {
    let now = new Date().getTime()
    if (now <= current._d.getTime()) {
      return true
    }
    const { begTime } = this.state
    // 填写了 begTime
    if (begTime > 0 && begTime * 1000 >= current._d.getTime()) {
      return true
    }
    return false
  }

  render () {
    const { TabPane } = Tabs
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='1' onChange={(record) => this.onTagChange(record)} type='card' size='large'>
            <TabPane tab='挂播报表' key='1'>
              <Row style={{ marginBottom: '1em' }}>
                <Col offset={0}>
                  <DatePicker
                    showTime
                    format='YYYY-MM-DD HH:mm'
                    placeholder='开始时间'
                    defaultValue={moment(timeFormater(parseInt(new Date().getTime() / 1000) - 7 * 24 * 60 * 60, 0), 'YYYY-MM-DD HH:mm')}
                    disabledDate={this.disabledBegTime}
                    onChange={(v) => { this.setState({ begTime: parseInt(v._d.getTime() / 1000) }) }}
                    style={{ marginLeft: 5 }}
                  />
                  <span style={{ marginLeft: 5 }}>~</span>
                  <DatePicker
                    showTime
                    format='YYYY-MM-DD HH:mm'
                    placeholder='结束时间'
                    defaultValue={moment(timeFormater(parseInt(new Date().getTime() / 1000), 0), 'YYYY-MM-DD HH:mm')}
                    disabledDate={this.disabledEndTime}
                    onChange={(v) => this.setState({ endTime: parseInt(v._d.getTime() / 1000) })}
                    style={{ marginLeft: 5 }}
                  />
                </Col>
                <Button style={{ marginLeft: 20 }} type='primary' onClick={e => this.searchHandle()}>查询</Button>
              </Row>

              {this.displayAfkChart()}
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default AfkChart
