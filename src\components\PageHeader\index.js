import React, { PureComponent } from 'react'
import { Tabs, Skeleton, Tooltip } from 'antd'
import classNames from 'classnames'
import styles from './index.module.less'
import BreadcrumbView from './breadcrumb'
import { QuestionCircleOutlined } from '@ant-design/icons'
import Modal from 'antd/lib/modal/Modal'
import Wiki from '@/pages/ServerDebug/Wiki/index.js'
const { TabPane } = Tabs
export default class PageHeader extends PureComponent {
  state = {
    helpModalVisable: false
  }
  onChange = key => {
    const { onTabChange } = this.props
    if (onTabChange) {
      onTabChange(key)
    }
  };

  genRemarkModel = (remark) => {
    if (!remark) {
      return ''
    }
    return (
      <span>
        <Tooltip title='查看使用帮助'>
          <QuestionCircleOutlined style={{ fontSize: 'medium', marginLeft: '1em' }} onClick={() => this.setState({ helpModalVisable: true })} />
        </Tooltip>
        <div>
          <Modal title='使用帮助' visible={this.state.helpModalVisable} onCancel={() => this.setState({ helpModalVisable: false })} okButtonProps={{ hidden: true }} cancelButtonProps={{ hidden: true }}>
            <pre>{remark}</pre>
          </Modal>
        </div>
      </span>
    )
  }

  render () {
    const {
      title,
      logo,
      action,
      content,
      extraContent,
      tabList,
      className,
      tabActiveKey,
      tabDefaultActiveKey,
      tabBarExtraContent,
      loading = false,
      wide = false,
      // remark = '',
      hiddenBreadcrumb = false
    } = this.props

    const clsString = classNames(styles.pageHeader, className)
    const activeKeyProps = {}
    if (tabDefaultActiveKey !== undefined) {
      activeKeyProps.defaultActiveKey = tabDefaultActiveKey
    }
    if (tabActiveKey !== undefined) {
      activeKeyProps.activeKey = tabActiveKey
    }
    return (
      <div className={clsString}>
        <div className={wide ? styles.wide : ''}>
          <Skeleton
            loading={loading}
            title={false}
            active
            paragraph={{ rows: 3 }}
            avatar={{ size: 'large', shape: 'circle' }}>

            {hiddenBreadcrumb ? null : <BreadcrumbView {...this.props} />} {/* 路径面包屑 */}

            <div className={styles.detail}>
              {logo && <div className={styles.logo}>{logo}</div>}
              <div className={styles.main}>

                <div className={styles.row}>
                  {title && <h1 className={styles.title}>{title}</h1>} {/* 大标题 & 使用帮助 */}

                  {action && <div className={styles.action}>{action}</div>}
                </div>

                <Wiki />  {/* Wiki文档 */}

                <div className={styles.row}>
                  {content && <div className={styles.content}>{content}</div>}
                  {extraContent && <div className={styles.extraContent}>{extraContent}</div>}
                </div>

              </div>
            </div>

            {tabList && tabList.length ? (
              <Tabs
                className={styles.tabs}
                {...activeKeyProps}
                onChange={this.onChange}
                tabBarExtraContent={tabBarExtraContent}
              >
                {tabList.map(item => (
                  <TabPane tab={item.tab} key={item.key} />
                ))}
              </Tabs>
            ) : null}

          </Skeleton>
        </div>
      </div>
    )
  }
}
