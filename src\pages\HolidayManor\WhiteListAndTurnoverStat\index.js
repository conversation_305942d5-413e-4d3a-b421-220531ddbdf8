﻿import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Button, Divider, Form, Card, Modal, Input, Popconfirm, InputNumber, Space, Tabs, DatePicker, message, Tooltip, Radio } from 'antd'
import { connect } from 'dva'
import exportExcel from '@/utils/exportExcel'

const namespace = 'holidayManor'
const defaultPageSize = 100
const FormItem = Form.Item
// const Option = Select.Option
var moment = require('moment')

// 业务类型

@connect(({ holidayManor }) => ({
  model: holidayManor
}))

class Index extends Component {
  // column structs.
  columns = [
    { title: '添加时间', dataIndex: 'createTime', key: 'createTime', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD HH:mm:ss') },
    { title: 'UID', dataIndex: 'uid', key: 'uid', align: 'center', width: 110 },
    { title: 'YY', dataIndex: 'imid', key: 'imid', align: 'center', width: 110 },
    { title: '用户昵称',
      dataIndex: 'nick',
      key: 'nick',
      align: 'center',
      ellipsis: true,
      render: (value, record) => {
        return (
          <Tooltip title={value}>
            <div className='ellipsis' style={{ float: 'left', maxWidth: '100%' }}
            > { value.substring(0, 10)}
            </div>
          </Tooltip>
        )
      } },

    { title: '微信配额（个/日）', dataIndex: 'numWeiXin', key: 'numWeiXin', align: 'left', width: 180, render: (_, record) => this.renderNumWeiXinConfig(record) },
    { title: '支付宝配额（个/日）', dataIndex: 'numAlipay', key: 'numAlipay', align: 'left', width: 180, render: (_, record) => this.renderNumAlipayConfig(record) },
    { title: '合计配额（个/日）', key: 'total', align: 'center', width: 100, render: (_, record) => (record.numWeiXin + record.numAlipay + record.numWeixinWap + record.numAlipayWap + record.numAlipayMinH5) },
    { title: '发放类型', dataIndex: 'issueType', key: 'issueType', align: 'center', render: text => (text === 2 ? '紫水晶券*1800' : '向日葵*18个') },
    {
      title: '生效时间', key: 'validTime', align: 'center', render: (_, record) => (moment.unix(record.startTime).format('YYYY-MM-DD') + '~' + moment.unix(record.endTime).format('YYYY-MM-DD'))
    },
    { title: '提交审批人', dataIndex: 'operator', key: 'operator', align: 'center' },
    { title: '活动链接', dataIndex: 'url', key: 'url', align: 'center' },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(record)}>修改</a><Divider type='vertical' />
          <Popconfirm title={'确认要删除?'} type='primary' onConfirm={this.handleDelOne(record)} okText='确认' cancelText='取消'>
            <a href=''>删除</a>
          </Popconfirm>
        </span>
      )
    }
  ]

  renderNumWeiXinConfig (record) {
    return (<div align='left'>
      微信Qrcode：{record.numWeiXin}<br />
      微信Wap：{record.numWeixinWap}
    </div>)
  }

  renderNumAlipayConfig (record) {
    return (<div align='left'>
      支付宝Qrcode：{record.numAlipay}<br />
      支付宝Wap：{record.numAlipayWap}<br />
      支付宝小程序：{record.numAlipayMinH5}
    </div>)
  }

  // column structs.
  statColumns = [
    { title: '日期', dataIndex: 'dt', key: 'dt', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD') },
    { title: 'UID', dataIndex: 'uid', key: 'uid', align: 'center' },
    { title: 'YY', dataIndex: 'imid', key: 'imid', align: 'center' },
    { title: '用户昵称',
      dataIndex: 'nick',
      key: 'nick',
      align: 'center',
      ellipsis: true,
      render: (value, record) => {
        return (
          <Tooltip title={value}>
            <div className='ellipsis' style={{ float: 'left', maxWidth: '100%' }}
            > { value.substring(0, 10)}
            </div>
          </Tooltip>
        )
      } },

    { title: '微信(包裹礼物)/个', dataIndex: 'weiXinTotal', key: 'weiXinTotal', align: 'center', render: (_, record) => this.renderNumWeiXinPackStats(record) },
    { title: '微信(紫水晶券)/个', dataIndex: 'weixinTotalVirt', key: 'weixinTotalVirt', align: 'center', render: (_, record) => this.renderNumAlipayPackStats(record) },
    { title: '支付宝(包裹礼物)/个）', dataIndex: 'alipayTotal', key: 'alipayTotal', align: 'center', render: (_, record) => this.renderNumWeiXinVirtStats(record) },
    { title: '支付宝(紫水晶券)/个）', dataIndex: 'alipayTotalVirt', key: 'alipayTotalVirt', align: 'center', render: (_, record) => this.renderNumAlipayVirtStats(record) },
    { title: '合计实际额度（个）', dataIndex: 'total', key: 'total', align: 'center' }
  ]

  renderNumWeiXinPackStats (record) {
    return (<div align='left'>
      微信Qrcode：{record.weixinQrcodeTotalPack}<br />
      微信Wap：{record.weixinWapTotalPack}
    </div>)
  }

  renderNumAlipayPackStats (record) {
    return (<div align='left'>
      支付宝Qrcode：{record.alipayQrcodeTotalPack}<br />
      支付宝Wap：{record.alipayWapTotalPack}<br />
      支付宝小程序：{record.alipayH5miniTotalPack}
    </div>)
  }

  renderNumWeiXinVirtStats (record) {
    return (<div align='left'>
      微信Qrcode：{record.weixinQrcodeTotalVirt}<br />
      微信Wap：{record.weixinWapTotalVirt}
    </div>)
  }

  renderNumAlipayVirtStats (record) {
    return (<div align='left'>
      支付宝Qrcode：{record.alipayQrcodeTotalVirt}<br />
      支付宝Wap：{record.alipayWapTotalVirt}<br />
      支付宝小程序：{record.alipayH5miniTotalVirt}
    </div>)
  }

  updateStatPagination = (page, pageSize, total) => {
    const { statPagination } = this.state
    statPagination.current = page || 1
    statPagination.pageSize = pageSize || defaultPageSize
    if (total !== undefined) {
      statPagination.total = total
    }
    console.log('total', total)
    this.setState({ statPagination: statPagination })
    return statPagination
  }

  updateWhitePagination = (page, pageSize, total) => {
    const { whitePagination } = this.state
    whitePagination.current = page || 1
    whitePagination.pageSize = pageSize || defaultPageSize
    if (total !== undefined) {
      whitePagination.total = total
    }
    console.log('total', total)
    this.setState({ whitePagination: whitePagination })
    return whitePagination
  }

  state = {
    visible: false,
    isUpdate: false,
    confirmVisible: false,
    deleteConfirmMsg: '',
    giftUseEstimate: '',
    numAlipay: 0,
    numAlipayWap: 0,
    numAlipayMinH5: 0,
    numWeiXin: 0,
    numWeixinWap: 0,
    value: {},
    statPagination: {
      pageSize: defaultPageSize,
      total: 0,
      current: 1,
      defaultCurrent: 1,
      pageSizeOptions: [100, 200, 500, 1000],
      showSizeChanger: true,
      onChange: (page, pageSize) => {
        this.statPageChange(page, pageSize)
      },
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    },
    whitePagination: {
      pageSize: defaultPageSize,
      total: 0,
      current: 1,
      defaultCurrent: 1,
      pageSizeOptions: [100, 200, 500, 1000],
      showSizeChanger: true,
      onChange: (page, pageSize) => {
        this.whitePageChange(page, pageSize)
      },
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    }
  }

  // show modal
  showModal = (record) => () => {
    var title = '修改'
    var opType = 1
    var isUpdate = false
    if (record == null) {
      title = '添加'
      isUpdate = false
      record = { opType: 1, issueType: 1, numAlipay: 0, numAlipayWap: 0, numAlipayMinH5: 0, numWeiXin: 0, numWeixinWap: 0 }
      this.setState({ numAlipay: 0, numAlipayWap: 0, numAlipayMinH5: 0 })
      this.setState({ numWeiXin: 0, numWeixinWap: 0 })
    } else {
      opType = 3
      isUpdate = true
    }
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.opType = opType
      if (isUpdate) {
        v.startDate = moment.unix(record.startTime)
        v.endDate = moment.unix(record.endTime)
        this.setState({ numAlipay: record.numAlipay })
        this.setState({ numAlipayWap: record.numAlipayWap })
        this.setState({ numAlipayMinH5: record.numAlipayMinH5 })
        this.setState({ numWeiXin: record.numWeiXin })
        this.setState({ numWeixinWap: record.numWeixinWap })
      }

      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, title: title, isUpdate: isUpdate })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // add and update
  onFinish = values => {
    const { dispatch } = this.props
    console.log('onFinish', values)
    let numWeiXin = parseInt(values.numWeiXin || 0, 10)
    let numAlipay = parseInt(values.numAlipay || 0, 10)
    let numWeixinWap = parseInt(values.numWeixinWap || 0, 10)
    let numAlipayWap = parseInt(values.numAlipayWap || 0, 10)
    let numAlipayMinH5 = parseInt(values.numAlipayMinH5 || 0, 10)
    if (numWeiXin <= 0 && numAlipay <= 0 &&
      numWeixinWap <= 0 && numAlipayWap <= 0 && numAlipayMinH5 <= 0) {
      message.error('至少要输入一个配额')
      return
    }

    if (values.opType === 1) {
      dispatch({
        type: `${namespace}/addItem`,
        payload: values
      })
    } else {
      dispatch({
        type: `${namespace}/updateItem`,
        payload: values
      })
    }

    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  handleDelOne = record => e => {
    const { dispatch } = this.props
    const data = { uid: record.uid }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    this.searchBy()
    this.setState({ start: moment.utc().subtract(7, 'days') })
    this.setState({ end: moment.utc() })
    this.setState({ startDate: moment.utc().subtract(7, 'days').format('YYYY-MM-DD') })
    this.setState({ endDate: moment.utc().format('YYYY-MM-DD') })
  }

  onSearchWhite = (value) => {
    this.searchBy()
  }

  searchBy = (page, pageSize) => {
    const { dispatch } = this.props
    page = page || 1
    pageSize = pageSize || defaultPageSize

    const query = { uid: this.state.whiteUid, page: page, pageSize: pageSize }
    console.log(query)
    dispatch({
      type: `${namespace}/getList`,
      payload: query,
      callback: (data) => {
        console.log('callback', data)
        this.updateWhitePagination(query.page || 1, query.pageSize || defaultPageSize, data ? data.total : 0)
      }
    })
  }

  onSearchStat = (value) => {
    this.searchStatBy()
  }

  searchStatBy = (page, pageSize) => {
    const { dispatch } = this.props
    page = page || 1
    pageSize = pageSize || defaultPageSize

    const query = { uid: this.state.statUid, startDate: this.state.startDate, endDate: this.state.endDate, page: page, pageSize: pageSize }

    console.log(query)
    if (query.uid === undefined && (query.startDate === undefined || query.endDate === undefined)) {
      message.error('请填写uid或日期范围查询')
      return
    }
    dispatch({
      type: `${namespace}/getStatList`,
      payload: query,
      callback: (data) => {
        console.log('callback', data)
        this.updateStatPagination(query.page || 1, query.pageSize || defaultPageSize, data ? data.total : 0)
      }
    })
  }

  // 分页信息变更
  statPageChange = (page, pageSize, total) => {
    let pagination = this.updateStatPagination(page, pageSize, total)
    this.searchStatBy(pagination.current, pagination.pageSize)
  }

  // 分页信息变更
  whitePageChange = (page, pageSize, total) => {
    let pagination = this.updateWhitePagination(page, pageSize, total)
    this.searchBy(pagination.current, pagination.pageSize)
  }

  handleGiftUseEstimate = (numAlipay, numWeiXin, numWeixinWap, numAlipayWap, numAlipayMinH5) => {
    var total = numAlipay + numWeiXin + numWeixinWap + numAlipayWap + numAlipayMinH5
    if (total <= 0) {
      return ''
    }
    return '相当于' + 2 * total + '元礼物流水'
  }

  handleNumChange = (field) => value => {
    this.setState({ [field]: value })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  // '假日庄园白名单配置'标签页html代码
  addWhiteListHtml = (list) => {
    // const { updating } = this.props.model
    // const { Option } = Select
    const { numAlipay, numWeiXin, numWeixinWap, numAlipayWap, numAlipayMinH5 } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 10 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 14 }
      }
    }
    return (
      <div>
        <Card>
          <Form>
            <Space>
              UID:
              <InputNumber min={1} style={{ width: 200 }} onChange={(e) => this.setState({ whiteUid: e })} />
              <Button type='primary' onClick={this.onSearchWhite}>查询</Button>
              <Button type='primary' onClick={this.showModal(null)}>配置白名单</Button>
            </Space>
            <Divider />
            <Table rowKey={(record, index) => index} dataSource={list} columns={this.columns} pagination={this.state.whitePagination} />
          </Form>
        </Card>

        <Modal visible={this.state.visible} title={this.state.title} onCancel={this.hideModal} onOk={this.handleSubmit} forceRender>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem label='opType' name='opType' rules={[{ required: true }]} hidden='true' >
              <Input readOnly={this.isUpdate} defaultValue='1' />
            </FormItem>
            <FormItem label='UID' name='uid' rules={[{ required: true }]} >
              <InputNumber disabled={this.state.isUpdate} precision={0} min={1} style={{ width: 200 }} />
            </FormItem>
            <FormItem label='生效开始时间' name='startDate' rules={[{ required: true }]} >
              <DatePicker format='YYYY-MM-DD'
                style={{ width: 200 }}
                placeholder='开始时间' />
            </FormItem>
            <FormItem label='生效结束时间' name='endDate' rules={[{ required: true }]} >
              <DatePicker format='YYYY-MM-DD'
                style={{ width: 200 }}
                placeholder='结束时间' />
            </FormItem>
            <FormItem label='支付宝Qrcode配额/个' name='numAlipay' >
              <InputNumber min={0} precision={0} style={{ width: 200 }} onChange={this.handleNumChange('numAlipay')} />
            </FormItem>
            <FormItem label='支付宝Wap配额/个' name='numAlipayWap' >
              <InputNumber min={0} precision={0} style={{ width: 200 }} onChange={this.handleNumChange('numAlipayWap')} />
            </FormItem>
            <FormItem label='支付宝小程序配额/个' name='numAlipayMinH5' >
              <InputNumber min={0} precision={0} style={{ width: 200 }} onChange={this.handleNumChange('numAlipayMinH5')} />
            </FormItem>
            <FormItem label='微信Qrcode配额/个' name='numWeiXin' >
              <InputNumber min={0} precision={0} style={{ width: 200 }} onChange={this.handleNumChange('numWeiXin')} />
            </FormItem>
            <FormItem label='微信Wap配额/个' name='numWeixinWap' >
              <InputNumber min={0} precision={0} style={{ width: 200 }} onChange={this.handleNumChange('numWeixinWap')} />
            </FormItem>
            <FormItem label='发放类型' name='issueType' >
              <Radio.Group>
                <Tooltip title={'1个礼包可获得向日葵*18个(价值1.8Y)'}>
                  <Radio value={1}>包裹礼物</Radio>
                </Tooltip>
                <Tooltip title={'1个礼包可获得紫水晶券*1800(价值1.8Y)'}>
                  <Radio value={2}>紫水晶券</Radio>
                </Tooltip>
              </Radio.Group>
            </FormItem>
            <label>{this.handleGiftUseEstimate(numAlipay, numWeiXin, numWeixinWap, numAlipayWap, numAlipayMinH5)}</label>
          </Form>
        </Modal>
      </div>
    )
  }

  onExport = () => {
    let headers = []
    this.statColumns.forEach(function (item) {
      if (item.dataIndex === 'weiXinTotal' || item.dataIndex === 'weixinTotalVirt' ||
        item.dataIndex === 'alipayTotal' || item.dataIndex === 'alipayTotalVirt'
      ) {
        return
      }
      headers.push({ key: item.dataIndex, header: item.title })
    })

    headers.push({ key: 'weixinQrcodeTotalPack', header: '微信Qrcode（包裹）个数' })
    headers.push({ key: 'weixinWapTotalPack', header: '微信Wap（包裹）个数' })
    headers.push({ key: 'alipayQrcodeTotalPack', header: '支付宝Qrcode（包裹）个数' })
    headers.push({ key: 'alipayWapTotalPack', header: '支付宝Wap（包裹）个数' })
    headers.push({ key: 'alipayH5miniTotalPack', header: '支付宝小程序（包裹）个数' })
    headers.push({ key: 'weixinQrcodeTotalVirt', header: '微信Qrcode（紫水晶券）个数' })
    headers.push({ key: 'weixinWapTotalVirt', header: '微信Wap（紫水晶券）个数' })
    headers.push({ key: 'alipayQrcodeTotalVirt', header: '支付宝Qrcode（紫水晶券）个数' })
    headers.push({ key: 'alipayWapTotalVirt', header: '支付宝Wap（紫水晶券）个数' })
    headers.push({ key: 'alipayH5miniTotalVirt', header: '支付宝小程序（紫水晶券）个数' })

    const { model: { statList } } = this.props
    var exportData = statList.map(item => {
      let v = $.extend(true, {}, item)
      v.dt = moment.unix(v.dt).format('YYYY-MM-DD')
      return v
    })

    exportExcel(headers, exportData)
  }

  // '统计'标签页html代码
  statHtml = (statList) => {
    // const { updating } = this.props.model
    // const { Option } = Select
    const { start, end } = this.state

    var disabledStartDate = (startValue) => {
      const endValue = this.state.end
      if (!startValue || !endValue) {
        return false
      }
      return startValue.valueOf() > endValue.valueOf() || endValue.diff(startValue, 'month') >= 6
    }

    var disabledEndDate = (endValue) => {
      const startValue = this.state.start
      if (!endValue || !startValue) {
        return false
      }
      return endValue.valueOf() <= startValue.valueOf() || endValue.diff(startValue, 'month') >= 6
    }

    return (
      <div>
        <Card>
          <Form>
            <Space>
              UID:
              <InputNumber min={1} style={{ width: 200 }} onChange={(e) => this.setState({ statUid: e })} />
              <DatePicker format='YYYY-MM-DD'
                style={{ width: '100%' }}
                placeholder='开始时间'
                defaultValue={start}
                disabledDate={disabledStartDate}
                onChange={(v, dateString) => this.setState({ start: v, startDate: dateString })} />
              <span>至</span>
              <DatePicker format='YYYY-MM-DD'
                style={{ width: '100%' }}
                placeholder='结束时间'
                defaultValue={end}
                disabledDate={disabledEndDate}
                onChange={(v, dateString) => this.setState({ end: v, endDate: dateString })} />
              <Button type='primary' onClick={this.onSearchStat}>查询</Button>
              <Button type='primary' onClick={this.onExport}>导出</Button>
            </Space>
            <Divider />
            <Table rowKey={(record, index) => index} dataSource={statList} columns={this.statColumns} pagination={this.state.statPagination} />
          </Form>
        </Card>
      </div>
    )
  }

  // 标签页发生切换
  onTagChange = (record) => {
    if (record === '1') { // 切换到'假日庄园白名单配置'
      this.initForm()
    }
    if (record === '2') { // 切换到'假日庄园流水统计'
      this.searchStatBy()
    }
  }

  // 清空输入表单
  initForm = () => {
    if (this.fromRef) {
      this.fromRef.resetFields()
    }
  }

  // content
  render () {
    const { TabPane } = Tabs
    const { route, model: { list, statList } } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='1' onChange={(record) => this.onTagChange(record)} type='card' size='large'>
            <TabPane tab='假日庄园白名单配置' key='1'>
              {this.addWhiteListHtml(list)}
            </TabPane>
            <TabPane tab='假日庄园流水统计' key='2'>
              {this.statHtml(statList)}
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default Index
