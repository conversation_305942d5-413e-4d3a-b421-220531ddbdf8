import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Checkbox, Col, Form, Input, Modal, Radio, Select, Table, message } from 'antd'

const Option = Select.Option
const namespace = 'appReviewConfig'

@connect(({ appReviewConfig }) => ({
  model: appReviewConfig
}))

// 当前配置
class AppReviewConfigList extends Component {
  state = {
    // list: configList,
    editItem: null,
    visible: false
  }

  componentDidMount = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getReviewList`
    })
  }

  showEditModal = (item) => {
    let editItem = Object.assign({}, item)
    if (this.formRefEdit) {
      this.formRefEdit.setFieldsValue(editItem)
    }
    this.setState({ visible: true, editItem: editItem })
  }

   isValidVersion = (input) => {
     // 检查是否包含空格
     if (/\s/.test(input)) {
       return false // 如果包含空格，直接返回 false
     }

     // 定义正则表达式，匹配版本号格式
     const versionRegex = /^\d+(\.\d+)+$/

     // 使用正则表达式进行匹配
     return versionRegex.test(input)
   }

  // 更新配置
  onEditFormSubmit = (values) => {
    console.log('onFinish values==>', values)
    if (values.hostVersion === undefined || !this.isValidVersion(values.hostVersion)) {
      message.error('版本号错误!')
      return
    }
    let data = { ...values }
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/updateReviewConfig`,
      payload: data
    })
    this.setState({ visible: false })
  }

  onEditModalOk = () => {
    console.log('onEditFormSubmit')
    this.formRefEdit.submit()
  }

  getPlatform = (val) => {
    switch (val) {
      case 3:
        return 'Android'
      case 4:
        return 'iOS'
      case 1:
        return 'PC'
      default:
        return '-'
    }
  }

  render () {
    const { visible, editItem } = this.state
    const { configList } = this.props.model
    const columns = [
      { title: '序号', dataIndex: 'index' },
      { title: 'app', dataIndex: 'hostName' },
      { title: '是否生效', dataIndex: 'filterOn', render: val => val > 0 ? '生效中' : '已失效' },
      { title: '平台', dataIndex: 'platform', render: val => this.getPlatform(val) },
      { title: '版本', dataIndex: 'hostVersion' },
      { title: 'app渠道来源',
        dataIndex: 'channelSource',
        render: channelSource => (Array.isArray(channelSource) ? channelSource.join(', ') : channelSource)
      },
      { title: '应用ID',
        dataIndex: 'ignoreEntryList',
        render: idList => (Array.isArray(idList) ? idList.join(', ') : idList) },
      {
        title: '操作',
        key: 'operation',
        render:
          (text, item) => (
            <span>
              <a style={{ marginRight: 4 }} size='small' type='primary'
                onClick={() => this.showEditModal(item)}>更新</a>
            </span>)
      }
    ].map(raw => {
      raw.align = 'center'
      return raw
    })

    const checkBoxOptions = [
      { label: 'hw', value: 'hw' },
      { label: 'vivo', value: 'vivo' },
      { label: 'oppo', value: 'oppo' },
      { label: '10001', value: '10001' },
      { label: 'xiaomi', value: 'xiaomi' }
    ]

    return (
      <Card>
        <Col span={24}>
          <Table columns={columns} rowKey={(record, index) => index}
            dataSource={configList} />
        </Col>

        <Modal visible={visible} forceRender title={'更新配置'}
          okText='确定' cancelText='取消'
          onOk={this.onEditModalOk}
          onFinish={this.onSubmit}
          onCancel={() => { this.setState({ visible: false }) }}
        >
          <Form labelCol={{ span: 4 }}
            ref={(form) => {
              if (!this.formRefEdit) {
                form.setFieldsValue(editItem)
              }
              this.formRefEdit = form
            }}
            onFinish={this.onEditFormSubmit}
          >

            <Form.Item hidden label='_id' name='_id' />
            <Form.Item style={{ width: '50em' }} label='app' name='hostName' >
              <Input disabled style={{ width: '35%' }} />
            </Form.Item>
            <Form.Item style={{ width: '50em' }} label='是否生效' name='filterOn' >
              <Radio.Group >
                <Radio value={1}>是</Radio>
                <Radio value={0}>否</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item style={{ width: '50em' }} label='平台' name='platform' >
              <Select disabled style={{ width: '35%' }}>
                <Option value={3}>iOS</Option>
                <Option value={4}>Android</Option>
                <Option value={4}>PC</Option>
              </Select>
            </Form.Item>
            <Form.Item style={{ width: '50em' }} label='版本' name='hostVersion' >
              <Input style={{ width: '35%' }} />
            </Form.Item>
            <Form.Item style={{ width: '50em' }} label='app渠道来源' name='channelSource' >
              <Checkbox.Group options={checkBoxOptions} />
            </Form.Item>
            <Form.Item style={{ width: '50em' }} label='功能id' name='ignoreEntryList' >
              <Input disabled style={{ width: '35%' }} />
            </Form.Item>
          </Form>

        </Modal>
      </Card>
    )
  }
}

export default AppReviewConfigList
