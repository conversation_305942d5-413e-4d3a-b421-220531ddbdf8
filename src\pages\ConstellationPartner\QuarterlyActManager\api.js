import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/constellation/get_quarterly_config_list?${stringify(params)}`)
}

export function add (params) {
  return request(`/constellation/add_quarterly_config_info?${stringify(params)}`)
}

export function remove (params) {
  return request(`/constellation/del_quarterly_config_info?${stringify(params)}`)
}

export function update (params) {
  return request(`/constellation/mod_quarterly_config_info?${stringify(params)}`)
}
