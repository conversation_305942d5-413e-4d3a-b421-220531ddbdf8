import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Table, Form, Button, Input, DatePicker, Modal, Select, Tabs, Divider, Row, Col, message } from 'antd'
import { connect } from 'dva'
import { exportExcel } from 'xlsx-oc'
import GuildInfoManageFile from '@/components/GuildInfoManageFile'
import GuildInfoManageImage from '@/components/GuildInfoManageImage'
import PopImage2 from '@/components/PopImage2'
import fetch from 'dva/fetch'

const namespace = 'guildInfoManage'
const getGuildInfoListUri = `${namespace}/getGuildInfoList`
const addContractItemUri = `${namespace}/addContractItem`
const updateContractItemUri = `${namespace}/updateContractItem`
const approveContractItemUri = `${namespace}/approveContractItem`
const getGuildContractListUri = `${namespace}/getGuildContractList`
const importGuildInfoManageButtonUri = `${namespace}/importGuildInfoManageButton`
const getUserApproveInfoUri = `${namespace}/getUserApproveInfo`
const getPrivateFileTokenItem = `${namespace}/getPrivateFileTokenItem`
const clearPrivateUrlList = `${namespace}/clearPrivateUrlList`

const Option = Select.Option
const { RangePicker } = DatePicker
const { TextArea } = Input

const { confirm } = Modal

var moment = require('moment')
var dateFormat = 'YYYY-MM-DD'

// const nodeApproveNoBegin = 1 // 节点审批未开始
const nodeApprovePass = 2 // 节点审批通过
const nodeApproveReject = 3 // 节点审批不通过

const addPage = 1
const updatePage = 2
const approvePage = 3

// const contractStatusNothing = '0' // 未签约
const contractStatusYes = '1' // 已签约
const contractStatusNo = '2' // 未签约

const superGuildYes = '1' // 是超级公会
const superGuildNo = '2' // 不是超级公会

const remitMannerTypePublic = '1' // 对公
const remitMannerTypePrivate = '2' // 对私

const contractNothing = '0' // 未签约
const contractExpire = '1' // 签约过期
const contractNotExpire = '2' // 签约未过期
// const contractNotContract = '3' // 签约未过期

const approveStatusNoBegin = 1 // 未开始
const approveStatusPass = 5 // 审批通过
const approveStatusReject = 6 // 审批不通过(打回)
const approveStatusOnGoing = 7 // 审批中(新)

// const nodeOperatorNoBegin = 1 // 节点未开始
// const nodeOperatorCurrent = 2 // 节点当前步骤
// const nodeOperatorOperated = 3 // 节点已操作

@connect(({ guildInfoManage }) => ({
  model: guildInfoManage
}))

class GuildInfoManage extends Component {
  constructor (props) {
    super(props)
    this.refreshGuildInfoList()
  }

  infoColumns = [
    { title: '长位ID', dataIndex: 'sid', width: 120, align: 'center', fixed: 'left' },
    {
      title: '短位ID',
      dataIndex: 'aSid',
      width: 120,
      align: 'center',
      fixed: 'left',
      render: (text, record) => {
        if (record.aSid > 0) {
          return record.aSid
        }
        return '-'
      }
    },
    {
      title: 'OW YY号',
      dataIndex: 'owYy',
      width: 150,
      align: 'center',
      fixed: 'left',
      render: (text, record) => {
        if (record.owYy > 0) {
          return record.owYy
        }
        return '-'
      }
    },
    {
      title: 'OW昵称',
      dataIndex: 'owNickName',
      width: 240,
      align: 'center',
      render: (text, record) => {
        if (record.owNickName !== '') {
          return record.owNickName
        }
        return '-'
      }
    },
    {
      title: '对公主持数',
      dataIndex: 'publicHostNumber',
      width: 200,
      align: 'center',
      render: (text, record) => {
        return record.publicHostNumber
      }
    },
    {
      title: '经营业务',
      dataIndex: 'business',
      width: 120,
      align: 'center',
      render: (text, record) => {
        switch (record.business) {
          case 1: return '交友'
        }
        return '-'
      }
    },
    {
      title: '签约主播数',
      dataIndex: 'contractAnchorNumber',
      width: 160,
      align: 'center',
      render: (text, record) => {
        if (record.contractAnchorNumber !== '') {
          return record.contractAnchorNumber
        }
        return '-'
      }
    },
    {
      title: '独家主播数',
      dataIndex: 'exclusiveAnchorNumber',
      width: 160,
      align: 'center',
      render: (text, record) => {
        if (record.exclusiveAnchorNumber !== '') {
          return record.exclusiveAnchorNumber
        }
        return '-'
      }
    },
    {
      title: '是否超级公会',
      dataIndex: 'isSuperGuild',
      width: 300,
      align: 'center',
      render: (text, record) => {
        if (record.isSuperGuild !== superGuildYes && record.isSuperGuild !== superGuildNo) {
          return record.isSuperGuild
        }
        switch (record.isSuperGuild) {
          case superGuildYes: return '是'
          case superGuildNo: return '否'
        }
        return '否'
      }
    },
    {
      title: '法人',
      dataIndex: 'legalPerson',
      width: 170,
      align: 'center',
      render: (text, record) => {
        if (record.legalPerson !== '') {
          return record.legalPerson
        }
        return '-'
      }
    },
    {
      title: '企业名称',
      dataIndex: 'companyName',
      width: 200,
      align: 'center',
      render: (text, record) => {
        if (record.companyName !== '') {
          return record.companyName
        }
        return '-'
      }
    },
    {
      title: '合约身份',
      dataIndex: 'contractIdentity',
      width: 200,
      align: 'center',
      render: (text, record) => {
        if (record.contractIdentity !== '') {
          return record.contractIdentity
        }
        return '-'
      }
    },
    {
      title: '打款结算身份',
      dataIndex: 'statusPaymentSettlement',
      width: 200,
      align: 'center',
      render: (text, record) => {
        if (record.statusPaymentSettlement !== '') {
          return record.statusPaymentSettlement
        }
        return '-'
      }
    },
    {
      title: '打款方式',
      dataIndex: 'remitManner',
      width: 150,
      align: 'center',
      render: (text, record) => {
        if (record.remitManner !== '0' && record.remitManner !== remitMannerTypePublic && record.remitManner !== remitMannerTypePrivate) {
          return record.remitManner
        }
        switch (record.remitManner) {
          case remitMannerTypePublic: return '对公'
          case remitMannerTypePrivate: return '对私'
        }
        return '对私'
      }
    },
    {
      title: '打款账户',
      dataIndex: 'remitAccount',
      width: 180,
      align: 'center',
      render: (text, record) => {
        if (record.remitAccount !== '') {
          return record.remitAccount
        }
        return '-'
      }
    },
    {
      title: '是否签约',
      dataIndex: 'contractStatus',
      width: 180,
      align: 'center',
      render: (text, record) => {
        if (record.contractStatus !== contractStatusYes && record.contractStatus !== contractStatusNo) {
          return record.contractStatus
        }
        switch (record.contractStatus) {
          case contractStatusYes: return '是'
          case contractStatusNo: return '否'
        }
        return '-'
      }
    },
    { title: '签约时间', dataIndex: 'contractUpdateTime', width: 250, align: 'center', render: (text, record) => { return record.contractUpdateStartTime > 0 ? moment(record.contractUpdateStartTime * 1000).format(dateFormat) + '-' + moment(record.contractUpdateEndTime * 1000).format(dateFormat) : '-' } },
    {
      title: '合约时长/年',
      dataIndex: 'contractDuration',
      width: 150,
      align: 'center',
      render: (text, record) => {
        if (record.contractDuration > 0) {
          return record.contractDuration
        }
        return '-'
      }
    },
    {
      title: '合约是否到期',
      dataIndex: 'isContractExpire',
      width: 200,
      align: 'center',
      render: (text, record) => {
        switch (record.isContractExpire) {
          case contractExpire: return '是'
          case contractNotExpire: return '否'
          case contractNothing: return '-'
          default:
            return record.isContractExpire
        }
      }
    },
    {
      title: '审批状态',
      dataIndex:
      'approveStatus',
      width: 120,
      align: 'center',
      render: (text, record) => {
        if (record.id === '') {
          return '-'
        }
        switch (record.approveStatus) {
          case approveStatusNoBegin: return '未开始'
          case approveStatusPass: return '审批通过'
          case approveStatusReject: return '审批不通过'
          case approveStatusOnGoing: return '审批中'
        }
        return '未开始'
      }
    },
    {
      title: '图片附件',
      key: 'operation1',
      width: 120,
      align: 'center',
      render: (record) => (
        record.idx !== 1 && record.imageUrlList !== null && record.imageUrlList.length > 0 && record.imageUrlList[0].url !== '' ? <span><a onClick={this.previewImage(record)}>预览</a><Divider type='vertical' /><a size='small' type='primary' onClick={this.downloadImage(record)}>下载</a></span> : '')
    },
    {
      title: '文件附件',
      key: 'operation2',
      width: 160,
      align: 'center',
      render: (text, record) => this.renderFile(text, record, 'infoPage')
    },
    { title: '操作',
      key: 'operation',
      align: 'center',
      width: 200,
      render: (text, record) => (
        record.idx === 1 ? '' : record.approveStatus === approveStatusNoBegin ? <div><Button size='small' type='primary' onClick={this.showModal(record, 1)}>新增签约信息</Button></div> : <div><Button size='small' type='primary' onClick={this.showModal(record, 2)}>修改</Button></div>)
    }
  ]

  contractColumns = [
    { title: '长位ID', dataIndex: 'sid', width: 120, fixed: 'left' },
    {
      title: '短位ID',
      dataIndex: 'aSid',
      width: 120,
      align: 'center',
      fixed: 'left',
      render: (text, record) => {
        if (record.aSid > 0) {
          return record.aSid
        }
        return '-'
      }
    },
    {
      title: 'OW YY号',
      dataIndex: 'owYy',
      width: 150,
      align: 'center',
      fixed: 'left',
      render: (text, record) => {
        if (record.owYy > 0) {
          return record.owYy
        }
        return '-'
      }
    },
    {
      title: 'OW昵称',
      dataIndex: 'owNickName',
      width: 200,
      align: 'center',
      render: (text, record) => {
        if (record.owNickName !== '') {
          return record.owNickName
        }
        return '-'
      }
    },
    {
      title: '对公主持数',
      dataIndex: 'publicHostNumber',
      width: 300,
      align: 'center',
      render: (text, record) => {
        return record.publicHostNumber
      }
    },
    {
      title: '经营业务',
      dataIndex: 'business',
      width: 120,
      align: 'center',
      render: (text, record) => {
        switch (record.business) {
          case 1: return '交友'
        }
        return '-'
      }
    },
    {
      title: '签约主播数',
      dataIndex: 'contractAnchorNumber',
      width: 150,
      align: 'center',
      render: (text, record) => {
        if (record.contractAnchorNumber !== '') {
          return record.contractAnchorNumber
        }
        return '-'
      }
    },
    {
      title: '独家主播数',
      dataIndex: 'exclusiveAnchorNumber',
      width: 150,
      align: 'center',
      render: (text, record) => {
        if (record.exclusiveAnchorNumber !== '') {
          return record.exclusiveAnchorNumber
        }
        return '-'
      }
    },
    {
      title: '是否超级公会',
      dataIndex: 'isSuperGuild',
      width: 300,
      align: 'center',
      render: (text, record) => {
        if (record.isSuperGuild !== superGuildYes && record.isSuperGuild !== superGuildNo) {
          return record.isSuperGuild
        }
        switch (record.isSuperGuild) {
          case superGuildYes: return '是'
          case superGuildNo: return '否'
        }
        return '否'
      }
    },
    {
      title: '法人',
      dataIndex: 'legalPerson',
      width: 170,
      align: 'center',
      render: (text, record) => {
        if (record.legalPerson !== '') {
          return record.legalPerson
        }
        return '-'
      }
    },
    {
      title: '企业名称',
      dataIndex: 'companyName',
      width: 200,
      align: 'center',
      render: (text, record) => {
        if (record.companyName !== '') {
          return record.companyName
        }
        return '-'
      }
    },
    {
      title: '合约身份',
      dataIndex: 'contractIdentity',
      width: 200,
      align: 'center',
      render: (text, record) => {
        if (record.contractIdentity !== '') {
          return record.contractIdentity
        }
        return '-'
      }
    },
    {
      title: '打款结算身份',
      dataIndex: 'statusPaymentSettlement',
      width: 200,
      align: 'center',
      render: (text, record) => {
        if (record.statusPaymentSettlement !== '') {
          return record.statusPaymentSettlement
        }
        return '-'
      }
    },
    {
      title: '打款方式',
      dataIndex: 'remitManner',
      width: 200,
      align: 'center',
      render: (text, record) => {
        if (record.remitManner !== remitMannerTypePublic && record.remitManner !== remitMannerTypePrivate) {
          return record.remitManner
        }
        switch (record.remitManner) {
          case remitMannerTypePublic: return '对公'
          case remitMannerTypePrivate: return '对私'
        }
        return '-'
      }
    },
    { title: '打款账户', dataIndex: 'remitAccount', width: 140, align: 'center' },
    { title: '签约时间', dataIndex: 'contractTime', width: 250, align: 'center', render: (text, record) => { return record.contractUpdateStartTime > 0 ? moment(record.contractUpdateStartTime * 1000).format(dateFormat) + '-' + moment(record.contractUpdateEndTime * 1000).format(dateFormat) : '-' } },
    {
      title: '合约时长/年',
      dataIndex: 'contractDuration',
      width: 150,
      align: 'center',
      render: (text, record) => {
        if (record.contractDuration > 0) {
          return record.contractDuration
        }
        return '-'
      }
    },
    {
      title: '合约是否到期',
      dataIndex: 'isContractExpire',
      width: 200,
      align: 'center',
      render: (text, record) => {
        if (record.isContractExpire !== contractNothing && record.isContractExpire !== contractExpire && record.isContractExpire !== contractNotExpire) {
          return record.isContractExpire
        }
        switch (record.isContractExpire) {
          case contractExpire: return '是'
          case contractNotExpire: return '否'
        }
        return '-'
      }
    },
    {
      title: '图片附件',
      key: 'operation1',
      width: 120,
      align: 'center',
      render: (record) => (
        record.imageUrlList === null ? '' : <span><a onClick={this.previewImage(record)}>预览</a><Divider type='vertical' /><a size='small' type='primary' onClick={this.downloadImage(record)}>下载</a></span>)
    },
    {
      title: '文件附件',
      key: 'operation2',
      width: 160,
      align: 'center',
      render: (text, record) => this.renderFile(text, record, 'contractPage')
    }
  ]

  defaultPageValue = {
    defaultPageSize: 10,
    pageSizeOptions: ['10', '20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = {
    visible: false,
    isUpdate: false,
    value: {},
    switchOpen: 0,
    rejectDesc: '',
    previewImageVisible: false,
    imageStep: 0,
    imageStepValue: '0 / 0',

    sid1: '',
    asid1: '',
    owYy1: '',
    statusPaymentSettlement1: '',
    remitManner1: 0,
    isSuperGuild1: 0,
    contractStatus1: 0,
    isContractExpire1: 0,
    approveStatus1: 0,
    contractUpdateStartTime1: '',
    contractUpdateEndTime1: '',

    sid2: '',
    asid2: '',
    owYy2: '',
    statusPaymentSettlement2: '',
    remitManner2: 0,
    isSuperGuild2: 0,
    contractStatus2: 0,
    isContractExpire2: 0,
    approveStatus2: 0,
    contractUpdateStartTime2: '',
    contractUpdateEndTime2: '',

    previewImage: '',
    previewVisible: false
  }

  renderFile (text, record, which) {
    if (which === 'infoPage') {
      if (record.idx === 1) {
        return ''
      }
    }

    if (record.fileUrlList !== null && record.fileUrlList.length > 0) {
      let content = null
      for (let i = 0; i < record.fileUrlList.length; i++) {
        if (record.fileUrlList[i].url === '') {
          continue
        }
        content = <div>{content}{<div><a type='primary' onClick={this.downLoadFile(record.fileUrlList[i].url, record.fileUrlList[i].name)}>附件{i + 1}：{record.fileUrlList[i].name}</a></div>}</div>
      }
      return <span>{content}</span>
    }
    return ''
  }

  // 标签页发生切换
  onTagChange = (record) => {
    if (record === '1') {
      this.refreshGuildInfoList()
    } else if (record === '2') {
      this.refreshGuildContractList()
    }
  }

  initForm = () => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
  }

  buildGuildInfoObject = (state) => {
    var obj = {
      sid: Number(state.sid1),
      aSid: Number(state.asid1),
      owYy: Number(state.owYy1),
      statusPaymentSettlement: state.statusPaymentSettlement1,
      remitManner: state.remitManner1,
      isSuperGuild: state.isSuperGuild1,
      contractStatus: state.contractStatus1,
      isContractExpire: state.isContractExpire1,
      approveStatus: state.approveStatus1,
      contractUpdateStartTime: state.contractUpdateStartTime1,
      contractUpdateEndTime: state.contractUpdateEndTime1
    }
    return obj
  }

  buildGuildContractObject = (state) => {
    var obj = {
      sid: Number(state.sid2),
      aSid: Number(state.asid2),
      owYy: Number(state.owYy2),
      statusPaymentSettlement: state.statusPaymentSettlement2,
      remitManner: state.remitManner2,
      isSuperGuild: state.isSuperGuild2,
      contractStatus: state.contractStatus2,
      isContractExpire: state.isContractExpire2,
      approveStatus: state.approveStatus2,
      contractUpdateStartTime: state.contractUpdateStartTime2,
      contractUpdateEndTime: state.contractUpdateEndTime2
    }
    return obj
  }

  // 获取公会信息列表
  refreshGuildInfoList = () => {
    var data = this.buildGuildInfoObject(this.state)
    this.props.dispatch({
      type: getGuildInfoListUri,
      payload: data
    })

    this.props.dispatch({
      type: getUserApproveInfoUri,
      payload: {}
    })
  }

  // 获取公会签约列表
  refreshGuildContractList = () => {
    var data = this.buildGuildContractObject(this.state)
    this.props.dispatch({
      type: getGuildContractListUri,
      payload: data
    })
  }

  previewImage = (record) => () => {
    const { dispatch } = this.props

    if (record !== undefined && record.imageUrlList !== null && record.imageUrlList.length > 0) {
      let count = 0
      // 获取私密链接
      let data = []
      for (let i = 0; i < record.imageUrlList.length; i++) {
        const element = record.imageUrlList[i]
        if (element.url === '') {
          continue
        }
        let urlTmp = this.getPrivateUrlFileName(element.url)
        urlTmp = decodeURIComponent(urlTmp)
        data.push(urlTmp)
        count++
      }
      dispatch({
        type: getPrivateFileTokenItem,
        payload: { fileName: data }
      })

      this.setState({ previewImageVisible: true })
      this.setState({ imageStepValue: '1 / ' + count })
    }
  }

  downloadImage = (record) => () => {
    if (record !== null && record.imageUrlList !== null && record.imageUrlList.length > 0) {
      // 获取私密链接
      let data = []
      for (let i = 0; i < record.imageUrlList.length; i++) {
        const element = record.imageUrlList[i]
        if (element.url === '') {
          continue
        }
        let urlTmp = this.getPrivateUrlFileName(element.url)
        urlTmp = decodeURIComponent(urlTmp)
        data.push(urlTmp)
      }
      const { dispatch } = this.props
      dispatch({
        type: getPrivateFileTokenItem,
        payload: { fileName: data }
      })

      // 延时1s下载， 等待获取到私密链接
      let downloadFunc = (ttl) => {
        if (ttl < 0) {
          console.error('下载失败')
          return
        }
        const { model: { privateUrlList } } = this.props
        if (privateUrlList !== null && privateUrlList.length > 0) {
          for (var i = 0; i < privateUrlList.length; i++) {
            // 确认已更新state
            let filename = this.getPrivateUrlFileName(privateUrlList[i])
            let urlTmp = privateUrlList[i].replace('http://', 'https://')
            fetch(urlTmp).then(res => res.blob()).then(blob => {
              var a = document.createElement('a')
              var url = window.URL.createObjectURL(blob)
              a.href = url
              a.download = filename.replace(/\+/g, ' ')
              a.click()
              window.URL.revokeObjectURL(url)
            })
          }
          // clear私密链接缓存
          dispatch({
            type: clearPrivateUrlList
          })
        }
      }
      setTimeout(() => { downloadFunc(3) }, 400)
    }
  }

  downLoadFile = (urlT, name) => () => {
    let data = []
    let filename = this.getPrivateUrlFileName(urlT)
    filename = decodeURIComponent(filename)
    data.push(filename)

    const { dispatch } = this.props
    dispatch({
      type: getPrivateFileTokenItem,
      payload: { fileName: data }
    })

    // 延时1s下载， 等待获取到私密链接
    let downloadFunc = (ttl) => {
      if (ttl < 0) {
        console.error('下载失败')
        return
      }
      const { model: { privateUrlList } } = this.props

      if (privateUrlList !== null && privateUrlList.length > 0) {
        for (var i = 0; i < privateUrlList.length; i++) {
          let filename = this.getSrcFileName(privateUrlList[i])
          let urlTmp = privateUrlList[i].replace('http://', 'https://')
          fetch(urlTmp).then(res => res.blob()).then(blob => {
            var a = document.createElement('a')
            var url = window.URL.createObjectURL(blob)
            a.href = url
            a.download = filename.replace(/\+/g, ' ')
            a.click()
            window.URL.revokeObjectURL(url)
          })
        }
        // clear私密链接缓存
        dispatch({
          type: clearPrivateUrlList
        })
      }
    }
    setTimeout(() => { downloadFunc(3) }, 400)
  }

  handleImportGuildInfo = () => {
    this.setState({ isImportVisible: true })
  }

  onExportInfo = () => {
    let headers = []
    let columns = this.infoColumns
    const { exportKey } = this.state
    columns.forEach(function (item) {
      headers.push({ k: item.dataIndex, v: item.title })
    })

    exportExcel(headers, exportKey)
  }

  rowSelectionInfo = {
    onChange: (selectedRowKeys, selectedRows) => {
      var value = selectedRows.map(item => item.Sid).join(',')
      this.setState({ removeKey: value })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name
    })
  }

  onExportContract = () => {
    let headers = []
    let columns = this.contractColumns
    const { exportKey } = this.state
    columns.forEach(function (item) {
      headers.push({ k: item.dataIndex, v: item.title })
    })

    exportExcel(headers, exportKey)
  }

  rowSelectionContract = {
    onChange: (selectedRowKeys, selectedRows) => {
      var value = selectedRows.map(item => item.Sid).join(',')
      this.setState({ removeKey: value })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name
    })
  }

  getTimeStamp = (tComent) => {
    if (tComent === null) {
      return 0
    }
    return new Date(tComent.format(dateFormat)).getTime() / 1000 - 8 * 3600
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  onChangeInfoDataRange = (dateRange) => {
    if (dateRange == null) {
      this.onChange('contractUpdateStartTime1', '')
      this.onChange('contractUpdateEndTime1', '')
      return
    }
    if (dateRange[0] != null) {
      this.onChange('contractUpdateStartTime1', this.getTimeStamp(dateRange[0]))
    } else {
      message.error('其实时间不能为空')
      return
    }
    if (dateRange[1] != null) {
      this.onChange('contractUpdateEndTime1', this.getTimeStamp(dateRange[1]))
    }
  }

  onChangeContractDataRange = (dateRange) => {
    if (dateRange == null) {
      this.onChange('contractUpdateStartTime2', '')
      this.onChange('contractUpdateEndTime2', '')
      return
    }
    if (dateRange[0] != null) {
      this.onChange('contractUpdateStartTime2', this.getTimeStamp(dateRange[0]))
    } else {
      message.error('其实时间不能为空')
      return
    }
    if (dateRange[1] != null) {
      this.onChange('contractUpdateEndTime2', this.getTimeStamp(dateRange[1]))
    }
  }

  showModal = (record, val) => () => {
    // 获取图片私密链接
    // 审批页面只读判断
    var readOnly = false
    if (this.formRef) {
      if (record.approveStatus === approveStatusNoBegin || record.approveStatus === approveStatusReject || record.approveStatus === approveStatusPass) {
        readOnly = false
      } else {
        readOnly = true
      }
    }

    // 审批通过且未过期, 不能修改签约时间
    var readOnlyContractTime = false
    if ((val === addPage || val === updatePage) && record.approveStatus === approveStatusPass && record.isContractExpire !== contractExpire) {
      readOnlyContractTime = true
    }

    this.formRef.resetFields()
    if (this.formRef && val === addPage) {
      this.formRef.setFieldsValue({ id: record.id, sid: record.sid, proposerNick: record.proposerNick, proposerDw: record.proposerDw })
    } else if (this.formRef) {
      // 公司/工作室/个人 xxx公司/xxx工作室/xxx个人 切分
      var index = 0
      var payment = ''
      var settlement = ''
      if (record.statusPaymentSettlement.lastIndexOf('公司') !== -1) {
        index = record.statusPaymentSettlement.lastIndexOf('公司')
        settlement = record.statusPaymentSettlement.slice(index)
        payment = record.statusPaymentSettlement.slice(0, index)
      } else if (record.statusPaymentSettlement.lastIndexOf('工作室') !== -1) {
        index = record.statusPaymentSettlement.lastIndexOf('工作室')
        settlement = record.statusPaymentSettlement.slice(index)
        payment = record.statusPaymentSettlement.slice(0, index)
      } else if (record.statusPaymentSettlement.lastIndexOf('个人') !== -1) {
        index = record.statusPaymentSettlement.lastIndexOf('个人')
        settlement = record.statusPaymentSettlement.slice(index)
        payment = record.statusPaymentSettlement.slice(0, index)
      }
      this.formRef.setFieldsValue({ image1: record.image1, image2: record.image2, image3: record.image3, image4: record.image4, image5: record.image5, file1: record.file1, file2: record.file2, id: record.id, sid: record.sid, proposerNick: record.proposerNick, proposerDw: record.proposerDw, legalPerson: record.legalPerson, companyName: record.companyName, statusPaymentSettlement: { payment: payment, settlement: settlement }, remitAccount: record.remitAccount, contractDuration: record.contractDuration, approvePersonList: record.approvePersonList })
      if (record.contractUpdateStartTime !== 0 || record.contractUpdateEndTime !== 0) {
        this.formRef.setFieldsValue({ contractTime: [moment(record.contractUpdateStartTime * 1000), moment(record.contractUpdateEndTime * 1000)] })
      }

      // 打开页面渲染
      console.log(record)
      this.setState({ image1Name: record.image1Name })
      this.setState({ image2Name: record.image2Name })
      this.setState({ image3Name: record.image3Name })
      this.setState({ image4Name: record.image4Name })
      this.setState({ image5Name: record.image5Name })
      this.setState({ file1Name: record.file1Name })
      this.setState({ file2Name: record.file2Name })
    }

    // 标题
    var title = ''
    var hiddenBottonTmp = true
    if (val === addPage) {
      title = '公会签约信息'
    } else if (val === updatePage) {
      title = '公会签约信息'
    } else if (val === approvePage) {
      title = '公会签约信息入库审核'
      hiddenBottonTmp = false
    }

    this.setState({ value: record, readOnly: readOnly, visible: true, title: title, switchOpen: val, hiddenBotton: hiddenBottonTmp, readOnlyContractTime: readOnlyContractTime })
  }

  textAreaChange = e => {
    this.setState({ rejectDesc: e.target.value })
  }

  // 打回意见弹窗确认
  handleOkTextArea = () => {
    const { value, rejectDesc } = this.state

    if (rejectDesc === '') {
      message.warn('打回信息必填')
      return
    }

    var data = { id: value.id, approveStatus: nodeApproveReject, rejectDesc: rejectDesc }
    this.props.dispatch({
      type: approveContractItemUri,
      payload: data
    })
    this.props.dispatch({
      type: getUserApproveInfoUri,
      payload: {}
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
    this.setState({ isTextAreaVisible: false })
    this.setState({ rejectDesc: '' })
  }

  // 打回意见弹窗取消
  handleCancelTextArea = () => {
    this.setState({ rejectDesc: '' })
    this.setState({ isTextAreaVisible: false })
  }

  // 新增/修改取消二次弹窗提示-确认
  addUpdateInfoOkHandle = () => {
    const { switchOpen } = this.state
    if (switchOpen === approvePage) {
      return
    }
    this.formRef.resetFields()
    this.setState({ visible: false })
    this.setState({ isCancelModelVisible: false })

    this.setState({ image1Name: '' })
    this.setState({ image2Name: '' })
    this.setState({ image3Name: '' })
    this.setState({ image4Name: '' })
    this.setState({ image5Name: '' })
    this.setState({ file1Name: '' })
    this.setState({ file2Name: '' })
  }

  // 新增/修改取消二次弹窗提示-取消
  addUpdateInfoCancelHandle = () => {
    const { switchOpen } = this.state
    if (switchOpen === approvePage) {
      return
    }
    this.setState({ isCancelModelVisible: false })
  }

  // 确认导入长位id
  handleOkImport = () => {
    const { importSidData } = this.state
    const { dispatch } = this.props

    var sidList = importSidData.split('\n')
    if (!Array.isArray(sidList)) {
      message.error('导入数据格式不对')
      return
    }

    var sidListNum = []
    for (let i = 0; i < sidList.length; i++) {
      if (sidList[i] === '' || isNaN(Number(sidList[i]))) {
        message.error('格式错误 位置' + (i + 1))
        return
      }
      sidListNum[i] = Number(sidList[i])
    }

    var count = sidListNum.length
    confirm({
      content: '一共有' + count.toString() + '个长位ID',
      onOk () {
        var data = { sids: sidListNum }
        dispatch({
          type: importGuildInfoManageButtonUri,
          payload: data
        })
      }
    })
    this.setState({ importSidData: '' })
    this.setState({ isImportVisible: false })
  }

  // 取消导入
  handleCancelImport = () => {
    this.setState({ importSidData: '' })
    this.setState({ isImportVisible: false })
  }

  handleCancelPreviewImage = () => {
    this.setState({ previewImageVisible: false, imageStep: 0 })
  }

  importTextChange = e => {
    this.setState({ importSidData: e.target.value })
  }

  handleCancel = e => {
    const { switchOpen } = this.state
    if (switchOpen === approvePage) {
      this.formRef.resetFields()
      this.setState({ visible: false })
      return
    }
    this.setState({ isCancelModelVisible: true })
  }

  handleSubmit = e => {
    const { value, switchOpen } = this.state
    if (switchOpen === addPage || switchOpen === updatePage) {
      if (value.approveStatus === approveStatusOnGoing) {
        message.warn('审批中, 不可保存提交')
        return
      }
    }

    this.formRef.submit()
    this.setState({ visible: false })
  }

  onFinish = values => {
    const { switchOpen, canaddUpdate } = this.state
    const { dispatch } = this.props
    var uri = ''
    var data = {}
    if (switchOpen === approvePage) {
      return
    }

    if (!canaddUpdate) {
      return
    }

    if (switchOpen === updatePage) {
      uri = updateContractItemUri
    } else if (switchOpen === addPage) {
      uri = addContractItemUri
    }

    const { image1Name, image2Name, image3Name, image4Name, image5Name, file1Name, file2Name } = this.state

    var imageUrlList = []
    if (values.image1 !== undefined && values.image1 !== null) {
      imageUrlList.push({ url: values.image1, name: image1Name })
    }
    if (values.image2 !== undefined && values.image2 !== null) {
      imageUrlList.push({ url: values.image2, name: image2Name })
    }
    if (values.image3 !== undefined && values.image3 !== null) {
      imageUrlList.push({ url: values.image3, name: image3Name })
    }
    if (values.image4 !== undefined && values.image4 !== null) {
      imageUrlList.push({ url: values.image4, name: image4Name })
    }
    if (values.image5 !== undefined && values.image5 !== null) {
      imageUrlList.push({ url: values.image5, name: image5Name })
    }

    var fileUrlList = []
    if (values.file1 !== undefined && values.file1 !== null) {
      fileUrlList.push({ url: values.file1, name: file1Name })
    }
    if (values.file2 !== undefined && values.file2 !== null) {
      fileUrlList.push({ url: values.file2, name: file2Name })
    }

    data = { id: values.id, legalPerson: values.legalPerson, companyName: values.companyName, statusPaymentSettlement: values.statusPaymentSettlement.payment + values.statusPaymentSettlement.settlement, remitAccount: values.remitAccount.toString(), contractUpdateStartTime: Date.parse(values.contractTime[0]).valueOf() / 1000, contractUpdateEndTime: Date.parse(values.contractTime[1]).valueOf() / 1000, contractDuration: values.contractDuration, imageUrlList: imageUrlList, fileUrlList: fileUrlList }

    dispatch({
      type: uri,
      payload: data
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  addUpdateCheckHandler = e => {
    const { value, switchOpen } = this.state
    if (switchOpen === addPage || switchOpen === updatePage) {
      if (value.approveStatus === approveStatusOnGoing) {
        this.setState({ canaddUpdate: false })
        message.warn('审批中, 不可保存提交')
      } else {
        this.setState({ canaddUpdate: true })
      }
    }
  }

  addUpdateCancelHandler = () => {
    const { switchOpen } = this.state
    if (switchOpen === approvePage) {
      this.formRef.resetFields()
      this.setState({ visible: false })
      return
    }
    this.setState({ isCancelModelVisible: true })
  }

  approveHandler = () => {
    const { switchOpen, value } = this.state
    const { dispatch } = this.props
    if (switchOpen !== approvePage) {
      return
    }

    if (value.approveStatus === approveStatusPass || value.approveStatus === approveStatusReject) {
      message.warn('请修改提交再审核')
      return
    }

    var data = { id: value.id, approveStatus: nodeApprovePass, publicHostNumber: Number(value.publicHostNumber), contractAnchorNumber: Number(value.contractAnchorNumber), exclusiveAnchorNumber: Number(value.exclusiveAnchorNumber), isSuperGuild: Number(value.isSuperGuild) }
    dispatch({
      type: approveContractItemUri,
      payload: data
    })
    dispatch({
      type: getUserApproveInfoUri,
      payload: {}
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 新增/修改/审批弹窗-取消
  cancelHandle = () => {
    const { value, switchOpen } = this.state
    if (switchOpen !== approvePage) {
      return
    }

    if (value.approveStatus === approveStatusPass || value.approveStatus === approveStatusReject) {
      message.warn('请修改提交再审核')
      return
    }

    this.setState({ isTextAreaVisible: true })
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  // 搜索公会信息
  handleSearchInfo = () => {
    this.refreshGuildInfoList()
  }

  // 搜索公会信息
  handleSearchContract = () => {
    this.refreshGuildContractList()
  }

  okText = () => {
    const { switchOpen } = this.state
    var text = ''
    if (switchOpen === addPage || switchOpen === updatePage) {
      text = '保存并提交审批'
    } else if (switchOpen === approvePage) {
      text = '确认'
    }
    return text
  }

  cancelText = () => {
    const { switchOpen } = this.state
    var text = ''
    if (switchOpen === addPage || switchOpen === updatePage) {
      text = '取消'
    } else if (switchOpen === approvePage) {
      text = '取消'
    }
    return text
  }

  curPreviewImage = (privateUrlList) => {
    if (privateUrlList !== null && privateUrlList !== undefined && privateUrlList.length > 0) {
      const { imageStep } = this.state
      return privateUrlList[imageStep]
    }
  }

  displayImageNext = () => {
    const { imageStep } = this.state
    const { model: { privateUrlList } } = this.props
    var imageStepTmp = imageStep

    if (privateUrlList !== undefined && privateUrlList !== null && privateUrlList.length > 0) {
      // 上
      console.log('上')
      if (imageStepTmp >= privateUrlList.length) {
        this.setState({ imageStepValue: privateUrlList.length + 1 + '/' + privateUrlList.length })
        return privateUrlList[privateUrlList.length - 1].url
      }

      imageStepTmp++
      if (imageStepTmp >= privateUrlList.length) {
        imageStepTmp = privateUrlList.length - 1
      }
      this.setState({ imageStep: imageStepTmp })
      this.setState({ imageStepValue: imageStepTmp + 1 + ' / ' + privateUrlList.length })
    }
  }

  displayImagePre = () => {
    const { imageStep } = this.state
    const { model: { privateUrlList } } = this.props
    var imageStepTmp = imageStep

    if (privateUrlList !== undefined && privateUrlList !== null && privateUrlList.length > 0) {
      // 下
      console.log('下')
      if (imageStepTmp < 0) {
        this.setState({ imageStepValue: 1 + '/' + privateUrlList.length })
        return privateUrlList[0].url
      }

      imageStepTmp--
      if (imageStepTmp < 0) {
        imageStepTmp = 0
      }
      this.setState({ imageStep: imageStepTmp })
      this.setState({ imageStepValue: imageStepTmp + 1 + ' / ' + privateUrlList.length })
    }
  }

  // 获取url原始文件名 http://xxxxx/xxxxx_时间戳_md5.png?token=xxx    xxxxx.png
  getSrcFileName = (url) => {
    if (url === undefined || url === '') {
      return ''
    }
    var urlTmp = url

    urlTmp = urlTmp.substring(0, urlTmp.lastIndexOf('?token'))
    let suffix = urlTmp.substring(urlTmp.lastIndexOf('.'))
    urlTmp = urlTmp.substring(urlTmp.lastIndexOf('/') + 1)
    urlTmp = urlTmp.substring(0, urlTmp.lastIndexOf('_'))
    var filename = urlTmp.substring(0, urlTmp.lastIndexOf('_'))
    return decodeURIComponent(filename.replace(/\+/g, ' ')) + suffix
  }

  // 获取私密链接文件名 http://xxxxx/xxxxx_时间戳_md5.png?token=xxx    xxxxx_时间戳_md5.png
  getPrivateUrlFileName = (url) => {
    if (url === '' || url === undefined) {
      return ''
    }
    let start = String(url).lastIndexOf('/')
    let end = String(url).lastIndexOf('?token')
    return String(url).substring(start + 1, end)
  }

  render () {
    const { TabPane } = Tabs
    const { route, model: { list, privateUrlList } } = this.props
    const { image1Name, image2Name, image3Name, image4Name, image5Name, file1Name, file2Name, imageStepValue, previewImageVisible, readOnly, visible, title, hiddenBotton, isCancelModelVisible, isImportVisible, readOnlyContractTime } = this.state

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 2 },
        sm: { span: 10 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='1' onChange={(record) => this.onTagChange(record)} type='card' size='large'>
            <TabPane tab='公会信息' key='1'>
              <span style={{ marginLeft: 5 }}>长位ID</span>
              <Input min={0} placeholder='请输入' onChange={e => this.setState({ sid1: e.target.value })} style={{ width: 100, marginLeft: 5 }} />

              <span style={{ marginLeft: 15 }}>短位ID</span>
              <Input min={0} placeholder='请输入' onChange={e => this.setState({ asid1: e.target.value })} style={{ width: 100, marginLeft: 5 }} />

              <span style={{ marginLeft: 15 }}>OW YY号</span>
              <Input min={0} placeholder='请输入' onChange={e => this.setState({ owYy1: e.target.value })} style={{ width: 100, marginLeft: 5 }} />

              <span style={{ marginLeft: 15 }}>打款结算身份</span>
              <Select defaultValue={''} onChange={(v) => this.setState({ statusPaymentSettlement1: v })} style={{ width: 100, marginLeft: 10 }}>
                <Option value={''}>全部</Option>
                <Option value={'工作室'}>工作室</Option>
                <Option value={'公司'}>公司</Option>
                <Option value={'个人'}>个人</Option>
              </Select>

              <span style={{ marginLeft: 15 }}>打款方式</span>
              <Select defaultValue={0} onChange={(v) => this.setState({ remitManner1: v })} style={{ width: 100, marginLeft: 10 }}>
                <Option value={0}>全部</Option>
                <Option value={1}>对公</Option>
                <Option value={2}>对私</Option>
              </Select>

              <span style={{ marginLeft: 15 }}>是否超级公会</span>
              <Select defaultValue={0} onChange={(v) => this.setState({ isSuperGuild1: v })} style={{ width: 100, marginLeft: 10 }}>
                <Option value={0}>全部</Option>
                <Option value={1}>是</Option>
                <Option value={2}>否</Option>
              </Select>

              <span style={{ marginLeft: 15 }}>是否签约</span>
              <Select defaultValue={0} onChange={(v) => this.setState({ contractStatus1: v })} style={{ width: 100, marginLeft: 10 }}>
                <Option value={0}>全部</Option>
                <Option value={1}>是</Option>
                <Option value={2}>否</Option>
              </Select>

              <span style={{ marginLeft: 15 }}>合约是否到期</span>
              <Select defaultValue={0} onChange={(v) => this.setState({ isContractExpire1: v })} style={{ width: 100, marginLeft: 10 }}>
                <Option value={0}>全部</Option>
                <Option value={1}>过期</Option>
                <Option value={2}>未过期</Option>
                <Option value={3}>未签约</Option>
              </Select>
              <br />
              签约时间
              <RangePicker allowEmpty={['false', 'true']} style={{ marginLeft: 5, marginTop: 5 }} format={'YYYY-MM-DD'} onChange={this.onChangeInfoDataRange} />

              <span style={{ marginLeft: 15 }}>审批状态</span>
              <Select defaultValue={0} onChange={(v) => this.setState({ approveStatus1: v })} style={{ width: 100, marginLeft: 10 }}>
                <Option value={0}>全部</Option>
                <Option value={1}>未开始</Option>
                <Option value={2}>审批中</Option>
                <Option value={5}>审批通过</Option>
                <Option value={6}>审批不通过</Option>
              </Select>

              <Button style={{ marginLeft: 30 }} type='primary' onClick={this.handleSearchInfo}>查询</Button>
              <Button style={{ marginLeft: 30 }} type='primary' onClick={this.handleImportGuildInfo}>导入长位ID数据</Button>
              <Form>
                <Table style={{ marginTop: 15 }} dataSource={list} bordered columns={this.infoColumns} size='small' pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
              </Form>
            </TabPane>

            <TabPane tab='签约记录' key='2'>
              <span style={{ marginLeft: 5 }}>长位ID</span>
              <Input placeholder='请输入' onChange={e => this.setState({ sid2: e.target.value })} style={{ width: 100, marginLeft: 5 }} />

              <span style={{ marginLeft: 15 }}>短位ID</span>
              <Input placeholder='请输入' onChange={e => this.setState({ asid2: e.target.value })} style={{ width: 100, marginLeft: 5 }} />

              <span style={{ marginLeft: 15 }}>OW YY号</span>
              <Input placeholder='请输入' onChange={e => this.setState({ owYy2: e.target.value })} style={{ width: 100, marginLeft: 5 }} />

              <span style={{ marginLeft: 15 }}>打款结算身份</span>
              <Select defaultValue={''} onChange={(v) => this.setState({ statusPaymentSettlement2: v })} style={{ width: 100, marginLeft: 10 }}>
                <Option value={''}>全部</Option>
                <Option value={'工作室'}>工作室</Option>
                <Option value={'公司'}>公司</Option>
                <Option value={'个人'}>个人</Option>
              </Select>

              <span style={{ marginLeft: 15 }}>打款方式</span>
              <Select defaultValue={0} onChange={(v) => this.setState({ remitManner2: v })} style={{ width: 100, marginLeft: 10 }}>
                <Option value={0}>全部</Option>
                <Option value={1}>对公</Option>
                <Option value={2}>对私</Option>
              </Select>

              <span style={{ marginLeft: 15 }}>合约是否到期</span>
              <Select defaultValue={0} onChange={(v) => this.setState({ isContractExpire2: v })} style={{ width: 100, marginLeft: 10 }}>
                <Option value={0}>全部</Option>
                <Option value={1}>过期</Option>
                <Option value={2}>未过期</Option>
              </Select>

              <span style={{ marginLeft: 15 }}>签约时间</span>
              <RangePicker allowEmpty={['false', 'true']} style={{ marginLeft: 10 }} format={'YYYY-MM-DD'} onChange={this.onChangeContractDataRange} />

              <Button style={{ marginLeft: 30 }} type='primary' onClick={this.handleSearchContract}>查询</Button>
              <Form>
                <Table dataSource={list} columns={this.contractColumns} size='small' pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
              </Form>
            </TabPane>
          </Tabs>
        </Card>

        <Modal forceRender footer={null} width={1000} visible={visible} title={title} onCancel={this.handleCancel} onOk={this.handleSubmit} okText={this.okText()} cancelText={this.cancelText()}>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <Form.Item name='id' hidden>
              <Input hidden />
            </Form.Item>
            <span><font style={{ fontSize: '20px' }}>申请人信息</font></span>
            <Row style={{ marginTop: 5 }}>
              <Col span={12}>
                <Form.Item label='申请人:' name='proposerNick'>
                  <Input disabled='true' />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label='账号:' name='proposerDw'>
                  <Input disabled='true' />
                </Form.Item>
              </Col>
            </Row>
            <Divider />

            <span><font style={{ fontSize: '20px' }}>签约信息</font></span>
            <Form.Item label='长位ID' name='sid'>
              <Input disabled='true' />
            </Form.Item>
            <Form.Item label='法人' name='legalPerson' rules={[{ required: true, message: '请输入名称' }]}>
              <Input disabled={readOnly} autoComplete='off' />
            </Form.Item>

            <Form.Item label='企业名称' name='companyName' rules={[{ required: true, message: '请输入企业名称' }]}>
              <Input disabled={readOnly} autoComplete='off' />
            </Form.Item>

            <Form.Item label='打款结算身份'>
              <Input.Group compact>
                <Form.Item name={['statusPaymentSettlement', 'payment']} noStyle rules={[{ required: true, message: '必须填入结算身份' }]}>
                  <Input disabled={readOnly} autoComplete='off' style={{ width: '64.5%' }} placeholder='请输入结算身份' />
                </Form.Item>
                <Form.Item name={['statusPaymentSettlement', 'settlement']} noStyle rules={[{ required: true, message: '必须选择身份' }]}>
                  <Select disabled={readOnly} placeholder='请选择身份'>
                    <Option value={'公司'}>公司</Option>
                    <Option value={'工作室'}>工作室</Option>
                    <Option value={'个人'}>个人</Option>
                  </Select>
                </Form.Item>
              </Input.Group>
            </Form.Item>

            <Form.Item label='打款账户' name='remitAccount' rules={[{ required: true, pattern: new RegExp(/^[1-9]\d*$/, 'g'), message: '请输入正确的打款账户' }]}>
              <Input disabled={readOnly} autoComplete='off' maxLength='30' />
            </Form.Item>

            <Form.Item label='签约时间' name='contractTime' rules={[{ required: true }]}>
              <RangePicker format={'YYYY-MM-DD'} showTime={{ format: 'YYYY-MM-DD' }} style={{ width: 300 }} disabled={readOnly || readOnlyContractTime} />
            </Form.Item>

            <Form.Item label='合约时长/年' name='contractDuration' rules={[{ required: true, message: '选择时长' }]}>
              <Select onChange={(v) => this.setState({ contractDuration: v })} style={{ width: 100 }} disabled={readOnly} >
                <Option value={3}>3</Option>
                <Option value={5}>5</Option>
              </Select>
            </Form.Item>
            <Divider />
            <div style={{ marginLeft: 150 }}>
              <div><b>上传图片附件</b></div>
              <Row>
                <Col span='6'>
                  <Form.Item label='图片1' name='image1'>
                    <GuildInfoManageImage onChange={(v) => v ? this.setState({ image1Name: this.getSrcFileName(v) }) : this.setState({ image1Name: '' })} />
                  </Form.Item>
                </Col>
                <Col style={{ marginTop: 5 }}>
                  文件名:  <b>{image1Name}</b>
                </Col>
              </Row>
              <Row>
                <Col span='6'>
                  <Form.Item label='图片2' name='image2'>
                    <GuildInfoManageImage onChange={(v) => v ? this.setState({ image2Name: this.getSrcFileName(v) }) : this.setState({ image2Name: '' })} />
                  </Form.Item>
                </Col>
                <Col style={{ marginTop: 5 }}>
                  文件名:  <b>{image2Name}</b>
                </Col>
              </Row>
              <Row>
                <Col span='6'>
                  <Form.Item label='图片3' name='image3'>
                    <GuildInfoManageImage onChange={(v) => v ? this.setState({ image3Name: this.getSrcFileName(v) }) : this.setState({ image3Name: '' })} />
                  </Form.Item>
                </Col>
                <Col style={{ marginTop: 5 }}>
                  文件名:  <b>{image3Name}</b>
                </Col>
              </Row>
              <Row>
                <Col span='6'>
                  <Form.Item label='图片4' name='image4'>
                    <GuildInfoManageImage onChange={(v) => v ? this.setState({ image4Name: this.getSrcFileName(v) }) : this.setState({ image4Name: '' })} />
                  </Form.Item>
                </Col>
                <Col style={{ marginTop: 5 }}>
                  文件名:  <b>{image4Name}</b>
                </Col>
              </Row>
              <Row>
                <Col span='6'>
                  <Form.Item label='图片5' name='image5'>
                    <GuildInfoManageImage onChange={(v) => v ? this.setState({ image5Name: this.getSrcFileName(v) }) : this.setState({ image5Name: '' })} />
                  </Form.Item>
                </Col>
                <Col style={{ marginTop: 5 }}>
                  文件名:  <b>{image5Name}</b>
                </Col>
              </Row>
            </div>
            <Divider />
            <div style={{ marginLeft: 150 }}>
              <div><b>上传文件附件</b></div>
              <Row>
                <Col span='6'>
                  <Form.Item label='文件1' name='file1'>
                    <GuildInfoManageFile onChange={(v) => v ? this.setState({ file1Name: this.getSrcFileName(v) }) : this.setState({ file1Name: '' })} />
                  </Form.Item>
                </Col>
                <Col style={{ marginTop: 5 }}>
                  文件名:  <b>{file1Name}</b>
                </Col>
              </Row>
              <Row>
                <Col span='6'>
                  <Form.Item label='文件2' name='file2'>
                    <GuildInfoManageFile onChange={(v) => v ? this.setState({ file2Name: this.getSrcFileName(v) }) : this.setState({ file2Name: '' })} />
                  </Form.Item>
                </Col>
                <Col style={{ marginTop: 5 }}>
                  文件名:  <b>{file2Name}</b>
                </Col>
              </Row>
            </div>
            <Divider />
            <div>
              <font style={{ marginLeft: 150, width: 300 }}><b>附件支持上传：</b></font>
            </div>
            <div>
              <font style={{ marginLeft: 150, width: 300 }}>1、文件：支持pdf格式，数量2个，单个最大2M</font>
            </div>
            <div>
              <font style={{ marginLeft: 150, width: 300 }}>2、图片：支持JPG/PNG格式，数量5张，单个最大7M</font>
            </div>
            <Divider />
            <Button hidden={!hiddenBotton} size='large' style={{ marginLeft: 220 }} type='primary' htmlType='submit' onClick={this.addUpdateCheckHandler}>
              保存并提交审批
            </Button>
            <Button hidden={!hiddenBotton} size='large' style={{ marginLeft: 15 }} type='button' onClick={this.addUpdateCancelHandler}>
              取消
            </Button>
          </Form>
        </Modal>

        <Modal title='确认取消' visible={isCancelModelVisible} onOk={this.addUpdateInfoOkHandle} onCancel={this.addUpdateInfoCancelHandle}>
          <p>当前信息不保存，是否退出</p>
        </Modal>

        <Modal forceRender title='导入长位ID' visible={isImportVisible} onOk={this.handleOkImport} onCancel={this.handleCancelImport}>
          <Form.Item>
            <TextArea allowClear rows={10} style={{ width: 470 }} placeholder='每一个长位ID为一行' onChange={this.importTextChange} />
          </Form.Item>
        </Modal>

        <Modal align='center' centered='true' footer={null} title='预览' width={440} visible={previewImageVisible} onCancel={this.handleCancelPreviewImage}>
          <PopImage2 value={this.curPreviewImage(privateUrlList)} />
          <div>
            <b style={{ alignContent: 'center', marginLeft: 170, marginTop: 40 }}>{imageStepValue}</b>
          </div>
          <div>
            <Button size='middle' style={{ marginLeft: 130, marginTop: 20 }} onClick={this.displayImagePre}>上一张</Button>
            <Button size='middle' style={{ marginLeft: 10, marginTop: 20 }} onClick={this.displayImageNext}>下一张</Button>
          </div>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default GuildInfoManage
