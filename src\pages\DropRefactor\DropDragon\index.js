import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, message } from 'antd'
// import DropDragonFriendComponent from './components/friend'
import DropDragonBabyComponent from './components/baby'
import { connect } from 'dva'
import DropDragonZWComponent from './components/zw'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import { isVoiceRoomPath } from '../dropCommon'

const namespace = 'dropDragon'

@connect(({ dropDragon }) => ({
  model: dropDragon
}))

class DropDragon extends Component { // 默认页面组件，不需要修改
  state = { }

  componentDidMount () {
    this.getAllPrize()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 获取所有奖励列表
  getAllPrize = () => {
    this.callModel('getAllPrizeList', {
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('获取奖励列表失败: msg=' + msg)
        }
      }
    })
  }

  render () {
    const { route } = this.props
    const isVoiceRoom = isVoiceRoomPath(route.path)

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs id='ddrg' type='card' defaultActiveKey={isVoiceRoom ? '3' : '2'} >
            {
              isVoiceRoom
                ? <TabPane tab='追玩医疗包配置' key='3'>
                  <DropDragonZWComponent />
                </TabPane>
                : <>
                  {/* <TabPane tab='交友医疗包配置' key='1'>
                    <DropDragonFriendComponent />
                  </TabPane> */}
                  <TabPane tab='宝贝医疗包配置' key='2'>
                    <DropDragonBabyComponent />
                  </TabPane>
                </>
            }
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default DropDragon // 保证唯一
