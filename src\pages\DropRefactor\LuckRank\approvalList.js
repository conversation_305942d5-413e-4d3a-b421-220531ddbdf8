import React, { Component } from 'react'
import { connect } from 'dva'
import { Col, Row, Table, Typography } from 'antd'
import ApprovalButton from '@/components/ApprovalButton'
import { actTypeOptions, rewardFormater, approvalFormater, timeRangeFormater } from './common.js'
const namespace = 'dropLuckRank'
const { Text, Link } = Typography
@connect(({ dropLuckRank }) => ({
  model: dropLuckRank
}))

class LuckRankApproval extends Component {
  state = {}

  componentDidMount = () => {
    this.callModel('getApprovalList')
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 审批状态格式化
  aprStatusFormater = (r) => {
    const { aprInfo: { status } } = r
    if (status === 'OnGoing') {
      return <Text type='warning' >待审批</Text>
    }
    if (status === 'Passed') {
      return '已通过'
    }
    if (status === 'Rejected') {
      return <Text type='danger'>已驳回</Text>
    }
    if (status === 'Abort') {
      return '已取回'
    }
    return '???'
  }

  render () {
    const { approvalList } = this.props.model

    const columns = [
      { title: '活动ID', dataIndex: 'id' },
      { title: '活动周期', dataIndex: 'actType', render: (v, r) => { return actTypeOptions.find(item => item.value === v)?.label } },
      { title: '活动时间', dataIndex: '', render: (v, r) => { return timeRangeFormater(r, r.autoWeek) } },
      { title: '道具配置', dataIndex: 'rewards', render: (v, r) => { return rewardFormater(r) } },
      { title: '状态', dataIndex: 'rewards', render: (v, r) => { return this.aprStatusFormater(r) } },
      { title: '审批流', dataIndex: '', render: (v, r) => { return approvalFormater(r) } },
      { title: '操作', dataIndex: 'aprInfo', render: (v, r) => { return <ApprovalButton aprId={v.aprId}><Link disabled={v.status !== 'OnGoing'} >审批</Link></ApprovalButton> } }
    ].map(item => { item.align = 'center'; return item })

    return (
      <Row>
        <Col span={24}>
          <Table columns={columns} dataSource={approvalList} />
        </Col>
      </Row>
    )
  }
}

export default LuckRankApproval
