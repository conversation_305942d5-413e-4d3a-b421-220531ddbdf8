import request from '@/utils/request'
import { stringify } from 'qs'

// 获取配置列表
export function getGuildNotifyList (params) {
  return request(`/fts_hgame/guild_notify/list_guild_notify?${stringify(params)}`)
}

// 添加配置
export function addGuildNotify (params) {
  return request(`/fts_hgame/guild_notify/add_guild_notify`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 更新配置
export function updateGuildNotify (params) {
  return request(`/fts_hgame/guild_notify/update_guild_notify`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 删除配置
export function deleteGuildNotify (params) {
  return request(`/fts_hgame/guild_notify/delete_guild_notify`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}
