import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/bigcustumer/boss/privilege_list?${stringify(params)}`)
}

export function add (params) {
  return request(`/bigcustumer/boss/privilege_add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function update (params) {
  return request(`/bigcustumer/boss/privilege_update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

// export function remove (params) {
//   return request(`/bigcustumer/boss/del`, {
//     method: 'GET',
//     headers: {
//       'Content-Type': 'application/json;charset=UTF-8'
//     },
//     body: JSON.stringify(params)
//   })
// }

export function remove (params) {
  return request(`/bigcustumer/boss/privilege_del?${stringify(params)}`)
}
