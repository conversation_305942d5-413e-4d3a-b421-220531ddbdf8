import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists () {
  return request('/AcvitityModuleWhiteList/GetList')
}

export function add (params) {
  return request(`/AcvitityModuleWhiteList/Add?${stringify(params)}`)
}

export function remove (params) {
  return request(`/AcvitityModuleWhiteList/Remove?${stringify(params)}`)
}

export function update (params) {
  return request(`/AcvitityModuleWhiteList/Update?${stringify(params)}`)
}
