import {
  Card,
  Form,
  Table,
  Input, Modal, Button, message, Select, InputNumber, Tooltip
} from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import {
  prizeRuleMap,
  prizeRuleNone,
  approvaMap,
  reviewMap,
  prizeRuleList,
  prizeRuleSystem, prizeRuleDown,
  yuanToWan
} from './common'
import { deepClone } from '../../../../utils/common'
import moment from 'moment/moment'
import exportExcel from '@/utils/exportExcel'

const { TextArea } = Input
const Option = Select.Option

const namespace = 'hatMonthTaskv3'
const getListUri = `${namespace}/listRewardConfirm`

@connect(({ rewardConfirm }) => ({
  model: rewardConfirm
}))

class RewardConfirm extends Component {
  constructor (props) {
    super(props)

    const { dataInfo, canAllConfirm, guildSum } = props

    const rewardConfirmTitleInfo = { year: 0, month: 0, guildSum: 0, reachTing: 0, sealSum: 0, guildGiftSum: 0, reward: 0, settlementReward: 0, adjustReward: 0 }
    this.state = { list: dataInfo, canAllConfirm: canAllConfirm, guildSum: guildSum, searchASID: 0, rewardConfirmTitleInfo: rewardConfirmTitleInfo }
  }

  componentDidMount () {
    const { dispatch } = this.props
    const { searchASID } = this.state
    let data = { asid: searchASID }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  onClick = () => {
    const { dispatch } = this.props
    const { searchASID } = this.state
    let data = { asid: searchASID }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  componentWillReceiveProps (nextProps) {
    const { dataInfo, canAllConfirm, guildSum } = nextProps
    this.setState({ list: dataInfo, canAllConfirm: canAllConfirm, guildSum: guildSum })
  }

  columns = [
    { title: '任务时间', dataIndex: 'month', fixed: 'left' },
    { title: '实际生效asid', dataIndex: 'guildAsid', fixed: 'left' },
    { title: '经营asid', dataIndex: 'asid', fixed: 'left' },
    { title: '签约asid', dataIndex: 'contractAsid', fixed: 'left' },
    { title: '厅ssid', dataIndex: 'ssid', fixed: 'left' },
    { title: '厅名', width: 50, dataIndex: 'tingName', fixed: 'left', render: (v, r) => (r.tingName.length > 20 ? <Tooltip title={r.tingName}>{ r.tingName.substring(0, 20) + '...'}</Tooltip> : r.tingName) },
    { title: '厅盖章流水/元', dataIndex: 'tingSealAmount', align: 'center', render: (text, record) => (record.tingSealAmount.toLocaleString()) },
    // { title: '公会礼物流水/元', dataIndex: 'guildGiftAmount', align: 'center', render: (text, record) => (record.guildGiftAmount.toLocaleString()) },

    { title: '厅礼物流水/元', dataIndex: 'tingGiftAmount' },
    { title: '有效运营天数', dataIndex: 'operateDay' },
    { title: '玩法流水占比', dataIndex: 'gameAmountProportion', render: (text, record) => (record.tingGameAmountProportion ? record.tingGameAmountProportion + '%' : '') },
    { title: '达标阶段', dataIndex: 'reachStep' },
    { title: '[预结算]奖励金额/元', dataIndex: 'settlementReward' },
    { title: '发奖规则', dataIndex: 'awardRule', render: (text, record) => (prizeRuleMap[record.awardRule]) },
    { title: '[调整后]奖励金额/元', dataIndex: 'adjustReward', render: (text, record) => (record.awardRule === prizeRuleNone ? '' : record.adjustReward) },
    { title: '运营备注', dataIndex: 'reviewRemark' },
    { title: '复核状态', dataIndex: 'reviewStatus', align: 'center', render: (text, record) => (reviewMap[record.reviewStatus]) },
    { title: '审核状态', dataIndex: 'approvalStatus', render: (text, record) => (approvaMap[record.approvalStatus]) },
    { title: '驳回原因', dataIndex: 'approvalRemark' },
    { title: '操作人', dataIndex: 'optUser', render: (text, record) => (record.optUser === 0 ? '' : record.optUser) }
    // { title: '操作时间', width: 50, dataIndex: 'optTime', render: (text, record) => (dateString(record.timestamp)) }
  ].map(item => {
    item.align = 'center'
    item.ellipsis = true
    return item
  })

  columnsConfirm = [
    { title: '任务时间', dataIndex: 'month', fixed: 'left' },
    { title: '实际生效asid', dataIndex: 'guildAsid', fixed: 'left' },
    { title: '经营asid', dataIndex: 'asid', fixed: 'left' },
    { title: '签约asid', dataIndex: 'contractAsid', fixed: 'left' },
    { title: '厅ssid', dataIndex: 'ssid', fixed: 'left' },
    { title: '厅名', width: 50, dataIndex: 'tingName', fixed: 'left', render: (v, r) => (r.tingName.length > 20 ? <Tooltip title={r.tingName}>{ r.tingName.substring(0, 20) + '...'}</Tooltip> : r.tingName) },
    { title: '厅盖章流水/元', dataIndex: 'tingSealAmount', align: 'center', render: (text, record) => (record.tingSealAmount.toLocaleString()) },
    // { title: '公会礼物流水/元', dataIndex: 'guildGiftAmount', align: 'center', render: (text, record) => (record.guildGiftAmount.toLocaleString()) },
    { title: '厅礼物流水/元', dataIndex: 'tingGiftAmount' },
    { title: '有效运营天数', dataIndex: 'operateDay' },
    { title: '达标阶段', dataIndex: 'reachStep' },
    { title: '[预结算]奖励金额/元', dataIndex: 'settlementReward' },
    { title: '发奖规则',
      dataIndex: 'awardRule',
      render: (text, record) => {
        return <Select allowClear defaultValue={record.awardRule} style={{ width: 150, marginLeft: 3 }} onChange={this.awardRuleChange(record)}>
          {prizeRuleList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}
        </Select>
      }
    },
    { title: '[调整后]奖励金额/元', dataIndex: 'adjustReward', render: (text, record) => (<InputNumber value={this.showAdjustReward(record)} onChange={this.adjustRewardChange(record)} />) },
    { title: '运营备注', width: 200, dataIndex: 'reviewRemark', render: (text, record) => (<Input allowClear onChange={e => { record.reviewRemark = e.target.value }} />) }
  ].map(item => {
    item.align = 'center'
    item.ellipsis = true
    return item
  })

  pagination = {
    pageSizeOptions: ['10', '20', '50', '100'],
    showSizeChanger: true,
    defaultPageSize: 20,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  showAdjustReward = (record) => {
    return record.adjustReward
  }

  awardRuleChange = (record) => v => {
    record.awardRule = v

    if (record.awardRule === prizeRuleSystem) {
      record.adjustReward = record.settlementReward
    } else {
      record.adjustReward = null
    }
    this.forceUpdate() // 强制刷新
  }

  adjustRewardChange = (record) => v => {
    if (record.awardRule === prizeRuleDown) {
      record.adjustReward = v
    } else if (record.awardRule === prizeRuleSystem) {
      record.adjustReward = record.settlementReward
    } else {
      record.adjustReward = null
    }
    this.forceUpdate() // 强制刷新
  }

  getFilterList = () => {
    const { list } = this.state
    const { searchASID } = this.state
    let filterList = list
    if (parseInt(searchASID) > 0) {
      filterList = filterList.filter((v) => { return v.asid === parseInt(searchASID) })
    }
    return filterList
  }

  onExport = () => {
    let list = this.getFilterList()

    let infos = deepClone(list)
    let exportData = infos.map(item => {
      const awardRule = item.awardRule
      item.awardRule = prizeRuleMap[item.awardRule]
      item.reviewStatus = reviewMap[item.reviewStatus]
      item.approvalStatus = approvaMap[item.approvalStatus]
      item.adjustReward = awardRule !== prizeRuleSystem && awardRule !== prizeRuleDown ? '' : item.adjustReward
      item.optUser = item.optUser === 0 ? '' : item.optUser

      let v = $.extend(true, {}, item)
      return v
    })
    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.export === undefined || col.export) {
        exportHeader.push({ key: col.dataIndex, header: col.title })
      }
    })
    let fileName = '盖章月度任务发奖确认-' + moment().format('YYYYMMDD') + '.xlsx'
    exportExcel(exportHeader, exportData, fileName)
  }

  inputReviewRemarkHandle = () => e => {
    let value = e.target.value
    this.setState({ inputReviewRemark: value })
  }

  hiddenModalRewardConfirm = () => {
    this.setState({ visibleRewardConfirm: false, inputReviewRemark: '', waitUpdateList: null, isFlush: false })
  }

  handleCancelRewardConfirm = e => {
    this.hiddenModalRewardConfirm()
  }

  handleSubmitRewardConfirm = e => {
    const { isFlush, inputReviewRemark, waitUpdateList } = this.state

    if (!isFlush) {
      message.warn('未更新配置统计')
      return
    }

    // <sid, ssid, rule,reward>
    let list = []
    waitUpdateList.forEach(v => {
      list.push({
        id: v.id,
        month: v.month,
        sid: v.sid,
        ssid: v.ssid,
        settlementReward: v.settlementReward,
        awardRule: v.awardRule,
        adjustReward: v.adjustReward,
        reviewRemark: v.reviewRemark
      })
    })

    console.log(list)

    this.props.dispatch({
      type: `${namespace}/approvalRewardConfirm`,
      payload: { remark: inputReviewRemark, list: list }
    })

    this.hiddenModalRewardConfirm()
  }

  flushHandle = () => () => {
    const { waitUpdateList, rewardConfirmTitleInfo, guildSum } = this.state
    if (!Array.isArray(waitUpdateList)) return

    console.log(waitUpdateList)

    let pass = true

    // 校验输入的值是否合法
    waitUpdateList.forEach(item => {
      if (item.awardRule !== prizeRuleDown && item.awardRule !== prizeRuleSystem) {
        pass = false
        message.warn('发奖规则选择错误1, ssid:', item.ssid)
      }
      if (item.awardRule === prizeRuleSystem && item.adjustReward !== item.settlementReward) {
        pass = false
        message.warn('发奖规则选择错误2, ssid:', item.ssid)
      }
      if (item.awardRule === prizeRuleDown && item.adjustReward < 0) {
        pass = false
        message.warn('发奖规则选择错误3, ssid:', item.ssid)
      }
    })

    if (!pass) return

    let info = { year: 0, month: 0, guildSum: 0, reachTing: 0, sealSum: 0, guildGiftSum: 0, reward: 0, settlementReward: 0, adjustReward: 0 }
    waitUpdateList.forEach(item => {
      info['year'] = rewardConfirmTitleInfo.year
      info['month'] = rewardConfirmTitleInfo.month
      info['guildSum'] = guildSum
      info['reachTing']++
      info['sealSum'] += item.tingSealAmount
      info['guildGiftSum'] += item.guildGiftAmount
      info['reward'] += item.adjustReward
      info['settlementReward'] += item.settlementReward
      info['adjustReward'] += item.adjustReward
    })

    this.setState({ isFlush: true, rewardConfirmTitleInfo: info })
  }

  // 批量任务确认
  confirmAllHandle =() => () => {
    // const { list } = this.state
    const { list, canAllConfirm, guildSum } = this.state

    if (!canAllConfirm) {
      message.warn('运营可操作时间内且未复核才能发起复核')
      // return  // FIXME: 改回 限制
    }

    let filterList = deepClone(list)

    if (filterList.length === 0) {
      message.warn('无符合审批的单')
      return
    }

    let year = null
    let month = null
    let infos = []
    filterList.forEach(info => {
      year = info.month.substr(0, 4)
      month = info.month.substr(5, 5)
      info.awardRule = prizeRuleSystem
      info.adjustReward = info.settlementReward
      infos.push(info)
    })

    const rewardConfirmTitleInfo = { year: year, month: month, guildSum: guildSum, reachTing: 0, sealSum: 0, guildGiftSum: 0, reward: 0, settlementReward: 0, adjustReward: 0 }
    this.setState({ visibleRewardConfirm: true, waitUpdateList: infos, rewardConfirmTitleInfo: rewardConfirmTitleInfo })
  }

  render () {
    const { visibleRewardConfirm, inputReviewRemark, waitUpdateList, rewardConfirmTitleInfo } = this.state
    console.log(rewardConfirmTitleInfo)
    return (
      <Card>
        <Form>
          短位ID：
          <Input style={{ width: '10em', marginRight: 10 }} placeholder='请输入' onChange={(e) => { this.setState({ 'searchASID': e.target.value }) }} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onExport}>导出</Button>
          <div />
          <Button style={{ marginTop: 5, marginBottom: 5 }} type='primary' onClick={this.confirmAllHandle()}>复核</Button>
          <Table style={{ marginTop: 10 }} dataSource={this.getFilterList()} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} scroll={{ x: 'max-content' }} size='small' />
        </Form>

        <Modal keyboard={false} destroyOnClose forceRender width={1600} visible={visibleRewardConfirm} title='发奖复核' onCancel={this.handleCancelRewardConfirm} onOk={this.handleSubmitRewardConfirm}>
          <div><font color='red'>{rewardConfirmTitleInfo.year}年{rewardConfirmTitleInfo.month}月任务</font></div>
          <div>1. 概述：当前参与公会{rewardConfirmTitleInfo.guildSum}家, 达标厅{rewardConfirmTitleInfo.reachTing}个，合计盖章流水{yuanToWan(rewardConfirmTitleInfo.sealSum)}万元，发放道具{rewardConfirmTitleInfo.reward}元</div>
          <div>2. 发奖概况：[预结算]合计奖励金额{rewardConfirmTitleInfo.settlementReward}元，[调整后]合计奖励金额{rewardConfirmTitleInfo.adjustReward}元</div>

          <font color='red'>运营复核备注:</font>
          <TextArea placeholder='选填' autoSize={{ minRows: 2, maxRows: 4 }} style={{ height: 50, width: 500 }} value={inputReviewRemark} onChange={this.inputReviewRemarkHandle()} />
          <Button style={{ marginTop: 5, marginBottom: 5 }} type='primary' onClick={this.flushHandle()}>更新配置统计</Button>
          <Table bordered dataSource={waitUpdateList} columns={this.columnsConfirm} pagination={false} scroll={{ x: 'max-content' }} size='small' />
        </Modal>
      </Card>
    )
  }
}

export default RewardConfirm
