import { simpleRequire2 } from '@/utils/common'

export default {
  namespace: 'essenceExchangeCfg',
  state: {
    accessCfg: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },

  effects: {
    * queryAccessCfg ({ payload }, { select, call, put }) { // 查询精粹入口限制配置
      let resp = yield call(simpleRequire2, `/courage_mall_boss/get_pod_exchange_entry_info`, null, false)
      if (!resp || resp === 1) {
        resp = [{ name: '', yyLevelLimit: 0, operator: '', timestamp: 0, formattedTime: '' }]
      }
      yield put({
        type: 'updateState',
        payload: { name: 'accessCfg', newValue: resp }
      })
    },
    * queryPodCfg ({ payload }, { select, call, put }) { // 查询精粹限制配置值
      let resp = yield call(simpleRequire2, `/essence_exchange_cfg/exchange_limit/get_cfg`, null, false)
      if (!resp || resp === 1) {
        resp = { value: 0 }
      }
      const { cbFunc } = payload
      if (cbFunc) {
        cbFunc(resp.value, resp.valueExt)
      }
    },
    * submitAccessCfg ({ payload }, { select, call, put }) { // 精粹入口限制配置更新
      const { params, cbFunc } = payload
      let resp = yield call(simpleRequire2, `/courage_mall_boss/mod_pod_exchange_entry_info`, params, false)
      if (resp == null) {
        cbFunc(false)
      }
      cbFunc(true)
    },
    * submitPodLimitValue ({ payload }, { select, call, put }) { // 更新精粹限制配置
      const { params, cbFunc } = payload
      let resp = yield call(simpleRequire2, `/essence_exchange_cfg/exchange_limit/update`, { newValue: params.totalLimitValue, newValueExt: params.virtOrTeamLimitValue }, true)
      if (!resp) {
        cbFunc(false)
        return
      }
      cbFunc(true)
    }
  }
}
