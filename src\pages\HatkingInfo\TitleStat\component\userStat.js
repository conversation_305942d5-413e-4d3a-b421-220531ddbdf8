import React, { Component } from 'react'
import { connect } from 'dva'
import { Row, Col, DatePicker, Button, Table, Space, Divider } from 'antd'
import moment from 'moment'
const namespace = 'hatkingInfoTitleInfo'

@connect(({ hatkingInfoTitleInfo }) => ({
  model: hatkingInfoTitleInfo
}))

class UserStat extends Component {
  state = {
    selectTimeRange: [moment().subtract(7, 'day'), moment()]
  }
  componentDidMount = () => {
    this.queryList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  getParams = (opTpe = 'query') => {
    const [start, end] = this.state.selectTimeRange
    const { tag } = this.props
    let startDate = start.format('YYYYMMDD')
    let endDate = end.format('YYYYMMDD')
    const params = {
      tag: tag,
      opType: opTpe,
      startDate,
      endDate
    } 
    return params
  }

  getFuncName = () => {
    const { tag } = this.props
    if (tag === 'user') {
      return 'queryUserStat'
    }
    if (tag === 'task') {
      return 'queryTaskStat'
    }
    return 'queryRewardStat'
  }

  // 查询表格
  queryList = () => {
    const params = this.getParams()
    this.callModel(this.getFuncName(), { params })
  }

  // 导出表格
  exportList = () => {
    const params = this.getParams('export')
    this.callModel(this.getFuncName(), { 
      params,
      isDownloadMode: true
    })
  }

  parseHeader = (table) => {
    if (!Array.isArray(table) || table.length === 0) {
      return []
    }
    const header = table[0]
    return header.map((title, index) => {
      return {
        title: title,
        dataIndex: `f${index}`,
        align: 'cneter'
      }
    })
  }

  parseDataSource = (table) => {
    if (!Array.isArray(table) || table.length === 0) {
      return []
    }
    let dataSource = []
    for (let i = 1; i < table.length; i++) {
      let row = table[i]
      let item = {}
      row.map((value, index) => {
        item[`f${index}`] = value 
      })
      dataSource.push(item)
    }
    return dataSource
  }

  render () {
    const { userList, taskList, rewardList } = this.props.model
    const { selectTimeRange } = this.state
    const { tag } = this.props
    const { RangePicker } = DatePicker

    let data = []
    if (tag === 'user') {
      data = userList
    } else if (tag === 'task') {
      data = taskList
    } else {
      data = rewardList
    }

    return (
      <>
        <Row>
          <Col span={24}>
            <Space>
              <RangePicker value={selectTimeRange} onChange={v => this.setState({ selectTimeRange: v })} />
              <Button onClick={this.queryList} type='primary'>查询</Button>
              <Button onClick={this.exportList}>导出</Button>
            </Space>
          </Col>
          <Divider />
          <Col span={24}>
            <Table columns={this.parseHeader(data)} dataSource={this.parseDataSource(data)} pagination={false} />
          </Col>
        </Row>
      </>
    )
  }
}

export default UserStat
