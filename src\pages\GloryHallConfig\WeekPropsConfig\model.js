import { getLists, update, getPropsList } from './api'
import { message as msg } from 'antd'

export default {
  namespace: 'weekProosConfig',

  state: {
    list: [],
    propsList: [],
    localPropsList: []
  },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }

      return {
        ...state,
        list: payload
      }
    },

    updatePropsList (state, { payload }) {
      return {
        ...state,
        propsList: payload
      }
    },

    updateLocalPropsList (state, { payload }) {
      return {
        ...state,
        propsList: payload,
        localPropsList: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getLists, payload)
      yield put({
        type: 'updateList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * getPropsConfigList ({ payload }, { call, put }) {
      const { data: { propsList } } = yield call(getPropsList)

      yield put({
        type: 'updateLocalPropsList',
        payload: Array.isArray(propsList) ? propsList : []
      })
    },

    * updateItem ({ payload }, { call, put }) {
      const { data: { status, message } } = yield call(update, payload)
      if (status === 0) {
        msg.success('update success')
      } else {
        msg.error('failed ' + message)
      }
      yield put({
        type: 'getList'
      })
    }
  }
}
