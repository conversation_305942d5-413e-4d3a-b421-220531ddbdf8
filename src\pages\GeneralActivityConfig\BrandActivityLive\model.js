import { message } from 'antd'
import {
  getMagicConfigList,
  updateMagicConfig,
  deleteMagicConfig,
  getRoutineConfigList,
  deleteRoutineConfig,
  updateRoutineConfig,
  getLiveConfigList,
  updateLiveConfig,
  deleteLiveConfig,
  getYYMobileConfigList,
  updateYYMobileConfig,
  deleteYYMobileConfig
} from './api'

export default {
  namespace: 'brandActLive',

  state: {
    magicConfigList: [],
    routineConfigList: [],
    liveConfigList: [],
    yymobileConfigList: []
  },

  reducers: {
    updateMagicConfigList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      for (let i = 0; i < payload.length; i++) {
        payload[i].idx = i + 1
      }
      return {
        ...state,
        magicConfigList: payload
      }
    },

    updateLiveConfigList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      for (let i = 0; i < payload.length; i++) {
        payload[i].idx = i + 1
      }
      return {
        ...state,
        liveConfigList: payload
      }
    },

    updateRoutineConfigList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      for (let i = 0; i < payload.length; i++) {
        payload[i].idx = i + 1
      }
      return {
        ...state,
        routineConfigList: payload
      }
    },

    updateYYMobileConfigList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      for (let i = 0; i < payload.length; i++) {
        payload[i].idx = i + 1
      }
      return {
        ...state,
        yymobileConfigList: payload
      }
    }

  },

  effects: {
    * getMagicConfigList ({ payload }, { call, put }) {
      try {
        const { data: { status, msg, list } } = yield call(getMagicConfigList, payload)
        if (status === 0) {
          yield put({
            type: 'updateMagicConfigList',
            payload: Array.isArray(list) ? list : []
          })
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('exception', e)
        console.log('getMagicConfigList', e)
      }
    },

    * updateMagicConfig ({ payload }, { call, put }) {
      try {
        let { data: { status, msg } } = yield call(updateMagicConfig, payload)
        console.log(status, msg)
        if (status !== 0) {
          message.error('操作失败:' + msg)
          return
        }
        message.success('操作成功~')
      } catch (e) {
        message.error('exception', e)
      }
    },

    * deleteMagicConfig ({ payload }, { call, put }) {
      try {
        let { data: { status, msg } } = yield call(deleteMagicConfig, payload)
        console.log(status, msg)
        if (status !== 0) {
          message.error('操作失败:' + msg)
          return
        }
        message.success('操作成功~')
      } catch (e) {
        message.error('exception', e)
      }
    },

    // 常规任务
    * getRoutineConfigList ({ payload }, { call, put }) {
      try {
        const { data: { status, msg, list } } = yield call(getRoutineConfigList, payload)
        if (status === 0) {
          yield put({
            type: 'updateRoutineConfigList',
            payload: Array.isArray(list) ? list : []
          })
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('exception', e)
        console.log('getRoutineConfigList', e)
      }
    },

    * updateRoutineConfig ({ payload }, { call, put }) {
      try {
        let { data: { status, msg } } = yield call(updateRoutineConfig, payload)
        console.log(status, msg)
        if (status !== 0) {
          message.error('操作失败:' + msg)
          return
        }
        message.success('操作成功~')
      } catch (e) {
        message.error('exception', e)
      }
    },

    * deleteRoutineConfig ({ payload }, { call, put }) {
      try {
        let { data: { status, msg } } = yield call(deleteRoutineConfig, payload)
        console.log(status, msg)
        if (status !== 0) {
          message.error('操作失败:' + msg)
          return
        }
        message.success('操作成功~')
      } catch (e) {
        message.error('exception', e)
      }
    },

    // 活动
    * getLiveConfigList ({ payload }, { call, put }) {
      try {
        const { data: { status, msg, list } } = yield call(getLiveConfigList, payload)
        if (status === 0) {
          yield put({
            type: 'updateLiveConfigList',
            payload: Array.isArray(list) ? list : []
          })
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('exception', e)
        console.log('getMagicConfigList', e)
      }
    },

    * updateLiveConfig ({ payload }, { call, put }) {
      try {
        let { data: { status, msg } } = yield call(updateLiveConfig, payload)
        console.log(status, msg)
        if (status !== 0) {
          message.error('操作失败:' + msg)
          return
        }
        message.success('操作成功~')
      } catch (e) {
        message.error('exception', e)
      }
    },

    * deleteLiveConfig ({ payload }, { call, put }) {
      try {
        let { data: { status, msg } } = yield call(deleteLiveConfig, payload)
        console.log(status, msg)
        if (status !== 0) {
          message.error('操作失败:' + msg)
          return
        }
        message.success('操作成功~')
      } catch (e) {
        message.error('exception', e)
      }
    },

    // 手Y专属
    * getYYMobileConfigList ({ payload }, { call, put }) {
      try {
        const { data: { status, msg, list } } = yield call(getYYMobileConfigList, payload)
        if (status === 0) {
          yield put({
            type: 'updateYYMobileConfigList',
            payload: Array.isArray(list) ? list : []
          })
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('exception', e)
        console.log('getYYLiveConfigList', e)
      }
    },

    * updateYYMobileConfig ({ payload }, { call, put }) {
      try {
        let { data: { status, msg } } = yield call(updateYYMobileConfig, payload)
        console.log(status, msg)
        if (status !== 0) {
          message.error('操作失败:' + msg)
          return
        }
        message.success('操作成功~')
      } catch (e) {
        message.error('exception', e)
      }
    },

    * deleteYYMobileConfig ({ payload }, { call, put }) {
      try {
        let { data: { status, msg } } = yield call(deleteYYMobileConfig, payload)
        console.log(status, msg)
        if (status !== 0) {
          message.error('操作失败:' + msg)
          return
        }
        message.success('操作成功~')
      } catch (e) {
        message.error('exception', e)
      }
    }

  }
}
