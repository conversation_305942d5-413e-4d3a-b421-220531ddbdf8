import * as api from './api'
import { message } from 'antd'

export default {
  namespace: 'newCompereInfo',

  state: {
    list: [],
    tabSumInfo: { firstTab: 0, secondTab: 0, thirdTab: 0, fourthTab: 0, fifthTab: 0 },
    inboundStatus: { uid: 0, business: '', realNameType: '', realNameStatus: '', realNameTime: '', relateUidContracts: [], curHadContractOther: false, curHadContractOtherExpand: [] }
  },

  reducers: {
    displayList (state, { payload, tabSumInfo }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        list: payload,
        tabSumInfo: tabSumInfo
      }
    },
    updateInboundStatus (state, { inboundStatus }) {
      return {
        ...state,
        inboundStatus: inboundStatus
      }
    }
  },

  effects: {
    * listCompereInfo ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listCompereInfo, payload)
      console.log(status, msg)

      let tabSumInfo = data === null || data.tabSumInfo === null || data.tabSumInfo === undefined ? { firstTab: 0, secondTab: 0, thirdTab: 0, fourthTab: 0, fifthTab: 0 } : data.tabSumInfo

      let dataNew = data === null ? [] : Array.isArray(data.list) ? data.list : []
      for (let i = 0; i < dataNew.length; i++) {
        dataNew[i].idx = i + 1
      }
      console.log(dataNew)
      yield put({
        type: 'displayList',
        payload: dataNew,
        tabSumInfo: tabSumInfo
      })
    },

    * removeNewCompere ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.removeNewCompere, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: '出库失败' })
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listCompereInfo',
        payload: {}
      })
    },

    * importNewCompere ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.importNewCompere, payload)
      console.log(status, msg)
      if (status !== 0) {
        if (status === 10001) {
          message.error({ content: msg })
          return
        }
        if (status === 10002) {
          message.error({ content: msg })
          return
        }
        if (status === 10003) {
          message.error({ content: msg })
          return
        }
        message.error({ content: '手工导入失败' })
      } else {
        message.success('导入成功')
      }
      console.log(payload)
      yield put({
        type: 'listCompereInfo',
        payload: { tabIdx: payload.tabIdx }
      })
    },

    * approvalNewCompere ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.approvalNewCompere, payload)
      console.log(status, msg)
      if (status !== 0) {
        if (status === 10003) {
          message.error(msg)
          return
        }
        message.warning(msg)
        return
      }
      let tabIdx = payload.tabIdx
      message.success('审批成功')
      yield put({
        type: 'listCompereInfo',
        payload: { tabIdx: tabIdx }
      })
    },

    * batchApprovalNewCompere ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.batchApprovalNewCompere, payload)
      console.log(status, msg)
      if (status !== 0) {
        if (status === 10003) {
          message.error(msg)
          return
        }
        message.warning(msg)
        return
      }
      let tabIdx = payload.tabIdx
      message.success('审批成功')
      yield put({
        type: 'listCompereInfo',
        payload: { tabIdx: tabIdx }
      })
    },

    * getCompereInboundStatus ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.getCompereInboundStatus, payload)
      console.log(status, msg)
      console.log(data)
      if (status !== 0) {
        message.warning(msg)
        return
      }
      if (status === 0 && data !== null) {
        let list = []
        if (data.curHadContractOtherExpand !== null) {
          data.curHadContractOtherExpand.forEach(item => {
            if (item.length > 0) {
              list.push(item)
            }
          })
        } else {
          data.curHadContractOtherExpand = list
        }

        let list2 = []
        if (data.relateUidContracts !== null) {
          data.relateUidContracts.forEach(item => {
            if (item.contractStatus) {
              item.contractStatus = '是'
            } else {
              item.contractStatus = '否'
            }
            list2.push(item)
          })
        } else {
          data.relateUidContracts = list2
        }
      }
      yield put({
        type: 'updateInboundStatus',
        inboundStatus: data
      })
    }
  }
}
