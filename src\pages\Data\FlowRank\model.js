
import { genGetRequireTemplate } from '@/utils/common'

const getTopRoomList = genGetRequireTemplate('/live_stat/boss/get_top_room_list', 'roomRank')
const getTopUserList = genGetRequireTemplate('/live_stat/boss/get_top_compere_list', 'userRank')
const deleteRoomItem = genGetRequireTemplate('/live_stat/boss/delete_room_item')
const deleteUserItem = genGetRequireTemplate('/live_stat/boss/delete_user_item')

export default {
  namespace: 'flowRankData',
  state: {
    userRank: [],
    roomRank: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getTopRoomList,
    getTopUserList,
    deleteRoomItem,
    deleteUserItem
  }
}
