/* eslint-disable react/jsx-boolean-value */
import React, { Component } from 'react'
import {
  Card,
  Divider,
  Button,
  Modal,
  Form,
  Table,
  Popconfirm,
  InputNumber,
  Input,
  Checkbox,
  DatePicker,
  message, Radio, Select
} from 'antd'
// import dateString from '@/utils/dateString'
import { connect } from 'dva'
import moment from 'moment'

const format = 'YYYY-MM-DD HH:mm:ss'

const RadioGroup = Radio.Group
const namespace = 'SynthesisConfig' // model 的 namespace
const FormItem = Form.Item
const { RangePicker } = DatePicker
const { Option } = Select

const stateTransfer = { 0: '提交人', 1: '审批人' }

const allowPropsTypeOptions = [
  { label: '豆荚', value: 1 },
  { label: '包裹礼物', value: 2 }
]

@connect(({ SynthesisConfig }) => ({ // model 的 namespace
  model: SynthesisConfig // model 的 namespace
}))
class SynthesisConfigComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      classification: 0
    }
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getConfigList`
    })
  }

  inTime = (begin, end) => {
    let now = moment().unix()
    return begin <= now && now < end
  }

  // 需要修改
  columns = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: '合成礼物ID', dataIndex: 'targetPropsID', align: 'center' },
    { title: '合成礼物名称', dataIndex: 'targetPropsName', align: 'center' },
    { title: '合成礼物单价(紫水晶)', dataIndex: 'targetPropsPrice', width: 90, align: 'center' },
    { title: '单次合成数量', dataIndex: 'onceTargetCount', align: 'center' },
    { title: '扣除道具方式', dataIndex: 'consumeGiftType', align: 'center', render: text => ['未定义', '按订单扣', '按次数扣'][text] },
    { title: '可用合成材料', dataIndex: 'allowPropsType', align: 'center', render: text => ['豆荚/包裹礼物', '豆荚', '包裹礼物'][text] },
    { title: '魔法棒', dataIndex: 'giftA', align: 'center' },
    { title: '藏宝图', dataIndex: 'giftB', align: 'center' },
    { title: '能量石', dataIndex: 'giftC', align: 'center' },
    { title: '荣耀星石', dataIndex: 'giftD', align: 'center' },
    { title: '夜光石', dataIndex: 'giftE', align: 'center' },
    { title: '标签文案', dataIndex: 'labelName', align: 'center' },
    { title: '标签背景颜色', dataIndex: 'labelColor', align: 'center' },
    { title: '分类', dataIndex: 'classification', align: 'center', render: text => text === undefined ? '-' : ['普通', '稀有', '周星'][text] },
    { title: '排序权重', dataIndex: 'weight', align: 'center' },
    { title: '魔法礼物', dataIndex: 'magicGift', align: 'center', render: text => text === true ? <a style={{ color: 'blue' }} >是</a> : <a style={{ color: 'black' }} >否</a> },
    { title: '开始时间', dataIndex: 'startTime', align: 'center', width: 90, render: (text, record, index) => <font style={{ color: this.inTime(record.startTime, record.endTime) ? 'red' : '' }}>{moment.unix(text).format(format)}</font> },
    { title: '结束时间', dataIndex: 'endTime', align: 'center', width: 90, render: (text, record, index) => <font style={{ color: this.inTime(record.startTime, record.endTime) ? 'red' : '' }}>{moment.unix(text).format(format)}</font> },
    // { title: '开始时间', dataIndex: 'startTime', align: 'center', width: 90, render: (text, record) => (text === 0 ? '无' : dateString(text)) },
    // { title: '结束时间', dataIndex: 'endTime', align: 'center', width: 90, render: (text, record) => (text === 0 ? '无' : dateString(text)) },
    { title: '审批流', dataIndex: 'progress', align: 'center', render: text => Array.isArray(text) ? text.map(i => <div key={Math.random().toString(32)}>{stateTransfer[i.state] + '： ' + i.passport}</div>) : '' },
    { title: '操作',
      align: 'center',
      render: (text, record) => (
        <span>
          <div>
            <a onClick={this.showModal(true, record)}>更新</a><Divider type='vertical' />
            <Popconfirm title='确认?' onConfirm={this.handleDel(record.id)}>
              <a href=''>删除</a>
            </Popconfirm>
          </div>
        </span>)
    }
  ]

  defaultValue = { }

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    let classification = isUpdate ? v.classification : 0
    if (this.formRef) {
      this.formRef.resetFields()
      v.timeRange = isUpdate ? [moment(record.startTime * 1000), moment(record.endTime * 1000)] : []
      v.commitType = isUpdate ? 2 : 1
      if (v.allowPropsType === 0) {
        v.allowPropsType = [1, 2]
      } else {
        v.allowPropsType = [v.allowPropsType]
      }
      v.classification = classification
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, isUpdate: isUpdate, title: isUpdate ? '更新' : '添加', classification: classification })
  }

  // delete
  handleDel = id => e => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/removeItem`,
      payload: { id }
    })
  }

  // 隐藏弹窗，不需要修改
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleGroupChange = (value) => {
    const { dispatch } = this.props
    const data = { livestate: value.key }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })

    this.setState({ searchLiveState: value.key })
  }

  onFinish = record => {
    const { dispatch } = this.props
    const { classification } = this.state
    if (record.giftA === 0 && record.giftB === 0 &&
      record.giftC === 0 && record.giftD === 0 && record.giftE === 0) {
      console.log('', record.giftA, record.giftB, record.giftC)
      message.error('魔法棒、藏宝图、能量石、荣耀星石不能同时为0！')
      return
    }
    record.startTime = record.timeRange[0].unix()
    record.endTime = record.timeRange[1].unix()
    if (Array.isArray(record.allowPropsType)) {
      if (record.allowPropsType.length === 1) { // 单选
        record.allowPropsType = record.allowPropsType[0]
      } else if (record.allowPropsType.length === 2) { // 全选
        record.allowPropsType = 0
      }
    }
    record.consumeGiftList = [
      { 'name': '魔法棒', 'id': 10001, 'count': record.giftA },
      { 'name': '藏宝图', 'id': 10002, 'count': record.giftB },
      { 'name': '能量石', 'id': 10003, 'count': record.giftC },
      { 'name': '荣耀星石', 'id': 10004, 'count': record.giftD },
      { 'name': '夜光石', 'id': 10005, 'count': record.giftE }
    ]
    record.classification = parseInt(classification)
    delete record.timeRange
    delete record.giftA
    delete record.giftB
    delete record.giftC
    delete record.giftD
    delete record.giftE
    // values.uidList = values.uidList.split(/,|，/, -1).map(i => parseInt(i))
    dispatch({
      type: `${namespace}/upsetItem`,
      payload: record
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 更新与删除
  handleSubmit = () => {
    this.formRef.submit()
  }

  handleRemove = id => () => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/removeList`,
      payload: { id }
    })
  }

  // 时间选择检查
  checkSelectTime = (tr) => {
    if (tr === null || tr === undefined || tr.length < 2) {
      message.warn('未选择时间')
    }
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { configList } } = this.props
    const { visible, title, isUpdate } = this.state
    const formItemLayout = { // 不需要修改
      labelCol: {
        xs: { span: 6 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 15 },
        sm: { span: 15 }
      }
    }

    return (
      <Card>
        <div><font color='red'>※注意※魔法礼物不能在手y展示与合成,请注意魔法礼物字段是否与营收配置一致</font></div>
        <Button onClick={this.showModal(false, this.defaultValue)}>添加</Button>
        <Divider />
        <Table rowKey={record => record.index} dataSource={configList} columns={this.columns} size='small' pagination={false} /> {/* 显示的列表 */}

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} ref={form => { this.formRef = form }} {...formItemLayout}>
            
            <FormItem label='合成礼物' name='targetPropsID' rules={[
              { required: true },
              ({ getFieldValue }) => ({
                validator (rule, value) {
                  if (value === 0) {
                    return Promise.reject(new Error('营收道具ID不能为0!'))
                  }
                  return Promise.resolve()
                }
              })
            ]}>
              <InputNumber readOnly={isUpdate} style={{ width: '100%' }} placeholder='请营收道具ID' />
            </FormItem>

            <FormItem label='单次合成数量' name='onceTargetCount' rules={[
              { required: true },
              ({ getFieldValue }) => ({
                validator (rule, value) {
                  if (value === 0) {
                    return Promise.reject(new Error('单次合成数量不能为0!'))
                  }
                  return Promise.resolve()
                }
              })
            ]}>
              <InputNumber style={{ width: '100%' }} placeholder='单次合成数量必填' />
            </FormItem>

            <FormItem {...formItemLayout} label='扣除道具方式' name='consumeGiftType' rules={[{ required: true, message: '扣除道具方式 不能为空' }]}>
              <RadioGroup>
                <Radio value={1}>按订单</Radio>
                <Radio value={2}>按次数扣</Radio>
              </RadioGroup>
            </FormItem>
            <FormItem {...formItemLayout} label='可用合成材料' name='allowPropsType' rules={[{ required: true, message: '合成道具 不能为空' }]}>
              <Checkbox.Group style={{ width: '100%' }} options={allowPropsTypeOptions} />
            </FormItem>
            <FormItem label='魔法棒' name='giftA' dependencies={['giftB']} shouldUpdate rules={[
              { required: true, message: '魔法棒消耗数量' },
              ({ getFieldValue }) => ({
                validator (rule, value) {
                  if (getFieldValue('giftA') < 0) {
                    return Promise.reject(new Error('不能小于0!'))
                  }
                  return Promise.resolve()
                }
              })
            ]}>
              <InputNumber style={{ width: '100%' }} placeholder='大于0则选择(魔法棒指魔豆章附赠道具)' />
            </FormItem>
            <FormItem label='藏宝图' name='giftB' shouldUpdate rules={[
              { required: true, message: '藏宝图消耗数量' },
              ({ getFieldValue }) => ({
                validator (rule, value) {
                  if (getFieldValue('giftB') < 0) {
                    return Promise.reject(new Error('不能小于0!'))
                  }
                  return Promise.resolve()
                }
              }) ]}>
              <InputNumber style={{ width: '100%' }} placeholder='大于0则选择(藏宝图指空投附赠道具)' />
            </FormItem>
            <FormItem label='能量石' name='giftC' shouldUpdate rules={[
              { required: true, message: '藏宝图消耗数量' },
              ({ getFieldValue }) => ({
                validator (rule, value) {
                  if (getFieldValue('giftC') < 0) {
                    return Promise.reject(new Error('不能小于0!'))
                  }
                  return Promise.resolve()
                }
              }) ]}>
              <InputNumber style={{ width: '100%' }} placeholder='大于0则选择(能量石来自星星夺宝奖励)' />
            </FormItem>
            <FormItem label='荣耀星石' name='giftD' shouldUpdate rules={[
              { required: true, message: '荣耀星石消耗数量' },
              ({ getFieldValue }) => ({
                validator (rule, value) {
                  if (getFieldValue('giftD') < 0) {
                    return Promise.reject(new Error('不能小于0!'))
                  }
                  return Promise.resolve()
                }
              }) ]}>
              <InputNumber style={{ width: '100%' }} placeholder='大于0则选择(公会预算赛累计消费每荣耀值达到10可获得1荣耀星石)' />
            </FormItem>
            <FormItem label='夜光石' name='giftE' shouldUpdate rules={[
              { required: true, message: '夜光石消耗数量' },
              ({ getFieldValue }) => ({
                validator (rule, value) {
                  if (getFieldValue('giftE') < 0) {
                    return Promise.reject(new Error('不能小于0!'))
                  }
                  return Promise.resolve()
                }
              }) ]}>
              <InputNumber style={{ width: '100%' }} placeholder='大于0则选择(公会预算赛累计消费每荣耀值达到10可获得1荣耀星石)' />
            </FormItem>
            <FormItem label='标签文案' name='labelName' rules={[{ required: false }]}>
              <Input placeholder='输入二个字,非必填' />
            </FormItem>
            <FormItem label='标签颜色' name='labelColor' rules={[{ required: false }]}>
              <Input placeholder='输入色,非必填' />
            </FormItem>
            <FormItem label='排序权重' name='weight' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} placeholder='输入数字,越大越靠前' />
            </FormItem>
            <FormItem label='分类' name='classification' >
              <Select style={{ width: 120 }} onChange={e => this.setState({ classification: e })}>
                <Option key={0} value={0}>普通</Option>
                <Option key={1} value={1}>稀有</Option>
                <Option key={2} value={2}>周星</Option>
              </Select>
            </FormItem>
            <Form.Item label='时间范围' name='timeRange' rules={[{ required: true }]}>
              <RangePicker format={'YYYY-MM-DD HH:mm:ss'} showTime={{ format: 'HH:mm:ss' }} onChange={(t) => this.checkSelectTime(t)} />
            </Form.Item>
            <FormItem name='commitType' hidden />
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default SynthesisConfigComponent
