import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  let url = `/white_list/videodating_config_list?${stringify(params)}`
  return request(url, { jsonp: true })
}

export function whiteListAdd (params) {
  let url = '/white_list/videodating_config_update'
  params['opType'] = parseInt(params['opType'], 10)
  params['sid'] = parseInt(params['sid'], 10)
  params['ssid'] = parseInt(params['ssid'], 10)
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function whiteListUpdate (params) {
  let url = '/white_list/videodating_config_update'
  params['opType'] = 3
  params['sid'] = parseInt(params['sid'], 10)
  params['ssid'] = parseInt(params['ssid'], 10)
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function whiteListDel (params) {
  let url = '/white_list/videodating_config_update'
  params['opType'] = 2
  params['sid'] = parseInt(params['sid'], 10)
  params['ssid'] = parseInt(params['ssid'], 10)
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
