import request from '@/utils/request'
import { stringify } from 'qs'

// 获取公告列表
export function listAnnouncement (params) {
  params.isV2 = true
  return request(`/hgamebackend/get_bulletin_list?${stringify(params)}`)
}

// 新增公告
export function addAnnouncement (params) {
  params.isV2 = true
  return request(`/hgamebackend/add_bulletin_info_v2`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 更新公告
export function updateAnnouncement (params) {
  params.isV2 = true
  return request(`/hgamebackend/mod_bulletin_info_v2`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 删除公告
export function deleteAnnouncement (params) {
  params.isV2 = true
  return request(`/hgamebackend/del_bulletin_info?${stringify(params)}`)
}
