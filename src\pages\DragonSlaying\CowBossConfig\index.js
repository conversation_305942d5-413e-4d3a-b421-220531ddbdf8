import React, { Component } from 'react'
import { connect } from 'dva'
import { <PERSON>, Tabs, Tooltip, Row, Table, Col, Popconfirm, message, Button, DatePicker, Modal, Input, Form } from 'antd'
import { timeFormater, tableStyle, trySeveralTimes, Inputlayout, checkIsNumber } from '@/utils/common'
import { DeleteOutlined, FormOutlined } from '@ant-design/icons'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import moment from 'moment'

const namespace = 'cowBossConfigNew'

@connect(({ cowBossConfigNew }) => ({
  model: cowBossConfigNew
}))

class CowBossConfigNew extends Component {
  state = {
    selectRecord: { // 选中行的信息
      actID: '',
      startTime: '',
      endTime: '',
      roundProfit: '',
      recycleLimit: '',
      delayGetReword: ''
    },
    modelVisable: false, // 修改配置模态框是否显示
    updating: false,
    appending: false
  }
  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 组件初始化
  componentDidMount = () => {
    if (this.modifyForm) {
      this.modifyForm.resetFields()
    }
    this.callModel('queryList', null)
  }
  // 标签页切换
  onTagChange = (page) => {
    if (page === '2') { // 新增配置页面
      let resetNewConfForm = () => {
        if (this.newConfForm) {
          this.newConfForm.resetFields()
          return true
        }
        return false
      }
      trySeveralTimes(resetNewConfForm, 500, 10)
      return
    }
    if (page === '1') { // 查看配置
      this.callModel('queryList', null)
      message.success('数据已刷新')
      return
    }
    console.error('unexpect page:', page)
  }
  // 表格变更
  onTableChange = (pagination) => {
    const { current, pageSize } = pagination
    this.changeState('page', current)
    this.changeState('size', pageSize)
  }
  // 确认根据id删除一项活动配置
  onComfirmDel = (id) => {
    if (typeof id !== 'string') {
      message.error('发生错误，请检查控制台')
      console.error('onComfirmDel() get unexpect actID, actID=', id)
      return
    }
    let callback = () => { // 删除成功后更新列表
      this.callModel('queryList', null)
      message.info('列表已刷新')
    }
    this.callModel('deleteConfig', { actID: id, callback: callback })
  }
  // 确认修改选中的配置
  onComfirmModify = (record) => {
    if (record.timeRange === null || record.timeRange === undefined) {
      message.warn('时间选择有误，请检查')
      return
    }
    record.startTime = record.timeRange[0].unix()
    record.endTime = record.timeRange[1].unix()
    delete record.timeRange
    if (!this.checkParams(record, true)) {
      return
    }
    this.setState({ updating: true })
    let callback = () => {
      this.setState({ modelVisable: false })
      this.setState({ updating: false })
      this.callModel('queryList', null)
      message.info('列表已刷新')
    }
    this.callModel('modifyOrAddConfig', {
      params: record,
      callback: callback
    })
  }
  // 确认新增一项活动配置
  onComfirmAddConf = (record) => {
    if (record.timeRange === null || record.timeRange === undefined) {
      message.warn('时间选择有误，请检查')
      return
    }
    record.startTime = record.timeRange[0].unix()
    record.endTime = record.timeRange[1].unix()
    delete record.timeRange
    if (!this.checkParams(record, false)) {
      return
    }
    this.setState({ appending: true })
    let callback = () => {
      this.setState({ appending: false })
    }
    this.callModel('modifyOrAddConfig', {
      params: record,
      callback: callback
    })
  }

  // 选中修改的表行,弹出修改配置模态框
  onSelectModifyRow = (record) => {
    this.setState({ selectRecord: record })
    this.setState({ modelVisable: true })
    let tryFunc = () => {
      if (!this.modifyForm) {
        return false
      }
      this.modifyForm.setFieldsValue({ actID: record.actID })
      this.modifyForm.setFieldsValue({ timeRange: [moment(record.startTime * 1000), moment(record.endTime * 1000)] })
      this.modifyForm.setFieldsValue({ roundProfit: record.roundProfit })
      this.modifyForm.setFieldsValue({ recycleLimit: record.recycleLimit })
      this.modifyForm.setFieldsValue({ delayGetReword: record.delayGetReword })
      return true
    }
    trySeveralTimes(tryFunc, 500, 10)
  }

  // 检查更新配置和新增配置的参数
  checkParams = (params, isModify) => {
    if (typeof params !== 'object') {
      message.warn('参数类型错误')
      console.error('unexpect type of params: params=', params)
      return false
    }
    const { actID, startTime, endTime, roundProfit, recycleLimit, delayGetReword } = params
    if (isModify && (typeof actID !== 'string' || actID === '')) {
      message.warn('actID有误，请检查, actID=' + actID)
      return false
    }
    if (typeof startTime !== 'number' || typeof endTime !== 'number' ||
      startTime >= endTime || startTime <= 0 || endTime <= 0) {
      message.warn('时间范围选择有误，请检查')
      return false
    }
    if (!checkIsNumber(roundProfit) || parseInt(roundProfit) < 0) {
      message.warn('回收阀值有误，请检查, roundProfit=' + roundProfit)
      return false
    }

    if (!checkIsNumber(recycleLimit) || parseInt(recycleLimit) < 0) {
      message.warn('回收上限有误，请检查, recycleLimit=' + recycleLimit)
      return false
    }
    if (!checkIsNumber(delayGetReword) || parseInt(delayGetReword) < 0) {
      message.warn('延迟领奖时间有误，请检查, delayGetReword=' + delayGetReword)
      return false
    }
    return true
  }

  // 时间选择检查
  checkSelectTime = (tr) => {
    if (tr === null || tr === undefined || tr.length < 2) {
      message.warn('未选择时间')
      return
    }
    let stu = tr[0].format('mm:ss')
    let etu = tr[1].format('mm:ss')
    if (stu !== '00:00' || etu !== '00:00') { // 选择时间不是整点时弹出警告提示
      message.warn('警告：当前选择的时间不是整点', 5)
    }
  }

  render () {
    const { route } = this.props
    const { displayList, page, size } = this.props.model
    const { TabPane } = Tabs
    const { RangePicker } = DatePicker

    const modifyFormlayout = {
      labelCol: { span: 6 },
      wrapperCol: { span: 18 }
    }

    const columns = [
      { title: '序号', width: '5em', render: (text, record, index) => ((page - 1) * size + index + 1), align: 'center' },
      { title: '活动ID', dataIndex: 'actID', align: 'center' },
      { title: '开始时间', dataIndex: 'startTime', render: (v) => timeFormater(v), align: 'center' },
      { title: '结束时间', dataIndex: 'endTime', render: (v) => timeFormater(v), align: 'center' },
      { title: <Tooltip title='活动期间用户单局盈利多少开始回收(单位紫水晶)'>回收阀值</Tooltip>, dataIndex: 'roundProfit', align: 'center' },
      { title: <Tooltip title='整个活动期间回收上限(单位紫水晶)'>回收上限</Tooltip>, dataIndex: 'recycleLimit', align: 'center' },
      { title: <Tooltip title='本期活动结束后，多长时间内仍能领奖(单位秒)'>延迟领奖时间</Tooltip>, dataIndex: 'delayGetReword', align: 'center' },
      { title: '操作人', dataIndex: 'operator', align: 'center' },
      { title: '操作',
        align: 'center',
        render: (text, record) => {
          return (
            <>
              {/* 修改按钮 */}
              <a href='#' onClick={() => { this.onSelectModifyRow(record) }}>
                <FormOutlined style={{ color: '#1890ff', fontSize: '1.2em', marginRight: '1em' }} />
              </a>
              {/* 删除按钮 */}
              <Popconfirm placement='leftTop' title={`确定要删除 ID=${record.actID} 的活动配置吗?`}
                okType='danger' okText='删除' cancelText='取消' onConfirm={() => this.onComfirmDel(record.actID)}>
                <a href='#'><DeleteOutlined style={{ color: '#ff3535', fontSize: '1.2em' }} /></a>
              </Popconfirm>
            </>
          )
        } }
    ]

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey={1} type='card' size='large' onChange={(record) => this.onTagChange(record)}>
            {/* 查看、删除、修改配置标签页 */}
            <TabPane tab='配置列表' key={1}>
              <Row>
                <Col span={24}>
                  <Table columns={columns}
                    dataSource={displayList}
                    pagination={tableStyle}
                    onChange={(pagination) => { this.onTableChange(pagination) }}
                    rowKey={(record, index) => index}
                    size='small'
                  />
                </Col>
              </Row>
            </TabPane>
            {/* 新增配置标签页 */}
            <TabPane tab='添加配置' key={2}>
              <Row>
                <Col span={24}>
                  <Form
                    {...Inputlayout}
                    name='newConfForm'
                    ref={from => { this.newConfForm = from }}
                    initialValues={{ actID: '', startTime: '', endTime: '', roundProfit: '', recycleLimit: '', delayGetReword: '' }}
                    onFinish={(r) => { this.onComfirmAddConf(r) }}
                  >
                    <Form.Item label='时间范围' name='timeRange'>
                      <RangePicker format={'YYYY-MM-DD HH:mm:ss'} showTime={{ format: 'HH:mm:ss' }} onChange={(t) => this.checkSelectTime(t)} />
                    </Form.Item>
                    <Form.Item label='回收阀值' name='roundProfit'>
                      <Input />
                    </Form.Item>
                    <Form.Item label='回收上限' name='recycleLimit'>
                      <Input />
                    </Form.Item>
                    <Form.Item label='延迟领奖时间' name='delayGetReword'>
                      <Input />
                    </Form.Item>
                    <Button type='primary' htmlType='submit' loading={this.state.appending} >
                      确定新增
                    </Button>
                  </Form>
                </Col>
              </Row>
            </TabPane>

          </Tabs>
          {/* 修改配置模态框 */}
          <Modal title='活动配置修改'
            footer={null}
            visible={this.state.modelVisable}
            onCancel={() => { this.setState({ modelVisable: false }) }}>
            <Row>
              <Col span={24}>
                <Form
                  {...modifyFormlayout}
                  name='modifyForm'
                  ref={from => { this.modifyForm = from }}
                  initialValues={{ actID: '', startTime: '', endTime: '', roundProfit: '', recycleLimit: '', delayGetReword: '' }}
                  onFinish={(r) => { this.onComfirmModify(r) }}
                >
                  <Form.Item label='活动ID' name='actID'>
                    <Input disabled />
                  </Form.Item>
                  <Form.Item label='时间范围' name='timeRange'>
                    <RangePicker format={'YYYY-MM-DD HH:mm:ss'} showTime={{ format: 'HH:mm:ss' }} onChange={(t) => this.checkSelectTime(t)} />
                  </Form.Item>
                  <Form.Item label='回收阀值' name='roundProfit'>
                    <Input />
                  </Form.Item>
                  <Form.Item label='回收上限' name='recycleLimit'>
                    <Input />
                  </Form.Item>
                  <Form.Item label='延迟领奖时间' name='delayGetReword'>
                    <Input />
                  </Form.Item>
                  <Button type='primary' htmlType='submit' loading={this.state.updating} style={{ float: 'right' }}>
                    确定修改
                  </Button>
                </Form>
              </Col>
            </Row>
          </Modal>

        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default CowBossConfigNew
