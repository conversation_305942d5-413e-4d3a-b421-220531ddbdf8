import React, { Component } from 'react'
import { connect } from 'dva'
import { Form, Row, Col, Table, Popconfirm, Modal, Button, Select, Switch, Input, Typography, Tooltip, message } from 'antd'
import { tableStyle, timeFormater } from '@/utils/common'
import { udbAppIDOptions } from './common'

const namespace = 'joinChannelCheck'

@connect(({ joinChannelCheck }) => ({
  model: joinChannelCheck
}))

class JoinChannelCheckRuleList extends Component {
  state = {
    modalVisable: false,
    opType: 'update'
  }

  componentDidMount = () => {
    this.refreshList()
  }
  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  onComfirmDelete = (r) => {
    this.callModel('deletePlayModeRule', {
      params: {
        key: r.key
      },
      cbFunc: (ok) => {
        if (ok) {
          message.success('删除成功')
          this.refreshList()
        } else {
          message.warn('删除失败, 请稍后再试')
        }
        this.setState({ modalVisable: false })
      }
    })
  }

  onComfirmSubmit = (f) => {
    // 检查是否key重复
    const { ruleList } = this.props.model
    const { opType } = this.state
    let match = ruleList.find((item) => { return item.playMode === f.playMode && item.udbAppId === f.udbAppId })
    if (opType === 'add' && match) {
      message.warn('id已存在，请通过修改方式配置~')
      return
    }
    f.opUid = 0
    f.timestamp = 0
    this.callModel('updatePlayModeRule', {
      params: f,
      cbFunc: (ok) => {
        if (ok) {
          message.success('更新成功')
          this.refreshList()
        } else {
          message.warn('更新失败, 请稍后再试')
        }
        this.setState({ modalVisable: false })
      }
    })
  }

  refreshList = () => {
    this.callModel('getPlayModeRuleList')
  }
  createTooltip = (v, width = 10) => {
    return !v ? '-' : <Tooltip title={v}>
      <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', width: `${width}em` }}>{v}</div>
    </Tooltip>
  }

  render () {
    const { modalVisable, opType } = this.state
    const { ruleList, palyModeList } = this.props.model
    const { Text } = Typography
    const columns = [
      { title: 'id', dataIndex: 'key', sorter: (a, b) => { return a.key > b.key ? 1 : -1 }, width: '15em' },
      { title: '玩法', dataIndex: 'playMode', sorter: (a, b) => { return a.playMode > b.playMode ? 1 : -1 }, render: (v) => { return palyModeList.find((entry) => { return entry.gameType === v })?.name } },
      { title: 'APP', dataIndex: 'udbAppId', sorter: (a, b) => { return a.udbAppId > b.udbAppId ? 1 : -1 }, render: (v) => { return udbAppIDOptions.find((entry) => { return entry.value === v })?.label } },
      { title: '限制版本', dataIndex: 'version' },
      { title: '说明', dataIndex: 'remark', width: '16em', render: (v) => { return this.createTooltip(v, 15) } },
      { title: '开关', dataIndex: 'status', render: (v) => { return <Text type={['secondary', 'success'][v]}>{['关闭', '开启'][v]}</Text> } },
      { title: '更新时间', dataIndex: 'timestamp', sorter: (a, b) => { return a.timestamp > b.timestamp ? 1 : -1 }, render: (v) => timeFormater(v) },
      { title: '操作人UID', dataIndex: 'opUid' },
      { title: '操作',
        width: 100,
        fixed: 'right',
        render: (v, r) => {
          return <div>
            <a style={{ marginRight: '1em', color: '#1890ff' }} onClick={() => { this.setState({ opType: 'update', modalVisable: true }); this.formRef.setFieldsValue(r) }} >修改</a>
            <Popconfirm title='确认删除这条规则么' onConfirm={(v) => { this.onComfirmDelete(r) }}><a style={{ color: '#ff4343' }}>删除</a></Popconfirm>
          </div>
        } }
    ].map((item) => { item.align = 'center'; return item })

    return (
      <div>
        <Row>
          <text style={{ color: '#339999ba' }}>
            简介：这里配置的规则用于限制低版本的移动端进入特定的玩法频道。<br />
            例如可实现 “限制Yo语音1.5版本及以下用户无法进入玩法为相亲的交友频道”。版本为1.5.0，则限制1.5.0及以下版本。
          </text>
        </Row>
        <Row style={{ margin: '1em' }}>
          <Button onClick={() => { this.formRef.resetFields(); this.setState({ modalVisable: true, opType: 'add' }) }}>新增规则</Button>
        </Row>
        <Row >
          <Col span={24}>
            <Table columns={columns} dataSource={ruleList} size='small' pagination={tableStyle} scroll={{ x: 'max-content' }}
              rowKey={record => record.uid} />
          </Col>
        </Row>
        <Modal title={opType === 'update' ? '更新规则' : '新建规则'} forceRender visible={modalVisable} cancelText='取消' okText='确认'
          onOk={() => this.formRef.submit()} onCancel={() => this.setState({ modalVisable: false })} >
          <Form onFinish={this.onComfirmSubmit} initialValues={{ playMode: 0, udbAppId: 'yymand', status: 1 }} ref={form => { this.formRef = form }} labelCol={{ span: 5 }} >
            <Form.Item name='playMode' label='玩法' tooltip='目前只支持交友业务的玩法'>
              <Select options={palyModeList.filter((item) => { return item.gameType !== 20 && item.gameType !== 22 /* 暂时隐藏这两个 */ }).map((v) => { return { label: `${v.name}_${v.gameType}`, value: v.gameType } })} disabled={opType === 'update'} />
            </Form.Item >
            <Form.Item name='udbAppId' label='终端' tooltip='控制本规则对哪个APP生效' >
              <Select options={udbAppIDOptions} disabled={opType === 'update'} />
            </Form.Item>
            <Form.Item name='version' label='限制版本' rules={[{ required: true, message: '请填写版本信息' }]} tooltip='规则生效后只允许大于该版本的客户端进入对应玩法的频道'>
              <Input placeholder='参考格式： 1.2.3' />
            </Form.Item>
            <Form.Item name='status' label='开关' tooltip='控制规则是否生效' getValueFromEvent={(v) => { return v ? 1 : 0 }} valuePropName='checked'>
              <Switch defaultChecked checkedChildren='开启' unCheckedChildren='关闭' />
            </Form.Item>
            <Form.Item name='remark' label='规则描述' rules={[{ required: true, message: '请填写规则描述~' }]} tooltip='请留下备注信息～' >
              <Input />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    )
  }
}

export default JoinChannelCheckRuleList
