import { getLists, update } from './api'
import { message } from 'antd'

export default {
  namespace: 'auctionHotPush',

  state: {
    list: []
  },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      console.log('payload:', payload)
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getLists, payload)
      console.log('list:', list)
      yield put({
        type: 'updateList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * updateItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(update, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed!' + msg)
      }
    }
  }
}
