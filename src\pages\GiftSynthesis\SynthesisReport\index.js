import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import RFIReportComponent from './component/daily'
import DetailReportComponent from './component/detail'
import RealTimeComponent from './component/realtime'

const TabPane = Tabs.TabPane

class SynthesisReportComponent extends Component { // 默认页面组件，不需要修改
  state = { activeKey: '1' }
  onTabChange = key => {
    this.setState({ activeKey: key }) // render tab pane
  }

  render () {
    const { route } = this.props
    const { activeKey } = this.state
    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs onChange={this.onTabChange} type='card'>

          <TabPane tab='日报' key='1'>
            {activeKey === '1' ? <RFIReportComponent {...this.state} /> : ''}
          </TabPane>

          <TabPane tab='实时信息' key='2'>
            {activeKey === '2' ? <RealTimeComponent {...this.state} /> : ''}
          </TabPane>

          <TabPane tab='兑换明细' key='3'>
            {activeKey === '3' ? <DetailReportComponent {...this.state} /> : ''}
          </TabPane>

        </Tabs>
      </PageHeaderWrapper>
    )
  }
}

export default SynthesisReportComponent // 保证唯一
