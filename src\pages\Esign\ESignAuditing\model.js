import { getLists, update, getProfile, getFileUrl, cancelContract } from './api'
import { message } from 'antd'

export default {
  namespace: 'esignAuditing', // 只有这里需要修改

  state: {
    list: [],
    profile: {},
    idCard: {}
  },

  reducers: {
    updateList (state, { payload, status }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload,
        status: status
      }
    },
    updateProfile (state, { payload }) {
      return {
        ...state,
        profile: payload
      }
    },
    updateFileUrl (state, { payload }) {
      return {
        ...state,
        idCard: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { list, status, msg } } = yield call(getLists, payload)

      yield put({
        type: 'updateList',
        payload: Array.isArray(list) ? list : [],
        status: status
      })

      if (status !== 0) {
        message.error('failed:' + msg)
      }
    },

    * auditItem ({ payload, getListParam }, { call, put }) {
      const { data: { status, msg } } = yield call(update, payload)
      console.log('getListParam:', getListParam)
      if (status === 0) {
        message.success('操作成功')
        yield put({
          type: 'getList',
          payload: getListParam
        })
      } else {
        message.error('操作失败:' + msg, 15)
      }
    },

    * cancelContract ({ payload, getListParam }, { call, put }) {
      const { data: { status, msg } } = yield call(cancelContract, payload)
      console.log('getListParam:', getListParam)
      if (status === 0) {
        message.success('操作成功')
        yield put({
          type: 'getList',
          payload: getListParam
        })
      } else {
        message.error('操作失败:' + msg)
      }
    },

    * getProfile ({ payload }, { call, put }) {
      const { data: { profile, status, msg } } = yield call(getProfile, payload)
      console.log('profile:', profile, status)
      if (status === 0) {
        yield put({
          type: 'updateProfile',
          payload: profile
        })
      } else {
        message.error('failed:' + msg)
      }
    },

    * getFileUrl ({ payload }, { call, put }) {
      const { data: { token, status, msg } } = yield call(getFileUrl, payload)
      console.log(token, status)
      if (status === 0) {
        yield put({
          type: 'updateFileUrl',
          payload: token
        })
      } else {
        message.error('failed:' + msg)
      }
    }
  }
}
