import request from '@/utils/request'

export function getLists () {
  let url = `/white_list/get_message_white_list`
  return request(url, { jsonp: true })
}

export function whiteListAddMult (uidList) {
  let form = 'uid_text=' + uidList
  return request(`/white_list/message_white_add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: form
  })
}
// 单个或批量删除消息中心白名单
export function whiteListDel (uids) {
  let form = 'uid_text=' + uids
  return request(`/white_list/message_white_list_batch_del`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: form
  })
}
