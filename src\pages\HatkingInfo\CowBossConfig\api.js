import request from '@/utils/request'
import { stringify } from 'qs'

let badResp = {
  data: {
    status: -1,
    message: '参数错误'
  }
}

export function getCurActConfig () {
  let url = `/fountain/cow_boss/get_cur_act_config`
  return request(url)
}

export function delActConfig (actID) {
  if (typeof actID !== 'string') {
    console.error('delActConfig() get unexpect actID: ', actID)
    return badResp
  }
  let url = `/fountain/cow_boss/del_act_config`
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: stringify({ actID: actID })
  })
}

export function addAupdateActConfig (params) {
  let url = `/fountain/cow_boss/add_update_act_config`
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: stringify(params)
  })
}
