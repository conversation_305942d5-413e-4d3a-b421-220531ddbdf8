import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import { connect } from 'dva'

import ArkDailyCrucialInfoComponent from '../components/ark_daily_crucial_info'
import ArkDailyBetRangeStatsComponent from '../components/ark_daily_bet_range_stats'
import ArkDailyBetPeriodStatsComponent from '../components/ark_daily_bet_period_stats'
import ArkDailyBetPositionStatsComponent from '../components/ark_daily_bet_position_stats'
import ArkDailyRetainUseInfoComponent from '../components/ark_daily_user_retain_info'
import ArkDailyBetTop10InfoComponent from '../components/ark_daily_bet_top_10_info'

const namespace = 'arkJy' // model 的 namespace
const TabPane = Tabs.TabPane

@connect(({ arkJy }) => ({ // model 的 namespace
  model: arkJy // model 的 namespace
}))
class ArkJyIndex extends Component { // 默认页面组件，不需要修改
  /** *****************************非活动流程配置文件更新与获取****************************************************************/
  updateBakConfig = value => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/upsetBakConfigItem`,
      payload: value
    })
  }

  tabOnChange = type => activityKey => {
    console.log(type, activityKey)
    if (type !== undefined || type != null) {
      activityKey = type
    }
  }

  /** *******************************页面布局*************************************************************/
  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs onChange={this.tabOnChange()} type='card'>
          <TabPane tab='关键信息' key='1'>
            <ArkDailyCrucialInfoComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='参与用户区间分布' key='2'>
            <ArkDailyBetRangeStatsComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='分时段监控' key='3'>
            <ArkDailyBetPeriodStatsComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='参与位置分布' key='4'>
            <ArkDailyBetPositionStatsComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='用户留存' key='5'>
            <ArkDailyRetainUseInfoComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='TOP10用户' key='6'>
            <ArkDailyBetTop10InfoComponent modelName={namespace} model={this.props.model} />
          </TabPane>
        </Tabs>
      </PageHeaderWrapper>
    )
  }
}

export default ArkJyIndex // 保证唯一
