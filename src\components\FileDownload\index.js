import React, { Component } from 'react'

class FileDownload extends Component {
  constructor (props) {
    super(props)

    const { value } = props

    console.log(value, value instanceof Promise)
    if (value instanceof Promise) {
      this.state = { value: '' }
      value.then(resp => {
        // console.log(resp)
        this.setState({ value: resp || '' })
      })
    } else {
      this.state = { value }
    }
  }

  render () {
    const { value } = this.state

    return (
      <a target='__blank' href={value}>下载</a>
    )
  }
}

export default FileDownload
