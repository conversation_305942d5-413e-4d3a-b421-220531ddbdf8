import request from '@/utils/request'
import { stringify } from 'qs'

// 常规任务配置列表
export function getMagicConfigList (params) {
  return request(`/brand_routine_boss/stage_config_list?${stringify(params)}`)
}

// 常规任务配置
export function updateMagicConfig (params) {
  return request(`/brand_routine_boss/stage_config_update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

// 更新常规任务配置
export function deleteMagicConfig (params) {
  return request(`/brand_routine_boss/stage_config_delete?${stringify(params)}`)
}
