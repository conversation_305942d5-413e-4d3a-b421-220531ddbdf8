import request from '@/utils/request'
import { stringify } from 'qs'

export function getCompereSummaryLists (params) {
  return request(`/aggregator/get_interact_compere_summary?${stringify(params)}`)
}

export function getChannelSummaryLists (params) {
  return request(`/aggregator/get_interact_channel_summary?${stringify(params)}`)
}

export function getAllChannelTypeSummaryLists (params) {
  return request(`/aggregator/get_all_channel_type_summary?${stringify(params)}`)
}
