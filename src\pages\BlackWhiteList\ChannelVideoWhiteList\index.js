import React, { Component } from 'react'
import { connect } from 'dva'
import { Tabs, Card, Input, Form, Row, Col, Button, Table, Popconfirm } from 'antd'
import { tableStyle, Inputlayout } from '@/utils/common'
import { DeleteOutlined } from '@ant-design/icons'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'

const namespace = 'channelVideoWhiteList'

@connect(({ channelVideoWhiteList }) => ({
  model: channelVideoWhiteList
}))

class ChannelVideoWhiteList extends Component {
  constructor (props) {
    super(props)
    this.state = { searchASID: 0, searchSID: 0, searchResult: [], searchDone: false }
    this.refreshVidioComperList()
  }

  // 标签页发生切换
  onTagChange = (record) => {
    if (record === '1') { // 切换到'添加标签页'
      this.initForm()
    }
    if (record === '2') { // 切换到'频道视频白名单标签页'
      this.refreshVidioComperList()
    }
  }

  // 清空输入框
  initForm = () => {
    this.setState({ newSid: '' })
    if (this.formRef) {
      this.formRef.resetFields()
    }
  }

  // 获取/刷新频道视频白名单列表数据
  refreshVidioComperList = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getWhiteListData`,
      payload: null
    })
  }

  // 点击 添加标签页-添加按钮
  onAddBtnClick = () => {
    const { newSid } = this.state
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/addWhiteListBySid`,
      payload: { newSid: newSid, callback: this.initForm }
    })
  }

  handleSearch = e => {
    const { model: { displayData } } = this.props
    // const { displayData } = this.props.model
    const { searchSID, searchASID } = this.state
    let dataSource = displayData
    const searchSIDInt = parseInt(searchSID || 0, 10)
    const searchASIDInt = parseInt(searchASID || 0, 10)
    if (searchSIDInt === 0 && searchASIDInt === 0) {
      this.setState({ searchDone: false })
      return
    }
    if (searchSIDInt > 0) {
      dataSource = dataSource.filter(v => v.sid === searchSIDInt)
    }
    if (searchASIDInt > 0) {
      dataSource = dataSource.filter(v => v.asid === searchASIDInt)
    }
    console.log('dataSource', dataSource)
    this.setState({ searchResult: dataSource, searchDone: true })
  }
  // '添加'标签页html代码
  addBlackLIstHtml = () => {
    const { updating } = this.props.model
    return (
      <div>
        <Row>
          <Col span='24'>
            <Form {...Inputlayout}
              initialValues={{ sid: '' }}
              ref={form => { this.formRef = form }}
            >
              <Form.Item label='顶级频道 [sid]:' name='sid'>
                <Input id='addWhiteListInput' placeholder='如50041789' allowClear
                  onChange={e => this.setState({ newSid: e.target.value })}
                  maxLength={20}
                />
              </Form.Item>
              <Form.Item>
                <Button type='primary' htmlType='submit' loading={updating} onClick={() => this.onAddBtnClick()}>
                  添加
                </Button>
              </Form.Item>
            </Form>
          </Col>
        </Row>
      </div>
    )
  }

  // 确认删除选中的白名单
  onComfirmDel = (sid) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/delWhiteListByUid`,
      payload: sid
    })
  }

  // 频道视频白名单-删除操作html代码
  deleteWhiteListHtml = (record) => {
    const { sid } = record
    let tmpStr = `确定要将 [sid=${sid}] 从频道视频白名单删除吗？`
    return (
      <Popconfirm placement='bottom' title={tmpStr}
        okType='danger' okText='删除' cancelText='取消' onConfirm={() => this.onComfirmDel(sid)}>
        <a href='#'><DeleteOutlined style={{ color: '#ff3535', fontSize: '1.2em' }} /></a>
      </Popconfirm>
    )
  }

  // ‘白名单展示列表标签页’html代码
  displayWhiteListHtml = () => {
    const columns = [
      { title: '#', dataIndex: 'idx' },
      { title: '短位频道', dataIndex: 'asid' },
      { title: '频道号', dataIndex: 'sid' },
      { title: '操作', render: (record) => this.deleteWhiteListHtml(record) }
    ]
    const { displayData } = this.props.model
    const { searchDone, searchResult } = this.state
    return (
      <Row >
        <Col span={24}>
          短位频道:
          <Input style={{ width: 200, marginLeft: 10, marginRight: 10 }} placeholder='搜索短位频道' onChange={e => this.setState({ searchASID: e.target.value })} /> {/* 搜索按钮 */}
          频道号:
          <Input style={{ width: 200, marginLeft: 10, marginRight: 10 }} placeholder='搜索频道号' onChange={e => this.setState({ searchSID: e.target.value })} /> {/* 搜索按钮 */}
          <Button type='primary' style={{ marginLeft: 10 }} onClick={this.handleSearch}>搜索</Button>
          <Table rowKey={(record, index) => index} columns={columns} dataSource={searchDone ? searchResult : displayData} size='small' pagination={tableStyle} />
        </Col>
      </Row>
    )
  }

  render () {
    const { TabPane } = Tabs
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='2' onChange={(record) => this.onTagChange(record)} type='card' size='large'>
            <TabPane tab='添加' key='1'>
              {this.addBlackLIstHtml()}
            </TabPane>
            <TabPane tab='频道视频白名单' key='2'>
              {this.displayWhiteListHtml()}
            </TabPane>
            abc
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default ChannelVideoWhiteList
