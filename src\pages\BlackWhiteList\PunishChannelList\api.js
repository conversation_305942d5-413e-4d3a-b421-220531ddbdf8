import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/black_list/punish_channel_get?${stringify(params)}`)
}

export function add (params) {
  return request(`/black_list/punish_channel_add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function remove (params) {
  return request(`/black_list/punish_channel_del`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
