import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Table, Divider, Button, Select, Modal, Input, Popconfirm, InputNumber } from 'antd'
import { Form } from '@ant-design/compatible'
import { connect } from 'dva'
import moment from 'moment'
import { SearchOutlined } from '@ant-design/icons'
import TextArea from 'antd/lib/input/TextArea'

const Option = Select.Option
const namespace = 'propsDetailConfig'
const EditableContext = React.createContext()

const EditableRow = ({ form, index, ...props }) => (
  <EditableContext.Provider value={form}>
    <tr {...props} />
  </EditableContext.Provider>
)

const tagOptions = [{ value: '', label: '无' }, { value: 'NEW', label: 'NEW' }]
const statusOptions = ['不可见', '可见']
let classOptions = []
const EditableFormRow = Form.create()(EditableRow)
class EditableCell extends React.Component {
  state = { editing: false }

  toggleEdit = () => {
    const editing = !this.state.editing
    this.setState({ editing }, () => {
      if (editing) {
        this.input.focus()
      }
    })
  }

  save = e => {
    const { record, handleSave } = this.props
    this.form.validateFields((error, values) => {
      if (error && error[e.currentTarget.id]) {
        return
      }
      this.toggleEdit()
      handleSave({ ...record, ...values })
    })
  }

  renderCell = form => {
    this.form = form
    const { children, dataIndex, record, title } = this.props
    const { editing } = this.state
    return editing ? (
      <Form.Item style={{ margin: 0 }}>
        {form.getFieldDecorator(dataIndex, {
          rules: [
            {
              required: true,
              message: `${title} is required.`
            }
          ],
          initialValue: record[dataIndex]
        })(<Input ref={node => (this.input = node)} onPressEnter={this.save} onBlur={this.save} />)}
      </Form.Item>
    ) : (
      <div
        className='editable-cell-value-wrap'
        style={{ paddingRight: 24 }}
        onClick={this.toggleEdit}
      >
        {children}
      </div>
    )
  }

  render () {
    const {
      editable,
      dataIndex,
      title,
      record,
      index,
      handleSave,
      children,
      ...restProps
    } = this.props
    return (
      <td {...restProps}>
        {editable ? (
          <EditableContext.Consumer>{this.renderCell}</EditableContext.Consumer>
        ) : (
          children
        )}
      </td>
    )
  }
}

@connect(({ propsDetailConfig }) => ({
  model: propsDetailConfig
}))
class PropsDetailConfig extends Component {
  state = { editingKey: '', visible: false, classVisible: false, selectedRowKeys: [], selectedProps: null, selectedClass: null, classSearch: 0, editRow: null, isUpdate: false, searchResult: [], searchDone: false }
  pagination = { pageSizeOptions: ['20', '50', '100'], defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  columns = [
    { title: '奖品ID',
      align: 'center',
      dataIndex: 'prizeId',
      render: (text, record) => !this.state.isUpdate && this.isEditing(record) ? <InputNumber min={0} defaultValue={text} onChange={(value) => { this.handleRowChange(record, 'prizeId', value) }} /> : text
    },
    { title: '类型ID', align: 'center', dataIndex: 'typeId' },
    { title: '类型名称',
      align: 'center',
      dataIndex: 'typeName',
      render: (text, record) => this.isEditing(record) ? <Button onClick={e => this.setState({ classVisible: true, editRow: record, selectedRowKeys: [] })}>{text}</Button> : text
    },
    { title: '礼物ID',
      align: 'center',
      dataIndex: 'propsId',
      render: (text, record) => this.isEditing(record) ? <Button onClick={e => this.setState({ visible: true, editRow: record, selectedRowKeys: [] })}>{text}</Button> : text
    },
    { title: '礼物名称', align: 'center', dataIndex: 'propsName' },
    { title: '礼物单价/紫水晶', align: 'center', dataIndex: 'propsPrice' },
    { title: '权重',
      dataIndex: 'weight',
      align: 'center',
      render: (text, record) => this.isEditing(record) ? <InputNumber min={0} defaultValue={text} onChange={(value) => { this.handleRowChange(record, 'weight', value) }} /> : text
    },
    { title: '标签',
      dataIndex: 'tag',
      align: 'center',
      render: (text, record) => this.isEditing(record) ? <Select onChange={(value) => { this.handleRowChange(record, 'tag', value) }} defaultValue={text}>{tagOptions.map((item, index) => <Option key={index} value={item.value}>{item.label}</Option>)}</Select> : (text.length > 0 ? text : '无')
    },
    { title: '状态',
      dataIndex: 'status',
      align: 'center',
      render: (text, record) => this.isEditing(record) ? <Select onChange={(value) => { this.handleRowChange(record, 'status', value) }} defaultValue={text}>{statusOptions.map((item, index) => <Option key={index} value={index}>{item}</Option>)}</Select> : statusOptions[text]
    },
    { title: '来源',
      dataIndex: 'source',
      align: 'center',
      render: (text, record) => this.isEditing(record) ? <TextArea defaultValue={text} maxlength='15' showCount onChange={(e) => { this.handleRowChange(record, 'source', e.target.value) }} /> : text
    },
    { title: '修改人', dataIndex: 'operator', align: 'center' },
    { title: '修改时间', dataIndex: 'timestamp', align: 'center', render: text => this.dateString(text) },
    { title: '操作',
      align: 'center',
      render: (text, record) => {
        const { editingKey } = this.state
        const editable = this.isEditing(record)
        return editable ? (
          <span>
            <EditableContext.Consumer>
              {form => (<a onClick={() => this.save(form, record)} style={{ marginRight: 8 }}>保存</a>)}
            </EditableContext.Consumer>
            <Popconfirm title='Sure to cancel?' onConfirm={() => this.cancel(record.index)}><a>取消</a></Popconfirm>
          </span>
        ) : (
          <span>
            <a disabled={editingKey !== ''} onClick={() => this.edit(record)} style={{ marginRight: 8 }}>编辑</a>
            <Popconfirm title='确认删除?' onConfirm={this.handleDel(record.prizeId)}><a href=''>删除</a></Popconfirm>
          </span>
        )
      }
    }
  ]

  propsColumns = [
    { title: '道具ID',
      align: 'center',
      width: 100,
      dataIndex: 'id',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ width: 300, padding: 8, borderRadius: 6, background: '#FFF', boxShadow: '0 1px 6px' }}>
          <Input style={{ width: 140, marginRight: 10 }} value={selectedKeys[0]} onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])} onPressEnter={this.handleSearchPropsId(selectedKeys, confirm)} />
          <Button style={{ marginRight: 5 }} type='primary' onClick={this.handleSearchPropsId(selectedKeys, confirm)}>搜索</Button>
          <Button onClick={this.handleResetPropsId(clearFilters)}>重置</Button>
        </div>
      ),
      onFilter: (value, record) => record.id.toString().toLowerCase().includes((value.toLowerCase())),
      filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#108ee9' : '#aaa' }} />,
      render: text => {
        text = text.toString()
        const { searchTextPropsId } = this.state
        return searchTextPropsId ? (
          <span>
            {text.split(new RegExp(`(?<=${searchTextPropsId})|(?=${searchTextPropsId})`, 'i')).map((fragment, i) => (
              fragment.toLowerCase() === searchTextPropsId.toLowerCase() ? <span key={i} style={{ color: '#f50' }}>{fragment}</span> : fragment
            ))}</span>) : text
      } },
    { title: '名称',
      align: 'center',
      width: 100,
      dataIndex: 'name',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ width: 300, padding: 8, borderRadius: 6, background: '#FFF', boxShadow: '0 1px 6px' }}>
          <Input style={{ width: 140, marginRight: 10 }} value={selectedKeys[0]} onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])} onPressEnter={this.handleSearch(selectedKeys, confirm)} />
          <Button style={{ marginRight: 5 }} type='primary' onClick={this.handleSearch(selectedKeys, confirm)}>搜索</Button>
          <Button onClick={this.handleReset(clearFilters)}>重置</Button>
        </div>
      ),
      onFilter: (value, record) => record.name.toLowerCase().includes((value.toLowerCase())),
      filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#108ee9' : '#aaa' }} />,
      render: text => {
        const { searchText } = this.state
        return searchText ? (
          <span>
            {text.split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i')).map((fragment, i) => (
              fragment.toLowerCase() === searchText.toLowerCase() ? <span key={i} style={{ color: '#f50' }}>{fragment}</span> : fragment
            ))}</span>) : text
      }
    },
    { title: '单价', align: 'center', width: 100, dataIndex: 'price' },
    { title: '货币类型', align: 'center', width: 100, dataIndex: 'priceType' },
    { title: '道具类型', align: 'center', width: 150, dataIndex: 'type' },
    { title: '生效日期', align: 'center', width: 180, dataIndex: 'startTime', render: text => moment.unix(text / 1000).format('YYYY-MM-DD HH:mm:ss') },
    { title: '失效日期', align: 'center', dataIndex: 'endTime', render: text => moment.unix(text / 1000).format('YYYY-MM-DD HH:mm:ss') }
  ]

  classColumns = [
    { title: '分类ID', dataIndex: 'typeId', align: 'center' },
    { title: '分类名称', dataIndex: 'typeName', align: 'center' }
  ]

  isEditing = record => record.index === this.state.editingKey

  edit (record) {
    this.toClassEdit()
    this.toPropsEdit()
    this.setState({ editingKey: record.index, editRow: record, isUpdate: true })
  }

  save (form, record) {
    const { dispatch } = this.props
    const { editRow } = this.state
    form.validateFields((err, values) => {
      console.log('save', record, editRow)
      if (!err) {
        dispatch({
          type: `${namespace}/update`,
          payload: editRow
        })
        form.resetFields()
        this.setState({ editingKey: '', editRow: null, isUpdate: false })
      }
    })
  }

  cancel = (key) => {
    const { isUpdate } = this.state
    if (isUpdate) {
      this.setState({ editingKey: '', editRow: null, isUpdate: false })
      return
    }
    var { dispatch, model: { list } } = this.props
    for (var i = 0; i < list.length; i++) {
      if (list[i].index === key) {
        list.splice(i, 1)
        break
      }
    }

    dispatch({
      type: `${namespace}/updateList`,
      payload: list
    })
    this.forceUpdate() // 强制刷新
    this.setState({ editingKey: '', editRow: null })
  }

  dateString (timestamp) {
    if (timestamp === 0) {
      return '-'
    }
    return moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss')
  }

  handleAdd = () => {
    var { dispatch, model: { list } } = this.props
    let cpList = $.extend([], true, list)
    let count = Array.isArray(list) ? list.length : 0
    const newRow = {
      index: count,
      prizeId: 0,
      typeName: '常规',
      propsId: 0,
      tag: '无',
      weight: 0,
      status: 0,
      timestamp: 0
    }
    if (count === 0) {
      cpList = [newRow]
    } else {
      cpList.push(newRow)
    }
    dispatch({
      type: `${namespace}/updateList`,
      payload: cpList
    })

    this.toClassEdit()
    this.toPropsEdit()
    this.setState({ editingKey: count, editRow: newRow })
    this.forceUpdate() // 强制刷新
  }

  // 删除
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { prizeId: key }
    dispatch({
      type: `${namespace}/delete`,
      payload: data
    })
  }

  // 编辑行信息
  handleRowChange = (record, field, value) => {
    var { dispatch, model: { list } } = this.props
    console.log('handleRowChange', record, field, value)
    if (value === undefined) {
      record[field] = value
      list[record.index][field] = value
      return
    }
    list[record.index][field] = value
    record[field] = value
    dispatch({
      type: `${namespace}/updateList`,
      payload: list
    })
    this.setState({ editRow: record })
    this.forceUpdate() // 强制刷新
  }

  // 本地搜索
  handleSearch = (selectedKeys, confirm) => () => {
    const { dispatch, model: { localPropsList } } = this.props
    let list = []
    // 根据条件筛选
    for (var i = 0; i < localPropsList.length; i++) {
      if (localPropsList[i].name.toString().toLowerCase().includes(selectedKeys[0].toLowerCase())) {
        list = [...list, localPropsList[i]]
      }
    }
    dispatch({
      type: `${namespace}/updatePropsList`,
      payload: Array.isArray(list) ? list : []
    })
    confirm()
    this.setState({ searchText: selectedKeys[0] })
  }
  // 重置搜索
  handleReset = clearFilters => () => {
    const { dispatch, model: { localPropsList } } = this.props
    dispatch({
      type: `${namespace}/updatePropsList`,
      payload: localPropsList
    })
    clearFilters()
    this.setState({ searchText: '' })
  }
  // 根据PropsId搜索
  handleSearchPropsId = (selectedKeys, confirm) => () => {
    const { dispatch, model: { localPropsList } } = this.props
    let list = []
    for (var i = 0; i < localPropsList.length; i++) {
      if (localPropsList[i].id.toString().toLowerCase().includes(selectedKeys[0].toLowerCase())) {
        list = [...list, localPropsList[i]]
      }
    }
    dispatch({
      type: `${namespace}/updatePropsList`,
      payload: Array.isArray(list) ? list : []
    })
    confirm()
    this.setState({ searchTextPropsId: selectedKeys[0] })
  }
  // 重置 PropsId搜索条件
  handleResetPropsId = clearFilters => () => {
    const { dispatch, model: { localPropsList } } = this.props
    dispatch({
      type: `${namespace}/updatePropsList`,
      payload: localPropsList
    })
    clearFilters()
    this.setState({ searchTextPropsId: '' })
  }

  // 加载礼物配置列表
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getPropsDetailList`
    })

    this.toClassEdit()
  }

  // 礼物类型查询下拉框
  renderClassSelect = () => {
    classOptions = []
    const { model: { classList } } = this.props
    for (let i = 0; i < classList.length; i++) {
      classOptions.push(<Option key={i} value={classList[i].typeId}>{classList[i].typeName}</Option>)
    }
  }

  // 更新营收礼物配置
  handlePropsSelect = () => {
    const { dispatch, model: { list } } = this.props
    const { selectedProps, editRow } = this.state
    if (selectedProps == null || editRow == null) {
      return
    }

    var newList = list.map(item => {
      if (item.index === editRow.index) {
        item.propsId = selectedProps.id
        item.propsName = selectedProps.name
        item.propsPrice = selectedProps.price
        editRow.propsId = selectedProps.id
        editRow.propsName = selectedProps.name
        editRow.propsPrice = selectedProps.price
      }
      return item
    })

    dispatch({
      type: `${namespace}/updateList`,
      payload: newList
    })

    this.setState({ editRow: editRow, visible: false, selectedProps: null, selectedRowKeys: [] })
    this.forceUpdate() // 强制刷新
  }

  // 更新礼物类型配置
  handleClassSelect = () => {
    const { dispatch, model: { list } } = this.props
    const { selectedClass, editRow } = this.state
    if (selectedClass == null || editRow == null) {
      return
    }
    var newList = list.map(item => {
      if (item.index === editRow.index) {
        item.typeId = selectedClass.typeId
        item.typeName = selectedClass.typeName
        editRow.typeId = selectedClass.typeId
        editRow.typeName = selectedClass.typeName
      }
      return item
    })

    dispatch({
      type: `${namespace}/updateList`,
      payload: newList
    })
    this.setState({ editRow: editRow, classVisible: false, selectedClass: null, selectedRowKeys: [] })
    this.forceUpdate() // 强制刷新
  }

  handleSelectPropsChange = (selectedRowKeys, record) => {
    this.setState({ selectedRowKeys: selectedRowKeys, selectedProps: record[0] })
  }

  handleSelectClassChange = (selectedRowKeys, record) => {
    this.setState({ selectedRowKeys: selectedRowKeys, selectedClass: record[0] })
  }

  toClassEdit = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getPropsClassList`
    })
  }

  toPropsEdit = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getPropsConfigList`
    })
  }

  onClassChange = (value) => {
    this.setState({ classSearch: value })
  }

  onClassSearch = () => {
    const { model: { list } } = this.props
    const { classSearch } = this.state

    var dataSource = list
    if (classSearch.key !== 0) {
      dataSource = dataSource.filter(data => data.typeId === classSearch.key)
    }

    this.setState({ searchResult: dataSource, searchDone: true })
  }

  render () {
    const { route, model: { list, classList, propsList } } = this.props
    const { visible, classVisible, selectedRowKeys, searchResult, searchDone } = this.state

    const components = {
      body: {
        row: EditableFormRow,
        cell: EditableCell
      }
    }

    const columns = this.columns.map(col => {
      if (!col.editable) {
        return col
      }
      return {
        ...col,
        onCell: record => ({
          record,
          editable: col.editable,
          dataIndex: col.dataIndex,
          title: col.title,
          handleSave: this.handleSave
        })
      }
    })

    this.renderClassSelect()
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <div>
            礼物类型
            <Select labelInValue defaultValue={{ key: 0 }} style={{ marginLeft: 10, width: 120 }} onChange={this.onClassChange}>
              <Option value={0}>全部</Option>
              {classOptions}
            </Select>
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onClassSearch}>查询</Button>
            <Button style={{ marginLeft: 5 }} onClick={this.handleAdd} type='primary'>新增</Button>
          </div>
          <Divider />
          <Table components={components} rowClassName={() => 'editable-row'} pagination={this.pagination} size='small' dataSource={searchDone ? searchResult : list} columns={columns} rowKey={(record, index) => index} />
        </Card>
        <div>
          <Modal onOk={this.handlePropsSelect} okText='确定' cancelText='取消' okButtonProps={{ disabled: selectedRowKeys.length === 0 }} width={1100} title='选择营收礼物' visible={visible} onCancel={e => this.setState({ visible: false })}>
            <Table bordered rowSelection={{ selectedRowKeys, onChange: this.handleSelectPropsChange, type: 'radio' }} pagination={false} scroll={{ y: 340 }} dataSource={propsList} size='small' columns={this.propsColumns} rowKey={(record, index) => index} />
          </Modal>
          <Modal onOk={this.handleClassSelect} okText='确定' cancelText='取消' okButtonProps={{ disabled: selectedRowKeys.length === 0 }} width={1100} title='选择礼物类型' visible={classVisible} onCancel={e => this.setState({ classVisible: false })}>
            <Table bordered rowSelection={{ selectedRowKeys, onChange: this.handleSelectClassChange, type: 'radio' }} pagination={false} scroll={{ y: 340 }} dataSource={classList} size='small' columns={this.classColumns} rowKey={(record, index) => index} />
          </Modal>
        </div>
      </PageHeaderWrapper>
    )
  }
}

export default PropsDetailConfig
