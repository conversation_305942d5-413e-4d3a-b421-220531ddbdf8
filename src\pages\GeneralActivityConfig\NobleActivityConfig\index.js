import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, message, Table, Row, Col, Button, Input, Form, Modal, Typography, Space, Popconfirm, Divider, InputNumber, Select } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import { ArrayEditor, CopyPasteButton } from '@/components/SimpleComponents'
import { timeFormater } from '@/utils/common'
import { SimpleTimePicker } from '@/components/SimpleComponents/timePicker'
import ApprovalButton from '@/components/ApprovalButton'
import moment from 'moment'
const namespace = 'nobleActivityConfig'
const { Text, Link } = Typography
// const timeNow = moment().unix()
const defaultFromValue = {
  id: moment().format('YYYYMMDD'),
  begin: moment().add(1, 'days').startOf('day').unix(),
  end: moment().add(2, 'days').startOf('day').unix(),
  iconBegin: moment().add(1, 'days').startOf('day').unix(),
  iconEnd: moment().add(2, 'days').startOf('day').unix(),
  rewardBegin: moment().add(1, 'days').startOf('day').unix(),
  rewardEnd: moment().add(2, 'days').startOf('day').unix(),
  sidList: [],
  consumeList: [],
  needValueList: []
}

const rankOptions = [
  { label: '骑士', value: 100 },
  { label: '男爵', value: 101 },
  { label: '子爵', value: 102 },
  { label: '伯爵', value: 103 },
  { label: '侯爵', value: 104 },
  { label: '公爵', value: 105 },
  { label: '国王', value: 106 },
  { label: '守护神', value: 107 }
]

@connect(({ nobleActivityConfig }) => ({
  model: nobleActivityConfig
}))

class NobleActivityConfig extends Component {
  state = {
    tagKey: 'list',
    opType: 'ADD', // ADD UPDATE
    modelVisible: false
  }

  componentDidMount = () => {
    this.getConfigList()
    this.getApprovalList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 获取配置列表
  getConfigList = () => {
    this.callModel('getActivityConfigList')
  }
  // 获取审批列表
  getApprovalList = () => {
    this.callModel('getActivityApprovalList')
  }

  // 标签页发生切换
  onTagChange = (k) => {
    if (k === 'list') {
      this.getConfigList()
    } else {
      this.getApprovalList()
    }
  }

  // 提交新增或更新活动配置
  onSubmitUpdate = (opType, value) => {
    const funcName = `${opType.toLowerCase()}ConfigList`

    this.callModel(funcName, {
      params: value,
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('操作失败:' + msg)
          return
        }
        message.success('已提交审批，请留意审批结果~')
        this.setState({ modelVisible: false })
      }
    })
  }

  // 贵族等级列表渲染
  rankListFormater = (list) => {
    return (
      <>
        {
          list.map(item => {
            return <><Text>{item.titleName}, {item.value}</Text><br /></>
          })
        }
      </>
    )
  }

  // 贵族等级配置编辑
  RankListUpdator = (props) => {
    const { value, onChange } = props
    const columns = [
      { dataIndex: 'titleId',
        title: '贵族等级',
        render: (v, r, i) => {
          return <Select size='small' options={rankOptions} value={v} onChange={(nv, option) => {
            let newVal = [...value]
            newVal[i].titleId = nv
            newVal[i].titleName = option.label
            onChange(newVal)
          }} />
        }
      },
      { dataIndex: 'value',
        title: '最低消费金额',
        render: (v, r, i) => {
          return <>
            <InputNumber size='small' value={v} onChange={(nv) => {
              let newVal = [...value]
              newVal[i].value = nv
              onChange(newVal)
            }} /><Text type='secondary'> 紫水晶</Text>
          </>
        }
      },
      { dataIndex: 'titleId',
        title: '操作',
        render: (v, r, i) => {
          return <Link onClick={() => {
            let newVal = [...value]
            newVal.splice(i, 1)
            onChange(newVal)
          }} >删除</Link>
        } }
    ]
    return <div>
      <Button size='small' style={{ marginBottom: '5px' }} onClick={() => {
        let newVal = [...value, { titleId: 100, titleName: '骑士', value: 100 }]
        onChange(newVal)
      }} ><PlusOutlined /></Button>
      <Table size='small' columns={columns} pagination={false} dataSource={value} />
    </div>
  }

  // 审批相关信息
  aprInfoFormater = (value) => {
    return <>
      <Text>{value.opNick}, {timeFormater(value.opTime)}</Text>
    </>
  }

  // 审批按钮
  aprButtonFormater = (aprInfo) => {
    const { pid, status } = aprInfo
    if (status === 'Rejected') {
      return <Text type='secondary'>已驳回</Text>
    }
    if (status === 'Passed') {
      return <Text type='secondary' >已通过</Text>
    }
    return <ApprovalButton aprId={pid} >
      <Button>审批</Button>
    </ApprovalButton>
  }

  columns = [
    { dataIndex: 'id', title: '活动ID' },
    { dataIndex: 'begin', title: '活动开始时间', render: (v) => timeFormater(v) },
    { dataIndex: 'end', title: '活动结束时间', render: (v) => timeFormater(v) },
    { dataIndex: 'iconBegin', title: '入口开始时间', render: (v) => timeFormater(v) },
    { dataIndex: 'iconEnd', title: '入口结束时间', render: (v) => timeFormater(v) },
    { dataIndex: 'rewardBegin', title: '领奖开始时间', render: (v) => timeFormater(v) },
    { dataIndex: 'rewardEnd', title: '领奖结束时间', render: (v) => timeFormater(v) },
    { dataIndex: 'sidList', width: '10em', title: '灰度频道', render: (v) => { return <ArrayEditor value={v} type='number' /> } },
    { dataIndex: 'consumeList', width: '10em', title: '消费列表', render: (v) => { return this.rankListFormater(v) } },
    { dataIndex: 'needValueList', width: '10em', title: '配置列表', render: (v) => { return this.rankListFormater(v) } },
    { dataIndex: 'approvalInfo', title: '更新信息', render: (v) => { return this.aprInfoFormater(v) } }
  ]

  fixColumn = (tagKey) => {
    let colum = []
    if (tagKey === 'list') {
      colum = [...this.columns,
        { dataIndex: 'id',
          title: '操作',
          render: (v, r) => {
            return <Space>
              <Link onClick={() => { this.formRef.setFieldsValue(r); this.setState({ opType: 'UPDATE', modelVisible: true }) }}>更新</Link>
              <Popconfirm title={`确认删除活动么? (ID=${v})`} onConfirm={() => this.onSubmitUpdate('DEL', r)} ><Link type='danger'>删除</Link></Popconfirm>
            </Space>
          } }
      ]
    }
    if (tagKey === 'apr') {
      colum = [...this.columns,
        { dataIndex: 'approvalInfo',
          title: '操作类型',
          render: (v) => {
            return <Text>{v.opTag}</Text>
          }
        },
        { dataIndex: 'approvalInfo',
          title: '操作',
          render: (v, r) => {
            return this.aprButtonFormater(v)
          }
        }]
    }

    return colum
  }

  render () {
    const { route } = this.props
    const { configList, approvalList } = this.props.model || {}
    const { modelVisible, opType } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='list' type='card' size='large' onChange={(r) => this.onTagChange(r)}>
            <TabPane tab='活动配置' key='list'>
              <Row>
                <Col span={24}>
                  <Button type='primary'
                    onClick={() => { this.formRef.setFieldsValue(defaultFromValue); this.setState({ modelVisible: true, opType: 'ADD' }) }}
                    style={{ marginBottom: '1em' }} >新增活动</Button>
                </Col>
                <Col span={24}>
                  <Table key='list' dataSource={configList} columns={this.fixColumn('list')} />
                </Col>
                <Col span={24}>
                  <Modal
                    forceRender
                    visible={modelVisible} title={opType === 'ADD' ? '新增活动' : '更新配置'}
                    onCancel={() => { this.setState({ modelVisible: false }) }}
                    onOk={() => { this.formRef.submit() }} >
                    <Form ref={form => { this.formRef = form }} initialValues={defaultFromValue} onFinish={(v) => this.onSubmitUpdate(opType, v)} >
                      <Form.Item label='活动ID' name='id'>
                        <Input style={{ width: '15em' }} disabled={opType !== 'ADD'} />
                      </Form.Item>
                      <Form.Item label='活动开始时间' name='begin'>
                        <SimpleTimePicker format='YYYY-MM-DD HH:mm' showTime inputType='timestamp' outputType='timestamp' />
                      </Form.Item>
                      <Form.Item label='活动结束时间' name='end'>
                        <SimpleTimePicker format='YYYY-MM-DD HH:mm' showTime inputType='timestamp' outputType='timestamp' />
                      </Form.Item>
                      <Form.Item label='入口开始时间' name='iconBegin'>
                        <SimpleTimePicker format='YYYY-MM-DD HH:mm' showTime inputType='timestamp' outputType='timestamp' />
                      </Form.Item>
                      <Form.Item label='入口结束时间' name='iconEnd'>
                        <SimpleTimePicker format='YYYY-MM-DD HH:mm' showTime inputType='timestamp' outputType='timestamp' />
                      </Form.Item>
                      <Form.Item label='领奖开始时间' name='rewardBegin'>
                        <SimpleTimePicker format='YYYY-MM-DD HH:mm' showTime inputType='timestamp' outputType='timestamp' />
                      </Form.Item>
                      <Form.Item label='领奖结束时间' name='rewardEnd'>
                        <SimpleTimePicker format='YYYY-MM-DD HH:mm' showTime inputType='timestamp' outputType='timestamp' />
                      </Form.Item>
                      <Form.Item label='灰度频道' name='sidList'>
                        <ArrayEditor type='number' isEdit />
                      </Form.Item>
                      <Divider />
                      <Form.Item label='消费列表' name='consumeList' tooltip='晋级消费金额  ？前3个月的平均消费金额，达到后可以获得升级头衔的资格'>
                        <this.RankListUpdator />
                      </Form.Item>
                      <Divider />
                      <Form.Item label='配置列表' name='needValueList' tooltip='领奖消费金额  ？获得升级头衔资格后，需要在活动期间消费足够的金额才能升级'>
                        <this.RankListUpdator />
                      </Form.Item>
                    </Form>
                    <CopyPasteButton getCopyVal={() => { return this.formRef.getFieldsValue() }}
                      pasteVal={(val) => { const idBefore = this.formRef.getFieldValue('id'); val.id = idBefore; this.formRef.setFieldsValue(val) }} />
                  </Modal>
                </Col>
              </Row>
            </TabPane>
            <TabPane tab='审批列表' key='apr'>
              <Table key='apr' dataSource={approvalList} columns={this.fixColumn('apr')} />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default NobleActivityConfig
