
import React from 'react'

// 任务完成情况
export const getFinishDesc = (isGuild, targetGuild, isSeal, targetSeal) => {
  if (targetGuild === 0 && targetSeal === 0) {
    return <div>-</div>
  } else {
    return (
      <div>
        <p>公会礼物流水达{yuanToWan(targetGuild)}万元{!isGuild ? <font color='red'>(✗)</font> : <font>(✓)</font>}</p>
        <p>主持盖章流水达{yuanToWan(targetSeal)}万元{!isSeal ? <font color='red'>(✗)</font> : <font>(✓)</font>}</p>
      </div>
    )
  }
}

export const getFinishDescDetail = (detail) => {
  if (detail === undefined) {
    return '-'
  }
  let isGuild = detail.guildSealWaterReach
  let targetGuild = detail.targetAmountGuild
  let isSeal = detail.compereSealWaterReach
  let targetSeal = detail.targetAmountSeal
  return getFinishDesc(isGuild, targetGuild, isSeal, targetSeal)
}

export const getExportFinishDescDetail = (detail) => {
  if (detail === undefined) {
    return '-'
  }
  let isGuild = detail.guildSealWaterReach
  let targetGuild = detail.targetAmountGuild
  let isSeal = detail.compereSealWaterReach
  let targetSeal = detail.targetAmountSeal
  let reachGuild = !isGuild ? '(✗)' : '(✓)'
  let reachSeal = !isSeal ? '(✗)' : '(✓)'
  if (targetGuild === 0 || targetSeal === 0) {
    return '-'
  } else {
    return '公会礼物流水达' + yuanToWan(targetGuild) + '万元' + reachGuild +
      ' 主持盖章流水达' + yuanToWan(targetSeal) + '万元' + reachSeal
  }
}

export const yuanToWan = (v) => {
  if (v === 0 || v === '' || v === '0') {
    return 0
  }
  return (v / 10000).toFixed(1)
}

export const wanToYuan = (v) => {
  return v * 10000
}

export const reviewMap = { 0: '-', 1: '待复核', 2: '已复核' }
export const reviewStatusList = [
  { label: '待复核', value: 1 },
  { label: '已复核', value: 2 }
]

export const approvalMap = { 0: '-', 1: '待审核', 2: '审批通过', 3: '审批不通过', 4: '一审中', 5: '二审中' }
export const approvalStatusList = [
  { label: '待审核', value: 1 },
  { label: '审批通过', value: 2 },
  { label: '审批不通过', value: 3 },
  { label: '一审中', value: 4 },
  { label: '二审中', value: 5 }
]

export const prizeRuleMap = { 0: '-', 1: '按系统结算', 2: '下调奖励-固定值', 3: '按配置金额' }
export const prizeRuleList = [
  { label: '按系统结算', value: 1 },
  { label: '下调奖励-固定值', value: 2 },
  { label: '按配置金额', value: 3 }
]

export const prizeRuleNone = 0 // 初始值
export const prizeRuleSystem = 1 // 按系统结算
export const prizeRuleDown = 2 // 下调奖励-固定值
export const prizeRuleConfig = 3 // 按配置金额

export const isModifyList = [
  { label: '是', value: true },
  { label: '否', value: false }
]
