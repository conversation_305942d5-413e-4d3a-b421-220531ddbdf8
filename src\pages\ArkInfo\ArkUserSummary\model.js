// eslint-disable-next-line no-unused-vars
import { getArkUserDailySummaryInfo } from './api'

export default {
  namespace: 'arkUserSummary',

  state: {
    topnUserInfoList: []
  },

  reducers: {
    updateUserTopNInfoList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        if (payload[i].amethyst > 0) {
          payload[i].amethyst /= 1000
        }
        if (payload[i].sealAmount > 0) {
          payload[i].sealAmount /= 1000
        }
        if (payload[i].awardAmount > 0) {
          payload[i].awardAmount /= 1000
        }
      }
      return {
        ...state,
        topnUserInfoList: payload
      }
    }
  },

  effects: {
    * getTopNRangeList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getArkUserDailySummaryInfo, payload)
      yield put({
        type: 'updateUserTopNInfoList',
        payload: Array.isArray(ret) ? ret : []
      })
    }
  }
}
