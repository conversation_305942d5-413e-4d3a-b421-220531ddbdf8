import React, { Component } from 'react'
import { Card, Table } from 'antd'
import moment from 'moment'
import { connect } from 'dva'
import { parseRewardFlowSourceOptions } from '../../dropCommon'

const namespace = 'RFIConfig' // model 的 namespace
const stateTransfer = { 0: '提交人', 1: '审批人' }

@connect(({ RFIConfig }) => ({ // model 的 namespace
  model: RFIConfig // model 的 namespace
}))
class RFIConfigListComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false
    }
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getConfigList`
    })
  }

  // 需要修改
  columns = [
    { title: '时间', dataIndex: 'timestamp', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD HH:mm:ss') },
    { title: '主持UID', dataIndex: 'uid', align: 'center' },
    { title: '主持YY昵称', dataIndex: 'nick', align: 'center' },
    { title: '签约公会', dataIndex: 'sid', align: 'center' },
    { title: '比例', dataIndex: 'rate', align: 'center', render: text => `${text}%` },
    { title: '渠道', dataIndex: 'source', align: 'center', render: text => parseRewardFlowSourceOptions(text) },
    { title: '审批流', dataIndex: 'progress', align: 'center', render: (text, record, index) => Array.isArray(text) ? text.map(i => <div key={Math.random().toString(32)}>{stateTransfer[i.state] + '： ' + i.passport}</div>) : '' },
    { title: '操作人', dataIndex: 'operator', align: 'center' },
    { title: '操作', dataIndex: 'opType', align: 'center' }
  ]

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { operaotrList } } = this.props

    return (
      <Card>
        <Table rowKey='index' dataSource={operaotrList} columns={this.columns} size='small' pagination={{ pageSize: 500 }} /> {/* 显示的列表 */}
      </Card>
    )
  }
}

export default RFIConfigListComponent
