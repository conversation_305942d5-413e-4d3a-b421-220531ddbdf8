import React, { Component } from 'react'
import { connect } from 'dva'
import { Tabs, Card, Input, Form, Row, Col, Button, Table, Popconfirm, Select, Divider, Modal } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'

const { Option } = Select

const formItemLayout = {
  labelCol: {
    xs: { span: 5 },
    sm: { span: 5 }
  },
  wrapperCol: {
    xs: { span: 12 },
    sm: { span: 16 }
  }
}

const toName = { 'duoteamfight': '双人团战', 'channelfight': '乱斗' }

const namespace = 'videodatingChannelWhiteList'

@connect(({ videodatingChannelWhiteList }) => ({
  model: videodatingChannelWhiteList
}))

class VideodatingChannelWhiteList extends Component {
  state = {
    modelVisable: false, // 修改配置模态框是否显示
    updating: false,
    modify: false,
    appending: false,
    tabKey: '1'
  }

  defaultPageValue = {
    defaultPageSize: 100,
    pageSizeOptions: ['100', '200', '500', '2000'],
    showSizeChanger: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  // 组件初始化
  componentDidMount = () => {
    this.setState({ currentPlay: 'duoteamfight' }, () => {
      this.refreshGameTypeWhiteList()
    })
    this.initForm()
  }

  // 标签页发生切换
  onTabChange = (index) => {
    if (index === '1') { // 切换到'玩法白名单'
      this.setState({ currentPlay: 'duoteamfight' }, () => {
        this.refreshGameTypeWhiteList()
      })
    }
    if (index === '2') { // 切换到'玩法黑名单'
      this.setState({ currentPlay: 'channelfight' }, () => {
        this.refreshGameTypeBlackList()
      })
    }
    this.setState({ tabKey: index })
  }

  // 清空输入表单
  initForm = () => {
    this.setState({ selectSid: '', selectSsid: '', selectPlay: 'duoteamfight' })
    if (this.fromRef) {
      this.fromRef.resetFields()
    }
  }

  // 获取/刷新白名单列表数据
  refreshGameTypeWhiteList = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getWhiteListData`,
      payload: { currentPlay: this.state.currentPlay }
    })
  }

  // 获取/刷新白名单列表数据
  refreshGameTypeBlackList = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getBlackListData`,
      payload: { currentPlay: this.state.currentPlay }
    })
  }

  // 点击 添加标签页-添加按钮
  onAddBtnClick = () => {
    const { dispatch } = this.props
    const { selectSid, selectSsid, selectPlay } = this.state
    console.log('onAddBtnClick', selectSid, selectSsid, selectPlay)
    if (this.state.tabKey === '1') {
      dispatch({
        type: `${namespace}/addWhiteList`,
        payload: { selectSid: selectSid, selectSsid: selectSsid, selectPlay: selectPlay }
      })
    }
    
    if (this.state.tabKey === '2') {
      dispatch({
        type: `${namespace}/addGameTypeBlackList`,
        payload: { selectSid: selectSid, selectSsid: selectSsid, selectPlay: 'channelfight' }
      })
    }
    
    this.setState({ modelVisable: false })
  }

  onAddConfig = () => {
    this.initForm()
    this.setState({ modify: false })
    this.setState({ modelVisable: true })
  }

  // 确认删除选中的白名单
  onComfirmDel = (sid, ssid, play) => {
    const { dispatch } = this.props
    if (this.state.tabKey === '1') {
      dispatch({
        type: `${namespace}/delWhiteList`,
        payload: { sid: sid, ssid: ssid, currentPlay: play }
      })
    } 
    
    if (this.state.tabKey === '2') {
      dispatch({
        type: `${namespace}/delGameTypeBlackList`,
        payload: { sid: sid, ssid: ssid, currentPlay: play }
      })
    }
  }

  // 多人玩法-删除操作html代码
  deleteWhiteListHtml = (record) => {
    let { sid, ssid, play } = record
    let tmpStr = `确定要将 [sid=${sid} ssid=${ssid}] 从[${toName[play]}]名单删除吗？`
    return (
      <Popconfirm placement='bottom' title={tmpStr}
        okType='danger' okText='删除' cancelText='取消' onConfirm={() => this.onComfirmDel(sid, ssid, play)}>
        <a href='#'><DeleteOutlined style={{ color: '#ff3535', fontSize: '1.2em' }} /></a>
      </Popconfirm>
    )
  }

  // 选择的玩法发生变化
  onPlayChange = (v) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getWhiteListData`,
      payload: { currentPlay: v }
    })
  }

  render () {
    const { TabPane } = Tabs
    const { route } = this.props
    const { gameTypeWhiteList } = this.props.model
    const { gameTypeBlackList } = this.props.model

    const columns = [
      { title: '#', dataIndex: 'idx' },
      { title: '频道', dataIndex: 'sid' },
      { title: '子频道', dataIndex: 'ssid' },
      { title: '操作', render: (record) => this.deleteWhiteListHtml(record) }
    ]

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='1' onChange={(record) => this.onTabChange(record)} type='card' size='large'>
            <TabPane tab='玩法白名单' key='1'>
              <Row style={{ paddingBottom: '1em' }}>
                <Col style={{ marginTop: '0.3em' }}>
                  频道玩法:
                </Col>
                <Col style={{ paddingLeft: '1em' }}>
                  <Select defaultValue='duoteamfight' style={{ width: '250px' }} onChange={(v) => { this.onPlayChange(v) }}>
                    <Option value='duoteamfight'>双人团战</Option>
                  </Select>
                </Col>
                <Divider type='vertical' /> {/* 分割线 */}
                <Button type='primary' onClick={() => { this.onAddConfig() }}>添加白名单</Button>
              </Row>
              <Row >
                <Col span={24}>
                  <Table columns={columns} dataSource={gameTypeWhiteList} size='small' pagination={this.defaultPageValue} />
                </Col>
              </Row>
            </TabPane>
            <TabPane tab='玩法黑名单' key='2'>
              <Row style={{ paddingBottom: '1em' }}>
                <Col style={{ marginTop: '0.3em' }}>
                  频道玩法:
                </Col>
                <Col style={{ paddingLeft: '1em' }}>
                  <Select defaultValue='channelfight' style={{ width: '250px' }} onChange={(v) => { this.onPlayChange(v) }}>
                    <Option value='channelfight'>乱斗</Option>
                  </Select>
                </Col>
                <Divider type='vertical' /> {/* 分割线 */}
                <Button type='primary' onClick={() => { this.onAddConfig() }}>添加黑名单</Button>
              </Row>
              <Row >
                <Col span={24}>
                  <Table columns={columns} dataSource={gameTypeBlackList} size='small' pagination={this.defaultPageValue} />
                </Col>
              </Row>
            </TabPane>
          </Tabs>
          {/* 修改配置模态框 */}
          <Modal title='添加'
            forceRender
            visible={this.state.modelVisable}
            onCancel={() => { this.setState({ modelVisable: false }) }} onOk={this.onAddBtnClick} >
            <Row>
              <Col span={24}>
                <Form {...formItemLayout}
                  initialValues={{ sid: '', ssid: '', play: 'duoteamfight', blackPlay: 'channelfight' }}
                  ref={form => { this.fromRef = form }}
                >
                  <Form.Item label='顶级频道' name='sid' rules={[{ required: true }]}>
                    <Input id='addWhiteListInput' placeholder='如50041789' allowClear
                      onChange={e => this.setState({ selectSid: e.target.value })}
                    />
                  </Form.Item>
                  <Form.Item label='子频道' name='ssidList' tooltip={{ title: '子频道，","号分隔，0表示全部子频道' }} rules={[{ required: true }]}>
                    <Input.TextArea placeholder='","号分隔，0表示全部子频道' autoSize={{ minRows: 1 }} onChange={e => this.setState({ selectSsid: e.target.value })} />
                  </Form.Item>
                  <Form.Item label='玩法' name='play' hidden={this.state.tabKey === '2'}>
                    <Select defaultValue='duoteamfight' onChange={(v) => this.setState({ selectPlay: v })}>
                      <Option value='duoteamfight'>双人团战</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item label='玩法' name='blackPlay' hidden={this.state.tabKey === '1'}>
                    <Select defaultValue='channelfight' onChange={(v) => this.setState({ selectPlay: v })}>
                      <Option value='channelfight'>乱斗</Option>
                    </Select>
                  </Form.Item>
                  {/* <Form.Item>
                    <Button type='primary' htmlType='submit' loading={updating} onClick={() => this.onAddBtnClick()}>
                      添加
                    </Button>
                  </Form.Item> */}
                </Form>
              </Col>
            </Row>
          </Modal>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default VideodatingChannelWhiteList
