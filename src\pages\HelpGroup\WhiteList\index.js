import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs, Card } from 'antd'
import { connect } from 'dva'

import SendWhiteList from './components/sendWhiteList'
import RecvWhiteList from './components/recvWhiteList'

const TabPane = Tabs.TabPane

@connect(({ helpGroup }) => ({
  model: helpGroup
}))

class HelpGroup extends Component {
  state = { 
    activeKey: '1'
  }

  onTabClick = key => {
    this.setState({ activeKey: key })
  }

  render () {
    const { route } = this.props
    const { activeKey } = this.state
    
    return (
      <PageHeaderWrapper title={route.name}>
        <Card style={{ marginTop: 20 }}>
          <Tabs type='card' defaultActiveKey='1' onTabClick={this.onTabClick}>
            <TabPane tab='帮帮团发放白名单' key='1'>
              { activeKey === '1' ? <SendWhiteList /> : ''}
            </TabPane>
            <TabPane tab='帮帮团接收白名单' key='2'>
              { activeKey === '2' ? <RecvWhiteList /> : '' }
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default HelpGroup
