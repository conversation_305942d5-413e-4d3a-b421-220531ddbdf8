import React, { Component } from 'react'
import { Table, Divider, But<PERSON>, Card, Form, DatePicker, Input } from 'antd'
import { connect } from 'dva'
import exportExcel from '@/utils/exportExcel'

var moment = require('moment')
const { RangePicker } = DatePicker

@connect(({ podExchange, loading }) => ({ // model 的 namespace
  model: podExchange, // model 的 namespace
  loading: loading.effects['podExchange/getPodExchangeDetailList']
}))
class PodExchangeDetailComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      dateRange: [moment().subtract(1, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.setState()
  }

  // 日期 豆荚兑换总流水  豆荚兑换魔豆流水 豆荚兑换道具流水  豆荚兑换免费礼物流水 豆荚兑换非免费礼物流水
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center', render: (text, record) => (text = moment.unix(record.timestamp).format('YYYY-MM-DD')) },
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    // { title: '消耗豆荚/紫水晶', dataIndex: 'costAmount', align: 'center' },
    { title: '兑换内容', dataIndex: 'goodsName', align: 'center' },
    { title: '兑换时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (text = moment.unix(record.timestamp).format('HH:MM:ss')) },
    { title: '所在频道', dataIndex: 'sid', align: 'center' },
    { title: '短位频道', dataIndex: 'asid', align: 'center' },
    { title: '频道主持', dataIndex: 'compereUid', align: 'center' },
    { title: '主持签约频道', dataIndex: 'signSid', align: 'center' }
  ]

  pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange, uid } = this.state
    const data = { startInSec: moment(dateRange[0]).unix(), endInSec: moment(dateRange[1]).unix(), uid: uid }
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getPodExchangeDetailList`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    this.setState({ dateRange: date })
  }

  onExport = () => {
    let headers = []
    this.columns.forEach(function (item) {
      headers.push({ key: item.dataIndex, header: item.title })
    })

    const { model: { detailList } } = this.props
    var exportData = detailList.map(item => {
      let v = $.extend(true, {}, item)
      v.date = moment.unix(v.timestamp).format('YYYY-MM-DD')
      v.timestamp = moment.unix(v.timestamp).format('HH:mm:ss')
      return v
    })

    exportExcel(headers, exportData)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { detailList }, loading } = this.props
    const { dateRange } = this.state

    return (
      <Card>
        <Form>
          UID:
          <Input onChange={e => this.setState({ uid: e.target.value })} style={{ marginLeft: 10, width: 150 }} /> {/* 搜索按钮 */}
          <span style={{ marginLeft: 10 }}>时间范围:</span>
          <RangePicker showTime style={{ marginLeft: 10 }} defaultValue={dateRange} onChange={this.onChange} />
          <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onClick}>查询</Button>
          <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
          <Divider />
          <Table loading={loading} dataSource={detailList} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
        </Form>
      </Card>
    )
  }
}

export default PodExchangeDetailComponent
