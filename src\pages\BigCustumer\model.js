import { getLists, add, update, remove } from './api'
import { message } from 'antd'

export default {
  namespace: 'bigCustumer', // 全局唯一标识

  state: {
    list: [
      { id: '50016575:1', uid: 50016575, imid: 909015575, reason: 'abc', privilegeName: 'privilegeName', startTime: 1621588051, endTime: 1621588052, opUser: 'dw_pengzhangjie', timestamp: 1621588053 }
    ]
  },

  reducers: {

    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {

    * getList ({ payload }, { call, put }) {
      try {
        const { data: { list } } = yield call(getLists, payload)

        yield put({
          type: 'updateList',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        message.error('exception', e)
      }
    },

    * addItem ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(add, payload)
        if (status === 0) {
          message.success('add success')
          yield put({
            type: 'getList'
          })
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('exception', e)
      }
    },

    * updateItem ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(update, payload)
        if (status === 0) {
          message.success('update success')
          yield put({
            type: 'getList'
          })
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('exception', e)
      }
    },

    * removeItem ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(remove, payload)
        if (status === 0) {
          message.success('remove success')
          yield put({
            type: 'getList'
          })
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('exception', e)
      }
    }
  }
}
