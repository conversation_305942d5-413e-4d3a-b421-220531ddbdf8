import React, { Component } from 'react'
import { Upload, Modal, Popover, message } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import Cropper from 'react-cropper'
import 'cropperjs/dist/cropper.css'
import PropTypes from 'prop-types'

// 在 Modal 内使用需要加上参数 maskClosable
// <Modal maskClosable={false}>your code here</Modal
class CropperImage extends Component {
  static propTypes = {
    title: PropTypes.string
  }
  constructor (props) {
    super(props)

    this.state = {
      fileList: props.value ? [urlValue(props.value)] : [],
      previewImage: '',
      previewVisible: false
    }
  }

  componentWillReceiveProps (nextProps) {
    // 受控组件， 用于更新
    const { value } = nextProps
    this.setState({
      fileList: value ? [urlValue(value)] : []
    })
  }

  handlePreview = file => {
    this.setState({
      previewImage: file.url || file.thumbUrl,
      previewVisible: true
    })
  }

  handleCancel = () => this.setState({ previewVisible: false })

  handleChange = ({ fileList }) => {
    let onChange = this.props.onChange ?? function () {}

    if (fileList.length === 0) {
      onChange()
    }
    if (fileList.length > 0 && fileList[fileList.length - 1].status === 'done' && fileList[fileList.length - 1].response.status === 0) {
      var url = fileList[fileList.length - 1].response.urls[0]
      onChange(url) // update getFiledDecorator
    }
    this.setState({ fileList })
  }

  renderImage = () => {
    const { value } = this.props
    const content = (
      <div>
        <img src={value} style={{ maxHeight: 200, maxWidth: 200 }} />
      </div>
    )
    return (
      <div>
        <Popover placement='right' content={content} title={null}>
          <img width='102' height='102' src={value} />
        </Popover>
      </div>
    )
  }

  beforeUpload = file => {
    return new Promise(async (resolve, reject) => {
      console.log(file)
      // gif不裁剪(裁剪会变成静态图) gif不检查文件大小，直接上传
      if (file.type === 'image/gif') {
        return resolve(file)
      }

      const arrayBytes = ['B', 'KB', 'MB', 'GB']
      if (this.props.minSize && this.props.minSize > file.size) {
        const index = Math.floor(Math.log(this.props.minSize) / Math.log(1024))
        message.warning(`file size is smaller than ${(this.props.minSize / Math.pow(1024, index))}${arrayBytes[index]}`)
        reject(new Error('file size not allowed'))
        return
      }

      if (this.props.maxSize && this.props.maxSize < file.size) {
        const index = Math.floor(Math.log(this.props.maxSize) / Math.log(1024))
        message.warning(`file size is bigger than ${(this.props.maxSize / Math.pow(1024, index))}${arrayBytes[index]}`)
        reject(new Error('file size not allowed'))
        return
      }

      // eslint-disable-next-line no-undef
      let reader = new FileReader()
      reader.readAsDataURL(file)
      let _this = this
      reader.onload = e => {
        _this.setState({ cropSrc: e.target.result, cropVisible: true, resolveRef: resolve, fileRef: file })
      }
    })
  }

  onCropper = () => {
    let cropperCanvas = this.cropper.getCroppedCanvas()
    cropperCanvas.toBlob(async blob => {
      const { type, name, uid } = this.state.fileRef

      // console.log(blob, type, name, uid)
      // eslint-disable-next-line no-undef
      let newFile = new File([blob], name, { type })
      newFile.uid = uid

      let resolve = this.state.resolveRef ?? function () {}
      resolve(newFile)

      this.setState({ cropVisible: false })
    }, 'image/jpeg', 0.92)
  }

  render () {
    const { title } = this.props
    const { previewVisible, previewImage, fileList, cropVisible } = this.state
    const uploadButton = (
      <div>
        <PlusOutlined />
        <div className='ant-upload-text'>Upload</div>
      </div>
    )

    // console.log(this.props)
    return (
      <div className='clearfix'>
        <Upload
          action='//fts.yy.com/fs/uploadfiles'
          listType='picture-card'
          fileList={fileList}
          onPreview={this.handlePreview}
          onChange={this.handleChange}
          // showUploadList={false}
          beforeUpload={this.beforeUpload}
          data={file => ({ bucket: 'makefriends', files: file })}
        >
          {fileList.length > 0 ? null : uploadButton}
        </Upload>
        <Modal visible={previewVisible} footer={null} onCancel={this.handleCancel}>
          <img alt='example' style={{ width: '100%' }} src={previewImage} />
        </Modal>
        <Modal width={1000} visible={cropVisible} title='裁剪图片' onOk={this.onCropper} onCancel={() => { this.setState({ cropVisible: false }) }}>
          <Cropper
            src={this.state.cropSrc}
            style={{ height: 600, width: '100%' }}
            onInitialized={cropper => { this.cropper = cropper }}
            {...this.props}
          />
        </Modal>
        {title !== undefined && title.length > 0 ? <font color='blue' style={{ marginLeft: 35 }} >{title}</font> : null }
      </div>
    )
  }
}

function urlValue (url) {
  return { uid: -1, status: 'done', url: url, thumbUrl: url }
}

export default CropperImage
