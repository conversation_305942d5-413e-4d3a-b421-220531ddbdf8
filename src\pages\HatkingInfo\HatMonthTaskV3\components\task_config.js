import {
  Card,
  Form,
  Table,
  Tooltip,
  Divider, Input, message, Modal, Button, DatePicker, Select, Typography, Space, InputNumber, Popover
} from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import dateString from '@/utils/dateString'
import { deepClone } from '../../../../utils/common'
import { tingSealStr, approvaMap, approvalStatusList, reviewMap, reviewStatusList } from './common'
import './style.css'
import { sprintTaskStepFormater } from '../../HatMonthTaskV2/components/common'
import exportExcel from '@/utils/exportExcel'

const { TextArea } = Input
const Option = Select.Option
const { Link } = Typography
const namespace = 'hatMonthTaskv3'

@connect(({ taskConfig }) => ({
  model: taskConfig
}))

class TaskConfig extends Component {
  state = {
    list: [],
    taskConfig: {},
    searchASID: 0,
    searchSSID: 0,
    searchMonth: '',
    reviewStatus: 0,
    approvalStatus: 0,
    visibleConfirmTask: false,
    visibleEdittask: false
  }

  componentDidMount () {
    const { dataInfo } = this.props
    this.setState({
      taskConfig: dataInfo.taskConfig,
      list: dataInfo.list
    })
    this.refreshList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 刷新列表
  refreshList = () => {
    const { dispatch } = this.props
    const { searchASID, searchSSID, searchTaskMonth, searchReviewStatus, searchApprovalStatus } = this.state
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    let data = { asid: searchASID, ssid: searchSSID, month: searchMonthTmp, reviewStatus: searchReviewStatus, approvalStatus: searchApprovalStatus }
    dispatch({
      type: `${namespace}/listTaskConfig`,
      payload: data
    })
  }

  onClick = () => {
    const { dispatch } = this.props
    const { searchASID, searchSSID } = this.state
    let data = { asid: searchASID, ssid: searchSSID }
    dispatch({
      type: `${namespace}/listTaskConfig`,
      payload: data
    })
  }

  componentWillReceiveProps (nextProps) {
    const { dataInfo } = nextProps
    this.setState({ list: dataInfo.list, taskConfig: dataInfo.taskConfig })
  }

  columns = [
    { title: '任务时间', dataIndex: 'month' },
    { title: '实际生效asid', width: 50, dataIndex: 'guildAsid' },
    { title: '经营asid', width: 50, dataIndex: 'asid' },
    { title: '签约asid', width: 50, dataIndex: 'contractAsid' },
    { title: '本月参与厅', dataIndex: 'ssid' },
    { title: '厅名', width: 50, dataIndex: 'tingName', render: (v, r) => (r.tingName.length > 20 ? <Tooltip title={r.tingName}>{ r.tingName.substring(0, 20) + '...'}</Tooltip> : r.tingName) },
    { title: '复核状态', dataIndex: 'reviewStatus', align: 'center', render: (v, r) => { return <Tooltip title={r.reviewRemark}>{ reviewMap[r.reviewStatus]}</Tooltip> } },
    { title: '审核状态', dataIndex: 'approvalStatus', render: (v, r) => (approvaMap[r.approvalStatus]) },
    { title: '驳回原因', dataIndex: 'approvalRemark', render: (v) => { return v || '-' } },
    { title: '操作人', dataIndex: 'optUser', render: (v, r) => (r.optUser === 0 ? '' : r.optUser) },
    { title: '操作时间', dataIndex: 'optTime', render: (v, r) => (dateString(r.timestamp)) },
    { title: '是否生效', dataIndex: 'isValid', render: (v, r) => (r.isValid ? '是' : '否') },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, r) => (
        <Space>
          <Link disabled={cantEditReason(r) !== ''} onClick={() => this.onEditTask(r)}>任务编辑</Link>
          <Link disabled={cantConfirmReason(r) !== ''} onClick={() => this.showConfirmTask(r)}>任务确认</Link>
          <Popover content={this.renderContent(r)} trigger='click'><Button type={'link'} disabled={r.isValid || r.approvalStatus !== 0}>删除</Button></Popover>
        </Space>
      ) }
  ].map(item => {
    item.align = 'center'
    item.ellipsis = true
    return item
  })

  columnsEditTask = [
    { title: '任务时间', width: 40, dataIndex: 'month' },
    { title: '实际生效asid', width: 50, dataIndex: 'guildAsid' },
    { title: '经营asid', width: 50, dataIndex: 'asid' },
    { title: '签约asid', width: 50, dataIndex: 'contractAsid' },
    { title: '参与厅SSID', width: 100, dataIndex: 'ssid', render: (v, r) => { return this.genSSIDInput(r) } }
  ].map(item => {
    item.align = 'center'
    return item
  })

  columnsConfirmTask = [
    { title: '任务时间', dataIndex: 'month' },
    { title: '实际生效asid', width: 50, dataIndex: 'guildAsid' },
    { title: '经营asid', width: 50, dataIndex: 'asid' },
    { title: '签约asid', width: 50, dataIndex: 'contractAsid' },
    { title: '参与厅SSID', dataIndex: 'ssid' }
  ].map(item => {
    item.align = 'center'
    return item
  })

  pagination = {
    pageSizeOptions: ['10', '20', '50', '100'],
    showSizeChanger: true,
    defaultPageSize: 20,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  renderContent = (record) => {
    return (
      record.isValid || record.approvalStatus !== 0 ? null
        : <div>
          <Input.TextArea onChange={this.onTextChange} row={5} placeholder={'删除原因选填，最多100字符'} />
          <Button onClick={this.deleteHandle(record)} style={{ marginLeft: 120, marginTop: 5 }} type='primary'>确定</Button>
        </div>
    )
  }

  onTextChange = e => {
    this.setState({ removeReason: e.target.value })
  }

  deleteHandle = (record) => () => {
    console.log(record)
    const { removeReason } = this.state
    let params = { id: record.id, removeReason: removeReason }
    console.log(params)
    this.callModel('deleteTaskConfig', {
      params: params,
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('确认失败: ' + msg)
          return
        }
        message.success('删除成功')
        this.refreshList()
      }
    })
  }

genSSIDInput = (r) => {
  return <InputNumber style={{ width: '10em' }} value={r.ssid} onChange={v => { r.ssid = v; this.forceUpdate() }} />
}
  // 单个记录任务确认 confirm-step-1.2
  showConfirmTask = (record) => {
    const reason = cantConfirmReason(record)
    if (reason) {
      message.warn(reason)
      return
    }

    let info = deepClone(record)
    let list = []

    // 已选阶段
    let data = { id: info.id,
      sid: info.sid,
      ssid: info.ssid,
      asid: info.asid,
      contractAsid: info.contractAsid,
      guildAsid: info.guildAsid,
      month: info.month,
      selectStep: []
    }
    if (Array.isArray(info.taskStep)) {
      for (let i = 0; i < info.taskStep.length; i++) {
        const { tingSealBegin, tingSealEnd } = info.taskStep[i]
        let desc = `盖章 ${tingSealStr(tingSealBegin, tingSealEnd)}万元`
        data.selectStep.push({ label: desc, value: info.taskStep[i].idx })
      }
    }
    list.push(data)
    this.setState({ visibleConfirmTask: true, waitConfirmList: list })
  }

  // 点编辑任务按钮,批量任务编辑 edit-step-1
  onEditTaskBulk = () => {
    const { list } = this.state
    let filterList = deepClone(list)
    filterList = filterList.filter((v) => { return cantEditReason(v) === '' })

    if (filterList.length === 0) {
      message.warn('无可更新的单 (当月且在操作时间内的未复核单才能更新)')
      return
    }

    let infos = []
    filterList.forEach(info => {
      let data = { id: info.id,
        sid: info.sid,
        ssid: info.ssid,
        asid: info.asid,
        contractAsid: info.contractAsid,
        guildAsid: info.guildAsid,
        month: info.month,
        selectStep: [] }
      if (Array.isArray(info.taskStep)) {
        for (let i = 0; i < info.taskStep.length; i++) {
          const { tingSealBegin, tingSealEnd } = info.taskStep[i]
          let desc = `盖章 ${tingSealStr(tingSealBegin, tingSealEnd)}万元`
          data.selectStep.push({ label: desc, value: info.taskStep[i].idx })
        }
      }
      infos.push(data)
    })

    this.setState({ visibleEdittask: true, waitUpdateList: infos })
  }

  // 单个编辑任务 edit-step-3
  onEditTask = (r) => {
    const reason = cantEditReason(r)
    if (reason) {
      message.warn(reason)
      return
    }

    let info = deepClone(r)
    let list = []

    let data = { id: info.id,
      sid: info.sid,
      ssid: info.ssid,
      asid: info.asid,
      contractAsid: info.contractAsid,
      guildAsid: info.guildAsid,
      month: info.month,
      selectStep: [1, 2, 3, 4, 5]
    }
    list.push(data)
    this.setState({ visibleEdittask: true, waitUpdateList: list })
  }

  // 批量任务确认 confirm-step-1.1
  onConfirmAll = () => {
    const { list } = this.state
    let filterList = deepClone(list)
    filterList = filterList.filter((v) => { return cantConfirmReason(v) === '' })
    if (filterList.length === 0) {
      message.warn('无符合审批的单(当月且在操作时间内的未复核单才能发起确认审批)')
      return
    }
    let infos = []
    filterList.forEach(info => {
      // 已选阶段
      let data = { id: info.id,
        ssid: info.ssid,
        sid: info.sid,
        asid: info.asid,
        contractAsid: info.contractAsid,
        guildAsid: info.guildAsid,
        month: info.month,
        selectStep: [] }
      if (Array.isArray(info.taskStep)) {
        for (let i = 0; i < info.taskStep.length; i++) {
          const { tingSealBegin, tingSealEnd } = info.taskStep[i]
          let desc = `盖章 ${tingSealStr(tingSealBegin, tingSealEnd)}万元`
          data.selectStep.push({ label: desc, value: info.taskStep[i].idx })
        }
      }
      infos.push(data)
    })

    this.setState({ visibleConfirmTask: true, waitConfirmList: infos })
  }

  getFilterList = () => {
    const { list } = this.state
    const { searchASID, searchSSID, searchTaskMonth, searchReviewStatus, searchApprovalStatus } = this.state
    let filterList = list
    if (parseInt(searchASID) > 0) {
      filterList = filterList.filter((v) => { return v.guildAsid === parseInt(searchASID) || v.contractAsid === parseInt(searchASID) || v.asid === parseInt(searchASID) })
    }
    if (parseInt(searchSSID) > 0) {
      filterList = filterList.filter((v) => { return v.ssid === parseInt(searchSSID) })
    }
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    if (parseInt(searchMonthTmp) > 0) {
      filterList = filterList.filter((v) => { return parseInt(v.month) === parseInt(searchMonthTmp) })
    }
    if (searchReviewStatus > 0) {
      filterList = filterList.filter((v) => { return v.reviewStatus === searchReviewStatus })
    }
    if (searchApprovalStatus > 0) {
      filterList = filterList.filter((v) => { return v.approvalStatus === searchApprovalStatus })
    }
    return filterList
  }

  inputReviewRemarkHandle = () => e => {
    let value = e.target.value
    this.setState({ inputReviewRemark: value })
  }

  // 关闭确认模态框
  hiddenModalConfirmTask = () => {
    this.setState({ visibleConfirmTask: false, waitConfirmList: [] })
  }

  // 确认编辑模态框
  hiddenModalEditTask = () => {
    this.setState({ visibleEdittask: false, inputReviewRemark: '', waitUpdateList: [] })
  }

  // 提交更新逻辑 edit-step-2.1
  handleSubmitEdit = () => {
    const { inputReviewRemark, waitUpdateList } = this.state
    if (!inputReviewRemark) {
      message.warning('请填写复核备注')
      return
    }

    console.debug('waitUpdateList===>', waitUpdateList)

    let progressMap = {}
    let ssidMap = {}

    waitUpdateList.forEach(v => {
      let idxList = []
      for (let i = 0; i < v.selectStep.length; i++) {
        idxList.push(v.selectStep[i].value)
      }
      progressMap[v.id] = idxList
      ssidMap[v.id] = v.ssid
    })

    let params = { remark: inputReviewRemark, progressMap: progressMap, ssidMap: ssidMap }
    console.debug('params====>', params)

    this.callModel('editaTaskConfig', {
      params: params,
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('更新失败: ' + msg)
          return
        }
        message.success('更新成功')
        this.refreshList()
      }
    })

    this.hiddenModalEditTask()
  }

  // 提交任务确认 confirm-step-2
  handleSubmitConfirm = () => {
    const { waitConfirmList } = this.state

    let idList = []
    waitConfirmList.forEach(item => {
      idList.push(item.id)
    })

    let params = { idList: idList }
    this.callModel('confirmTaskConfig', {
      params: params,
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('确认失败: ' + msg)
          return
        }
        message.success('任务确认成功')
        this.refreshList()
      }
    })
    this.hiddenModalConfirmTask()
  }

  onExport = () => {
    let headers = []
    let columns = this.columns
    columns.forEach(function (item) {
      headers.push({ key: item.dataIndex, header: item.title })
    })

    const exportData = this.getFilterList().map(item => {
      let v = $.extend(true, {}, item)
      let taskStep = []
      if (sprintTaskStepFormater(v)) {
        sprintTaskStepFormater(v).forEach(function (item) {
          taskStep.push(item.props.children)
        })
      }
      v.taskStep = taskStep
      v.reviewStatus = reviewMap[v.reviewStatus]
      v.approvalStatus = approvaMap[v.approvalStatus]
      v.optTime = v.optTime > 0 ? dateString(v.optTime) : ''
      v.optUser = v.optUser > 0 ? v.optUser : ''
      v.isValid = v.isValid ? '是' : '否'
      return v
    })

    exportExcel(headers, exportData, '房管厅盖章流水任务(进阶).xlsx')
  }

  onFinishAddTask = values => {
    console.log(values)
    this.callModel('addTaskConfig', {
      params: { month: values.month.format('YYYYMM'), sid: parseInt(values.sid), ssid: parseInt(values.ssid) },
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('添加失败: ' + msg)
          return
        }
        message.success('添加成功')
        this.refreshList()
      }
    })
    // this.setState({ visibleAddTask: false })
  }

  render () {
    const { visibleEdittask, visibleConfirmTask, waitUpdateList, waitConfirmList, inputReviewRemark, visibleAddTask } = this.state // visibleConfirmTask

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 2 },
        sm: { span: 10 }
      }
    }

    return (
      <Card>
        <Form>
          任务时间：
          <DatePicker
            format='YYYY-MM'
            picker='month'
            placeholder='任务时间'
            onChange={(v) => this.setState({ searchTaskMonth: v })}
            style={{ width: 100, marginRight: 10 }}
          />
          <Divider type='vertical' />

          短位ID：
          <Input style={{ width: 100, marginRight: 10 }} placeholder='请输入' onChange={(e) => { this.setState({ searchASID: e.target.value }) }} />
          <Divider type='vertical' />

          SSID：
          <Input style={{ width: 100, marginRight: 10 }} placeholder='请输入' onChange={(e) => { this.setState({ searchSSID: e.target.value }) }} />
          <Divider type='vertical' />

          复核状态
          <Select allowClear placeholder='请选择' style={{ width: 100, marginLeft: 3 }} onChange={(v) => this.setState({ searchReviewStatus: v })}>
            {reviewStatusList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}
          </Select>
          <Divider type='vertical' />

          审批状态
          <Select allowClear placeholder='请选择' style={{ width: 130, marginLeft: 3 }} onChange={(v) => this.setState({ searchApprovalStatus: v })}>
            {approvalStatusList.map((item, index) => (<Option key={item.value} value={item.value}>{item.label}</Option>))}
          </Select>

          <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
        </Form>

        <div />
        <div style={{ margin: '0.5em 0' }}>
          <Space>
            <Button type='primary' onClick={() => this.onEditTaskBulk()}>批量任务编辑</Button>
            <Button type='primary' onClick={() => this.onConfirmAll()}>批量任务确认</Button>
            <Button type='primary' onClick={() => this.setState({ visibleAddTask: true })}>新增任务</Button>
          </Space>
        </div>
        <Table style={{ marginTop: 10 }} dataSource={this.getFilterList()} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} scroll={{ x: 'max-content' }} size='small' />

        {/* 任务编辑模态框 */}
        <Modal keyboard={false} destroyOnClose forceRender width={1000} visible={visibleEdittask} title='任务编辑' onCancel={() => this.hiddenModalEditTask()} onOk={() => this.handleSubmitEdit()}>
          <TaskUpdateDesc />
          <div style={{ margin: '1em 0' }}>
            <span><font color='red'>运营复核备注:</font></span>
            <TextArea placeholder='选填' autoSize={{ minRows: 1, maxRows: 4 }} style={{ height: 50, width: 500 }} value={inputReviewRemark} onChange={this.inputReviewRemarkHandle()} />
          </div>
          <Table bordered dataSource={waitUpdateList} columns={this.columnsEditTask} pagination={false} />
        </Modal>

        {/* 任务确认模态框 */}
        <Modal keyboard={false} destroyOnClose forceRender width={1000} visible={visibleConfirmTask} title='任务确认' onCancel={() => this.hiddenModalConfirmTask()} onOk={() => this.handleSubmitConfirm()}>
          <TaskConfirmDesc />
          <Table bordered dataSource={waitConfirmList} columns={this.columnsConfirmTask} pagination={false} />
        </Modal>

        {/* 添加任务模态框 */}
        <Modal keyboard={false}
          destroyOnClose
          width={400}
          visible={visibleAddTask}
          title='新增任务'
          onCancel={() => this.setState({ visibleAddTask: false })}
          onOk={() => this.formRefAddTask.submit()}>
          <Form {...formItemLayout} ref={form => { this.formRefAddTask = form }} onFinish={this.onFinishAddTask} >
            <Form.Item label='任务月份' name='month' rules={[{ required: true }]}>
              <DatePicker
                format='YYYY-MM'
                picker='month'
                placeholder='参与任务的月份'
                style={{ width: 150 }}
              />
            </Form.Item>
            <Form.Item label='sid' name='sid' rules={[{ required: true }]}>
              <Input autoComplete='off' style={{ width: '150px' }} />
            </Form.Item>
            <Form.Item label='ssid' name='ssid' rules={[{ required: true }]}>
              <Input autoComplete='off' style={{ width: '150px' }} />
            </Form.Item>
          </Form>
        </Modal>

      </Card>
    )
  }
}

export default TaskConfig

// 判断是否可编辑
const cantEditReason = (r) => {
  const { inOpTime, approvalStatus, isValid } = r
  if (!inOpTime) { // 非可操作时间 (26号～下月1号可操作)
    return '非可操作时间'
  }
  if (isValid) {
    return '已生效，不可编辑'
  }
  if (approvalStatus === 3) { // 审批不通过可以重新改
    return ''
  }
  if (approvalStatus === 2) {
    return '已审批通过,不可编辑'
  }
  if (approvalStatus === 4 || approvalStatus === 5 || approvalStatus === 1) {
    return '任务审批中,不可编辑'
  }
  // if (reviewStatus !== 1) { // 待审批
  //   return '已复核不可编辑'
  // }
  return ''
}

// 判断是否可以确认
const cantConfirmReason = (r) => {
  const { inOpTime, reviewStatus, approvalStatus, isValid } = r
  if (!inOpTime) { // 非可操作时间 (26号～下月1号可操作)
    return '非可操作时间'
  }
  if (isValid) {
    return '已生效，不可编辑'
  }
  if (approvalStatus === 2) {
    return '审批通过,不可确认'
  }
  if (approvalStatus === 4 || approvalStatus === 5 || approvalStatus === 1) {
    return '任务审批中,不可确认'
  }
  if (reviewStatus !== 2) {
    return '任务未复核'
  }
  return ''
}

// 任务更新提示
const TaskUpdateDesc = () => {
  return <div>
    <h3>任务编辑</h3>
    1.审批操作单击列表上方【任务配置】按钮,单条操作对应条目后的【任务编辑】按钮;<br />
    2.参与厅默认为该公会上月最高盖章流水厅,若需变更请手动输入对应的ssid，若输入的ssid不存在或为空任务不会下发生效;<br />
    3.任务阶段默认选择上月选中的阶段,可手动勾选/取消;<br />
    4.任务配置时间结束后未提交修改(每月26日至31日)，默认按自动生成的任务内容下发生效;<br />
    <p />
  </div>
}

// 任务确认提示
const TaskConfirmDesc = () => {
  return <div>
    <h3>任务确认</h3>
    1. 任务已确认审批未完成时，可通过审批打回修改任务内容; <br />
    2. 任务已确认审批完成后，本月任务即生效，不支持后台修改;<br />
    <p />
  </div>
}
