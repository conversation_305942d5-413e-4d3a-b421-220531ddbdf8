import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card } from 'antd'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import TemplateList from './tabs/templateList'
import GatewayDebug from './tabs/gatewayDebug' 

const namespace = 'templateGateWay'

@connect(({ templateGateWay }) => ({
  model: templateGateWay
}))

class SeaMain extends Component {
  state = {}
  componentDidMount = () => {}

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // 标签页发生切换
  onTagChange = (record) => { }

  render () {
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='list' size='small' type='card' onChange={(record) => this.onTagChange(record)}>
            <TabPane tab='白名单配置' key='list'>
              <TemplateList />
            </TabPane>
            <TabPane tab='灰度策略测试' key='debug'>
              <GatewayDebug />
            </TabPane>
            {/* <TabPane tab='频道匹配设置' key='setting'>
              <SettingUpdate />
            </TabPane> 
            <TabPane tab='操作记录' key='history'>
              <SettingHistory />
            </TabPane> */}
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default SeaMain
