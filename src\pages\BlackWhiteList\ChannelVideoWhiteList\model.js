/* eslint-disable eqeqeq */
import { getLists, whiteListAdd, whiteListDel } from './api'
import { checkSid } from '@/utils/common'
import { Modal, message } from 'antd'

export default {
  namespace: 'channelVideoWhiteList',
  state: {
    updating: false, // 添加白名单事件是否在处理中
    displayData: [],
    selectUid: 0, // 删除的uid
    selectSid: 0, // 删除的sid
    newSid: ''
  },

  reducers: {
    // 更新data到频道视频白名单数据列表
    displayList (state, { payload: data }) {
      if (!Array.isArray(data)) {
        console.error('unexpect data type: data=', data)
        return
      }
      return {
        ...state,
        displayData: data
      }
    },
    // 设置‘添加标签页-添加’按钮的状态，true为正在处理中
    setBtnStatus (state, { payload: status }) {
      if (status !== true && status !== false) {
        console.error('unexpect argument in setBtnStatus: status=', status)
        Modal.error('发生错误，请查看控制台')
        return
      }
      return {
        ...state,
        updating: status
      }
    }
  },

  effects: {
    // 请求并刷新频道视频白名单列表数据
    * getWhiteListData ({ params }, { select, call, put }) {
      let resp = yield call(getLists)
      let { data: { status, sidList } } = resp
      if (sidList === null) {
        message.warning('数据为空')
        yield put({
          type: 'displayList',
          payload: []
        })
        return
      }
      if (status !== 0 || !Array.isArray(sidList)) {
        console.error('getWhiteListData() get data error: response=', resp)
        Modal.error({ content: '获取频道视频白名单数据失败，请检查控制台' })
        return
      }
      for (let i = 0; i < sidList.length; i++) {
        sidList[i].idx = i + 1
      }
      yield put({
        type: 'displayList',
        payload: sidList
      })
    },
    // 添加频道视频白名单
    * addWhiteListBySid ({ payload }, { call, put }) {
      const { newSid, callback } = payload
      if (!checkSid(newSid)) {
        return
      }
      yield put({
        type: 'setBtnStatus',
        payload: true
      })
      let resp = yield call(whiteListAdd, newSid)
      const { data } = resp
      if (data == undefined) {
        Modal.error({ content: '发生错误，请检查控制台' })
        console.error('[添加频道视频白名单]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 0) {
        message.warn('重复添加, 请检查~')
      } else if (status === 1) {
        message.success('添加成功')
        if (callback) {
          callback()
        }
      } else {
        console.error('addWhiteListBySid()：[添加白名单] 返回结果为：', resp)
        Modal.warn({ content: '添加失败, 请检查控制台' })
      }
      yield put({
        type: 'setBtnStatus',
        payload: false
      })
    },
    // 删除频道视频白名单
    * delWhiteListByUid ({ payload }, { call, put }) {
      const sid = payload
      if (!checkSid(sid)) {
        return
      }
      let resp = yield call(whiteListDel, sid)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除频道视频白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 1) {
        message.success('删除成功')
        yield put({ // 更新列表
          type: 'getWhiteListData'
        })
      } else {
        Modal.error({ content: '删除失败：status=' + status })
        console.error('delWhiteListByUid() resp=', resp)
      }
    }
  }
}
