import * as api from './api'
import { message } from 'antd'
import { genUpdateTemplate } from '@/utils/common'

const editaTaskConfig = genUpdateTemplate('/fts_hgame/roommgr_seal_task/boss/advance/edit_task_config') // 更新任务配置
const confirmTaskConfig = genUpdateTemplate('/fts_hgame/roommgr_seal_task/boss/advance/confirm_task_config') // 任务确认
const deleteTaskConfig = genUpdateTemplate('/fts_hgame/roommgr_seal_task/boss/advance/delete_task_config') // 任务确认
const addTaskConfig = genUpdateTemplate('/fts_hgame/roommgr_seal_task/boss/advance/add_task_config') // 任务确认

export default {
  namespace: 'hatMonthTaskv3',

  state: {
    // 任务配置
    taskConfigInfo: { list: [], taskConfig: [], optStartTime: 0, optEndTime: 0 },
    // 发奖确认
    rewardConfirmInfo: { list: [], ruleList: [], optStartTime: 0, optEndTime: 0, canAllConfirm: false, guildSum: 0 },
    // 明细
    rewardHistoryList: [],
    // 进度
    taskProgressInfo: { list: [], info: { year: 0, month: 0, guildCount: 0, reachGuildCount: 0, reachTingCount: 0, totalSealAmount: 0, reachReward: 0 } }
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },

    updateTaskConfigList (state, { payload }) {
      return {
        ...state,
        taskConfigInfo: payload
      }
    },

    updateRewardConfirm (state, { payload }) {
      return {
        ...state,
        rewardConfirmInfo: payload
      }
    },

    updateProgressList (state, { payload }) {
      return {
        ...state,
        taskProgressInfo: payload
      }
    },

    updateRewardHistory (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        rewardHistoryList: payload
      }
    }
  },

  effects: {
    editaTaskConfig,
    confirmTaskConfig,
    deleteTaskConfig,
    addTaskConfig,

    // ------------------------任务配置-----------------------------------//
    * listTaskConfig ({ payload }, { call, put }) {
      try {
        let { data: { data, status, msg } } = yield call(api.listTaskConfig, payload)
        if (status !== 0) {
          message.error('错误' + msg)
          return
        }
        let list = Array.isArray(data.list) ? data.list : []
        yield put({
          type: 'updateTaskConfigList',
          payload: { list: list, taskConfig: data.taskConfig, optStartTime: data.optStartTime, optEndTime: data.optEndTime }
        })
      } catch (e) {
        message.error('exception', e)
      }
    },

    // ------------------------任务配置-----------------------------------//

    // ------------------------发奖确认-----------------------------------//
    * listRewardConfirm ({ payload }, { call, put }) {
      try {
        let { data: { data, status, msg } } = yield call(api.listRewardConfirm, payload)
        if (status !== 0) {
          message.error('错误' + msg)
          return
        }
        console.log(data)
        let list = Array.isArray(data.list) ? data.list : []
        for (let i = 0; i < list.length; i++) {
          list[i].idx = i + 1
        }
        yield put({
          type: 'updateRewardConfirm',
          payload: { list: list, optStartTime: data.optStartTime, optEndTime: data.optEndTime, canAllConfirm: data.canAllConfirm, guildSum: data.guildSum }
        })
      } catch (e) {
        message.error('exception', e)
      }
    },

    * flushRewardConfirm ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.flushRewardConfirm, payload)
      if (status !== 0) {
        message.error('错误' + msg)
        return
      }
      yield put({
        type: 'updateRewardConfirmTitleInfo',
        payload: {
          year: data.year,
          month: data.month,
          guildSum: data.guildSum,
          reachTing: data.reachTing,
          sealSum: data.sealSum,
          guildGiftSum: data.guildGiftSum,
          reward: data.reward,
          settlementReward: data.settlementReward,
          adjustReward: data.adjustReward
        }
      })
      console.log(data)
    },

    * approvalRewardConfirm ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.approvalRewardConfirm, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        message.success('ok')
        yield put({
          type: 'listRewardConfirm',
          payload: {}
        })
      }
    },
    // ------------------------发奖确认-----------------------------------//

    // 任务进度
    * listTaskProgress ({ payload }, { call, put }) {
      try {
        let { data: { data, status, msg } } = yield call(api.listTaskProgress, payload)
        if (status !== 0) {
          message.error('错误' + msg)
          return
        }
        let list = Array.isArray(data.list) ? data.list : []
        yield put({
          type: 'updateProgressList',
          payload: { list: list, info: data.info }
        })
      } catch (e) {
        message.error('exception', e)
      }
    },

    // 发奖明细
    * listRewardHistory ({ payload }, { call, put }) {
      try {
        let { data: { data, status, msg } } = yield call(api.listRewardHistory, payload)
        console.log(status, msg)
        data = Array.isArray(data) ? data : []
        for (let i = 0; i < data.length; i++) {
          data[i].idx = i + 1
        }
        yield put({
          type: 'updateRewardHistory',
          payload: data
        })
      } catch (e) {
        message.error('exception', e)
      }
    }

  }
}
