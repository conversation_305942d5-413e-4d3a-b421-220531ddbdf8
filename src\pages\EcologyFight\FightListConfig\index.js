import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Button, Table, Card, Form, Divider, Input, Popconfirm, Modal, Select, TimePicker, DatePicker } from 'antd'
import { connect } from 'dva'
import moment from 'moment'
const Option = Select.Option

const namespace = 'fightListConfig'
const FormItem = Form.Item
const { RangePicker } = DatePicker

@connect(({ fightListConfig }) => ({
  model: fightListConfig
}))

class FightListConfig extends Component {
  // 定义列表结构，
  columns = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: '交友sid', dataIndex: 'jySid', align: 'center' },
    { title: '交友ssid', dataIndex: 'jySsid', align: 'center' },
    { title: '交友主持uid', dataIndex: 'jyUid', align: 'center' },
    { title: '约战sid', dataIndex: 'pkSid', align: 'center' },
    { title: '约战ssid', dataIndex: 'pkSsid', align: 'center' },
    { title: '约战uid1', dataIndex: 'pkUid1', align: 'center' },
    { title: '约战uid2', dataIndex: 'pkUid2', align: 'center' },
    { title: '开启日期', dataIndex: 'date', align: 'center' },
    { title: '开启时间', dataIndex: 'time', align: 'center' },
    { title: '匹配方式', dataIndex: 'matchMode', align: 'center', render: text => ['未知', '按厅匹配', '按人匹配'][text] },
    { title: '状态', dataIndex: 'status', align: 'center', render: text => ['', '已过期', <font color='red'>生效中</font>, <font color='blue'>未生效</font>][text] },
    // { title: '添加时间', dataIndex: 'createTime', align: 'center', render: text => dateString(text) },
    // { title: '添加人', dataIndex: 'creator', align: 'center' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (record) => (
        <span>
          <Button type='primary' onClick={this.showModal(true, record)}>编辑</Button><Divider type='vertical' />
          <Popconfirm title='确认删除?' onConfirm={this.handleDel(record.id)}> <Button type='danger'>删除</Button> </Popconfirm>
        </span>),
      export: false
    }
  ]

  paginationProps = { showSizeChanger: true, pageSize: 10, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, uid: 0, sid: 0, matchMode: 0, status: 2, dataSource: [], searchDone: false }

  // 获取列表
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`
    })
  }

  // show modal
  showModal = (isUpdate, record) => () => {
    if (record == null) record = { matchMode: 1 }

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.timeLimit = v.startDate && v.endDate ? [moment.unix(v.startDate), moment.unix(v.endDate)] : []
      v.startTime = moment((v.startTime === '' || isUpdate === false ? '00:00:00' : v.startTime), 'HH:mm:ss')
      v.endTime = moment((v.endTime === '' || isUpdate === false ? '23:59:59' : v.endTime), 'HH:mm:ss')
      v.matchMode = v.matchMode == null ? 1 : v.matchMode
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // add
  onFinish = values => {
    const { dispatch } = this.props
    const data = { id: values.id, jySid: values.jySid, jySsid: values.jySsid, jyUid: values.jyUid, pkSid: values.pkSid, pkSsid: values.pkSsid, pkUid1: values.pkUid1, pkUid2: values.pkUid2, startDate: values.timeLimit[0].unix(), endDate: values.timeLimit[1].unix(), startTime: values.startTime.format('HH:mm:ss'), endTime: values.endTime.format('HH:mm:ss'), matchMode: values.matchMode, createTime: moment().unix() }
    console.log('handleSubmit', data)
    dispatch({
      type: `${namespace}/addItem`,
      payload: data
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { id: key }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // search
  handleSearch = e => {
    const { model: { list } } = this.props
    const { uid, sid, matchMode, status } = this.state
    let dataSource = list
    const userId = parseInt(uid || 0, 10)
    if (!isNaN(userId) && userId > 0) {
      dataSource = dataSource.filter(data => (data.jyUid === userId || data.pkUid1 === userId || data.pkUid2 === userId))
    }

    const shortSid = parseInt(sid || 0, 10)
    if (!isNaN(shortSid) && shortSid > 0) {
      dataSource = dataSource.filter(data => data.jySid === shortSid || data.pkSid === shortSid)
    }

    const shortMatchMode = parseInt(matchMode || 0, 10)
    if (!isNaN(shortMatchMode) && shortMatchMode > 0) {
      dataSource = dataSource.filter(data => data.matchMode === shortMatchMode)
    }

    const statusId = parseInt(status || 0, 10)
    if (!isNaN(shortMatchMode) && statusId > 0) {
      dataSource = dataSource.filter(data => data.status === statusId)
    }
    this.setState({ dataSource: dataSource, searchDone: true })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list } } = this.props
    const { dataSource, searchDone, visible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }
    let headers = []
    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.expand === undefined || !col.expand) {
        headers.push(col)
      }

      if (col.export === undefined || col.export) {
        exportHeader.push(col)
      }
    })

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form layout='inline'>
            <Form.Item label='UID:' >
              <Input onChange={e => this.setState({ uid: e.target.value })} />
            </Form.Item>
            <Form.Item label='频道:' >
              <Input onChange={e => this.setState({ sid: e.target.value })} />
            </Form.Item>
            <span style={{ marginLeft: 10, marginRight: 10 }}>匹配方式:</span>
            <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 120 }} onChange={e => this.setState({ matchMode: e.key })}>
              <Option value='0'>全部</Option>
              <Option value='1'>按厅匹配</Option>
              <Option value='2'>按人匹配</Option>
            </Select>
            <span style={{ marginLeft: 10, marginRight: 10 }}>状态:</span>
            <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 120 }} onChange={e => this.setState({ status: e.key })}>
              <Option value='0'>全部</Option>
              <Option value='1'>已过期</Option>
              <Option value='2'>生效中</Option>
              <Option value='3'>未生效</Option>
            </Select>
            <Form.Item>
              <Button style={{ marginLeft: 10, marginRight: 10 }} type='primary' onClick={this.handleSearch}>搜索</Button>
              <Button style={{ marginLeft: 10, marginRight: 10 }} type='primary' onClick={this.showModal(false)}>添加</Button>
            </Form.Item>
          </Form>
          <Divider />
          <Form>
            <Table
              dataSource={searchDone ? dataSource : list}
              columns={headers}
              rowKey={(record, index) => index}
              pagination={this.paginationProps}
              size='small'
            />
          </Form>
        </Card>

        <Modal visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit} forceRender>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <FormItem name='id' hidden>
              <Input hidden />
            </FormItem>
            <FormItem label='交友Sid' name='jySid' rules={[{ required: false, message: 'sid can not be null' }]}>
              <Input placeholder='交友Sid(按厅匹配时必填)' />
            </FormItem>
            <FormItem label='交友Ssid' name='jySsid' rules={[{ required: false, message: 'ssid can not be null' }]}>
              <Input placeholder='交友Ssid(按厅匹配时必填)' />
            </FormItem>
            <FormItem label='交友uid' name='jyUid' rules={[{ required: false, message: 'ssid can not be null' }]}>
              <Input placeholder='交友uid(按人匹配时必填)' />
            </FormItem>
            <FormItem label='约战Sid' name='pkSid' rules={[{ required: false, message: 'sid can not be null' }]}>
              <Input placeholder='约战Sid(按厅匹配时必填)' />
            </FormItem>
            <FormItem label='约战Ssid' name='pkSsid' rules={[{ required: false, message: 'ssid can not be null' }]}>
              <Input placeholder='约战Ssid(按厅匹配时必填)' />
            </FormItem>
            <FormItem label='约战uid1' name='pkUid1' rules={[{ required: false, message: 'ssid can not be null' }]}>
              <Input placeholder='约战uid1(按人匹配时必填)' />
            </FormItem>
            <FormItem label='约战uid2' name='pkUid2' rules={[{ required: false, message: 'ssid can not be null' }]}>
              <Input placeholder='约战uid2(按人匹配时选填)' />
            </FormItem>
            <FormItem label='开启日期:' name='timeLimit' rules={[{ required: true, message: 'timeLimit can not be null' }]}>
              <RangePicker showTime format='YYYY-MM-DD' />
            </FormItem>
            <FormItem label='开始时间:' name='startTime' rules={[{ required: true, message: '开始时间不能为空' }]}>
              <TimePicker style={{ width: '60%' }} />
            </FormItem>
            <FormItem label='结束时间:' name='endTime' rules={[{ required: true, message: '结束时间不能为空' }]}>
              <TimePicker style={{ width: '60%' }} />
            </FormItem>
            <FormItem label='匹配方式' name='matchMode' rules={[{ required: true, message: '匹配方式不能为空' }]}>
              <Select>
                <Option value={1}>按厅匹配</Option>
                <Option value={2}>按人匹配</Option>
              </Select>
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default FightListConfig
