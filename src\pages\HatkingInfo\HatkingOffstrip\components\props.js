import React, { Component } from 'react'
import { Button, Table, Input, Modal } from 'antd'
import { connect } from 'dva'
import { SearchOutlined } from '@ant-design/icons'

const namespace = 'offstripMain' // model 的 namespace

@connect(({ offstripMain }) => ({ // model 的 namespace
  model: offstripMain // model 的 namespace
}))
class OffstripPropsComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      selectedRowKeys: []
    }
  }

  componentDidMount () {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/getPropsConfigList`
    })
  }

  columns = [
    { title: '道具ID',
      align: 'center',
      width: 120,
      dataIndex: 'id',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ width: 300, padding: 8, borderRadius: 6, background: '#FFF', boxShadow: '0 1px 6px' }}>
          <Input style={{ width: 140, marginRight: 10 }} value={selectedKeys[0]} onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={this.handleSearchPropsId(selectedKeys, confirm)} />
          <Button style={{ marginRight: 5 }} type='primary' onClick={this.handleSearchPropsId(selectedKeys, confirm)}>搜索</Button>
          <Button onClick={this.handleResetPropsId(clearFilters)}>重置</Button>
        </div>
      ),
      filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#108ee9' : '#aaa' }} />,
      render: text => {
        text = text !== undefined ? text.toString() : ''
        const { searchTextPropsId } = this.state
        return searchTextPropsId ? (
          <span>
            {text.split(new RegExp(`(${searchTextPropsId})`, 'i')).map((fragment, i) => (
              fragment.toLowerCase() === searchTextPropsId.toLowerCase() ? <span key={i} style={{ color: '#f50' }}>{fragment}</span> : fragment
            ))}</span>) : text
      } },
    { title: '名称',
      align: 'center',
      width: 120,
      dataIndex: 'name',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ width: 300, padding: 8, borderRadius: 6, background: '#FFF', boxShadow: '0 1px 6px' }}>
          <Input style={{ width: 140, marginRight: 10 }} value={selectedKeys[0]} onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={this.handleSearch(selectedKeys, confirm)} />
          <Button style={{ marginRight: 5 }} type='primary' onClick={this.handleSearch(selectedKeys, confirm)}>搜索</Button>
          <Button onClick={this.handleReset(clearFilters)}>重置</Button>
        </div>
      ),
      filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#108ee9' : '#aaa' }} />,
      render: text => {
        const { searchText } = this.state
        return searchText && searchText.length > 0 ? (
          <span>
            {text.split(new RegExp(`(${searchText})`, 'i')).map((fragment, i) => (
              fragment.toLowerCase() === searchText.toLowerCase() ? <span key={i} style={{ color: '#f50' }}>{fragment}</span> : fragment
            ))}</span>) : text
      }
    },
    { title: '单价', align: 'center', width: 120, dataIndex: 'price' },
    // { title: '货币类型', align: 'center', width: 120, dataIndex: 'priceType' },
    { title: '道具类型', align: 'center', width: 150, dataIndex: 'type', render: text => text && text.length > 0 ? text : '付费道具' }
    // { title: '生效日期', align: 'center', width: 180, dataIndex: 'startTime', render: text => moment.unix(text / 1000).format('YYYY-MM-DD HH:mm:ss') },
    // { title: '失效日期', align: 'center', dataIndex: 'endTime', render: text => moment.unix(text / 1000).format('YYYY-MM-DD HH:mm:ss') }
  ]

   // 本地搜索
   handleSearch = (selectedKeys, confirm) => () => {
     const { dispatch, model: { localPropsList } } = this.props
     let list = []
     // 根据条件筛选
     for (var i = 0; i < localPropsList.length; i++) {
       if (localPropsList[i].name.toString().toLowerCase().includes(selectedKeys[0].toLowerCase())) {
         list = [...list, localPropsList[i]]
       }
     }
     dispatch({
       type: `${namespace}/updatePropsList`,
       payload: Array.isArray(list) ? list : []
     })
     confirm()
     this.setState({ searchText: selectedKeys[0] })
   }

  // 重置搜索
  handleReset = clearFilters => () => {
    const { dispatch, model: { localPropsList } } = this.props
    dispatch({
      type: `${namespace}/updatePropsList`,
      payload: localPropsList
    })
    clearFilters()
    this.setState({ searchText: '' })
  }

  // 根据PropsId搜索
  handleSearchPropsId = (selectedKeys, confirm) => () => {
    const { dispatch, model: { localPropsList } } = this.props
    let list = []
    for (var i = 0; i < localPropsList.length; i++) {
      if (localPropsList[i].id.toString().toLowerCase().includes(selectedKeys[0].toLowerCase())) {
        list = [...list, localPropsList[i]]
      }
    }
    dispatch({
      type: `${namespace}/updatePropsList`,
      payload: Array.isArray(list) ? list : []
    })
    confirm()
    this.setState({ searchTextPropsId: selectedKeys[0] })
  }

  // 重置 PropsId搜索条件
  handleResetPropsId = clearFilters => () => {
    const { dispatch, model: { localPropsList } } = this.props
    dispatch({
      type: `${namespace}/updatePropsList`,
      payload: localPropsList
    })
    clearFilters()
    this.setState({ searchTextPropsId: '' })
  }

  // 更新营收礼物配置
  handleSelect = () => {
    const { onChange } = this.props
    const { selectedProps } = this.state
    console.log(selectedProps)
    if (selectedProps == null) {
      return
    }

    // 调用父组件的方法，通知父组件更新的信息
    if (onChange) {
      onChange(selectedProps)
    }

    this.setState({ editRow: null, selectedProps: null, selectedRowKeys: [] })
  }

  handleSelectOnChange = (selectedRowKeys, record) => {
    console.log(selectedRowKeys, record)
    this.setState({ selectedRowKeys: selectedRowKeys, selectedProps: record[0] })
  }

  onRow = (record, index) => {
    return {
      onClick: () => { // 单击选中行， 重复单击取消选中
        const { selectedRowKeys } = this.state
        let list = selectedRowKeys.length === 0 ? [index] : selectedRowKeys[0] === index ? [] : [index]
        this.setState({ selectedRowKeys: list, selectedProps: record })
      }
    }
  }

  // 禁用单选
  getCheckboxProps = () => {
    return { disabled: true }
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { visible, onCancel, model: { propsList } } = this.props
    const { selectedRowKeys } = this.state
    return (
      <Modal onOk={this.handleSelect} okText='选中' cancelText='取消' okButtonProps={{ disabled: selectedRowKeys.length === 0 }} width={1100} title='选择礼物' visible={visible} onCancel={onCancel}>
        <Table onRow={this.onRow} bordered rowSelection={{ getCheckboxProps: this.getCheckboxProps, selectedRowKeys, onChange: this.handleSelectOnChange, type: 'radio' }} scroll={{ y: 340 }} dataSource={propsList} size='small' columns={this.columns} rowKey={(record, index) => index} />
      </Modal>
    )
  }
}

export default OffstripPropsComponent
