import { getLists, update, remove } from './api'
import { genGetRequireTemplate } from '@/utils/common'
import { message } from 'antd'

const getActivityList = genGetRequireTemplate('/reward_agent/activity/simple_list', 'activityList')
const getGroupList = genGetRequireTemplate('/reward_agent/user_group/list_group', 'groupList')

export default {
  namespace: 'activityIconConfig', // 只有这里需要修改

  state: {
    list: []
    // activityList: [], // 发奖代理_活动列表
    // groupList: [] // 发奖代理_业务列表
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    getActivityList,
    getGroupList,
    * getList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getLists, payload)

      yield put({
        type: 'updateList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * addItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(update, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'update'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * updateItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * removeItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(remove, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * getItemByKey ({ payload }, { call, put }) {
      const { data: { status, list } } = yield call(payload)
      if (status === 0) {
        yield put({
          type: 'updateList',
          payload: list
        })
      } else {
        message.warning('not found')
      }
    }
  }
}
