import { timeFormater } from '@/utils/common.js'
import { getLists } from './api'
import { Modal } from 'antd'

export default {
  namespace: 'afkChart',
  state: {
    updating: false,
    displayData: [],
    begTime: 0,
    endTime: 0,
    currentPage: 1,
    currentSize: 0
  },

  reducers: {
    // 更新单个state成员的值
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },
    // 更新currentSize和currentPage
    updatePageAndSize (state, { payload: data }) {
      const { page, size } = data
      if (page === undefined || size === undefined) {
        console.error('unexpect params page or size:', page, size)
        return
      }
      return {
        ...state,
        currentSize: size,
        currentPage: page
      }
    }
  },

  effects: {
    // 请求并刷新列表数据
    * getAfkChartData ({ payload }, { select, call, put }) {
      let resp = yield call(getLists, payload)

      const { data } = resp
      if (data === undefined) {
        Modal.error({ content: '获取数据失败，请检查控制台' })
        console.error('getAfkChartData() get data error: response=', resp)
        return
      }
      const { status, msg, list } = data
      if (status !== 0) {
        Modal.error({ content: '获取数据有误，请检查控制台' })
        console.error('getAfkChartData() status=' + status + ' msg=' + msg)
        return
      }
      let rspList = list
      // 数据列表
      let dataList = []
      rspList.forEach(function (item) {
        let date = timeFormater(item.date, 0)
        // 当前交友开播总主持数
        dataList.push({ date: date, type: '当前交友开播总主持数', value: item.liveCnt })

        // 所有符合手Y推荐的主播
        dataList.push({ date: date, type: '所有符合手Y推荐的主播', value: item.canRecommendCnt })

        // 命中挂播主持数
        dataList.push({ date: date, type: '命中挂播主持数', value: item.liveHangUpCnt })
      })

      yield put({
        type: 'updateState',
        payload: { name: 'displayData', newValue: dataList }
      })
    }
  }
}
