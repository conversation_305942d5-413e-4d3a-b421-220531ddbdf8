import { genGetListTemplate, genUpdateTemplate } from '@/utils/common'

const getUIDBlackList = genGetListTemplate('/ch_protection/admin/get_uid_black_list', 'uidBlackList')
const getOpLogList = genGetListTemplate('/ch_protection/admin/get_black_list_op_log', 'OplogList', (raw) => { return raw.sort((a, b) => { return b.timestamp - a.timestamp }) })
const getChannelStatus = genGetListTemplate('/ch_protection/admin/get_all_channel_status', 'ChannelStatus', (raw) => {
  let after = []
  raw.map((v, i) => {
    const { opLog } = v
    delete v['opLog']
    after.push({ ...v, ...opLog })
  })
  return after
})
const updateUIDBlackList = genUpdateTemplate('/ch_protection/admin/batch_update_black_list')
const updateChannelEnterConfig = genUpdateTemplate('/ch_protection/admin/update_channel_enter_config')

export default {
  namespace: 'channelEnterControl',
  state: {
    currentPage: 1,
    currentSize: 0,
    uidBlackList: [],
    OplogList: [],
    ChannelStatus: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getUIDBlackList,
    updateUIDBlackList,
    getOpLogList,
    getChannelStatus,
    updateChannelEnterConfig
  }
}
