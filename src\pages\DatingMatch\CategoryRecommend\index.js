import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Modal, Input, DatePicker, Select, Upload, message } from 'antd'
import { connect } from 'dva'
import PopImage from '@/components/PopImage'
import { CSVLink } from 'react-csv'
import { UploadOutlined } from '@ant-design/icons'

const namespace = 'CategoryRecommend'
const FormItem = Form.Item
const Search = Input.Search
const Option = Select.Option
var moment = require('moment')

@connect(({ CategoryRecommend }) => ({
  CategoryRecommend
}))

class Index extends Component {
  // column structs.
  columns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: '主持YY号',
      dataIndex: 'yy_number',
      key: 'yy_number',
      align: 'center',
      render: (text, record) => {
        if (text === 0) {
          return ''
        }
        return text
      }
    },
    { title: 'UID',
      dataIndex: 'uid',
      key: 'uid',
      align: 'center',
      render: (text, record) => {
        if (text === 0) {
          return ''
        }
        return text
      }
    },
    { title: '开播状态',
      dataIndex: 'live_state',
      key: 'live_state',
      align: 'center',
      render: (text, record) => {
        switch (text) {
          case 1: return '开播中'
          case 2: return '未开播'
        }
        return text
      }
    },
    { title: '主持昵称', dataIndex: 'nick', key: 'nick', align: 'center' },
    { title: '配置频道', dataIndex: 'sid', key: 'sid', align: 'center' },
    { title: '配置子频道',
      dataIndex: 'ssid',
      key: 'ssid',
      align: 'center',
      render: (text, record) => {
        if (text === 0) {
          return ''
        }
        return text
      }
    },
    { title: '推荐标题', dataIndex: 'title', key: 'title', align: 'center' },
    { title: '推荐海报',
      dataIndex: 'cover',
      key: 'cover',
      align: 'center',
      render: (text, record) => (
        <PopImage value={record.cover} />
      )
    },
    { title: '权重', dataIndex: 'weight', key: 'weight', align: 'center' },
    { title: '推荐位置',
      dataIndex: 'zone',
      key: 'zone',
      align: 'center',
      render: (text, record) => {
        switch (text) {
          case 1100: return '声音恋人'
          case 3007: return '独家主持'
          case 3008: return '颜值在线'
          case 3009: return '新人可撩'
          case 3010: return '超级天团'
          case 3011: return '精彩直播'
          case 3014: return 'A0活动态'
          case 3015: return 'A1活动态'
          case 3016: return 'A2活动态'
          case 3012: return 'A3活动态'
          case 3017: return 'A4活动态'
          case 3018: return 'A5活动态'
          case 3013: return 'A6活动态'
          case 3019: return '乱斗1'
          case 3020: return '乱斗2'
        }
        return ''
      }
    },
    { title: '推荐开始时间', dataIndex: 'recomm_start_time', key: 'recomm_start_time', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD HH:mm:ss') },
    { title: '推荐结束时间', dataIndex: 'recomm_end_time', key: 'recomm_end_time', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD HH:mm:ss') },
    { title: '推荐状态',
      dataIndex: 'state',
      key: 'state',
      align: 'center',
      render: (text, record) => (
        text === 0 ? '推荐' : '未推荐'
      )
    },
    { title: '推荐方式',
      dataIndex: 'mode',
      key: 'mode',
      align: 'center',
      render: (text, record) => (
        text === 1 ? '按人推荐' : '按厅推荐'
      )
    },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <Button style={{ marginRight: 10 }} size='small' type='primary' onClick={this.showModal(true, record)}>修改</Button>
          { record.state === 1 ? <Button size='small' type='primary' onClick={this.updateState(record.id, 1)}>上推荐</Button> : <Button size='small' type='primary' onClick={this.updateState(record.id, 2)}>下推荐</Button>}
        </span>)
    }
  ]

  defaultPageValue = {
    defaultPageSize: 50,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, selectedRowKeys: [] }

  // show modal
  showModal = (isUpdate, record) => () => {
    if (record == null) record = { uid: '', weight: '', recomm_start_time: moment().unix(), recomm_end_time: moment().unix() }

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.recomm_start_time = moment.unix(v.recomm_start_time)
      v.recomm_end_time = moment.unix(v.recomm_end_time)
      v.weight = v.weight ? v.weight : 1100
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  onSelectChange = (selectedRowKeys, selectedRows) => {
    var value = selectedRows.map(item => item.id).join(',')
    console.log(value)
    this.setState({ selectedRowKeys })
    this.setState({ removeKey: value })
    this.setState({ exportKey: selectedRows })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // add and update
  onFinish = values => {
    const { dispatch } = this.props
    const { isUpdate } = this.state
    values.recomm_start_time = values.recomm_start_time.unix()
    values.recomm_end_time = values.recomm_end_time.unix()
    values.weight = parseInt(values.weight)
    const url = isUpdate ? `${namespace}/updateItem` : `${namespace}/addItem`
    dispatch({
      type: url,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { ids: this.state.removeKey }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
    this.setState({ selectedRowKeys: null })
  }

  handleDelOne = id => e => {
    const { dispatch } = this.props
    const data = { ids: id }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  updateState = (id, recomm) => e => {
    const { dispatch } = this.props
    const data = { id: id, recomm: recomm }
    dispatch({
      type: `${namespace}/updateItem`,
      payload: data
    })
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = {}
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // reset search info
  handleReset = clearFilters => () => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  // get list from server.
  searchBySid = (value) => {
    const { dispatch } = this.props
    const data = { sid: this.state.searchSid, livestate: this.state.livestate }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }
  searchBySsid = (value) => {
    const { dispatch } = this.props
    const data = { ssid: this.state.searchSsid, livestate: this.state.livestate }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }
  searchByYY = (value) => {
    const { dispatch } = this.props
    const data = { yynum: this.state.searchYY }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  handleLiveStateChange = (value) => {
    const { dispatch } = this.props
    const data = { livestate: value.key, zone: this.state.zone, mode: this.state.mode }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })

    this.setState({ livestate: value.key })
  }

  handleModeChange = (value) => {
    const { dispatch } = this.props
    const data = { livestate: this.state.livestate, zone: this.state.zone, mode: value.key }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })

    this.setState({ mode: value.key })
  }

  handleZoneChange = (value) => {
    const { dispatch } = this.props
    const data = { livestate: this.state.livestate, mode: this.state.mode, zone: value.key }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })

    this.setState({ zone: value.key })
  }

  UpLoadOnChange = info => {
    if (info.file.status !== 'done') {
      return
    }
    if (info.file.response.status === 0) {
      message.success(`${info.file.name} file uploaded successfully`)
    } else {
      message.error(info.file.response.msg)
    }
    const { dispatch } = this.props
    const data = {}
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // content
  render () {
    const { selectedRowKeys, visible, title, value, isUpdate } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange
    }
    const { route, CategoryRecommend: { list } } = this.props
    let headers = [
      { label: '主持YY号', key: 'yy_number' },
      { label: 'Uid', key: 'uid' },
      { label: '配置频道', key: 'sid' },
      { label: '配置子频道', key: 'ssid' },
      { label: '推荐标题', key: 'title' },
      { label: '权重', key: 'weight' },
      { label: '推荐标题', key: 'title' },
      { label: '推荐位置', key: 'zone' },
      { label: '推荐开始时间', key: 'recomm_start_time' },
      { label: '推荐结束时间', key: 'recomm_end_time' },
      { label: '推荐状态', key: 'state' },
      { label: '推荐方式', key: 'mode' }
    ]

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Search onSearch={value => this.searchByYY(value)} placeholder='search by yy' onChange={e => this.setState({ searchYY: e.target.value })} style={{ width: 150 }} /> {/* 搜索按钮 */}
            <Search onSearch={value => this.searchBySid(value)} placeholder='search by sid' onChange={e => this.setState({ searchSid: e.target.value })} style={{ width: 150 }} /> {/* 搜索按钮 */}
            <Search onSearch={value => this.searchBySsid(value)} placeholder='search by ssid' onChange={e => this.setState({ searchSsid: e.target.value })} style={{ width: 150 }} /> {/* 搜索按钮 */}
            <Divider type='vertical' /> {/* 分割线 */}
            开播状态
            <Divider type='vertical' /> {/* 分割线 */}
            <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 120 }} onChange={this.handleLiveStateChange}>
              <Option value='0'>全部</Option>
              <Option value='1'>开播中</Option>
              <Option value='2'>未开播</Option>
            </Select>
            <Divider type='vertical' /> {/* 分割线 */}
            选择分类
            <Divider type='vertical' /> {/* 分割线 */}
            <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 120 }} onChange={this.handleZoneChange}>
              <Option value='0'>全部</Option>
              <Option value='1100'>声音恋人</Option>
              <Option value='3007'>独家主持</Option>
              <Option value='3008'>颜值在线</Option>
              <Option value='3009'>新人可撩</Option>
              <Option value='3010'>超级天团</Option>
              <Option value='3011'>精彩直播</Option>
              <Option value='3014'>A0活动态</Option>
              <Option value='3015'>A1活动态</Option>
              <Option value='3016'>A2活动态</Option>
              <Option value='3012'>A3活动态</Option>
              <Option value='3017'>A4活动态</Option>
              <Option value='3018'>A5活动态</Option>
              <Option value='3013'>A6活动态</Option>
              <Option value='3019'>乱斗1</Option>
              <Option value='3020'>乱斗2</Option>
            </Select>
            <Divider type='vertical' /> {/* 分割线 */}
            推荐方式
            <Divider type='vertical' /> {/* 分割线 */}
            <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 120 }} onChange={this.handleModeChange}>
              <Option value='0'>全部</Option>
              <Option value='1'>按人推荐</Option>
              <Option value='2'>按厅推荐</Option>
            </Select>
            <Divider type='vertical' /> {/* 分割线 */}
            <Button type='primary'>
              <a href='http://makefriends.bs2dl.yy.com/1542101590_4135a76904a4ca3155f5a6031a24bd15.xlsx' type='primary' onClick={this.downLoad}>配置模板</a>
            </Button>
            <Divider type='vertical' />
            <Upload action='/dating_match_bosssvr/v3/category_recommend/insert' onChange={this.UpLoadOnChange} >
              <Button type='primary'>
                <UploadOutlined /> 批量导入
              </Button>
            </Upload>
            <Divider /> {/* 分割线 */}
            <Button type='primary' onClick={this.handleDel(1)}>删除</Button>
            <Divider type='vertical' /> {/* 分割线 */}
            <CSVLink data={list} filename={'output.csv'} headers={headers}>导出</CSVLink>
            <Divider />`
            <Table rowKey={record => record.id} rowSelection={rowSelection} dataSource={list} columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
          </Form>
        </Card>

        { value.mode === 1
          ? <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
            <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
              <FormItem label='id' name='id' rules={[{ required: true }]}>
                <Input readOnly={isUpdate} />
              </FormItem>
              <FormItem label='uid' name='uid' rules={[{ required: true }]}>
                <Input readOnly={isUpdate} />
              </FormItem>
              <FormItem label='权重' name='weight' rules={[{ required: true }]}>
                <Input />
              </FormItem>
              <FormItem label='推荐标题' name='title' rules={[{ required: true }]}>
                <Input />
              </FormItem>
              <FormItem label='推荐位置' name='zone' rules={[{ required: true }]}>
                <Select>
                  <Option value={0}>未知</Option>
                  <Option value={1100}>声音恋人</Option>
                  <Option value={3007}>独家主持</Option>
                  <Option value={3008}>颜值在线</Option>
                  <Option value={3009}>新人可撩</Option>
                  <Option value={3010}>超级天团</Option>
                  <Option value={3011}>精彩直播</Option>
                  <Option value={3014}>A0活动态</Option>
                  <Option value={3015}>A1活动态</Option>
                  <Option value={3016}>A2活动态</Option>
                  <Option value={3012}>A3活动态</Option>
                  <Option value={3017}>A4活动态</Option>
                  <Option value={3018}>A5活动态</Option>
                  <Option value={3013}>A6活动态</Option>
                  <Option value={3019}>乱斗1</Option>
                  <Option value={3020}>乱斗2</Option>
                </Select>
              </FormItem>
              <FormItem label='开始时间' name='recomm_start_time' rules={[{ required: true, message: '开始时间不能为空' }]}>
                <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
              </FormItem>
              <FormItem label='结束时间' name='recomm_end_time' rules={[{ required: true, message: '结束时间不能为空' }]}>
                <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
              </FormItem>
            </Form>
          </Modal>
          : <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
            <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
              <FormItem label='id' name='id' rules={[{ required: true }]}>
                <Input readOnly={isUpdate} />
              </FormItem>
              <FormItem label='sid' name='sid' rules={[{ required: true }]}>
                <Input readOnly={isUpdate} />
              </FormItem>
              <FormItem label='ssid' name='ssid' rules={[{ required: true }]}>
                <Input readOnly={isUpdate} />
              </FormItem>
              <FormItem label='权重' name='weight' rules={[{ required: true }]}>
                <Input />
              </FormItem>
              <FormItem label='推荐标题' name='title' rules={[{ required: true }]}>
                <Input />
              </FormItem>
              <FormItem label='推荐位置' name='zone' rules={[{ required: true }]}>
                <Select>
                  <Option value={0}>未知</Option>
                  <Option value={1100}>声音恋人</Option>
                  <Option value={3007}>独家主持</Option>
                  <Option value={3008}>颜值在线</Option>
                  <Option value={3009}>新人可撩</Option>
                  <Option value={3010}>超级天团</Option>
                  <Option value={3011}>精彩直播</Option>
                  <Option value={3014}>A0活动态</Option>
                  <Option value={3015}>A1活动态</Option>
                  <Option value={3016}>A2活动态</Option>
                  <Option value={3012}>A3活动态</Option>
                  <Option value={3017}>A4活动态</Option>
                  <Option value={3018}>A5活动态</Option>
                  <Option value={3013}>A6活动态</Option>
                  <Option value={3019}>乱斗1</Option>
                  <Option value={3020}>乱斗2</Option>
                </Select>
              </FormItem>
              <FormItem label='开始时间' name='recomm_start_time' rules={[{ required: true, message: '开始时间不能为空' }]}>
                <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
              </FormItem>
              <FormItem label='结束时间' name='recomm_end_time' rules={[{ required: true, message: '结束时间不能为空' }]}>
                <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
              </FormItem>
            </Form>
          </Modal> }
      </PageHeaderWrapper>
    )
  }
}

export default Index
