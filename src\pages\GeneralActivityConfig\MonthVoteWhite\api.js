import request from '@/utils/request'
import { stringify } from 'querystring'

export function getLists () {
  return request(`/rank/activity_vote/vote_white_list`)
}

export function whiteListAdd (params) {
  return request(`/rank/activity_vote/vote_white_add?${stringify(params)}`)
}

export function whiteListDel (params) {
  return request(`/rank/activity_vote/vote_white_remove?${stringify(params)}`)
}
