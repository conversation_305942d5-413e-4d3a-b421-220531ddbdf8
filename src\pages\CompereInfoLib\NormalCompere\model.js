import { message } from 'antd'
import * as api from './api'

export default {
  namespace: 'normalCompere',

  state: {
    list: [],
    remarkInfo: {},
    totalSize: 10,
    exportTotalList: [],
    remarkHistoryRecords: [],
    compereDetailInfo: {}
  },

  reducers: {
    // 单个修改某个state成员
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },

    displayList (state, { payload, total }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      console.log(total)
      return {
        ...state,
        list: payload,
        totalSize: total
      }
    },

    remarkRecords (state, { payload }) {
      console.log(payload)
      return {
        ...state,
        remarkHistoryRecords: payload
      }
    },

    listExport (state, { payload, total }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }

      return {
        ...state,
        exportTotalList: payload
      }
    },

    getRemark (state, { payload }) {
      return {
        ...state,
        remarkInfo: payload
      }
    }
  },

  effects: {
    * getRemarkInfo ({ payload }, { call, put }) {
      console.log('payload=', payload)
      const { cbFunc } = payload
      // eslint-disable-next-line camelcase
      let { data: { code, msg, remark_info } } = yield call(api.getRemarkInfo, payload)
      console.log(code, msg, remark_info, cbFunc)
      if (code !== 0) {
        message.error({ content: '查询备注失败' })
      }
      cbFunc(remark_info)
      yield put({
        type: 'getRemark',
        payload: remark_info
      })
    },

    * updateRemarkInfo ({ payload }, { call, put }) {
      const { cbFunc } = payload

      let { data: { code, msg } } = yield call(api.updateRemarkInfo, payload)
      console.log(code, msg)
      if (code !== 0) {
        message.error({ content: '备注更新失败' })
      }

      message.success({ content: '修改备注成功' })
      cbFunc()
    },

    * listCompere ({ payload }, { call, put }) {
      if (!payload.page) {
        payload.page = 1
      }
      if (!payload.size) {
        payload.size = 10
      }
      let { data: { list, total, code, msg } } = yield call(api.listCompere, payload)
      if (code !== 0) {
        message.warning(msg)
        return
      }

      let dataNew = Array.isArray(list) ? list : []

      for (let i = 0; i < dataNew.length; i++) {
        dataNew[i].idx = i + 1
      }
      yield put({
        type: 'displayList',
        payload: dataNew,
        total
      })
    },

    * refreshCompereList ({ payload }, { call, put }) {
      let { data: { code, msg } } = yield call(api.refreshCompereList, payload)
      if (code !== 0) {
        message.warning(msg)
        return
      }
      yield put({
        type: 'listCompere',
        payload: { uid: payload.uids, ds: payload.ds }
      })
    },

    * exportList ({ payload }, { call, put }) {
      const { cbFunc } = payload
      let { data: { list, total, code, msg } } = yield call(api.listCompere, payload)
      if (code !== 0) {
        message.warning(msg)
      }
      console.log(list)
      let dataNew = Array.isArray(list) ? list : []
      for (let i = 0; i < dataNew.length; i++) {
        dataNew[i].idx = i + 1
      }
      console.log(dataNew)
      yield put({
        type: 'listExport',
        payload: dataNew,
        total
      })

      cbFunc()
    },

    * getRemarkHistory ({ payload }, { call, put }) {
      console.log('payload=', payload)

      // eslint-disable-next-line camelcase
      let { data: { code, msg, records } } = yield call(api.getRemarkHistory, payload)
      console.log(code, msg, records)
      if (code !== 0) {
        message.error({ content: '查询备注历史失败' })
      }

      yield put({
        type: 'remarkRecords',
        payload: records
      })
    },

    * getCompereDetailInfo ({ payload }, { call, put }) {
      try {
        let { data: { data, status, msg } } = yield call(api.getCompereDetailInfo, payload)

        if (status !== 0) {
          message.error(msg)
          return
        }
        yield put({
          type: 'updateState',
          payload: { name: 'compereDetailInfo', newValue: data }
        })
      } catch (e) {
        message.error('exception: ', e)
      }
    },

    * recoverSuperCompere ({ payload }, { call, put }) {
      try {
        let { data: { status, msg } } = yield call(api.recoverSuperCompere, payload)

        if (status !== 0) {
          message.error(msg)
        } else {
          message.info('已发起申请, 请到通用审批页面审批')
        }
      } catch (e) {
        message.error('exception: ', e)
      }
    }
  }
}
