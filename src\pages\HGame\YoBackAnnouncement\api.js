import request from '@/utils/request'
import { stringify } from 'qs'

// 获取公告列表
export function listAnnouncement (params) {
  return request(`/dating_match/yo/boss/announcement/list?${stringify(params)}`)
}

// 新增公告
export function addAnnouncement (params) {
  return request(`/dating_match/yo/boss/announcement/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 更新公告
export function updateAnnouncement (params) {
  return request(`/dating_match/yo/boss/announcement/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 删除公告
export function deleteAnnouncement (params) {
  return request(`/dating_match/yo/boss/announcement/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}
