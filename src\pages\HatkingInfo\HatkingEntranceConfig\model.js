import { message } from 'antd'
import { getHistorySwitchLists, getLevelLists, updateHistorySwitchInfo, updateLevelLimitInfo } from './api'

export default {
  namespace: 'hatkingEntranceConfig',

  state: {
    list: [],
    historyList: []
  },

  reducers: {
    updateLevelList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    },

    updateHistoryList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        historyList: payload
      }
    }
  },

  effects: {
    * getLevelList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getLevelLists, payload)

      yield put({
        type: 'updateLevelList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * getHistoryList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getHistorySwitchLists, payload)

      yield put({
        type: 'updateHistoryList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * updateLevelItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updateLevelLimitInfo, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getLevelLists'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * updateHistoryItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updateHistorySwitchInfo, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getHistorySwitchLists'
        })
      } else {
        message.error('failed' + msg)
      }
    }
  }
}
