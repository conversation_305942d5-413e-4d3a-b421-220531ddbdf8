import { getPropsList, getPropsDetailList, updatePropsDetail, deletePropsDetail, getPropsClassList } from './api'
import { message } from 'antd'

export default {
  namespace: 'propsDetailConfig',

  state: {
    list: [],
    propsList: [],
    classList: [],
    localPropsList: []
  },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i
      }
      return {
        ...state,
        list: payload
      }
    },

    updatePropsList (state, { payload }) {
      return {
        ...state,
        propsList: payload
      }
    },

    updateLocalPropsList (state, { payload }) {
      return {
        ...state,
        propsList: payload,
        localPropsList: payload
      }
    },

    updateClassList (state, { payload }) {
      return {
        ...state,
        classList: payload
      }
    },

    updateOption (state, { legend, xAxis, series, count }) {
      return {
        ...state,
        legend: legend,
        xAxis: xAxis,
        series: series,
        count: count
      }
    }
  },

  effects: {
    * getPropsDetailList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getPropsDetailList)

      yield put({
        type: 'updateList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * getPropsClassList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getPropsClassList)

      yield put({
        type: 'updateClassList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * getPropsConfigList ({ payload }, { call, put }) {
      const { data: { propsList } } = yield call(getPropsList)

      yield put({
        type: 'updateLocalPropsList',
        payload: Array.isArray(propsList) ? propsList : []
      })
    },

    * update ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updatePropsDetail, payload)
      if (status === 0) {
        message.success('更新成功!', 5)
        yield put({
          type: 'getPropsDetailList'
        })
      } else {
        // yield put({
        //   type: 'getPropsDetailList'
        // })
        message.error('更新失败： ' + msg, 10)
      }
    },

    * delete ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(deletePropsDetail, payload)
      if (status === 0) {
        message.success('更新成功!', 5)
        yield put({
          type: 'getPropsDetailList'
        })
      } else {
        message.error('更新失败： ' + msg, 10)
      }
    }
  }
}
