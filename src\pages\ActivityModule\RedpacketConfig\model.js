import { getPropsList, getConfigList, updateConfig, getRoleList, updateRoleList } from './api'
import { message } from 'antd'

export default {
  namespace: 'redpacketLotteryConfig',

  state: {
    list: [
    ],
    propsList: [
    ]
  },

  reducers: {
    updateList (state, { list }) {
      for (var i = 0; i < list.length; i++) {
        list[i].index = i
      }
      return {
        ...state,
        list: list
      }
    },
    updateRoleList (state, { roles }) {
      for (var i = 0; i < roles.length; i++) {
        roles[i].index = i
      }
      return {
        ...state,
        roles: roles
      }
    },
    updatePropsList (state, { payload }) {
      return {
        ...state,
        propsList: payload
      }
    }
  },

  effects: {
    * getConfig ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getConfigList, payload)

      yield put({
        type: 'updateList',
        list: Array.isArray(list) ? list : []
      })
    },

    * getRole ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getRoleList, payload)

      yield put({
        type: 'updateRoleList',
        roles: Array.isArray(list) ? list : []
      })
    },

    * getPropsConfigList ({ payload }, { call, put }) {
      const { data: { propsList } } = yield call(getPropsList)

      yield put({
        type: 'updatePropsList',
        payload: Array.isArray(propsList) ? propsList : []
      })
    },

    * update ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updateConfig, payload)
      if (status === 0) {
        message.success('更新成功!', 5)
      } else {
        message.error('更新失败： ' + msg, 10)
      }
    },

    * updateRole ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updateRoleList, payload)
      if (status === 0) {
        message.success('更新成功!', 5)
      } else {
        message.error('更新失败： ' + msg, 10)
      }
    }
  }
}
