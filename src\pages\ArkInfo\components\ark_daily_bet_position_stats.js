import React, { Component } from 'react'
import { Table, Divider, <PERSON><PERSON>, Card, Form, DatePicker } from 'antd'
import { connect } from 'dva'
import exportExcel from '@/utils/exportExcel'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
const positionMap = { 0: 'ALL', 1: '位置1', 2: '位置2', 3: '位置3', 4: '位置4', 5: '位置5', 6: '位置6', 7: '位置7', 8: '位置8' }

let positionFilter = []
for (let i = 0; i < 9; i++) {
  positionFilter.push({ text: positionMap[i], value: i })
}

@connect(({ arkJy }) => ({ // model 的 namespace
  model: arkJy // model 的 namespace
}))
class ArkDailyBetPositionStatsComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      list: [],
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
  }

  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '参与位置', dataIndex: 'position', align: 'center', render: text => { return positionMap[text] }, filters: positionFilter, defaultFilteredValue: ['0'], onFilter: (value, record) => record.position === value },
    { title: '参与流水(元)', dataIndex: 'betAmethyst', align: 'center' },
    { title: '发放流水(元)', dataIndex: 'winAmethyst', align: 'center' },
    { title: '发放占比', dataIndex: 'betRewardRatio', align: 'center' }
  ]

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getPositionStatsList`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  onExport = () => {
    let headers = []
    let columns = this.columns
    columns.forEach(function (item) {
      headers.push({ key: item.dataIndex, header: item.title })
    })

    const { model: { positionStatsList } } = this.props
    var exportData = positionStatsList.map(item => {
      let v = $.extend(true, {}, item)
      v.position = positionMap[v.position]
      return v
    })

    exportExcel(headers, exportData)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { positionStatsList } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form>
          <span style={{ marginLeft: 10 }}>时间范围:</span>
          <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
          <Divider />
          <Table dataSource={positionStatsList} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default ArkDailyBetPositionStatsComponent
