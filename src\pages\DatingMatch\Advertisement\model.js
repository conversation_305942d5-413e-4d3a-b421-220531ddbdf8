import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const getAdvertisementList = genGetRequireTemplate('/dating_match/advertisement/getList', 'list')
const addAdvertisement = genUpdateTemplate('/dating_match/advertisement/ADD')
const updateAdvertisement = genUpdateTemplate('/dating_match/advertisement/UPDATE')
const deleteAdvertisement = genUpdateTemplate('/dating_match/advertisement/DELETE')

export default {
  namespace: 'advertisement',
  state: {
    list: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getAdvertisementList,
    addAdvertisement,
    updateAdvertisement,
    deleteAdvertisement
  }
}
