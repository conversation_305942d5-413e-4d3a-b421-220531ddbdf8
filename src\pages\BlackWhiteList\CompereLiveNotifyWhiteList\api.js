import request from '@/utils/request'

export function getLists () {
  let url = `/white_list/compere_live_notify_list`
  return request(url, { jsonp: true })
}

export function whiteListAdd (uid, sid) {
  let form = 'uid=' + uid + '&sid=' + sid
  return request(`/white_list/compere_live_notify_add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: form
  })
}

export function whiteListDel (uid, sid) {
  let form = 'uid=' + uid + '&sid=' + sid
  return request(`/white_list/compere_live_notify_del`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: form
  })
}
