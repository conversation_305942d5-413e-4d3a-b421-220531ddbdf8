/* eslint-disable eqeqeq */
import { getLists, whiteListAdd, whiteListDel } from './api'
import { message } from 'antd'

export default {
  namespace: 'monthVoteWhite',
  state: {
    list: []
  },

  reducers: {
    // 更新data到频道视频白名单数据列表
    updateList (state, { payload }) {
      for (let i = 0; i < payload.length; i++) payload[i].index = i + 1

      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    // 请求并刷新频道视频白名单列表数据
    * getWhiteListData ({ params }, { call, put }) {
      const { data: { status, msg, list } } = yield call(getLists)
      if (status === 0) {
        yield put({
          type: 'updateList',
          payload: Array.isArray(list) ? list : []
        })
      } else {
        message.error(msg)
      }
    },

    // 添加频道视频白名单
    * addWhiteListBySid ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(whiteListAdd, payload)
      if (status === 0) {
        yield put({
          type: 'getWhiteListData'
        })
      } else {
        message.error(msg)
      }
    },

    // 删除频道视频白名单
    * delWhiteListByUid ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(whiteListDel, payload)
      if (status === 0) {
        yield put({
          type: 'getWhiteListData'
        })
      } else {
        message.error(msg)
      }
    }
  }
}
