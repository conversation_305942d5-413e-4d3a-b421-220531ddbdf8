import request from '@/utils/request'

export function getConfigList (params) {
  return request(`/redpacket/get_lottery_config_list`)
}

export function getPropsList () {
  return request(`/lottery/get_props_config_list`)
}

export function updateConfig (params) {
  return request(`/redpacket/update_lottery_config_list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function getRoleList (params) {
  return request(`/redpacket/get_lottery_role_list`)
}

export function updateRoleList (params) {
  return request(`/redpacket/update_lottery_role_list`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
