import { message } from 'antd'
import moment from 'moment'

// ============= 背景状态 ====================

export const bgStatusOptions = [
  { label: '全部', value: -1 }, 
  { label: '生效中', value: 1 }, 
  { label: '未生效', value: 2 },
  { label: '已过期', value: 3 }
]

export const getBgStatus = (start, end) => {
  let now = moment().unix()
  // 3种会出现0的情况
  if (start === 0 && end === 0) { 
    return 1
  }
  if (start === 0) {
    return now < end ? 1 : 3
  }
  if (end === 0) { 
    return now < start ? 2 : 1
  }
  // 剩余的都是有边界的情况
  if (now < start) {
    return 2
  }
  if (now > end) {
    return 3
  }
  return 1
}

export const bgStatusFormater = (start, end) => {
  let status = getBgStatus(start, end)
  let name = '?'
  bgStatusOptions.forEach(item => {
    if (item.value === status) {
      name = item.label
    }
  })
  return name
}

// ============= 背景类型 ====================

// 背景类型 5-活动背景 4-基础背景
export const bgTypeOptions = [
  { label: '活动背景', value: 5 },
  { label: '基础背景', value: 4 } 
]

export const bgTypeOptionsV2 = [
  { label: '全部', value: -1 }, 
  ...bgTypeOptions
]

export const bgTypeFormaterJY = (v) => {
  if (!v) {
    return ''
  }
  let name = `${v}`
  bgTypeOptions.forEach(item => {
    if (item.value === v) {
      name = item.label
    }
  })
  return name
}

export const bgTypeFormaterVR = (v) => {
  let code = getBgTypeCode(v)
  if (code === 0) {
    return v
  }
  return bgTypeFormaterJY(code) 
}

export const getBgTypeCode = (v) => {
  if (v === 'ACTIVITY') {
    return 5
  }
  if (v === 'BASE') {
    return 4
  }
  return 0
}

export const releaseStatusFormaterVR = (v) => {
  if (v === 'WAITING') {
    return '待发放'
  }
  if (v === 'CANCEL') {
    return '取消发放'
  }
  if (v === 'DONE') {
    return '已发放'
  }
}

// ============= Ohter ====================

export const appIdOptions = [
  { label: '交友', value: 2 },
  { label: '语音房', value: 34 }
]

export const parseBgIDOptionsJY = (list) => {
  let res = [{ value: null, label: '未选择' }]
  if (!list) {
    return res
  }
  list.map(item => {
    res.push({ value: item.id, label: `${item.id}_${item.name}` })
  })
  return res
}

export const parseBgIDOptionsVR = (list) => {
  let res = [{ value: null, label: '未选择' }]
  if (!list) {
    return res
  }
  list.map(item => {
    res.push({ value: item.bgId, label: `${item.bgId}_${item.bgName}` })
  })
  return res
}

export const getSelectBG = (list, id) => {
  return list?.find(item => item.id === id || item.bgId === id)
}
 
export const parseRoomList = (str) => {
  if (!str) {
    return []
  }
  let list = String(str).replace(',', '\n').split('\n')
  let allPass = true
  list.forEach(item => {
    if (!/^\d+:\d+$/.test(item)) {
      allPass = false
      message.warn(`发现不满足格式的范围: ${item}`)
    }
  })
  if (!allPass) {
    return false
  }
  return list
}

export const parseRoomListV2 = (str) => {
  if (!str) {
    return []
  }
  let list = String(str).replace(',', '\n').split('\n')
  let result = []
  let allPass = true
  list.forEach(item => {
    if (!/^\d+$/.test(item)) {
      allPass = false
      message.warn(`发现不满足格式的范围: ${item}`)
      return
    }
    result.push(Number(item))
  })
  if (!allPass) {
    return false
  }
  return result
}

export const defaultBgJY = {
  id: 0,
  name: '',
  bgType: 4,
  tag: '',
  url: 'https://zhuiya.bs2cdn.yy.com/pctbi/8snmpwetryrdpasixpnannxexfzkwdpz.png',
  appUrl: 'https://zhuiya.bs2cdn.yy.com/6550efa3812a4e3eb308082bdfe27c52.jpg',
  sideUrl: 'https://res.yy.com/fts/client/bgnew/202410301/0/bg.png',
  color: '#865232',
  rankColor: '#865232',
  giftTopColor: '#643928F2', 
  giftBottomColor: 'linear-gradient(90deg, rgba(255, 163, 97, 0.4) 0%, rgba(211, 111, 29, 0.4) 92.01%)',
  giftButtonColor: 'linear-gradient(110.01deg, #FF9E6D 0%, #F28831 100%)'
}

export const defaultBgVR = {
  bgId: -1,
  bgName: '',
  fixedBgType: 4,
  bgTag: '',
  pcPluginImageUrl: 'https://zhuiya.bs2cdn.yy.com/adminweb/hxjtspspdpw5h7jmnpgydyfjtym82xyq.jpg',
  pcTemplateImageUrl: 'https://zhuiya.bs2cdn.yy.com/pctbi/fcg2nettixmjw7dgkpbytbjdefen4rtz.jpg',
  firstSeatImageUrl: 'https://zhuiya.bs2cdn.yy.com/adminweb/crmprhhrthpj7nxqzscdk7dwnhktb8sn.png',
  appImageUrl: 'https://zhuiya.bs2cdn.yy.com/adminweb/ahxjwhfxnhprjyb4wywqhfjgd4ygh7y8.jpg'
}

export const defaultRewardJY = {
  appId: 2, 
  isAll: false,
  backgroundId: null,
  roomList: '',
  startTime: moment(),
  endTime: null
}

export const defaultRewardVR = {
  appId: 34,
  isAll: false,
  backgroundId: null,
  roomList: '',
  startTime: moment(),
  endTime: null,
  selectBG: {}
}
