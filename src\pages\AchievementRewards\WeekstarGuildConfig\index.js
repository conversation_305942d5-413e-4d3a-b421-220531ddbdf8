import React, { Component } from 'react'
import dateString from '@/utils/dateString'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import PicturesWall from '@/components/PicturesWall'
import { Button, Table, Card, Form, Divider, DatePicker, Input, Modal, Row, Col } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const FormItem = Form.Item
const namespace = 'weekGuildRewardConfig'
const getListUri = `${namespace}/getList`
const updateItemUri = `${namespace}/updateItem`
// 2022-01-17, 因产品要求, 调整页面中出现的关键字"奖励"
@connect(({ weekGuildRewardConfig }) => ({
  model: weekGuildRewardConfig
}))
class WeekGuildRewardConfig extends Component {
  // 定义列表结构，
  columns = [
    { title: '开始时间', dataIndex: 'startDate', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD') },
    { title: '结束时间', dataIndex: 'endDate', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD') },
    { title: '周星成就', dataIndex: 'achievementLevel', align: 'center', render: (text, record) => (text + '级') },
    // { title: '奖励1', dataIndex: 'giftName1', align: 'center', render: (text, record) => this.renderReward(record.giftName1, record.giftCount1, record.giftUnit1) },
    // { title: '奖励2', dataIndex: 'giftName2', align: 'center', render: (text, record) => this.renderReward(record.giftName2, record.giftCount2, record.giftUnit2) },
    // { title: '奖励3', dataIndex: 'giftName3', align: 'center', render: (text, record) => this.renderReward(record.giftName3, record.giftCount3, record.giftUnit3) },
    { title: '1', dataIndex: 'giftName1', align: 'center', render: (text, record) => this.renderReward(record.giftName1, record.giftCount1, record.giftUnit1) },
    { title: '2', dataIndex: 'giftName2', align: 'center', render: (text, record) => this.renderReward(record.giftName2, record.giftCount2, record.giftUnit2) },
    { title: '3', dataIndex: 'giftName3', align: 'center', render: (text, record) => this.renderReward(record.giftName3, record.giftCount3, record.giftUnit3) },
    { title: '操作时间', dataIndex: 'operateTime', align: 'center', render: text => dateString(text) },
    { title: '操作人', dataIndex: 'operator', align: 'center' },
    { title: '操作',
      align: 'center',
      render: (text, record) => this.renderOpColumn(record.startDate, record.endDate, record) }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  pagination = { pageSizeOptions: ['20', '50', '100'], showSizeChanger: true, pageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, startValue: null, endValue: null, searchResult: [], searchDone: false }

  // 获取列表
  componentDidMount () {
    const { dispatch, model: { list } } = this.props
    dispatch({
      type: getListUri
    })

    this.setState({ list })
  }

  renderReward = (giftName, giftCount, giftUnit) => {
    if (giftName === '' || giftCount <= 0) {
      return '-'
    }

    if (giftUnit === '') {
      return giftName + giftCount
    }

    return giftName + giftCount + giftUnit
  }

  renderOpColumn = (start, end, record) => {
    let now = moment().unix()
    let nextWeek = now + 7 * 24 * 60 * 60
    if ((now >= start && now < end) || (nextWeek >= start && nextWeek < end)) {
      return (<span>
        {/* <a onClick={this.showModal(true, record)}>编辑奖励</a> */}
        <a onClick={this.showModal(true, record)}>编辑</a>
      </span>)
    }

    return (<span>
      <a onClick={this.showModal(false, record)}>查看详情</a>
    </span>)
  }

  // show modal
  showModal = (isUpdate, record) => () => {
    if (record == null) record = {}

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? '更新' : '查看详情' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // update
  onFinish = values => {
    const { dispatch } = this.props
    const { isUpdate } = this.state

    if (!isUpdate) {
      this.setState({ visible: false })
      return
    }

    values.giftCount1 = parseInt(values.giftCount1)
    values.giftCount2 = parseInt(values.giftCount2)
    values.giftCount3 = parseInt(values.giftCount3)
    dispatch({
      type: updateItemUri,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  onSearch = () => {
    const { model: { list } } = this.props
    const { startValue, endValue } = this.state

    var dataSource = list
    const start = startValue.unix()
    const end = endValue.unix()
    if (start > 0 && end > 0) {
      dataSource = dataSource.filter(data => (data.startDate <= start && data.endDate >= start) || (data.startDate <= end && data.endDate >= end))
    }

    this.setState({ searchResult: dataSource, searchDone: true })
  }

  render () {
    const { route, model: { list } } = this.props
    const { startValue, endValue, searchResult, searchDone, visible, title, isUpdate } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 12 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <div>
            时间范围
            <DatePicker
              format='YYYY-MM-DD'
              value={startValue}
              placeholder='开始时间'
              onChange={this.onStartChange}
              style={{ marginLeft: 10 }}
            />
            <span style={{ marginLeft: 10 }}>~</span>
            <DatePicker
              format='YYYY-MM-DD'
              value={endValue}
              placeholder='结束时间'
              onChange={this.onEndChange}
              style={{ marginLeft: 10 }}
            />
            <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onSearch}>查询</Button>
          </div>
          <Divider />
          <Form>
            <Table dataSource={searchDone ? searchResult : list} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <Row gutter={24}>
              <Col span={12}>
                <FormItem label='名称1' name='giftNam1'>
                  <Input disabled={!isUpdate} />
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label='数量1' name='giftCount1'>
                  <Input disabled={!isUpdate} />
                </FormItem>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <FormItem label='单位1' name='giftUnit1'>
                  <Input disabled={!isUpdate} />
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label='说明1' name='giftHover1'>
                  <Input disabled={!isUpdate} />
                </FormItem>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <FormItem label='图片1' name='giftImage1'>
                  <PicturesWall />
                </FormItem>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <FormItem label='名称2' name='giftName2'>
                  <Input disabled={!isUpdate} />
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label='数量2' name='giftCount2'>
                  <Input disabled={!isUpdate} />
                </FormItem>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <FormItem label='单位2' name='giftUnit2'>
                  <Input disabled={!isUpdate} />
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label='说明2' name='giftHover2'>
                  <Input disabled={!isUpdate} />
                </FormItem>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <FormItem label='图片2' name='giftImage2'>
                  <PicturesWall />
                </FormItem>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <FormItem label='名称3' name='giftName3'>
                  <Input disabled={!isUpdate} />
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label='数量3' name='giftCount3'>
                  <Input disabled={!isUpdate} />
                </FormItem>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <FormItem label='单位3' name='giftUnit3'>
                  <Input disabled={!isUpdate} />
                </FormItem>
              </Col>
              <Col span={12}>
                <FormItem label='说明3' name='giftHover3'>
                  <Input disabled={!isUpdate} />
                </FormItem>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={12}>
                <FormItem label='图片3' name='giftImage3'>
                  <PicturesWall />
                </FormItem>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={6}>
                <FormItem name='seqId'>
                  <Input hidden />
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem name='startDate'>
                  <Input hidden />
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem name='endDate'>
                  <Input hidden />
                </FormItem>
              </Col>
              <Col span={6}>
                <FormItem name='achievementLevel'>
                  <Input hidden />
                </FormItem>
              </Col>
            </Row>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default WeekGuildRewardConfig
