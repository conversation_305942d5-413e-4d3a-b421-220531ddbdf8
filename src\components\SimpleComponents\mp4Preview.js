import React, { Component } from 'react'
import { Popover, Typography, Row, Col, Modal } from 'antd'

const { Text } = Typography

/* mp4素材预览
url: mp4资源url
emptyTip: 无值时显示内容
*/
export default class Mp4Preview extends Component {
    state = {
      url: '', // mp4资源url
      previewData: '', // 预览图数据
      width: '3em',
      emptyTip: '(空)', // 无值时显示内容
      showModal: false
    }
  
    componentDidMount = () => {
      const { url, emptyTip, width } = this.props
      this.setState({ 
        url: url,
        emptyTip: emptyTip || '(空)',
        width: width || '3em'
      })
      this.refreshComponent(this.props)
    }
  
    componentDidUpdate = (prevProps) => {
      const { url } = this.props
      if (url !== prevProps.url) { // url发生变化时刷新
        this.refreshComponent(this.props)
      }
    }
  
    // 刷新组件
    refreshComponent = (props) => {
      const { url } = props
      if (!url) {
        return
      }
      this.setState({ url: url })
    }
  
    render () {
      const { url, emptyTip, width, showModal } = this.state
  
      const popContent = (
        <Row>
          <Col span={24}>
            <Text type='secondary'>URL：{url || emptyTip}</Text>
            {/* <Button type='primary' style={{ marginLeft: '1em' }}>预览</Button> */}
          </Col>
        </Row>
      )
  
      return (
        !url
          ? <Text code>{emptyTip}</Text>
          : <>
            <Popover content={popContent} mouseLeaveDelay={0.2} title={null} trigger='hover'>
              <span >
                <img src='https://makefriends.bs2dl.yy.com/makefriends_50071797_1678170807505' onClick={() => { this.setState({ showModal: true }) }} style={{ width: width }} />
              </span>
            </Popover>  
            <Modal title={null} footer={null} visible={showModal} onCancel={() => { this.setState({ showModal: false }) }} width='80vw' height='80vh' >
              <div>
                <video id='myVideo' controls style={{ maxWidth: '100%', maxHeight: '100%' }}>
                  <source src={url} type='video/mp4' />
                </video>
              </div>
            </Modal>
          </>
      )
    }
}
