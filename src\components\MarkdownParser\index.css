
.markdownBox {
  height: 100%;
  margin: 0;
  font: 300 16px 'Cousine';
  color: #444;
  line-height: 1.2em;
  padding: 10px;
  background-color: #ffffff;
}

.markdownBox p {
  margin-top: 0;
  margin-bottom: 16px;
}

.markdownBox div {
  margin: 0;
  padding: 0;
}

.markdownBox h1,h2,h3,h4,h5 {
  position: relative;
  margin-top: 1em;
  margin-bottom: 16px;
  font-weight: bold;
}

.markdownBox h1 {
  font-size: 2em;
  margin: 0 0 .444em;
}

.markdownBox h2 {
  font-size: 1.5em;
  margin: 0 0 .666em;
}

.markdownBox h3 {
  font-size: 1.125em;
  margin: 0 0 .888em;
}

.markdownBox b {
  font-weight: 700;
}

.markdownBox pre {
  padding: 1em;
  border: none;
  overflow: auto;
  line-height: 1.45;
  max-height: 35em;
  position: relative;
  background: #F6F6F6;
  border-radius: 4px;
  word-break: break-all;
  word-wrap: break-word;
}

.markdownBox pre > p {
  line-height: 1.1em;
}

.markdownBox code {
  border: 1px solid #ddd;
  background: #f6f6f6;
  padding: 3px;
  border-radius: 3px;
  font-size: 14px;
}

.markdownBox blockquote {
  color: #2C3E50;
  border-left: 4px solid #D6DBDF;
  font-size: 14px;
  background: none repeat scroll 0 0 rgba(102,128,153,.05);
  margin: 8px 0;
  padding: 8px 16px;
}

.markdownBox strong {
  font-weight: bold;
}

.markdownBox hr {
  border-top: 1px solid rgba(0,0,0,.06);
  color: rgba(0,0,0,.85);
  list-style: none;
  background: none;
  margin: 15px 0;
  overflow: hidden;
  clear: both;
  width: 100%;
  min-width: 100%;
}
