import { getLists } from './api'
import { Modal } from 'antd'

export default {
  namespace: 'AfkRecentAlert',
  state: {
    updating: false,
    displayData: [],
    total: 0,
    begTime: 0,
    endTime: 0,
    currentPage: 1
  },

  reducers: {
    // 更新单个state成员的值
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },
    displayList (state, { payload, total, currentPage, pageSize }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        displayData: payload,
        total: total,
        currentPage: currentPage,
        pageSize: pageSize
      }
    }
  },

  effects: {
    // 请求并刷新列表数据
    * getAfkRecentAlert ({ payload }, { select, call, put }) {
      if (!payload.page) {
        payload.page = 1
      }
      if (!payload.size) {
        payload.size = 20
      }
      let callback = payload.callback
      if (callback) {
        delete payload.callback
      }
      let resp = yield call(getLists, payload)

      const { data } = resp
      if (data === undefined) {
        Modal.error({ content: '获取数据失败，请检查控制台' })
        console.error('getAfkRecentAlert() get data error: response=', resp)
        return
      }
      if (data.status !== 0) {
        Modal.error({ content: '获取数据有误，请检查控制台' })
        console.error('getAfkRecentAlert() status=' + data.status + ' msg=' + data.msg)
        return
      }
      if (callback) {
        callback(data.data.list, data.data.total, payload.page, payload.size)
        return
      }
      yield put({
        type: 'displayList',
        payload: data.data.list,
        total: data.data.total,
        currentPage: payload.page,
        pageSize: payload.size
      })
    }
  }
}
