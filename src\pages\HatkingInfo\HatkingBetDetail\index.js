import React, { Component } from 'react'
import dateString from '@/utils/dateString'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Button, Table, Card, Form, Divider, DatePicker, Input, message, Space, BackTop } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const namespace = 'hatkingBetDetail'
const getListUri = `${namespace}/getList`

// const dateFormat = 'YYYY-MM-DD HH:mm'
// const { RangePicker } = DatePicker

const gameMap = { 0: 'ALL', 1: '武器大师', 2: '热血屠龙', 3: '星球漫步', 4: '明星衣橱', 5: '森林探险' }

// 热血屠龙、武器大师
const armoryOptions = [
  { position: 1, value: '激光棒' },
  { position: 2, value: '冲锋枪' },
  { position: 3, value: '锤子' },
  { position: 4, value: '大刀' },
  { position: 5, value: '电锯' },
  { position: 6, value: '左轮' },
  { position: 7, value: '加特林' },
  { position: 8, value: '扳手' },
  { position: 9, value: '大炮' }
]

// 星球漫步
const planetOptions = [
  { position: 1, value: '土星' },
  { position: 2, value: '金星' },
  { position: 3, value: '木星' },
  { position: 4, value: '水星' },
  { position: 5, value: '火星' },
  { position: 6, value: '天王星' },
  { position: 7, value: '海王星' },
  { position: 8, value: '冥王星' },
  { position: 9, value: '地球' }
]

// 明星衣橱
const photoOptions = [
  { position: 1, value: '火龙' },
  { position: 2, value: '白虎' },
  { position: 3, value: '雪狼' },
  { position: 4, value: '麋鹿' },
  { position: 5, value: '麒麟' },
  { position: 6, value: '朱雀' },
  { position: 7, value: '飞猫' },
  { position: 8, value: '独角兽' },
  { position: 9, value: '雄狮' }
]

// 森林探险
const islandOptions = [
  { position: 1, value: '迷宫' },
  { position: 2, value: '丘陵' },
  { position: 3, value: '巨石' },
  { position: 4, value: '古树' },
  { position: 5, value: '古堡' },
  { position: 6, value: '花海' },
  { position: 7, value: '幽谷' },
  { position: 8, value: '湖泊' },
  { position: 9, value: '洞穴' }
]

@connect(({ hatkingBetDetail }) => ({
  model: hatkingBetDetail
}))
class HatkingBetDetail extends Component {
  // 定义列表结构，
  columns = [
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '玩法类型', dataIndex: 'arenaId', align: 'center', render: (text, record, index) => { return gameMap[text] } },
    { title: '参与模式', dataIndex: 'mode', align: 'center', render: (text, record) => (text === 1 ? '单个(X8)' : text === 2 ? '相邻2个(X4)' : '一列3个(X2)') },
    { title: '参与位置', dataIndex: 'betPosition', align: 'center', render: (text, record) => this.renderArmory(text, record) },
    { title: '投入道具', dataIndex: 'propsId', align: 'center' },
    { title: '投入数量(个)', dataIndex: 'count', align: 'center' },
    { title: '参与花费(紫水晶)', dataIndex: 'amethyst', align: 'center' },
    { title: '参与时间', dataIndex: 'betTime', align: 'center', render: text => dateString(text) },
    { title: '参与轮次', dataIndex: 'roundStart', align: 'center', render: text => dateString(text) },
    { title: '获胜位置', dataIndex: 'winPosition', align: 'center', render: (text, record) => this.renderArmory(text, record) },
    { title: '是否获胜', dataIndex: 'isWinner', align: 'center', render: (text, record) => (text === 0 ? '否' : '是') },
    { title: '奖励道具发放', dataIndex: 'hasIssued', align: 'center', render: (text, record) => (text === 0 ? '否' : '是') }
  ]

  renderArmory (armory, record) {
    let positionOpts = []
    switch (record.arenaId) {
      case 1:
      case 2:
        positionOpts = armoryOptions
        break
      case 3:
        positionOpts = planetOptions
        break
      case 4:
        positionOpts = photoOptions
        break
      case 5:
        positionOpts = islandOptions
        break
    }
    let names = []
    const arms = armory.split(',')
    console.log(arms, armory)
    if (Array.isArray(arms) && arms.length) {
      arms.forEach((position) => {
        for (let i = 0; i < positionOpts.length; i++) {
          console.log(position, positionOpts[i].position.toString())
          if (positionOpts[i].position.toString() === position) {
            names.push(positionOpts[i].value)
            break
          }
        }
      })
    }
    return names.join('|') + '(' + armory + ')'
  }

  pagination = { pageSizeOptions: ['20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, startValue: null, endValue: null, uid: 0 }

  // 获取列表
  componentDidMount () {
    // const { dispatch, model: { list } } = this.props
    // dispatch({
    //   type: getListUri
    // })

    // this.setState({ list })
  }

  disabledStartDate = (startValue) => {
    const endValue = this.state.endValue
    if (!startValue || !endValue) {
      return false
    }
    return startValue.valueOf() > endValue.valueOf()
  }

  disabledEndDate = (endValue) => {
    const startValue = this.state.startValue
    if (!endValue || !startValue) {
      return false
    }
    return endValue.valueOf() <= startValue.valueOf()
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  onClick = () => {
    const { dispatch } = this.props
    const { startValue, endValue, uid } = this.state
    if (uid <= 0) {
      message.error('uid不能为空且大于0')
      return
    }
    var data = { startDate: moment(startValue).format('YYYY-MM-DD HH:mm:00'), endDate: moment(endValue).format('YYYY-MM-DD HH:mm:00'), uid: uid }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  render () {
    const { route, model: { list } } = this.props
    const { startValue, endValue } = this.state
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Space align='center'>
            <div>
              UID
              <Input onChange={e => this.setState({ uid: e.target.value })} style={{ marginLeft: 10, width: 150 }} /> {/* 搜索按钮 */}
              <span style={{ marginLeft: 10 }}>时间范围</span>
              <DatePicker
                disabledDate={this.disabledStartDate}
                showTime={{ format: 'HH:mm' }}
                format='YYYY-MM-DD HH:mm'
                value={startValue}
                placeholder='开始时间'
                onChange={this.onStartChange}
                style={{ marginLeft: 10 }}
              />
              <span style={{ marginLeft: 10 }}>~</span>
              <DatePicker
                disabledDate={this.disabledEndDate}
                showTime={{ format: 'HH:mm' }}
                format='YYYY-MM-DD HH:mm'
                value={endValue}
                placeholder='结束时间'
                onChange={this.onEndChange}
                style={{ marginLeft: 10 }}
              />
              <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
            </div>
            <div>
              <div><font color='red'>1. 神兵勇士，参与位置对应 1：激光棒 2：冲锋枪 3：锤子 4：大刀 5：电锯 6：左轮 7：加特林 8：扳手 9：大炮</font></div>
              <div><font color='red'>2. 明星衣橱，参与位置对应 1：火龙 2：白虎 3：雪狼 4：麋鹿 5：麒麟 6：朱雀 7：飞猫 8：独角兽 9：雄狮</font></div>
              <div><font color='red'>3. 森林探险，参与位置对应 1：迷宫 2：丘陵 3：巨石 4：古树 5：古堡 6：花海 7：幽谷 8：湖泊 9：洞穴</font></div>
            </div>
          </Space>
          <Divider />
          <Form>
            <BackTop />
            <Table dataSource={list} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
          </Form>
        </Card>
      </PageHeaderWrapper>
    )
  }
}
export default HatkingBetDetail
