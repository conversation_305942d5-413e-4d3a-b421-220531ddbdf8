import { connect } from 'dva'
import React, { Component } from 'react'
import { Button, Col, Divider, Form, Input, message, Popconfirm, Row, Table } from 'antd'
import { formatTimestamp } from '../common'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import Modal from 'antd/es/modal/Modal'

const namespace = 'BusinessManage'
const defaultPageSize = 10

@connect(({ BusinessManage }) => ({
  model: BusinessManage
}))

class BusinessManage extends Component { // 默认页面组件，不需要修改
  constructor (props) {
    super(props)
    this.initState()
    this.searchHandle()
  }

  initState = () => {
    this.state = {
      pagination: {
        pageSize: defaultPageSize,
        total: 0,
        current: 1,
        defaultCurrent: 1,
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        onChange: (page, pageSize) => {
          this.pageChange(page, pageSize)
        },
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      }
    }
  }

  updatePagination = (page, pageSize, total) => {
    const { pagination } = this.state
    pagination.current = page || 1
    pagination.pageSize = pageSize || defaultPageSize
    if (total !== undefined) {
      pagination.total = total
    }
    this.setState({ pagination: pagination })
    return pagination
  }

  // 分页信息变更
  pageChange = (page, pageSize, total) => {
    let pagination = this.updatePagination(page, pageSize, total)
    this.searchHandle(pagination.current, pagination.pageSize)
  }

  // 获取当前查询条件
  getQueryCondition = (page, size, cond) => {
    const { pagination } = this.state
    page = page || pagination.current || 1
    size = size || pagination.pageSize || defaultPageSize
    let pageInfo = {
      pageNo: page,
      pageSize: size
    }
    if (!cond) {
      return pageInfo
    }

    return Object.assign(cond, pageInfo)
  }

  // 处理查询事件
  searchHandle = (page, size, cond) => {
    const { dispatch } = this.props

    let query = this.getQueryCondition(page, size, cond)
    if (!query) {
      return
    }
    let self = this
    dispatch({
      type: `${namespace}/pageListBusiness`,
      payload: query,
      callback: (data) => {
        self.updatePagination(query.pageNo || 1, query.pageSize || defaultPageSize, data ? data.total : 0)
      }
    })
  }

  onQueryClick = (values) => {
    const { pagination } = this.state
    let size = pagination.pageSize || defaultPageSize
    this.searchHandle(1, size, values)
  }

  // 规则函数表头
  getRuleDataColumns = () => {
    return [
      { title: '序号', dataIndex: 'index', align: 'left' },
      { title: '业务ID', dataIndex: 'id', align: 'left' },
      { title: '业务名称', dataIndex: 'name', align: 'left' },
      { title: '更新时间', dataIndex: 'updateTime', align: 'left', render: (v) => formatTimestamp(v) },
      { title: '操作人', dataIndex: 'lastOperator', align: 'left' },
      {
        title: '操作',
        key: 'operation',
        align: 'left',
        render:
          (text, item) => (
            <span>
              <a size='small' type='primary' onClick={() => this.showEditModal(item, 'edit')}>编辑</a>
              <Divider type='vertical' /> {/* 分割线 */}
              <Popconfirm title='确认删除?' onConfirm={() => this.onDelete(item)}><a style={{ color: 'red' }}>删除</a></Popconfirm>
            </span>)
      }
    ]
  }

  showEditModal = (editItem, editMode) => {
    if (editMode === 'add') {
      if (this.formRefEdit) {
        this.formRefEdit.resetFields()
      }
    }
    editItem.managersText = ''
    if (editItem.managers && editItem.managers.length > 0) {
      editItem.managersText = editItem.managers.join('\n')
    }
    if (this.formRefEdit) {
      this.formRefEdit.setFieldsValue(editItem)
    }
    this.setState({ editModalVisible: true, editItem: editItem, editMode: editMode })
  }

  closeEditModal = () => {
    if (this.formRefEdit) {
      this.formRefEdit.resetFields()
    }
    this.setState({ editModalVisible: false })
  }

  onEditModalOk = () => {
    this.formRefEdit.submit()
  }

  onEditFormSubmit = (values) => {
    const { editItem, editMode } = this.state
    let submitItem = editItem
    if (editMode === 'edit' && editItem.configType === 'INNER') { // 内置函数，只允许修改名称、介绍
      editItem.name = values.name
    } else {
      submitItem = Object.assign(editItem, values)
    }

    if (editItem.managersText) {
      editItem.managers = editItem.managersText.split(/[\s\n\r,，；]+/)
    }

    console.log('待提交的数据: ', submitItem, values)
    const reqType = editMode === 'add' ? `${namespace}/addBusiness` : `${namespace}/updateBusiness`
    const { dispatch } = this.props
    let self = this
    dispatch({
      type: reqType,
      payload: submitItem,
      callback: (rsp) => {
        if (rsp && rsp.status === 0) {
          self.setState({ editModalVisible: false, editItem: undefined })
          if (editMode === 'add') {
            self.searchHandle()
          }
        } else {
          message.error(editMode + ' 失败: ' + rsp.msg)
        }
      }
    })
  }

  onDelete = (item) => {
    console.log('准备删除：', item)
    let self = this
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/removeBusiness`,
      payload: { id: item.id },
      callback: (rsp) => {
        if (rsp && rsp.status === 0) {
          self.searchHandle()
        } else {
          message.error('删除失败: ' + rsp.msg)
        }
      }
    })
  }

  // 编辑规则数据对话框
  renderEditModal = () => {
    const { editModalVisible, editItem, editMode } = this.state
    return (
      <Modal width={500} title={editMode === 'add' || !editItem ? '添加' : '编辑 【' + editItem.id + '】'} visible={editModalVisible} onCancel={this.closeEditModal} onOk={this.onEditModalOk}>
        <Form labelCol={{ span: 6 }} ref={(form) => {
          if (!this.formRefEdit) {
            form.setFieldsValue(editItem)
          }
          this.formRefEdit = form
        }} onFinish={this.onEditFormSubmit}>
          <Form.Item name='id' label={'业务ID'} required rules={[{ required: true, min: 1, max: 256, message: '请输入' }]}>
            <Input disabled={editMode === 'edit'} placeholder={'输入业务ID'} />
          </Form.Item>

          <Form.Item name='name' label={'业务名称'} required rules={[{ required: true, min: 1, max: 256, message: '请输入' }]}>
            <Input placeholder={'请输入'} />
          </Form.Item>

          <Form.Item name='managersText' label={'管理员通行证'} tooltip={{ placement: 'leftTop', title: '格式：多个管理员用 换行分隔' }}>
            <Input.TextArea rows={3} placeholder='输入管理员通行证' />
          </Form.Item>
        </Form>
      </Modal>
    )
  }

  // 渲染函数
  render () {
    const { route, model: { dataList } } = this.props
    const { pagination } = this.state
    const columns = this.getRuleDataColumns()

    return (
      <>
        <PageHeaderWrapper title={route.name}>
          <Row style={{ marginBottom: '1em' }}>
            <Form layout={'inline'} ref={form => {
              this.formRefQuery = form
            }} onFinish={this.onQueryClick}>
              <Form.Item name={'name'} label={'关键字'}>
                <Input placeholder='name' style={{ width: 150 }} allowClear />
              </Form.Item>

              <Button type='primary' htmlType='submit'>查询</Button>
              <Divider type={'vertical'} />
              <Button type='primary' onClick={() => {
                this.formRefQuery.resetFields()
                this.formRefQuery.submit()
              }}>重置</Button>
              <Divider type={'vertical'} />
              <Button type={'primary'} onClick={() => this.showEditModal({}, 'add')}>添加</Button>
            </Form>
          </Row>
          <Row style={{ marginBottom: '1em' }}>
            <Col span={24}>
              <Table columns={columns}
                dataSource={dataList}
                size='small'
                pagination={pagination}
                showSorterTooltip={false}
                rowKey={record => record.id}
              />
            </Col>
          </Row>

          {/* 渲染编辑对话框 */}
          {this.renderEditModal()}
        </PageHeaderWrapper>
      </>
    )
  }
}

export default BusinessManage
