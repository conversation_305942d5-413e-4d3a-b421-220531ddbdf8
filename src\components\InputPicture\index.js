/*
  通用组件——表单图片输入器
  参数:
  value: string, 图标url
  sizeLimit: number, 限制文件大小_mb
  inputType: string, 输入类型 [string, array]
  fileServer: string, 目标文件服务器 [jy_default, zhuiwan_baiduBOS]
  onChange： function, 图片更新回调 (value)=>{...}
  width: number, 预览图片宽度_px
  height: number, 预览图片高度_px
  onlyUpload: bool, 是否禁止手动输入, 默认允许
  allowHeight: number, 限制的高度
  allowWidth: number, 限制的宽度
*/

import React, { Component } from 'react'
import { Tooltip, Image as AntImage, Typography, Modal, Tabs, Input, message, Upload, Button, Popover, Row, Col } from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import uploadFileToBaiduOBS, { uploadFileToVRBG } from '@/utils/zhuiwanBaiduBos'
import { uploadFileToJYProServer } from '@/utils/request'

const defaultImgSize = 100 // 默认展示宽度
const defaultSizeLimit = 2 // 默认上传大小限制
const defaultFileServer = 'jy_default'
const defaultInputType = 'string'
const { Text } = Typography
const { TabPane } = Tabs

class InputPicture extends Component {
  state = {
    imgUrl: '',
    tmpEditingURL: '',
    singleEditerVisible: false,
    batchEditerVisible: false,
    width: defaultImgSize,
    height: defaultImgSize,
    sizeLimit: 1, // 单位-Mb
    fileServer: '',
    inputType: '',
    allowType: [],
    notMatchTip: ''
  }

  componentDidMount = () => {
    const { value, width, height, sizeLimit, fileServer, inputType, onlyUpload, allowHeight, allowWidth, allowType, notMatchTip } = this.props
    // console.debug('InputPicture==>', value)
    this.setState({
      imgUrl: value || '',
      width: width || null,
      height: height || null,
      fileServer: fileServer || defaultFileServer,
      inputType: inputType || defaultInputType,
      sizeLimit: sizeLimit || defaultSizeLimit,
      onlyUpload: onlyUpload || false,
      allowHeight: allowHeight || 0,
      allowWidth: allowWidth || 0,
      allowType: allowType || ['image/jpeg', 'image/png'],
      notMatchTip: notMatchTip || '仅支持jpg或png格式～'
    })
  }

  // 上传图片前的检查
  beforeUpload = (file) => {
    console.info('file=', file)
    const { sizeLimit, allowType, allowHeight, allowWidth, notMatchTip } = this.state

    const isMatchType = allowType.indexOf(file.type) >= 0 // file.type === 'image/jpeg' || file.type === 'image/png'
    if (!isMatchType) {
      message.error(notMatchTip)
      return false
    }

    const overLoad = file.size / 1024 / 1024 > sizeLimit
    if (overLoad) {
      message.error(`图片大小超出限制: 必须小于${sizeLimit}mb`)
      return false
    }

    if (allowHeight > 0 || allowWidth > 0) {
      return this.checkFileSize(file, allowWidth, allowHeight)
    }

    return true
  }

  // 检查图片宽度高度
  checkFileSize = (file, allowWidth, allowHeight) => {
    return new Promise((resolve, reject) => {
      // eslint-disable-next-line no-undef
      let reader = new FileReader()
      reader.onload = (theFile) => {
        // eslint-disable-next-line no-undef
        let image = new Image()
        image.onload = () => {
          if (image.width !== allowWidth || image.height !== allowHeight) {
            let tip = `图片宽高不合规范, 必须为: ${allowWidth}x${allowHeight} (当前为${image.width}x${image.height})`
            message.error(tip)
            reject(new Error(tip))
          } else {
            resolve()
          }
        }
        image.onerror = () => {
          reject(new Error('无法加载图片'))
        }
        image.src = theFile.target.result
      }
      reader.readAsDataURL(file)
    })
  }

  // 图片修改回调
  onImageValueChange = (newValue) => {
    const { onChange } = this.props
    if (typeof onChange === 'function') {
      onChange(newValue)
      this.setState({ imgUrl: newValue })
    } else {
      message.warn('使用方法错误,请检查代码')
    }
  }

  // 上传文件回调_1
  uploadCallbackOnString = (isOk, newUrl) => {
    if (isOk) {
      message.success('上传成功～')
      this.onImageValueChange(newUrl)
    } else {
      message.warn('上传失败,请稍后再试')
    }
    this.setState({ singleEditerVisible: false })
  }

  // 上传文件回调_2
  uploadCallbackOnArray = (isOk, newUrl) => {
    const { imgUrl } = this.state
    let imgList = [...imgUrl]
    imgList.push(newUrl)

    if (isOk) {
      message.success('上传成功～')
      this.onImageValueChange(imgList)
    } else {
      message.warn('上传失败,请稍后再试')
    }
    this.setState({ batchEditerVisible: false })
  }

  // 单个输入模式
  SingleInput = (props) => {
    const { width, height, singleEditerVisible, fileServer, onlyUpload } = props
    const { imgUrl } = this.state

    const popContent = (
      <Row>
        <Col span={24}>
          <Text type='secondary'>图片URL：{imgUrl || '(空)'}</Text>
          <Button size='small' type='primary' style={{ marginLeft: '1em' }} onClick={() => { this.setState({ singleEditerVisible: true }) }}>点我编辑</Button>
        </Col>
      </Row>
    )

    return (
      <div>
        <Popover content={popContent} mouseLeaveDelay={0.2} title={null} trigger='hover'>
          {
            imgUrl === ''
              ? <Text code>(空)</Text>
              : <AntImage width={width} height={height} src={imgUrl} />
          }
        </Popover>
        <Modal visible={singleEditerVisible} title='编辑url或上传' footer={null} onCancel={() => this.setState({ singleEditerVisible: false })} style={{ padding: '10px' }}>
          <Tabs size='small' defaultActiveKey={onlyUpload ? '2' : '1'}>
            <TabPane tab='编辑url' key='1' disabled={onlyUpload}>
              <Input value={imgUrl} onChange={(e) => { this.setState({ imgUrl: e.target.value }) }} />
              <Button style={{ margin: '0.5em' }} onClick={() => { this.onImageValueChange(imgUrl) }}>保存</Button>
            </TabPane>
            <TabPane tab='上传图片' key='2'>
              <div style={{ marginBottom: '20px' }}>
                <Upload
                  showUploadList={false}
                  beforeUpload={this.beforeUpload}
                  customRequest={(r) => { 
                    switch (fileServer) {
                      case 'zhuiwan_baiduBOS':
                        uploadFileToBaiduOBS(r, this.uploadCallbackOnString)
                        break
                      case 'jybossroombg':
                        uploadFileToVRBG(r, this.uploadCallbackOnString)
                        break
                      default:
                        uploadFileToJYProServer(r, this.uploadCallbackOnString)
                    }
                  }} >
                  <Button>上传</Button>
                </Upload>
              </div>
              <Tooltip title={imgUrl}>
                <AntImage width={width} height={height} src={imgUrl} />
              </Tooltip>
            </TabPane>
          </Tabs>
        </Modal>
      </div>
    )
  }

  // 批量输入模式
  BatchInput = (props) => {
    const { imgUrl: imgList, tmpEditingURL, width, height, batchEditerVisible, fileServer, onlyUpload } = props
    if (!imgList) {
      message.warn(`参数错误，请检查代码！InputPicture value=${imgList}`)
      return ''
    }
    const popContent = (url, index) => (
      <Row>
        <Col span={24}>
          <Text type='secondary'>图片URL：{url} </Text>
          <Button size='small' danger type='dashed' style={{ marginLeft: '1em' }}
            onClick={() => { this.onImageValueChange(removeItemByIndex(imgList, index)) }}>删除</Button>
        </Col>
      </Row>
    )
    const picStyle = { marginLeft: '10px', marginBottom: '10px' }

    return (
      <div>
        <Row>
          {
            imgList.map((imgUrl, index) => {
              return <Col style={picStyle} key={imgUrl}>
                <Popover mouseLeaveDelay={0.5} content={() => popContent(imgUrl, index)} title={null} trigger='hover'>
                  <AntImage width={width} height={height} src={imgUrl} />
                </Popover>
              </Col>
            })
          }
          <Col style={picStyle}>
            <Button type='dashed' onClick={() => { this.setState({ batchEditerVisible: true }) }} >
              <Text type='secondary'><PlusOutlined /></Text>
            </Button>
          </Col>
        </Row>
        <Modal visible={batchEditerVisible} title='手动输入url或上传' footer={null} onCancel={() => this.setState({ batchEditerVisible: false })} style={{ padding: '10px' }}>
          <Tabs size='small' defaultActiveKey={onlyUpload ? '2' : '1'}>
            <TabPane tab='手动填写' key='1' disabled={onlyUpload}>
              <Row>
                <Col span={16}><Input value={tmpEditingURL} onChange={(e) => { this.setState({ tmpEditingURL: e.target.value }) }} /></Col>
                <Col span={4} push={1}>
                  <Button onClick={() => {
                    if (!tmpEditingURL) {
                      message.warn('禁止非空值～')
                      return
                    }
                    const { imgUrl } = this.state
                    let imgList = [...imgUrl]
                    imgList.push(tmpEditingURL)
                    this.onImageValueChange(imgList)
                    this.setState({ batchEditerVisible: false })
                  }}>确认添加</Button>
                </Col>
              </Row>
            </TabPane>
            <TabPane tab='上传图片' key='2'>
              <Upload
                multiple
                showUploadList={false}
                beforeUpload={this.beforeUpload}
                customRequest={(r) => {
                  switch (fileServer) {
                    case 'zhuiwan_baiduBOS':
                      uploadFileToBaiduOBS(r, this.uploadCallbackOnArray)
                      break
                    case 'jybossroombg':
                      uploadFileToVRBG(r, this.uploadCallbackOnArray)
                      break
                    default:
                      uploadFileToJYProServer(r, this.uploadCallbackOnArray)
                  }
                }} >
                <Button>上传</Button>
              </Upload><br />
            </TabPane>
          </Tabs>
        </Modal>
      </div>
    )
  }

  render () {
    const { inputType } = this.state
    switch (inputType) {
      case 'array':
        return <this.BatchInput {...this.state} />
      case 'string':
        return <this.SingleInput {...this.state} />
      default:
        return '????'
    }
  }
}

export default InputPicture

// 删除数据元素
const removeItemByIndex = (before, index) => {
  let after = []
  before.map((item, i) => {
    if (i !== index) {
      after.push(item)
    }
  })
  return after
}
