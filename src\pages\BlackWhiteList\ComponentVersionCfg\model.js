/* @Author: <PERSON><PERSON><PERSON><PERSON>  @LastModified: 2021-08-14 17:08:52  */

import { genGetListTemplate, genUpdateTemplate, simpleRequire2 } from '@/utils/common'

// 获取所有组件列表
const getAllSimpleComponent = genGetListTemplate('/fts_component/component/get_all_simple', 'simpleComponentList', (raw) => {
  return raw.sort((a, b) => { return a.key > b.key ? 1 : -1 })
})

// 获取所有业务信息
const getAllComponentTypeList = genGetListTemplate('/fts_component/type/get_all_cfg', 'componentTypeList', (raw) => {
  return raw.sort((a, b) => { return a.key > b.key ? 1 : -1 })
})

// 根据筛选条件查询白名单列表
const selectWhiteListByCondition = genGetListTemplate('/fts_component/whitelist/select_whitelist', 'versionWhiteList')

// 根据筛选条件查询组件列表
const selectComponentCfgList = genGetListTemplate('/fts_component/component/select_list', 'componentCfgList')

// 查询数据更新历史
const getOpHistory = genGetListTemplate('/fts_component/util/get_op_history', 'ophistory')

// 更新或新增版本灰度白名单
const addOrUpdateWhiteListCfg = genUpdateTemplate('/fts_component/whitelist/add_update')

// 批量删除白名单
const batchDelteWhiteList = genUpdateTemplate('/fts_component/whitelist/batch_del')

// 更新或新增组件
const addOrUpdateComponent = genUpdateTemplate('/fts_component/component/upsert')

// 更新或新增业务类型
const addOrUpdateComponentType = genUpdateTemplate('/fts_component/type/upsert')

export default {
  namespace: 'componentVersionCfg',
  state: {
    simpleTypeList: [],
    simpleComponentList: [],
    versionWhiteList: [],
    componentCfgList: [],
    componentTypeList: [],
    verisonList: [],
    ophistory: []
  },
  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getAllSimpleComponent,
    getAllComponentTypeList,
    selectWhiteListByCondition,
    addOrUpdateWhiteListCfg,
    batchDelteWhiteList,
    selectComponentCfgList,
    addOrUpdateComponent,
    addOrUpdateComponentType,
    getOpHistory,
    * getVersionListByVersionUrl ({ payload }, { select, call, put }) { // 根据版本url获取版本列表
      const { params, cbFunc } = payload
      if (!params || params.url === '') { // 如果url为空，将组件列表清空
        yield put({
          type: 'updateState',
          payload: { name: 'verisonList', newValue: [] }
        })
        cbFunc('')
        return
      }
      let resp = yield call(simpleRequire2, '/fts_component/util/get_version_list', params, false)
      if (!resp || resp === 1) {
        resp = []
      }
      if (resp.length > 0) {
        resp = resp.reverse()
        cbFunc(resp[0])
      }
      yield put({
        type: 'updateState',
        payload: { name: 'verisonList', newValue: resp }
      })
    },
    * clearVersionList ({ payload }, { select, call, put }) {
      yield put({
        type: 'updateState',
        payload: { name: 'verisonList', newValue: [] }
      })
    }
  }
}
