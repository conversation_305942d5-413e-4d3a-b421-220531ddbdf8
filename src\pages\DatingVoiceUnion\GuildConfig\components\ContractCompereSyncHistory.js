import { connect } from 'dva'
import React, { Component } from 'react'
import { But<PERSON>, Col, DatePicker, Divider, Form, InputNumber, Row, Select, Table } from 'antd'
import { formatTimestamp } from '../../common'
import { formatOptions } from '@/utils/common'

const namespace = 'GuildConfigManage'
const defaultPageSize = 10

@connect(({ ContractCompereSyncHistory }) => ({
  model: ContractCompereSyncHistory
}))

class ContractCompereSyncHistory extends Component { // 默认页面组件，不需要修改
  constructor (props) {
    super(props)
    this.initState(props.sid)
    this.searchHandle(1, defaultPageSize, { sid: props.sid })
  }

  initState = (sid) => {
    this.state = {
      sid: sid,
      compereSyncList: [],
      syncStatusOptions: [],
      triggerTypeOptions: [],
      pagination: {
        pageSize: defaultPageSize,
        total: 0,
        current: 1,
        defaultCurrent: 1,
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        onChange: (page, pageSize) => {
          this.pageChange(page, pageSize)
        },
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      }
    }
  }

  updatePagination = (page, pageSize, total, dataList) => {
    const { pagination } = this.state
    pagination.current = page || 1
    pagination.pageSize = pageSize || defaultPageSize
    if (total !== undefined) {
      pagination.total = total
    }
    let update = { pagination: pagination }
    if (dataList) {
      update.compereSyncList = dataList
    }
    this.setState(update)
    return pagination
  }

  // 分页信息变更
  pageChange = (page, pageSize, total) => {
    let pagination = this.updatePagination(page, pageSize, total)
    this.searchHandle(pagination.current, pagination.pageSize)
  }

  // 获取当前查询条件
  getQueryCondition = (page, size, cond) => {
    const { pagination } = this.state
    page = page || pagination.current || 1
    size = size || pagination.pageSize || defaultPageSize
    let pageInfo = {
      pageNo: page,
      pageSize: size
    }
    if (!cond) {
      return pageInfo
    }

    let query = Object.assign(cond, pageInfo)
    if (query.startTime) {
      query.begSyncTime = query.startTime.unix()
      delete query.startTime
    }
    if (query.endTime) {
      query.endSyncTime = query.endTime.unix()
      delete query.endTime
    }
    return query
  }

  // 处理查询事件
  searchHandle = (page, size, cond) => {
    const { dispatch } = this.props

    let query = this.getQueryCondition(page, size, cond)
    if (!query) {
      return
    }
    let self = this
    dispatch({
      type: `${namespace}/pageContractCompereSyncList`,
      payload: query,
      callback: (data) => {
        self.updatePagination(query.pageNo || 1, query.pageSize || defaultPageSize, data ? data.total : 0, (data.list || []))
        self.setState({ syncStatusOptions: data.syncStatusOptions, triggerTypeOptions: data.triggerTypeOptions })
      }
    })
  }

  onQueryClick = (values) => {
    const { pagination } = this.state
    let size = pagination.pageSize || defaultPageSize
    this.searchHandle(1, size, values)
  }

  formatContract =(contract) => {
    if (!contract || contract.signTime === 0) {
      return '未签约'
    }
    let signTime = formatTimestamp(contract.signTime / 1000)
    let finishedTime = formatTimestamp(contract.finishedTime / 1000)
    return <div><span style={{ color: 'red' }}>签约时间:</span>[{signTime} ~ {finishedTime}}] <span style={{ color: 'red' }}><br />分成比例:</span>{contract.weight} %</div>
  }

  getShowColumns = () => {
    const { syncStatusOptions, triggerTypeOptions } = this.state
    return [
      { title: 'TraceID', dataIndex: 'traceID', align: 'left' },
      { title: 'UID', dataIndex: 'uid', align: 'left' },
      { title: 'SID', dataIndex: 'sid', align: 'left' },
      { title: '家族ID', dataIndex: 'familyID', align: 'left' },
      { title: '触发类型', dataIndex: 'triggerType', align: 'left', render: (v) => formatOptions(v, triggerTypeOptions) },
      { title: '操作人', dataIndex: 'operator', align: 'left' },
      { title: '同步时间', dataIndex: 'syncTime', align: 'left', render: (v) => formatTimestamp(v) },
      { title: '状态', dataIndex: 'status', align: 'left', render: (v) => formatOptions(v, syncStatusOptions) },
      { title: '备注', dataIndex: 'remark', align: 'left' },
      {
        title: '同步时交友签约信息',
        width: 360,
        dataIndex: 'datingContract',
        align: 'left',
        render: this.formatContract
      },
      { title: '同步前语音房签约信息',
        width: 360,
        dataIndex: 'yyfContract',
        align: 'left',
        render: this.formatContract
      }
    ]
  }

  // 渲染函数
  render () {
    const { pagination, compereSyncList, syncStatusOptions, sid } = this.state
    const columns = this.getShowColumns()

    return (
      <>
        <Row style={{ marginBottom: '1em' }}>
          <Form layout={'inline'} ref={form => {
            if (!this.formRefQuery) {
              this.formRefQuery = form
            }
          }} onFinish={this.onQueryClick}>
            <Form.Item name={'sid'} label={'sid'} initialValue={sid}>
              <InputNumber placeholder='输入频道SID' style={{ width: 150 }} allowClear />
            </Form.Item>
            <Form.Item name={'uid'} label={'UID'}>
              <InputNumber placeholder='输入主持UID' style={{ width: 150 }} allowClear />
            </Form.Item>
            <Form.Item name={'syncStatus'} label={'状态'}>
              <Select style={{ width: 200 }} allowClear options={syncStatusOptions || []} />
            </Form.Item>
            <Form.Item label='同步开始时间' name='startTime'>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </Form.Item>
            <Form.Item label='同步结束时间' name='endTime'>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </Form.Item>

            <Button type='primary' htmlType='submit'>查询</Button>
            <Divider type={'vertical'} />
            <Button type='primary' onClick={() => {
              this.formRefQuery.resetFields()
              this.formRefQuery.submit()
            }}>重置</Button>
          </Form>
        </Row>
        <Row style={{ marginBottom: '1em' }}>
          <Col span={24}>
            <Table columns={columns}
              dataSource={compereSyncList || []}
              size='small'
              pagination={pagination}
              showSorterTooltip={false}
              rowKey={record => record.id}
            />
          </Col>
        </Row>
      </>
    )
  }
}

export default ContractCompereSyncHistory
