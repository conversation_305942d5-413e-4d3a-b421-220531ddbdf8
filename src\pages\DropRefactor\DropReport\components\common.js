import { SimpleCollapse } from '@/components/SimpleComponents'
import React from 'react'

const useTipText = `1.普通/疯狂 收入：用户参与【普通/疯狂】抽取道具投入的金额合计
2.普通/疯狂 支出：用户参与【普通/疯狂】抽取道具获得的礼物价值合计。计算：道具池奖励支出+进度奖励支出，2022.9.20及以前的数据还计入了道具池物资支出
3.普通/疯狂 人数：参与【普通/疯狂】抽取道具的用户数去重
4.收入：用户参与【普通&疯狂】抽取道具投入的金额合计，即空投流水。计算：普通收入+疯狂收入
5.支出：用户参与【普通&疯狂】抽取道具获得的礼物价值合计。计算：道具池奖励支出+进度奖励支出
6.抽取道具人数：参与空投玩法的用户数去重
7.抽取道具偏移：系统在【普通&疯狂】道具池的累计失败情况（大于0为累计成功状态）。计算：收入-支出
8.龙宫支出：龙宫发放道具礼物价值合计，不含龙宫物资支出
9.龙宫开启次数：龙宫开启次数统计（龙宫达进度100%的用户领奖次数）
10.补给箱支出：补给箱发放道具礼物价值合计，不含补给箱物资支出
11.发放物资支出：给白名单主持的发放道具紫金币合计。计算：物资流水发放金额*白名单主持的物资分配比例
12.总偏移：空投道具池的总累计失败情况（大于0为累计成功状态）。计算：收入-支出-龙宫支出-发放补给箱支出-发放物资支出`

export const GenTip = ({ isShow }) => {
  if (!isShow) { // TODO: 临时隐藏使用提示,后面可能要还原
    return ''
  }
  return (
    <SimpleCollapse title='点击展开使用提示' value={useTipText} />
  )
}

export const numberFormater = (v) => {
  return v || '0'
}

export const totalOutFormater = (r) => {
  const { totalOut, frags } = r
  return numberFormater(totalOut + frags)
}

// 礼物支出 or 总支出
export const prizeOutFormater = (r, includeFrags) => {
  const { normalOut, crazyOut, extraOut, dragonOut, compensateOut, frags } = r
  let result = 0
  result += normalOut || 0
  result += crazyOut || 0
  result += extraOut || 0
  result += dragonOut || 0
  result += compensateOut || 0
  if (includeFrags) {
    result += frags || 0
  }
  return result
}

// 礼物支出/模拟收入
export const outRateFormaterV2 = (r) => {
  const { totalIn } = r
  if (totalIn === 0) {
    return '0%'
  }
  return `${(prizeOutFormater(r, false) * 100.0 / totalIn).toFixed(1)}%`
}

// 总支出/模拟收入
export const outRateFormater = (r) => {
  const { totalIn } = r
  if (totalIn === 0) {
    return '0%'
  }
  return `${(prizeOutFormater(r, true) * 100.0 / totalIn).toFixed(1)}%`
}

// 总偏移
export const totalOffsetFormater = (r) => {
  const { totalOffset, frags } = r
  return numberFormater(totalOffset - frags)
}

/**
 * 占比格式化
 * @param {*} a 被除数
 * @param {*} b 除数
 * @param {*} n 保留多少位小数
 */
export const rataFormater = (a, b, n = 0) => {
  if (b === 0 || b === undefined) {
    return '0%'
  }
  return Number(a * 100.0 / b).toFixed(n) + '%'
}
