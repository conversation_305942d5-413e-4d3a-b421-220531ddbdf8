import React, { Component } from 'react'
import { Card, Divider, But<PERSON>, Modal, Form, Table, Popconfirm, message, InputNumber } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import { connect } from 'dva'

const namespace = 'robMoney' // model 的 namespace
const FormItem = Form.Item

@connect(({ rob<PERSON><PERSON> }) => ({ // model 的 namespace
  model: robMoney // model 的 namespace
}))
class MoneyCfgComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false
    }
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getMoneyCfgList`
    })
  }

  // 需要修改
  columns = [
    { title: 'ID', dataIndex: 'id', align: 'center' },
    { title: '目标(紫水晶)', dataIndex: 'value', align: 'center' },
    { title: '奖励(紫水晶)', dataIndex: 'reward', align: 'center' },
    { title: '操作',
      align: 'center',
      render: (text, record) => (
        <div>
          <a><DeleteOutlined style={{ marginRight: 10 }} onClick={this.showModal(true, record)} /></a>
          <Popconfirm onConfirm={this.handleRemove(record.id)} title='确认删除？'><a><DeleteOutlined style={{ color: 'red' }} type='delete' /></a></Popconfirm>
        </div>
      )
    }
  ]

  defaultValue = {}

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // 隐藏弹窗，不需要修改
  hideModal = () => {
    this.setState({ visible: false })
  }

  onFinish = values => {
    const { dispatch, model: { tagList } } = this.props
    const { isUpdate } = this.state
    var list = tagList
    if (!isUpdate) {
      for (var i = 0; Array.isArray(list) && i < list.length; i++) {
        if (list[i].tagId === values.tagId) {
          message.error('period exist', 5)
          return
        }
      }
    }

    // console.log(values, list)
    dispatch({
      type: `${namespace}/upsetMoneyCfgList`,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 更新与删除
  handleSubmit = () => {
    this.formRef.submit()
  }

  handleRemove = id => () => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/removeMoneyCfgList`,
      payload: { id: id }
    })
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { moneyList } } = this.props
    const { visible, title, isUpdate } = this.state
    const formItemLayout = { // 不需要修改
      labelCol: {
        xs: { span: 6 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 18 },
        sm: { span: 18 }
      }
    }

    return (
      <Card>
        <Button onClick={this.showModal(false, this.defaultValue)}>添加</Button>
        <Divider />
        <Table rowKey={(record, index) => index} dataSource={moneyList} columns={this.columns} size='small' pagination={false} /> {/* 显示的列表 */}

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} ref={form => { this.formRef = form }} {...formItemLayout}>
            <FormItem label='ID' name='id' rules={[{ required: true }]}>
              <InputNumber min={1} readOnly={isUpdate} style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='目标(紫水晶)' name='value' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='奖励(紫水晶)' name='reward' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </FormItem>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default MoneyCfgComponent
