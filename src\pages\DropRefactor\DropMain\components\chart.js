import React, { Component } from 'react'
import { connect } from 'dva'
import { Row, Col, Form, Select, Button, Divider, DatePicker } from 'antd'
import moment from 'moment'

var echarts = require('echarts')

const selOptions = [
  { label: '最近3小时', value: 3600 * 3 },
  { label: '最近6小时', value: 3600 * 6 },
  { label: '最近12小时', value: 3600 * 12 },
  { label: '最近24小时', value: 3600 * 24 },
  { label: '最近48小时', value: 3600 * 48 },
  { label: '最近72小时', value: 3600 * 72 },
  { label: '最近7天', value: 86400 * 7 },
  { label: '最近15天', value: 86400 * 15 },
  { label: '最近30天', value: 86400 * 30 },
  { label: '最近60天', value: 86400 * 60 },
  { label: '最近90天', value: 86400 * 90 },
  { label: '最近半年', value: 86400 * 183 },
  { label: '最近一年', value: 86400 * 366 }
]

const namespace = 'dropMain'

@connect(({ dropMain }) => ({
  model: dropMain
}))

class DropQueryChart extends Component {
  onChange = step => {
    let end = moment().unix()
    let begin = end - step
    this.formRef.setFieldsValue({ range: [moment.unix(begin), moment.unix(end)] })
    this.formRef.submit()
  }
  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  onFinish = values => {
    let params = {}
    params.begin = values.range[0].unix()
    params.end = values.range[1].unix()
    params.pid = values.pid
    this.queryChart(params)
  }

  // 渲染表格
  componentDidMount () {
    const { defaultPID } = this.props
    if (this.formRef) {
      let end = moment().unix()
      let begin = end - 3600 * 3
      this.formRef.setFieldsValue({ pid: defaultPID, range: [moment.unix(begin), moment.unix(end)] })
    }

    let end = moment().unix()
    let begin = end - 3600 * 3
    let params = { begin, end, pid: defaultPID }
    this.queryChart(params)
  }

  // 查询数据
  queryChart = (params, useTip = false) => {
    this.callModel('queryChart', {
      params
    })
  }

  componentDidUpdate () {
    const { model: { chartValue } } = this.props
    let dataSetCount = chartValue.dsCount
    let dataSetValue = chartValue.dsValue
    let series = []
    let legend = Array.isArray(chartValue.legend) ? chartValue.legend : []

    for (let i = 0; i < legend.length; i++) {
      series.push({ type: 'line', name: legend[i], smooth: true, seriesLayoutBy: 'row', emphasis: { focus: 'series' } })
    }

    let option = {
      title: { x: 'center', y: 'bottom', text: '价值趋势图/紫水晶' },
      legend: {},
      tooltip: {
        trigger: 'axis',
        showContent: true,
        axisPointer: {
          type: 'cross' // 显示十字交叉指示器
        },
        position: function (pos, params, el, elReact, size) {
          let obj = { top: 10 }
          obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 30
          return obj
        }
      },
      dataset: {
        source: dataSetValue
      },
      xAxis: { type: 'category' },
      yAxis: { gridIndex: 0 },
      series: series
    }

    this.renderCharts(option, 'chart_value')

    option.dataset.source = dataSetCount
    option.title.text = '数量趋势图/个'
    this.renderCharts(option, 'chart_count')
  }

  renderCharts (optioin, idName) {
    var chart = echarts.getInstanceByDom(document.getElementById(idName))
    if (chart !== undefined) {
      chart.clear() // 先清除掉旧的画板，防止冲突
    }

    if (chart === undefined) {
      chart = echarts.init(document.getElementById(idName))
    }

    chart.setOption(optioin)
  }

  render () {
    const { poolList } = this.props

    return (
      <div>
        <Form onFinish={this.onFinish} ref={form => { this.formRef = form }}>
          <Row gutter={4}>
            <Col>
              <Form.Item name='pid' label='渠道' >
                <Select style={{ width: '12em' }} options={poolList} />
              </Form.Item>
            </Col>
            <Col>
              <Form.Item name='range' label='时间区间'>
                <DatePicker.RangePicker showTime format='YYYY-MM-DD HH:mm' />
              </Form.Item>
            </Col>
            <Col>
              <Form.Item>
                <Select placeholder='快速选择' onChange={this.onChange} style={{ width: 120 }} options={selOptions} />
              </Form.Item>
            </Col>
            <Col>
              <Form.Item>
                <Button htmlType='submit' type='primary'>确定</Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Divider style={{ marginTop: '0px' }} />
        <Row gutter={2}>
          <Col span={12}>
            <div id='chart_value' style={{ width: '100%', height: 400 }} />
          </Col>
          <Col span={12}>
            <div id='chart_count' style={{ width: '100%', height: 400 }} />
          </Col>
        </Row>

      </div>
    )
  }
}

export default DropQueryChart
