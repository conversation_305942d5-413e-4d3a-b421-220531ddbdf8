import { addGuildConfig, pageListGuildConfig, removeGuildConfig, updateGuildConfig, pageContractCompereList, syncContract, pageContractCompereSyncList, pageDatingYyfGuildChangeHistoryList, getAllowSyncUIDList, updateAllowSyncUIDList } from './api'

export default {
  namespace: 'GuildConfigManage',
  state: {
    dataList: [],
    compereList: [],
    compereSyncList: [],
    guildChangeHistoryList: [],
    allowSyncUIDList: []
  },

  reducers: {
    // 更新数据列表，usage:
    // yield put({
    //  type: 'updateList',
    //  payload: {
    //    name: 'configList', list: Array.isArray(data) ? data : []
    //  }
    // })
    updateList (state, { payload }) {
      let obj = { ...state }
      obj[payload.name] = (payload.list || []).map((i, index) => {
        i.index = index + 1
        return i
      })
      return obj
    }
  },

  // 数据变更
  effects: {
    * pageListGuildConfig ({ payload, callback }, { call, put }) {
      const { data } = yield call(pageListGuildConfig, payload)
      yield put({
        type: 'updateList',
        payload: {
          name: 'dataList',
          list: data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
        }
      })

      if (callback) {
        callback(data.data)
      }
    },

    * updateGuildConfig ({ payload, callback }, { call, put }) {
      const { data } = yield call(updateGuildConfig, payload)
      if (callback) {
        callback(data)
      }
    },

    * addGuildConfig ({ payload, callback }, { call, put }) {
      const { data } = yield call(addGuildConfig, payload)
      if (callback) {
        callback(data)
      }
    },

    * removeGuildConfig ({ payload, callback }, { call, put }) {
      const { data } = yield call(removeGuildConfig, payload)
      if (callback) {
        callback(data)
      }
    },

    * pageContractCompereList ({ payload, callback }, { call, put }) {
      const { data } = yield call(pageContractCompereList, payload)
      yield put({
        type: 'updateList',
        payload: {
          name: 'compereList',
          list: data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
        }
      })

      if (callback) {
        callback(data.data)
      }
    },
    * syncContract ({ payload, callback }, { call, put }) {
      const { data } = yield call(syncContract, payload)
      if (callback) {
        callback(data)
      }
    },
    * pageDatingYyfGuildChangeHistoryList ({ payload, callback }, { call, put }) {
      const { data } = yield call(pageDatingYyfGuildChangeHistoryList, payload)
      yield put({
        type: 'updateList',
        payload: {
          name: 'guildChangeHistoryList',
          list: data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
        }
      })

      if (callback) {
        callback(data.data)
      }
    },
    * pageContractCompereSyncList ({ payload, callback }, { call, put }) {
      const { data } = yield call(pageContractCompereSyncList, payload)
      yield put({
        type: 'updateList',
        payload: {
          name: 'compereSyncList',
          list: data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
        }
      })

      if (callback) {
        callback(data.data)
      }
    },

    * updateAllowSyncUIDList ({ payload, callback }, { call, put }) {
      const { data } = yield call(updateAllowSyncUIDList, payload)
      if (callback) {
        callback(data)
      }
    },

    * getAllowSyncUIDList ({ payload, callback }, { call, put }) {
      const { data } = yield call(getAllowSyncUIDList, payload)
      yield put({
        type: 'updateList',
        payload: {
          name: 'allowSyncUIDList',
          list: data && data.data && data.data.list && Array.isArray(data.data.list) ? data.data.list : []
        }
      })

      if (callback) {
        callback(data.data)
      }
    }
  }
}
