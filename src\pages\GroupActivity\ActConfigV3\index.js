import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Row, Col, Card, Button, Modal, Form, Input, message, Space, DatePicker, InputNumber, Select, Typography, Divider, Tooltip, Popconfirm } from 'antd'
import { timeFormater, deepClone } from '@/utils/common'
import { ImagesList } from '@/components/SimpleComponents'
import moment from 'moment'

const { Text } = Typography
const namespace = 'groupActivityv3'
const frameID = ENV_TAG === 'prod' ? 355 : 430 // 头像框propsId
const showKnightID = ENV_TAG === 'prod' ? 10010 : 10054 // 幻影骑士propsId
const showCarId = 124 // 兰博基尼入场秀propsId

const defaultActConfig = {
  actId: moment().format('YYYYMMDDHHmmss'),
  leaderUid: 0,
  teamUserLimit: 1,
  rewardThreshold: 1,
  targetAmount: 100,
  rewardType: 100,
  rewardsRebate: 1000,
  timeRange: [moment().startOf('days'), moment().endOf('days')],
  note: '新配置',
  memberRewardList: [
    { rewardType: 0, name: '无', propsId: 0, rewardDays: 0, rewardId: 0, icon: '' },
    { rewardType: 0, name: '无', propsId: 0, rewardDays: 0, rewardId: 0, icon: '' }
  ]
}

@connect(({ groupActivityv3 }) => ({
  model: groupActivityv3
}))

class GroupActivityV3 extends Component {
  state = {
    showModal: false,
    opType: 'update', // update / insert / delete
    selectItem: {},
    searchActID: '',
    searchUID: null,
    searchTime: [moment().add(-7, 'days').startOf('days'), moment().endOf('days')]
  }

  componentDidMount = () => {
    this.refreshList()
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // 刷新配置列表
  refreshList = () => {
    const { searchActID, searchUID, searchTime } = this.state
    // console.debug('refreshList===>', searchActID, searchUID, searchTime)
    let start = 0
    let end = 0
    if (searchTime && searchTime[0] && searchTime[1]) {
      start = searchTime[0].startOf('days').unix()
      end = searchTime[1].endOf('days').unix()
    }
    this.callModel('getActivityConfigList', {
      params: {
        actId: searchActID,
        uid: searchUID || 0,
        start: start,
        end: end
      }
    })
  }

  // 参数检验
  checkConfig = (opType, v, rawList) => {
    if (v.teamUserLimit <= 0) {
      return '队伍成员数量上限填写有误'
    }
    if (opType === 'update' || opType === 'insert') {
      // 相同队长UID不允许参与时间重复
      for (let i = 0; i < rawList.length; i++) {
        const item = rawList[i]
        if (item.leaderUid !== v.leaderUid) {
          continue
        }
        if (item.actId === v.actId) {
          continue
        }
        if (!(v.endTime < item.startTime || v.startTime >= item.endTime)) {
          return `活动时段重叠: id=${item.actId}`
        }
      }
      // 不能设置已过去的时间
      if (v.endTime <= moment().unix()) {
        return '不能设置已结束的活动'
      }
    }

    if (opType === 'insert') {
      for (let i = 0; i < rawList.length; i++) {
        const item = rawList[i]
        if (item.actId === v.actId) {
          return '活动ID已存在'
        }
      }
    }
    return ''
  }

  fixConfig = (opType, v) => {
    if (v.timeRange && v.timeRange[0]) {
      v.startTime = v.timeRange[0].unix()
      v.endTime = v.timeRange[1].unix()
    }
    delete v['timeRange']
    if (opType === 'insert') {
      v.actId = moment().format('YYYYMMDDHHmmss')
    }

    // 补充队长奖励相关信息
    rewardTypeOptions.forEach(item => {
      if (item.value === v.rewardType) {
        v.rewardName = item.label
        v.rewardId = item.ext.rewardId
        v.rewardIcon = item.ext.icon
      }
    })

    let opDesc = (opType === 'udpate' ? '更新' : opType === 'delete' ? '删除' : '新增')

    const aprInfoMap = {
      actId: v.actId,
      opDesc: '这是概述',
      leader: `${v.leaderUid}`,
      timeRange: `${timeFormater(v.startTime, 1)}~${timeFormater(v.endTime, 1)}`,
      teamUserLimit: `${v.teamUserLimit}`,
      rewardThreshold: `${v.rewardThreshold}元`,
      targetAmount: `${v.targetAmount}元`,
      rewardType: `${v.rewardName}`,
      rewardsRebate: `${Number(v.rewardsRebate / 10)}%`,
      memberRewardList: `${MemberRewardListFormater(v.memberRewardList)}`,
      note: `${v.note}`
    }
    v.aprInfoMap = aprInfoMap

    // 补充审批相关字段
    v.aprText = `操作描述：${opDesc}配置
    活动ID: ${v.actId}
    队长UID: ${aprInfoMap.leader}
    活动时间: ${aprInfoMap.timeRange}
    队伍成员数量上限: ${v.teamUserLimit}
    兑换门槛: ${v.rewardThreshold}元
    活动期任务指标: ${v.targetAmount}元
    队长奖励货币: ${v.rewardName}
    队长奖勤返点: ${aprInfoMap.rewardsRebate}
    队员奖勤: ${aprInfoMap.memberRewardList}
    备注: ${v.note}
    `
    return v
  }

  // 提交 更改 或 新增 操作
  submitUpdate = (opType, v, configList) => {
    let funcName = 'insertActivityConfig'
    let okTip = '送审成功，请联系审批人审批～'
    if (opType === 'update') {
      funcName = 'udpateActivityConfig'
    }
    if (opType === 'delete') {
      funcName = 'deleteActivityConfig'
      okTip = '删除成功'
    }
    v = this.fixConfig(opType, v)
    const reason = this.checkConfig(opType, v, configList)
    if (reason) {
      message.warn(reason)
      return
    }
    this.callModel(funcName, {
      params: v,
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('操作失败: ' + msg)
          return
        }
        message.success(okTip)
        this.setState({ showModal: false })
        this.refreshList()
      }
    })
  }

  // 刷新队长昵称
  refreshUserNick = (uid) => {
    this.callModel('queryNick', {
      params: { uid: uid }
    })
  }

  showAddModal = () => {
    this.setState({ showModal: true, opType: 'insert' })
    this.changeState('userNick', '')
    if (this.formRef) {
      this.formRef.resetFields()
    }
  }

  showUpdateModal = (v) => {
    v.timeRange = [moment(v.startTime * 1000), moment(v.endTime * 1000)]
    if (this.formRef) {
      this.formRef.setFieldsValue(v)
    }
    this.changeState('userNick', v.leaderNick)
    this.setState({ showModal: true, opType: 'update' })
  }

  columns = [
    { title: '活动ID', dataIndex: 'actId' },
    { title: '队长UID', dataIndex: 'leaderUid' },
    { title: '队长YY号', dataIndex: 'leaderImid' },
    { title: '队长昵称', dataIndex: 'leaderNick' },
    { title: '队伍人数上限', dataIndex: 'teamUserLimit' },
    { title: '活动时间', dataIndex: 'startTime', render: (v, r) => { return TimeRangeFormater(r.startTime, r.endTime) } },
    { title: '兑奖门槛/元', dataIndex: 'rewardThreshold' },
    { title: '活动期指标/元', dataIndex: 'targetAmount' },
    { title: '奖励货币', dataIndex: 'rewardName', render: (v, r) => { return RewardNameFormater(r) } }, // rewardType对应的类型
    { title: '队长奖勤返点', dataIndex: 'rewardsRebate', render: (v) => { return `${Number(v / 10)}%` } },
    { title: '队员奖勤', dataIndex: 'memberRewardList', render: (v) => { return MemberRewardListFormater(v) } },
    { title: '操作人', dataIndex: 'opUid', render: (v, r) => { return <Tooltip title={`${r.opNick}, ${timeFormater(r.opTime)}`}>{v}</Tooltip> } },
    { title: '操作',
      dataIndex: 'actId',
      render: (v, r) => {
        return <div>
          <Button type='link' disabled={moment().unix() > r.endTime} onClick={() => { this.showUpdateModal(r) }}>更新</Button>
          <Text type='secondary'>|</Text>
          <Popconfirm title='确认删除这个活动么？' onConfirm={() => { this.submitUpdate('delete', r, []) }}>
            <Button type='link' danger>删除</Button>
          </Popconfirm>
        </div>
      } }
  ].map(item => {
    item.align = 'center'
    return item
  })

  render () {
    const { route } = this.props
    const { configList, userNick } = this.props.model || { userNick: '' }
    const { showModal, opType } = this.state
    const { searchActID, searchUID, searchTime } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Row>
            <Col span={24} style={{ marginBottom: '1em' }}>
              <Space>
                <Text>活动ID:</Text>
                <Input value={searchActID} style={{ width: '8em' }} onChange={(e) => { this.setState({ searchActID: e.target.value }) }} />
                <Divider type='vertical' />

                <Text>UID:</Text>
                <InputNumber value={searchUID} style={{ width: '8em' }} onChange={(v) => { this.setState({ searchUID: v }) }} />
                <Divider type='vertical' />

                <Text>参与时间范围:</Text>
                <DatePicker.RangePicker value={searchTime} onChange={v => { this.setState({ searchTime: v }) }} />
                <Divider type='vertical' />
                <Button type='primary' onClick={() => this.refreshList()}>查询</Button>
                <Button onClick={() => this.showAddModal()} >新增活动</Button>
              </Space>
            </Col>

            <Col span={24}>
              <Table columns={this.columns} dataSource={configList} />
            </Col>
          </Row>
        </Card>

        <Modal forceRender title={opType === 'insert' ? '新增活动配置' : '更新活动配置'} visible={showModal} okText='提交'
          onCancel={() => this.setState({ showModal: false, selectItem: null })}
          onOk={() => { this.formRef.submit() }}
        >

          <Form ref={r => { this.formRef = r }} labelCol={{ span: 7 }} initialValues={defaultActConfig} onFinish={v => this.submitUpdate(opType, v, configList)}>
            <Form.Item label='活动ID' name='actId'>
              <Input disabled={opType !== 'insert'} />
            </Form.Item>
            <Form.Item label='队长uid' name='leaderUid'>
              <InputNumber style={{ width: '12em' }} onChange={(v) => { this.refreshUserNick(v) }} disabled={opType !== 'insert'} />
            </Form.Item>
            <Form.Item label='队长昵称'>
              <Text type='success'>{userNick}</Text>
            </Form.Item>
            <Form.Item label='队长昵称' name='leaderNick' hidden>
              <Input type='success' />
            </Form.Item>
            <Form.Item label='活动时间' name='timeRange'>
              <DatePicker.RangePicker showTime format='YYYY-MM-DD HH:mm:ss' style={{ width: '26em' }} />
            </Form.Item>
            <Form.Item label='队伍成员数量上限' name='teamUserLimit'>
              <InputNumber disabled={opType !== 'insert'} style={{ width: '8em' }} />
            </Form.Item>
            <Form.Item label='兑换门槛/元' name='rewardThreshold'>
              <InputNumber disabled={opType !== 'insert'} style={{ width: '8em' }} />
            </Form.Item>
            <Form.Item label='活动期任务指标/元' name='targetAmount'>
              <InputNumber disabled={opType !== 'insert'} style={{ width: '8em' }} />
            </Form.Item>
            <Form.Item label='队长奖励货币' name='rewardType'>
              <Select disabled={opType !== 'insert'} options={rewardTypeOptions} style={{ width: '8em' }} />
            </Form.Item>
            <Form.Item label='队长奖勤返点' name='rewardsRebate'>
              <RewardsRebateEditor disabled={opType !== 'insert'} />
            </Form.Item>
            <Form.Item label='队员奖励内容' name='memberRewardList'>
              <MemberRewardListEditor disabled={opType !== 'insert'} />
            </Form.Item>
            <Form.Item label='备注' name='note'>
              <Input disabled={opType !== 'insert'} />
            </Form.Item>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default GroupActivityV3

// 奖励返点，用整数表示，100%用1000表示， 10%用100表示
const RewardsRebateEditor = (props) => {
  const { value, onChange, disabled } = props
  const fixValue = Number(value / 10)
  const fixInput = (v) => {
    if (v < 0) {
      v = 1
    }
    if (v > 100) {
      v = 100
    }
    return Number((v * 10).toFixed(0))
  }
  return <div>
    <InputNumber disabled={disabled} value={fixValue} onChange={(v) => { onChange(fixInput(v)) }} style={{ width: '8em' }} />%
  </div>
}

// 队员奖励内容编辑器
const MemberRewardListEditor = (props) => {
  const { value, onChange, disabled } = props
  const [first, second] = value

  const updateFirstReward = (option) => {
    const { value, ext: { rewardType, propsId, rewardId, icon } } = option
    let cp = deepClone(props.value[0])
    cp.name = value
    cp.rewardType = rewardType
    cp.propsId = propsId
    cp.rewardId = rewardId
    cp.icon = icon
    if (cp.rewardType === 0) {
      cp.rewardDays = 0
    }
    if (cp.rewardType !== 0 && cp.rewardDays === 0) {
      cp.rewardDays = 1
    }
    const newValue = [cp, second]
    onChange(newValue)
  }

  const updateSecondReward = (option) => {
    const { value, ext: { rewardType, propsId, rewardId, icon } } = option
    let cp = deepClone(props.value[1])
    cp.name = value
    cp.rewardType = rewardType
    cp.propsId = propsId
    cp.rewardId = rewardId
    cp.icon = icon
    if (cp.rewardType === 0) {
      cp.rewardDays = 0
    }
    if (cp.rewardType !== 0 && cp.rewardDays === 0) {
      cp.rewardDays = 1
    }
    const newValue = [first, cp]
    onChange(newValue)
  }

  const updateFirstDays = (v) => {
    let cp = deepClone(props.value[0])
    cp.rewardDays = v
    const newValue = [cp, second]
    onChange(newValue)
  }

  const updateSecondDays = (v) => {
    let cp = deepClone(props.value[1])
    cp.rewardDays = v
    const newValue = [first, cp]
    onChange(newValue)
  }

  return <div>
    <Space>
      <Select disabled={disabled} value={first.name} options={rewardTypeOptionsV2} onChange={(v, e) => { updateFirstReward(e) }} style={{ width: '10em' }} />
      <Select disabled={disabled} value={first.rewardDays} options={rewardDaysOptions} onChange={v => updateFirstDays(v)} style={{ width: '6em' }} />
      <Tooltip title={first.propsId}><Text type='secondary'>{first.rewardId}</Text></Tooltip>
      <ImagesList imgList={first.icon} height={30} width={30} />
    </Space>
    <div style={{ height: '1em' }} />
    <Space>
      <Select disabled={disabled} value={second.name} options={rewardTypeOptionsV3} onChange={(v, e) => { updateSecondReward(e) }} style={{ width: '10em' }} />
      <Select disabled={disabled} value={second.rewardDays} options={rewardDaysOptions} onChange={v => updateSecondDays(v)} style={{ width: '6em' }} />
      <Tooltip title={second.propsId}><Text type='secondary'>{second.rewardId}</Text></Tooltip>
      <ImagesList imgList={second.icon} height={30} width={30} />
    </Space>
  </div>
}

const MemberRewardListFormater = (value) => {
  const [first, second] = value
  let firstTip = `${first.rewardDays}天${first.name}`
  let secondTip = `${second.rewardDays}天${second.name}`
  if (firstTip === '0天无') {
    firstTip = '无'
  }
  if (secondTip === '0天无') {
    secondTip = '无'
  }
  return `${firstTip}; ${secondTip}`
}

const RewardNameFormater = (r) => {
  return <div>
    <Tooltip title={`rewardId=${r.rewardId}`}>{r.rewardName}</Tooltip>
    <Divider type='vertical' />
    <ImagesList imgList={r.rewardIcon} width={30} height={30} /></div>
}

const TimeRangeFormater = (start, end) => {
  let current = moment().unix()
  let isOnTime = current >= start && current <= end
  return <Text type={isOnTime ? 'danger' : ''}>{timeFormater(start, 1)}<br />{timeFormater(end, 1)}</Text>
}

// 奖励货币类型
// Y币（100）、紫水晶（200）、布料（300）、紫金币（400）、紫水晶券（500）、能量（600）、队友金（700）
const rewardTypeOptions = [
  { label: 'Y币', value: 100, ext: { rewardId: 727, icon: 'https://res.yy.com/fts/client/groupact/yb.png' } },
  { label: '紫水晶', value: 200, ext: { rewardId: 728, icon: 'https://res.yy.com/fts/client/groupact/amethyst.png' } },
  { label: '布料', value: 300, ext: { rewardId: 732, icon: 'https://res.yy.com/fts/client/podcustomer/fabric.png' } },
  { label: '紫金币', value: 400, ext: { rewardId: 736, icon: 'https://res.yy.com/fts/client/prop/purpleCoin.png' } },
  { label: '紫水晶券', value: 500, ext: { rewardId: 734, icon: 'https://res.yy.com/fts/client/prop/amethyst_ticket.png' } },
  { label: '能量', value: 600, ext: { rewardId: 733, icon: 'https://res.yy.com/fts/client/podcustomer/energy.png' } },
  { label: '队友金', value: 700, ext: { rewardId: 735, icon: 'https://res.yy.com/fts/client/prop/54.png' } }
]

// 豪标--1000 头像框--2000 入场秀--3000
const rewardTypeOptionsV2 = [
  { label: '无', value: '无', ext: { rewardType: 0, propsId: 0, rewardId: 0, icon: '' } },
  { label: '豪标', value: '豪标', ext: { rewardType: 1000, propsId: 0, rewardId: 731, icon: 'https://res.yy.com/fts/client/groupact/haobao.png' } }
]

// 幻紫骑士入场秀、兰博基尼入场秀、充值头像框
const rewardTypeOptionsV3 = [
  { label: '无', value: '无', ext: { rewardType: 0, propsId: 0, rewardId: 0, icon: '' } },
  { label: '幻紫骑士入场秀', value: '幻紫骑士入场秀', ext: { rewardType: 3000, rewardId: 729, propsId: showKnightID, icon: 'https://zhuiya.bs2cdn.yy.com/adminWeb/17d805aaf6934d14a3d8e624fb69222b.png' } },
  { label: '兰博基尼入场秀', value: '兰博基尼入场秀', ext: { rewardType: 3000, rewardId: 729, propsId: showCarId, icon: 'https://zhuiya.bs2cdn.yy.com/adminWeb/9d00cd0d1dfb4a76bef038cd32a25511.png' } },
  { label: '头像框', value: '头像框', ext: { rewardType: 2000, rewardId: 730, propsId: frameID, icon: 'https://zhuiya.bs2cdn.yy.com/adminWeb/9d3eb3fa46ed4e6f9bd24b741e1827d7.PNG' } } // 充值头像框
]

// 1、3、7、15、30、60
const rewardDaysOptions = [
  { label: '1天', value: 1 },
  { label: '3天', value: 3 },
  { label: '7天', value: 7 },
  { label: '15天', value: 15 },
  { label: '30天', value: 30 },
  { label: '60天', value: 60 }
]
