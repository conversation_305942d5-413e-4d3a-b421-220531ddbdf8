import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Tabs, Modal, Space, Button, message } from 'antd'
import Session from './session'

const moment = require('moment')
const namespace = 'competitionSession'

@connect(({ competitionSession }) => ({
  model: competitionSession
}))

class CompetitionSession extends Component {
  state = {
    comfirmVisble: false,
    selectID: ''
  }
  componentDidMount = () => {
    this.getConfigList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 新增或删除任务
  onEditMission = (key, action) => {
    console.debug(key, action)
    if (action === 'remove') {
      this.setState({ selectID: key, comfirmVisble: true })
    }
    if (action === 'add') {
      this.newMission()
    }
  }

  // 查询配置列表
  getConfigList = () => {
    this.onRefresh()
  }

  // 刷新列表
  onRefresh = () => {
    this.callModel('getConfigList')
  }

  // 确认删除任务
  onComfirmDelete () {
    message.warn('删除功能暂不开放, 请联系管理员处理')
  }

  // 确认新增任务
  newMission () {
    const { sessionList = [] } = this.props.model
    let nextYear = 0
    sessionList.forEach(item => {
      if (item.year > nextYear) {
        nextYear = item.year + 1
      }
    })
    const newConfig = { year: nextYear, stageList: [] }
    this.callModel('updateConfig', {
      isDetailMode: true,
      isJsonMode: true,
      params: newConfig,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('新增失败,请稍后再试: ' + msg)
          return
        }
        message.success('新增配置成功～')
        this.onRefresh()
      }
    })
  }

  render () {
    const { route } = this.props
    const { sessionList = [] } = this.props.model
    const { comfirmVisble } = this.state

    const { TabPane } = Tabs
    const timeNow = moment().unix()

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs type='editable-card' onEdit={this.onEditMission}>
            {sessionList.map((item, i) => {
              return <TabPane type='card' size='small' tab={item.year} key={timeNow + i}>
                <Session session={item} onRefresh={this.onRefresh} />
              </TabPane>
            })}
          </Tabs>
        </Card>

        <Modal title='确认删除这个报表任务么？' visible={comfirmVisble} footer={null} onCancel={() => this.setState({ comfirmVisble: false })} >
          <Space>
            <Button danger onClick={() => this.onComfirmDelete()} >确认</Button>
            <Button onClick={() => this.setState({ comfirmVisble: false })}>取消</Button>
          </Space>
        </Modal>

      </PageHeaderWrapper>
    )
  }
}

export default CompetitionSession
