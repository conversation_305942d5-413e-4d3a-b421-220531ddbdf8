import request from '@/utils/request'
import { stringify } from 'qs'

export function getRewardSummary (params) {
  return request(`/fts_hgame/guild_task/baby/boss/reward_summary_list?${stringify(params)}`)
}

export function getTaskProgress (params) {
  return request(`/fts_hgame/guild_task/baby/boss/task_progress_list?${stringify(params)}`)
}

// 发奖确认
export function listAwardConfirm (params) {
  return request(`/fts_hgame/guild_task/baby/boss/list_award_confirm?${stringify(params)}`)
}

// 发奖确认-调整合计
export function flushAwardConfirm (params) {
  return request(`/fts_hgame/guild_task/baby/boss/flush_award_confirm?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 发奖明细
export function listAwardDetail (params) {
  return request(`/fts_hgame/guild_task/baby/boss/list_award_detail?${stringify(params)}`)
}

// 提交审核
export function approvalReward (params) {
  return request(`/fts_hgame/guild_task/baby/boss/approval_reward?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}
