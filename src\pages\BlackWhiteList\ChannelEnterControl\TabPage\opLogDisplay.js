import React, { Component } from 'react'
import { connect } from 'dva'
import { tableStyle, timeFormater } from '@/utils/common'
import { Row, Col, Button, Table, InputNumber, Select, Divider, Form, Tooltip } from 'antd'
import { SearchOutlined } from '@ant-design/icons'

// 监控目标UID配置页面
const namespace = 'channelEnterControl'

@connect(({ channelEnterControl }) => ({
  model: channelEnterControl
}))

class OpLogDisplayer extends Component {
  state = {
    queryParams: {
      sid: 0,
      ssid: 0,
      uid: 0,
      limited: 100
    }
  }

  componentDidMount = () => {
    this.refreshList()
  }
  refreshList = () => {
    this.callModel('getOpLogList', { params: this.state.queryParams })
  }
  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 翻页后更新表格相关参数
  pageChange (pagination) {
    const { current, pageSize } = pagination
    this.changeState('page', current)
    this.changeState('size', pageSize)
  }
// 创建tooltip
createTooltip = (v, width = 10) => {
  return !v ? '-' : <Tooltip title={v}>
    <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', width: `${width}em` }}>{v}</div>
  </Tooltip>
}
render () {
  const { OplogList, currentPage, currentSize } = this.props.model

  const columns = [
    { title: '序号', render: (text, record, index) => ((currentPage - 1) * currentSize + index + 1) },
    { title: 'SID', dataIndex: 'sid' },
    { title: 'SSID', dataIndex: 'ssid' },
    { title: '时间', dataIndex: 'timestamp', render: (v) => { return timeFormater(v) } },
    { title: '事件', dataIndex: 'isAdd', render: (v) => { return v ? '开启限制' : '解除限制' } },
    { title: '相关UID', dataIndex: 'uid' },
    { title: '触发事件', dataIndex: 'event' },
    { title: '限制渠道', dataIndex: 'appIdList', render: (v) => { return v ? JSON.stringify(v) : '-' } },
    { title: '结果描述', dataIndex: 'result', width: '12em', align: 'center', render: (v) => { return this.createTooltip(v, 10) } }
  ]

  return (
    <div>
      <Row>
        <Form>
          <InputNumber placeholder='SID' style={{ width: '7em' }}
            onChange={(v) => { let before = this.state.queryParams; before.sid = v || 0; this.setState({ queryParams: before }) }} />
          <Divider type='vertical' />
          <InputNumber placeholder='SSID' style={{ width: '7em' }}
            onChange={(v) => { let before = this.state.queryParams; before.ssid = v || 0; this.setState({ queryParams: before }) }} />
          <Divider type='vertical' />
          <InputNumber placeholder='UID' style={{ width: '7em' }}
            onChange={(v) => { let before = this.state.queryParams; before.uid = v || 0; this.setState({ queryParams: before }) }} />
          <Divider type='vertical' />
          <Select style={{ width: '7em' }} placeholder='数量限制'
            onChange={(v) => { let before = this.state.queryParams; before.limited = v || 0; this.setState({ queryParams: before }) }}>
            <Select.Option key={50} value={50}>50</Select.Option>
            <Select.Option key={100} value={100}>100</Select.Option>
            <Select.Option key={200} value={200}>200</Select.Option>
            <Select.Option key={400} value={400}>200</Select.Option>
            <Select.Option key={1000} value={1000}>1000</Select.Option>
            <Select.Option key={2000} value={2000}>2000</Select.Option>
          </Select>
          <Button type='primary' style={{ marginLeft: '1em' }} onClick={() => { this.refreshList() }}>
            <SearchOutlined /> 查询
          </Button>
        </Form>
      </Row>
      <br />
      <Row >
        <Col span={24}>
          <Table columns={columns} dataSource={OplogList} size='small' pagination={tableStyle} scroll={{ x: 'max-content' }}
            rowKey={record => record.uid} onChange={(pagination) => { this.pageChange(pagination) }} />
        </Col>
      </Row>
    </div>
  )
}
}

export default OpLogDisplayer
