import React, { Component } from 'react'
import { connect } from 'dva'
import { Button, Col, Row, Form, Modal, Input, Select, DatePicker, Divider, Typography, Space, InputNumber, Tooltip, message, Table, Popconfirm } from 'antd'
import {
  actTypeOptions,
  propsTypeOptions,
  currencyOptions,
  defaultItem,
  rewardFormater,
  statusFormater,
  approvalFormater,
  timeRangeFormater,
  enterShowOptions,
  medalOptions,
  defaultFirstConfigList,
  defaultSecondConfigList,
  defaultThridConfigList,
  defaultPrizeItem
} from './common.js'
import { SearchSelect } from '@/components/SimpleComponents'
import moment from 'moment'
import { deepClone, checkIsInterge } from '@/utils/common'
const namespace = 'dropLuckRank'
const { Text, Link } = Typography

const defaultStartTime = moment().startOf('isoWeek').add(1, 'week')
const defaultEndTime = moment().endOf('isoWeek').add(2, 'week')
const defaultAutoWeek = 2

@connect(({ dropLuckRank }) => ({
  model: dropLuckRank
}))

class LuckRankActivity extends Component {
  state = {
    opType: 'add',
    isAutoWeek: false,
    isShowDetail: false,
    selectTarget: {},
    modalVisible: false,
    firstConfigList: [],
    secondConfigList: [],
    thirdConfigList: [],
    useAutoWeek: defaultAutoWeek,
    useStartTime: defaultStartTime
  }

  componentDidMount = () => {
    this.callModel('getActivityConfigList')
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 提交新增或更新操作
  onSubmitUpdate = (v, opType) => {
    const { firstConfigList, secondConfigList, thirdConfigList } = this.state
    if (Array.isArray(firstConfigList) && firstConfigList.length === 0) {
      message.error('第1名奖励列表不能为空')
      return
    } else if (Array.isArray(secondConfigList) && secondConfigList.length === 0) {
      message.error('第2名奖励列表不能为空')
      return
    } else if (Array.isArray(thirdConfigList) && thirdConfigList.length === 0) {
      message.error('第3名奖励列表不能为空')
      return
    }
    let params = { ...v }
    let week = params.autoWeek || 0
    if (!checkIsInterge(week) || week < 0 || week > 100) {
      message.error('周数填写有误，请检查')
      return
    }

    if (week > 0) { // 自动周活动
      params.startTime = v.startTime.unix()
      params.endTime = v.endTime.unix()
      params.actType = 7
    } else if (v.timeRange && v.timeRange.length === 2 && v.timeRange[0] != null && v.timeRange[1] != null) {
      params.startTime = v.timeRange[0].unix()
      params.endTime = v.timeRange[1].unix()
    } else {
      message.error('请输入活动时间')
      return
    }

    delete params['timeRange']
    delete params['rewards']

    firstConfigList.forEach(item => { item.desc = `${item.name}×${item.count}` })
    secondConfigList.forEach(item => { item.desc = `${item.name}×${item.count}` })
    thirdConfigList.forEach(item => { item.desc = `${item.name}×${item.count}` })
    params.firstRewards = firstConfigList
    params.secondRewards = secondConfigList
    params.thirdRewards = thirdConfigList
    // console.debug('params===>', params)
    let reason = this.checkTimeRange(params, opType)
    if (reason !== '') {
      message.warn(reason)
      return
    }

    let funcName = opType === 'add' ? 'addActivity' : 'updateActivity'
    this.callModel(funcName, {
      params: params,
      isDetailMode: true,
      isJsonMode: true,
      isSlientMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('新增失败：' + msg)
          return
        }
        message.info('已提交审批～')
        this.setState({ modalVisible: false, firstConfigList: [], secondConfigList: [], thirdConfigList: [] })
      }
    })
  }

  // 提交删除操作
  onSubmitDelete = (v) => {
    this.callModel('deleteActivity', {
      params: v,
      isJsonMode: true,
      isDetailMode: true,
      isSlientMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('删除失败: ' + msg)
          return
        }
        message.success('已提交审批')
      }
    })
  }

  // 将服务端配置转换成模态框表单数据
  parseToFormVal = (before) => {
    let after = { ...before }
    after.timeRange = [moment(before.startTime * 1000), moment(before.endTime * 1000)]
    let first = Array.isArray(before.firstRewards) ? before.firstRewards : []
    let second = Array.isArray(before.secondRewards) ? before.secondRewards : []
    let third = Array.isArray(before.thirdRewards) ? before.thirdRewards : []
    first = first.map((item, index) => { item.idx = index + 1; item.ranking = 1; return item })
    second = second.map((item, index) => { item.idx = index + 1; item.ranking = 2; return item })
    third = third.map((item, index) => { item.idx = index + 1; item.ranking = 3; return item })

    this.setState({ firstConfigList: first, secondConfigList: second, thirdConfigList: third })
    return after
  }

  // 点击新增配置按钮
  onCreateActivity = (isAutoWeek) => {
    let newID = parseInt(moment().format('YYMMDD')) * 100 + 1
    const { configList } = this.props.model
    configList.forEach(item => {
      if (`${item.id}`.length <= 9 && item.id >= newID) {
        newID = item.id + 1
      }
    })
    this.setState({ modalVisible: true, opType: 'add', isAutoWeek: isAutoWeek, firstConfigList: [] })
    let newValue = { id: newID, ...defaultItem }
    if (isAutoWeek) {
      newValue.autoWeek = defaultAutoWeek
      newValue.startTime = defaultStartTime
      newValue.endTime = defaultEndTime
    } else {
      newValue.timeRange = [defaultStartTime, moment(defaultStartTime).add(1, 'week').subtract(1, 'second')]
    }
    newValue.rewards = deepClone(defaultItem.rewards)
    this.formRef.setFieldsValue(newValue)
  }

  // 操作区域格式化
  opFormater = (r) => {
    const { endTime } = r
    const now = moment().unix()
    if (now > endTime) {
      return '-'
    }
    return (
      <Space>
        <Link onClick={() => {
          this.setState({ modalVisible: true, opType: 'update', isAutoWeek: false })
          this.formRef.setFieldsValue(this.parseToFormVal(r))
        }}>更新</Link>
        <Popconfirm title='确定删除么?' onConfirm={() => this.onSubmitDelete(r)}>
          <Link type='danger'>删除</Link>
        </Popconfirm>
      </Space>
    )
  }

  // 检查时间范围是否出现重叠
  checkTimeRange = (v, opType) => {
    const { configList } = this.props.model
    let testList = [...configList]
    if (opType === 'add') {
      testList.push(v)
    }
    if (opType === 'update') {
      let id = testList.findIndex(item => item.id === v.id)
      testList[id] = v
    }
    testList.sort((a, b) => { return a.startTime < b.startTime ? -1 : 1 })
    let lastTime = 0
    for (let act of testList) {
      if (lastTime > act.startTime) {
        return '活动时间出现重叠'
      }
      lastTime = act.startTime
      if (lastTime > act.endTime) {
        return '活动时间出现重叠'
      }
      lastTime = act.endTime
    }
    return ''
  }

  handleAddItem = (ranking) => () => {
    const { firstConfigList, secondConfigList, thirdConfigList } = this.state
    let newList = []
    if (ranking === 1) {
      newList = firstConfigList
    } else if (ranking === 2) {
      newList = secondConfigList
    } else if (ranking === 3) {
      newList = thirdConfigList
    }

    newList.push(defaultPrizeItem)
    for (let i = 0; i < newList.length; i++) {
      newList[i].idx = i + 1
      newList[i].ranking = ranking
    }
    // console.log('handleAddItem', ranking, firstConfigList, newList)
    if (ranking === 1) {
      this.setState({ firstConfigList: deepClone(newList) })
    } else if (ranking === 2) {
      this.setState({ secondConfigList: deepClone(newList) })
    } else if (ranking === 3) {
      this.setState({ thirdConfigList: deepClone(newList) })
    }
  }

  handleDel = (key, ranking) => {
    const { firstConfigList, secondConfigList, thirdConfigList } = this.state
    let newList = []
    if (ranking === 1) {
      newList = firstConfigList
    } else if (ranking === 2) {
      newList = secondConfigList
    } else if (ranking === 3) {
      newList = thirdConfigList
    }
    let targetList = []
    let index = 0
    newList.map(item => {
      console.log(key, item)
      if (item.idx !== key) {
        item.idx = index++
        targetList.push(item)
      }
    })
    console.log('handleDel', key, ranking, firstConfigList, targetList)
    if (ranking === 1) {
      this.setState({ firstConfigList: deepClone(targetList) })
    } else if (ranking === 2) {
      this.setState({ secondConfigList: deepClone(targetList) })
    } else if (ranking === 3) {
      this.setState({ thirdConfigList: deepClone(targetList) })
    }
  }

  countItemPrize = (item) => {
    // console.log('countItemPrize', item)
    let value = 0
    if (parseInt(item.count) > 0 && parseInt(item.price) > 0) {
      value = item.count * item.price / 1000
    }
    return value.toLocaleString()
  }

  countTotalPrize = (list) => {
    let value = 0
    list.forEach(item => {
      if (parseInt(item.count) > 0 && parseInt(item.price) > 0) {
        value += item.count * item.price
      }
    })
    return (value / 1000).toLocaleString()
  }

  countAllTotalPrize = (list1, list2, list3) => {
    let value = 0
    list1.forEach(item => {
      if (parseInt(item.count) > 0 && parseInt(item.price) > 0) {
        value += item.count * item.price
      }
    })
    list2.forEach(item => {
      if (parseInt(item.count) > 0 && parseInt(item.price) > 0) {
        value += item.count * item.price
      }
    })
    list3.forEach(item => {
      if (parseInt(item.count) > 0 && parseInt(item.price) > 0) {
        value += item.count * item.price
      }
    })
    return (value / 1000).toLocaleString()
  }

  changePropsTypeValue = (index, ranking, newVal) => {
    const { firstConfigList, secondConfigList, thirdConfigList } = this.state
    let targetList = []
    if (ranking === 1) {
      targetList = firstConfigList
    } else if (ranking === 2) {
      targetList = secondConfigList
    } else if (ranking === 3) {
      targetList = thirdConfigList
    }
    for (let i = 0; i < targetList.length; i++) {
      if (targetList[i].idx === index) {
        targetList[i].propsType = newVal
        targetList[i].prizeId = null
      }
    }
    console.log('changePropsTypeValue', index, ranking, newVal, targetList)
    if (ranking === 1) {
      this.setState({ firstConfigList: deepClone(targetList) })
    } else if (ranking === 2) {
      this.setState({ secondConfigList: deepClone(targetList) })
    } else if (ranking === 3) {
      this.setState({ thirdConfigList: deepClone(targetList) })
    }
  }

  changeCount = (index, ranking, newValue) => {
    const { firstConfigList, secondConfigList, thirdConfigList } = this.state
    let targetList = []
    if (ranking === 1) {
      targetList = firstConfigList
    } else if (ranking === 2) {
      targetList = secondConfigList
    } else if (ranking === 3) {
      targetList = thirdConfigList
    }
    for (let i = 0; i < targetList.length; i++) {
      if (targetList[i].idx === index) {
        targetList[i].count = newValue
        // targetList[i].totalPrice = targetList[i].price * newValue / 1000
      }
    }
    console.log('changeCount', index, ranking, newValue, targetList)
    if (ranking === 1) {
      this.setState({ firstConfigList: deepClone(targetList) })
    } else if (ranking === 2) {
      this.setState({ secondConfigList: deepClone(targetList) })
    } else if (ranking === 3) {
      this.setState({ thirdConfigList: deepClone(targetList) })
    }
  }

  changePrize = (index, ranking, item) => {
    const { raw = {} } = item
    const { firstConfigList, secondConfigList, thirdConfigList } = this.state
    let targetList = []
    if (ranking === 1) {
      targetList = firstConfigList
    } else if (ranking === 2) {
      targetList = secondConfigList
    } else if (ranking === 3) {
      targetList = thirdConfigList
    }
    for (let i = 0; i < targetList.length; i++) {
      if (targetList[i].idx === index) {
        targetList[i].prizeId = raw.id
        targetList[i].name = raw.name
        targetList[i].price = raw.price
        targetList[i].url = raw.url
      }
    }
    console.log('changePrize', index, ranking, item, targetList)
    if (ranking === 1) {
      this.setState({ firstConfigList: deepClone(targetList) })
    } else if (ranking === 2) {
      this.setState({ secondConfigList: deepClone(targetList) })
    } else if (ranking === 3) {
      this.setState({ thirdConfigList: deepClone(targetList) })
    }
  }

  optionsSelect = (propsType, prizeOptions) => {
    if (propsType === 0) {
      return prizeOptions
    }
    if (propsType === 1) {
      return currencyOptions
    }
    if (propsType === 2) {
      return enterShowOptions
    }
    if (propsType === 3) {
      return medalOptions
    }
  }

  // 使用默认奖励
  useDefaultPrze = () => {
    this.setState({
      firstConfigList: deepClone(defaultFirstConfigList),
      secondConfigList: deepClone(defaultSecondConfigList),
      thirdConfigList: deepClone(defaultThridConfigList)
    })
    this.formRef.setFieldsValue({ rewardLimit: 5000000 })
  }

  // 生成活动时间范围列表
  genTimeLineDetail = (week, startTime) => {
    if (!startTime || week <= 0) {
      return <div>请正确填写开始时间和周数</div>
    }
    let items = []
    for (let i = 0; i < week; i++) {
      let t1 = moment(startTime)
      let t2 = moment(startTime)
      t1.add(i * 7, 'day')
      t2.add(i * 7 + 7, 'day').subtract(1, 'second')
      items.push(<Row>{t1.format('YYYY-MM-DD HH:mm:ss')} ~ {t2.format('YYYY-MM-DD HH:mm:ss')}</Row>)
    }
    return <div>{items}</div>
  }

  render () {
    const { opType, isAutoWeek, modalVisible, firstConfigList, secondConfigList, thirdConfigList, isShowDetail, useAutoWeek, useStartTime } = this.state
    const { prizeOptions, configList } = this.props.model
    console.debug('firstConfigList==>', firstConfigList)
    const columns = [
      { title: '活动ID', dataIndex: 'id' },
      { title: '活动周期', dataIndex: 'actType', render: (v) => { return actTypeOptions.find(item => item.value === v)?.label } },
      { title: '活动时间', dataIndex: '', render: (v, r) => { return timeRangeFormater(r, 0) } },
      { title: '道具配置', dataIndex: 'rewards', render: (v, r) => { return rewardFormater(r) } },
      { title: '活动状态', dataIndex: '', render: (v, r) => { return statusFormater(r) } },
      { title: '审批流', dataIndex: '', render: (v, r) => { return approvalFormater(r) } },
      { title: '操作', dataIndex: '', render: (v, r) => { return this.opFormater(r) } }
    ].map(item => { item.align = 'center'; return item })

    const columnsConfig = [
      { title: '',
        dataIndex: 'propsType',
        render: (v, r) => {
          return <Tooltip title='礼物类型'>
            <Select options={propsTypeOptions} style={{ width: '8em' }} value={v} onChange={(val) => { this.changePropsTypeValue(r.idx, r.ranking, val) }} />
          </Tooltip>
        } },
      { title: '',
        dataIndex: 'prizeId',
        render: (v, r) => {
          return <Tooltip title='礼物或道具'>
            <SearchSelect options={this.optionsSelect(r.propsType, prizeOptions)} style={{ minWidth: '8em' }} value={v} onChange={(val, raw) => { this.changePrize(r.idx, r.ranking, raw) }} />
          </Tooltip>
        } },
      { title: '',
        dataIndex: 'count',
        render: (v, r) => {
          return <Tooltip title='发放数量'>
            <InputNumber style={{ width: '6em' }} placeholder={r.propsType <= 1 ? '数量/个' : '天数/天'} min={0} value={v} onChange={(val) => { this.changeCount(r.idx, r.ranking, val) }} />
          </Tooltip>
        } },
      { title: '', dataIndex: 'totalPrice', render: (v, r) => { return r.totalPrice === null ? ' - ' : '总金额:' + this.countItemPrize(r) + '元' } },
      { title: '', align: 'center', render: (text, record) => { return <span><Popconfirm title='确认删除?' onConfirm={() => this.handleDel(record.idx, record.ranking)}><a href='' style={{ color: 'red' }}>✗</a></Popconfirm></span> } }
    ].map(item => { item.align = 'center'; return item })

    return (
      <Row>
        <Col span={24} style={{ marginBottom: '1em' }}>
          <Space>
            <Button type='primary' onClick={() => this.onCreateActivity(false)}>新增</Button>
            <Button type='primary' onClick={() => this.onCreateActivity(true)}>批量新增</Button>
          </Space>
        </Col>
        <Col span={24}>
          <Table columns={columns} dataSource={configList} scroll={{ x: 'max-content' }} />
        </Col>
        <Col span={24}>
          <Modal width={550} visible={modalVisible} title={opType === 'add' ? '新增活动配置' : '更新活动配置'} maskClosable={false} forceRender
            onCancel={() => this.setState({ modalVisible: false, firstConfigList: [], secondConfigList: [], thirdConfigList: [] })}
            onOk={() => { this.formRef.submit() }} >
            <Form labelCol={{ span: 4 }} ref={from => { this.formRef = from }} onFinish={(v) => this.onSubmitUpdate(v, opType)} >
              <Form.Item label='活动ID' name='id'>
                <Input style={{ width: '70%' }} disabled />
              </Form.Item>
              {
                isAutoWeek
                  ? <>
                    <Form.Item label='活动周数' name='autoWeek' rules={[{ required: true, message: '请输入活动周数' }]} >
                      <InputNumber style={{ width: '70%' }} step={1} min={2} max={100}
                        onChange={v => {
                          let endTime = moment(this.formRef.getFieldValue('startTime')).add(7 * v, 'day').subtract(1, 'second')
                          this.setState({ useAutoWeek: v })
                          this.formRef.setFieldsValue({ endTime: endTime })
                        }
                        } />
                    </Form.Item>
                    <Form.Item label='开始时间' name='startTime' rules={[{ required: true, message: '请输入活动时间' }]} >
                      <DatePicker showTime format='YYYY-MM-DD HH:mm:ss'
                        onChange={v => {
                          let endTime = moment(v).add(7 * this.formRef.getFieldValue('autoWeek'), 'day').subtract(1, 'second')
                          this.setState({ useStartTime: v })
                          this.formRef.setFieldsValue({ endTime: endTime })
                        }
                        } />
                    </Form.Item>
                    <Form.Item label='结束时间' name='endTime'>
                      <DatePicker showTime format='YYYY-MM-DD HH:mm:ss' disabled />
                    </Form.Item>
                    <Text type='secondary'>*以上时段内默认每个自然周开始, <Link onClick={() => { this.setState({ isShowDetail: !isShowDetail }) }} >点击预览时间</Link>, 每周奖励如下 </Text>
                    {
                      isShowDetail
                        ? this.genTimeLineDetail(useAutoWeek, useStartTime)
                        : ''
                    }
                  </>
                  : <>
                    <Form.Item label='活动周期' name='actType' >
                      <Select style={{ width: '70%' }} options={actTypeOptions} />
                    </Form.Item>
                    <Form.Item label='活动时间' name='timeRange' rules={[{ type: 'array', required: true, message: '请输入活动时间' }]}>
                      <DatePicker.RangePicker style={{ width: '70%' }} showTime format='YYYY-MM-DD HH:mm:ss' />
                    </Form.Item>
                  </>
              }

              <Divider style={{ color: '#c4c4c4e0', padding: '0 2em' }}>道具配置</Divider>
              <Row style={{ marginBottom: '1em' }}>
                <Button type='primary' onClick={() => { this.useDefaultPrze() }}>使用默认配置</Button>
              </Row>
              <Form.Item labelCol={1} name='rewards'>
                <Row>
                  <Col>
                    <Text type='secondary'>第1名 </Text>
                    <Button style={{ marginLeft: 10, marginRight: 10 }} onClick={this.handleAddItem(1)} >新增奖励</Button>
                    <Text type='secondary'> 总金额: {this.countTotalPrize(firstConfigList)} 元</Text>
                  </Col>
                  <Col>
                    {
                      Array.isArray(firstConfigList) && firstConfigList.length > 0
                        ? <Table size='small' rowKey='idx' pagination={false} columns={columnsConfig} dataSource={firstConfigList} />
                        : ''
                    }
                  </Col>
                </Row>
              </Form.Item>

              <Form.Item labelCol={1} name='rewards'>
                <Row>
                  <Col>
                    <Text type='secondary'>第2名 </Text>
                    <Button style={{ marginLeft: 10, marginRight: 10 }} onClick={this.handleAddItem(2)} >新增奖励</Button>
                    <Text type='secondary'> 总金额: {this.countTotalPrize(secondConfigList)} 元</Text>
                  </Col>
                  <Col>
                    {
                      Array.isArray(secondConfigList) && secondConfigList.length > 0
                        ? <Table size='small' rowKey='idx' pagination={false} columns={columnsConfig} dataSource={secondConfigList} />
                        : ''
                    }
                  </Col>
                </Row>
              </Form.Item>

              <Form.Item labelCol={1} name='rewards'>
                <Row>
                  <Col>
                    <Text type='secondary'>第3名 </Text>
                    <Button style={{ marginLeft: 10, marginRight: 10 }} onClick={this.handleAddItem(3)} >新增奖励</Button>
                    <Text type='secondary'> 总金额: {this.countTotalPrize(thirdConfigList)} 元</Text>
                  </Col>
                  <Col>
                    {
                      Array.isArray(thirdConfigList) && thirdConfigList.length > 0
                        ? <Table size='small' rowKey='idx' pagination={false} columns={columnsConfig} dataSource={thirdConfigList} />
                        : ''
                    }
                  </Col>
                </Row>
              </Form.Item>

              <Text type='secondary'>前3名合计金额: {this.countAllTotalPrize(firstConfigList, secondConfigList, thirdConfigList)} 元</Text>
              <Divider style={{ color: '#c4c4c4e0' }}>发放道具门槛配置</Divider>
              <Form.Item labelCol={{ span: 10 }} label='发放道具的最低幸运值' name='rewardLimit' >
                <InputNumber min={0} style={{ marginLeft: 10, width: '10em' }} />
              </Form.Item>
              <Text style={{ marginLeft: 50 }} type='secondary'>(0.1YB=10幸运值)</Text>
            </Form>
          </Modal>
        </Col>
      </Row>
    )
  }
}

export default LuckRankActivity
