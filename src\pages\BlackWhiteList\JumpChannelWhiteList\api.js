import request from '@/utils/request'

export function getLists () {
  let url = `/white_list/jump_channel_list`
  return request(url, { jsonp: true })
}

export function whiteListAddMult (sidSsidList) {
  let form = 'sid_ssid_text=' + sidSsidList
  return request(`/white_list/jump_channel_add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: form
  })
}

export function whiteListDel (sid, ssid) {
  let form = 'sid=' + sid + '&ssid=' + ssid
  return request(`/white_list/jump_channel_del`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: form
  })
}

export function whiteListDelMultMult (sidSsidList) {
  let form = 'sid_ssid_text=' + sidSsidList
  return request(`/white_list/jump_channel_batch_del`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: form
  })
}
