/*
  通用组件——表单文件输入器
  参数:
  value: string, 文件下载链接
  sizeLimit: number, 限制文件大小_mb
  typeLimit: []string, 限制的类型,默认不限制
  fileServer: string, 目标文件服务器 [jy_default, zhuiwan_baiduBOS]
  onChange： function, 链接更新回调 (value)=>{...}
  onlyUpload: bool, 是否禁止手动填写, 默认允许
*/

import React, { Component } from 'react'
import { Input, message, Upload, Button, Tooltip } from 'antd'
import { SyncOutlined } from '@ant-design/icons'
import uploadFileToBaiduOBS from '@/utils/zhuiwanBaiduBos'
import { uploadFileToJYProServer } from '@/utils/request'
import { DownloadURL } from '@/components/SimpleComponents'

const defaultSizeLimit = 2 // 默认上传大小限制
const defaultFileServer = 'jy_default'

class InputFile extends Component {
  state = {
    downloadURL: '',
    singleEditerVisible: false,
    batchEditerVisible: false,
    sizeLimit: 0,
    fileServer: '',
    onlyUpload: false,
    notSHowDownloadURL: false,
    editMode: true // true-上传, false-编辑
  }

  componentDidMount = () => {
    const { value, sizeLimit, fileServer, typeLimit, onlyUpload, notSHowDownloadURL } = this.props
    this.setState({
      downloadURL: value || '',
      fileServer: fileServer || defaultFileServer,
      sizeLimit: sizeLimit || defaultSizeLimit,
      typeLimit: typeLimit,
      onlyUpload: onlyUpload || false,
      notSHowDownloadURL: notSHowDownloadURL || false
    })
  }

  // 上传文件前的检查
  beforeUpload = (file) => {
    console.info('file=', file)
    const { sizeLimit, typeLimit } = this.state
    if (typeLimit && typeLimit.indexOf(file.type) < 0) {
      message.warn(`仅支持以下格式的文件: ${typeLimit}, 当前格式: ${file.type}`)
      return false
    }
    const overLoad = file.size / 1024 / 1024 > sizeLimit
    if (overLoad) {
      message.error(`文件大小不能大于${sizeLimit}mb`)
      return false
    }
    return true
  }

  // 文件修改回调
  onDownloadValueChange = (newValue) => {
    const { onChange } = this.props
    if (typeof onChange === 'function') {
      onChange(newValue)
      this.setState({ downloadURL: newValue })
    } else {
      message.warn('使用方法错误,请检查代码')
    }
  }

  // 上传文件回调
  uploadCallback = (isOk, newUrl) => {
    if (isOk) {
      message.success('上传成功～')
      this.onDownloadValueChange(newUrl)
    } else {
      message.warn('上传失败,请稍后再试')
    }
    this.setState({ singleEditerVisible: false })
  }
  // 从下载链接中获取文件名
  getFileNameFromLink = (link) => {
    if (!link) link = ''
    const items = link.split('?')[0].split('/')
    return items[items.length - 1]
  }

  render () {
    const { downloadURL, editMode, fileServer, onlyUpload, notSHowDownloadURL } = this.state
    const SwitchBtn = () => {
      return <Tooltip title='切换输入模式' >
        <Button disabled={onlyUpload} style={{ marginLeft: '5px' }} onClick={() => this.setState({ editMode: !editMode })}><SyncOutlined /></Button>
      </Tooltip>
    }

    return (
      <>
        {!notSHowDownloadURL ? <span style={{ marginRight: '1em' }}><DownloadURL src={downloadURL} /></span> : <span />}
        {
          !editMode
            ? <span>
              <Input style={{ width: '15em' }} value={downloadURL} onChange={e => this.onDownloadValueChange(e.target.value)} /><SwitchBtn />
            </span>
            : <span>
              <Upload showUploadList={false}
                beforeUpload={this.beforeUpload}
                customRequest={(r) => {
                  switch (fileServer) {
                    case 'zhuiwan_baiduBOS':
                      uploadFileToBaiduOBS(r, this.uploadCallback)
                      break
                    default:
                      uploadFileToJYProServer(r, this.uploadCallback)
                  }
                }} >
                <Button>上传</Button>
              </Upload>
              <SwitchBtn />
            </span>
        }
      </>
    )
  }
}

export default InputFile
