import { getLists } from './api'

export default {
  namespace: 'hatkingBetDetail',

  state: {
    list: []
  },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }

      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getLists, payload)
      yield put({
        type: 'updateList',
        payload: Array.isArray(list) ? list : []
      })
    }
  }
}
