import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const addActivity = genUpdateTemplate('/rank/drop_luck_rank_boss/update_activity_config/ADD')
const updateActivity = genUpdateTemplate('/rank/drop_luck_rank_boss/update_activity_config/UPDATE')
const deleteActivity = genUpdateTemplate('/rank/drop_luck_rank_boss/update_activity_config/DEL')
const getActivityConfigList = genGetRequireTemplate('/rank/drop_luck_rank_boss/get_activity_config', 'configList')
const getApprovalList = genGetRequireTemplate('/rank/drop_luck_rank_boss/get_approval_list', 'approvalList')
const getPrizeList = genGetRequireTemplate('/drop/admin/query_prize_list?appId=2', 'prizeOptions', (ret) => {
  return ret.map(item => {
    return {
      label: `${item.name} (${item.id})`,
      value: item.id,
      key: item.id,
      raw: { id: item.id, name: item.name, price: item.price, url: item.url } }
  })
})

export default {
  namespace: 'dropLuckRank',
  state: {
    prizeOptions: [],
    configList: [],
    title: 'Bind success!'
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getPrizeList,
    addActivity,
    updateActivity,
    deleteActivity,
    getActivityConfigList,
    getApprovalList
  }
}
