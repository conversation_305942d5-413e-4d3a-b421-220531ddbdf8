import React, { Component } from 'react'
import Iframe from 'react-iframe'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Spin, Typography } from 'antd'
import { connect } from 'dva'
import styles from './index.module.less'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
const { Text } = Typography

@connect(({ global }) => ({
  ...global
}))

class LegacyPage extends Component {
  state = {
    loading: true,
    selectApp: ''
  }

  // 处理内嵌页面的菜单路径，得到真实访问链接
  getLegacyUrl () {
    const { match, uri, OutURL } = this.props
    const { location } = window

    let prefix = Object.keys(OutURL) || [] // [ "/outer/pkbaby", "/outer/zhuiya", "/outer/ys" ]

    let searchParams = new URLSearchParams(window.location.search)
    // 处理追呀或宝贝后台外部页面
    for (let i = 0; i < prefix.length; i++) {
      let fixPath = location.pathname
      // eslint-disable-next-line no-useless-escape
      let reg = /\/n\/[\d\/]+\/outer/ 
      fixPath = fixPath.replace(reg, '/n/outer') 
      let begin = fixPath.indexOf(prefix[i])  
      if (begin >= 0) {
        const host = OutURL[prefix[i]] // 例如 https://zhuiya-test.yy.com/admin-static/iframe.html#
        const path = fixPath.substr(begin + prefix[i].length) // 例如 /banner
        if (prefix[i] === '/outer/ys') {
          searchParams.append('isHide', 'hide')
        }

        const search = '?' + searchParams.toString()

        let entryURL = host + path + search
        return entryURL
      }
    }
    // 处理boss旧后台页面
    let fixUri = `${uri || match.path}`.replace(/^\/+/, '')
    fixUri = fixUri.replace(/\/+$/, '')
    let entryURL = `/admin/${fixUri}.html` 
    return entryURL
  }

  getMatchLegacCase = () => {
    const { OutURL } = this.props
    const { location } = window
    let prefix = Object.keys(OutURL) || []
    for (let entry of prefix) {
      if (location.pathname.indexOf(entry) >= 0) {
        return entry
      }
    }
    return ''
  }

  onLoad = () => {
    try {
      const document = window.frames['legacy-frame'].document
      if (!document) {
        this.setState({ loading: false })
        return
      }
      $(document).find('.navbar').hide()
      $(document).find('.sidebar-nav').hide()
      $(document).find('.content').css({
        'margin-left': 0,
        'border-left': 0
      })
      $(document).find('.header').hide()
      $(document).find('footer').hide()
    } catch (err) {
      console.log('can not hide item:', err)
    }
    this.setState({ loading: false })
  }

  render () {
    const { route } = this.props
    const { selectApp } = this.state
    return (
      <PageHeaderWrapper title={route.name}>
        <Text style={{ color: 'darkgray' }}>外部链接: {this.getLegacyUrl()}</Text>
        <Spin size='large' spinning={this.state.loading} wrapperClassName={styles.spinStyle}>
          <div style={{ height: '75vh', borderRadius: '4px', backgroundColor: '#fff' }} key={selectApp}>

            {/* 业务选择器,仅追呀后台显示 */}
            <div hidden={this.getMatchLegacCase() !== '/outer/zhuiya'} style={{ position: 'absolute', right: '0', top: '-2.5em' }}>
              <Tabs id='app' defaultActiveKey='zhuiwan' hideSuffix type='card' size='small' onChange={v => { this.setState({ selectApp: v }) }}>
                <TabPane tab='追玩' key='zhuiwan' />
                <TabPane tab='Yo语音' key='yomi' />
                <TabPane tab='YaYa语音' key='yaya' />
              </Tabs>
            </div>

            <Iframe
              url={this.getLegacyUrl()}
              name='legacy-frame'
              position='relative'
              onLoad={this.onLoad}
              display={this.state.loading ? 'none' : 'block'}
              overflow='scroll'
              height='100%'
              width='100%'
              frameBorder={0}
            />
          </div>
        </Spin>
      </PageHeaderWrapper>
    )
  }
}

export default LegacyPage
