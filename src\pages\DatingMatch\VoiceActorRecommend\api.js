import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/VoiceActorRecommend/GetList?${stringify(params)}`)
}

export function add (params) {
  return request(`/VoiceActorRecommend/Insert?${stringify(params)}`)
}

export function remove (params) {
  return request(`/VoiceActorRecommend/Remove`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: stringify(params)
  })
}

export function update (params) {
  return request(`/VoiceActorRecommend/Update?${stringify(params)}`)
}
