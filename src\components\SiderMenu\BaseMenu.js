/* eslint-disable eqeqeq */
import React, { PureComponent } from 'react'
import { Menu } from 'antd'
import { Link } from 'dva/router'
// import { formatMessage } from 'umi/locale'
import pathToRegexp from 'path-to-regexp'
import styles from './index.module.less'
import { SettingOutlined } from '@ant-design/icons'

const { SubMenu } = Menu

// Allow menu.js config icon as string or ReactNode
//   icon: 'setting',
//   icon: 'http://demo.com/icon.png',
//   icon: <SettingOutlined />,
const getIcon = icon => {
  if (typeof icon === 'string' && icon.indexOf('http') === 0) {
    return <img src={icon} alt='icon' className={styles.icon} />
  }
  if (typeof icon === 'string') {
    return <SettingOutlined />
  }
  return icon
}

export const getMenuMatches = (flatMenuKeys, path) =>
  flatMenuKeys.filter(item => item && pathToRegexp(item).test(path))

export default class BaseMenu extends PureComponent {
  /**
   * 获得菜单子节点
   * @memberof SiderMenu
   */
  getNavMenuItems = (menusData, parent) => {
    if (!menusData) {
      return []
    } 
    let tmp = []
    menusData.forEach(item => {
      if (!item.name || item.hideInMenu) {
        return
      }
      if (!item.children && item.auth === 0) { // 隐藏没权限的子页面
        return
      }
      const ItemDom = this.getSubMenuOrItem(item, parent)
      if (!ItemDom) {
        return
      }
      tmp.push(this.checkPermissionItem(item.authority, ItemDom)) 
    })
    return tmp
  }

  /**
   * get SubMenu or Item
   */
  getSubMenuOrItem = (item) => {
    // doc: add hideChildrenInMenu
    if (item.children && !item.hideChildrenInMenu && item.children.some(child => child.name)) {
      // const name = formatMessage({ id: item.locale })
      let fixdChild = this.getNavMenuItems(item.children)
      if (!fixdChild || fixdChild.length === 0) {
        return null
      }
      const name = item.name
      return (
        <SubMenu
          title={
            item.icon ? (
              <span>
                {getIcon(item.icon)}
                <span>{name}</span>
              </span>
            ) : (
              name
            )
          }
          key={item.path}
        >
          {fixdChild}
        </SubMenu>
      )
    }
    // console.log('menuItem', item)
    // 页面菜单的key加多一个后缀避免一个选中一个高光多个的问题
    return <Menu.Item key={item.path + '%'}>
      {this.getMenuItemPath(item)}
    </Menu.Item>
  };

  /**
   * 判断是否是http链接.返回 Link 或 a
   * Judge whether it is http link.return a or Link
   * @memberof SiderMenu
   */
  getMenuItemPath = item => {
    // const name = formatMessage({ id: item.locale })
    const name = item.name
    const itemPath = this.conversionPath(item.path)
    const icon = getIcon(item.icon)
    const { target } = item
    // Is it a http link
    if (/^https?:\/\//.test(itemPath)) {
      return (
        <a href={itemPath} target={target}>
          {icon}
          <span>{name}</span>
        </a>
      )
    }
    const { location, isMobile, onCollapse } = this.props
    // console.log('item', item)
    return (
      <Link
        to={itemPath}
        target={target}
        replace={itemPath === location.pathname}
        onClick={() => {
          // console.debug('baseMenu.js---> item.path=', item.path)
          // console.debug('baseMenu.js---> windows=', window)
          window.bossSelectKey = item.path
          if (isMobile) {
            onCollapse(true)
          }
        }}
      >
        {icon}
        <span>{name}</span>
      </Link>
    )
  };

  // permission to check
  checkPermissionItem = (authority, ItemDom) => {
    const { Authorized } = this.props
    if (Authorized && Authorized.check) {
      const { check } = Authorized
      return check(authority, ItemDom)
    }
    return ItemDom
  };

  conversionPath = path => {
    if (path.indexOf('/outer') > -1) {
      return path // 外部域名
    }
    if (path && path.indexOf('http') === 0) {
      return path
    }
    return `/${path || ''}`.replace(/\/+/g, '/')
  };

  render () {
    const { theme, mode, style } = this.props
    const { openKeys, selectedKeys } = this.props
    const { handleOpenChange, menuData } = this.props
    // console.debug('menuData====>', menuData)
    return (
      <Menu
        key='Menu'
        mode={mode}
        theme={theme}
        onOpenChange={handleOpenChange}
        style={style}
        className={mode === 'horizontal' ? 'top-nav-menu' : ''}
        selectedKeys={selectedKeys + '%'}
        openKeys={openKeys}
      >
        {this.getNavMenuItems(menuData)}
      </Menu>
    )
  }
}
