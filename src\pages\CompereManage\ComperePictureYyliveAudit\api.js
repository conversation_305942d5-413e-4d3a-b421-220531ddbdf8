import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/yylive_compere_picture/search_picture_list?${stringify(params)}`)
}

export function auditStatus (params) {
  return request(`/yylive_compere_picture/update_audit_status`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
