import LineWrap from '@/components/LineWrapper'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { DeleteOutlined, EditOutlined, CopyOutlined, SearchOutlined } from '@ant-design/icons'
import { Button, Card, DatePicker, Divider, Form, Input, message, Modal, Popconfirm, Select, Table, Tooltip } from 'antd'
import { connect } from 'dva'
import { copyToClip } from '@/utils/common'
import React, { Component } from 'react'
var moment = require('moment')

const namespace = 'moduleSwitch'
const FormItem = Form.Item
const TextArea = Input.TextArea
const Option = Select.Option

const statusMap = { 0: '开启', 1: '暂停', 2: '升级', 3: '停止' }
const statusConfig = [
  { value: 0, label: '开启' },
  { value: 1, label: '暂停' },
  { value: 2, label: '升级' },
  { value: 3, label: '停止' }
]
let statusOption = []
for (let i = 0; i < statusConfig.length; i++) {
  statusOption.push(<Option key={i} value={statusConfig[i].value}>{statusConfig[i].label}</Option>)
}

@connect(({ moduleSwitch }) => ({
  model: moduleSwitch
}))

class ModuleSwitch extends Component {
  columns = [
    { title: '模块ID', dataIndex: 'moduleId', align: 'center' },
    { title: '模块名',
      dataIndex: 'moduleName',
      align: 'left',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ width: 300, padding: 8, borderRadius: 6, background: '#FFF', boxShadow: '0 1px 6px' }}>
          <Input style={{ width: 140, marginRight: 10 }} value={selectedKeys[0]} onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={this.handleSearch(selectedKeys, confirm)} />
          <Button style={{ marginRight: 5 }} type='primary' onClick={this.handleSearch(selectedKeys, confirm)}>搜索</Button>
          <Button onClick={this.handleReset(clearFilters)}>重置</Button>
        </div>
      ),
      onFilter: (value, record) => record.moduleName.toLowerCase().includes((value.toLowerCase())),
      filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#108ee9' : '#aaa' }} />,
      width: '15%',
      render: text => {
        const { searchText } = this.state
        return searchText ? (
          <span>
            {text.split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i')).map((fragment, i) => (
              fragment.toLowerCase() === searchText.toLowerCase() ? <span key={i} style={{ color: '#f50' }}><LineWrap title={fragment} lineClampNum={1} /></span> : <LineWrap title={fragment} lineClampNum={1} />
            ))}</span>) : <LineWrap title={text} lineClampNum={1} />
      }
    },
    { title: '状态', dataIndex: 'status', align: 'center', render: (text, record) => { return statusMap[text] }, filters: [{ text: '开启', value: 0 }, { text: '暂停', value: 1 }, { text: '升级', value: 2 }, { text: '停止', value: 3 }], onFilter: (value, record) => record.status === value },
    { title: '提示消息', dataIndex: 'msg', align: 'left', width: '15%', render: text => (<LineWrap title={text} lineClampNum={1} />) },
    { title: '开始时间', dataIndex: 'startTime', align: 'center', render: text => this.dateString(text) },
    { title: '结束时间', dataIndex: 'endTime', align: 'center', render: text => this.dateString(text) },
    { title: '功能简介', dataIndex: 'synopsis', align: 'left', width: '20%', render: text => (<LineWrap title={text} lineClampNum={1} />) },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => (
        <span>
          <a><Tooltip title='修改'><EditOutlined style={{ marginRight: 5 }} onClick={this.showModal(true, record)} /></Tooltip></a>
          <Popconfirm title='Sure to delete?' onConfirm={this.handleDel(record.moduleId)}>
            <a><Tooltip title='删除'><DeleteOutlined style={{ color: 'red', marginRight: 5 }} /></Tooltip></a>
          </Popconfirm>
          <a onClick={() => { this.copyRecordToClipboard(record) }}>
            <Tooltip title='复制'><CopyOutlined style={{ color: '#1890ff' }} /></Tooltip>
          </a>
        </span>)
    }
  ].map(item => {
    // item.width = 100
    item.ellipsis = true
    return item
  })

  pagination = { pageSizeOptions: ['200', '500', '1000'], showSizeChanger: true, defaultPageSize: 200, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }
  state = { visible: false, isUpdate: false, value: {}, searchResult: [], searchDone: false }

  showModal = (isUpdate, record) => () => {
    if (record == null) record = { moduleId: 0, status: 0, startTime: 0, endTime: 0 }

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.startTime = moment.unix(v.startTime)
      v.endTime = moment.unix(v.endTime)
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  hideModal = () => {
    this.setState({ visible: false })
  }

  dateString (timestamp) {
    if (timestamp === 0) {
      return '-'
    }
    return moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss')
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  onFinish = values => {
    const { dispatch, model: { list } } = this.props
    const { isUpdate } = this.state

    if (!isUpdate && Array.isArray(list)) {
      for (let i = 0; i < list.length; i++) {
        if (list[i].moduleId.toString() === values.moduleId) {
          message.error('模块ID已存在，添加失败!')
          return
        }
      }
    }

    // values.platform = values.platform.join()
    // values.channel = values.channel.join()
    values.startTime = values.startTime === null ? 0 : values.startTime.unix()
    values.endTime = values.endTime === null ? 0 : values.endTime.unix()
    const url = isUpdate ? `${namespace}/updateItem` : `${namespace}/addItem`
    dispatch({
      type: url,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { moduleId: key }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  handleSearch = (selectedKeys, confirm) => () => {
    confirm()
    this.setState({ searchText: selectedKeys[0] })
  }

  handleReset = clearFilters => () => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  // 复制当前行到粘贴板
  copyRecordToClipboard = (form) => {
    let tmpRecord = form
    copyToClip(JSON.stringify(tmpRecord))
  }
  // 从剪切板获取内容，并复制到表单
  parseClipboardToFormRef = (fromRef) => {
    if (navigator.clipboard == null || navigator.clipboard.readText == null) {
      message.warn('浏览器不支持,请使用谷歌浏览器')
      return
    }
    navigator.clipboard.readText().then(text => {
      let obj = JSON.parse(text)
      if (!obj.hasOwnProperty('moduleId')) {
        console.log('Object.keys(obj)=', Object.keys(obj))
        message.warn('剪切板内容解析失败~')
        return
      }

      if (this.formRef) {
        this.formRef.resetFields()
        obj.startTime = moment.unix(obj.startTime)
        obj.endTime = moment.unix(obj.endTime)
        this.formRef.setFieldsValue(obj)
      }
      message.success('自动填写完成~')
    })
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`
    })
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list } } = this.props
    const { searchResult, searchDone, visible, title, isUpdate } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.showModal(false)}>添加</Button>
            <font style={{ marginLeft: 10 }} color='red'>状态：0为正常开启，1暂停，2升级，3停止，目前处理逻辑除开启状态外都为关闭</font>
            <Divider />
            <Table dataSource={searchDone ? searchResult : list} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit} footer={[
          <Button key='paste' disabled={isUpdate} type='dashed' onClick={() => { this.parseClipboardToFormRef(this.formRef) }} >粘贴</Button>,
          <Button key='cancel' onClick={this.hideModal}>取消</Button>,
          <Button key='submit' type='primary' onClick={this.handleSubmit}>确定</Button>
        ]}>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem label='模块ID' name='moduleId' rules={[{ required: true }]}>
              <Input readOnly={isUpdate} />
            </FormItem>
            <FormItem label='模块名' name='moduleName' rules={[{ required: true }]}>
              <Input />
            </FormItem>
            <FormItem label='状态' name='status' rules={[{ required: true }]}>
              <Select>
                {statusOption}
              </Select>
            </FormItem>
            <FormItem label='提示消息' name='msg'>
              <Input />
            </FormItem>
            <FormItem label='开始时间' name='startTime'>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='结束时间' name='endTime'>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='功能简介' name='synopsis' rules={[{ required: true }]}>
              <TextArea />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default ModuleSwitch
