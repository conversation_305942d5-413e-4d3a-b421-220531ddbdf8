/* eslint-disable no-template-curly-in-string */
import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Row, Typography, Col, Table, Space, Divider, DatePicker, Select, Button } from 'antd'
import { onExportExcel } from '@/utils/common'
import { statTypeOptions, valueFormater, outRateFormaterV2, extOutFormater, optionsFormater, ballTypeOptions, totalOutFormater, outRateFormater, totalOffsetFormater } from '../statCommon' // extOutFormater, sumIncome,
import { genColumnTooltip } from '@/components/SimpleComponents'
import moment from 'moment'

const { Text } = Typography
const namespace = 'cupidStat'

@connect(({ cupidStat }) => ({
  model: cupidStat
}))

class DataMonitor extends Component {
  state = {
    timeRange: [moment().subtract(7, 'day'), moment()],
    selectStatType: 1000,
    selectBallType: 0
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  componentDidMount = () => {
    const { originID } = this.props
    this.getMonitorData(originID)
  }

  // 查询数据
  getMonitorData = () => {
    const { timeRange, selectStatType, selectBallType } = this.state
    const { originID } = this.props
    const [ t1, t2 ] = timeRange

    this.callModel('getMonitorData', {
      params: {
        start: t1.format('YYYYMMDD'),
        end: t2.format('YYYYMMDD'),
        ballType: selectBallType,
        statType: selectStatType,
        originId: originID
      }
    })
  }

  avgFormater = (a, b) => {
    if (b === 0) {
      return 0
    }
    return Number(a / b).toFixed(0)
  }

  render () {
    const { timeRange, selectStatType, selectBallType } = this.state
    const { monitorData } = this.props.model

    const columns = [
      { title: '日期', dataIndex: 'date', tooltip: '' },
      { title: '奖池名称', dataIndex: 'ballType', render: (v, r) => { return optionsFormater(ballTypeOptions, v) } },
      // { title: '渠道类型', dataIndex: '' },
      // { title: '奖池状态', dataIndex: '' },
      { title: '总支出', dataIndex: '_1', render: (v, r) => { return totalOutFormater(r) }, ...genColumnTooltip('礼物支出+装扮碎片流水') },
      { title: '人均支出', dataIndex: '_2', align: 'center', render: (_, r) => { return this.avgFormater(r.totalIn, r.totalCount) }, ...genColumnTooltip('模拟收入 / 付费用户数') },
      { title: '礼物支出', dataIndex: 'totalOut', render: (v, r) => { return valueFormater(v) }, ...genColumnTooltip('只含抽出奖品，不含装扮碎片流水') },
      { title: '模拟收入', dataIndex: 'totalIn', render: (v) => { return valueFormater(v) } },
      { title: '付费用户数', dataIndex: 'totalCount', render: (v) => { return v } },
      { title: '发放占比', dataIndex: '_3', render: (v, r) => { return outRateFormater(r) } },  
      { title: '粗发放占比', dataIndex: '_4', render: (v, r) => { return outRateFormaterV2(r) } },  
      { title: '装扮碎片流水', dataIndex: 'frags', render: (v, r) => { return valueFormater(v) } },
      { title: '粗偏移', dataIndex: 'totalOffset', render: (v) => { return valueFormater(v) }, ...genColumnTooltip('模拟收入-礼物支出') },
      { title: '总偏移', dataIndex: '_5', render: (v, r) => { return totalOffsetFormater(r) }, ...genColumnTooltip('模拟收入-礼物支出-装扮碎片流水') },
      // { title: '附加池流入比例', dataIndex: 'flowRate', render: (v) => { return rateForamter(v) } },
      { title: '常规产出', dataIndex: 'normalOut', render: (v) => { return valueFormater(v) } },
      { title: '常规产出发放占比', dataIndex: '_6', render: (v, r) => { return `${Number(r.normalOut * 100.0 / r.totalIn).toFixed(2)}%` } }, // 常规产出/用户总投入
      { title: '乐园祝福产出', dataIndex: 'blessOut', render: (v) => { return valueFormater(v) } },
      { title: '乐园祝福发放占比', dataIndex: '_7', render: (v, r) => { return `${Number(r.blessOut * 100.0 / r.totalIn).toFixed(2)}%` } }, // 乐园祝福/用户总投入
      { title: '狂欢时刻产出', dataIndex: 'crazyOut', render: (v) => { return valueFormater(v) } },
      { title: '狂欢时刻发放占比', dataIndex: '_8', render: (v, r) => { return `${Number(r.crazyOut * 100.0 / r.totalIn).toFixed(2)}%` } }, // 狂欢时刻/用户总投入
      { title: '附加池流入金额', dataIndex: '_9', render: (v, r) => { return extOutFormater(r) } } // 附加池流入金额 = 当天玩法流水（totalIn） * rate/base
      // { title: '附加池产出金额', dataIndex: '_2', render: (v, r) => { return Number((r.blessOut + r.crazyOut).toFixed(0)).toLocaleString() } }, // 附加池流入 = 狂欢产出 + 祝福产出
      // { title: '累计金额', dataIndex: '_3', render: (v, r) => { return sumIncome(r) } } // 累计金额 = 附加池流入 - 附加池产出
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <Card>
        <Row>
          <Col span={24} style={{ marginBottom: '1em' }}>
            {/* 筛选项 */}
            <Space>
              <Text>时间范围:</Text>
              <DatePicker.RangePicker format='YYYY-MM-DD' value={timeRange} onChange={v => { this.setState({ timeRange: v }) }} />
              <Divider type='vertical' />

              <Text>奖池名称：</Text>
              <Select style={{ width: '7em' }} options={ballTypeOptions} value={selectBallType} onChange={(v) => this.setState({ selectBallType: v })} />

              <Text>渠道：</Text>
              <Select style={{ width: '5em' }} options={statTypeOptions} value={selectStatType} onChange={(v) => this.setState({ selectStatType: v })} />
              <Divider type='vertical' />

              <Button type='primary' onClick={() => this.getMonitorData()}>查询</Button>
              <Button disabled={monitorData.length === 0}
                onClick={() => { onExportExcel(columns, monitorData, '丘比特-数据统计-数据监控.xlsx') }}>导出</Button>
            </Space>
          </Col>
          <Col span={24}>
            <Table scroll={{ x: 'max-content' }} columns={columns} dataSource={monitorData} />
          </Col>
        </Row>
      </Card>
    )
  }
}

export default DataMonitor
