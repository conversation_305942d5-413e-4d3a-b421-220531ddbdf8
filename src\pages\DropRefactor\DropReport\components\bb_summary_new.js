import React, { Component } from 'react'
import { Card, Table, DatePicker, Row, Col, Button, Select, Form, Tooltip } from 'antd'
import { connect } from 'dva'
import moment from 'moment'
import { CSVLink } from 'react-csv'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { GenTip, totalOutFormater, outRateFormater, totalOffsetFormater } from './common'

const namespace = 'dropReport' // model 的 namespace

const defaultDropType = 0
const defaultStatType = 0

// 统计渠道: 汇总，PC，Yo语音， 追玩，手Y
const statTypeOptions = [
  // { label: '汇总', value: 1000 },
  { label: 'PC', value: 0 }
  // { label: '手Y', value: 1 },
  // { label: '追玩', value: 2 },
  // { label: 'Yomi', value: 3 },
  // { label: 'Web', value: 4 }
  // { label: 'h5', value: 7 }
]

// 玩法
const dropTypeOptions = [
  // { label: '汇总', value: 1000 },
  { label: '抢空投', value: 0 }
  // { label: '抢物资', value: 1 }
  // { label: '幸运小狗', value: 2 },
  // { label: '物资大战', value: 3 }
]

@connect(({ dropReport }) => ({ // model 的 namespace
  model: dropReport // model 的 namespace
}))

class DropReportBBSummaryComponentNew extends Component {
  componentDidMount () {
    this.queryData()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  queryData () {
    let begin = moment().add(-7, 'days')
    let end = moment()
    const params = { appID: 36, dropType: defaultDropType, statType: defaultStatType, begin: begin.format('YYYYMMDD'), end: end.format('YYYYMMDD') }
    this.formRef.setFieldsValue({ range: [begin, end], dropType: defaultDropType, statType: defaultStatType })
    this.callModel('getDailySummaryBb', { params })
  }

  // 查询数据
  onFinish = values => {
    let begin = values.range[0].format('YYYYMMDD')
    let end = values.range[1].format('YYYYMMDD')
    const dropType = parseInt(values.dropType)
    const statType = parseInt(values.statType)
    let params = { appID: 36, dropType: dropType, statType: statType, begin: begin, end: end }
    this.callModel('getDailySummaryBb', { params })
  }

  // 表头显示提示语
  genColumnTooltip = (title) => {
    return {
      filterDropdown: (<span />),
      filterIcon: (
        <Tooltip placement='top' title={title}>
          <QuestionCircleOutlined style={{ fontSize: '16px' }} />
        </Tooltip>
      )
    }
  }

  numberFormater = (v) => {
    if (typeof (v) === 'number') {
      return v.toLocaleString()
    }
    return '0'
  }

  // 需要修改
  columns = [
    { title: '日期', dataIndex: 'id' },
    { title: '普通收入', dataIndex: 'normalIn', ...this.genColumnTooltip('普通空投收入'), render: v => this.numberFormater(v) },
    { title: '普通支出', dataIndex: 'normalOut', ...this.genColumnTooltip('普通支出 ==  普通道具池支出+普通进度支出'), render: v => this.numberFormater(v) },
    { title: '普通人数', dataIndex: 'normalCount', render: v => this.numberFormater(v) },
    { title: '疯狂收入', dataIndex: 'crazyIn', render: v => this.numberFormater(v) },
    { title: '疯狂支出', dataIndex: 'crazyOut', ...this.genColumnTooltip('疯狂支出 ==  疯狂道具池支出+疯狂进度支出'), render: v => this.numberFormater(v) },
    { title: '疯狂人数', dataIndex: 'crazyCount', render: v => this.numberFormater(v) },
    { title: '收入', dataIndex: 'totalIn', render: v => this.numberFormater(v) },
    { title: '支出', dataIndex: 'totalOut', ...this.genColumnTooltip('空投支出'), render: (v, r) => totalOutFormater(r) }, // (不包含物资支出)
    { title: '发放占比', dataIndex: '_totalIn', render: (v, r) => outRateFormater(r) },
    { title: '装扮碎片流水', dataIndex: 'frags', render: v => this.numberFormater(v) },
    { title: '抽取道具人数', dataIndex: 'totalCount' },
    { title: '抽取道具偏移', dataIndex: 'normalOffset', ...this.genColumnTooltip('空投偏移（空投收入-空投支出）'), render: v => this.numberFormater(v) },
    { title: '龙宫支出', dataIndex: 'dragonOut', ...this.genColumnTooltip('龙宫支出'), render: v => this.numberFormater(v) }, // （不包含物资支出）
    { title: '龙宫开启次数', dataIndex: 'dragonTimes', render: v => v ? v?.toLocaleString() : 0 },
    { title: '补给箱支出', dataIndex: 'compensateOut', ...this.genColumnTooltip('补给箱支出'), render: v => v ? v.toLocaleString() : 0 }, // （不包含物资支出）
    // { title: '发放物资支出', dataIndex: 'flowOut', ...this.genColumnTooltip('物资支出'), render: v => this.numberFormater(v) },
    { title: '总偏移', dataIndex: 'totalOffset', render: (v, r) => totalOffsetFormater(r) }
  ].map(item => {
    item.align = 'center'
    return item
  })

  export = () => {
    const { model: { dailysummarybb } } = this.props
    const column = this.columns
    let list = []
    dailysummarybb.forEach(v => {
      list.push(this.genExportItem(column, v))
    })

    return list
  }

  genExportItem = (column, item) => {
    let result = {}
    column.forEach(v => {
      const { title, dataIndex, render } = v
      result[title] = render ? render(item[dataIndex], item) : item[dataIndex]
    })
    return result
  }
  onDropTypeChange = (v) => {
    this.setState({ dropType: v })
    this.formRef.submit()
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { dailysummarybb } } = this.props

    return (
      <Card>
        <Form ref={form => { this.formRef = form }} onFinish={this.onFinish}>
          <Row gutter={6}>
            <Col>
              <Tooltip title='时间范围'>
                <Form.Item name='range'>
                  <DatePicker.RangePicker />
                </Form.Item>
              </Tooltip>
            </Col>
            <Col>
              <Tooltip title='玩法'>
                <Form.Item name='dropType' >
                  <Select defaultValue={0} options={dropTypeOptions} style={{ width: '11em' }} onChange={(v) => this.onDropTypeChange(v)} />
                </Form.Item>
              </Tooltip>
            </Col>
            <Col>
              <Tooltip title='渠道'>
                <Form.Item name='statType' >
                  <Select defaultValue={0} options={statTypeOptions} style={{ width: '10em' }} />
                </Form.Item>
              </Tooltip>
            </Col>
            <Col>
              <Button htmlType='submit' type='primary'>查询</Button>
            </Col>
            <Col>
              <Button><CSVLink data={this.export()}>导出</CSVLink></Button>
            </Col>
          </Row>
        </Form>

        <GenTip />
        <Table
          scroll={{ x: 'max-content' }}
          rowKey={(record, index) => index}
          bordered dataSource={dailysummarybb}
          columns={this.columns} pagination={false} /> {/* 显示的列表 */}
      </Card>
    )
  }
}

export default DropReportBBSummaryComponentNew
