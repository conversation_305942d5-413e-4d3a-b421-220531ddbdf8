import {
  upsetItem,
  getConfigList,
  removeConfig,
  auditUpdate,
  getAuditList,
  getHistoryList
} from './api'
import { message } from 'antd'

export default {
  namespace: 'SynthesisConfig',

  state: {
    author: 0,
    configList: [
      // { 'index': 1, 'targetPropsID': 12, 'targetPropsName': '爱心', 'onceTargetCount': 100, 'consumeGiftType': [1], 'allowPropsType': [1, 2], 'consumeGiftList': [{ 'name': '魔法棒', 'id': 10001, 'count': 100 }, { 'name': '藏宝图', 'id': 10002, 'count': 1000 }], 'gift1': 1, 'gift2': 100, 'labelName': '活动', 'labelColor': '#11511', 'weight': 45, 'startTime': 1607221207, 'endTime': 1609430400, 'progress': [{ 'state': 0, 'passport': 'dw_zhangyuting2' }] }
    ],
    auditList: [
      // { 'index': 1, 'targetPropsID': 12, 'targetPropsName': '爱心', 'onceTargetCount': 100, 'consumeGiftType': [1], 'allowPropsType': [1, 2], 'gift1': 1, 'gift2': 100, 'labelName': '活动', 'labelColor': '#11511', 'weight': 45, 'startTime': 1607221207, 'endTime': 1609430400, 'progress': [{ 'state': 0, 'passport': 'dw_zhangyuting2' }] }
    ],
    historyList: [
      // { 'index': 1, 'targetPropsID': 12, 'targetPropsName': '爱心', 'onceTargetCount': 100, 'consumeGiftType': [1], 'allowPropsType': [1, 2], 'gift1': 1, 'gift2': 100, 'labelName': '活动', 'labelColor': '#11511', 'weight': 45, 'startTime': 1607221207, 'endTime': 1609430400, 'progress': [{ 'state': 0, 'passport': 'dw_zhangyuting2' }] }
    ]
  },

  reducers: {
    updateConfigList (state, { payload, allow }) {
      for (let i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
        for (let j = 0; j < payload[i].consumeGiftList.length; j++) {
          if (payload[i].consumeGiftList[j].id === 10001) {
            payload[i].giftA = payload[i].consumeGiftList[j].count
            continue
          }
          if (payload[i].consumeGiftList[j].id === 10002) {
            payload[i].giftB = payload[i].consumeGiftList[j].count
          }
          if (payload[i].consumeGiftList[j].id === 10003) {
            payload[i].giftC = payload[i].consumeGiftList[j].count
          }
          if (payload[i].consumeGiftList[j].id === 10004) {
            payload[i].giftD = payload[i].consumeGiftList[j].count
          }
          if (payload[i].consumeGiftList[j].id === 10005) {
            payload[i].giftE = payload[i].consumeGiftList[j].count
          }
        }
      }
      return {
        ...state,
        configList: payload
      }
    },

    updateAuditList (state, { payload, allow }) {
      for (let i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
        for (let j = 0; j < payload[i].consumeGiftList.length; j++) {
          if (payload[i].consumeGiftList[j].id === 10001) {
            payload[i].giftA = payload[i].consumeGiftList[j].count
            continue
          }
          if (payload[i].consumeGiftList[j].id === 10002) {
            payload[i].giftB = payload[i].consumeGiftList[j].count
          }
          if (payload[i].consumeGiftList[j].id === 10003) {
            payload[i].giftC = payload[i].consumeGiftList[j].count
          }
          if (payload[i].consumeGiftList[j].id === 10004) {
            payload[i].giftD = payload[i].consumeGiftList[j].count
          }
          if (payload[i].consumeGiftList[j].id === 10005) {
            payload[i].giftE = payload[i].consumeGiftList[j].count
          }
        }
      }
      return {
        ...state,
        auditList: payload
      }
    },

    updateHistoryList (state, { payload }) {
      for (let i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
        for (let j = 0; j < payload[i].consumeGiftList.length; j++) {
          if (payload[i].consumeGiftList[j].id === 10001) {
            payload[i].giftA = payload[i].consumeGiftList[j].count
            continue
          }
          if (payload[i].consumeGiftList[j].id === 10002) {
            payload[i].giftB = payload[i].consumeGiftList[j].count
          }
          if (payload[i].consumeGiftList[j].id === 10003) {
            payload[i].giftC = payload[i].consumeGiftList[j].count
          }
          if (payload[i].consumeGiftList[j].id === 10004) {
            payload[i].giftD = payload[i].consumeGiftList[j].count
          }
          if (payload[i].consumeGiftList[j].id === 10005) {
            payload[i].giftE = payload[i].consumeGiftList[j].count
          }
        }
      }
      return {
        ...state,
        historyList: payload
      }
    }
  },

  effects: {
    * getConfigList ({ payload }, { call, put }) {
      try {
        const { data: { list, allow } } = yield call(getConfigList)

        yield put({
          type: 'updateConfigList',
          payload: Array.isArray(list) ? list : [],
          allow: allow
        })
      } catch (e) {
        message.error('err:' + e)
      }
    },

    * upsetItem ({ payload }, { call, put }) {
      try {
        let { data: { status, msg } } = yield call(upsetItem, payload)
        if (status === 0) {
          if (msg === undefined || msg.length === 0) {
            message.success('add success')
          } else {
            message.success(msg)
          }
        } else {
          message.error('failed' + msg, 10)
        }
      } catch (e) {
        message.error('err:' + e, 10)
      }
    },

    * approvalItem ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(auditUpdate, payload)
        if (status === 0) {
          message.success('add success')
          yield put({
            type: 'getAuditList'
          })
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('err:' + e)
      }
    },

    * removeItem ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(removeConfig, payload)
        if (status === 0) {
          message.success('remove success')
        } else {
          message.error('failed ' + msg)
        }
      } catch (e) {
        message.error('err:' + e)
      }
    },

    * getHistoryList ({ payload }, { call, put }) {
      try {
        const { data: { list } } = yield call(getHistoryList, payload)

        yield put({
          type: 'updateHistoryList',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        message.error('err:' + e)
      }
    },

    * getAuditList ({ payload }, { call, put }) {
      try {
        const { data: { list, allow } } = yield call(getAuditList)

        yield put({
          type: 'updateAuditList',
          payload: Array.isArray(list) ? list : [],
          allow: allow
        })
      } catch (e) {
        message.error('err:' + e)
      }
    }

  }
}
