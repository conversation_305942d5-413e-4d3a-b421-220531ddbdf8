import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card } from 'antd'
import { connect } from 'dva'
import DropQuerySignal from './components/signal'
import DropQueryDrop from './components/drop'
import QueryUserSummary from './components/summary'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import { isVoiceRoomPath } from '../dropCommon'
import { initGlobalBossConfig } from '../globalConfig'

const namespace = 'dropQuery'

@connect(({ dropQuery }) => ({
  model: dropQuery
}))

class DropRefactoryQuery extends Component {
  state = {
    groupType: isVoiceRoomPath(this.props.route.path) ? 'vr' : 'jy'
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  componentDidMount = () => {
    initGlobalBossConfig(this.changeState, this.state.groupType)
  }

  render () {
    const { route } = this.props
    const { groupType } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs id='dq' type='card' defaultActiveKey='1' >

            <TabPane tab='信号弹' key='1'>
              <DropQuerySignal />
            </TabPane>

            <TabPane tab='空投记录' key='2'>
              <DropQueryDrop groupType={groupType} />
            </TabPane>

            <TabPane tab='用户查询' key='3'>
              <QueryUserSummary />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default DropRefactoryQuery // 保证唯一
