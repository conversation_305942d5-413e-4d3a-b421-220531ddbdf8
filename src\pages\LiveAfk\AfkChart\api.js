import request from '@/utils/request'

/**
 * @param options
 *  - begTime: 开始时间，单位：秒
 *  - endTime: 结束时间，单位：秒
 * @returns
 * {
 *   status: 0,
 *   msg: 'ok',
 *   data: [
 *     {
 *       date:    1632793470     // 日期, 单位： 秒
 *       liveCnt: 493,           // 当前交友开播总主持数
 *       canRecommendCnt: 295,   // 所有符合手Y推荐的主播
 *       liveHangUpCnt: 120      // 命中挂播主持数
 *     }
 *   ]
 * }
 */
export function getLists (options) {
  let url = '/live_afk/chat_data?begTime=' + parseInt(options.begTime) + '&endTime=' + parseInt(options.endTime)
  return request(url)
}
