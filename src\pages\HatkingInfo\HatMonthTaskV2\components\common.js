import React from 'react'

export const idxDesc = { 1: '一阶段', 2: '二阶段', 3: '三阶段', 4: '四阶段', 5: '五阶段', 6: '六阶段' }

export function tingSealStr (start, end) {
  return '[' + yuan<PERSON><PERSON><PERSON><PERSON>(start) + ',' + (end === -1 ? '∞' : yuan<PERSON><PERSON><PERSON><PERSON>(end)) + ')'
}

export function yuanToWan (v) {
  return Math.floor(v / 10000 * 10) / 10
}

export const prizeRuleMap = { 0: '-', 1: '按系统结算', 2: '固定值' }
export const prizeRuleList = [
  { label: '按系统结算', value: 1 },
  { label: '固定值', value: 2 }
]
export const prizeRuleNone = 0 // 初始值
export const prizeRuleSystem = 1 // 按系统结算
export const prizeRuleDown = 2 // 下调奖励-固定值

export const approvaMap = { 0: '未送审', 1: '待审核', 2: '审批通过', 3: '审批不通过', 4: '一审中', 5: '二审中' }

export const approvalStatusList = [
  { label: '未送审', value: 0 },
  { label: '待审核', value: 1 },
  { label: '审批通过', value: 2 },
  { label: '审批不通过', value: 3 },
  { label: '一审中', value: 4 },
  { label: '二审中', value: 5 }
]

export const reviewMap = { 0: '-', 1: '待复核', 2: '已复核' }
export const reviewStatusList = [
  { label: '待复核', value: 1 },
  { label: '已复核', value: 2 }
]

export const taskStepFormater = (step) => {
  const { tingSealBegin, tingSealEnd } = step
  return `盖章${tingSealStr(tingSealBegin, tingSealEnd)}万元`
}

// 任务阶段信息渲染
export const sprintTaskStepFormater = (r) => {
  if (!r.taskStep) {
    return '?'
  }
  return r.taskStep.map(item => {
    return <div>{taskStepFormater(item)}</div>
  })
}
