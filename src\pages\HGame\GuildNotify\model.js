import * as api from './api'
import { message } from 'antd'

export default {
  namespace: 'guildNotify',

  state: {
    list: []
  },

  reducers: {
    displayList (state, { payload: data }) {
      if (!Array.isArray(data)) {
        console.error('unexpect data type: data=', data)
        return
      }
      return {
        ...state,
        list: data
      }
    }
  },

  effects: {
    * getGuildNotifyList ({ payload }, { call, put }) {
      let { data: { data, status } } = yield call(api.getGuildNotifyList, payload)
      if (status !== 0) {
        message.error({ content: '获取失败，请检查控制台' })
        return
      }

      data = Array.isArray(data) ? data : []
      if (data !== []) {
        for (let i = 0; i < data.length; i++) {
          data[i].idx = i + 1
        }
      }
      yield put({
        type: 'displayList',
        payload: data
      })
    },

    * addGuildNotify ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(api.addGuildNotify, payload)
      if (status === 0) {
        message.success('addGuildNotify success')
        yield put({
          type: 'getGuildNotifyList'
        })
      } else {
        message.error('addGuildNotify failed')
        message.error(msg)
      }
    },

    * updateGuildNotify ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(api.updateGuildNotify, payload)
      if (status === 0) {
        message.success('updateGuildNotify success')
        yield put({
          type: 'getGuildNotifyList'
        })
      } else {
        message.error('updateGuildNotify failed')
        message.error(msg)
      }
    },

    * deleteGuildNotify ({ payload }, { call, put }) {
      const { data: { status } } = yield call(api.deleteGuildNotify, payload)
      if (status === 0) {
        message.success('deleteGuildNotify success')
        yield put({
          type: 'getGuildNotifyList'
        })
      } else {
        message.error('deleteGuildNotify failed')
      }
    }
  }
}
