import { getUserInfo, getSubscribeMap, getOutURL } from '@/services/api'
import { message } from 'antd'
import { updateYyMessageImg } from '@/utils/upload'
import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const queryMenuAuth = genGetRequireTemplate('/boss_menu/query_view_auth')
const applyMenuAuth = genGetRequireTemplate('/boss_menu/apply_view_auth')
const queryUnAuthMenu = genGetRequireTemplate('/admin_menu/QueryUnAuthMenu')
const bossAuthApply = genUpdateTemplate('/admin_menu/AuthApplyV2')

export default {
  namespace: 'global',

  state: {
    currentUser: {},
    collapsed: false,
    navTheme: 'dark', // theme for nav menu
    primaryColor: '#1890FF', // primary color of ant design
    layout: 'sidemenu', // nav menu position: sidemenu or topmenu
    contentWidth: 'Fluid', // layout of content: Fluid or Fixed, only works when layout is topmenu
    fixedHeader: false, // sticky header
    autoHideHeader: false, // auto hide header
    fixSiderbar: true, // sticky siderbar
    subscribeMenus: {}, // subscribe menus
    OutURL: {}
  },

  effects: {
    queryMenuAuth,
    applyMenuAuth,
    queryUnAuthMenu,
    bossAuthApply,
    * fetchCurrent ({ payload }, { call, put }) {
      const { uid: uids, username } = payload
      const response = yield call(getUserInfo, { uids })
      const { data: { infoList: [{ nick: name, avatarUrl: avatar }] } } = response
      yield put({
        type: 'saveCurrentUser',
        payload: {
          name,
          avatar: String(avatar).replace('http:', ''),
          username
        }
      })
    },

    * fetchSubscribeMenus (state, { call, put }) {
      const { data: { list } } = yield call(getSubscribeMap)

      yield put({
        type: 'saveSubscribeMenus',
        payload: list instanceof Object ? list : {}
      })
    },

    * fetchOutURL (state, { call, put }) {
      const { data: { list } } = yield call(getOutURL)

      yield put({
        type: 'saveOutURL',
        payload: list !== undefined ? list : {}
      })
    },

    // 上传可以在yy消息中展示的图片
    * uploadYyMessageImg ({ file, callback }, { call, put }) {
      if (!callback) {
        message.warn('callback is required')
        return
      }
      const url = yield call(updateYyMessageImg, file)
      callback(url, 0, '')
    }
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },
    changeLayoutCollapsed (state, { payload }) {
      return {
        ...state,
        collapsed: payload
      }
    },
    saveCurrentUser (state, action) {
      return {
        ...state,
        currentUser: action.payload || {}
      }
    },

    saveSubscribeMenus (state, { payload }) {
      return {
        ...state,
        subscribeMenus: payload
      }
    },

    saveOutURL (state, { payload }) {
      return {
        ...state,
        OutURL: payload
      }
    }
    // saveNotices (state, { payload }) {
    //   return {
    //     ...state,
    //     notices: payload
    //   }
    // },
    // saveClearedNotices (state, { payload }) {
    //   return {
    //     ...state,
    //     notices: state.notices.filter(item => item.type !== payload)
    //   }
    // }
  },

  subscriptions: {
    // setup ({ history }) {
    //   // Subscribe history(url) change, trigger `load` action if pathname is `/`
    //   return history.listen(({ pathname, search }) => {
    //     if (typeof window.ga !== 'undefined') {
    //       window.ga('send', 'pageview', pathname + search)
    //     }
    //   })
    // }
  }
}
