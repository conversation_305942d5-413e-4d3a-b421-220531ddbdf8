/* eslint-disable eqeqeq */
import React, { Component } from 'react'
import { connect } from 'dva'
import { timeFormater } from '@/utils/common'
import { Modal, Form, Input, Row, Col, Button, Table, Tooltip, message, Switch, AutoComplete, Select } from 'antd'
import { EyeOutlined, EyeInvisibleOutlined, CloseCircleOutlined, CheckCircleOutlined, SearchOutlined, AppstoreAddOutlined } from '@ant-design/icons'

const namespace = 'componentVersionCfg'

@connect(({ componentVersionCfg }) => ({
  model: componentVersionCfg
}))

class ComponentCfg extends Component {
  state = {
    modelVisable: false,
    opTypeIsAdd: true,
    selectKeyword: '',
    searchTKey: 'jy',
    searchCKey: '',
    searchOnlyValid: 1,
    searchOnlyVisble: 4,
    searchEachPage: 200
  }
  componentDidMount = () => {
    this.getComponentListByFilter()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 修改、删除操作 html代码
  operationHtml = (record) => {
    return (
      <>
        <a style={{ color: '#1890ff', fontSize: '1.2em', marginRight: '1em' }}
          onClick={() => {
            this.setState({ modelVisable: true, opTypeIsAdd: false })
            setTimeout(() => {
              this.formRef.setFieldsValue(record)
            }, 500)
          }
          }>
          修改
        </a>
      </>
    )
  }
  // 提交新增或修改业务请求
  submitAddOrUpdateRequire = (v) => {
    const { opTypeIsAdd } = this.state
    if (opTypeIsAdd) {
      v.opType = 'ADD'
      v.id = 0
    } else {
      v.opType = 'UPDATE'
    }
    if (!v.width) v.width = 0
    if (!v.height) v.height = 0
    v.opUid = 0
    v.timestamp = 0
    this.callModel('addOrUpdateComponent', {
      params: v,
      cbFunc: (ok) => {
        if (ok) {
          message.success(opTypeIsAdd ? '新增成功' : '更新成功')
          this.callModel('getAllSimpleComponent')
          this.formRef.resetFields()
          this.getComponentListByFilter()
          this.setState({ modelVisable: false })
        } else {
          message.warn((opTypeIsAdd ? '新增' : '更新') + '失败，请稍后重试...')
        }
      } })
  }

  // 创建tooltip
  createTooltip = (v, width = 10) => {
    return !v ? '-' : <Tooltip title={v}>
      <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', width: `${width}em` }}>{v}</div>
    </Tooltip>
  }
  // 将业务列表整理成自动完成下拉框所需的列表
  autoCompleteForamter = (list, keyword) => {
    if (!list) return []
    let newList = []
    for (let i = 0; i < list.length; i++) {
      newList[i] = { value: list[i].key, label: `${list[i].key} (${list[i].alias})` }
    }
    if (typeof (keyword) == 'string' && keyword != '') {
      newList = newList.filter((v) => { return v.value.indexOf(keyword) >= 0 })
    }
    return newList
  }
  // 根据条件筛选组件列表
  getComponentListByFilter = () => {
    const { searchEachPage, searchTKey, searchCKey, searchOnlyValid, searchOnlyVisble } = this.state
    if (searchCKey.length == 1) {
      message.warn('组件关键字最短两个字母~')
      return
    }
    this.callModel('selectComponentCfgList', {
      params: {
        cKey: searchCKey,
        appKey: searchTKey,
        validStatus: searchOnlyValid,
        visibleStatus: searchOnlyVisble,
        limited: searchEachPage
      }
    })
  }

  render () {
    const { componentCfgList, componentTypeList } = this.props.model
    const { modelVisable, opTypeIsAdd, selectKeyword, searchTKey } = this.state
    const { TextArea } = Input
    const { Option } = Select

    const columns = [
      { title: '组件名称', dataIndex: 'key', align: 'center' },
      { title: '组件别名', dataIndex: 'alias', align: 'center' },
      { title: '所属业务', dataIndex: 'appKey', align: 'center', render: (v, r) => { return <Tooltip title={r.appAlias}>{v}</Tooltip> } },
      { title: '组件URL', dataIndex: 'url', align: 'center', width: '15em', render: (v, r) => { return this.createTooltip(v, 14) } },
      { title: '版本信息链接', dataIndex: 'versionUrl', align: 'center', width: '15em', render: (v, r) => { return this.createTooltip(v, 14) } },
      { title: '高度', dataIndex: 'height', align: 'center' },
      { title: '宽度', dataIndex: 'width', align: 'center' },
      { title: '所属业务可用', dataIndex: 'isAppValid', align: 'center', render: (v) => { return v ? <CheckCircleOutlined style={{ fontSize: '1.2em' }} /> : <CloseCircleOutlined style={{ fontSize: '1.2em', color: '#aeaeae5e' }} /> } },
      { title: '是否可用', dataIndex: 'isValid', align: 'center', render: (v) => { return v ? <CheckCircleOutlined style={{ fontSize: '1.2em' }} /> : <CloseCircleOutlined style={{ fontSize: '1.2em', color: '#aeaeae5e' }} /> } },
      { title: '是否可见', dataIndex: 'isVisible', align: 'center', render: (v) => { return v ? <EyeOutlined style={{ fontSize: '1.3em' }} /> : <EyeInvisibleOutlined style={{ fontSize: '1.3em', color: '#aeaeae5e' }} /> } },
      { title: '扩展信息', dataIndex: 'extend', align: 'center', width: '18em', render: (v, r) => { return this.createTooltip(v, 17) } },
      { title: '修改备注', dataIndex: 'tag', align: 'center' },
      { title: '更新人uid', dataIndex: 'opUid', align: 'center' },
      { title: '更新时间', dataIndex: 'timestamp', align: 'center', render: (v) => { return timeFormater(v) } },
      { title: '操作',
        align: 'center',
        render: (v, r) => {
          return this.operationHtml(r)
        } }
    ]

    return (
      <div>
        <Row style={{ marginBottom: '1em' }} >
          <Tooltip title='使用输入的关键字对组件key进行模糊匹配'>
            <Input style={{ width: '12em' }} placeholder='输入组件key' onChange={(v) => this.setState({ searchCKey: v.target.value })} />
          </Tooltip>
          <Tooltip title='根据业务key进行筛选 (完全匹配)'>
            <AutoComplete style={{ width: '12em', marginLeft: '1em' }} placeholder='选择指定业务'
              value={searchTKey}
              options={this.autoCompleteForamter(componentTypeList, searchTKey)}
              onChange={(v) => this.setState({ searchTKey: v })} />
          </Tooltip>
          <Tooltip title='根据组件是否可用进行筛选 (若所属业务不可用则该组件也不可用)'>
            <Select style={{ width: '10em', marginLeft: '1em' }} defaultValue={1} onChange={(v) => { this.setState({ searchOnlyValid: v }) }}>
              <Option key={4} value={4}>全部</Option>
              <Option key={1} value={1}>仅可用</Option>
              <Option key={2} value={2}>仅不可用</Option>
            </Select>
          </Tooltip>
          <Tooltip title='根据组件是否可见进行筛选'>
            <Select style={{ width: '10em', marginLeft: '1em' }} defaultValue={4} onChange={(v) => { this.setState({ searchOnlyVisble: v }) }}>
              <Option key={4} value={4}>全部</Option>
              <Option key={1} value={1}>仅可见</Option>
              <Option key={2} value={2}>仅不可见</Option>
            </Select>
          </Tooltip>
          <Tooltip title='每页显示多少行'>
            <Select style={{ width: '8em', marginLeft: '1em' }} defaultValue={200} onChange={(v) => { this.setState({ searchEachPage: v }) }}>
              <Option key={20} value={20}>20</Option>
              <Option key={50} value={50}>50</Option>
              <Option key={100} value={100}>100</Option>
              <Option key={200} value={200}>200</Option>
              <Option key={0} value={0}>不限制</Option>
            </Select>
          </Tooltip>
          <Tooltip title='查询满足条件的组件'>
            <Button type='primary' style={{ width: '6em', marginLeft: '1em' }} onClick={() => this.getComponentListByFilter()}><SearchOutlined />搜索</Button>
          </Tooltip>
          <span style={{ marginLeft: '2em', marginRight: '2em', borderLeft: '1px #d7d7d7 solid' }} />
          <Button type='primary' onClick={() => {
            this.setState({ modelVisable: true, opTypeIsAdd: true })
            setTimeout(() => { this.formRef.resetFields() }, 100)
          }} >
            <AppstoreAddOutlined />新增组件
          </Button>
        </Row>
        <Row>
          <Col span={24}>
            <Table columns={columns} dataSource={componentCfgList} pagination={{ pageSize: 50 }} size='small' scroll={{ x: 'max-content' }} />
          </Col>
        </Row>
        {/* 新增或修改 */}
        <Modal visible={modelVisable} title={opTypeIsAdd ? '新增组件' : '修改组件信息'} okText={opTypeIsAdd ? '确认新增' : '确认修改'} cancelText='取消'
          onCancel={() => { this.setState({ modelVisable: false }) }} onOk={() => { this.formRef.submit() }} >
          <Form onFinish={(v) => { this.submitAddOrUpdateRequire(v) }} ref={(v) => { this.formRef = v }}
            labelCol={{ span: 6, align: 'left' }} initialValues={{ key: '', versionUrl: '', appKey: '', alias: '', url: '', extend: '', width: '', height: '', isValid: true, isVisible: true, tag: '' }}>
            <Form.Item label='组件key' name='key' rules={[{ required: true, message: '组件key不能为空' }, { pattern: /^[a-zA-Z0-9][\w-_]{1,149}$/, message: '包含非法字符~' }]}>
              <Input placeholder='由大字母、数字,下划线或中划线组成~' disabled={!opTypeIsAdd} />
            </Form.Item>
            <Form.Item label='所属业务' name='appKey' rules={[{ required: true, message: '业务key不能为空' }, { pattern: /^[a-zA-Z0-9][\w-_]{1,149}$/, message: '不合法的业务key~' }]}>
              <AutoComplete
                options={this.autoCompleteForamter(componentTypeList, selectKeyword)}
                onChange={(v) => this.setState({ selectKeyword: v })}
              />
            </Form.Item>
            <Form.Item label='组件别名' name='alias' rules={[{ required: true }]} >
              <Input />
            </Form.Item>
            <Form.Item label='组件URL' name='url' rules={[{ required: true, message: '组件URL不能为空' }, { pattern: /^https?:\/\/.+$/, message: '不合法的url~' }]} >
              <Input />
            </Form.Item>
            <Form.Item label='版本url' name='versionUrl' rules={[{ required: false }, { pattern: /^^[\S]+$/, message: '不能包含空字符~' }]} >
              <Input placeholder='获取版本列表的链接~' />
            </Form.Item>
            <Form.Item label='高度' name='height'>
              <Input placeholder='0' />
            </Form.Item>
            <Form.Item label='宽度' name='width'>
              <Input placeholder='0' />
            </Form.Item>
            <Form.Item label='是否可用' name='isValid' valuePropName='checked' >
              <Switch checkedChildren='可用' unCheckedChildren='不可' size='default' onChange={(v) => this.formRef.setFieldsValue({ 'isValid': v })} />
            </Form.Item>
            <Form.Item label='是否可见' name='isVisible' valuePropName='checked' >
              <Switch checkedChildren='可见' unCheckedChildren='不可' size='default' onChange={(v) => this.formRef.setFieldsValue({ 'isVisible': v })} />
            </Form.Item>
            <Form.Item label='扩展信息' name='extend' >
              <TextArea />
            </Form.Item>
            <Form.Item label='修改备注' name='tag'>
              <Input />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    )
  }
}

export default ComponentCfg
