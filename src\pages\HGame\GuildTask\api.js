import request from '@/utils/request'
import { stringify } from 'qs'

// 获取公会任务发奖审批列表
export function getRewardApprovalList (params) {
  return request(`/fts_hgame/guildtask/list_reward_approval?${stringify(params)}`)
}

// 获取最终成功发奖记录列表
export function getRewardRealSendList (params) {
  return request(`/fts_hgame/guildtask/list_reward_real_send?${stringify(params)}`)
}

// 发起发奖审批
export function rewardApproval1 (params) {
  return request(`/fts_hgame/guildtask/reward_approval?tabIdx=1`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 发起发奖审批
export function rewardApproval3 (params) {
  return request(`/fts_hgame/guildtask/reward_approval?tabIdx=3`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 新增修改指定参考月份
export function upsertReferMonth (params) {
  return request(`/fts_hgame/guildtask/upsert_refer_month`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 获取指定参考月份列表
export function getReferMonthList (params) {
  return request(`/fts_hgame/guildtask/list_refer_month?${stringify(params)}`)
}

// 删除指定参考月份
export function deleteReferMonth (params) {
  return request(`/fts_hgame/guildtask/delete_refer_month?${stringify(params)}`)
}

// 获取指定参考月份列表
export function listReferPeriod (params) {
  return request(`/fts_hgame/guild_task/refer_period/list?${stringify(params)}`)
}

// 添加指定参考周期
export function addReferPeriod (params) {
  return request(`/fts_hgame/guild_task/refer_period/add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 更新指定参考周期
export function updateReferPeriod (params) {
  return request(`/fts_hgame/guild_task/refer_period/update`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 删除指定参考周期
export function deleteReferPeriod (params) {
  return request(`/fts_hgame/guild_task/refer_period/delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 获取发奖汇总列表
export function listRewardSummary (params) {
  return request(`/fts_hgame/guildtask/list_reward_summary?${stringify(params)}`)
}
