import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import RFIConfigComponent from './component/config'
import RFIConfigListComponent from './component/config_list'

const TabPane = Tabs.TabPane

class RewardFlowInfoComponent extends Component { // 默认页面组件，不需要修改
  onTabChange = key => {
    this.setState({ key }) // render tab pane
  }

  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs onChange={this.onTabChange} type='card'>
          <TabPane tab='当前配置' key='1'>
            <RFIConfigComponent {...this.state} />
          </TabPane>
          <TabPane tab='配置记录' key='2'>
            <RFIConfigListComponent {...this.state} />
          </TabPane>
        </Tabs>
      </PageHeaderWrapper>
    )
  }
}

export default RewardFlowInfoComponent // 保证唯一
