import { addWhiteList, updateRoomMgr, whiteList, roomMgrList, deleteRoomMgr, updateRoomMgrGrade, getAssessHistory } from './api'
import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'
import { message } from 'antd'

const getAssessInfo = genGetRequireTemplate('/room_manager/boss/get_assess_info')
const getSystemAssessList = genGetRequireTemplate('/room_manager/boss/get_system_assess_list')
const gradeReview = genUpdateTemplate('/room_manager/boss/grade_review')

export default {
  namespace: 'RoomMangeList',

  state: {
    roomMgrList: [],
    whitelist: [],
    assessHistory: [
      { 'month': '202405', 'ssid': 124234, 'name': 'abc' }
    ]
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },
    updateRoomMgrList (state, { payload }) {
      for (let i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        roomMgrList: payload
      }
    },

    updateWhitelist (state, { payload }) {
      console.log('', whiteList())
      for (let i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        whitelist: payload
      }
    },

    updateAssessHistory (state, { payload }) {
      for (let i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        assessHistory: payload
      }
    }
  },

  effects: {
    getAssessInfo,
    getSystemAssessList,
    gradeReview,
    * getRoomMgrList ({ payload }, { call, put }) {
      try {
        // let resp = JSON.parse(`{"status":0,"list":[{"uid":2849402207,"imid":0,"nick":"mftest8","type":1,"settlement":0,"sid":11297016,"ssid":2721503270,"startTime":1661961600000,"endTime":1693497600000,"owUID":50014069,"isPublic":false},{"uid":1180961595,"imid":0,"nick":"Rudy127Anchor","type":0,"settlement":2,"sid":1507588028,"ssid":1507588028,"startTime":1661961600000,"endTime":4102329600000,"owUID":1512025257,"isPublic":false},{"uid":1688617281,"imid":0,"nick":"端木尧哦","type":0,"settlement":2,"sid":1451420998,"ssid":2806303606,"startTime":1661961600000,"endTime":1693497600000,"owUID":1688617281,"isPublic":false},{"uid":1623336935,"imid":0,"nick":"潮田赐恒","type":0,"settlement":2,"sid":1352000930,"ssid":2806362004,"startTime":1661961600000,"endTime":1693497600000,"owUID":1623336935,"isPublic":false},{"uid":1623336932,"imid":0,"nick":"YY用户","type":0,"settlement":2,"sid":1451420998,"ssid":2806592430,"startTime":1661961600000,"endTime":1693497600000,"owUID":1623336932,"isPublic":false},{"uid":231119173,"imid":0,"nick":"现实与梦境-","type":0,"settlement":2,"sid":1451119345,"ssid":2804715258,"startTime":1661961600000,"endTime":1693497600000,"owUID":1366168715,"isPublic":false},{"uid":2849621836,"imid":0,"nick":"mftest13","type":1,"settlement":0,"sid":87814665,"ssid":2793149050,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":2849402401,"imid":0,"nick":"mftest10","type":1,"settlement":0,"sid":11297016,"ssid":2461312866,"startTime":1661961600000,"endTime":1693497600000,"owUID":50014069,"isPublic":true},{"uid":2895268203,"imid":0,"nick":"呵呵呵呵","type":1,"settlement":0,"sid":87814665,"ssid":2808053603,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":2849216867,"imid":0,"nick":"mftest7","type":0,"settlement":2,"sid":87814665,"ssid":2808053660,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":true},{"uid":2717576946,"imid":0,"nick":"crid_t35","type":1,"settlement":0,"sid":87814665,"ssid":2677843610,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":2608236388,"imid":0,"nick":"crid_t34","type":0,"settlement":2,"sid":87814665,"ssid":2805640294,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":2558896892,"imid":0,"nick":"crid_t33","type":0,"settlement":2,"sid":87814665,"ssid":2805640301,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":2849622337,"imid":0,"nick":"mftest20","type":1,"settlement":0,"sid":87814665,"ssid":2805640284,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":2464534135,"imid":0,"nick":"小可爱举着大碗来咯🤪😯🥐🍔🍟🌯","type":0,"settlement":2,"sid":87814665,"ssid":2784837179,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":2465950205,"imid":0,"nick":"采蘑菇滴小菇凉🍓","type":0,"settlement":2,"sid":87814665,"ssid":2785159893,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":141802640244746,"imid":0,"nick":"YY~咩呀","type":1,"settlement":0,"sid":87814665,"ssid":2532761320,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":1640772986,"imid":0,"nick":"crid_t7","type":0,"settlement":2,"sid":1451119349,"ssid":2808600398,"startTime":1661961600000,"endTime":1693497600000,"owUID":603532236,"isPublic":false},{"uid":1094530066,"imid":0,"nick":"jy_zlkcs37","type":0,"settlement":2,"sid":1451119349,"ssid":2808600401,"startTime":1661961600000,"endTime":1693497600000,"owUID":603532236,"isPublic":false},{"uid":16408030681,"imid":0,"nick":"","type":0,"settlement":2,"sid":1451119349,"ssid":2808600405,"startTime":1661961600000,"endTime":1693497600000,"owUID":603532236,"isPublic":false},{"uid":1640772965,"imid":0,"nick":"crid_t6gs ","type":0,"settlement":2,"sid":1451119349,"ssid":2808600410,"startTime":1661961600000,"endTime":1693497600000,"owUID":603532236,"isPublic":false},{"uid":1640778529,"imid":0,"nick":"crid_t1","type":0,"settlement":2,"sid":1451119349,"ssid":2808600567,"startTime":1661961600000,"endTime":1693497600000,"owUID":603532236,"isPublic":false},{"uid":2849622291,"imid":0,"nick":"mftest19","type":0,"settlement":2,"sid":87814665,"ssid":2805640287,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":2849622195,"imid":0,"nick":"mftest18","type":1,"settlement":0,"sid":87814665,"ssid":2808699097,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":2849226570,"imid":0,"nick":"mftest17","type":1,"settlement":0,"sid":87814665,"ssid":2808699248,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":2895385561,"imid":0,"nick":"lsl321aaa","type":1,"settlement":0,"sid":1456288771,"ssid":2808699732,"startTime":1661961600000,"endTime":1693497600000,"owUID":50073905,"isPublic":false},{"uid":1640778871,"imid":0,"nick":"crid_t20","type":0,"settlement":2,"sid":1451119347,"ssid":2808596406,"startTime":1661961600000,"endTime":1693497600000,"owUID":539246676,"isPublic":false},{"uid":1640778860,"imid":0,"nick":"crid_t19","type":0,"settlement":2,"sid":1451119347,"ssid":2808596411,"startTime":1661961600000,"endTime":1693497600000,"owUID":539246676,"isPublic":false},{"uid":1640803235,"imid":0,"nick":"怪我未能长成你爱的样子","type":0,"settlement":2,"sid":1451119347,"ssid":2808596418,"startTime":1661961600000,"endTime":1693497600000,"owUID":539246676,"isPublic":false},{"uid":1640797460,"imid":0,"nick":"crid_t15","type":0,"settlement":2,"sid":1451119347,"ssid":2808596423,"startTime":1661961600000,"endTime":1693497600000,"owUID":539246676,"isPublic":false},{"uid":1640778835,"imid":0,"nick":"crid_t17","type":0,"settlement":2,"sid":1451119347,"ssid":2808596420,"startTime":1661961600000,"endTime":1693497600000,"owUID":539246676,"isPublic":false},{"uid":1640797170,"imid":0,"nick":"crid_3","type":0,"settlement":2,"sid":1451119347,"ssid":2808596428,"startTime":1661961600000,"endTime":1693497600000,"owUID":539246676,"isPublic":false},{"uid":2849511315,"imid":0,"nick":"mftest16","type":0,"settlement":2,"sid":1451119352,"ssid":2808723179,"startTime":1661961600000,"endTime":1693497600000,"owUID":1330735047,"isPublic":false},{"uid":2849621971,"imid":0,"nick":"我要把你按在墙上让你看看我的奖状","type":1,"settlement":0,"sid":1451119352,"ssid":2808723185,"startTime":1661961600000,"endTime":1693497600000,"owUID":1330735047,"isPublic":false},{"uid":2849511124,"imid":0,"nick":"mftest14","type":1,"settlement":0,"sid":1451119352,"ssid":2808723195,"startTime":1661961600000,"endTime":1693497600000,"owUID":1330735047,"isPublic":false},{"uid":4425231,"imid":0,"nick":"玫玫","type":0,"settlement":2,"sid":87814665,"ssid":2849622291,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":2710198547,"imid":0,"nick":"yytest128","type":1,"settlement":0,"sid":87814665,"ssid":2804165390,"startTime":1612800000,"endTime":1612800000,"owUID":368842903,"isPublic":false},{"uid":675709759,"imid":0,"nick":"GoodDays!","type":0,"settlement":2,"sid":1451119345,"ssid":2804720979,"startTime":1661961600000,"endTime":1693497600000,"owUID":1366168715,"isPublic":false},{"uid":50074306,"imid":0,"nick":"90时光很残忍去总是装好人","type":0,"settlement":2,"sid":87814665,"ssid":2750861704,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":453701578,"imid":0,"nick":"忙只是他敷衍她的借口","type":0,"settlement":2,"sid":1451119345,"ssid":2805917725,"startTime":1661961600000,"endTime":1693497600000,"owUID":1366168715,"isPublic":false},{"uid":2804590700,"imid":0,"nick":"在日光岩闲逛的美人鱼","type":1,"settlement":0,"sid":90961437,"ssid":1640772937,"startTime":1664467200000,"endTime":1696003200000,"owUID":1640772937,"isPublic":false},{"uid":50047715,"imid":0,"nick":"交友昵称","type":0,"settlement":2,"sid":87814665,"ssid":2793548112,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":2631805324,"imid":0,"nick":"YY用户2586619412清河超长名称","type":0,"settlement":2,"sid":1455080648,"ssid":2810306725,"startTime":1661961600000,"endTime":1693497600000,"owUID":2632067707,"isPublic":false},{"uid":1090712086,"imid":0,"nick":"jy_zlkcs35jy_zlkcs35","type":0,"settlement":2,"sid":11297016,"ssid":2760597633,"startTime":1661961600000,"endTime":1693497600000,"owUID":50014069,"isPublic":false},{"uid":1090717987,"imid":0,"nick":"jy_zlkcs36","type":0,"settlement":2,"sid":11297016,"ssid":2760598024,"startTime":1661961600000,"endTime":1693497600000,"owUID":50014069,"isPublic":true},{"uid":499327484,"imid":0,"nick":"499人在℡心却空","type":0,"settlement":2,"sid":1451119345,"ssid":2804715255,"startTime":1661961600000,"endTime":1693497600000,"owUID":1366168715,"isPublic":false},{"uid":675707016,"imid":0,"nick":"- 我的心没人懂070707゛","type":0,"settlement":2,"sid":11297016,"ssid":2506681762,"startTime":1661961600000,"endTime":1693497600000,"owUID":50014069,"isPublic":false},{"uid":10813121731,"imid":0,"nick":"","type":1,"settlement":0,"sid":93151813,"ssid":2780293467,"startTime":1661961600000,"endTime":1693497600000,"owUID":991349326,"isPublic":false},{"uid":1081922493,"imid":0,"nick":"jy_zlkcs11","type":0,"settlement":2,"sid":93151813,"ssid":2780293461,"startTime":1661961600000,"endTime":1693497600000,"owUID":991349326,"isPublic":false},{"uid":2895267654,"imid":0,"nick":"aalsl123","type":1,"settlement":0,"sid":10578,"ssid":2811009470,"startTime":1661961600000,"endTime":1693497600000,"owUID":50014069,"isPublic":false},{"uid":2750229567,"imid":0,"nick":"crid_t37","type":1,"settlement":0,"sid":11297016,"ssid":2760598043,"startTime":1669305600000,"endTime":1700841600000,"owUID":50014069,"isPublic":false},{"uid":2849272015,"imid":0,"nick":"mftest4","type":0,"settlement":2,"sid":11297016,"ssid":2783500765,"startTime":1669564800000,"endTime":1701100800000,"owUID":50014069,"isPublic":false},{"uid":50079644,"imid":0,"nick":"dw_zhang~~","type":0,"settlement":2,"sid":87814665,"ssid":28128134711,"startTime":1661961600000,"endTime":1693497600000,"owUID":368842903,"isPublic":false},{"uid":1796119310,"imid":0,"nick":"εうぁ42356號θ☯借口프로大丰收CD","type":1,"settlement":0,"sid":11297016,"ssid":2805811570,"startTime":1676217600000,"endTime":1707753600000,"owUID":50014069,"isPublic":false}]}`)
        // let list = resp ? resp.list || [] : []
        const { data: { list } } = yield call(roomMgrList, payload)

        yield put({
          type: 'updateRoomMgrList',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        message.error('e:' + e)
      }
    },

    * getWhitelist ({ payload }, { call, put }) {
      try {
        const { data: { list } } = yield call(whiteList, payload)

        yield put({
          type: 'updateWhitelist',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        message.error('e:' + e)
      }
    },

    * addWhitelist ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(addWhiteList, payload)
        if (status !== 0) {
          message.error('error:' + msg)
        } else if (status === 0) {
          message.success('添加成功!')
        }
      } catch (e) {
        message.error('e:' + e)
      }
    },

    * updateRoomMgr ({ payload, callback }, { call, put }) {
      const { data } = yield call(updateRoomMgr, payload)
      if (callback) {
        callback(data)
      }
    },

    * deleteRoomMgr ({ payload, callback }, { call, put }) {
      const { data } = yield call(deleteRoomMgr, payload)
      if (callback) {
        callback(data)
      }
    },

    * updateRoomMgrGrade ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(updateRoomMgrGrade, payload)
        if (status !== 0) {
          message.error('error:' + msg)
        } else if (status === 0) {
          message.success('操作成功!已提交审批')
        }
      } catch (e) {
        message.error('e:' + e)
      }
    },

    * getAssessHistory ({ payload }, { call, put }) {
      // 清除原有的信息
      yield put({
        type: 'updateAssessHistory',
        payload: []
      })
      try {
        const { data: { list } } = yield call(getAssessHistory, payload)

        yield put({
          type: 'updateAssessHistory',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        message.error('e:' + e)
      }
    }
  }
}
