import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Table, InputNumber, Select, DatePicker, Button, message, Modal, Form, Input, Row, Col, Divider, Upload } from 'antd'
import { UploadOutlined } from '@ant-design/icons'

const Option = Select.Option

const namespace = 'newGuildSettleIn'

const yesNoMap = [
  { label: '全部', value: 0 },
  { label: '是', value: 1 },
  { label: '否', value: 2 }
]
const yesNoMap2 = { 0: '全部', 1: '是', 2: '否' }

const reviewMap = [
  { label: '全部', value: 0 },
  { label: '待审核', value: 1 },
  { label: '审核通过', value: 2 },
  { label: '审核不通过', value: 3 }
]

const approvalMap = [
  { label: '全部', value: 0 },
  { label: '待审核', value: 1 },
  { label: '一审中', value: 2 },
  { label: '一审通过，二审中', value: 3 },
  { label: '审核通过', value: 4 },
  { label: '审核不通过', value: 5 }
]

const companyRealAuthMap = { '': '未审核', 'S0I': '待审核', 'S0S': '审核中', 'S0A': '审核通过', 'S0X': '审核不通过' }

const faildDescList = [
  { label: '公会合作协议填写有误', value: '公会合作协议填写有误' },
  { label: '三方协议填写有误', value: '三方协议填写有误' },
  { label: '乙方公会主要管理人员填写有误', value: '乙方公会主要管理人员填写有误' }
]

var moment = require('moment')
var dateFormat = 'YYYY-MM-DD'

@connect(({ newGuildSettleIn }) => ({
  model: newGuildSettleIn
}))

class NewGuildSettleInSuperCrysta extends Component {
  columns = [
    { title: '序号', dataIndex: 'idx', align: 'center', fixed: 'left' },
    { title: '频道ID', dataIndex: 'sid', align: 'center', fixed: 'left' },
    { title: '短位ID', dataIndex: 'asid', align: 'center', fixed: 'left' },
    { title: '是否达到申请条件', dataIndex: 'isReachCondition', align: 'center', render: v => yesNoMap.find(i => i.value === v).label },
    { title: '是否绿色通道白名单', dataIndex: 'isGreenChannel', align: 'center', render: v => yesNoMap.find(i => i.value === v).label },
    { title: '企业认证审核状态', dataIndex: 'companyRealAuthStatus', align: 'center', render: (text, record) => (companyRealAuthMap[record.companyRealAuthStatus]) },
    { title: '申请时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (record.timestamp === 0 ? '' : moment.unix(record.timestamp).format(dateFormat)) },
    { title: '运营审核状态', dataIndex: 'reviewStatus', align: 'center', render: v => reviewMap.find(i => i.value === v).label },
    { title: '业务审核状态', dataIndex: 'approvalStatus', align: 'center', render: v => approvalMap.find(i => i.value === v).label },
    { title: '审核完成时间', dataIndex: 'approvalPassTime', align: 'center', render: (text, record) => (record.approvalPassTime === 0 ? '' : moment.unix(record.approvalPassTime).format(dateFormat)) },
    { title: '运营备注', dataIndex: 'reviewReason', align: 'center' },
    { title: '历史申请记录', key: 'historyRecord', align: 'center', render: (text, record) => (<span><a onClick={this.showHistoryModal(record)}>查看</a></span>) },
    { title: '查看详情及操作', key: 'operation', align: 'center', render: (text, record) => (record.uploadStatus === false ? <span><a onClick={this.showUploadModal(record)}>上传合同附件</a></span> : <span><a onClick={this.showDetailModal(record)}>查看并审批</a></span>) }
  ]

  columnshistory = [
    { title: '日期', dataIndex: 'timestamp', align: 'center', render: (text, record) => (record.timestamp === 0 ? '' : moment.unix(record.timestamp).format('YYYY-MM-DD hh:mm:ss')) },
    { title: '申请时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (record.timestamp === 0 ? '' : moment.unix(record.timestamp).format(dateFormat)) },
    { title: '运营审核状态', dataIndex: 'reviewStatus', align: 'center', render: v => reviewMap.find(i => i.value === v).label },
    { title: '业务审核状态', dataIndex: 'approvalStatus', align: 'center', render: v => approvalMap.find(i => i.value === v).label },
    { title: '审核运营', dataIndex: 'reviewUser', align: 'center' },
    { title: '运营备注', dataIndex: 'reviewReason', align: 'center' },
    { title: '业务备注', dataIndex: 'approvalReason', align: 'center', render: (text, record) => (record.reviewStatus === 3 ? record.reviewReason : record.approvalReason) }
  ]

  state = {
    visible: false,
    uploadSuccess: false,
    approvalRejectReasonQuick: '',
    approvalRejectReason: ''
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  componentDidMount () {
    this.loadData()
  }

  showHistoryModal = (record) => () => {
    this.setState({ visibleHistory: true, historySID: record.sid })
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/listSuperCrystalHistory`,
      payload: { sid: record.sid }
    })
  }

  showDetailModal = (record) => () => {
    this.setState({ curID: record.id, record: record, visibleSuperCrystalDetail: true })
  }

  showUploadModal = (record) => () => {
    if (this.formRef) {
      this.formRef.resetFields()
      console.log(record)
      // this.formRef.setFieldsValue(record)
      this.formRef.setFieldsValue({ id: record.id })
      
      if (record.guildCooperationAgreementURL !== '') {
        let file = { uid: '1', name: record.guildCooperationAgreementName, status: 'done', url: record.guildCooperationAgreementURL, response: { msg: 'success!', status: 0, urls: [record.guildCooperationAgreementURL] } }
        this.formRef.setFieldsValue({ guildCooperationAgreementURL: { file: file, fileList: [file] } })
        this.setState({ guildCooperationAgreementURLList: [file] })
      }
      
      if (record.threePartiesAgreementURL !== '') {
        let file = { uid: '2', name: record.threePartiesAgreementName, status: 'done', url: record.threePartiesAgreementURL, response: { msg: 'success!', status: 0, urls: [record.threePartiesAgreementURL] } }
        this.formRef.setFieldsValue({ threePartiesAgreementURL: { file: file, fileList: [file] } })
        this.setState({ threePartiesAgreementURLList: [file] })
      }

      if (record.guildManagerURL !== '') {
        let file = { uid: '3', name: record.guildManagerName, status: 'done', url: record.guildManagerURL, response: { msg: 'success!', status: 0, urls: [record.guildManagerURL] } }
        this.formRef.setFieldsValue({ guildManagerURL: { file: file, fileList: [file] } })
        this.setState({ guildManagerURLList: [file] })
      }
    }
    this.setState({ curID: record.id, visibleUpload: true })
  }

  loadData = () => {
    const { searchSID, searchIsReachCondition, searchIsGreenChannel, searchReviewStatus, searchApprovalStatus, searchApplyStartTime, searchApplyEndTime } = this.state
    const { dispatch } = this.props

    let searchApplyStartTimeTmp = 0
    let searchApplyEndTimeTmp = 0
    if (searchApplyStartTime) {
      searchApplyStartTimeTmp = moment(searchApplyStartTime).unix()
    }
    if (searchApplyEndTime) {
      searchApplyEndTimeTmp = moment(searchApplyEndTime).unix()
    }

    if (searchApplyStartTimeTmp !== 0 && searchApplyEndTimeTmp !== 0 && searchApplyStartTimeTmp >= searchApplyEndTimeTmp) {
      message.warn('开始时间不能大于结束时间')
      return
    }

    let data = { sid: searchSID, isReachCondition: searchIsReachCondition, isGreenChannel: searchIsGreenChannel, reviewStatus: searchReviewStatus, approvalStatus: searchApprovalStatus, applyStartTime: searchApplyStartTimeTmp, applyEndTime: searchApplyEndTimeTmp }
    dispatch({
      type: `${namespace}/listSuperCrystal`,
      payload: data
    })
  }

  searchHandle = () => () => {
    this.loadData()
  }

  handleCancelHistory = e => {
    this.setState({ visibleHistory: false })
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  handleCancelUpload = e => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.setState({ visibleUpload: false, guildCooperationAgreementURLList: [], threePartiesAgreementURLList: [], guildManagerURLList: [] })
  }

  handleSubmitUpload = e => {
    if (this.formRef) {
      this.formRef.submit()
    }
  }

  onFinishUpload = values => {
    const { curID } = this.state

    console.log(values)

    if (values && (values.guildCooperationAgreementURL.fileList.length !== 1 || values.threePartiesAgreementURL.fileList.length !== 1 || values.guildManagerURL.fileList.length !== 1)) {
      message.warning('每个表单上传一个文件')
      console.log(values)
      return
    }

    let guildCooperationAgreementURL = ''
    let guildCooperationAgreementName = ''
    if (values && values.guildCooperationAgreementURL.fileList[0].status === 'done' && values.guildCooperationAgreementURL.fileList[0].response.status === 0 && values.guildCooperationAgreementURL.fileList[0].response.urls[0]) {
      guildCooperationAgreementURL = values.guildCooperationAgreementURL.fileList[0].response.urls[0]
      guildCooperationAgreementName = values.guildCooperationAgreementURL.fileList[0].name
    }

    let threePartiesAgreementURL = ''
    let threePartiesAgreementName = ''
    if (values && values.threePartiesAgreementURL.fileList[0].status === 'done' && values.threePartiesAgreementURL.fileList[0].response.status === 0 && values.threePartiesAgreementURL.fileList[0].response.urls[0]) {
      threePartiesAgreementURL = values.threePartiesAgreementURL.fileList[0].response.urls[0].replace('http://', 'https://')
      threePartiesAgreementName = values.threePartiesAgreementURL.fileList[0].name
    }

    let guildManagerURL = ''
    let guildManagerName = ''
    if (values && values.guildManagerURL.fileList[0].status === 'done' && values.guildManagerURL.fileList[0].response.status === 0 && values.guildManagerURL.fileList[0].response.urls[0]) {
      guildManagerURL = values.guildManagerURL.fileList[0].response.urls[0].replace('http://', 'https://')
      guildManagerName = values.guildManagerURL.fileList[0].name
    }

    if (guildCooperationAgreementURL === '' || threePartiesAgreementURL === '' || guildManagerURL === '') {
      message.warning('上传文件错误, 重新上传', guildCooperationAgreementURL, threePartiesAgreementURL, guildManagerURL)
      console.log(values)
      return
    }

    let data = { id: curID, guildCooperationAgreementURL: guildCooperationAgreementURL, guildCooperationAgreementName: guildCooperationAgreementName, threePartiesAgreementURL: threePartiesAgreementURL, threePartiesAgreementName: threePartiesAgreementName, guildManagerURL: guildManagerURL, guildManagerName: guildManagerName }

    console.log(data)
    this.props.dispatch({
      type: `${namespace}/uploadSuperSrysta`,
      payload: data
    })
    this.setState({ visibleUpload: false, guildCooperationAgreementURLList: [], threePartiesAgreementURLList: [], guildManagerURLList: [] })
  }

  handleCancelDetail = e => {
    this.setState({ curID: '', visibleSuperCrystalDetail: false })
  }

  cancelDetailHandle = () => () => {
    this.setState({ visibleSuperCrystalDetail: false })
  }

  UpLoadOnChange = info => {
    if (info.file.status !== 'done') {
      return
    }

    if (info.file.type === 'image/png' || info.file.type === 'image/jpg' || info.file.type === 'image/jpeg' || info.file.type === 'image/gif') {
      this.setState({ uploadSuccess: false })
      message.error(`${info.file.name} 不支持文档类型, 请重新上传`)
      return
    }

    if (info.file.response.status === 0) {
      this.setState({ uploadSuccess: true })
      message.success(`${info.file.name} uploaded successfully`)
    } else {
      message.error(info.file.response.msg)
    }
  }

  approvalPassHandle = () => () => {
    let detailHTML = document.getElementById('inner')?.innerHTML
    console.log(detailHTML)
    const { curID, approvalPassReason } = this.state
    let data = { id: curID, reviewStatus: 2, reviewReason: approvalPassReason, html: detailHTML }
    console.log(data)
    this.props.dispatch({
      type: `${namespace}/approvalSuperCrysta`,
      payload: data
    })
    this.hiddenApprovalPass()
    this.setState({ visibleSuperCrystalDetail: false })
    detailHTML = null
  }

  showApprovalPass = () => () => {
    this.setState({ approvalPassVisible: true })
  }

  hiddenApprovalPass = () => {
    this.setState({ approvalPassVisible: false, approvalPassReason: '' })
  }

  hiddenApprovalPass2 = () => () => {
    this.setState({ approvalPassVisible: false, approvalPassReason: '' })
  }

  showApprovalReject = () => () => {
    this.setState({ approvalRejectVisible: true })
  }

  hiddenApprovalReject = () => {
    this.setState({ approvalRejectVisible: false, approvalRejectReason: '', approvalRejectReasonQuick: '' })
  }

  hiddenApprovalReject2 = () => () => {
    this.setState({ approvalRejectVisible: false, approvalRejectReason: '', approvalRejectReasonQuick: '' })
  }

  approvalRejectHandle = () => () => {
    const { curID, approvalRejectReason, approvalRejectReasonQuick } = this.state
    console.log(approvalRejectReason, approvalRejectReasonQuick)
    if (approvalRejectReason && approvalRejectReasonQuick && approvalRejectReason !== '' && approvalRejectReasonQuick !== '') {
      message.warning('快速选择和手动输入只能选其一')
      return
    }
    let reviewReason = ''
    if (approvalRejectReason && approvalRejectReason !== '') {
      reviewReason = approvalRejectReason
    } else if (approvalRejectReasonQuick && approvalRejectReasonQuick !== '') {
      reviewReason = approvalRejectReasonQuick
    }

    let data = { id: curID, reviewStatus: 3, reviewReason: reviewReason }
    console.log(data)

    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/approvalSuperCrysta`,
      payload: data
    })
    this.hiddenApprovalReject()
    this.setState({ visibleSuperCrystalDetail: false })
  }

  faildDescHandler = (value) => {
    this.setState({ approvalRejectReasonQuick: value })
  }

  render () {
    const { guildCooperationAgreementURLList, threePartiesAgreementURLList, guildManagerURLList, visibleHistory, historySID, visibleUpload, approvalRejectVisible, visibleSuperCrystalDetail, record, approvalRejectReason, approvalPassVisible } = this.state
    const { model: { listSuperCrystal, listSuperCrystalHistory, tableLoadingSuperCrystalHistory } } = this.props

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 10 }
      },
      wrapperCol: {
        xs: { span: 2 },
        sm: { span: 15 }
      }
    }
    console.log(record)
    return (
      <Card>
        <span>频道ID</span>
        <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchSID: e })} style={{ width: 100, marginLeft: 3 }} />
        <span style={{ marginLeft: 10 }}>是否达到申请条件</span>
        <Select style={{ marginLeft: 5, width: 100 }} placeholder='请选择' onChange={(v) => this.setState({ searchIsReachCondition: v })}>{ yesNoMap.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
        <span style={{ marginLeft: 10 }}>是否绿色通道白名单</span>
        <Select style={{ marginLeft: 5, width: 100 }} placeholder='请选择' onChange={(v) => this.setState({ searchIsGreenChannel: v })}>{ yesNoMap.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
        <span style={{ marginLeft: 10 }}>运营审核状态</span>
        <Select style={{ marginLeft: 5, width: 100 }} placeholder='请选择' onChange={(v) => this.setState({ searchReviewStatus: v })}>{ reviewMap.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
        <span style={{ marginLeft: 10 }}>业务审核状态</span>
        <Select style={{ marginLeft: 5, width: 140 }} placeholder='请选择' onChange={(v) => this.setState({ searchApprovalStatus: v })}>{ approvalMap.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
        <div style={{ marginTop: 10 }} />
        <span>申请时间</span>
        <DatePicker
          format='YYYY-MM-DD'
          placeholder='开始时间'
          onChange={(v) => this.setState({ searchApplyStartTime: v })}
          style={{ marginLeft: 10 }}
        />
        <span style={{ marginLeft: 5 }}>~</span>
        <DatePicker
          format='YYYY-MM-DD'
          placeholder='结束时间'
          onChange={(v) => this.setState({ searchApplyEndTime: v })}
          style={{ marginLeft: 5 }}
        />
        <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
        <Table style={{ marginTop: 10 }} size='small' rowKey='idx' pagination={this.defaultPageValue} columns={this.columns} dataSource={listSuperCrystal} />

        <Modal footer={null} forceRender width={1200} visible={visibleHistory} title='历史记录' onCancel={this.handleCancelHistory}>
          <Card>
            <span><font style={{ marginLeft: 5, fontSize: '20px' }}>频道ID:  </font><font style={{ fontSize: '20px' }}>{historySID}</font></span>
            <Table loading={tableLoadingSuperCrystalHistory} style={{ marginTop: 10 }} size='small' rowKey='idx' pagination={this.defaultPageValue} columns={this.columnshistory} dataSource={listSuperCrystalHistory} />
          </Card>
        </Modal>

        <Modal forceRender width={600} visible={visibleUpload} title='上传合同附件' onCancel={this.handleCancelUpload} onOk={this.handleSubmitUpload}>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinishUpload}>
            <Form.Item name='id' hidden>
              <Input hidden />
            </Form.Item>
            <Form.Item label='公会合作协议' name='guildCooperationAgreementURL' rules={[{ required: true }]}>
              <Upload name='file' action='https://fts.yy.com/fs/uploadfiles' data={file => ({ bucket: 'makefriends', files: file })} onChange={this.UpLoadOnChange} multiple={false} accept='.pdf, .docx, .xlsx' defaultFileList={guildCooperationAgreementURLList}>
                <Button type='primary'>
                  <UploadOutlined /> 上传
                </Button>
              </Upload>
            </Form.Item>
            <Form.Item label='三方协议' name='threePartiesAgreementURL' rules={[{ required: true }]}>
              <Upload name='file' action='https://fts.yy.com/fs/uploadfiles' data={file => ({ bucket: 'makefriends', files: file })} onChange={this.UpLoadOnChange} multiple={false} accept='.pdf, .docx, .xlsx' defaultFileList={threePartiesAgreementURLList}>
                <Button type='primary'>
                  <UploadOutlined /> 上传
                </Button>
              </Upload>
            </Form.Item>
            <Form.Item label='附乙方公会主要管理人员' name='guildManagerURL' rules={[{ required: true }]}>
              <Upload name='file' action='https://fts.yy.com/fs/uploadfiles' data={file => ({ bucket: 'makefriends', files: file })} onChange={this.UpLoadOnChange} multiple={false} accept='.pdf, .docx, .xlsx' defaultFileList={guildManagerURLList}>
                <Button type='primary'>
                  <UploadOutlined /> 上传
                </Button>
              </Upload>
            </Form.Item>
          </Form>
          <div style={{ marginLeft: 100 }}>注：上传文件支持格式：word、excel、pdf</div>
        </Modal>

        <Modal footer={null} width={800} visible={visibleSuperCrystalDetail} title='申请超级水晶公会审批' onCancel={this.handleCancelDetail}>
          <div id='inner'>
            <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px', fontWeight: '500' }}>申请频道基本信息</font></div>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>频道ID：{record !== null && record !== undefined ? record.asid : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>申请时间：{record !== null && record !== undefined ? moment.unix(record.timestamp).format(dateFormat) : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>申请前1个自然月礼物流水/元：{record !== null && record !== undefined ? record.amount : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>申请前1个自然月日均有收入主持数/人：{record !== null && record !== undefined ? record.compereCount : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>是否达到申请条件：{record !== null && record !== undefined ? yesNoMap2[record.isReachCondition] : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>是否绿色通道白名单：{record !== null && record !== undefined ? yesNoMap2[record.isGreenChannel] : ''}</font>
              </Col>
            </Row>
            <Divider />

            <div style={{ marginLeft: 42 }}><font color='red'>若审核不通过，可联系客服(钟淑玲)</font></div>
            <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px', fontWeight: '500' }}>企业认证</font></div>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>企业认证审核状态：{record !== null && record !== undefined ? companyRealAuthMap[record.companyRealAuthStatus] : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>审核描述：{record !== null && record !== undefined ? record.companyRealAuthDesc : ''}</font>
              </Col>
            </Row>
            <Divider />

            <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px', fontWeight: '500' }}>合同附件</font></div>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公会合作协议：</font><a href={record !== null && record !== undefined ? record.guildCooperationAgreementURL.replace('http://', 'https://') : ''}>{record !== null && record !== undefined && record.guildCooperationAgreementName ? record.guildCooperationAgreementName : ''}</a>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>三方协议：</font><a href={record !== null && record !== undefined ? record.threePartiesAgreementURL.replace('http://', 'https://') : ''}>{record !== null && record !== undefined && record.threePartiesAgreementName ? record.threePartiesAgreementName : ''}</a>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>附乙方公会主要管理人员：</font><a href={record !== null && record !== undefined ? record.guildManagerURL.replace('http://', 'https://') : ''}>{record !== null && record !== undefined && record.guildManagerName ? record.guildManagerName : ''}</a>
              </Col>
            </Row>
          </div>
          <Divider />
          <Button disabled={record !== null && record !== undefined && (record.reviewStatus === 2 || record.reviewStatus === 3 || record.approvalStatus === 4 || record.approvalStatus === 5)} style={{ marginLeft: 230 }} type='primary' onClick={this.showApprovalPass()}>通过</Button>
          <Button disabled={record !== null && record !== undefined && (record.reviewStatus === 2 || record.reviewStatus === 3 || record.approvalStatus === 4 || record.approvalStatus === 5)} style={{ marginLeft: 20 }} type='primary' danger onClick={this.showApprovalReject()}>不通过</Button>
          <Button style={{ marginLeft: 20 }} type='default' onClick={this.cancelDetailHandle()}>取消</Button>
        </Modal>

        <Modal footer={null} visible={approvalRejectVisible} title='是否确认审批不通过？' onCancel={this.hiddenApprovalReject}>
          <span>失败原因快速选择</span>
          <Select style={{ marginLeft: 5, width: 230 }} placeholder='请选择' onChange={(v) => { this.faildDescHandler(v) }}>{ faildDescList.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
          <div style={{ marginTop: 10 }} />
          <span>手动输入原因</span>
          <Input placeholder='请输入' value={approvalRejectReason} onChange={e => this.setState({ approvalRejectReason: e.target.value })} style={{ width: 250, marginLeft: 10 }} />
          <div style={{ marginTop: 10 }}>注：原因选填，原因最多输入20字符。提交后，公会后台将提示审批不通过，并展示填写的原因。</div>
          <Divider />
          <Button style={{ marginTop: 10, marginLeft: 150 }} type='primary' onClick={this.approvalRejectHandle()}>确认</Button>
          <Button style={{ marginTop: 10, marginLeft: 30 }} type='default' onClick={this.hiddenApprovalReject2()}>取消</Button>
        </Modal>

        <Modal footer={null} visible={approvalPassVisible} title='是否确认审批通过？' onCancel={this.hiddenApprovalPass}>
          <span style={{ marginLeft: 24, marginTop: 20 }}>输入原因</span>
          <Input style={{ width: 230, marginLeft: 10 }} placeholder='请输入' onChange={e => this.setState({ approvalPassReason: e.target.value })} />
          <div style={{ marginTop: 10 }}>注：原因选填，原因最多输入20字符。</div>
          <Divider />
          <Button style={{ marginTop: 10, marginLeft: 150 }} type='primary' onClick={this.approvalPassHandle()}>确认</Button>
          <Button style={{ marginTop: 10, marginLeft: 30 }} type='default' onClick={this.hiddenApprovalPass2()}>取消</Button>
        </Modal>
      </Card>
    )
  }
}

export default NewGuildSettleInSuperCrysta
