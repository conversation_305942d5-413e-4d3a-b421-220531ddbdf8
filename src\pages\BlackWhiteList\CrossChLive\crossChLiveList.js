import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Row, Col, Typography, Space, Table, InputNumber, Button, Popconfirm, Modal, message, Form } from 'antd'
const { Text, Link } = Typography

const namespace = 'crossLive'

@connect(({ crossLive }) => ({
  model: crossLive
}))

class CrossChLiveList extends Component {
  state = {
    searchSid: '',
    searchAsid: '',
    passReason: '',
    showModal: false,
    notPassReason: ''
  }

  componentDidMount = () => {
    this.refreshList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // 查询列表数据
  refreshList = () => {
    const { searchAsid, searchSid } = this.state
    this.callModel('listCrossChLive', {
      params: {
        asid: searchAsid,
        sid: searchSid
      }
    })
  }

  // 删除白名单
  onDeleteItem = (r) => {
    const { sid } = r
    this.callModel('delCrossChLive', {
      params: { sid: sid },
      isJsonMode: true,
      isDetailMode: true,
      isSlientMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('操作失败: ' + msg)
          return
        }
        this.refreshList()
      }
    })
  }

  // 提交添加申请
  onSubmitAdd = (v) => {
    this.setState({ notPassReason: '' })
    const { sid } = v
    if (!sid || sid <= 0) {
      message.warn('格式有误,请检查后再提交')
      return
    }

    this.callModel('addCrossChLive', {
      params: v,
      isJsonMode: true,
      isDetailMode: true,
      isSlientMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status < 0) {
          message.error('提交出错: ' + status)
          return
        }
        if (status > 0) { // 检验失败
          this.setState({ notPassReason: msg })
          message.warn('SID检验失败: ' + msg)
          return
        }
        message.success('添加成功~')
        this.setState({ passReason: '添加成功', notPassReason: '' })
        setTimeout(() => {
          this.setState({ showModal: false })
          this.refreshList()
        }, 2000)
      }
    })
  }

  render () {
    const { searchAsid, searchSid, showModal, notPassReason, passReason } = this.state
    const { crossChliveList } = this.props.model

    const columns = [
      { title: '短位频道', dataIndex: 'asid' },
      { title: '频道号', dataIndex: 'sid' },
      { title: '签约房管数', dataIndex: 'roomMgrNum' },
      { title: '星光主持数', dataIndex: 'starCompereNum' },
      { title: '添加人UID', dataIndex: 'opUid' },
      { title: '添加时间', dataIndex: 'optTime' },
      { title: '操作',
        dataIndex: 'asid',
        render: (v, r) => {
          return <Popconfirm title='确认删除白名单么?' onConfirm={() => this.onDeleteItem(r)} >
            <Link type='danger'>删除</Link>
          </Popconfirm>
        } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <Card>
        <Row>

          <Col span={24} style={{ marginBottom: '1em' }}>
            <Space>
              <div>
                <Text>短位频道：</Text>
                <InputNumber style={{ width: '12em' }} placeholder='搜索短位频道' value={searchAsid} onChange={v => this.setState({ searchAsid: v })} />
              </div>
              <div>
                <Text>搜索频道号: </Text>
                <InputNumber style={{ width: '12em' }} placeholder='搜索频道号' value={searchSid} onChange={v => this.setState({ searchSid: v })} />
              </div>
              <Button type='primary' onClick={this.refreshList}>搜索</Button>
              <Button onClick={() => this.setState({ showModal: true })}>添加白名单</Button>
            </Space>
          </Col>

          <Col span={24}>
            <Table columns={columns} dataSource={crossChliveList} />
          </Col>

          <Modal visible={showModal} title='添加白名单'
            okText='添加'
            cancelText='取消'
            onOk={() => this.formRef.submit()}
            onCancel={() => { this.setState({ showModal: false }) }}
          >
            <Form ref={r => { this.formRef = r }} onFinish={(v) => this.onSubmitAdd(v)} >
              <Form.Item name='sid' label='顶级频道SID' required>
                <InputNumber placeholder='请输入SID' style={{ width: '100%' }} />
              </Form.Item>
            </Form>
            <Row>
              <Text type='success'>{passReason}</Text>
              <Text type='danger'>{notPassReason}</Text>
            </Row>
          </Modal>
        </Row>
      </Card>
    )
  }
}

export default CrossChLiveList
