import { getLists, update, remove, confirm, down, add } from './api'
import { message } from 'antd'

export default {
  namespace: 'hatkingUpgradeConfig', // 只有这里需要修改

  state: {
    list: [],
    extra: ''
  },

  reducers: {
    updateList (state, { payload, extra }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload,
        extra: extra
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { list, extra } } = yield call(getLists, payload)

      yield put({
        type: 'updateList',
        payload: Array.isArray(list) ? list : [],
        extra: extra
      })
    },

    * addItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(add, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'update'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * updateItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(update, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * removeItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(remove, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * downItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(down, payload)
      if (status === 0) {
        message.success('down success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * confirmItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(confirm, payload)
      if (status === 0) {
        message.success('confirm success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * getItemByKey ({ payload }, { call, put }) {
      const { data: { status, list } } = yield call(payload)
      if (status === 0) {
        yield put({
          type: 'updateList',
          payload: list
        })
      } else {
        message.warning('not found')
      }
    }
  }
}
