import { getList, approvalItem, updateItem, getStatusDetail } from './api'
import { message } from 'antd'
// import { message } from 'antd'

export default {
  namespace: 'compereLibrary', // 全局唯一标识

  state: {
    compereList: [
      // { index: 1, businessType: 4, sid: 87814665, ssid: 87814665, compereUID: 123, takeEffect: 1, featurePicture: '', currentFeaturePicture: 1, 'picture': '' },
      // { index: 1, id: '1', businessType: 101, asid: 753, sid: 87814665, ssid: 111, compereUID: 123, takeEffect: 1, featurePicture: '', currentFeaturePicture: 1, 'picture': 'http://s1.yy.com/guild/header/10001.jpg', pictureStatus: 1, title: '大傻叉', titleStatus: 2 },
      // { index: 2, id: '2', businessType: 1, asid: 489, sid: 123, ssid: 456, compereUID: 123, takeEffect: 1, featurePicture: '', currentFeaturePicture: 1, 'picture': 'http://s1.yy.com/guild/header/10001.jpg', pictureStatus: 3, title: 'abc', titleStatus: 1 }
    ]
  },

  reducers: {
    updateCompereList (state, { payload }) {
      // console.log('updateCompereList', payload)

      for (let i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        compereList: payload
      }
    },

    updateChannelList (state, { payload }) {
      // console.log('updateChannelList', payload)

      for (let i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        channelInfoList: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      try {
        const { data: { recommendList } } = yield call(getList, payload)
        yield put({
          type: 'updateCompereList',
          payload: Array.isArray(recommendList) ? recommendList : []
        })
      } catch (e) {
        message.error('exception', e)
      }
    },

    * getStatusDetail ({ payload, callback }, { call, put }) {
      try {
        const { data: { status, data } } = yield call(getStatusDetail, payload)
        console.log(data)
        if (status === 0 && callback) {
          callback(data)
        }
      } catch (e) {
        message.error('exception', e)
      }
    },

    * approvalItem ({ payload }, { call, put }) {
      try {
        const { data: { ret, msg } } = yield call(approvalItem, payload)
        if (ret === 0) {
          message.info('success!')
          // yield put({
          //   type: `getList`,
          //   payload: { from: from }
          // })
        } else {
          message.error(msg)
        }
      } catch (e) {
        message.error('exception', e)
      }
    },

    * updateItem ({ payload }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(updateItem, payload)
        if (status === 0) {
          message.info('success!')
          // yield put({
          //   type: `getList`,
          //   payload: { from: from }
          // })
        } else {
          message.error(msg)
        }
      } catch (e) {
        message.error('exception', e)
      }
    }
  }
}
