import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Modal, Input, Select, Upload, message, Popconfirm } from 'antd'
import { connect } from 'dva'
import { exportExcel } from 'xlsx-oc'
import { UploadOutlined } from '@ant-design/icons'
const namespace = 'CityPartnerRecommend'
const FormItem = Form.Item
const Option = Select.Option

@connect(({ CityPartnerRecommend }) => ({
  CityPartnerRecommend
}))

class Index extends Component {
  // column structs.
  columns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: 'asid', dataIndex: 'asid', key: 'asid', align: 'center' },
    { title: 'sid', dataIndex: 'sid', key: 'sid', align: 'center' },
    { title: 'ssid', dataIndex: 'ssid', key: 'ssid', align: 'center' },
    { title: '推荐标题', dataIndex: 'title', key: 'title', align: 'center' },
    { title: '是否开播',
      dataIndex: 'livestate',
      key: 'livestate',
      align: 'center',
      render: (text, record) => (
        text === 1 ? '是' : '否'
      )
    },
    { title: '推荐权重', dataIndex: 'weight', key: 'weight', align: 'center' },
    { title: '推荐开始时间',
      dataIndex: 'recomm_start',
      key: 'recomm_start',
      align: 'center'
    },
    { title: '推荐结束时间',
      dataIndex: 'recomm_end',
      key: 'recomm_end',
      align: 'center'
    },
    { title: '操作时间',
      dataIndex: 'updtime',
      key: 'updtime',
      align: 'center'
    },

    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <Button style={{ marginRight: 10 }} size='small' type='primary' onClick={this.showModal(true, record)}>修改</Button>
          <Popconfirm title='是否删除?' type='primary' onConfirm={this.handleDelOne(record.ssid)} okText='是的' cancelText='暂不'>
            <a href=''>删除</a>
          </Popconfirm>
        </span>)
    }
  ]

  defaultPageValue = {
    defaultPageSize: 50,
    pageSizeOptions: ['50', '100', '500', '2000'],
    showSizeChanger: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = {
    visible: false,
    isUpdate: false,
    confirmVisible: false,
    deleteConfirmMsg: '',
    value: { asid: '', sid: '', ssid: '' }
  }

  // preDelete
  preDelete = () => {
    const { exportKey } = this.state
    console.log(exportKey)
    if (exportKey === undefined || exportKey.length === 0) {
      this.setState({ deleteConfirmMsg: '未选中数据' })
    } else {
      this.setState({ deleteConfirmMsg: '' })
    }
    this.setState({ confirmVisible: true })
  }

  // show modal
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.recomm_start = v.recomm_start == null ? '' : v.recomm_start
      v.recomm_end = v.recomm_end == null ? '' : v.recomm_end
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      // console.log(exportedRows)
      var value = selectedRows.map(item => item.ssid).join(',')
      this.setState({ removeKey: value })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name
    })
  }

  handleSubmit = () => {
    this.formRef.submit()
  }

  // add and update
  onFinish = values => {
    const { dispatch } = this.props
    const { isUpdate } = this.state
    const url = isUpdate ? `${namespace}/updateItem` : `${namespace}/addItem`
    dispatch({
      type: url,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { ssid: this.state.removeKey }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
    this.setState({ confirmVisible: false })
  }

  handleDelOne = ssid => e => {
    const { dispatch } = this.props
    const data = { ssid: ssid }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = { type: this.state.type }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // reset search info
  handleReset = clearFilters => () => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  // get list from server.
  searchBy = (value) => {
    const { dispatch } = this.props
    const data = { asid: this.state.searchAsid, ssid: this.state.searchSSid, livestate: this.state.searchLiveState }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  handleLiveStateChange = (value) => {
    const { dispatch } = this.props
    const data = { livestate: value.key }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })

    this.setState({ searchLiveState: value.key })
    // console.log(value) // { key: "lucy", label: "Lucy (101)" }
  }

  onExport = () => {
    let headers = []
    let columns = this.columns
    const { exportKey } = this.state
    columns.forEach(function (item) {
      headers.push({ k: item.dataIndex, v: item.title })
    })
    exportExcel(headers, exportKey, '城市合伙人推荐数据.xlsx')
  }

  UpLoadOnChange = info => {
    if (info.file.status !== 'done') {
      return
    }
    if (info.file.response.status === 0) {
      message.success(`${info.file.name} file uploaded successfully`)
    } else {
      message.error(info.file.response.msg)
    }
    const { dispatch } = this.props
    const data = {}
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // content
  render () {
    const { route, CityPartnerRecommend: { list } } = this.props
    const { visible, title, isUpdate } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            asid
            <Divider type='vertical' />
            <Input placeholder='asid' onChange={e => this.setState({ searchAsid: e.target.value })} style={{ width: 150 }} />
            <Divider type='vertical' />
            ssid
            <Divider type='vertical' />
            <Input placeholder='ssid' onChange={e => this.setState({ searchSSid: e.target.value })} style={{ width: 150 }} />
            <Divider type='vertical' />
            开播状态
            <Divider type='vertical' />
            <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 120 }} onChange={this.handleLiveStateChange}>
              <Option value='0'>全部</Option>
              <Option value='1'>开播中</Option>
              <Option value='2'>未开播</Option>
            </Select>
            <Divider type='vertical' />
            <Button style={{ marginLeft: 5 }} type='primary' onClick={value => this.searchBy(value)}>查询</Button>
            <Divider />

            <Button type='primary' onClick={this.showModal(false, null)}>新增</Button>
            <Divider type='vertical' />
            <Button type='danger' onClick={this.preDelete}>删除</Button>
            <Divider type='vertical' />
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onExport}>导出</Button>
            <Divider type='vertical' />

            <Upload action='/dating_match_bosssvr/city_partner/import_recommend_items' onChange={this.UpLoadOnChange} multiple={false} showUploadList={false}>
              <Button type='primary'>
                <UploadOutlined /> 批量导入
              </Button>
            </Upload>
            <Divider type='vertical' />
            <Button >
              <a href='http://makefriends.bs2dl.yy.com/【模板】城市合伙人推荐申请表.xlsx' onClick={this.downLoad}>导入模板下载</a>
            </Button>
            <Divider />
            <Table rowKey={(record, index) => index} rowSelection={this.rowSelection} dataSource={list} columns={this.columns} pagination={this.defaultPageValue} />
          </Form>
        </Card>
        <Modal onOk={this.handleDel(1)} title='确认删除？' onCancel={e => this.setState({ confirmVisible: false })} okText='删除' cancelText='取消' visible={this.state.confirmVisible}>
          <div align='center'>
            <br />{this.state.deleteConfirmMsg}
          </div>
        </Modal>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} {...formItemLayout} ref={form => { this.formRef = form }}>
            <FormItem label='asid' name='asid' rules={[{ required: true }]}>
              <Input readOnly={isUpdate} />
            </FormItem>
            <FormItem label='sid' name='sid' rules={[{ required: true }]}>
              <Input readOnly={isUpdate} />
            </FormItem>
            <FormItem label='ssid' name='ssid' rules={[{ required: true }]}>
              <Input readOnly={isUpdate} />
            </FormItem>
            <FormItem label='推荐标题' name='title' rules={[{ required: true }]}>
              <Input />
            </FormItem>
            <FormItem label='推荐权重' name='weight' rules={[{ required: true }]}>
              <Input />
            </FormItem>
            <FormItem label='推荐开始时间' name='recomm_start' rules={[{ required: true }]}>
              <Input placeholder='YYYY-MM-DD HH:mm' />
            </FormItem>
            <FormItem label='推荐结束时间' name='recomm_end' rules={[{ required: true }]}>
              <Input placeholder='YYYY-MM-DD HH:mm' />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default Index
