import {
  updateGrandPeriodProbabilityApi,
  getGrandPeriodProbabilityApi,
  getGrandPeriodProbabilityHistoryApi
} from './api'
import { message } from 'antd'

export default {
  namespace: 'hatkingProbability',

  state: {
    listen: null,
    oldProbability: '',
    probability: [], // 单个道具数额汇总配置
    historyList: []
  },

  reducers: {

    listen (state, { payload }) { return { ...state, listen: payload } },

    updateGrandPeriodProbabilityInfo (state, { payload }) {
      let oldValue = ''
      if (payload.probability !== 'undefined' && payload.probability.length) {
        oldValue = payload.probability
      }
      var data = [
        {
          key: 1,
          name: '发放监控汇总',
          stage1: '(0w-30w]',
          strategy1: '[各帽子不同参与类型下的期望奖励道具]最小的三个中随机取一个',
          stage2: '(30w-50w]',
          strategy2: '排除（各帽子不同参与类型下的期望奖励道具）的最大值外，随机标记',
          stage3: '(50w-100w]',
          strategy3: oldValue + '的概率取[各帽子不同参与类型下的期望奖励道具]最大的'
        },
        {
          key: 2,
          name: '发放监控汇总',
          stage1: '(100w-300w]',
          strategy1: '[各帽子不同参与类型下的期望奖励道具]最小的三个中随机取一个',
          stage2: '(300w-600w]',
          strategy2: '排除（各帽子不同参与类型下的期望奖励道具）的最大值外，随机标记',
          stage3: '(600w-正无穷)',
          strategy3: oldValue + '的概率取[各帽子不同参与类型下的期望奖励道具]最大的'
        }
      ]

      if (state.listen) {
        state.listen(payload)
      }

      return {
        ...state,
        oldProbability: oldValue,
        probability: data
      }
    },

    updateGrandPeriodProbabilityHistory (state, { payload }) {
      payload.sort((a, b) => b.reviewTime - a.reviewTime)
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }

      return {
        ...state,
        historyList: payload
      }
    }
  },

  effects: {
    * getGrandPeriodProbability ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getGrandPeriodProbabilityApi, payload)

      yield put({
        type: 'updateGrandPeriodProbabilityInfo',
        payload: list || {}
      })
    },

    * updateGrandPeriodProbability ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updateGrandPeriodProbabilityApi, payload)
      if (status === 0) {
        message.success('update success')
      } else {
        message.error('failed' + msg)
      }
    },

    * getGrandPeriodProbabilityHistory ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getGrandPeriodProbabilityHistoryApi, payload)

      yield put({
        type: 'updateGrandPeriodProbabilityHistory',
        payload: Array.isArray(list) ? list : []
      })
    }
  }
}
