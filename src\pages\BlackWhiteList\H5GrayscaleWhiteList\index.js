import React, { Component } from 'react'
import { connect } from 'dva'
import { Tabs, Card, Input, Form, Row, Col, Button, Table, Popconfirm, Select } from 'antd'
import { tableStyle, Inputlayout } from '@/utils/common'
import { DeleteOutlined } from '@ant-design/icons'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'

const namespace = 'h5GrayscaleWhiteList'

@connect(({ h5GrayscaleWhiteList }) => ({
  model: h5GrayscaleWhiteList
}))

class H5GrayscaleWhiteList extends Component {
  constructor (props) {
    super(props)
    this.refreshH5WhiteList(true)
  }

  // 标签页发生切换
  onTagChange = (record) => {
    if (record === '1') {
      this.initForm(true)
    }
    if (record === '3') {
      this.refreshH5WhiteList(true)
    }
    if (record === '2') {
      this.initForm(false)
    }
    if (record === '4') {
      this.refreshH5WhiteList(false)
    }
  }

  // 修改state中某个成员到特定值
  changeState = (name, newValue) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/updateState`,
      payload: { name: name, newValue: newValue }
    })
  }

  // 获取灰度白/黑名单列表数据(黑/白)
  refreshH5WhiteList = (isWhiteList = true) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getWhiteListData`,
      payload: { isWhiteList: isWhiteList }
    })
  }

  // 点击 添加标签页-添加按钮(黑/白)
  onAddBtnClick = (isWhiteList = true) => {
    const { dispatch } = this.props
    const { newSid, newSsid, newPlay } = this.state
    dispatch({
      type: `${namespace}/addWhiteList`,
      payload: {
        newSid: newSid,
        newSsid: newSsid,
        newPlay: (isWhiteList ? newPlay : 0),
        isWhiteList: isWhiteList,
        callback: this.initForm
      }
    })
  }

  // 确认删除选中的白名单(黑/白)
  onComfirmDel = (record, isWhiteList = true) => {
    const { dispatch } = this.props
    const { sid, ssid, play } = record
    dispatch({
      type: `${namespace}/delWhiteList`,
      payload: { sid: sid, ssid: ssid, play: play, isWhiteList: isWhiteList }
    })
  }

  // 清空输入表单(黑/白)
  initForm = (isWhite = true) => {
    this.setState({ newSid: '', newSsid: '', newPlay: '' })
    if (isWhite && this.fromRef) {
      this.fromRef.resetFields()
    } else if (this.fromRef2) {
      this.fromRef2.resetFields()
    }
  }

  // '添加白名单'标签页html代码
  addWhiteLIstHtml = () => {
    const { Option } = Select
    const { updating } = this.props.model
    return (
      <div>
        <Row>
          <Col span='24'>
            <Form {...Inputlayout} name='input-sid'
              initialValues={{ sid: '', ssid: '', play: '-99' }}
              ref={form => { this.fromRef = form }}>
              <Form.Item name='sid' label='顶级频道 [sid]:'>
                <Input placeholder='如50041789' allowClear
                  onChange={e => this.setState({ newSid: e.target.value })}
                  maxLength={20}
                />
              </Form.Item>
              <Form.Item name='ssid' label='子频道 [ssid]:'>
                <Input placeholder='如50041789 (0表示全部)' allowClear
                  onChange={e => this.setState({ newSsid: e.target.value })}
                  maxLength={20}
                />
              </Form.Item>
              <Form.Item name='play' label='玩法 [play]:'>
                <Select defaultValue='-99' onChange={(v) => this.setState({ newPlay: v })}>
                  <Option value='-99'>未选择</Option>
                  <Option value='0'>所有</Option>
                  <Option value='20'>跨业务PK</Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Button type='primary' htmlType='submit' loading={updating} onClick={() => this.onAddBtnClick(true)}>
                  添加
                </Button>
              </Form.Item>
            </Form>
          </Col>
        </Row>
      </div>
    )
  }

  // '添加黑名单'标签页html代码
  addBlackIistHtml = () => {
    const { updating } = this.props.model
    return (
      <div>
        <Row>
          <Col span='24'>
            <Form {...Inputlayout} name='input-sid'
              initialValues={{ sid: '', ssid: '', play: '-99' }}
              ref={form => { this.fromRef2 = form }}>
              <Form.Item name='sid' label='顶级频道 [sid]:'>
                <Input placeholder='如50041789' allowClear
                  onChange={e => this.setState({ newSid: e.target.value })}
                  maxLength={20}
                />
              </Form.Item>
              <Form.Item name='ssid' label='子频道 [ssid]:'>
                <Input placeholder='如50041789' allowClear
                  onChange={e => this.setState({ newSsid: e.target.value })}
                  maxLength={20}
                />
              </Form.Item>
              <Form.Item>
                <Button type='primary' htmlType='submit' loading={updating} onClick={() => this.onAddBtnClick(false)}>
                  添加
                </Button>
              </Form.Item>
            </Form>
          </Col>
        </Row>
      </div>
    )
  }

  // 灰度白名单-删除操作html代码
  deleteWhiteListHtml = (record) => {
    const { sid, ssid, play } = record
    let tmpStr = `确定要将 [sid=${sid} ssid=${ssid} play=${play}] 从灰度白名单删除吗？`
    return (
      <Popconfirm placement='bottom' title={tmpStr}
        okType='danger' okText='删除' cancelText='取消' onConfirm={() => this.onComfirmDel(record, true)}>
        <a href='#'><DeleteOutlined style={{ color: '#ff3535', fontSize: '1.2em' }} /></a>
      </Popconfirm>
    )
  }

  // 灰度黑名单-删除操作html代码
  deleteBlackBlackListHtml = (record) => {
    const { sid, ssid, play } = record
    let tmpStr = `确定要将 [sid=${sid} ssid=${ssid} play=${play}] 从灰度黑名单删除吗？`
    return (
      <Popconfirm placement='bottom' title={tmpStr}
        okType='danger' okText='删除' cancelText='取消' onConfirm={() => this.onComfirmDel(record, false)}>
        <a href='#'><DeleteOutlined style={{ color: '#ff3535', fontSize: '1.2em' }} /></a>
      </Popconfirm>
    )
  }

  // 格式化玩法数据
  playFormater (play) {
    if (play === 0) return '所有'
    if (play === 20) return '跨业务PK'
    return play + '?'
  }

  pageChange (pagination) {
    const { current, pageSize } = pagination
    this.changeState('currentPage', current)
    this.changeState('currentSize', pageSize)
  }

  pageChange2 (pagination) {
    const { current, pageSize } = pagination
    this.changeState('currentPage2', current)
    this.changeState('currentSize2', pageSize)
  }

  // ‘白名单展示列表标签页’html代码
  displayWhiteListHtml = () => {
    const { currentPage, currentSize } = this.props.model
    const columns = [
      {
        title: '#',
        render: (text, record, index) => ((currentPage - 1) * currentSize + index + 1)
      },
      { title: '短位频道',
        dataIndex: 'asid',
        sorter: { compare: (a, b) => a.asid - b.asid }
      },
      { title: '频道号',
        dataIndex: 'sid',
        sorter: { compare: (a, b) => a.sid - b.sid }
      },
      { title: '子频道',
        dataIndex: 'ssid',
        render: (val) => val === 0 ? '*' : val,
        sorter: { compare: (a, b) => a.ssid - b.ssid }
      },
      { title: '玩法',
        dataIndex: 'play',
        render: (val) => this.playFormater(val),
        sorter: { compare: (a, b) => a.play - b.play }
      },
      { title: '操作', render: (record) => this.deleteWhiteListHtml(record) }
    ]
    const { displayData } = this.props.model
    return (
      <Row >
        <Col span={24}>
          <Table columns={columns}
            dataSource={displayData}
            size='small'
            pagination={tableStyle}
            onChange={(pagination) => { this.pageChange(pagination) }}
            showSorterTooltip={false}
          />
        </Col>
      </Row>
    )
  }

  // ‘黑名单展示列表标签页’html代码
  displayBlackListtHtml = () => {
    const { currentPage2, currentSize2 } = this.props.model
    const columns = [
      {
        title: '#',
        render: (text, record, index) => ((currentPage2 - 1) * currentSize2 + index + 1)
      },
      { title: '短位频道',
        dataIndex: 'asid',
        sorter: { compare: (a, b) => a.asid - b.asid }
      },
      { title: '频道号',
        dataIndex: 'sid',
        sorter: { compare: (a, b) => a.sid - b.sid }
      },
      { title: '子频道',
        dataIndex: 'ssid',
        render: (val) => val === 0 ? '*' : val,
        sorter: { compare: (a, b) => a.ssid - b.ssid }
      },
      { title: '操作', render: (record) => this.deleteBlackBlackListHtml(record) }
    ]
    const { displayData2 } = this.props.model
    return (
      <Row >
        <Col span={24}>
          <Table columns={columns}
            dataSource={displayData2}
            size='small' pagination={tableStyle}
            showSorterTooltip={false}
            onChange={(pagination) => { this.pageChange2(pagination) }}
          />
        </Col>
      </Row>
    )
  }

  render () {
    const { TabPane } = Tabs
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='3' onChange={(record) => this.onTagChange(record)} type='card' size='large'>
            <TabPane tab='添加白名单' key='1'>
              {this.addWhiteLIstHtml()}
            </TabPane>
            <TabPane tab='H5模板灰度白名单' key='3'>
              {this.displayWhiteListHtml()}
            </TabPane>
            <TabPane tab='H5模板灰度黑名单' key='4'>
              {this.displayBlackListtHtml()}
            </TabPane>
            <TabPane tab='添加黑名单' key='2'>
              {this.addBlackIistHtml()}
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default H5GrayscaleWhiteList
