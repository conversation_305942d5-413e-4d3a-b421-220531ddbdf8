import { connect } from 'dva'
import React, { Component } from 'react'
import { <PERSON><PERSON>, Col, Divider, Form, Input, message, Modal, Popconfirm, Row, Select, Table, Tooltip } from 'antd'
import { configTypeOptions, defaultBoolElementTypeOptions, elementTypeOptions, elementTypeOptionsValidator, elementTypeWithoutObjectOptions, formatTimestamp, ruleDataTypeOptions } from '../common'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { CopyTwoTone } from '@ant-design/icons'
import { copyToClip, formatOptions, optionsToMultiLineString, parseOptionsFromMultiLineString } from '@/utils/common'

const namespace = 'RuleDataManage'
const defaultPageSize = 10

@connect(({ RuleDataManage }) => ({
  model: RuleDataManage
}))

class RuleDataManage extends Component { // 默认页面组件，不需要修改
  constructor (props) {
    super(props)
    this.initState()
    this.searchHandle()
    this.loadCommonSelectOptions()
  }

  initState = () => {
    this.state = {
      pagination: {
        pageSize: defaultPageSize,
        total: 0,
        current: 1,
        defaultCurrent: 1,
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        onChange: (page, pageSize) => {
          this.pageChange(page, pageSize)
        },
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      }
    }
  }

  updatePagination = (page, pageSize, total) => {
    const { pagination } = this.state
    pagination.current = page || 1
    pagination.pageSize = pageSize || defaultPageSize
    if (total !== undefined) {
      pagination.total = total
    }
    this.setState({ pagination: pagination })
    return pagination
  }

  // 分页信息变更
  pageChange = (page, pageSize, total) => {
    let pagination = this.updatePagination(page, pageSize, total)
    this.searchHandle(pagination.current, pagination.pageSize)
  }

  // 获取当前查询条件
  getQueryCondition = (page, size, cond) => {
    const { pagination } = this.state
    page = page || pagination.current || 1
    size = size || pagination.pageSize || defaultPageSize
    let pageInfo = {
      pageNo: page,
      pageSize: size
    }
    if (!cond) {
      return pageInfo
    }

    return Object.assign(cond, pageInfo)
  }

  loadCommonSelectOptions = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/listAllBusiness`
    })
  }

  // 处理查询事件
  searchHandle = (page, size, cond) => {
    const { dispatch } = this.props

    let query = this.getQueryCondition(page, size, cond)
    if (!query) {
      return
    }
    let self = this
    dispatch({
      type: `${namespace}/pageListRuleData`,
      payload: query,
      callback: (data) => {
        self.updatePagination(query.pageNo || 1, query.pageSize || defaultPageSize, data ? data.total : 0)
      }
    })
  }

  onQueryClick = (values) => {
    const { pagination } = this.state
    let size = pagination.pageSize || defaultPageSize
    this.searchHandle(1, size, values)
  }

  // 规则函数表头
  getColumns = () => {
    const { model: { businessList } } = this.props
    return [
      { title: '序号', dataIndex: 'index', align: 'left' },
      { title: '业务', dataIndex: 'businessID', align: 'left', render: (v) => formatOptions(v, businessList, '-', 'id', 'name') },
      {
        title: '引用ID',
        dataIndex: 'id',
        align: 'left',
        render: (v) => {
          let expression = '$[' + v + ']'
          return (<><span>{v}</span><CopyTwoTone style={{ marginLeft: 5 }} size={'small'} onClick={() => copyToClip(expression, expression)} /></>)
        }
      },
      { title: '值类型', dataIndex: 'dataType', align: 'left', render: (v) => formatOptions(v, elementTypeOptions) },
      {
        title: '名称',
        dataIndex: 'name',
        align: 'left',
        render: (v, record) => {
          return <Tooltip placement={'bottomLeft'} title={!record.desc || record.desc === '-' ? v : record.desc}>
            <div>{v || record.desc}</div>
          </Tooltip>
        }
      },
      {
        title: '可选项',
        dataIndex: 'options',
        align: 'left',
        render: (v, record) => {
          if (!v || v.length < 1) {
            return ''
          }
          return <Select value={v[0].value} style={{ width: 120 }} bordered={false} options={v} />
        }
      },
      { title: '配置类型', dataIndex: 'configType', align: 'left', render: (v) => formatOptions(v, configTypeOptions) },
      { title: '执行方式', dataIndex: 'type', align: 'left', render: (v) => formatOptions(v, ruleDataTypeOptions) },
      {
        title: '函数表达式',
        dataIndex: 'expression',
        align: 'left',
        render:
          (v) => {
            let showExpression = v
            if (v && v.length > 20) {
              showExpression = v.substring(0, 20) + '... ...'
            }
            if (!v) {
              return ''
            }
            return !v ? '' : (
              <div>
                <Tooltip placement={'bottomLeft'} title={v}>
                  <span>{showExpression}</span>
                </Tooltip>
                <CopyTwoTone style={{ marginLeft: 5 }} size={'small'} onClick={() => copyToClip(v, v)} />
              </div>
            )
          }
      },
      { title: '更新时间', dataIndex: 'updateTime', align: 'left', render: (v) => formatTimestamp(v) },
      { title: '操作人', dataIndex: 'lastOperator', align: 'left' },
      {
        title: '操作',
        key: 'operation',
        align: 'left',
        render:
          (text, item) => (
            <span>
              <a size='small' type='primary' onClick={() => this.showEditModal(item, 'edit')}>编辑</a>
              <span hidden={item.configType === 'INNER' || item.type === 'PARAMETER'}>
                <Divider type='vertical' /> {/* 分割线 */}
                <Popconfirm hidden={item.configType !== 'CUSTOM'} title='确认删除?' onConfirm={() => this.onDeleteRuleData(item)}><a style={{ color: 'red' }}>删除</a></Popconfirm>
              </span>
            </span>)
      }
    ]
  }

  showEditModal = (editItem, editMode) => {
    if (editMode === 'add') {
      if (this.formRefEdit) {
        this.formRefEdit.resetFields()
      }
      editItem = Object.assign(editItem, {
        configType: 'CUSTOM', type: 'EXPRESSION'
      })
    }

    editItem.optionsText = optionsToMultiLineString(editItem.options)
    if (this.formRefEdit) {
      this.formRefEdit.setFieldsValue(editItem)
    }
    this.setState({ editModalVisible: true, editItem: editItem, editMode: editMode })
  }

  closeEditModal = () => {
    if (this.formRefEdit) {
      this.formRefEdit.resetFields()
    }
    this.setState({ editModalVisible: false })
  }

  onEditModalOk = () => {
    this.formRefEdit.submit()
  }

  onEditFormSubmit = (values) => {
    const { editItem, editMode } = this.state
    let submitItem = editItem
    if (editMode === 'edit' && editItem.configType === 'INNER') { // 内置函数，只允许修改名称、介绍
      editItem.name = values.name
      editItem.desc = values.desc
    } else {
      submitItem = Object.assign(editItem, values)
    }

    let options = []
    if (values.dataType !== 'object') {
      options = parseOptionsFromMultiLineString(values.optionsText)
      if (values.dataType === 'bool' && options.length < 1) {
        options = defaultBoolElementTypeOptions
      }
    }
    editItem.options = options

    console.log('待提交的数据: ', submitItem, values)
    const reqType = editMode === 'add' ? `${namespace}/addRuleData` : `${namespace}/updateRuleData`
    const { dispatch } = this.props
    let self = this
    dispatch({
      type: reqType,
      payload: submitItem,
      callback: (rsp) => {
        if (rsp && rsp.status === 0) {
          self.setState({ editModalVisible: false, editItem: undefined })
          if (editMode === 'add') {
            self.searchHandle()
          }
        } else {
          message.error(editMode + ' 失败: ' + rsp.msg)
        }
      }
    })
  }

  onDeleteRuleData = (item) => {
    console.log('准备删除：', item)
    let self = this
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/removeRuleData`,
      payload: { id: item.id },
      callback: (rsp) => {
        if (rsp && rsp.status === 0) {
          self.searchHandle()
        } else {
          message.error('删除失败: ' + rsp.msg)
        }
      }
    })
  }

  // 编辑规则数据对话框
  renderEditModal = () => {
    const { editModalVisible, editItem, editMode } = this.state
    const { model: { businessList } } = this.props
    return (
      <Modal width={800} title={editMode === 'add' || !editItem ? '添加' : '编辑 【' + editItem.id + '】'} visible={editModalVisible} onCancel={this.closeEditModal} onOk={this.onEditModalOk}>
        <Form labelCol={{ span: 4 }} ref={(form) => {
          if (!this.formRefEdit) {
            form.setFieldsValue(editItem)
          }
          this.formRefEdit = form
        }} onFinish={this.onEditFormSubmit}>
          <Form.Item name='businessID' label={'业务'}>
            <Select disabled={editMode === 'edit' && editItem && editItem.configType === 'INNER'} placeholder={'请选择业务'} options={(businessList || []).map(v => {
              return { label: v.name, value: v.id }
            })} />
          </Form.Item>

          <Form.Item name='id' label={'引用ID'} required rules={[{ required: true, min: 1, max: 256, message: '请输入正确的引用ID' }]}>
            <Input disabled={editMode === 'edit' && editItem && editItem.configType === 'INNER'} placeholder={'输入引用ID'} />
          </Form.Item>

          <Form.Item name='dataType' label={'值类型'} required rules={[{ required: true, min: 1, max: 256, message: '请选择值类型' }]}>
            <Select disabled={editMode === 'edit' && editItem && editItem.configType === 'INNER'} placeholder={'请选择'} options={elementTypeWithoutObjectOptions} />
          </Form.Item>

          <Form.Item name='optionsText' label={'值可选项'} rules={elementTypeOptionsValidator} tooltip={{ placement: 'leftTop', title: '格式 [选项值:选项说明], 多个选项换行分隔' }}>
            <Input.TextArea rows={3} placeholder='可选项' />
          </Form.Item>

          <Form.Item name='name' label={'名称'} required rules={[{ required: true, min: 1, max: 256, message: '名称必填' }]}>
            <Input placeholder={'请输入'} />
          </Form.Item>

          <Form.Item name='desc' label={'数据说明'}>
            <Input.TextArea rows={3} placeholder={'请输入'} />
          </Form.Item>

          <Form.Item name='configType' label={'配置类型'} required>
            <Select disabled placeholder={'请选择'} options={configTypeOptions} />
          </Form.Item>

          <Form.Item name='type' label={'执行类型'} required>
            <Select disabled placeholder={'请选择'} options={ruleDataTypeOptions} />
          </Form.Item>

          <Form.Item name='expression' label={'函数表达式'} required tooltip={{ placement: 'topRight', title: '请再【规则函数】中复制' }} rules={[
            {
              validator: (_, value) => {
                if (editItem && editItem.type === 'EXPRESSION') {
                  // 表达式，必须填写
                  if (!value) {
                    return Promise.reject(new Error('表达式必填'))
                  }
                }
                return Promise.resolve()
              }
            }
          ]}>
            <Input.TextArea rows={3} disabled={editMode === 'edit' && editItem && editItem.configType === 'INNER'} placeholder={'请输入'} />
          </Form.Item>
        </Form>
      </Modal>
    )
  }

  // 渲染函数
  render () {
    const { route, model: { dataList, businessList } } = this.props
    const { pagination } = this.state
    const columns = this.getColumns()

    return (
      <>
        <PageHeaderWrapper title={route.name}>
          <Row style={{ marginBottom: '1em' }}>
            <Form layout={'inline'} ref={form => {
              this.formRefQuery = form
            }} onFinish={this.onQueryClick}>
              <Form.Item name={'keyword'} label={'关键字'}>
                <Input placeholder='keyword' style={{ width: 150 }} allowClear />
              </Form.Item>

              <Form.Item name={'businessID'} label={'归属业务'}>
                <Select placeholder='请选择归属业务' style={{ width: 120 }} options={(businessList || []).map(v => {
                  return { label: v.name, value: v.id }
                })} onChange={() => this.formRefQuery.submit()} filterOption allowClear />
              </Form.Item>

              <Form.Item name={'configType'} label={'配置类型'}>
                <Select placeholder='请选择配置类型' style={{ width: 120 }} options={configTypeOptions} onChange={() => this.formRefQuery.submit()} filterOption allowClear />
              </Form.Item>

              <Form.Item name={'type'} label={'执行方式'}>
                <Select placeholder='请选择执行方式' style={{ width: 120 }} options={ruleDataTypeOptions} onChange={() => this.formRefQuery.submit()} filterOption allowClear />
              </Form.Item>

              <Form.Item name={'dataType'} label={'值类型'}>
                <Select placeholder='值类型' style={{ width: 120 }} options={elementTypeWithoutObjectOptions} onChange={() => this.formRefQuery.submit()} filterOption allowClear />
              </Form.Item>

              <Button type='primary' htmlType='submit'>查询</Button>
              <Divider type={'vertical'} />
              <Button type='primary' onClick={() => {
                this.formRefQuery.resetFields()
                this.formRefQuery.submit()
              }}>重置</Button>
              <Divider type={'vertical'} />
              <Button type={'primary'} onClick={() => this.showEditModal({}, 'add')}>添加</Button>
            </Form>
          </Row>
          <Row style={{ marginBottom: '1em' }}>
            <Col span={24}>
              <Table columns={columns}
                dataSource={dataList}
                size='small'
                pagination={pagination}
                showSorterTooltip={false}
                rowKey={record => record.id}
              />
            </Col>
          </Row>

          {/* 渲染编辑对话框 */}
          {this.renderEditModal()}
        </PageHeaderWrapper>
      </>
    )
  }
}

export default RuleDataManage
