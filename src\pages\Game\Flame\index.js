import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card } from 'antd'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import PoolView from './tabs/poolView'
import PoolEdit from './tabs/poolEdit'
import FlameCrucialReport from './tabs/crucialReport'
import FlameDetailReport from './tabs/detailReport'

const poolId = 'main'

@connect(({ flameGame }) => ({
  model: flameGame
}))

class FlameGameConfig extends Component {
  state = {}
  componentDidMount = () => {}

  render () {
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='poolView' type='card' size='small'>
            <TabPane tab='玩法配置' key='poolView'>
              <PoolView poolId={poolId} />
            </TabPane>
            <TabPane tab='配置编辑or审批' key='poolEdit'>
              <PoolEdit poolId={poolId} />
            </TabPane>
            <TabPane tab='关键日报' key='crucialReport'>
              <FlameCrucialReport />
            </TabPane>
            <TabPane tab='明细报表' key='detailReport'>
              <FlameDetailReport />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default FlameGameConfig
