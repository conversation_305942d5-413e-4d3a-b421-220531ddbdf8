import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import PopImage from '@/components/PopImage'
import PicturesWall from '@/components/PicturesWall'
import { Table, Divider, Button, Card, Form, Popconfirm, Modal, Input, DatePicker, Select } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const namespace = 'hGameActivityPagesConfig'
const FormItem = Form.Item

const actSel = [
  { label: '请选择', value: 0 },
  { label: 'Banner位置', value: 1 },
  { label: '全部活动', value: 2 },
  { label: '特权&功能', value: 3 }
]

const typeSel = [
  { label: '请选择', value: 0 },
  { label: '活动', value: 1 },
  { label: '频道', value: 2 },
  { label: '主持', value: 3 }
]

@connect(({ hGameActivityPagesConfig }) => ({
  model: hGameActivityPagesConfig
}))
class HGameActivityPagesConfig extends Component {
  // column structs.
  columns = [
    { title: '序号', dataIndex: 'id', align: 'center' },
    { title: '活动名称', dataIndex: 'actName', align: 'center' },
    { title: '活动图', dataIndex: 'url', align: 'center', render: v => <PopImage value={v} /> },
    { title: '链接', dataIndex: 'jumpUrl', align: 'center', render: v => <a target='blank' href={v}>详细链接</a> },
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '频道', dataIndex: 'sid', align: 'center' },
    { title: '子频道', dataIndex: 'ssid', align: 'center' },
    { title: '活动版块', dataIndex: 'module', align: 'center', render: v => actSel.find(item => item.value === v).label },
    { title: '推荐维度', dataIndex: 'dimension', align: 'center', render: v => typeSel.find(item => item.value === v).label },
    { title: '开始时间', dataIndex: 'startTime', align: 'center' },
    { title: '结束时间', dataIndex: 'endTime', align: 'center' },
    { title: '权重', dataIndex: 'weight', align: 'center' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(true, record)}>更新</a><Divider type='vertical' />
          <Popconfirm title='Sure to delete?' onConfirm={this.handleDel(record.id)}>
            <a href=''>删除</a>
          </Popconfirm>
        </span>)
    }
  ]

  state = { visible: false, isUpdate: false, value: {} }

  // show modal
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (record) {
      v.jump = v.jumpUrl
      v.startTime = moment(record.startTime, 'YYYY-MM-DD HH:mm')
      v.endTime = moment(record.endTime, 'YYYY-MM-DD HH:mm')
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? '修改' : '新增' })
  }

  // hide modal
  onCancel = e => {
    this.setState({ visible: false })
  }

  onFinish = values => {
    const { dispatch } = this.props

    values.startTime = values.startTime.format('YYYY-MM-DD HH:mm')
    values.endTime = values.endTime.format('YYYY-MM-DD HH:mm')
    const url = this.state.isUpdate ? `${namespace}/updateItem` : `${namespace}/addItem`
    dispatch({
      type: url,
      payload: values
    })

    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // add and update
  handleSubmit = () => {
    this.formRef.submit()
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { id: key }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // get list from server.
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`
    })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  // content
  render () {
    const { route, model: { list } } = this.props
    const { visible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Button type='primary' onClick={this.showModal(false)}>添加</Button>
            <Divider />
            <Table rowKey='id' dataSource={list} columns={this.columns} pagination={{ pageSize: 20 }} />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.onCancel} onOk={this.handleSubmit}>
          <Form ref={form => { this.formRef = form }} onFinish={this.onFinish} {...formItemLayout}>
            <FormItem name='id' hidden>
              <Input />
            </FormItem>
            <FormItem label='活动名称' name='actName' >
              <Input />
            </FormItem>
            <FormItem label='活动奖励' name='actPrize' >
              <Input />
            </FormItem>
            <FormItem label='活动版块' name='module'>
              <Select options={actSel} />
            </FormItem>
            <FormItem label='推荐类型' name='dimension'>
              <Select options={typeSel} />
            </FormItem>
            <FormItem label='UID' name='uid'>
              <Input />
            </FormItem>
            <FormItem label='权重' name='weight'>
              <Input />
            </FormItem>
            <FormItem label='频道号' name='sid'>
              <Input />
            </FormItem>
            <FormItem label='子频道号' name='ssid'>
              <Input />
            </FormItem>
            <FormItem label='开始时间' name='startTime'>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm' />
            </FormItem>
            <FormItem label='结束时间' name='endTime'>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm' />
            </FormItem>
            <FormItem label='跳转链接' name='jump' >
              <Input />
            </FormItem>
            <FormItem label='上传图片' name='url'>
              <PicturesWall />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default HGameActivityPagesConfig
