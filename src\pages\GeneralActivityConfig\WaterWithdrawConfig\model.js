import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const getConfigData = genGetRequireTemplate('/boss/water_withdraw/get_config', 'configData')
const updateConfigData = genUpdateTemplate('/boss/water_withdraw/update_config')

export default {
  namespace: 'waterWithdraw',
  state: {
    configData: {}
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getConfigData,
    updateConfigData
  }
}
