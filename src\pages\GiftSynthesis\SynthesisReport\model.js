import {
  dailyFlowReportList,
  detailFlowReportList,
  realTimeData
} from './api'
import { message } from 'antd'

export default {
  namespace: 'SynthesisReport',

  state: {
    dailyReportList: [],
    detailReportList: [],
    realtimeList: []
  },

  reducers: {

    updateDailyReportList (state, { payload }) {
      for (let i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        dailyReportList: payload
      }
    },

    updateDetailReportList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        detailReportList: payload
      }
    },

    updateRealtimeList (state, { payload }) {
      for (let i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        realtimeList: payload
      }
    }
  },

  effects: {

    * getDailyReportList ({ payload }, { call, put }) {
      try {
        const { data: { list } } = yield call(dailyFlowReportList, payload)

        yield put({
          type: 'updateDailyReportList',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        message.error('e:' + e)
      }
    },

    * getDetailReportList ({ payload }, { call, put }) {
      try {
        const { data: { list } } = yield call(detailFlowReportList, payload)

        yield put({
          type: 'updateDetailReportList',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        message.error('e:' + e)
      }
    },

    * getRealTimeList ({ payload }, { call, put }) {
      try {
        const { data: { list } } = yield call(realTimeData, payload)

        yield put({
          type: 'updateRealtimeList',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        message.error('e:' + e)
      }
    }
  }
}
