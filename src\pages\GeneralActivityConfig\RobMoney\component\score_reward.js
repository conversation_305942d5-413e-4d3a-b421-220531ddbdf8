import React, { Component } from 'react'
import { <PERSON>, Divider, Button, Modal, Form, Table, message, Input, InputNumber } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'
import styles from '../index.module.less'
import { connect } from 'dva'
import { CSVLink } from 'react-csv'

const namespace = 'robMoney' // model 的 namespace
const FormItem = Form.Item

@connect(({ rob<PERSON>oney }) => ({ // model 的 namespace
  model: robMoney // model 的 namespace
}))
class ScoreRewardComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false
    }
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getScoreRewardList`
    })
  }

  // 需要修改
  columns = [
    { title: 'Uid', dataIndex: 'uid', align: 'center' },
    { title: '当前荣耀分', dataIndex: 'value', align: 'center' },
    { title: '操作',
      align: 'center',
      render: (text, record) => (
        <div>
          <a><DeleteOutlined style={{ marginRight: 10 }} onClick={this.showModal(true, record)} /></a>
        </div>
      )
    }
  ]

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.score = v.value
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, isUpdate: isUpdate, title: '积分转赠' })
  }

  // 隐藏弹窗，不需要修改
  hideModal = () => {
    this.setState({ visible: false })
  }

  onFinish = values => {
    const { dispatch } = this.props

    if (values.grantScore === 0) {
      message.error('转赠积分为0')
      return
    }

    if (values.grantScore > values.value) {
      message.error('转赠积分不得大于奖励积分', 10)
      return
    }

    if (values.uid === values.grant) {
      message.error('不得转赠给自己', 10)
      return
    }

    // console.log(values, list)
    dispatch({
      type: `${namespace}/upsetScoreRewardList`,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 更新与删除
  handleSubmit = () => {
    this.formRef.submit()
  }

  handleRemove = id => () => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/removeDescCfgList`,
      payload: { id: id }
    })
  }

  // 预结算
  handlePreSettlement = () => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/preScoreRewardList`
    })
  }

  // 结算
  handleSettlement = () => {
    const { dispatch } = this.props

    this.handlePreSettlement()

    dispatch({
      type: `${namespace}/settlmentScoreRewardList`
    })
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onRowClassName = (record) => {
    // console.log(record)
    return record.reward === 3 ? styles.grayColumn : ''
  }

  export = () => {
    const { model: { scoreList } } = this.props
    return scoreList.map(i => { return { UID: i.uid, Score: i.value } })
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { scoreList } } = this.props
    const { visible, title } = this.state
    const formItemLayout = { // 不需要修改
      labelCol: {
        xs: { span: 4 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 20 },
        sm: { span: 20 }
      }
    }

    return (
      <Card>
        <CSVLink data={this.export()} target='_blank'><Button type='primary' style={{ marginRight: 20 }}>导出</Button></CSVLink>
        <Divider />
        <Table rowClassName={this.onRowClassName} rowKey={(record, index) => index} dataSource={scoreList} columns={this.columns} size='small' pagination={false} /> {/* 显示的列表 */}

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} ref={form => { this.formRef = form }} {...formItemLayout}>
            <FormItem name='uid' hidden>
              <Input />
            </FormItem>
            <FormItem name='score' hidden>
              <Input />
            </FormItem>
            <FormItem name='grant' label='转移给'>
              <InputNumber style={{ width: '100%' }} />
            </FormItem>
            <FormItem name='grantScore' label='转移分值'>
              <InputNumber style={{ width: '100%' }} />
            </FormItem>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default ScoreRewardComponent
