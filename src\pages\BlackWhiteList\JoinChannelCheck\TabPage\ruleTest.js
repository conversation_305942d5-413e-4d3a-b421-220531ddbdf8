import React, { Component } from 'react'
import { connect } from 'dva'
import { Row, Button, Input, Select, Col, Form, message } from 'antd'
import { udbAppIDOptions } from './common'

const namespace = 'joinChannelCheck'

@connect(({ joinChannelCheck }) => ({
  model: joinChannelCheck
}))

class JoinChanneleRuleTest extends Component {
  state = {}
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  onSubmit = (f) => {
    this.callModel('playModeRuleEntry', {
      params: f,
      cbFunc: (blocked) => {
        if (blocked) {
          message.warning('拦截进入频道')
        } else {
          message.success('允许进入频道')
        }
      }
    })
  }

  render () {
    return (
      <div>
        <Row style={{ marginBottom: '1em' }}>
          <text style={{ color: '#339999ba' }}> 通过模拟特定版本的客户端进入频道, 简单测试规则是否生效 </text>
        </Row>
        <Row >
          <Form onFinish={(v) => { this.onSubmit(v) }} ref={(v) => { this.formRef = v }}
            initialValues={{ }} labelCol={{ span: 5 }} style={{ width: '20em' }}>
            <Form.Item name='udbAppId' label='终端'>
              <Select options={udbAppIDOptions} />
            </Form.Item>
            <Form.Item name='version' label='版本'>
              <Input placeholder='客户端版本' />
            </Form.Item>
            <Form.Item name='sid' label='sid'>
              <Input placeholder='进入的频道id' />
            </Form.Item>
            <Form.Item name='ssid' label='ssid'>
              <Input placeholder='进入的子频道id' />
            </Form.Item>
            <Col offset={5}>
              <Button htmlType='submit'>测试结果</Button>
            </Col>
          </Form>
        </Row>
      </div>
    )
  }
}

export default JoinChanneleRuleTest
