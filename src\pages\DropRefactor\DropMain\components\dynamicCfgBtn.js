import React, { Component } from 'react'
import { Typo<PERSON>, Modal, Switch, Input, Form, Select, Divider, Button, Tooltip, Popover, InputNumber, message } from 'antd'
import { InfoBoard } from '@/components/SimpleComponents'
import { getFieldsValueByPoolID } from '../../globalConfig'
import { limitModeOptions } from './list_common'
import { checkIsNumber } from '@/utils/common'

const { Link, Text } = Typography
const { Item } = Form
const defaultValue = { poolID: 0, mode: 1, isOpen: false, incomeTypePreview: '???', incomeBegin: 0, incomeEnd: 0, incomeStep: 0, addition: 0, repeatDay: 0, offsetDay: 0, offsetLimit: 0 }

class DynamicCfgButton extends Component {
  state = {
    editValue: {},
    modalVisible: false
  }

  componentDidMount = () => {
    const { value } = this.props
    this.formRef.setFieldsValue(value)
    this.fromRefMonitor(value)
  }

  // 弹出编辑模态框, 会重置数据
  showEditerModal = () => {
    const { value } = this.props
    this.fromRefMonitor(value || defaultValue)
    this.setState({ modalVisible: true })
  }

  // 关闭编辑模态框
  closeEditerModal = () => {
    this.setState({ modalVisible: false })
  }

  // 表单每次更新时调用, 维护临时配置值
  fromRefMonitor = (v) => {
    if (!v) return ''
    if (v.isOpen && !v.mode) { // 将mode的默认值调成1
      v.mode = 1
    }
    this.setState({ editValue: v })
    const cp = { ...v, incomeTypePreview: this.inComeTypeFormater(v.mode, v.poolID) }
    this.formRef.setFieldsValue(cp)
  }

  inComeTypeFormater = (mode, poolID) => {
    const { inComeType } = this.props
    if (inComeType) {
      return inComeType
    }
    if (mode === 1 || mode === 2) {
      return mode === 2 ? `${getFieldsValueByPoolID(poolID, 'Name')}道具池发放道具` : `${getFieldsValueByPoolID(poolID, 'BusinessName')}全业务`
    }
    return '-'
  }

  // 更新外层配置值
  comfirmUpdateOuterValue = () => {
    const { onChange } = this.props
    const { editValue } = this.state
    let reason = this.checkFromValue(editValue)
    if (!editValue.isOpen) {
      editValue.mode = 0
    }
    if (reason) {
      message.warn(reason)
      return
    }
    this.setState({ modalVisible: false })
    onChange(editValue)
  }

  // 检查参数
  checkFromValue = (v) => {
    if (!checkIsNumber(v.incomeBegin) || !checkIsNumber(v.incomeEnd) || !checkIsNumber(v.incomeStep) ||
       !checkIsNumber(v.addition) || !checkIsNumber(v.repeatDay) || !checkIsNumber(v.offsetDay) || !checkIsNumber(v.offsetLimit)) {
      return '信息填写有误,请检查~'
    }
    if (v.incomeBegin < 0) {
      return '“生效收入起点” 不能小于0'
    }
    if (v.incomeEnd < 0) {
      return '“生效收入终点” 不能小于0'
    }
    if (v.incomeBegin + v.incomeBegin > 0 && v.incomeBegin >= v.incomeEnd) { // 填0时可以相等，否则不能终点必须大于起点
      return '"生效收入终点" 必须大于 "生效收入起点"'
    }

    if (v.incomeStep <= 0) {
      return '"生效收入加成" 必须大于0'
    }
    if (v.repeatDay < 0 || v.offsetDay < 0) {
      return '"重复限制天数" 或 "偏移限制天数" 不能小于0'
    }
    if (v.addition < 0) {
      return '"每日上限增加数量" 必须大于等于0'
    }
    return ''
  }

  // 动态发放道具选项调整
  fixDynamicModeOptions = (before, hidePoolMode) => {
    if (hidePoolMode !== true) {
      return before
    }
    let after = [...before]
    after.forEach(item => {
      if (item.value === 2) {
        item.disabled = true
      }
    })
    return after
  }

  compereValue = (before, after) => {
    const { diffMode } = this.props
    if (!diffMode) return after
    if (before === undefined) before = ''
    if (before === after) return <Text>{after}</Text>
    return <Text type='danger'><Text type='secondary'>{`${before} => `}</Text>{after}</Text>
  }

  modeFormater = (mode) => {
    return limitModeOptions.find(item => item.value === mode)?.label
  }

  render () {
    const { value: initvalValue, cmpValue, isEdit, propsName, limit, diffMode, globalModeOnly, inComeType } = this.props
    // console.debug('initvalValue===>', initvalValue)
    const { modalVisible, editValue } = this.state
    const infoColumns = [
      { label: '是否开启', dataIndex: 'isOpen', span: 24, render: (v) => { return this.compereValue(cmpValue?.isOpen ? '开启' : '关闭', v ? '开启' : '关闭') } },
      { label: '动态发放道具', dataIndex: 'mode', span: 24, render: (v) => { return this.compereValue(this.modeFormater(cmpValue?.mode), this.modeFormater(v)) } },
      { label: '大道具名称', dataIndex: 'mode', span: 24, render: (v) => { return propsName } },
      { label: '收入口径', dataIndex: 'mode', span: 24, render: (v, r) => { return inComeType || this.compereValue(this.inComeTypeFormater(cmpValue?.mode, r.poolID), this.inComeTypeFormater(v, r.poolID)) } },
      { label: '生效收入起点/元', dataIndex: 'incomeBegin', span: 24, render: (v) => this.compereValue(cmpValue?.incomeBegin, v) },
      { label: '生效收入终点/元', dataIndex: 'incomeEnd', span: 24, render: (v) => this.compereValue(cmpValue?.incomeEnd, v) },
      { label: '生效收入加成/元', dataIndex: 'incomeStep', span: 24, render: (v) => this.compereValue((cmpValue?.incomeStep === 0 ? '不生效' : cmpValue?.incomeStep), (v === 0 ? '不生效' : v)) },
      { label: '每日上限增加数量', dataIndex: 'addition', span: 24, render: (v) => this.compereValue(cmpValue?.addition, v) },
      { label: '重复限制天数', dataIndex: 'repeatDay', span: 24, render: (v) => this.compereValue((cmpValue?.repeatDay === 0 ? '不限制' : cmpValue?.repeatDay), (v === 0 ? '不限制' : v)) },
      { label: '偏移限制天数', dataIndex: 'offsetDay', span: 24, render: (v) => this.compereValue((cmpValue?.offsetDay === 0 ? '不限制' : cmpValue?.offsetDay), (v === 0 ? '不限制' : v)) },
      { label: '偏移限制金额/元', dataIndex: 'offsetLimit', span: 24, render: (v) => this.compereValue(cmpValue?.offsetLimit, v) }
    ]

    const infoBoxContent = <div style={{ width: '20em' }}><InfoBoard key={initvalValue} columns={infoColumns} dataSource={initvalValue || {}} divider={false} /></div>

    let placeholder = ''
    let isChangeTip = ''
    if (diffMode && JSON.stringify(initvalValue) !== JSON.stringify(cmpValue)) {
      isChangeTip = <Text type='danger'>*</Text>
    }

    if (!isEdit) { // 查看模式
      placeholder = <>{ initvalValue == null || initvalValue.isOpen === false
        ? <Popover placement='leftTop' trigger='hover' content={isChangeTip === '' ? '关闭' : infoBoxContent}>
          <Text type='secondary'>{isChangeTip}关闭</Text>
        </Popover>
        : <Popover placement='leftTop' trigger='hover' content={infoBoxContent}><Text type='success'>{isChangeTip}开启</Text></Popover>
      }</>
    }
    if (isEdit) { // 编辑模式
      placeholder = <>{ initvalValue && initvalValue.isOpen
        ? <Text>已开启, <Link onClick={() => this.showEditerModal()}>编辑</Link></Text>
        : <Button type='link' onClick={() => this.showEditerModal()}>开启</Button>}</>
    }
    if (limit < 0) { // 禁用模式
      placeholder = <Tooltip title='不支持配置'><Text type='secondary' disabled> - </Text></Tooltip>
    }

    return (
      <div>
        {placeholder}
        <Modal forceRender title='动态发放道具配置' visible={modalVisible}
          onCancel={() => this.closeEditerModal()}
          onOk={() => { this.comfirmUpdateOuterValue() }} >
          <Form labelCol={{ span: 9 }} forceRender ref={from => { this.formRef = from }} initialValues={defaultValue} onFinish={(v) => { console.debug(v) }} onValuesChange={(v, r) => this.fromRefMonitor(r)} >
            <Item label='道具池ID' name='poolID' hidden>
              <Input />
            </Item>
            <Item label='是否支持动态配置' name='isOpen' getValueFromEvent={(v) => { return !!v }} valuePropName='checked' >
              <Switch checkedChildren='开启' unCheckedChildren='关闭' />
            </Item>
            <div hidden={editValue && editValue.isOpen === false}>
              <Divider />
              <Item label='动态发放道具' name='mode'>
                <Select options={this.fixDynamicModeOptions(limitModeOptions, globalModeOnly)} />
              </Item>
              <Item label='大道具名称'>
                <Input disabled value={propsName} />
              </Item>
              <Item label='收入口径' name='incomeTypePreview'>
                <Input disabled />
              </Item>
              <Item label='生效收入起点/元' name='incomeBegin' rules={[{ required: true }]} tooltip='开始生效的收入'>
                <InputNumber />
              </Item>
              <Item label='生效收入终点/元' name='incomeEnd' rules={[{ required: true }]} tooltip='结束生效的收入'>
                <InputNumber />
              </Item>
              <Item label='生效收入加成/元' name='incomeStep' rules={[{ required: true }]} tooltip='生效起点之上每增加X元,大道具自动增加'>
                <InputNumber />
              </Item>
              <Item label='每日上限增加数量' name='addition' rules={[{ required: true }]} tooltip='大道具上限自动增加X个'>
                <InputNumber />
              </Item>
              <Item label='重复限制天数' name='repeatDay' rules={[{ required: true }]} tooltip='X天内在该业务空投不能重复中该奖励; 0代表不限制'>
                <InputNumber />
              </Item>
              <Item label='偏移限制天数' name='offsetDay' rules={[{ required: true }]} tooltip='X天内该业务空投下累计偏移; 0代表不限制'>
                <InputNumber />
              </Item>
              <Item label='偏移限制金额/元' name='offsetLimit' rules={[{ required: true }]} tooltip='累计偏移 (用户抽取道具金额-获得金额) 大于X元才能中; 正数代表用户累计失败'>
                <InputNumber />
              </Item>
            </div>

          </Form>
        </Modal>
      </div>
    )
  }
}
export default DynamicCfgButton
