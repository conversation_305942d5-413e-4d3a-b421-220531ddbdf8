import React, { Component } from 'react'
import { connect } from 'dva'
import { Row, Col, Space, Divider, Button, DatePicker, Table, Select, Tooltip } from 'antd'
import { businessTypeOptisons, businessTypeFormater, productTypeFormater, productTypeOptions } from './common'
import { onExportExcel } from '@/utils/common'
import moment from 'moment'
const namespace = 'fragmentReport'

@connect(({ fragmentReport }) => ({
  model: fragmentReport
}))

class ProductReportTag extends Component {
  state = {
    queryTimeRange: [moment().add(-7, 'days').startOf('days'), moment().endOf('days')],
    queryBusinessType: 1000,
    queryProductType: 1000
  }

  componentDidMount = () => {
    const { queryBusinessType, queryTimeRange, queryProductType } = this.state
    this.queryData(queryTimeRange, queryBusinessType, queryProductType)
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  queryData = (timeRange, businessType, productType) => {
    let start = ''
    let end = ''
    if (timeRange && timeRange[0] && timeRange[1]) {
      start = timeRange[0].format('YYYYMMDD')
      end = timeRange[1].format('YYYYMMDD')
    }
    this.callModel('queryProductList', {
      params: {
        startDate: start,
        endDate: end,
        businessType: businessType,
        productType: productType
      }
    })
  }

  mainColumns = [
    { title: '日期', dataIndex: 'date' },
    { title: '业务', dataIndex: 'businessType', render: (v) => { return businessTypeFormater(v) } },
    { title: '发放碎片玩法', dataIndex: 'productType', render: (v) => { return productTypeFormater(v) } },
    { title: '发放碎片价值/紫水晶', dataIndex: 'rewardValue' }
  ].map(item => {
    item.align = 'center'
    return item
  })

  render () {
    const { queryBusinessType, queryTimeRange, queryProductType } = this.state
    const { productList } = this.props.model

    return (
      <div>
        <Row>
          <Col span={24}>
            <Space>
              <DatePicker.RangePicker format={'YYYY-MM-DD'} value={queryTimeRange} onChange={v => this.setState({ queryTimeRange: v })} />
              <Tooltip title='业务类型'>
                <Select style={{ width: '6em' }} options={businessTypeOptisons} value={queryBusinessType} onChange={v => this.setState({ queryBusinessType: v })} />
              </Tooltip>
              <Tooltip title='玩法类型'>
                <Select style={{ width: '12em' }} options={productTypeOptions} value={queryProductType} onChange={v => this.setState({ queryProductType: v })} />
              </Tooltip>
              <Button onClick={() => { this.queryData(queryTimeRange, queryBusinessType, queryProductType) }} type='primary'>查询</Button>
              <Button onClick={() => { onExportExcel(this.mainColumns, productList, '获得装扮碎片日报.xlsx') }} >导出</Button>
            </Space>
          </Col>
          <Divider />
          <Col span={24}>
            <Table columns={this.mainColumns} dataSource={productList} />
          </Col>
        </Row>
      </div>
    )
  }
}

export default ProductReportTag
