import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import { connect } from 'dva'
// import EssenceExchangeStatsComponent from './components/essence_exchange_stats'
import EssenceExchangeDetailComponent from './components/essence_exchange_detail'

const namespace = 'essenceExchange' // model 的 namespace
const TabPane = Tabs.TabPane

@connect(({ essenceExchange }) => ({ // model 的 namespace
  model: essenceExchange // model 的 namespace
}))
class EssenceExchangeReport extends Component { // 默认页面组件，不需要修改
  /** *****************************非活动流程配置文件更新与获取****************************************************************/
  tabOnChange = type => activityKey => {
    console.log(type, activityKey)
    if (type !== undefined || type != null) {
      activityKey = type
    }
  }

  /** *******************************页面布局*************************************************************/
  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs onChange={this.tabOnChange()} type='card'>
          <TabPane tab='精粹兑换日报' key='2'>
            <EssenceExchangeDetailComponent modelName={namespace} model={this.props.model} />
          </TabPane>
        </Tabs>

      </PageHeaderWrapper>
    )
  }
}

export default EssenceExchangeReport // 保证唯一
