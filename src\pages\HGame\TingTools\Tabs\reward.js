import { connect } from 'dva'
import React, { Component } from 'react'
import { Row, Col, Table, Modal, Typography, Tooltip, Input } from 'antd'
import { InfoBoard } from '@/components/SimpleComponents'
import { timeFormater } from '@/utils/common'
import SearchParams from '@/components/SimpleComponents/searchParams'

const { Link, Text } = Typography

const namespace = 'hgameTingTools'

@connect(({ hgameTingTools }) => ({
  model: hgameTingTools
}))

class RewardList extends Component {
  state = {
    detailVisible: false,
    refruseVisible: false,
    paramsVal: {}
  }

  // 查询条件
  parmasColumn = [
    { label: '频道ID', fieldName: 'channelID', inputType: 'InputNumber', defaultValue: null, extProps: { style: { width: '10em' } } },
    { label: '申请时间', fieldName: 'submitTime', inputType: 'TimeRange', defaultValue: [null, null], extProps: { style: { width: '16em' }, showTime: true, allowClear: true } },
    { label: '活动时间', fieldName: 'actTime', inputType: 'TimeRange', defaultValue: [null, null], extProps: { style: { width: '16em' }, showTime: true, allowClear: true } }
  ]

  componentDidMount = () => {
    this.getRewardList()
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  getRewardList = () => {
    this.callModel('getRewardList')
  }

  // 查询活动详情
  showDetailByActID= (id) => {
    this.callModel('getRewardDetail', {
      params: { id: id },
      isDetailMode: true,
      cbFunc: (ret) => {
        this.setState({ detailVisible: true })
      }
    })
  }

  // 根据参数过滤申请列表
  approvalLitsFilter = (before) => {
    const { paramsVal } = this.state
    const { channelID, submitTime1, submitTime2, actTime1, actTime2 } = paramsVal
    let after = []
    before.forEach(item => {
      if (channelID && item.sid !== channelID && item.asid !== channelID) return
      if (submitTime1 && item.submitTime < submitTime1) return
      if (submitTime2 && item.submitTime > submitTime2) return
      if (actTime1 && item.startTime < actTime1) return
      if (actTime2 && item.startTime > actTime2) return
      after.push(item)
    })
    return after
  }

  statusDescHightLighter = (desc, tag) => {
    return <Tooltip title={tag}>
      <Text type={['', 'secondary'][['已结束', '已下发'].indexOf(desc)]}>{desc}</Text>
    </Tooltip>
  }

  render () {
    const { rewardDetail, rewardList } = this.props.model
    const { detailVisible } = this.state
    const columns = [
      { title: '活动ID', dataIndex: 'id', render: (v, r) => { return <Tooltip title={r.id}>{v}</Tooltip> } },
      { title: '频道ID', dataIndex: 'sid' },
      { title: '公会短号', dataIndex: 'asid' },
      { title: '发起人yy', dataIndex: 'imid' },
      { title: '活动时间', dataIndex: 'startTime', sorter: (a, b) => a.startTime - b.startTime, render: (v) => { return timeFormater(v) } },
      { title: '申请时间', dataIndex: 'submitTime', sorter: (a, b) => a.submitTime - b.submitTime, render: (v) => { return timeFormater(v) } },
      { title: '状态', dataIndex: 'statusDesc', render: (v, r) => { return this.statusDescHightLighter(v, r.tag) } },
      { title: '操作',
        dataIndex: 'id',
        render: (v) => {
          return <Link onClick={() => this.showDetailByActID(v)}>详情</Link>
        } }
    ]

    const tingColumns = [
      { title: '频道ssid', dataIndex: 'ssid' },
      { title: '频道昵称', dataIndex: 'name' }
    ]

    const tingRankColumns = [
      { title: '排名', dataIndex: 'rank', render: (v) => { return `第${v}名` } },
      { title: '房管UID', dataIndex: 'adminUid' },
      { title: '频道ssid', dataIndex: 'ssid' },
      { title: '厅战流水', dataIndex: 'flow', render: (v) => { return `${(v / 100).toFixed(2)}元` } },
      { title: '奖励数量(个)', dataIndex: 'reward' },
      { title: '道具价值(元)', dataIndex: 'rewardVal', render: (v) => { return `${(v / 1000).toFixed(2)}` } }
    ]

    const userRankColumns = [
      { title: '排名', dataIndex: 'rank', render: (v) => { return `第${v}名` } },
      { title: '用户昵称', dataIndex: 'nick' },
      { title: '用户UID', dataIndex: 'uid' },
      { title: '厅战流水', dataIndex: 'flow', render: (v) => { return `${(v / 100).toFixed(2)}元` } },
      { title: '黑卡', dataIndex: 'cardReward', render: (v) => { return `${v || 0}天` } },
      { title: '进场秀', dataIndex: 'showReward', render: (v) => { return `${v || 0}天` } }
    ]

    const detailColumns = [
      { label: '活动ID', span: 24, dataIndex: 'id' },
      { label: '发起公会短号', span: 24, dataIndex: 'asid' },
      { label: '厅战开始时间', span: 24, dataIndex: 'startTime', render: (v) => { return timeFormater(v) } },
      { label: '厅战结束时间', dataIndex: 'endTime', render: (v) => { return timeFormater(v) } },
      { label: '时长', span: 24, dataIndex: 'duration', render: (v) => { return `${v} 小时` } },
      { label: '道具配置',
        span: 24,
        dataIndex: 'rewardConfig',
        render: (v) => {
          v = v || []
          return <Row>{v.map((item, index) => {
            return <Col span={24} style={{ marginBottom: '5px' }}>
              <Text type='secondary'>第{index + 1}名 --
                <Input value={`大于 ${item.flowLimit / 100} 元`} disabled style={{ width: '8em' }} size='small' /><Text > -- </Text>
                <Input value={`${item.owReward} 礼物, (${(item.owRewardVal / 1000).toFixed(2)}元)`} disabled style={{ width: '12em' }} size='small' /><Text > -- </Text>
                <Input value={`${item.extraReward} 礼物, (${(item.extraRewardVal / 1000).toFixed(2)}元)`} disabled style={{ width: '12em' }} size='small' />
              </Text>
            </Col>
          })}</Row>
        } },
      { label: '参与房管厅',
        span: 24,
        dataIndex: 'channelList',
        render: (v) => {
          return <Table columns={tingColumns} dataSource={v} size='small' pagination={{ pageSize: 4 }} bordered rowKey={(item) => { return item.ssid }} />
        } },
      { label: '厅奖励明细',
        span: 24,
        dataIndex: 'tingRank',
        render: (v) => {
          return <Table columns={tingRankColumns} dataSource={v} size='small' pagination={{ pageSize: 4 }} borderd rowKey={item => { return item.rank }} />
        } },
      { label: '用户奖励明细',
        span: 24,
        dataIndex: 'userRank',
        render: (v) => {
          return <Table columns={userRankColumns} dataSource={v} size='small' pagination={{ pageSize: 4 }} borderd rowKey={item => { return item.rank }} />
        } }
    ]

    return (
      <Row>
        <Col span={24}>
          <SearchParams formatTimeRange columns={this.parmasColumn} onChange={(v) => { this.setState({ paramsVal: v }) }} />
        </Col>

        <Col span={24}>
          <Table columns={columns} dataSource={this.approvalLitsFilter(rewardList)} size='small' pagination={{ pageSize: 50 }} />
        </Col>

        {/* 详细信息模态框 */}
        <Modal visible={detailVisible} title='厅战活动详情' footer={false} onCancel={() => this.setState({ detailVisible: false })} width='45em'>
          <Row>
            <Col span={24}>
              <InfoBoard columns={detailColumns} dataSource={rewardDetail} />
            </Col>
          </Row>
        </Modal>

      </Row>
    )
  }
}

export default RewardList
