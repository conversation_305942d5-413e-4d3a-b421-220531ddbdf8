import { searchTime } from './api'
import { message } from 'antd'

export default {
  namespace: 'beansLotteryDataReport',

  state: {
    list: []
  },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * searchTime ({ payload }, { call, put }) {
      const { data: { status, msg, list } } = yield call(searchTime, payload)
      if (status === 0) {
        yield put({
          type: 'updateList',
          payload: list
        })
      } else {
        message.error('failed' + msg)
      }
    }
  }
}
