import { genGetRequireTemplate, genUpdateTemplate } from '@/utils/common'

const listCrossChLive = genGetRequireTemplate('/room_manager/boss/crossChLive/whitelist', 'crossChliveList')
const addCrossChLive = genUpdateTemplate('/room_manager/boss/crossChLive/add_white_list')
const delCrossChLive = genUpdateTemplate('/room_manager/boss/crossChLive/del_white_list')
const listRoomMgr = genGetRequireTemplate('/room_manager/boss/crossChLive/list_room_mgr', 'roomMgrList')
const listStarCompere = genGetRequireTemplate('/room_manager/boss/crossChLive/list_star_compere', 'starCompereList')

export default {
  namespace: 'crossLive',
  state: {
    crossChliveList: [],
    roomMgrList: [],
    starCompereList: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    listCrossChLive,
    delCrossChLive,
    addCrossChLive,
    listRoomMgr,
    listStarCompere
  }
}
