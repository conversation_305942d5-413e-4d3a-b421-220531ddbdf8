import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import { connect } from 'dva'
import BeanBalanceDetailComponent from './components/bean_balance_detail'
import EnergyBalanceDetailComponent from './components/energy_balance_detail'

const namespace = 'gamePropsQuery' // model 的 namespace
const TabPane = Tabs.TabPane

@connect(({ gamePropsQuery }) => ({ // model 的 namespace
  model: gamePropsQuery // model 的 namespace
}))
class GamePropsQuery extends Component { // 默认页面组件，不需要修改
  /** *****************************非活动流程配置文件更新与获取****************************************************************/
  tabOnChange = type => activityKey => {
    if (type !== undefined || type != null) {
      activityKey = type
    }
  }

  /** *******************************页面布局*************************************************************/
  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs onChange={this.tabOnChange()} type='card'>
          <TabPane tab='布料查询' key='1'>
            <BeanBalanceDetailComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='能量查询' key='2'>
            <EnergyBalanceDetailComponent modelName={namespace} model={this.props.model} />
          </TabPane>
        </Tabs>

      </PageHeaderWrapper>
    )
  }
}

export default GamePropsQuery // 保证唯一
