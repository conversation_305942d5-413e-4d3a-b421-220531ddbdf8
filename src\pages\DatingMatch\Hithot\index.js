import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Button, Divider, Form, Card, Modal, Input, Select } from 'antd'
import { connect } from 'dva'
import PopImage from '@/components/PopImage'
import { exportExcel } from 'xlsx-oc'
import { CSVLink } from 'react-csv'

// import moment from 'moment'

const namespace = 'Hithot'
const FormItem = Form.Item
const Option = Select.Option

@connect(({ Hithot }) => ({
  Hithot
}))

class Index extends Component {
  // column structs.
  columns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: '主持YY号', dataIndex: 'yy_number', key: 'yy_number', align: 'center' },
    { title: 'UID', dataIndex: 'uid', key: 'uid', align: 'center' },
    { title: '开播状态', dataIndex: 'live_state', key: 'live_state', align: 'center' },
    { title: '视频开播状态', dataIndex: 'is_video', key: 'is_video', align: 'center' },
    { title: '主持昵称', dataIndex: 'nick', key: 'nick', align: 'center' },
    { title: '配置频道', dataIndex: 'sid', key: 'sid', align: 'center' },
    { title: '直播顶级频道', dataIndex: 'topsid', key: 'topsid', align: 'center' },
    { title: '直播子频道', dataIndex: 'ssid', key: 'ssid', align: 'center' },
    { title: '推荐标题', dataIndex: 'title', key: 'title', align: 'center' },
    { title: '推荐海报',
      dataIndex: 'cover',
      key: 'cover',
      align: 'center',
      render: (text, record) => (
        <PopImage value={record.cover} />
      )
    },
    { title: '权重', dataIndex: 'weight', key: 'weight', align: 'center' },
    { title: '推荐位置',
      dataIndex: 'zone',
      key: 'zone',
      align: 'center',
      render: (text, record) => {
        switch (text) {
          case 'A0': return 'A0'
          case 'A1': return 'A1'
          case 'A2': return 'A2'
          case 'A3': return 'A3'
          case 'A4': return 'A4'
          case 'A5': return 'A5'
          case 'A6': return 'A6'
        }
        return ''
      }
    },
    { title: '推荐时间', dataIndex: 'recomm_date', key: 'recomm_date', align: 'center' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <Button style={{ marginRight: 10 }} size='small' type='primary' onClick={this.showModal(true, record)}>修改</Button>
          <Button style={{ marginRight: 10 }} size='small' type='primary' onClick={this.showModal(false, record)}>下推荐</Button>
        </span>)
    }
  ]

  defaultPageValue = {
    defaultPageSize: 200,
    pageSizeOptions: ['100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, livestate: -1, value: {} }

  // show modal
  showModal = (isUpdate, record) => () => {
    if (this.formRef) {
      this.formRef.setFieldsValue(record)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'remove' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      var value = selectedRows.map(item => item.id).join(',')
      this.setState({ removeKey: value })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User', // Column configuration not to be checked
      name: record.name
    })
  }

  handleSubmit = () => {
    this.formRef.submit()
  }

  onRef = form => {
    this.formRef = form
  }

  // add and update
  onFinish = values => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/updateItem`,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    const data = { live_state: this.state.livestate }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // reset search info
  handleReset = clearFilters => () => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  handleLiveStateChange = (value) => {
    this.setState({ livestate: value.key })
    // console.log(value) // { key: "lucy", label: "Lucy (101)" }
  }

  handleZoneChange = (value) => {
    this.setState({ zone: value.key })
    // console.log(value) // { key: "lucy", label: "Lucy (101)" }
  }

  handleSearch = () => {
    const { dispatch } = this.props
    const data = { yynum: this.state.searchYY, sid: this.state.searchSid, zone: this.state.zone, live_state: this.state.livestate }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  onExport = () => {
    const { Hithot: { list } } = this.props
    let headers = []
    let columns = this.columns
    const exportKey = list
    let exportItems = ['主持YY号', 'UID', '开播状态', '视频开播状态', '主持昵称', '配置频道', '直播顶级频道', '直播子频道', '推荐标题', '推荐海报', '权重', '推荐位置', '推荐开始时间']
    columns.forEach(function (item) {
      let index = exportItems.indexOf(item.title)
      if (index !== -1) {
        headers.push({ k: item.dataIndex, v: item.title })
      }
    })
    console.log('start export...')
    exportExcel(headers, exportKey)
    console.log('end export...')
  }

  // content
  render () {
    const { route, Hithot: { list } } = this.props
    const { visible, title, isUpdate } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    let headers = [
      { label: '主持YY号', key: 'yy_number' },
      { label: 'Uid', key: 'uid' },
      { label: '开播状态', key: 'live_state' },
      { label: '视频开播状态', key: 'is_video' },
      { label: '主持昵称', key: 'nick' },
      { label: '配置频道', key: 'sid' },
      { label: '直播顶级频道', key: 'topsid' },
      { label: '直播子频道', key: 'ssid' },
      { label: '推荐标题', key: 'title' },
      { label: '推荐海报', key: 'title' },
      { label: '权重', key: 'weight' },
      { label: '推荐位置', key: 'zone' },
      { label: '推荐时间', key: 'recomm_date' }
    ]
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Input placeholder='主持YY号' onChange={e => this.setState({ searchYY: e.target.value })} style={{ width: 250 }} /> {/* 搜索按钮 */}
            <Divider type='vertical' /> {/* 分割线 */}
            <Input placeholder='频道号' onChange={e => this.setState({ searchSid: e.target.value })} style={{ width: 250 }} /> {/* 搜索按钮 */}
            <Divider type='vertical' /> {/* 分割线 */}
            <Select labelInValue defaultValue={{ key: '全部' }} style={{ width: 120 }} onChange={this.handleZoneChange}>
              <Option value=''>全部</Option>
              <Option value='A0'>A0</Option>
              <Option value='A1'>A1</Option>
              <Option value='A2'>A2</Option>
              <Option value='A3'>A3</Option>
              <Option value='A4'>A4</Option>
              <Option value='A5'>A5</Option>
              <Option value='A6'>A6</Option>
            </Select>
            <Divider type='vertical' /> {/* 分割线 */}
            <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 120 }} onChange={this.handleLiveStateChange}>
              <Option value='0'>全部</Option>
              <Option value='1'>开播中</Option>
              <Option value='2'>未开播</Option>
            </Select>
            <Divider type='vertical' /> {/* 分割线 */}
            <Button type='primary' onClick={this.handleSearch}>搜索</Button>
            <Divider type='vertical' /> {/* 分割线 */}
            <CSVLink data={list}filename={'今日热门.csv'} headers={headers}>导出</CSVLink>
            <Table rowKey={(record, index) => index} dataSource={list} columns={this.columns} pagination={this.defaultPageValue} />
          </Form>
        </Card>

        { isUpdate
          ? <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
            <Form ref={form => { this.formRef = form }} onFinish={this.onFinish} {...formItemLayout}>
              <FormItem label='uid' name='submit_uid' rules={[{ required: true }]}>
                <Input readOnly={isUpdate} />
              </FormItem>
              <FormItem label='sid' name='sid' rules={[{ required: true }]}>
                <Input />
              </FormItem>
              <FormItem label='title' name='title' rules={[{ required: true }]}>
                <Input />
              </FormItem>
              <FormItem label='权重' name='weight' rules={[{ required: true }]}>
                <Input />
              </FormItem>
              <FormItem label='推荐位置' name='zone' rules={[{ required: true }]}>
                <Select>
                  <Option value={0}>未知</Option>
                  <Option value='A0'>A0</Option>
                  <Option value='A1'>A1</Option>
                  <Option value='A2'>A2</Option>
                  <Option value='A3'>A3</Option>
                  <Option value='A4'>A4</Option>
                  <Option value='A5'>A5</Option>
                  <Option value='A6'>A6</Option>
                </Select>
              </FormItem>
            </Form >
          </Modal>
          : <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
            <Form ref={form => { this.formRef = form }} onFinish={this.onFinish} {...formItemLayout}>
              <FormItem label='uid' name='cancel_uid' rules={[{ required: true }]}>
                <Input readOnly={!isUpdate} />
              </FormItem>
              <FormItem label='reason' name='reason' rules={[{ required: true }]}>
                <Input />
              </FormItem>
            </Form>
          </Modal>
        }
      </PageHeaderWrapper>
    )
  }
}

export default Index
