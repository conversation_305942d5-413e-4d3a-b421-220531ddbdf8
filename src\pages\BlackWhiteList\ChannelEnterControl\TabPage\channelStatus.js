import React, { Component } from 'react'
import { connect } from 'dva'
import { tableStyle, timeFormater } from '@/utils/common'
import { Row, Col, Table } from 'antd'

// 监控目标UID配置页面
const namespace = 'channelEnterControl'

@connect(({ channelEnterControl }) => ({
  model: channelEnterControl
}))

class ChannelStatusDisplayer extends Component {
  state = {}

  componentDidMount = () => {
    this.refreshList()
  }
  refreshList = () => {
    this.callModel('getChannelStatus')
  }
  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 翻页后更新表格相关参数
  pageChange (pagination) {
    const { current, pageSize } = pagination
    this.changeState('page', current)
    this.changeState('size', pageSize)
  }

  render () {
    const { ChannelStatus, currentPage, currentSize } = this.props.model

    const columns = [
      { title: '序号', render: (text, record, index) => ((currentPage - 1) * currentSize + index + 1) },
      { title: 'SID', dataIndex: 'sid' },
      { title: 'SSID', dataIndex: 'ssid' },
      { title: '当前状态', dataIndex: 'status', render: (v) => { return v ? '限制中' : '不限制' } },
      { title: '更新时间', dataIndex: 'timestamp', render: (v) => { return timeFormater(v) } },
      { title: '相关UID', dataIndex: 'uid' },
      { title: '相关事件', dataIndex: 'event' },
      { title: '相关渠道', dataIndex: 'appIdList', render: (v) => { return v ? JSON.stringify(v) : '-' } }
    ]

    return (
      <Row >
        <Col span={24}>
          <Table columns={columns} dataSource={ChannelStatus} size='small' pagination={tableStyle} scroll={{ x: 'max-content' }}
            rowKey={record => record.uid} onChange={(pagination) => { this.pageChange(pagination) }} />
        </Col>
      </Row>
    )
  }
}

export default ChannelStatusDisplayer
