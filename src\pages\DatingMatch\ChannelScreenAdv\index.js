import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import PopImage from '@/components/PopImage'
import { connect } from 'dva'
import { Card, Table, Popconfirm, Input, Form, Modal, DatePicker, Divider, Button } from 'antd'
import PicturesWall from '@/components/PicturesWall'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'

var moment = require('moment')
const namespace = 'channelScreenAdv'
const FormItem = Form.Item

@connect(({ channelScreenAdv }) => ({
  model: channelScreenAdv
}))
class ChannelScreenAdv extends Component {
  columns = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: '介绍', dataIndex: 'introduce', align: 'cneter' },
    { title: '大广告图', dataIndex: 'bigUrl', align: 'center', render: text => <PopImage value={text} /> },
    { title: '小广告图', dataIndex: 'smallUrl', align: 'center', render: text => <PopImage value={text} /> },
    { title: '跳转链接', dataIndex: 'jumpUrl', align: 'center', render: text => text.length > 30 ? <a href={text}>详细链接</a> : text },
    { title: '开始时间', dataIndex: 'beginTime', align: 'center' },
    { title: '结束时间', dataIndex: 'endTime', align: 'center' },
    { title: '权重', dataIndex: 'weight', align: 'center' },
    { title: '操作',
      align: 'center',
      render: record => (
        <div>
          <a><EditOutlined style={{ marginRight: 10 }} onClick={this.showModal(true, record)} /></a>
          <Popconfirm onConfirm={this.handleRemove(record.id)} title='确认删除？'><a><DeleteOutlined style={{ color: 'red' }} /></a></Popconfirm>
        </div>)
    }
  ]

  state = { visible: false, record: {} }
  defaultValue = { introduce: null, jumpUrl: '', bigUrl: null, smallUrl: null, weight: 0, beginTime: moment().format('YYYY-MM-DD hh:mm:ss'), endTime: moment().format('YYYY-MM-DD hh:mm:ss') }

  showModal = (update, record) => () => {
    record = update ? record : this.defaultValue

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.jump = v.jumpUrl
      v.starTime = moment(v.beginTime, 'YYYY-MM-DD hh:mm:ss')
      v.endTime = moment(v.endTime, 'YYYY-MM-DD hh:mm:ss')
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, update: update, record: record, title: update ? '修改' : '添加' })
  }

  handleRemove = id => () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/removeItem`,
      payload: { id: id, request: this.state.request }
    })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  onFinish = values => {
    const { dispatch } = this.props
    const { update } = this.state
    values.starTime = values.starTime.format('YYYY-MM-DD hh:mm:ss')
    values.endTime = values.endTime.format('YYYY-MM-DD hh:mm:ss')
    values.request = this.state.request
    values.id = this.state.record.id
    const url = update ? `${namespace}/updateItem` : `${namespace}/addItem`
    dispatch({
      type: url,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  hideModal = () => {
    this.setState({ visible: false })
  }

  componentDidMount () {
    const { match, dispatch } = this.props
    const request = { 'box': 0, 'screen': 1 }[match.path.split('-')[1]]
    this.setState({ request: request })

    dispatch({
      type: `${namespace}/getList`,
      payload: { request: request }
    })
  }

  saveFormRef = formRef => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list } } = this.props
    const { visible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper route={route}>
        <Card>
          <Button type='primary' onClick={this.showModal(false)}>添加</Button>
          <Divider />
          <Table rowKey={(_, index) => index} columns={this.columns} dataSource={list} pagination={{ pageSize: 100 }} />
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem label='介绍' name='introduce'>
              <Input />
            </FormItem>
            <FormItem label='跳转链接' name='jump' rules={[{ required: true }]}>
              <Input />
            </FormItem>
            <FormItem label='开始时间' name='starTime'>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </FormItem>
            <FormItem label='结束时间' name='endTime'>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' />
            </FormItem>
            <FormItem label='权重' name='weight' rules={[{ required: true }]}>
              <Input />
            </FormItem>
            <FormItem label='大广告图' name='bigUrl' rules={[{ required: true }]}>
              <PicturesWall />
            </FormItem>
            <FormItem label='小广告图' name='smallUrl' rules={[{ required: true }]}>
              <PicturesWall />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default ChannelScreenAdv
