import request from '@/utils/request'
import { stringify } from 'qs'

// 获取申请超级水晶公会列表
export function listSuperCrystal (params) {
  return request(`/fts_hgame/boss/guild_settle_in/list_super_crystal?${stringify(params)}`)
}

// 获取申请超级水晶公会历史记录列表
export function listSuperCrystalHistory (params) {
  return request(`/fts_hgame/boss/guild_settle_in/list_super_crystal_history?${stringify(params)}`)
}

// 超级水晶公会详情
export function detailSuperCrysta (params) {
  return request(`/fts_hgame/boss/guild_settle_in/detail_super_crysta?${stringify(params)}`)
}

// 超级水晶公会审批
export function approvalSuperCrysta (params) {
  return request(`/fts_hgame/boss/guild_settle_in/approval_super_crysta?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 超级水晶公会上传合同附件
export function uploadSuperSrysta (params) {
  return request(`/fts_hgame/boss/guild_settle_in/upload_super_crysta?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 发起入驻邀请
export function inviteSuperCrysta (params) {
  return request(`/fts_hgame/boss/guild_settle_in/invite_super_crysta?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

export function getOwInfo (params) {
  return request(`/fts_hgame/boss/guild_settle_in/get_ow_info?${stringify(params)}`)
}
