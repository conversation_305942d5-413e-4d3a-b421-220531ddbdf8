.scroll-bar(@width: 10px, @color: #fff) {
  // overflow: auto;
  &::-webkit-scrollbar-track {
      border-radius: @width;
      background-color: transparent;
  }
  &::-webkit-scrollbar {
      width: @width;
      background-color: transparent;
  }
  &::-webkit-scrollbar-thumb {
      border-radius: @width;
      background-color: rgba(@color, .2);
      &:hover {
          background-color: rgba(@color, .4);
      }
  }
}

.ant-table-body {
  .scroll-bar(10px, #000);
}

.wikiUpdateTip {
  font-size: 25px;
  margin: 0;
  animation-name: wikiTipFlash;
  animation-duration:  1s;
  animation-iteration-count: infinite;
  animation-timing-function: steps(2);
}

@keyframes wikiTipFlash {
  from {color: #f00c6d;}
  to {color: #00ffcf;;}
}
