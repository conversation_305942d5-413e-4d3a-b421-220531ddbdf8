const { ReactAdapter } = window.EMP_ADAPTER_LIB

export const getDns = (test, online) => {
  return /test|webtest|localhost/.test(window.location.hostname) ? test : online
}

export const getParam = (key) => {
  const r = new RegExp('(\\?|#|&)' + key + '=([^&#]*)(&|#|$)')
  const m = window.location.href.match(r)
  return decodeURI(!m ? '' : m[2])
}

const localUrlEmp = 'https://localhost:40402/emp.js'
const testUrlEmp = 'https://hd-admin-in-test.yy.com/jyboss_admin_react/emp.js?v=' + Date.now()
const proUrlEmp = 'https://hd-admin-in.yy.com/jyboss_admin_react/emp.js?v=' + Date.now()

// 本地开发 url 增加参数 isDev=1 切换为本地 emp.js 地址，服务器地址配置参数不会生效防止错误发版
const isDev = getParam('isDev') && __DEV__

// 初始化配置
ReactAdapter.init({
  remotes: [
    {
      name: 'HD_Astro_UI',
      entry: 'https://unpkg.yy.com/@hd/astro-ui@0.26.0/dist/emp.js'
    },
    {
      name: 'JyBossAdmin',
      entry: isDev ? localUrlEmp : getDns(testUrlEmp, proUrlEmp),
      alias: 'AdminUnitUi/jyboss_admin'
    }
  ]
})
