import { connect } from 'dva'
import React, { Component } from 'react'
import { Row, Col, Button, Select, Table, Modal, Form, message } from 'antd'
import { timeFormater, checkIsNumber } from '@/utils/common'
import { InputNumber } from '@/components/SimpleComponents'

const namespace = 'hgameTingTools'

@connect(({ hgameTingTools }) => ({
  model: hgameTingTools
}))

class WhiteList extends Component {
  state = {
    visible: false
  }

  componentDidMount = () => {
    this.getWhiteList()
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  getWhiteList = () => {
    this.refreshList()
  }

  // 获取白名单
  refreshList = () => {
    this.callModel('getWhiteList')
  }

  // 添加白名单
  addWhiteList = (f) => {
    if (!checkIsNumber(f.channelID)) {
      message.warn('请输入正确的频道号')
      return
    }
    this.callModel('addWhiteList', {
      params: f,
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        if (ret) {
          message.success('添加成功！')
          this.refreshList()
        }
      }
    })
  }

  render () {
    const { whitelist } = this.props.model
    const { visible } = this.state
    const columns = [
      { title: '短位ID', dataIndex: 'asid' },
      { title: 'sid', dataIndex: 'sid' },
      { title: '添加人', dataIndex: 'updateInfo', render: (v) => { return v?.passport } },
      { title: '添加时间', dataIndex: 'updateInfo', render: (v) => { return timeFormater(v?.timestamp) } }
    ]

    return (
      <Row>
        <Col span={24} style={{ marginBottom: '1em' }}>
          <Button onClick={() => this.setState({ visible: true })}>添加白名单</Button>
        </Col>
        <Col span={24}>
          <Table columns={columns} dataSource={whitelist} size='small' rowKey={item => { return item.sid }} pagination={{ pageSize: 50 }} />
        </Col>
        <Modal visible={visible} title='添加白名单' onCancel={() => { this.setState({ visible: false }) }} onOk={() => this.formRef.submit()} >
          <Form ref={ref => { this.formRef = ref }} onFinish={(v) => { this.addWhiteList(v) }} initialValues={{ channelType: 'ASID', channelID: 0 }} labelCol={{ span: 4 }} labelAlign='left' >
            <Form.Item label='频道类型' name='channelType'>
              <Select options={[{ label: '短位ID', value: 'ASID' }, { label: '长位ID', value: 'SID' }]} style={{ width: '12em' }} />
            </Form.Item>
            <Form.Item label='频道ID' name='channelID'>
              <InputNumber style={{ width: '12em' }} />
            </Form.Item>
          </Form>
        </Modal>
      </Row>
    )
  }
}

export default WhiteList
