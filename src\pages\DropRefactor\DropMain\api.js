import request from '@/utils/request'
import { stringify } from 'qs'

export function removeRuleFromServer (params) {
  return request(`/drop/admin/remove_rule?${stringify(params)}`)
}

export function getRuleListFromServer () {
  return request(`/drop/admin/query_all_rule`)
}

export function updateRuleFromServer (params) {
  return request(`/drop/admin/update_rule`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function addRuleFromServer (params) {
  return request(`/drop/admin/add_rule`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function getPoolFromServer (params) {
  return request(`/drop/admin/query_pool?${stringify(params)}`)
}

export function removePoolFromServer (params) {
  return request(`/drop/admin/remove_pool?${stringify(params)}`)
}

export function editPoolFromServer (params) {
  return request(`/drop/admin/edit_pool`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
export function doApprovalFromServer (params) {
  return request(`/approval/admin/do_approval`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
export function updatePoolFromServer (params) {
  return request(`/drop/admin/update_pool`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function updatePoolDescFromServer (params) {
  return request(`/drop/admin/update_pool?desc=1`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function addPoolFromServer (params) {
  return request(`/drop/admin/add_pool`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function getSummaryFromServer (params) {
  // return request(`/drop/admin/summary_list?${stringify(params)}`)
  return request(`/drop_stat/summary_list?${stringify(params)}`)
}

export function getProgressFromServer () {
  return request(`/drop/admin/progress_query`)
}

export function upsetProgressFromServer (params) {
  return request(`/drop/admin/progress_upset`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function removeProgressFromServer (params) {
  return request(`/drop/admin/progress_remove?${stringify(params)}`)
}

export function getWarnFromServer () {
  return request(`/drop/admin/warn_query`)
}

export function upsetWarnFromServer (params) {
  return request(`/drop/admin/warn_upset`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function removeWarnFromServer (params) {
  return request(`/drop/admin/warn_remove?${stringify(params)}`)
}

export function getPoolEstimateFromServer () {
  return request(`/drop/admin/query_pool_estimate`)
}

export function upsetPoolEstimateFromServer (params) {
  return request(`/drop/admin/update_pool_estimate`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
