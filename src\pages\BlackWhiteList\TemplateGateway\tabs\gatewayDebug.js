import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Row, Col, Typography, Select, Space, Button, Divider, InputNumber } from 'antd'
import { templateIdOptions } from '../common'

const { Text } = Typography
const namespace = 'templateGateWay'

@connect(({ templateGateWay }) => ({
  model: templateGateWay
}))

class GatewayDebug extends Component {
  state = {
    templateID: 'makefriend',
    sid: 0,
    ssid: 0
  }

  componentDidMount = () => { }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 获取debug结果
  queryDebugResult = (templateId, sid, ssid) => {
    this.callModel('getDebugResult', {
      params: { templateId, sid, ssid }
    })
  }

  render () {
    const { sid, ssid, templateID } = this.state
    const { debugResult } = this.props.model

    return (
      <Card>
        <Row>
          <Col span={24}>
            <Space>
              <Text>模板ID:</Text>
              <Select options={templateIdOptions} value={templateID} style={{ width: '8em' }} onChange={(v) => { this.setState({ templateID: v }) }} />
              <Divider type='vertical' />
              <Text>sid:</Text>
              <InputNumber value={sid} onChange={v => this.setState({ sid: v })} style={{ width: '10em' }} />
              <Divider type='vertical' />
              <Text>ssid:</Text>
              <InputNumber value={ssid} onChange={v => this.setState({ ssid: v })} style={{ width: '10em' }} />
              <Button type='primary' onClick={() => this.queryDebugResult(templateID, sid, ssid)}>测试</Button>
            </Space>
          </Col>
          <Col span={24}>
            {
              debugResult.map(item => {
                let color = ''
                if (item.indexOf('验证成功') >= 0) {
                  color = 'success'
                }
                if (item.indexOf('验证失败') >= 0) {
                  color = 'danger'
                }
                return <Text type={color}>{item}<br /></Text>
              })
            }
          </Col>
        </Row>
      </Card>
    )
  }
}

export default GatewayDebug
