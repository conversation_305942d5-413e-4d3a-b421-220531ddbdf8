import {
  Card,
  Form,
  Table,
  Divider, Button, Input, DatePicker
} from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import exportExcel from '@/utils/exportExcel'
import moment from 'moment'

const namespace = 'guildTaskBabyRevision'
const getListUri = `${namespace}/listAwardDetail`

@connect(({ awardDetail }) => ({
  model: awardDetail
}))

class AwardDetail extends Component {
  constructor (props) {
    super(props)

    const { dataInfo } = props
    // console.log('constructor dataInfo', dataInfo)
    this.state = { list: dataInfo, searchASID: 0, searchTaskMonth: '' }
  }

  componentDidMount () {
    const { dispatch } = this.props
    const { searchASID, searchTaskMonth } = this.state
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    let data = { asid: searchASID, month: searchMonthTmp }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  onClick = () => {
    const { dispatch } = this.props
    const { searchASID, searchTaskMonth } = this.state
    let searchMonthTmp = ''
    if (searchTaskMonth !== undefined && searchTaskMonth !== null && searchTaskMonth !== '') {
      searchMonthTmp = searchTaskMonth.format('YYYYMM')
    }
    let data = { asid: searchASID, month: searchMonthTmp }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  componentWillReceiveProps (nextProps) {
    const { dataInfo } = nextProps
    // console.log('componentWillReceiveProps', dataInfo)
    this.setState({ list: dataInfo })
  }

  state = {
    visible: false,
    isUpdate: false,
    isNotifyValue: true,
    searchInput: '',
    searchKeyword: '',
    channel: { key: -1 },
    gameType: { key: -1 }
  }

  tableInitValue = {}

  columns = [
    { title: '任务时间', width: 40, dataIndex: 'month' },
    { title: '短位ID', width: 50, dataIndex: 'asid' },
    { title: '上月礼物流水/元', width: 50, dataIndex: 'lastMonthTurnover', align: 'center' },
    { title: '本月礼物流水基准/元', width: 50, dataIndex: 'curMonthReferenceTurnover', align: 'center' },
    { title: '是否达标基准流水', width: 50, dataIndex: 'isReachReferenceTurnover', align: 'center' },
    { title: '公会任务礼物流水/元', width: 40, dataIndex: 'turnoverAmount' },
    { title: '流水任务最终发奖金额/元', width: 50, dataIndex: 'finallyTurnoverReward' },
    { title: '配置发奖规则', width: 50, dataIndex: 'configTurnoverRule', render: (v, item) => item.revenueRemark === '未确认发奖，默认按照系统结算' ? '' : v },
    { title: '最终发奖规则', width: 50, dataIndex: 'finallyTurnoverRule' },
    { title: '合计最终发奖金额/元', width: 50, dataIndex: 'finallyTotalReward' },
    { title: '流水任务-备注', width: 50, dataIndex: 'revenueRemark', render: (v, item) => item.revenueRemark === '' ? '-' : v }
  ].map(item => {
    item.align = 'center'
    item.ellipsis = true
    return item
  })

  pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  getFilterList = () => {
    const { list } = this.state
    // const { searchImid } = this.state
    let filterList = list
    // if (searchImid && searchImid.length > 0) {
    //   filterList = filterList.filter((v) => { return v.imid === parseInt(searchImid) })
    // }
    console.log('getFilterList', list)
    return filterList
  }

  showModal = (isUpdate, record) => () => {
    if (record == null) {
      record = this.tableInitValue
    }
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(record)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate })
  }

  onExport = () => {
    let list = this.getFilterList()

    let exportData = list.map(item => {
      let v = $.extend(true, {}, item)
      console.log(v)
      return v
    })
    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.export === undefined || col.export) {
        console.log(col.title)
        exportHeader.push({ key: col.dataIndex, header: col.title })
      }
    })
    let fileName = '公会任务发奖明细-' + moment().format('YYYYMMDD') + '.xlsx'
    exportExcel(exportHeader, exportData, fileName)
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  render () {
    return (
      <Card>
        <Form>
          <Divider type='vertical' />
          任务时间：
          <DatePicker
            format='YYYY-MM'
            picker='month'
            placeholder='任务时间'
            onChange={(v) => this.setState({ searchTaskMonth: v })}
            style={{ width: 100, marginRight: 10 }}
          />
          短位ID：
          <Input style={{ width: 100, marginLeft: 0 }} placeholder='请输入' onChange={(e) => { this.setState({ 'searchASID': e.target.value }) }} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onExport}>导出</Button>
          <Divider type='vertical' />
          <Table style={{ marginTop: 10 }} dataSource={this.getFilterList()} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
        </Form>
      </Card>
    )
  }
}

export default AwardDetail
