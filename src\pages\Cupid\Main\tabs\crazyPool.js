import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Row, Col, Space, Button, InputNumber, message, Modal, Input, Spin, Form, Typography, Divider, Select } from 'antd'
import { crazyColumn, AprInfoDesc, parseCrazyPoolContent, RateEditor, rateForamter, defaultCrazyItem, broadcastOptionsVR } from '../mainCommon'
import { deepClone, getCookie } from '@/utils/common'
import PopImage2 from '@/components/PopImage2'
import DiffTable from '@/components/DiffTable'
import PrizeSelector from '../components/prizeSelector'
import { screenshotByID } from '@/utils/screenShot'
import moment from 'moment'
const { Link, Text } = Typography

const namespace = 'cupid'
const poolTypeCrazy = 3

@connect(({ cupid }) => ({
  model: cupid
}))

class CupidCrazyPool extends Component {
  state = {
    editing: false,
    temporary: null,
    updateItem: {},
    diffRefreshID: 0,
    updateModalVisible: false,
    prizeSelectorVisible: false,
    updateComfirmVisible: false,
    approvalVisible: false,
    spanning: false,
    toApproval: null,
    approvalRemark: '',
    updateRemark: '',
    selectRow: -1,
    prodList: [], // 生效中的道具列表
    tempList: [], // 待审批的道具列表
    tempRule: { flowRate: 0, crazyDuration: 0, thresholdC: 0 },
    prodRule: { flowRate: 0, crazyDuration: 0, thresholdC: 0 }
  }

  componentDidMount = () => {
    this.getNormalPool()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 获取配置列表 query-step-1
  getNormalPool = () => {
    this.callModel('getCrazyPool', {
      params: { originID: this.props.originID },
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg, data } = ret
        if (status !== 0) {
          message.warn('获取数据失败: ' + msg)
          return
        }
        const { temporary, content } = data
        const { diffRefreshID } = this.state
        const prodConfig = parseCrazyPoolContent(content)
        const tempConfig = parseCrazyPoolContent(temporary?.content)
        this.setState({ rawData: data,
          diffRefreshID: diffRefreshID + 1,
          temporary: temporary,
          prodList: prodConfig.prizeList,
          prodRule: prodConfig.ruleConfig,
          tempList: tempConfig.prizeList,
          tempRule: tempConfig.ruleConfig })
      }
    })
  }

  // 放弃修改
  onCancelEdit = () => {
    const { temporary, diffRefreshID } = this.state
    const tempConfig = parseCrazyPoolContent(temporary.content)
    const { prizeList, ruleConfig } = tempConfig
    this.setState({ diffRefreshID: diffRefreshID + 1, editing: false, tempList: prizeList, tempRule: ruleConfig })
  }

  // 提交更新-前置 update-step-5
  beforeSubmitEditing = () => {
    const { tempList } = this.state
    if (!this.checkSubmitData(tempList)) {
      return
    }
    this.setState({ editing: false, spanning: true })
    setTimeout(() => {
      screenshotByID('poolTableLuck', (isOk, url) => {
        if (!isOk) {
          message.warn('截图失败~')
        }
        console.debug('screenShot===>', url)
        this.setState({ updateComfirmVisible: true, screenShot: url, spanning: false })
      })
    }, 400)
  }

  // 提交审批 update-step-6
  onSubmit = () => {
    const { updateRemark, screenShot, tempList, tempRule, temporary, rawData } = this.state
    // 数据检验
    if (!this.checkSubmitData(tempList)) {
      return
    }
    if (!updateRemark) {
      message.warn('请填写备注信息')
      return
    }
    const fixedList = tempList.map(item => {
      return item
    })
    const newRule = {
      prizeList: fixedList,
      ruleConfig: tempRule
    }
    temporary.content = JSON.stringify(newRule)
    temporary.aprInfo.opRemark = updateRemark || '空'
    temporary.aprInfo.screenShot = screenShot
    rawData.temporary = temporary
    this.callModel('updateCrazyPool', {
      params: rawData,
      isDetailMode: true,
      isJsonMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('操作失败:' + msg)
          return
        }
        message.success('操作成功！')
        this.setState({ editing: false, updateComfirmVisible: false })
        this.getNormalPool()
      }
    })
  }

  // 根据流程ID获取审批单信息，展示审批模态框
  getApprovalInfo = (aprID) => {
    if (aprID === '') {
      message.warn('未创建审批流')
      return
    }
    this.callModel('getToApproval', {
      params: { pid: aprID },
      isRawRespMode: true,
      cbFunc: (ret) => {
        const { status, msg, list } = ret
        if (status !== 0) {
          message.error('获取审批状态失败' + msg)
          return
        }
        if (!Array.isArray(list) || list.length === 0) {
          message.warn('当前非待审批状态~')
          return
        }
        let info = list[0]
        if (info === undefined || info.rule === undefined || info.rule.approvals === undefined) {
          message.warn('审批流信息异常，请稍后再试..')
          return
        }
        let uid = Number(getCookie('yyuid'))
        const { progress, rule } = info
        const aprList = progress === 0 ? rule.approvals : rule.secondary
        for (let i = 0; i < aprList.length; i++) {
          if (aprList[i].uid === uid) {
            this.setState({ approvalVisible: true, toApproval: info })
            return
          }
        }
        message.warn('非指定审批人')
      }
    })
  }

  // 提交审批逻辑
  doApproval = (pass) => {
    const { approvalRemark, toApproval } = this.state
    let req = toApproval
    if (req === undefined || req.rule === undefined || req.rule.approvals === undefined) {
      message.warn('审批流信息异常，请稍后再试..')
      return
    }
    req.reason = approvalRemark || '无'
    if (pass) {
      req.result = 'Passed'
    } else {
      req.result = 'Rejected'
    }
    this.callModel('doApproval', {
      params: req,
      isDetailMode: true,
      isJsonMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('审批失败:' + msg)
          return
        }
        message.success('审批完成')
        this.setState({ approvalVisible: false })
        this.getNormalPool()
      }
    })
  }

  // ============ Table Columns 相关 ===================

  // 这里控制需要修改的字段,
  copyEditItem = (r) => {
    return {
      id: r.id,
      prizeId: r.prizeId,
      propsId: r.propsId,
      appId: r.appId,
      name: r.name,
      url: r.url,
      value: r.value,
      count: r.count,
      rate: r.rate,
      thresholdR: r.thresholdR,
      bcType: r.bcType
    }
  }

  // 构造 DiffTable 专用的columns,  query-step-2.1
  renderDiffColumn = () => {
    let fixList = crazyColumn.map((item, i) => {
      return item
    })
    // 需要用到，但是无需显示的，要加一下
    fixList.push({ title: null, dft_hidden: true, dataIndex: 'appId' })
    fixList.push({
      title: '操作',
      dft_hidden: true,
      align: 'center',
      render: (v, r) => (
        <div>
          <Button type='link' onClick={() => this.setState({ updateModalVisible: true, updateItem: this.copyEditItem(r) })} >编辑</Button>
          <Button type='link' onClick={() => this.onDeleteTempItem(r)} >删除</Button>
        </div>
      )
    })
    return fixList
  }

  // ================ 更改配置相关 ====================

  // 更换道具ID  update-step-5
  onPropChangeNew = (prizeInfo) => {
    let { updateItem } = this.state
    const { appId, id, name, url, price, prizeType } = prizeInfo
    updateItem.appId = appId // appId
    updateItem.propsId = id // 礼物ID
    updateItem.name = name // 礼物名称
    updateItem.url = url // 礼物图片
    updateItem.value = price // 礼物
    updateItem.prizeType = prizeType // 礼物渠道
    this.setState({ updateItem: updateItem, prizeSelectorVisible: false })
  }

  // 检查道具池配置合法性, 返回false表示不合法
  checkSubmitData = (tempList) => {
    console.debug('check tempList===>', tempList)
    if (tempList.length === 0) {
      message.error('最少保留一个礼物')
      return false
    }
    return true
  }

  // 更新 tempItem 的值  update-step-3
  updateTempItem = (before, field, newVal) => {
    before[field] = newVal
    this.setState({ updateItem: before })
  }

  // 确认某个奖品的编辑  update-step-4
  onComfirmItemUpdate = (newItem) => {
    const { tempList, diffRefreshID } = this.state
    const fixedList = tempList.map(item => {
      if (item.id === newItem.id) {
        return newItem
      }
      return item
    })
    this.setState(({ diffRefreshID: diffRefreshID + 1, tempList: fixedList, editing: true, updateModalVisible: false }))
  }

  // 从列表删除某个奖品 update-step-4.2
  onDeleteTempItem = (delItem) => {
    const { tempList, diffRefreshID } = this.state
    const after = tempList.filter(item => {
      return item.prizeId !== delItem.prizeId
    })
    this.setState({ tempList: after, editing: true, diffRefreshID: diffRefreshID + 1 })
  }

  // 添加奖品 update-step-5
  onInsertNewItem = () => {
    let newItem = deepClone(defaultCrazyItem)
    newItem.prizeId = `${this.props.originID}_${poolTypeCrazy}_${moment().format('DDHHmmss')}`
    const { tempList, diffRefreshID } = this.state

    // 自动生成id，prizeId字段
    tempList.forEach((item, index) => {
      if (item.id >= newItem.id) {
        newItem.id = item.id + 1
      }
      if (item.prizeId === newItem.prizeId) {
        newItem.prizeId += `${index}`
      }
    })
    let newList = deepClone(tempList)
    newList.push(newItem)
    this.setState({ diffRefreshID: diffRefreshID + 1, tempList: newList, editing: true })
  }

  // 更新临时规则
  onUpdateTempRule = (newRule) => {
    const { diffRefreshID } = this.state
    this.setState(({ diffRefreshID: diffRefreshID + 1, editing: true, tempRule: newRule }))
  }

  // ==================================================

  render () {
    const { editing, temporary, prizeSelectorVisible, approvalVisible, updateComfirmVisible, spanning } = this.state
    const { updateItem, updateModalVisible } = this.state
    const { tempList, tempRule, prodRule, prodList, diffRefreshID } = this.state
    const { globalPrizeList } = this.props.model
    const { aprInfo } = temporary || {}

    // console.debug('diffRefreshID===>', diffRefreshID)
    // console.debug('prodList===>', prodList)

    return (
      <Card>
        <Row>
          <Col span={24} style={{ marginBottom: '1em' }}>
            <Space>
              {
                editing
                  ? <>
                    <Button onClick={() => this.onCancelEdit()} style={{ marginRight: 20 }}>放弃修改</Button>
                    <Button type='dash' danger onClick={() => { this.beforeSubmitEditing() }}>提交更新</Button>
                  </>
                  : <>
                    <Button type='primary' disabled={aprInfo?.aprId === '' || aprInfo?.aprResult !== 'OnGoing'}
                      onClick={() => this.getApprovalInfo(aprInfo?.aprId)}>审批</Button>
                  </>
              }
              { spanning ? <Spin /> : ''}
            </Space>
          </Col>

          <Col span={24} style={{ marginBottom: '1em' }}>
            <AprInfoDesc value={aprInfo} />
          </Col>

          <Col span={24}>
            <div id='poolTableLuck'>
              <RuleConfigRender oldValue={prodRule} value={tempRule} onChange={(v) => this.onUpdateTempRule(v)} />
              {
                <DiffTable
                  key={diffRefreshID}
                  oldProps={{ pagination: false, size: 'small' }}
                  columns={this.renderDiffColumn()}
                  before={prodList}
                  after={tempList} />
              }
            </div>
            <div style={{ width: '100%', textAlign: 'right', margin: '1em' }}>
              <Button size='small' type='primary' onClick={() => { this.onInsertNewItem() }} >添加奖品</Button>
            </div>
          </Col>

        </Row>

        {/* 道具选择器 */}
        <PrizeSelector type='modal' appIDLimit={34} visible={prizeSelectorVisible}
          prizeList={globalPrizeList}
          onCancel={() => { this.setState({ prizeSelectorVisible: false }) }}
          onComfirm={(v) => { this.onPropChangeNew(v) }}
        />

        {/* 提交修改确认模态框 */}
        <Modal visible={updateComfirmVisible} title='确认提交审批么？' footer={null} onCancel={() => { this.setState({ updateComfirmVisible: false, editing: true }) }}>
          <Row style={{ marginBottom: '1em' }}>
            <Input placeholder='请输入备注信息 (选填)' onChange={(e) => { this.setState({ updateRemark: e.target.value }) }} />
          </Row>
          <Space>
            <Button onClick={() => { this.setState({ updateComfirmVisible: false, editing: true }) }}>再看看</Button>
            <Button danger type='primary' onClick={() => { this.onSubmit() }}>提交审批</Button>
          </Space>
        </Modal>

        {/* 审批模态框 */}
        <Modal visible={approvalVisible} title='道具池配置审批' footer={null}
          onCancel={() => { this.setState({ approvalVisible: false }) }}>
          <Row style={{ marginBottom: '1em' }}>
            <Input placeholder='请输入备注信息 (通过或驳回的原因,选填)' onChange={(e) => { this.setState({ approvalRemark: e.target.value }) }} />
          </Row>
          <Space>
            <Button onClick={() => { this.setState({ approvalVisible: false }) }}>取消</Button>
            <Button danger type='primary' onClick={() => { this.doApproval(false) }}>驳回</Button>
            <Button type='primary' onClick={() => { this.doApproval(true) }}>通过</Button>
          </Space>
        </Modal>

        {/* 更新模态框 */}
        <Modal visible={updateModalVisible} title={`奖品配置`} onCancel={() => { this.setState({ updateModalVisible: false }) }} onOk={() => { this.onComfirmItemUpdate(updateItem) }} >
          <Form labelCol={{ span: 6 }}>
            <Form.Item label='奖品礼物'>
              <Link onClick={() => { this.setState({ prizeSelectorVisible: true }) }}>
                {updateItem.name}
                <PopImage2 value={updateItem.url} height='2em' />
              </Link>
            </Form.Item>
            <Form.Item label='奖品数量'>
              <InputNumber style={{ width: '10em' }} value={updateItem.count} onChange={(v) => { this.updateTempItem(updateItem, 'count', v) }} />
            </Form.Item>
            <Form.Item label='中奖概率'>
              <RateEditor value={updateItem.rate} onChange={(v) => { this.updateTempItem(updateItem, 'rate', v) }} />
              <span> %</span>
            </Form.Item>
            <Form.Item label='返奖率阈值'>
              <RateEditor value={updateItem.thresholdR} onChange={(v) => { this.updateTempItem(updateItem, 'thresholdR', v) }} />
              <span> %</span>
            </Form.Item>
            <Form.Item label='广播类型'>
              <Select style={{ width: '10em' }} value={updateItem.bcType} options={broadcastOptionsVR} onChange={(v) => { this.updateTempItem(updateItem, 'bcType', v) }} />
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default CupidCrazyPool

class RuleConfigRender extends Component {
  state = {
    isEdit: false
  }

  onUpdateField = (before, field, newVal) => {
    const { onChange } = this.props
    before[field] = newVal
    onChange(before)
  }

  render () {
    console.debug('value===>', this.props)
    const { value, oldValue } = this.props
    const { flowRate, crazyDuration, thresholdC, thresholdB } = value

    const { flowRate: flowRate2, crazyDuration: crazyDuration2, thresholdC: thresholdC2, thresholdB: thresholdB2 } = oldValue
    const { blessRebate, crazyRebate } = oldValue

    const { isEdit } = this.state
    if (isEdit) {
      return (
        <Row style={{ marginBottom: '1em' }}>
          <Space>
            <Text>流入比例: <RateEditor value={flowRate} onChange={(v) => { this.onUpdateField(value, 'flowRate', v) }} /> </Text>
            <Divider type='vertical' />
            <Text>狂欢时刻开启值: <InputNumber value={thresholdC} onChange={(v) => { this.onUpdateField(value, 'thresholdC', v) }} /> </Text>
            <Divider type='vertical' />
            <Text>狂欢时刻持续时间: <InputNumber value={crazyDuration} onChange={(v) => { this.onUpdateField(value, 'crazyDuration', v) }} /> </Text>
            <Divider type='vertical' />
            <Text>乐园祝福的阈值: <RateEditor value={thresholdB} onChange={(v) => { this.onUpdateField(value, 'thresholdB', v) }} /> </Text>
            <Button type='link' onClick={() => { this.setState({ isEdit: false }) }}>确定</Button>
          </Space>
        </Row>
      )
    }

    return (
      <Row style={{ marginBottom: '1em', fontSize: '1.2em' }}>
        <Space>
          <Text>流入比例:
            {
              flowRate !== flowRate2
                ? <Text><Text type='danger'>{rateForamter(flowRate)}</Text> ({rateForamter(flowRate2)}) </Text>
                : <Text type='success'>{rateForamter(flowRate)}</Text>
            }
          </Text>
          <Divider type='vertical' />
          <Text>狂欢时刻开启值:
            {
              thresholdC !== thresholdC2
                ? <Text><Text type='danger'>{thresholdC}</Text> ({thresholdC2})</Text>
                : <Text type='success'>{thresholdC}</Text>
            }
          </Text>
          <Divider type='vertical' />
          <Text>狂欢时刻持续时间:
            {
              crazyDuration !== crazyDuration2
                ? <Text><Text type='danger'>{crazyDuration}</Text> ({crazyDuration2})</Text>
                : <Text type='success'>{crazyDuration} s</Text>
            }
          </Text>
          <Divider type='vertical' />
          <Text>乐园祝福阈值:
            {
              thresholdB !== thresholdB2
                ? <Text><Text type='danger'>{rateForamter(thresholdB)}</Text> ({rateForamter(thresholdB2)})</Text>
                : <Text type='success'>{rateForamter(thresholdB)} </Text>
            }
          </Text>
          <Divider type='vertical' />
          <Text>祝福实时返奖率:
            <Text type='success'>{rateForamter(blessRebate)}</Text>
          </Text>
          <Divider type='vertical' />
          <Text>狂欢实时返奖率:
            <Text type='success'>{rateForamter(crazyRebate)}</Text>
          </Text>

          <Button type='link' onClick={() => { this.setState({ isEdit: true }) }}>编辑</Button>
        </Space>
      </Row>
    )
  }
}
