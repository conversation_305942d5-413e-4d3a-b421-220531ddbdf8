import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Card, Tabs, DatePicker, Button, Input } from 'antd'
import exportExcel from '@/utils/exportExcel'
import { connect } from 'dva'

var moment = require('moment')
const { RangePicker } = DatePicker
const dateFormat = 'YYYY-MM-DD'

const namespace = 'offstripDaily'
const listOffstripDailyURL = `${namespace}/listOffstripDaily`
const listOffstripRewardHistoryURL = `${namespace}/listOffstripRewardHistory`

@connect(({ offstripDaily }) => ({
  model: offstripDaily
}))

class OffstripDaily extends Component {
  constructor (props) {
    super(props)

    this.onTagChange('1')
  }

  configColumnsDaily = [
    // { title: '序号', dataIndex: 'index', align: 'center' },
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '发奖金额', dataIndex: 'outAmethyst', align: 'center' },
    { title: '发奖频道数(去重)', dataIndex: 'sidNum', align: 'center' },
    { title: '发奖子频道数(去重)', dataIndex: 'ssidNum', align: 'center' },
    { title: '发奖主持数(去重)', dataIndex: 'compereNum', align: 'center' },
    { title: '发奖用户数(去重)', dataIndex: 'playerNum', align: 'center' }
  ]

  configColumnsDetail = [
    { title: '日期', dataIndex: 'index', align: 'center', render: (text, record) => (text = moment.unix(record.timestamp).format('YYYY-MM-DD')) },
    { title: 'sid', dataIndex: 'sid', align: 'center' },
    { title: 'asid', dataIndex: 'asid', align: 'center' },
    { title: 'ssid', dataIndex: 'ssid', align: 'center' },
    { title: '公会昵称', dataIndex: 'guildName', align: 'center' },
    { title: '主持uid', dataIndex: 'compereUid', align: 'center' },
    { title: '中奖uid', dataIndex: 'uid', align: 'center' },
    { title: '奖品ID', dataIndex: 'propsId', align: 'center' },
    { title: '奖品名称', dataIndex: 'name', align: 'center' },
    { title: '奖品数量', dataIndex: 'count', align: 'center' },
    { title: '奖品价值(紫水晶)', dataIndex: 'price', align: 'center' },
    { title: '中奖时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (text = moment.unix(record.timestamp).format('HH:mm:ss')) }
  ]

  state = {
    searchDailyStartTime: 0,
    searchDailyEndTime: 0,
    dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')],
    dateRangeDetail: [moment().subtract(7, 'days'), moment().add(1, 'days')]
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  onTagChange = (record) => {
    if (record === '1') {
      this.refreshDaily()
    } else if (record === '2') {
      this.refreshRewardHistory()
    }
  }

  refreshDaily = () => {
    const { dateRange } = this.state
    const { dispatch } = this.props
    let data = {}
    if (dateRange !== undefined && dateRange !== null) {
      data = { startTime: moment(dateRange[0]).format(dateFormat), endTime: moment(dateRange[1]).format(dateFormat) }
    }

    dispatch({
      type: listOffstripDailyURL,
      payload: data
    })
  }

  refreshRewardHistory = () => {
    const { dateRangeDetail, searchASid, searchSSid, searchCompereUid, searchRewardUid } = this.state
    const { dispatch } = this.props

    let searchASidTmp = 0
    if (searchASid !== undefined && searchASid !== null && searchASid.length !== 0) {
      searchASidTmp = parseInt(searchASid)
    }

    let searchSSidTmp = 0
    if (searchSSid !== undefined && searchSSid !== null && searchSSid.length !== 0) {
      searchSSidTmp = parseInt(searchSSid)
    }

    let searchCompereUidTmp = 0
    if (searchCompereUid !== undefined && searchCompereUid !== null && searchCompereUid.length !== 0) {
      searchCompereUidTmp = parseInt(searchCompereUid)
    }

    let searchRewardUidTmp = 0
    if (searchRewardUid !== undefined && searchRewardUid !== null && searchRewardUid.length !== 0) {
      searchRewardUidTmp = parseInt(searchRewardUid)
    }

    let startTime = ''
    let endTime = ''
    if (dateRangeDetail !== undefined && dateRangeDetail !== null) {
      startTime = moment(dateRangeDetail[0]).format(dateFormat)
      endTime = moment(dateRangeDetail[1]).format(dateFormat)
    }

    let data = { startTime: startTime, endTime: endTime, asid: searchASidTmp, ssid: searchSSidTmp, compereUid: searchCompereUidTmp, rewardUid: searchRewardUidTmp }
    console.log(data)
    dispatch({
      type: listOffstripRewardHistoryURL,
      payload: data
    })
  }

  onChangeDailyDataRange = (dateRange) => {
    this.setState({ dateRange: dateRange })
  }

  onChangeDetailDataRange = (dateRangeDetail) => {
    this.setState({ dateRangeDetail: dateRangeDetail })
  }

  searchDailyHandle = () => () => {
    this.refreshDaily()
  }

  searchDetailHandle = () => () => {
    this.refreshRewardHistory()
  }

  onExport = () => {
    let headers = []
    let columns = this.configColumnsDetail
    columns.forEach(function (item) {
      headers.push({ key: item.dataIndex, header: item.title })
    })

    const { model: { detailList } } = this.props
    var exportData = detailList.map(item => {
      let v = $.extend(true, {}, item)
      v.index = moment.unix(v.timestamp).format('YYYY-MM-DD')
      v.timestamp = moment.unix(v.timestamp).format('YYYY-MM-DD HH:mm:ss')
      return v
    })

    exportExcel(headers, exportData)
  }

  render () {
    const { dateRange, dateRangeDetail } = this.state
    const { route, model: { dailyList, detailList } } = this.props
    const { TabPane } = Tabs

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='1' onChange={(record) => this.onTagChange(record)} type='card' size='large'>
            <TabPane tab='日报' key='1'>
              <span>时间范围</span>
              <RangePicker defaultValue={dateRange} format={'YYYY-MM-DD'} onChange={this.onChangeDailyDataRange} style={{ marginLeft: 5 }} />
              <Button style={{ marginLeft: 5 }} type='primary' onClick={this.searchDailyHandle()}>查询</Button>
              <Table style={{ marginTop: 10 }} rowKey={(record, index) => index} bordered dataSource={dailyList} columns={this.configColumnsDaily} pagination={this.defaultPageValue} size='small' />
            </TabPane>
            <TabPane tab='发奖明细' key='2'>
              <span>时间范围</span>
              <RangePicker defaultValue={dateRangeDetail} format={'YYYY-MM-DD'} onChange={this.onChangeDetailDataRange} style={{ marginLeft: 5 }} />
              <span style={{ marginLeft: 5 }}>asid</span>
              <Input min={0} placeholder='请输入' onChange={e => this.setState({ searchASid: e.target.value })} style={{ width: 100, marginLeft: 5 }} />
              <span style={{ marginLeft: 5 }}>ssid</span>
              <Input min={0} placeholder='请输入' onChange={e => this.setState({ searchSSid: e.target.value })} style={{ width: 100, marginLeft: 5 }} />
              <span style={{ marginLeft: 5 }}>主持uid</span>
              <Input min={0} placeholder='请输入' onChange={e => this.setState({ searchCompereUid: e.target.value })} style={{ width: 100, marginLeft: 5 }} />
              <span style={{ marginLeft: 5 }}>中奖uid</span>
              <Input min={0} placeholder='请输入' onChange={e => this.setState({ searchRewardUid: e.target.value })} style={{ width: 100, marginLeft: 5 }} />
              <Button style={{ marginLeft: 5 }} type='primary' onClick={this.searchDetailHandle()}>查询</Button>
              <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
              <Table style={{ marginTop: 10 }} rowKey={(record, index) => index} bordered dataSource={detailList} columns={this.configColumnsDetail} pagination={this.defaultPageValue} size='small' />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default OffstripDaily
