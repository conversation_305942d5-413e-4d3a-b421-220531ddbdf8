import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
// import dateString from '@/utils/dateString'
import { Table, Divider, Button, Form, Card, DatePicker } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const namespace = 'cashaponCrucialReport'
const getListUri = `${namespace}/getList`

const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker

@connect(({ cashaponCrucialReport }) => ({
  model: cashaponCrucialReport
}))
class CashaponCrucialReport extends Component {
  // column structs.
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '扭蛋总流水', dataIndex: 'incoming', align: 'center' },
    { title: '扭蛋用户数', dataIndex: 'totalUsers', align: 'center' },
    { title: '扭蛋新增用户数', dataIndex: 'newPlayers', align: 'center' },
    { title: '剩余累计奖池', dataIndex: 'jackpotLeft', align: 'center' },
    { title: '中奖总流水', dataIndex: 'rewardPrice', align: 'center' },
    { title: '预算支出(紫水晶)', dataIndex: 'budgetOutlay', align: 'center' }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, dateRange: [moment().subtract(7, 'days'), moment().subtract(-1, 'days')] }

  // get list from server.
  componentDidMount () {
    const { dispatch } = this.props
    const { dateRange } = this.state
    var data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  onChange = (date, format) => {
    this.setState({ dateRange: date })
  }

  onClick = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    var data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    dispatch({
      type: getListUri,
      payload: data
    })
  }
  // content
  render () {
    const { route, model: { list } } = this.props
    const { dateRange } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <div>
              日期范围
              <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
              <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
            </div>
            <Divider />
            <Table dataSource={list} columns={this.columns} rowKey={(record, index) => index} pagination={this.defaultPageValue} size='small' />
          </Form>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default CashaponCrucialReport
