import { genGetListTemplate } from '@/utils/common'

const getDailyList = genGetListTemplate('/activityconfig/fire_power_pk/get_statistics_data', 'dailyState', (raw) => {
  for (let i = 0; i < raw.length; i++) raw[i].key = raw[i].date
  raw = raw.sort((a, b) => { return a.date > b.date ? -1 : 1 })
  return raw
})

const getWeeklyList = genGetListTemplate('/activityconfig/fire_power_pk/get_statistics_data', 'weeklyState', (raw) => {
  for (let i = 0; i < raw.length; i++) raw[i].key = raw[i].date
  raw = raw.sort((a, b) => { return a.date > b.date ? -1 : 1 })
  return raw
})

export default {
  namespace: 'firePowerPK',
  state: {
    dailyState: [],
    weeklyState: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },

  effects: {
    getDailyList,
    getWeeklyList
  }
}
