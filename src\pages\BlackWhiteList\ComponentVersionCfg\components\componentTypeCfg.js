/* eslint-disable eqeqeq */
import React, { Component } from 'react'
import { connect } from 'dva'
import { timeFormater } from '@/utils/common'
import { Modal, Form, Input, Row, Col, Button, Table, Tooltip, message, Switch } from 'antd'
import { CloseCircleOutlined, CheckCircleOutlined } from '@ant-design/icons'

const namespace = 'componentVersionCfg'

@connect(({ componentVersionCfg }) => ({
  model: componentVersionCfg
}))

class ComponentTypeCfg extends Component {
  state = {
    modelVisable: false,
    opTypeIsAdd: true
  }
  componentDidMount = () => {}

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 修改、删除操作 html代码
  operationHtml = (record) => {
    return (
      <>
        <a style={{ color: '#1890ff', fontSize: '1.2em', marginRight: '1em' }}
          onClick={() => {
            this.setState({ modelVisable: true, opTypeIsAdd: false })
            setTimeout(() => {
              this.formRef.setFieldsValue(record)
            }, 500)
          }
          }>
          修改
        </a>
      </>
    )
  }
  // 提交新增或修改业务请求
  submitAddOrUpdateRequire = (v) => {
    const { opTypeIsAdd } = this.state
    if (opTypeIsAdd) {
      v.opType = 'ADD'
      v.id = 0
    } else {
      v.opType = 'UPDATE'
    }
    v.opUid = 0
    v.timestamp = 0

    this.callModel('addOrUpdateComponentType', {
      params: v,
      cbFunc: (ok) => {
        if (ok) {
          message.success(opTypeIsAdd ? '新增成功' : '更新成功')
          this.callModel('getAllComponentTypeList')
          this.formRef.resetFields()
          this.callModel('getAllComponentTypeList')
          this.setState({ modelVisable: false })
        } else {
          message.warn(opTypeIsAdd ? '新增' : '更新' + '失败，请稍后重试...')
        }
      } })
  }
  // 创建tooltip
  createTooltip = (v, width = 10) => {
    return !v ? '-' : <Tooltip title={v}>
      <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', width: `${width}em` }}>{v}</div>
    </Tooltip>
  }
  render () {
    const { componentTypeList } = this.props.model
    const { modelVisable, opTypeIsAdd } = this.state

    const columns = [
      { title: '业务名称', dataIndex: 'key', align: 'center' },
      { title: '业务别名', dataIndex: 'alias', align: 'center' },
      { title: '备注信息', dataIndex: 'tag', align: 'center' },
      { title: '是否可用', dataIndex: 'isValid', align: 'center', render: (v) => { return v ? <CheckCircleOutlined style={{ fontSize: '1.2em' }} /> : <CloseCircleOutlined style={{ fontSize: '1.2em', color: '#aeaeae5e' }} /> } },
      { title: '更新人uid', dataIndex: 'opUid', align: 'center' },
      { title: '更新时间', dataIndex: 'timestamp', align: 'center', render: (v) => { return timeFormater(v) } },
      { title: '操作',
        align: 'center',
        render: (v, r) => {
          return this.operationHtml(r)
        } }
    ]

    return (
      <div>
        <Row style={{ marginBottom: '1em' }} >
          <Button type='primary' onClick={() => {
            this.setState({ modelVisable: true, opTypeIsAdd: true })
            setTimeout(() => { this.formRef.resetFields() }, 100)
          }} >
            新增业务
          </Button>
        </Row>
        <Row>
          <Col span={24}>
            <Table columns={columns} dataSource={componentTypeList} size='small' scroll={{ x: 'max-content' }} />
          </Col>
        </Row>
        {/* 新增或修改 */}
        <Modal visible={modelVisable} title={opTypeIsAdd ? '新增业务' : '修改业务信息'} okText={opTypeIsAdd ? '确认新增' : '确认修改'} cancelText='取消'
          onCancel={() => { this.setState({ modelVisable: false }) }} onOk={() => { this.formRef.submit() }} >
          <Form onFinish={(v) => { this.submitAddOrUpdateRequire(v) }} ref={(v) => { this.formRef = v }}
            labelCol={{ span: 6, align: 'left' }} initialValues={{ key: '', alias: '', isValid: true, tag: '' }}>
            <Form.Item label='业务key' name='key' rules={[{ required: true, message: '业务key不能为空' }, { pattern: /^[a-zA-Z0-9][\w-_]{1,149}$/, message: '包含非法字符~' }]} >
              <Input disabled={!opTypeIsAdd} placeholder='由大字母、数字,下划线或中划线组成~' />
            </Form.Item>
            <Form.Item label='业务别名' name='alias' rules={[{ required: true }]}>
              <Input />
            </Form.Item>
            <Form.Item label='是否可用' name='isValid' valuePropName='checked' >
              <Switch checkedChildren='可用' unCheckedChildren='不可' size='default' onChange={(v) => this.formRef.setFieldsValue({ 'isVisible': v })} />
            </Form.Item>
            <Form.Item label='修改备注' name='tag'>
              <Input />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    )
  }
}

export default ComponentTypeCfg
