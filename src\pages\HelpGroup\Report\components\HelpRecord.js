import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Table, DatePicker, Input, Button, message } from 'antd'
import { stringify } from 'qs'

const namespace = 'helpGroupReport'

var moment = require('moment')
var dateFormat = 'YYYY-MM-DD'

@connect(({ helpGroupReport }) => ({
  model: helpGroupReport
}))

class HelpRecord extends Component {
  columns = [
    { title: '发放时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (record.timestamp === 0 ? '' : moment.unix(record.timestamp).format(dateFormat)) },
    { title: '发放人UID', dataIndex: 'sendUid', align: 'center' },
    { title: '发放人昵称', dataIndex: 'sendNick', align: 'center' },
    { title: '接收人UID', dataIndex: 'recvUid', align: 'center' },
    { title: '接收人昵称', dataIndex: 'recvNick', align: 'center' },
    { title: '发放金额(元)', dataIndex: 'sendAmount', align: 'center', render: (text, record) => (record.sendAmount / 1000) },
    { title: '帮扶内容', dataIndex: 'helpProjectName', align: 'center' },
    { title: '个', dataIndex: 'helpProjectNum', align: 'center' }
  ]

  state = {
    visible: false
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  componentDidMount () {
    this.loadData()
  }
  
  searchHandle = () => () => {
    this.loadData()
  }

  searchParam = (isExport) => {
    const { searchSendUid, searchSendYY, searchRecvUid, searchRecvYY, searchCreateStartTime, searchCreateEndTime } = this.state

    let searchCreateStartTimeTmp
    let searchCreateEndTimeTmp

    if (searchCreateStartTime) {
      searchCreateStartTimeTmp = moment(searchCreateStartTime).format(dateFormat)
    }
    if (searchCreateEndTime) {
      searchCreateEndTimeTmp = moment(searchCreateEndTime).format(dateFormat)
    }

    if (searchCreateStartTimeTmp !== '' && searchCreateEndTimeTmp !== '' && searchCreateStartTimeTmp > searchCreateEndTimeTmp) {
      message.warn('开始时间不能大于结束时间')
      return
    }

    let sendUid
    if (searchSendUid && searchSendUid !== '') {
      sendUid = Number(searchSendUid)
    }
    
    let recvUid
    if (searchRecvUid && searchRecvUid !== '') {
      recvUid = Number(searchRecvUid)
    }

    let recvYY
    if (searchRecvYY && searchRecvYY !== '') {
      recvYY = Number(searchRecvYY)
    }

    let sendYY
    if (searchSendYY && searchSendYY !== '') {
      sendYY = Number(searchSendYY)
    }

    let data = { sendUid: sendUid, recvUid: recvUid, sendYY: sendYY, recvYY: recvYY, createStartTime: searchCreateStartTimeTmp, createEndTime: searchCreateEndTimeTmp, export: isExport }
    return data
  }

  exportURL = () => {
    let data = this.searchParam(1)

    return `/fts_hgame/boss/help_group/list_statistics?${stringify(data)}`
  }

  loadData = () => {
    const { dispatch } = this.props

    let data = this.searchParam()

    dispatch({
      type: `${namespace}/listStatistics`,
      payload: data
    })
  }

  render () {
    const { model: { listReport } } = this.props
 
    return (
      <Card> 
        <span>发送UID</span>
        <Input allowClear min={0} placeholder='请输入uid' onChange={e => this.setState({ searchSendUid: e.target.value })} style={{ width: 130, marginLeft: 3 }} />
        
        <span style={{ marginLeft: 20 }}>发送YY</span>
        <Input allowClear min={0} placeholder='请输入yy' onChange={e => this.setState({ searchSendYY: e.target.value })} style={{ width: 130, marginLeft: 3 }} />

        <span style={{ marginLeft: 20 }}>接收UID</span>
        <Input allowClear min={0} placeholder='请输入uid' onChange={e => this.setState({ searchRecvUid: e.target.value })} style={{ width: 130, marginLeft: 3 }} />
        
        <span style={{ marginLeft: 20 }}>接收YY</span>
        <Input allowClear min={0} placeholder='请输入yy' onChange={e => this.setState({ searchRecvYY: e.target.value })} style={{ width: 130, marginLeft: 3 }} />

        <span style={{ marginLeft: 20 }}>添加时间</span>
        <DatePicker
          format='YYYY-MM-DD'
          placeholder='开始时间'
          onChange={(v) => this.setState({ searchCreateStartTime: v })}
          style={{ marginLeft: 10 }}
        />
        <span style={{ marginLeft: 3 }}>~</span>
        <DatePicker
          format='YYYY-MM-DD'
          placeholder='结束时间'
          onChange={(v) => this.setState({ searchCreateEndTime: v })}
          style={{ marginLeft: 3 }}
        />
        <Button style={{ marginLeft: 5 }} type='primary' onClick={this.searchHandle()}>查询</Button>
        <Button style={{ marginLeft: 5 }} type='primary'><a href={this.exportURL()} target='_blank'>导出</a></Button>
        <Table style={{ marginTop: 10 }} rowKey='idx' pagination={this.defaultPageValue} columns={this.columns} dataSource={listReport} />
 
      </Card>
    )
  }
}

export default HelpRecord
