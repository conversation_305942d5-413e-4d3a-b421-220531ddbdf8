import React, { Component } from 'react'
import { connect } from 'dva' 
import dateString from '@/utils/dateString'
import { Table, message, Input, Modal, Form, Select, Popover, Button, DatePicker, Tooltip, Popconfirm } from 'antd'

const namespace = 'dailyWithdrawalWhitelist'

const { TextArea } = Input
const Option = Select.Option

const whitelistTab = 'whitelist'
const assessmentTab = 'assessment'

@connect(({ dailyWithdrawalWhitelist }) => ({
  model: dailyWithdrawalWhitelist
}))

class DailyWithdrawalWhitelist extends Component {
  componentDidMount () {
    this.refreshInfo()
  }

  columns = [
    { title: '申请时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (record.timestamp === 0 ? '' : dateString(record.timestamp)) },
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: 'YY号', dataIndex: 'yy', align: 'center' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: '签约家族id', dataIndex: 'familyId', align: 'center' },
    { title: '提现周期', dataIndex: 'settleCycle', align: 'center', render: (text, record) => (record.settleCycle === 4 ? '日提' : '') },
    { title: '结算周期', dataIndex: 'intervalHour', align: 'center', render: (text, record) => (record.intervalHour === 0 ? '' : record.intervalHour + '小时') },
    { title: '操作人', dataIndex: 'optUser', align: 'center' },
    { title: '操作', key: 'operation', align: 'center', render: (text, record) => (<span><Popover content={this.renderContent(record)} trigger='click'><a>取消日提资格</a></Popover></span>) }
  ]

  state = {
    visible: false, 
    hadClickCheck: false,
    intervalHour: 48
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  refreshInfo = () => {
    const { searchUID, searchYY, searchStartTime, searchEndTime } = this.state

    let searchStartTimeTmp = 0
    let searchEndTimeTmp = 0
    if (searchStartTime) {
      let nowTimeDate = new Date(searchStartTime)
      nowTimeDate.setHours(0, 0, 0, 0)
      searchStartTimeTmp = nowTimeDate.getTime() / 1000
    }
    if (searchEndTime) {
      let nowTimeDate = new Date(searchEndTime)
      nowTimeDate.setHours(0, 0, 0, 0)
      searchEndTimeTmp = nowTimeDate.getTime() / 1000 + (24 * 3600)
      console.log(searchEndTimeTmp)
    }

    if (searchStartTimeTmp !== 0 && searchEndTimeTmp !== 0 && searchStartTimeTmp > searchEndTimeTmp) {
      message.warn('开始时间不能大于结束时间')
      return
    }

    this.props.dispatch({
      type: `${namespace}/listInfo`,
      payload: { uid: searchUID, yy: searchYY, startTime: searchStartTimeTmp, endTime: searchEndTimeTmp }
    })

    this.props.dispatch({
      type: `${namespace}/getLeastAmt`
    })
  }

  renderContent = (record) => {
    return (
      <div>
        <Input.TextArea onChange={this.onTextChange} row={5} placeholder={'删除原因选填，最多100字符'} />
        <Button onClick={this.deleteHandle(record)} style={{ marginLeft: 120, marginTop: 5 }} type='primary'>确定</Button>
      </div>
    )
  }

  onTextChange = e => {
    this.setState({ removeReason: e.target.value })
  }

  deleteHandle = (record) => () => {
    const { removeReason } = this.state
    const { dispatch } = this.props
    let data = { uid: record.uid, removeReason: removeReason }
    console.log(data)
    dispatch({
      type: `${namespace}/deleteInfo`,
      payload: data
    })
  }

  showModel = () => () => {
    const { intervalHour } = this.state
    let intervalHourTmp = intervalHour
    if (intervalHourTmp === 0) {
      intervalHourTmp = 48
      if (this.formRef) {
        this.formRef.setFieldsValue({ intervalHour: intervalHourTmp })
      }
    }
    console.log(intervalHourTmp)
    this.setState({ visible: true, intervalHour: intervalHourTmp })
  }

  searchHandle = () => () => {
    this.refreshInfo()
  }

  batchDeleteHandle = () => () => {
    const { removeKey } = this.state
    console.log(removeKey)

    let data = { uids: removeKey, removeReason: '' }
    console.log(data)
    this.props.dispatch({
      type: `${namespace}/batchDeleteInfo`,
      payload: data
    })
  }

  hiddenModal = () => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.setState({ visible: false, inputUIDList: '', intervalHour: 0, conformUidList: [], hadClickCheck: false })
  }

  handleCancel = e => {
    this.hiddenModal()
  }

  handleSubmit = e => {
    if (this.formRef) {
      this.formRef.submit()
    }
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onFinish = values => {
    const { conformUidList, hadClickCheck } = this.state
    const { dispatch } = this.props
    console.log(values, conformUidList)

    if (!hadClickCheck) {
      message.warning('未检测结果, 请先检测结果')
      return
    }

    if (conformUidList === null || conformUidList === undefined || conformUidList === '') {
      message.warning('没有符合的uid添加')
      return
    }

    let data = { intervalHour: values.intervalHour, uidList: conformUidList }
    console.log(data)
    dispatch({
      type: `${namespace}/addInfo`,
      payload: data
    })

    this.hiddenModal()
  }

  checkLegitimacyhandle = () => () => {
    const { inputUIDList, intervalHour } = this.state 

    const { dispatch, model: { list } } = this.props
    if (inputUIDList === undefined || inputUIDList === '') {
      message.warning('请输入要修改的UID')
      return
    }

    if (intervalHour === null || intervalHour === undefined || intervalHour === 0) {
      message.warning('请选择提现周期')
      return
    }

    let listMap = {}
    for (let i = 0; i < list.length; i++) {
      listMap[list[i].uid] = list[i].intervalHour
    }
    console.log(listMap)

    let uidList = ''
    let uidListTmp = []
    if (inputUIDList && inputUIDList.length > 0) {
      uidListTmp = inputUIDList.split('\n')
      uidList = uidListTmp.join(',')
    }

    let conformUidList = []
    for (let i = 0; i < uidListTmp.length; i++) {
      const uid = Number(uidListTmp[i])
      conformUidList.push(uid)
    }

    let updateUIDInfo = (conformUid, unConformUid) => {
      if (this.formRef) {
        let conformUidStr = ''
        if (conformUid && conformUid.length > 0) {
          conformUidStr = conformUid.join('\n')
        }
        let unConformUidStr = ''
        if (unConformUid && unConformUid.length > 0) {
          unConformUidStr = unConformUid.join('\n')
        }
        this.formRef.setFieldsValue({ conformUid: conformUidStr, unConformUid: unConformUidStr })
        this.setState({ conformUidList: conformUid, hadClickCheck: true })
      }
    }

    dispatch({
      type: `${namespace}/checkLegitimacy`,
      payload: { play: { uidList: uidList }, func: updateUIDInfo }
    })
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(selectedRowKeys, selectedRows)
      var value = selectedRows.map(item => item.uid).join(',')
      console.log(value)
      this.setState({ removeKey: value })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User',
      name: record.name
    })
  }

  onSearchYY = e => {
    console.log(e)
    this.setState({ searchYY: null })
  }

  checkUid = uid => {
    if (uid === undefined) {
      message.error('Error: uid is undefined!')
      return false
    }
    uid += ''
    const uidReg = /^[1-9][0-9]{0,19}$/
    if (!uidReg.test(uid)) {
      message.error('输入有误，请检查后再提交 (错误内容=' + uid + ')')
      console.warn('unexpect input:' + uid)
      return false
    }
    return true
  }

  // 批量输入完成
  onBatchInputComplete = () => {
    const { searchMultiUID } = this.state
    let tmpInput = searchMultiUID.trim()
    if (tmpInput === '') {
      message.warn('数据为空, 请检查')
      return
    }
    if (tmpInput.indexOf(',') >= 0) {
      message.warn('输入格式不正确，请使用换行副隔开每个元素')
      return
    }
    tmpInput = tmpInput.replaceAll(' ', '')
    let result = tmpInput.replaceAll('\n', ',')
    let tmpSlice = result.split(',')
    if (tmpSlice.length > 1000 || result.length > 1000) { // 限制批量查询数量,确保不超出url长度限制
      message.warn('批量查询数据量过大，请检查输入')
      return
    }
    for (let i = 0; i < tmpSlice.length; i++) {
      if (!this.checkUid(tmpSlice[i])) {
        return
      }
    }

    console.log(result)
    if (this.inputRef) { // 设置小输入框的值
      this.inputRef.setValue(result)
    }
    this.setState({ searchUID: result, showUIDModel: false })
    setTimeout(() => {
      this.refreshInfo()
    }, 100)
  }

  // 批量输入完成
  onBatchInputCompleteYY = () => {
    const { searchMultiYY } = this.state
    let tmpInput = searchMultiYY.trim()
    if (tmpInput === '') {
      message.warn('数据为空, 请检查')
      return
    }
    if (tmpInput.indexOf(',') >= 0) {
      message.warn('输入格式不正确，请使用换行副隔开每个元素')
      return
    }
    tmpInput = tmpInput.replaceAll(' ', '')
    let result = tmpInput.replaceAll('\n', ',')
    let tmpSlice = result.split(',')
    if (tmpSlice.length > 1000 || result.length > 1000) { // 限制批量查询数量,确保不超出url长度限制
      message.warn('批量查询数据量过大，请检查输入')
      return
    }
    for (let i = 0; i < tmpSlice.length; i++) {
      if (!this.checkUid(tmpSlice[i])) {
        return
      }
    }

    console.log(result)
    if (this.inputRefYY) { // 设置小输入框的值
      this.inputRefYY.setValue(result)
    }
    this.setState({ searchYY: result, showYYModel: false })
    setTimeout(() => {
      this.refreshInfo()
    }, 100)
  }

  onChangeTab = tab => {
    const { dispatch } = this.props
    console.log('tab: ' + tab)
    if (tab === whitelistTab) {
      dispatch({
        type: `${namespace}/listInfo`,
        payload: {}
      })
    } else if (tab === assessmentTab) {
      dispatch({
        type: `${namespace}/getAssessmentList`,
        payload: { his: true }
      })
    }
  }
  
  render () {
    const { visible, showUIDModel, showYYModel } = this.state
    const { list } = this.props.model

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 10 }
      },
      wrapperCol: {
        xs: { span: 2 },
        sm: { span: 15 }
      }
    }
 
    return (
      <div>
        <div><Button type='primary' onClick={this.showModel()}>添加日提白名单</Button></div>
        <div style={{ marginTop: 10 }} />

        <Tooltip title='批量输入请双击'>
          UID：
          <Input
            allowClear
            ref={(input) => { this.inputRef = input }}
            style={{ width: 200, marginLeft: 5, marginRight: 5 }}
            placeholder={'批量输入请双击'}
            onChange={(e) => { this.setState({ searchUID: e.target.value }) }}
            onDoubleClick={(e) => { this.setState({ showUIDModel: true }) }}
          />
        </Tooltip>

        <Tooltip title='批量输入请双击'>
          YY：
          <Input
            allowClear
            ref={(input) => { this.inputRefYY = input }}
            style={{ width: 200, marginLeft: 5, marginRight: 5 }}
            placeholder={'批量输入请双击'}
            onChange={(e) => { this.setState({ searchYY: e.target.value }) }}
            onDoubleClick={(e) => { this.setState({ showYYModel: true }) }}
          />
        </Tooltip>
        <DatePicker
          format='YYYY-MM-DD'
          placeholder='开始时间'
          onChange={(v) => this.setState({ searchStartTime: v })}
          style={{ marginLeft: 10 }}
        />
        <span style={{ marginLeft: 5 }}>~</span>
        <DatePicker
          format='YYYY-MM-DD'
          placeholder='结束时间'
          onChange={(v) => this.setState({ searchEndTime: v })}
          style={{ marginLeft: 5 }}
        />
        <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
        <Popconfirm title='确认批量删除?' onConfirm={this.batchDeleteHandle()} okText='是' cancelText='否'>
          <Button style={{ marginLeft: 20 }} type='primary'>批量删除</Button>
        </Popconfirm>
        <Table style={{ marginTop: 10 }} tabelLayout='fixed' rowSelection={this.rowSelection} rowKey={(record, index) => record.uid} bordered dataSource={list} columns={this.columns} pagination={this.defaultPageValue} />

        <Modal
          title={'批量查询UID'}
          visible={showUIDModel}
          onCancel={() => { this.setState({ showUIDModel: false }) }}
          onOk={(v) => { this.onBatchInputComplete(v) }}
          centered
        >
          <Input.TextArea
            allowClear
            placeholder='tip:每项数据占一行, 限制500行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiUID: e.target.value }) }}
          />
        </Modal>

        <Modal
          title={'批量查询YY'}
          visible={showYYModel}
          onCancel={() => { this.setState({ showYYModel: false }) }}
          onOk={(v) => { this.onBatchInputCompleteYY(v) }}
          centered
        >
          <Input.TextArea
            allowClear
            placeholder='tip:每项数据占一行, 限制500行以内'
            autoSize={{ minRows: 6, maxRows: 12 }}
            onChange={(e) => { this.setState({ searchMultiYY: e.target.value }) }}
          />
        </Modal>

        <Modal forceRender width={500} visible={visible} title='添加日提白名单' onCancel={this.handleCancel} onOk={this.handleSubmit} okText='提交审核'>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <div style={{ marginLeft: 235 }}><font color='red'>选项有24小时, 48小时, 72小时</font></div>
            <Form.Item label='修改提现周期' name='intervalHour' rules={[{ required: true }]}>
              <Select style={{ marginLeft: 5 }} placeholder='请选择' defaultValue={48} onChange={(v) => this.setState({ intervalHour: v })}>
                <Option value={24}>24小时</Option>
                <Option value={48}>48小时</Option>
                <Option value={72}>72小时</Option>
              </Select>
            </Form.Item>
            <Form.Item label='请输入要修改的UID' name='uidList' rules={[{ required: true }]}>
              <TextArea rows={4} placeholder='UID(回车换行)' onChange={e => this.setState({ inputUIDList: e.target.value })} />
            </Form.Item>
            <Button style={{ marginLeft: 350 }} type='primary' onClick={this.checkLegitimacyhandle()}>检查结果</Button>
            <div style={{ marginTop: 10 }} />
            <Form.Item label='符合修改要求的UID' name='conformUid'>
              <TextArea readOnly rows={4} />
            </Form.Item>
            <Form.Item label='不符合修改要求的UID' name='unConformUid'>
              <TextArea disabled rows={4} />
            </Form.Item>
          </Form>
        </Modal>

      </div>
    )
  }
}

export default DailyWithdrawalWhitelist
