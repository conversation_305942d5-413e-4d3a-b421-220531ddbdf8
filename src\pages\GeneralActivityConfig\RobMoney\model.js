import {
  settlementScoreRewardFromServer,
  preScoreRewardFromServer,
  upsetScoreRewardFromServer,
  getScoreRewardListFromServer,
  getActCfgListFromServer,
  upsetActCfgFromServer,
  removeActCfgFromServer,
  getMoneyCfgListFromServer,
  upsetMoneyCfgFromServer,
  removeMoneyCfgFromServer,
  removeDescCfgFromServer,
  upsetDescCfgFromServer,
  getDescCfgListFromServer,
  getBlackCompereFromServer,
  delBlackCompereFromServer,
  addBlackCompereFromServer,
  getPkRewrdDetailFromServer
} from './api'
import { message } from 'antd'

export default {
  namespace: 'robMoney',

  state: {
    list: [],
    actCfgList: [], // 活动配置
    moneyList: [], // 带入金配置
    descList: [], // 文案配置
    scoreList: [], // 积分
    blackList: [], // 黑名单主持
    pkList: [] // PK对战信息查询
  },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    },

    updateActCfgList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        actCfgList: payload
      }
    },

    updateMoneyCfgList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        moneyList: payload
      }
    },

    updateDescCfgList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        descList: payload
      }
    },

    updateScoreRewardList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        scoreList: payload
      }
    },

    updateBlackCompereList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        blackList: payload
      }
    },

    updatePkList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        pkList: payload
      }
    }
  },

  effects: {
    // 时间配置
    * getActCfgList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getActCfgListFromServer)

      yield put({
        type: 'updateActCfgList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * upsetActCfgList ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(upsetActCfgFromServer, payload)
      if (status === 0) {
        message.success('add success')
        yield put({
          type: 'getActCfgList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * removeActCfgList ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(removeActCfgFromServer, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getActCfgList'
        })
      } else {
        message.error('failed ' + msg)
      }
    },

    // 带入金配置
    * getMoneyCfgList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getMoneyCfgListFromServer)

      yield put({
        type: 'updateMoneyCfgList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * upsetMoneyCfgList ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(upsetMoneyCfgFromServer, payload)
      if (status === 0) {
        message.success('add success')
        yield put({
          type: 'getMoneyCfgList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * removeMoneyCfgList ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(removeMoneyCfgFromServer, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getMoneyCfgList'
        })
      } else {
        message.error('failed ' + msg)
      }
    },

    // 文案配置
    * getDescCfgList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getDescCfgListFromServer)

      yield put({
        type: 'updateDescCfgList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * upsetDescCfgList ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(upsetDescCfgFromServer, payload)
      if (status === 0) {
        message.success('add success')
        yield put({
          type: 'getDescCfgList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * removeDescCfgList ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(removeDescCfgFromServer, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getDescCfgList'
        })
      } else {
        message.error('failed ' + msg)
      }
    },

    // 积分结算
    * getScoreRewardList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getScoreRewardListFromServer)

      yield put({
        type: 'updateScoreRewardList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * preScoreRewardList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(preScoreRewardFromServer)

      yield put({
        type: 'getScoreRewardList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * settlmentScoreRewardList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(settlementScoreRewardFromServer)

      yield put({
        type: 'getScoreRewardList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * upsetScoreRewardList ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(upsetScoreRewardFromServer, payload)
      if (status === 0) {
        message.success('add success')
        yield put({
          type: 'getScoreRewardList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    // 黑名单配置
    * getBlackCompereList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getBlackCompereFromServer)

      yield put({
        type: 'updateBlackCompereList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * upsetBlackCompereList ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(addBlackCompereFromServer, payload)
      if (status === 0) {
        message.success('add success')
        yield put({
          type: 'getBlackCompereList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * removeBlackCompereList ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(delBlackCompereFromServer, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getBlackCompereList'
        })
      } else {
        message.error('failed ' + msg)
      }
    },

    * getPkList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getPkRewrdDetailFromServer, payload)

      yield put({
        type: 'updatePkList',
        payload: Array.isArray(list) ? list : []
      })
    }
  }
}
