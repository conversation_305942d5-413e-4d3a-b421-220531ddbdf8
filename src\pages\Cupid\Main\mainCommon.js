/* eslint-disable no-unused-vars */
import React from 'react'
import { Typo<PERSON>, Tooltip, Divider, Row, Col, message, InputNumber, Space } from 'antd'
import { timeFormater } from '@/utils/common'
import PopImage2 from '@/components/PopImage2'
const { Text } = Typography

const probabilityBase = 10000

// =================== 常量枚举 =========================

// 语音房业务广播类型枚举
export const broadcastOptionsVR = [
  { label: '无广播', value: 0 },
  { label: '本房间', value: 1 },
  { label: '本家族', value: 5 },
  { label: '全服', value: 3 }
]

// TODO: 废弃
export const broadcastOptionsJY = [
  { label: '无广播', value: 0 },
  { label: '子频道', value: 1 },
  { label: '全频道', value: 2 },
  { label: '全服', value: 3 }
]

// 默认疯狂礼物
export const defaultCrazyItem = {
  id: 1,
  ballName: '狂欢礼物1',
  prizeId: '10002',
  appId: 34,
  propsId: 340009,
  name: '掌声',
  url: 'https://turnover-cdn.yy.com/zw/static/340009.png?v=78',
  count: 1,
  value: 100,
  rate: 5000,
  thresholdR: 3300
}

// =================== 工具函数 =========================

// 解析配置content
export const parseNormalPoolContent = (content) => {
  let list = []
  if (content) {
    try {
      list = JSON.parse(content)
    } catch (e) {
      message.error('json convert error', e)
    }
  }
  return list
}

export const parseCrazyPoolContent = (content) => {
  let list = {}
  if (content) {
    try {
      list = JSON.parse(content)
    } catch (e) {
      message.error('json convert error', e)
    }
  }
  return list
}

// =================== Formater ========================

// 广播类型格式化-语音房业务
const broadcastTypeFormater = (v) => {
  let label = ''
  broadcastOptionsVR.forEach(item => {
    if (item.value === v) {
      label = item.label
    }
  })
  return label
}

// ================= Table Columns =========================

// 常规配置表头
export const normalColumn = [
  { title: '编号', dataIndex: 'id' },
  { title: 'prizeId', dataIndex: 'prizeId', dft_hidden: true },
  { title: '奖池名称', dataIndex: 'ballName' },
  { title: '奖池类型', dataIndex: 'ballType' },
  // { title: 'appId', dataIndex: 'appId', dft_hidden: true },
  { title: '营收ID', dataIndex: 'propsId' },
  { title: '奖品名称', dataIndex: 'name' },
  { title: '礼物图', dataIndex: 'url', dft_hidden: true, render: (v) => { return <PopImage2 value={v} height='2em' /> } },
  { title: '奖品数量', dataIndex: 'count', render: (v) => { return Number(v).toLocaleString(v) } },
  { title: '单价', dataIndex: 'value', render: (v) => { return Number(v).toLocaleString() } },
  { title: '单次消耗', dataIndex: 'consume', dft_hidden: true, render: (v) => { return Number(v).toLocaleString() } },
  { title: '常规数量区间', dataIndex: 'normalRange', render: (v) => { return rangeFormater(v) } },
  { title: '狂欢数量区间', dataIndex: 'crazyRange', render: (v) => { return rangeFormater(v) } },
  { title: '中奖概率', dataIndex: 'rate', render: (v) => { return rateForamter(v) } },
  // eslint-disable-next-line block-spacing
  { title: '实时返奖率', dataIndex: 'rebate', dft_hidden: true, render: (v) => {return rateForamter(v)} },
  { title: '返奖率阈值', dataIndex: 'thresholdR', render: (v) => { return rateForamter(v) } },
  { title: '阈值生效金额(金钻)', dataIndex: 'thresholdA', render: (v) => { return Number(v).toLocaleString() } },
  { title: '祝福值', dataIndex: 'blessLimit' },
  { title: '广播类型', dataIndex: 'bcType', render: (v) => { return broadcastTypeFormater(v) } }
].map(item => {
  item.align = 'center'
  return item
})

export const crazyColumn = [
  { title: '编号', dataIndex: 'id' },
  { title: 'prizeId', dataIndex: 'prizeId', dft_hidden: true },
  { title: '营收ID', dataIndex: 'propsId' },
  { title: '奖品名称', dataIndex: 'name' },
  { title: '礼物图', dataIndex: 'url', dft_hidden: true, render: (v) => { return <PopImage2 value={v} height='2em' /> } },
  { title: '单价', dataIndex: 'value', render: (v) => { return Number(v).toLocaleString() } },
  { title: '奖品数量', dataIndex: 'count', render: (v) => { return Number(v).toLocaleString(v) } },
  { title: '中奖概率', dataIndex: 'rate', render: (v) => { return rateForamter(v) } },
  { title: '返奖率阈值', dataIndex: 'thresholdR', render: (v) => { return rateForamter(v) } },
  { title: '广播类型', dataIndex: 'bcType', render: (v) => { return broadcastTypeFormater(v) } }
].map(item => {
  item.align = 'center'
  return item
})

// 厘转元
const rmbFormater = (v) => {
  return Number(v / 1000.0).toFixed(2)
}

// 范围格式化
const rangeFormater = (v) => {
  if (!Array.isArray(v) || v.length < 2) {
    return `${v} ?`
  }
  const [s, e] = v
  return `[${s}, ${e}]`
}

// 概率格式化
export const rateForamter = (v) => {
  if (v === 0 || v === undefined) {
    return '0.00%'
  }
  return `${Number(v * 100.0 / probabilityBase).toFixed(2)}%`
}

// ================= 组件放下面 ===========================

// 范围编辑器
export const ArrayEditor = ({ value, onChange }) => {
  if (!value) {
    value = []
  }
  return <Space>
    <InputNumber value={value[0]} onChange={v => { value[0] = v; onChange(value) }} />
    <span>~</span>
    <InputNumber value={value[1]} onChange={v => { value[1] = v; onChange(value) }} />
  </Space>
}

// 概率编辑器
export const RateEditor = ({ value, onChange }) => {
  const fixedValue = Number(value * 100.0 / probabilityBase).toFixed(2)
  return <InputNumber value={fixedValue} onChange={v => { onChange(Number(v) * probabilityBase / 100.0) }} />
}

// 展示审批信息
export const AprInfoDesc = (props) => {
  const { value } = props
  const { aprUid, aprNick, aprRemark, aprResult, aprTime } = value || {}
  const { opUid, opNick, opRemark, opTime } = value || {}
  let statusColor = ['warning', 'danger', 'success'][['OnGoing', 'Rejected', 'Passed'].indexOf(aprResult)]
  let statusTip = ['待审批', '被驳回', '已通过'][['OnGoing', 'Rejected', 'Passed'].indexOf(aprResult)]
  return (
    <Row>
      <Col>
        <Row>提交人：
          <Text type='secondary'>
            <Tooltip title={opUid}>{`${opNick}_(${timeFormater(opTime)})`}</Tooltip> <Divider type='vertical' />
          </Text>
        </Row>
        <Row>审批人：<Text type='secondary'> {aprUid === 0 ? '系统自动审批' : aprNick}_({timeFormater(aprTime)}) <Divider type='vertical' /></Text> </Row>
      </Col>
      <Col>
        <Row>申请理由：<Text type='secondary'> {opRemark || '(空)'} <Divider type='vertical' /></Text></Row>
        <Row>审批备注：<Text type='secondary'> {aprRemark || '(空)'}</Text><Divider type='vertical' /></Row>
      </Col>
      <Col>
        <Row><Text style={{ height: '1.5715em', width: '1em' }} /></Row>
        <Row>审批结果：<Text type={statusColor}> {statusTip}</Text></Row>
      </Col>
    </Row>
  )
}
