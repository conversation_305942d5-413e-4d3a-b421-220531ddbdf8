// 概述：解析和渲染markdown格式文本
import React, { useState } from 'react'
import { Row, Col, Input, Space, Button } from 'antd'
import './index.css'
const { TextArea } = Input

/* 渲染markdown文本，参数如下:
value: string, 原文
isEdit: bool, 是否编辑模式
onUpdate: (v)=>{...v}, 更新文本
*/
const MarkdownDisplayer = (props) => {
  const { value, isEdit, onUpdate } = props
  const [tmpVal, setTmpVal] = useState(value || demoCode)

  if (!isEdit) { // 阅读模式
    return <div className='markdownBox' dangerouslySetInnerHTML={{ __html: parseMd(tmpVal) }} />
  }

  // 编辑+预览模式
  return (<Row>
    <Col span={24} style={{ marginBottom: '10px', overflow: 'auto' }}>
      <Space>
        <Button onClick={() => { onUpdate(tmpVal) }} >保存</Button>
      </Space>
    </Col>
    <Col span={11} >
      <TextArea placeholder={demoCode} autoSize={false} defaultValue={tmpVal} style={{ position: 'absolute', height: '100%', width: '100%' }}
        onChange={(v) => { setTmpVal(v.target.value) }} />
    </Col>
    <Col span={11} push={1}>
      <div className='markdownBox' dangerouslySetInnerHTML={{ __html: parseMd(tmpVal) }} />
    </Col>
    {/* <Col span={11} push={1}>{parseMd(tmpVal)}</Col> */}
  </Row>)
}

export default MarkdownDisplayer

/* eslint-disable no-useless-escape */
/* eslint-disable semi */
// 参考： https://codepen.io/kvendrik/pen/bGKeEE
function parseMd (md) {
  // 预处理代码块
  let preRwoList = md.match(/^\`\`\`([\S\s]+?)\`\`\`/gm) || []
  for (let i = 0; i < preRwoList.length; i++) {
    let before = preRwoList[i]
    let after = `<@PrePlaceholder${i}/>`
    md = md.replace(before, after)
  }

  // ul
  md = md.replace(/^\s*\n\*/gm, '<ul>\n*');
  md = md.replace(/^(\*.+)\s*\n([^\*])/gm, '$1\n</ul>\n\n$2');
  md = md.replace(/^\*(.+)/gm, '<li>$1</li>');

  // ol
  md = md.replace(/^\s*\n\d\./gm, '<ol>\n1.');
  md = md.replace(/^(\d\..+)\s*\n([^\d\.])/gm, '$1\n</ol>\n\n$2');
  md = md.replace(/^\d\.(.+)/gm, '<li>$1</li>');

  // blockquote
  md = md.replace(/^\>(.+)/gm, '<blockquote>$1</blockquote>');

  // h
  md = md.replace(/[\#]{6}(.+)/g, '<h6>$1</h6>');
  md = md.replace(/[\#]{5}(.+)/g, '<h5>$1</h5>');
  md = md.replace(/[\#]{4}(.+)/g, '<h4>$1</h4>');
  md = md.replace(/[\#]{3}(.+)/g, '<h3>$1</h3>');
  md = md.replace(/[\#]{2}(.+)/g, '<h2>$1</h2>');
  md = md.replace(/[\#]{1}(.+)/g, '<h1>$1</h1>');

  // alt h
  md = md.replace(/^(.+)\n\=+/gm, '<h1>$1</h1>');
  md = md.replace(/^(.+)\n\-+/gm, '<h2>$1</h2>');

  // images
  md = md.replace(/\!\[([^\]]+)\]\(([^\)]+)\)/g, '<img src="$2" alt="$1" />');

  // links
  md = md.replace(/[\[]{1}([^\]]+)[\]]{1}[\(]{1}([^\)\"]+)(\"(.+)\")?[\)]{1}/g, '<a href="$2" title="$4">$1</a>');

  // font styles
  md = md.replace(/[\*\_]{2}([^\*\_]+)[\*\_]{2}/g, '<b>$1</b>');
  md = md.replace(/[\*\_]{1}([^\*\_]+)[\*\_]{1}/g, '<i>$1</i>');
  md = md.replace(/[\~]{2}([^\~]+)[\~]{2}/g, '<del>$1</del>');

  // code
  md = md.replace(/[\`]{1}([^\`]+)[\`]{1}/g, '<code>$1</code>');

  // hr
  md = md.replace(/^[-]{5,}\n/gm, '<hr/>')

  // p
  md = md.replace(/^\s*(\n)?(.+)/gm, function (m) {
    return /\<(\/)?(h\d|ul|ol|li|blockquote|pre|img)/.test(m) ? m : '<p>' + m + '</p>';
  });

  // 还原代码块
  for (let i = 0; i < preRwoList.length; i++) {
    let before = `<@PrePlaceholder${i}/>`
    let after = `<pre>${preRwoList[i].replaceAll('<', '&lt;').replaceAll('```', '')}</pre>`
    md = md.replace(before, after)
  }

  return md;
}

const demoCode = `
【MarkDown 格式参考】

# h1
## h2
### h3
#### h4

【分割线】

----------

【无序列表】

* one
* two

【有序列表】

1. one
2. two

【文本样式】
aaaaa *斜体* aaa
aaaa **加粗** aaa
aaaa ~~删除~~ aaaa
aaaa \`代码\` aaaa

[链接](https://jyboss.yy.com)
![图片](http://makefriends.bs2dl.yy.com/%E8%A6%81%E9%A5%AD%E5%A4%B4%E5%83%8F.jpg)

> 引用

\`\`\`
代码块
代码块
代码块
\`\`\`

`
