// eslint-disable-next-line no-unused-vars
import { getBeanDetailList, getEnergyDetailList } from './api'

export default {
  namespace: 'gamePropsQuery',

  state: {
    beanDetailList: [],
    energyDetailList: []
  },

  reducers: {
    updateBeanDetailList (state, { payload }) {
      return {
        ...state,
        beanDetailList: payload
      }
    },
    updateEnergyDetailList (state, { payload }) {
      return {
        ...state,
        energyDetailList: payload
      }
    }
  },

  effects: {
    * getBeanDetailLists ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getBeanDetailList, payload)
      yield put({
        type: 'updateBeanDetailList',
        payload: Array.isArray(list) ? list : []
      })
    },
    * getEnergyDetailLists ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getEnergyDetailList, payload)
      yield put({
        type: 'updateEnergyDetailList',
        payload: Array.isArray(list) ? list : []
      })
    }
  }
}
