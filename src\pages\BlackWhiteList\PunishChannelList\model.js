import { getLists, add, remove } from './api'
import { message } from 'antd'

export default {
  namespace: 'punishChannel',

  state: {
    list: []
  },

  reducers: {
    refreshList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getLists, payload)
      yield put({
        type: 'refreshList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * addItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(add, payload)
      if (status === 0) {
        message.success('添加成功')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('添加失败' + msg)
      }
    },

    * removeItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(remove, payload)
      if (status === 0) {
        message.success('删除成功')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('删除失败' + msg)
      }
    }
  }
}
