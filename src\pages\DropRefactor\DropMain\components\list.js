import React, { Component } from 'react'
import { Card, Table, Divider, Select, Form, message, Row, Col, Typography } from 'antd'
import { connect } from 'dva'
import DropQuery<PERSON>hart from './chart'
import { columns, TipDIv, PoolChangeDesc, countSURT, broadcastOptionsJY, broadcastOptionsVR } from './list_common'
import { timeFormater } from '@/utils/common'
const namespace = 'dropMain'

const defaultChangeDataSource = { rta: 0, rtb: 0, sma: 0, smb: 0, ela: 0, elb: 0 }
const defaultPoolConfig = { arpUid: 0, aprPassport: '', aprTimestamp: 0, aprId: 0, changeInfo: defaultChangeDataSource }
const defaultAprInfo = { timestamp: 0, operator: 0, passport: '', opRemark: '(空)', aprUid: 0, aprRemark: '(空)', aprPassport: '' }

@connect(({ dropMain }) => ({
  model: dropMain
}))

class DropMainComponent extends Component {
  state = {
    pid: this.props.groupType === 'vr' ? 13000 : 17000,
    list: [], // 道具池
    temporary: null,
    defaultPoolConfig: defaultPoolConfig,
    poolChangeDataSource: defaultChangeDataSource
  }

  componentDidMount () {
    const { dispatch } = this.props
    const { pid } = this.state

    // 注册监听函数，在model的poolConfig发生更新时调用，用来更新当前组件的state的list
    dispatch({
      type: `${namespace}/listen`,
      payload: (poolConfig) => {
        let list = []
        if (poolConfig !== undefined && poolConfig.content !== undefined) {
          try {
            list = JSON.parse(poolConfig.content)
            list.forEach((item, index, ary) => { item.limitSetting.poolID = pid })
          } catch (e) {
            message.error('json convert error ', e)
          }
        }
        this.setState({ list, poolConfig })
        this.updateChangeInfo()
      }
    })

    // 刷新页面默认加载第一个道具池
    dispatch({
      type: `${namespace}/getPool`,
      payload: { id: pid }
    })
  }

  onSelectChange = cid => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getPool`,
      payload: { id: cid }
    })

    this.setState({ pid: cid })
  }

  // 刷新实时计算数值
  updateChangeInfo = () => {
    const { list, pid } = this.state
    const result = countSURT(list, pid)
    this.setState({
      poolChangeDataSource: { rta: result.rt, sma: result.sm }
    })
  }

  fixColumn = () => {
    return columns.map(item => {
      if (item.dataIndex === 'broadCastType') {
        item.render = (v, r) => { return (this.props.groupType === 'vr' ? broadcastOptionsVR : broadcastOptionsJY).find(item => item.value === v)?.label }
      }
      return item
    })
  }

  poolOptionsFilter = (before) => {
    // console.debug('before=====>', before)
    const offlineList = [9000, 10000, 11000, 12000]
    return before.filter(item => { return offlineList.indexOf(item.value) === -1 })
  }

  render () {
    const { list, pid, poolConfig, poolChangeDataSource } = this.state
    const { poolNameOptions } = this.props.model
    const { timestamp, operator, aprInfo, changeInfo } = poolConfig || defaultPoolConfig
    const { passport, opRemark, aprUid, aprPassport, aprRemark, timestamp: aprTimestamp } = aprInfo || defaultAprInfo
    const { Text } = Typography

    return (
      <Card>
        <Row gutter={8}>
          <Col span={4}>
            <Form>
              <Form.Item label='道具池选择'>
                <Select defaultValue={pid} onChange={this.onSelectChange} options={this.poolOptionsFilter(poolNameOptions)} />
              </Form.Item>
            </Form>
          </Col>
          <Col>
            <PoolChangeDesc rtb={changeInfo.rtb} smb={changeInfo.smb} ela={changeInfo.ela} elb={changeInfo.elb} rta={poolChangeDataSource.rta} sma={poolChangeDataSource.sma} isEdit={false} />
          </Col>
        </Row>

        <Row>
          <Col>
            <Row>提交：<Text type='secondary'> {`${passport}_(${timeFormater(timestamp)})`} <Divider type='vertical' /> </Text></Row>
            <Row>审批人：<Text type='secondary'> {aprUid === 0 ? '系统自动审批' : aprPassport}_({timeFormater(aprTimestamp)}) <Divider type='vertical' /></Text> </Row>
          </Col>
          <Col>
            <Row>申请理由: <Text type='secondary'> { opRemark || '(空)'} <Divider type='vertical' /></Text></Row>
            <Row>审批备注：<Text type='secondary'> {aprRemark || '(空)'}</Text></Row>
          </Col>
        </Row>
        <Text type='warning' hidden={operator !== 10101} >{`请注意: 该道具池最近触发过周星替换逻辑~ (${timeFormater(timestamp)})`}</Text>
        <Row>
          <TipDIv />
        </Row>
        <Divider />
        <Table key={list} columns={this.fixColumn()} dataSource={list} pagination={false} size='small' rowKey='id' scroll={{ x: 'max-content' }} />
        <Divider />
        <DropQueryChart poolList={poolNameOptions} defaultPID={this.props.groupType === 'vr' ? 13000 : 17000} />
      </Card>
    )
  }
}

export default DropMainComponent
