import React, { Component } from 'react'
import { Popover } from 'antd'

class Pop<PERSON>mage extends Component {
  constructor (props) {
    super(props)

    const { value } = props

    // console.log(value, value instanceof Promise)
    if (value instanceof Promise) {
      this.state = { value: '' }
      value.then(resp => {
        console.log(resp)
        this.setState({ value: resp || '' })
      })
    } else {
      this.state = { value }
    }
  }

  componentWillReceiveProps (props) {
    const { value } = props

    // console.log(value, value instanceof Promise)
    if (value instanceof Promise) {
      this.setState({ value: '' })
      value.then(resp => {
        console.log(resp)
        this.setState({ value: resp || '' })
      })
    } else {
      this.setState({ value })
    }
  }

  render () {
    const { value } = this.state
    const content = (
      <div>
        <img src={value ? value.replace('http://', 'https://') : value} style={{ maxHeight: 800, maxWidth: 800 }} />
      </div>
    )

    return (
      <Popover placement='right' content={content} title={null}>
        <img src={value ? value.replace('http://', 'https://') : value} style={{ maxHeight: 60, maxWidth: 120 }} />
      </Popover>
    )
  }
}

export default PopImage
