import request from '@/utils/request'
import { stringify } from 'qs'

// 获取列表
export function listInfo (params) {
  return request(`/new_compere_info/boss/star_compere/list?${stringify(params)}`)
}

// 添加
export function addInfo (params) {
  return request(`/new_compere_info/boss/star_compere/add?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 删除
export function deleteInfo (params) {
  return request(`/new_compere_info/boss/star_compere/delete?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 检查是否满足绑定
export function checkInfo (params) {
  return request(`/new_compere_info/boss/star_compere/check?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}
