import {
  Card,
  Row,
  Form,
  Table,
  Input,
  Col,
  But<PERSON>, Tooltip
} from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import { yuanToWan } from './common'
import { onExportExcel } from '@/utils/common'

const namespace = 'hatMonthTaskv2'
const getListUri = `${namespace}/listTaskProgress`

@connect(({ taskProgress }) => ({
  model: taskProgress
}))

class TaskProgress extends Component {
  constructor (props) {
    super(props)

    const { dataInfo, progressSummaryInfo } = props
    this.state = { list: dataInfo, info: progressSummaryInfo, searchASID: 0 }
  }

  componentDidMount () {
    const { dispatch } = this.props
    const { searchASID } = this.state
    let data = { asid: searchASID }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  onClick = () => {
    const { dispatch } = this.props
    const { searchASID } = this.state
    let data = { asid: searchASID }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  componentWillReceiveProps (nextProps) {
    const { dataInfo, progressSummaryInfo } = nextProps
    this.setState({ list: dataInfo, info: progressSummaryInfo })
  }

  columns = [
    { title: '任务时间', dataIndex: 'month' },
    { title: '实际生效asid', dataIndex: 'guildAsid' },
    { title: '经营asid', dataIndex: 'asid' },
    { title: '签约asid', dataIndex: 'contractAsid' },
    { title: '厅ssid', dataIndex: 'ssid' },
    { title: '厅名', width: 50, dataIndex: 'tingName', render: (v, r) => (r.tingName.length > 20 ? <Tooltip title={r.tingName}>{ r.tingName.substring(0, 20) + '...'}</Tooltip> : r.tingName) },
    { title: '厅盖章流水/元', dataIndex: 'tingSeal', align: 'center' },
    { title: '公会礼物流水/元', dataIndex: 'guildGift', align: 'center' },
    { title: '达标阶段', dataIndex: 'reachStep' },
    { title: '奖励金额/元', dataIndex: 'reward' }
  ].map(item => {
    item.align = 'center'
    item.ellipsis = true
    return item
  })

  pagination = {
    pageSizeOptions: ['10', '20', '50', '100'],
    showSizeChanger: true,
    defaultPageSize: 20,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  getFilterList = () => {
    const { list, searchASID } = this.state
    let filterList = list
    if (parseInt(searchASID) > 0) {
      filterList = filterList.filter((v) => { return v.asid === parseInt(searchASID) })
    }
    return filterList
  }

  render () {
    const { info } = this.state
    console.log(info)
    return (
      <Card>
        <Row>
          <Col span={20}>
            <Form>
              <Form.Item label='短位ID'>
                <Input style={{ width: '12em', marginRight: 10 }} placeholder='请输入' onChange={(e) => { this.setState({ 'searchASID': e.target.value }) }} />
              </Form.Item>
            </Form>
          </Col>
          <Col span={4}>
            <Button type='primary' style={{ float: 'right' }}
              onClick={() => onExportExcel(this.columns, this.getFilterList(), '任务完成进度表.xlsx')}>
              导出
            </Button>
          </Col>
        </Row>
        <p style={{ marginTop: 10, color: 'red' }}> {info.year}年{info.month}月任务 </p>
        <p style={{ marginTop: 10, color: 'black' }}>概述：当前参与公会{info.guildCount}家，达标{info.reachGuildCount}家, 达标厅{info.reachTingCount}个。合计盖章流水{yuanToWan(info.totalSealAmount)}万元，发放道具{info.reachReward}元 </p>
        <Table style={{ marginTop: 10 }} dataSource={this.getFilterList()} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} scroll={{ x: 'max-content' }} size='small' />
      </Card>
    )
  }
}

export default TaskProgress
