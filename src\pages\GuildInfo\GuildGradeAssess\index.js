import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { connect } from 'dva'
import { Card } from 'antd'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import GuildGradeAssess from './gradeAssess'
import DebugPage from './debugPage'
const namespace = 'guildGradeAssess'

@connect(({ guildGradeAssess }) => ({
  model: guildGradeAssess
}))

class MainPage extends Component {
  componentDidMount = () => {}

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='grade' type='card' size='small'>
            <TabPane tab='超级水晶公会信息' key='grade'>
              <GuildGradeAssess />
            </TabPane>
            <TabPane tab='测试工具' key='debug'>
              <DebugPage />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default MainPage
