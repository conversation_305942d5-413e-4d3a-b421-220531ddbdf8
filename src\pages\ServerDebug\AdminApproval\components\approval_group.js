import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Space, Input, Row, Col, Typography, Divider, Button, message } from 'antd'
import { parseNumberList } from '@/utils/common'
const { Text } = Typography
const namespace = 'adminApproval'

@connect(({ adminApproval }) => ({
  model: adminApproval
}))

class AprGroup extends Component {
  state = {}
  componentDidMount = () => {
    this.queryList()
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  queryList = () => {
    this.callModel('getApprovalGroup')
  }

  batchUpdateAprGroup = (ridList, first, second) => {
    const params = {
      ridList: ridList,
      first: parseNumberList(first) || [],
      second: parseNumberList(second) || []
    }
    this.callModel('updateAprGroup', {
      params: params,
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('更新失败: ' + msg)
          return
        }
        message.success('更新成功~')
        this.queryList()
      }
    })
  }

  render () {
    const { aprGroupList } = this.props.model || {}
    return (
      <Card>
        <Space direction='vertical' size='large'>
          {
            aprGroupList.map(item => {
              return <AprInfoCard data={item} onSubmitUpdate={this.batchUpdateAprGroup} />
            })
          }
        </Space>
      </Card>
    )
  }
}

export default AprGroup

class AprInfoCard extends Component {
  state = {
    first: '',
    second: ''
  }

  componentDidMount = () => {
    const { first, second } = this.props.data
    this.setState({
      first: first ? first.join('\n') : '',
      second: second ? second.join('\n') : '' })
  }

  render () {
    const { key, ruleList, ridList } = this.props.data
    const { onSubmitUpdate } = this.props
    const { first, second } = this.state

    return <Card size='small' title={key} style={{ maxWidth: '80em' }}>
      <Row>
        <Text type='success'>审批规则: </Text>
        {
          ruleList.map(item => {
            return <Text> <Divider type='vertical' />{item.rid}-{item.name}</Text>
          })
        }
      </Row>

      <Row>
        <Col>
          <Text type='success'>一级审批人</Text>
          <Input.TextArea style={{ width: '15em' }} autoSize={{ minRows: 3 }} value={first} onChange={e => this.setState({ first: e.target.value })} />
        </Col>
        <Col push={1}>
          <Text type='success'>二级审批人</Text>
          <Input.TextArea style={{ width: '15em' }} autoSize={{ minRows: 3 }} value={second} onChange={e => this.setState({ second: e.target.value })} />
        </Col>
      </Row>

      <Row>
        <Button style={{ marginTop: '1em' }} type='primary' onClick={() => onSubmitUpdate(ridList, first, second)} >批量更新</Button>
      </Row>

    </Card>
  }
}
