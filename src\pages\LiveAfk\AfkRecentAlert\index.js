import React, { Component } from 'react'
import { connect } from 'dva'
import { But<PERSON>, Col, DatePicker, Divider, Input, message, Modal, Row, Select, Table } from 'antd'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { timeFormater } from '@/utils/common'
import exportExcel from '@/utils/exportExcel'
import moment from 'moment'

const namespace = 'AfkRecentAlert'

const afkTypes = [
  { idx: 0, label: '全部' },
  { idx: 1, label: '连续x分钟上座嘉宾=y' },
  { idx: 2, label: '连续x分钟多媒体视频播放' },
  { idx: 3, label: '连续x分钟同时没有视频流和音频流' },
  { idx: 4, label: '连续x分钟人气值=[a, b]' },
  { idx: 5, label: '连续x分钟鼠标和键盘均无在模板内操作' }
]

@connect(({ AfkRecentAlert }) => ({
  model: AfkRecentAlert
}))

class AfkRecentAlert extends Component {
  constructor (props) {
    super(props)
    this.state = {
      pageSize: 20,
      begDate: moment(timeFormater(parseInt(new Date().getTime() / 1000) - 60 * 60, 0), 'YYYY-MM-DD HH:mm'),
      endDate: moment(timeFormater(parseInt(new Date().getTime() / 1000), 0), 'YYYY-MM-DD HH:mm')
    }
    this.refreshAfkRecentAlertData()
  }

  refreshAfkRecentAlertData = () => {
    this.searchHandle()
  }

  defaultPageValue = () => {
    return {
      pageSize: this.props.model.pageSize,
      total: this.props.model.total,

      onChange: (page, pageSize) => { this.pageChange(page, pageSize) },
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    }
  }

  resetQueryInputs = () => {
    this.setState({
      uid: '',
      asid: '',
      ssid: '',
      afkType: '',
      posType: '',
      begDate: moment(timeFormater(parseInt(new Date().getTime() / 1000) - 60 * 60, 0), 'YYYY-MM-DD HH:mm'),
      endDate: moment(timeFormater(parseInt(new Date().getTime() / 1000), 0), 'YYYY-MM-DD HH:mm')
    })
    const { dispatch } = this.props
    const { currentPage, pageSize } = this.state
    dispatch({
      type: `${namespace}/getAfkRecentAlert`,
      payload: { page: currentPage, size: pageSize }
    })
  }

  pageChange = (page, pageSize) => {
    this.setState({ currentPage: page, pageSize: pageSize })
    this.searchHandle(page, pageSize)
  }

  // 将字符串文本转成数字类型的查询字符串
  toNumberArrayQueryParamString = (text) => {
    if (!text || text.length < 1) {
      return ''
    }
    let arr = text.split(/[^\d]/g)
    return arr.join(',')
  }

  // 获取当前查询条件
  getQueryCondition = (page, size) => {
    const { begDate, endDate, currentPage, pageSize, uid, yyno, asid, ssid, afkType, posType } = this.state
    if (page === undefined) {
      page = currentPage
    }
    if (size === undefined) {
      size = pageSize
    }
    let begTime = !begDate ? undefined : parseInt(begDate._d.getTime() / 1000)
    let endTime = !endDate ? undefined : parseInt(endDate._d.getTime() / 1000)

    if (!endTime) {
      endTime = parseInt(new Date().getTime() / 1000)
    }
    if (!begTime) {
      begTime = parseInt(new Date().getTime() / 1000) - 3600
    }

    let maxTimeRange = 10 * 24 * 60 * 60
    if (endTime - begTime > maxTimeRange) {
      Modal.error({ content: '所选时间跨度太大，不允许超过 10 天' })
      return false
    }
    return { page: page,
      size: size,
      begDate: begTime,
      endDate: endTime,
      uid: this.toNumberArrayQueryParamString(uid),
      yyno: this.toNumberArrayQueryParamString(yyno),
      asid: this.toNumberArrayQueryParamString(asid),
      ssid: this.toNumberArrayQueryParamString(ssid),
      afkType: afkType,
      posType: posType
    }
  }

  // 处理查询事件
  searchHandle = (page, size) => {
    const { dispatch } = this.props

    let query = this.getQueryCondition(page, size)
    if (!query) {
      return
    }
    dispatch({
      type: `${namespace}/getAfkRecentAlert`,
      payload: query
    })
  }

  // 点击导出按钮
  onExportClick = () => {
    const { dispatch } = this.props

    let page = 1
    let size = 500
    let total = -1

    let query = this.getQueryCondition(page, size)
    if (!query) {
      return
    }

    let loadingCloser = message.loading('正在下载数据, 请稍候......', 0)
    console.log('loading:  ', loadingCloser)

    let exportHandler = this.exportExcel
    let list = []
    let callback = (subList, totalCnt, p, s) => {
      // console.log('第 ', p, ' 页数据，共计 ', s, ' 条，总条数:', totalCnt)
      if (total === -1) {
        total = totalCnt
      }
      if (subList && subList.length > 0) {
        subList.forEach(item => list.push(item))
      }
      if (total === -1 || list.length < total) {
        let nQuery = Object.assign(query, { page: query.page + 1, callback: callback })
        // console.log('查询第 ', query.page + 1, ' 页数据，pageSize:', query.size, query)
        // 继续
        dispatch({
          type: `${namespace}/getAfkRecentAlert`,
          payload: nQuery
        })
      } else {
        console.log('总数:', list.length, '每次导出:', size, '共计', query.page, '页')
        let filename = '低质量开播取消推荐记录'
        // 执行导出逻辑
        if (query.begDate) {
          filename += '_' + timeFormater(query.begDate, 1)
        }
        if (query.endDate) {
          filename += '_' + timeFormater(query.endDate, 1)
        }
        // 关闭提示框
        loadingCloser()
        exportHandler(list, filename + '.xlsx')
      }
    }

    query.callback = callback
    dispatch({
      type: `${namespace}/getAfkRecentAlert`,
      payload: query
    })
  }

  // 导出给定列表数据
  exportExcel = (list, filename) => {
    if (!list || list.length < 1) {
      Modal.error({ content: '当前条件没有任何数据' })
      return
    }
    let headers = []
    this.columns.forEach(function (item) {
      headers.push({
        key: item.dataIndex,
        header: item.title,
        render: (v) => {
          if (item.renderExcel) {
            let r = item.renderExcel(v)
            console.log(v, r, item)
            return r
          }
          return v
        } })
    })

    let exportList = []
    list.forEach(item => {
      let exportItem = Object.assign({}, item)
      headers.forEach(header => {
        let v = exportItem[header.key]
        if (header.render) {
          exportItem[header.key] = header.render(v)
        }
      })
      exportList.push(exportItem)
    })
    exportExcel(headers, exportList, filename || '低质量开播取消推荐记录.xlsx')
  }

  // 开始时间禁用
  disabledBegDate = (current) => {
    let now = new Date().getTime()
    if (now <= current._d.getTime()) {
      return true
    }
    const { endDate } = this.state
    // 填写了 endDate
    if (endDate && endDate._d.getTime() <= current._d.getTime()) {
      return true
    }
    return false
  }

  // 开始时间禁用
  disabledEndDate = (current) => {
    let now = new Date().getTime()
    if (now <= current._d.getTime()) {
      return true
    }
    const { begDate } = this.state
    // 填写了 begDate
    if (begDate && begDate._d.getTime() >= current._d.getTime()) {
      return true
    }
    return false
  }

  columns = [
    { title: '取消推荐时间', dataIndex: 'Timestamp', render: (ctime) => timeFormater(ctime, 1), renderExcel: (ctime) => timeFormater(ctime, 1) },
    { title: 'UID', dataIndex: 'UID' },
    { title: '主持YY号', dataIndex: 'yyno' },
    { title: '昵称', dataIndex: 'nick' },
    { title: '短位ID', dataIndex: 'asid' },
    { title: 'SSID', dataIndex: 'SSID' },
    { title: '挂机类型',
      dataIndex: 'afkTypeDesc',
      render: (desc) => {
        if (!desc) {
          return ''
        }
        let arr = desc.split('、')
        let br = <br />
        let result = null
        arr.forEach(function (item) {
          result = <span>{result}{br}{item}</span>
        })
        return (<div>{result}</div>)
      },
      renderExcel: (desc) => {
        return desc
      }
    },
    { title: '飞机票',
      dataIndex: 'flyTick',
      render: (flyTick) => (
        <span>
          <a href={flyTick}>{ flyTick }</a>
        </span>
      ),
      renderExcel: (v) => { return v }
    }
  ]

  render () {
    const { route } = this.props
    const { displayData } = this.props.model
    let pagination = this.defaultPageValue()

    return (
      <>
        <PageHeaderWrapper title={route.name}>
          <Row style={{ marginBottom: '1em' }}>
            <Col offset={0}>
              <span style={{ marginLeft: 15 }}>取消推荐时间</span>
              <DatePicker
                showTime
                format='YYYY-MM-DD HH:mm:ss'
                placeholder='开始时间'
                defaultValue={moment(timeFormater(parseInt(new Date().getTime() / 1000) - 60 * 60, 0), 'YYYY-MM-DD HH:mm')}
                value={this.state.begDate}
                disabledDate={this.disabledBegDate}
                onChange={(v) => { this.setState({ begDate: v }) }}
                style={{ marginLeft: 5 }}
              />
              <span style={{ marginLeft: 5 }}>~</span>
              <DatePicker
                showTime
                format='YYYY-MM-DD HH:mm:ss'
                placeholder='结束时间'
                defaultValue={moment(timeFormater(parseInt(new Date().getTime() / 1000), 0), 'YYYY-MM-DD HH:mm')}
                value={this.state.endDate}
                disabledDate={this.disabledEndDate}
                onChange={(v) => this.setState({ endDate: v })}
                style={{ marginLeft: 5 }}
              />

              <span style={{ marginLeft: 15 }}>挂播类型</span>
              <Select style={{ width: 300, marginLeft: 5 }} placeholder='全部' value={this.state.afkType} onChange={(v) => this.setState({ afkType: v })} allowClear>
                {afkTypes.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>

              <span style={{ marginLeft: 15 }}>UID</span>
              <Input placeholder='请输入,可多个' value={this.state.uid} onChange={e => this.setState({ uid: e.target.value })} style={{ width: 240, marginLeft: 3 }} allowClear />

              <span style={{ marginLeft: 15 }}>YY号</span>
              <Input placeholder='请输入,可多个' value={this.state.yyno} onChange={e => this.setState({ yyno: e.target.value })} style={{ width: 240, marginLeft: 3 }} allowClear />

            </Col>
          </Row>
          <Row style={{ marginBottom: '1em' }}>
            <Col offset={0}>
              <span style={{ marginLeft: 15 }}>短位ID</span>
              <Input placeholder='请输入,可多个' value={this.state.asid} onChange={e => this.setState({ asid: e.target.value })} style={{ width: 240, marginLeft: 3 }} allowClear />

              <span style={{ marginLeft: 15 }}>SSID</span>
              <Input placeholder='请输入,可多个' value={this.state.ssid} onChange={e => this.setState({ ssid: e.target.value })} style={{ width: 240, marginLeft: 3 }} allowClear />

              <Divider type='vertical' /> {/* 分割线 */}
              <Button style={{ marginLeft: 20 }} type='primary' onClick={e => this.searchHandle()}>查询</Button>
              <Button style={{ marginLeft: 5 }} type='primary' onClick={e => this.resetQueryInputs()}>Reset</Button>
              <Divider type='vertical' /> {/* 分割线 */}
              <Button style={{ marginLeft: 5 }} type='primary' onClick={e => this.onExportClick()}>导出</Button>
            </Col>
          </Row>

          <Row style={{ marginBottom: '1em' }}>
            <Col span={24}>
              <Table columns={this.columns}
                dataSource={displayData}
                size='small'
                pagination={pagination}
                showSorterTooltip={false}
                rowKey={record => record.ID}
              />
            </Col>
          </Row>
        </PageHeaderWrapper>
      </>
    )
  }
}

export default AfkRecentAlert
