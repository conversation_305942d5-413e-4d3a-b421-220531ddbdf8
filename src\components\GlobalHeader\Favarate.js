import React, { PureComponent } from 'react'
import styles from './index.module.less'
import { StarOutlined, StarTwoTone } from '@ant-design/icons'
import { subscribeMenu, unSubscribeMene } from '@/services/api'
import { message } from 'antd'

export default class GlobalFavarate extends PureComponent {
  onFavarate = () => {
    const { location } = this.props
    let uri = location.pathname.split('/', -1).filter(item => !/\d/.test(item)).join('/')

    subscribeMenu({ uri }).then(result => {
      const { data: { status, msg } } = result
      if (status === 0) {
        this.props.dispatch({
          type: 'global/fetchSubscribeMenus'
        })
      } else {
        message.error(msg)
      }
    })
  }

  onCancelFav = () => {
    const { location } = this.props
    let uri = location.pathname.split('/', -1).filter(item => !/\d/.test(item)).join('/')

    unSubscribeMene({ uri }).then(result => {
      const { data: { status, msg } } = result
      if (status === 0) {
        this.props.dispatch({
          type: 'global/fetchSubscribeMenus'
        })
      } else {
        message.error(msg)
      }
    })
  }

  render () {
    const {
      subscribeMenus,
      theme,
      location
    } = this.props

    let className = styles.right
    if (theme === 'dark') {
      className = `${styles.right}  ${styles.dark}`
    }

    return (
      <div className={className}>
        {!subscribeMenus[location.pathname.split('/', -1).filter(item => !/\d/.test(item)).join('/')] ? (
          <span onClick={this.onFavarate} className={`${styles.action} ${styles.account}`}><StarOutlined style={{ marginRight: 5 }} />收藏</span>
        ) : (
          <span onClick={this.onCancelFav} className={`${styles.action} ${styles.account}`}><StarTwoTone style={{ marginRight: 5 }} twoToneColor='#cc9211' />取消收藏</span>
        )}
      </div>
    )
  }
}
