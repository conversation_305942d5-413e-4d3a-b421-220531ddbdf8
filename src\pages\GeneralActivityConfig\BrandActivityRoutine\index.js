import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs, Card } from 'antd'
import { connect } from 'dva'

import BrandActivityRoutine from './components/routineTaskConfig.js'

const TabPane = Tabs.TabPane

@connect(({ brandActRoutine }) => ({
  model: brandActRoutine
}))

class HelpGroup extends Component {
  state = {
    activeKey: '1'
  }

  onTabClick = key => {
    this.setState({ activeKey: key })
  }

  render () {
    const { route } = this.props
    const { activeKey } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Card style={{ marginTop: 20 }}>
          <Tabs type='card' defaultActiveKey='1' onTabClick={this.onTabClick}>
            <TabPane tab='常规任务配置' key='1'>
              { activeKey === '1' ? <BrandActivityRoutine /> : ''}
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default HelpGroup
