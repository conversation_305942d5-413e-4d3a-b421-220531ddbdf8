import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { timeFormater } from '@/utils/common'
import { Card, Row, Button, Tooltip, message } from 'antd'

const namespace = 'cowBossDetail'

@connect(({ cowBossDetail }) => ({
  model: cowBossDetail
}))

class cowBossDetail extends Component {
  state = {
    title: 'SUCCESS'
  }
  componentDidMount = () => {
    this.callModel('queryList', null)
  }
  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  createListHtml = (title, content, tip = '') => {
    return (
      <dl style={{ margin: '0 2em 0 0' }}>
        <Tooltip title={tip}>
          <dt style={{ display: 'inline-block', marginRight: '2em' }}>
            <p style={{ fontSize: '1.1em', color: '#1890ff', fontWeight: 'bold', margin: '0' }}>
              {title + ' :'}
            </p>
          </dt>
          <dd style={{ display: 'inline-block' }}>
            <p style={{ fontSize: '1.3em', margin: '0' }}>
              {content}
            </p>
          </dd>
        </Tooltip>
      </dl>
    )
  }

  render () {
    const { route } = this.props
    const d = this.props.model.displayData
    if (d === undefined) {
      message.error('发生错误,请检查控制台')
      console.error('unexpect displayData: ', d)
      return (<>error</>)
    }
    const actTime = d.startTime === '' ? '' : timeFormater(d.startTime, 1) + ' ~ ' + timeFormater(d.endTime, 1)
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Row style={{ marginBottom: '1em' }}>
            <Button type='primary' onClick={() => { this.callModel('queryList', null) }}>刷新</Button>
          </Row>
          <div>
            {this.createListHtml('活动ID', d.actID)}
            {this.createListHtml('数据更新时间', d.timestamp === '' ? '' : timeFormater(d.timestamp, 4))}
            {this.createListHtml('活动时间', actTime)}
            {this.createListHtml('今日系统合计发放魔戒数量', d.todayRewardCount)}
            {this.createListHtml('今日系统合计发放魔戒模拟价值', d.todayRewardValue)}
            {this.createListHtml('活动期间总发放魔戒模拟价值', d.totalRewardValue)}
            {this.createListHtml('活动期间总发放魔戒数量', d.totalRewardCount)}
          </div>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default cowBossDetail
