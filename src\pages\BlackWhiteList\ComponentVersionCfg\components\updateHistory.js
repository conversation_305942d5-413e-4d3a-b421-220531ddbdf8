import React, { Component } from 'react'
import { connect } from 'dva'
import { Row, Col, Table, Button, Modal, Descriptions, Empty } from 'antd'
import { timeFormater } from '@/utils/common'
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter'
import { coy } from 'react-syntax-highlighter/dist/esm/styles/prism'
const namespace = 'componentVersionCfg'

@connect(({ componentVersionCfg }) => ({
  model: componentVersionCfg
}))

class UpdateHistory extends Component {
  state = {
    modalVisable: false,
    selectItem: null
  }
  componentDidMount = () => { }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 查询数据更新历史
  getOpHistory = () => {
    this.callModel('getOpHistory')
  }

  render () {
    const { modalVisable, selectItem } = this.state
    const { ophistory } = this.props.model
    const columns = [
      { title: 'id', dataIndex: 'id', align: 'center' },
      { title: '操作描述', dataIndex: 'tag', align: 'center' },
      { title: '执行时间', dataIndex: 'timestamp', align: 'center', render: (v) => { return timeFormater(v) } },
      { title: '操作人UID', dataIndex: 'opUid', align: 'center' },
      { title: '操作结果', dataIndex: 'resErr', align: 'center', render: (v) => { return v === '<nil>' ? '成功' : `结果异常: err=[${v}]` } },
      { title: '操作',
        align: 'center',
        render: (v, r) => {
          return <a onClick={() => { this.setState({ modalVisable: true, selectItem: r }) }}>查看详情</a>
        } }
    ]
    return (
      <div>
        <Row>
          <Col span={24} style={{ marginBottom: '1em' }}>
            <Button type='primary' onClick={() => { this.getOpHistory() }}>刷新</Button>
          </Col>
          <Col span={24}>
            <Table columns={columns} dataSource={ophistory} size='small' scroll={{ x: 'max-content' }} pagination={{ pageSize: 25 }} />
          </Col>
        </Row>
        <Modal visible={modalVisable} title='操作详情' cancelText='关闭' width='60em'
          onCancel={() => { this.setState({ modalVisable: false }) }} okButtonProps={{ hidden: true }}>
          {
            selectItem === null
              ? <Empty />
              : <Descriptions span={24} bordered size='small'>
                <Descriptions.Item span={24} label='操作描述' style={{ width: '10em', whiteSpace: 'pre-line' }}>{selectItem.tag}</Descriptions.Item>
                <Descriptions.Item span={24} label='更新前'>
                  <SyntaxHighlighter language='json' style={coy}>
                    {JSON.stringify(JSON.parse(selectItem.before), null, 2)}
                  </SyntaxHighlighter>
                </Descriptions.Item>
                <Descriptions.Item span={24} label='参数'>
                  <SyntaxHighlighter language='json' style={coy}>
                    {JSON.stringify(JSON.parse(selectItem.params), null, 2)}
                  </SyntaxHighlighter>
                </Descriptions.Item>
                <Descriptions.Item span={24} label='更新后'>
                  <SyntaxHighlighter language='json' style={coy}>
                    {JSON.stringify(JSON.parse(selectItem.after), null, 2)}
                  </SyntaxHighlighter>
                </Descriptions.Item>
              </Descriptions>
          }
        </Modal>
      </div>
    )
  }
}

export default UpdateHistory
