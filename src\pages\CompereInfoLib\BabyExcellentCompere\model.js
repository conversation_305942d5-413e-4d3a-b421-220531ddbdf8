import * as api from './api'
import { message } from 'antd'

export default {
  namespace: 'babyExcellentCompere',

  state: {
    list: []
  },

  reducers: {
    displayList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * listInfo ({ payload }, { call, put }) {
      let { data: { data } } = yield call(api.listInfo, payload)
      data = Array.isArray(data) ? data : []
      for (let i = 0; i < data.length; i++) {
        data[i].idx = i + 1
      }
      console.log(data)
      yield put({
        type: 'displayList',
        payload: data
      })
    },

    * addInfo ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.addInfo, payload)
      if (status !== 0) {
        message.error(msg)
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listInfo',
        payload: {}
      })
    },

    * deleteInfo ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.deleteInfo, payload)
      if (status !== 0) {
        message.error(msg)
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listInfo',
        payload: {}
      })
    },

    * updateInfo ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.updateInfo, payload)
      if (status !== 0) {
        message.error(msg)
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listInfo',
        payload: {}
      })
    }
  }
}
