import React, { Component } from 'react'
// import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import { connect } from 'dva'
import RoomMgrLibrary from './components/compere_library'

// const namespace = 'compereLibrary' // model 的 namespace
const TabPane = Tabs.TabPane

@connect(({ roomMgrLibrary }) => ({ // model 的 namespace
  model: roomMgrLibrary // model 的 namespace
}))
class Index extends Component { // 默认页面组件，不需要修改
  /** *******************************页面布局*************************************************************/
  render () {
    const { model: { roomMgrList } } = this.props

    return (
      <Tabs>
        <TabPane>
          <RoomMgrLibrary configList={roomMgrList} />
        </TabPane>
      </Tabs>
    )
  }
}

export default Index // 保证唯一
