import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Modal, Row, Col, Table, Button, Form, InputNumber, Space, Select } from 'antd'
import message from 'antd/lib/message'
import { propTypeOptions } from '../../dropCommon'

const namespace = 'dropMain'

@connect(({ dropMain }) => ({
  model: dropMain
}))

class NewUserReward extends Component {
  state = {
    modalVisble: false,
    opIsAdd: true,
    selectItem: {}
  }
  componentDidMount = () => {
    this.callModel('getNewUserRewardConfig')
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 获取配置
  refreshList = () => {
    this.callModel('getNewUserRewardConfig')
  }
  // 提交更新
  submitUpdate = (f) => {
    console.debug('f===>', f)
    this.callModel('updateNewUserOdds', {
      params: f,
      cbFunc: (ok) => {
        if (ok) {
          message.success('更新完成')
          this.refreshList()
        } else {
          message.warning('更新失败，请稍后再试')
        }
        this.setState({ modalVisble: false })
      }
    })
  }

  render () {
    const { modalVisble } = this.state
    const { newUserRewardConfig } = this.props.model
    const column = [
      { title: '稀有度', dataIndex: 'propsType', render: (v) => { return propTypeOptions.find(item => item.value === v)?.label } },
      { title: '概率', dataIndex: 'rate' },
      { title: '价格区间1', dataIndex: 'prizeL' },
      { title: '价格区间2', dataIndex: 'prizeR' },
      { title: '操作',
        width: '10em',
        render: (v, r, i) => {
          return (
            <Space>
              <Button onClick={() => { this.setState({ modalVisble: true }); this.formRef.setFieldsValue(r) }}>编辑</Button>
            </Space>
          )
        } }
    ]
    return (
      <Card>
        <Row>
          <Col span={24}>
            <Table columns={column} dataSource={newUserRewardConfig} />
          </Col>
        </Row>
        <Modal forceRender visible={modalVisble} title='更新' okText='确认更新' cancelText='取消'
          onOk={() => { this.formRef.submit() }}
          onCancel={() => this.setState({ modalVisble: false })}>
          <Form labelCol={{ span: 6 }} ref={form => { this.formRef = form }} onFinish={(f) => this.submitUpdate(f)}>
            <Form.Item label='稀有度' name='propsType' rules={[{ required: true }]}>
              <Select options={propTypeOptions} disabled />
            </Form.Item>
            <Form.Item label='概率' name='rate' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>
            <Form.Item label='价格区间1' name='prizeL' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>
            <Form.Item label='价格区间2' name='prizeR' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default NewUserReward
