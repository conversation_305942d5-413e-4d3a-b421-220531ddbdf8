import request from '@/utils/request'
import { stringify } from 'qs'

// 获取申请水晶公会列表
export function listCrystal (params) {
  return request(`/fts_hgame/new_guild_settle_in/list_crystal?${stringify(params)}`)
}

// 获取申请超级水晶公会列表
export function listSuperCrystal (params) {
  return request(`/fts_hgame/new_guild_settle_in/list_super_crystal?${stringify(params)}`)
}

// 获取绿色通道白名单列表
export function listGreenChannelWhite (params) {
  return request(`/fts_hgame/new_guild_settle_in/list_green_channel_white?${stringify(params)}`)
}

// 获取申请水晶公会历史记录列表
export function listCrystalHistory (params) {
  return request(`/fts_hgame/new_guild_settle_in/list_crystal_history?${stringify(params)}`)
}

// 获取申请超级水晶公会历史记录列表
export function listSuperCrystalHistory (params) {
  return request(`/fts_hgame/new_guild_settle_in/list_super_crystal_history?${stringify(params)}`)
}

// 水晶公会详情
export function detailCrysta (params) {
  return request(`/fts_hgame/new_guild_settle_in/detail_crysta?${stringify(params)}`)
}

// 超级水晶公会详情
export function detailSuperCrysta (params) {
  return request(`/fts_hgame/new_guild_settle_in/detail_super_crysta?${stringify(params)}`)
}

// 水晶公会审批
export function approvalCrysta (params) {
  return request(`/fts_hgame/new_guild_settle_in/approval_crysta?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 超级水晶公会审批
export function approvalSuperCrysta (params) {
  return request(`/fts_hgame/new_guild_settle_in/approval_super_crysta?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 超级水晶公会上传合同附件
export function uploadSuperSrysta (params) {
  return request(`/fts_hgame/new_guild_settle_in/upload_super_crysta?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 添加绿色通道白名单
export function addGreenChannelWhite (params) {
  return request(`/fts_hgame/new_guild_settle_in/add_green_channel_white?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 白名单公会当前身份
export function getGuildRole (params) {
  return request(`/fts_hgame/new_guild_settle_in/get_guild_role?${stringify(params)}`)
}
