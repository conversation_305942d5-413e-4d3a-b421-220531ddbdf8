import React, { Component } from 'react'
import { Card, Divider, But<PERSON>, Table, Select } from 'antd'
import { connect } from 'dva'
import { CSVLink } from 'react-csv'

const namespace = 'robMoney' // model 的 namespace
const { Option } = Select

@connect(({ rob<PERSON><PERSON> }) => ({ // model 的 namespace
  model: robMoney // model 的 namespace
}))
class PkRewardDetailComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false
    }
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getPkList`
    })
  }

  onSelChange = val => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getPkList`,
      payload: { period: val }
    })
  }

  // 需要修改
  columns = [
    { title: '主持类型', dataIndex: 'group', align: 'center', render: text => text === 'super' ? '超主' : '天团' },
    { title: '主持', dataIndex: 'uid', align: 'center' },
    { title: '带入金', dataIndex: 'money', align: 'center' },
    { title: '对战主持', dataIndex: 'pkUid', align: 'center' },
    { title: '对战主持带入金', dataIndex: 'pkMoney', align: 'center' },
    { title: 'PK值', dataIndex: 'value', align: 'center' },
    { title: '对战主持PK值', dataIndex: 'pkValue', align: 'center' },
    { title: '彩蛋奖励', dataIndex: 'bonus', align: 'center' },
    { title: '胜方获得赏金', dataIndex: 'final', align: 'center' }
  ]

  export = () => {
    const { model: { pkList } } = this.props
    return pkList
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { pkList } } = this.props
    return (
      <Card>
        <CSVLink data={this.export()} target='_blank'><Button type='primary' style={{ marginRight: 20 }}>导出</Button></CSVLink>
        <Select onChange={this.onSelChange} defaultValue={1}>
          <Option value={1}>01</Option>
          <Option value={2}>02</Option>
          <Option value={3}>03</Option>
          <Option value={4}>04</Option>
          <Option value={5}>05</Option>
          <Option value={6}>06</Option>
          <Option value={7}>07</Option>
          <Option value={8}>08</Option>
          <Option value={9}>09</Option>
          <Option value={10}>10</Option>
          <Option value={11}>11</Option>
        </Select>
        <Divider />
        <Table rowKey={(record, index) => index} dataSource={pkList} columns={this.columns} size='small' pagination={false} /> {/* 显示的列表 */}
      </Card>
    )
  }
}

export default PkRewardDetailComponent
