import { <PERSON><PERSON>, <PERSON>, DatePicker, Divider, Form, Table, Tooltip, Select } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import { onExportExcel } from '@/utils/common'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { genColumnTooltip } from '@/components/SimpleComponents'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
const gameMap = { 0: 'ALL', 5: '诺亚方舟' }
const chanMap = { all: 'ALL', pc: 'PC端', dreamer: 'YO交友', yomi: 'YO语音' }

@connect(({ arkJy }) => ({ // model 的 namespace
  model: arkJy // model 的 namespace
}))

// 诺亚方舟日报-关键信息
class ArkCrucialInfoComponent extends Component {
  state = {
    isLoading: false,
    value: {},
    visible: false,
    list: [],
    dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')],
    filterPlatform: 'all',
    filterArenaId: 5
  }

  componentDidMount () {
    this.loadData()
  }

 // 表头显示提示语
 genColumnTooltip = (title) => {
   return {
     filterDropdown: (<span />),
     filterIcon: (
       <Tooltip placement='top' title={title}>
         <QuestionCircleOutlined style={{ fontSize: '16px' }} />
       </Tooltip>
     )
   }
 }

 // render tooltip
 createTooltip = (v, width = 10) => {
   return !v ? '-' : <Tooltip title={v}>
     <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', width: `${width}em` }}>{v}</div>
   </Tooltip>
 }

 avgFormater = (a, b) => {
   if (b === 0) {
     return 0
   }
   return Number(a / b).toFixed(0)
 }

 dataFilter = (before) => {
   let after = []
   const { filterArenaId, filterPlatform } = this.state
   before.forEach(item => {
     if (filterArenaId && item?.arenaId !== filterArenaId) {
       return
     }
     if (filterPlatform && !item?.platform.includes(filterPlatform)) {
       return
     }
     after.push(item)
   })
   return after
 }

  // 日期 渠道类型  魔豆模拟流水 付费用户数 剩余发放监控汇总  成功总流水 成功总人数 累计成功人数  累计失败人数
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '玩法类型', dataIndex: 'arenaId', align: 'center', render: (text, record, index) => { return gameMap[text] } },
    { title: '渠道类型', dataIndex: 'platform', align: 'center', render: (text, record, index) => { return chanMap[text] } },
    { title: '模拟收入', dataIndex: 'betAmethyst', align: 'center', ...genColumnTooltip('魔豆模拟流水') },
    { title: '付费用户数', dataIndex: 'betUser', align: 'center' },
    { title: '人均支出', dataIndex: '_6', align: 'center', render: (_, r) => { return this.avgFormater(r.betAmethyst, r.betUser) } },
    { title: '发放监控汇总', dataIndex: 'jackpotLeft', align: 'center' },
    { title: '总支出', dataIndex: '_1', align: 'center', render: (v, r) => { return r.betRewardAmethyst + r.fragCount }, ...genColumnTooltip('礼物支出+装扮碎片流水') },  
    { title: '礼物支出', dataIndex: 'betRewardAmethyst', align: 'center', ...genColumnTooltip('只含抽出奖品，不含装扮碎片流水') },  
    { title: '装扮碎片流水', dataIndex: 'fragCount', align: 'center' },
    { title: '发放占比', dataIndex: '_2', align: 'center', render: (_, r) => { return this.betRewardRatioFormater(r, true) }, ...genColumnTooltip('总支出/模拟收入') },
    { title: '粗发放占比', dataIndex: '_3', align: 'center', render: (_, r) => { return this.betRewardRatioFormater(r, false) }, ...genColumnTooltip('礼物支出/模拟收入') },
    { title: '粗偏移', dataIndex: '_4', align: 'center', render: (_, r) => { return r.betAmethyst - r.betRewardAmethyst }, ...genColumnTooltip('模拟收入-礼物支出') },
    { title: '总偏移', dataIndex: '_5', align: 'center', render: (_, r) => { return r.betAmethyst - (r.betRewardAmethyst + r.fragCount) }, ...genColumnTooltip('模拟收入-总支出-装扮碎片流水') },
    { title: '成功总人数', dataIndex: 'betRewardUser', align: 'center' },
    { title: '累计成功人数', dataIndex: 'profitUser', align: 'center' },
    { title: '累计失败人数', dataIndex: 'lossUser', align: 'center' }
  ]

  betRewardRatioFormater = (r, isIncludeFrag) => {
    let betRewardRatio = 0
    const { betRewardAmethyst, betAmethyst, fragCount } = r
    let reward = isIncludeFrag ? betRewardAmethyst + fragCount : betRewardAmethyst
    if (reward > 0 && betAmethyst > 0) {
      betRewardRatio = Number(reward / betAmethyst * 100).toFixed(2) + '%' 
    }
    return betRewardRatio
  }

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    const { modelName } = this.props
    this.setState({ isLoading: true })
    dispatch({
      type: `${modelName}/getCrucialInfoList`,
      payload: data,
      cbFunc: (ret) => { this.setState({ isLoading: false }) }
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  handleSelectChange = (value) => {
    console.log(value)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { crucialList } } = this.props
    const { dateRange, isLoading, filterArenaId, filterPlatform } = this.state
    return (
      <Card>
        <Form>
          <span style={{ marginLeft: 10 }}>时间范围:</span>
          <RangePicker style={{ marginRight: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
          <span style={{ marginRight: 10 }}>玩法类型:</span>
          <Select value={filterArenaId} options={[{ label: 'ALL', value: 0 }, { label: '诺亚方舟', value: 5 }]} style={{ minWidth: '6em', marginRight: 10 }} onChange={v => { this.setState({ filterArenaId: v }) }} />
          <span style={{ marginRight: 10 }}>渠道类型:</span>
          <Select value={filterPlatform} options={[{ label: 'ALL', value: 'all' }, { label: 'PC端', value: 'pc' }, { label: 'YO交友', value: 'dreamer' }, { label: 'YO语音', value: 'yomi' }]} style={{ minWidth: '6em', marginRight: 10 }} onChange={v => { this.setState({ filterPlatform: v }) }} />
          <Button style={{ marginRight: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Button style={{ marginRight: 10 }} onClick={() => onExportExcel(this.columns, this.dataFilter(crucialList), '诺亚方舟日报-关键信息.xlsx')}>导出</Button>
          <Divider />
          <Table loading={isLoading} dataSource={this.dataFilter(crucialList)} columns={this.columns} rowKey={(record, index) => index} size='small' />
        </Form>
      </Card>
    )
  }
}

export default ArkCrucialInfoComponent
