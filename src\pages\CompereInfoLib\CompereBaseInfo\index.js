import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import { connect } from 'dva'

import CompereLibrary from './components/compere_library'

const TabPane = Tabs.TabPane

@connect(({ compereBaseInfo }) => ({
  model: compereBaseInfo
}))

class CompereBaseInfo extends Component {
  state = { activeKey: '1' }
  onTabClick = key => {
    this.setState({ activeKey: key })
  }

  render () {
    const { route, model: { total } } = this.props
    const { activeKey } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs type='card' defaultActiveKey='1' onTabClick={this.onTabClick}>
          <TabPane tab={'交友' + '(' + total + ')'} key='1'>
            { activeKey === '1' ? <CompereLibrary /> : ''}
          </TabPane>
        </Tabs>
      </PageHeaderWrapper>
    )
  }
}

export default CompereBaseInfo
