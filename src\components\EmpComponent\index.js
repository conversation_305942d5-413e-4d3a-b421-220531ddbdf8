import React, { Component, createRef } from 'react'
import loadModule from './loadModule'
import { withRouter } from 'dva/router'

const isTest = window.location.hostname !== 'jyboss-test.yy.com/'

const YoAdminSchemaForm = 'https://unpkg.yy.com/@hd/yo-admin-schema-form/dist/emp.js'
// const YoAdminSchemaForm = 'http://localhost:8000/emp.js'

const AdminUnitUi = [
  'https://hd-admin-in-test.yy.com/admin-unite-ui/emp.js',
  'https://hd-admin-in.yy.com/admin-unite-ui/emp.js'
][isTest ? 0 : 1]
// const AdminUnitUi = 'https://localhost:5001/emp.js'

const empUrl = {
  YoAdminSchemaForm,
  AdminUnitUi
}

class EmpComponent extends Component {
  state = { loading: false, error: false }
  constructor (props) {
    super(props)

    this.elRef = createRef()
    this.componentRef = createRef()

    console.log('ReactVersion', 'jb-boss', React.version, props)
  }

  getRenderProps = () => {
    const { push, replace, goBack } = this.props.history
    const { params, path } = this.props.match
    const urlSearch = new URLSearchParams(this.props.location.search)
    const search = {}
    urlSearch.forEach((value, key) => {
      search[key] = value
    })

    const router = {
      path,
      push,
      replace,
      goBack,
      search,
      params
    }
    return Object.assign({}, this.props.params, { router })
  }

  renderRoot = () => {
    if (this.componentRef.current) {
      this.componentRef.current.render(this.getRenderProps())
    }
  }

  componentDidUpdate (prevProps, prevState, snapshot) {
    this.renderRoot()
  }

  componentDidMount () {
    const { scopeName, module } = this.props

    if (module) {
      const url = empUrl[scopeName]

      loadModule(url, scopeName, module)
        .then(factory => {
          this.setState({
            loading: false
          }, () => {
            this.componentRef.current = factory.default(this.elRef.current, this.getRenderProps())
          })
        })
        .catch(e => {
          this.setState({
            loading: false,
            error: true
          })
        })
    }
  }
  componentWillUnmount () {
    if (this.componentRef.current) {
      this.componentRef.current.unmount()
    }
  }

  render () {
    const { loading, error } = this.state
    if (loading) {
      return <div>loading</div>
    }
    if (error) {
      return <div>error</div>
    }
    return <div ref={this.elRef} />
  }
}

export default withRouter(EmpComponent)
