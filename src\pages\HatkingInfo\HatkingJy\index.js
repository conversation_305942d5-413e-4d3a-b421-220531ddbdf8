import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import { connect } from 'dva'
import HatKingCrucialInfoNewComponent from '../components/hatking_crucial_info_new'
import HatKingComboInfoComponent from '../components/hatking_combo_info'
import HatKingModouInfoComponent from '../components/hatking_modou_info'
import HatKingDailyModeStatsNewComponent from '../components/hatking_daily_mode_stats_new'
import HatKingDailyBetRangeStatsComponent from '../components/hatking_daily_bet_range_stats'
import HatKingDailyBetPeriodStatsComponent from '../components/hatking_daily_bet_period_stats'
import HatKingDailyRoundStatsComponent from '../components/hatking_daily_round_stats'
import HatKingUpgradeCrucialInfoComponent from '../components/hatking_upgrade_crucial_info'

const namespace = 'hatkingJy' // model 的 namespace
const TabPane = Tabs.TabPane

@connect(({ hatkingJy }) => ({ // model 的 namespace
  model: hatkingJy // model 的 namespace
}))
class Index extends Component { // 默认页面组件，不需要修改
  /** *****************************非活动流程配置文件更新与获取****************************************************************/
  updateBakConfig = value => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/upsetBakConfigItem`,
      payload: value
    })
  }

  tabOnChange = type => activityKey => {
    console.log(type, activityKey)
    if (type !== undefined || type != null) {
      activityKey = type
    }
  }

  /** *******************************页面布局*************************************************************/
  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs onChange={this.tabOnChange()} type='card'>
          <TabPane tab='关键信息' key='1'>
            <HatKingCrucialInfoNewComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='连胜信息' key='2'>
            <HatKingComboInfoComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='魔豆日报' key='3'>
            <HatKingModouInfoComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='升级场发放道具监控' key='4'>
            <HatKingUpgradeCrucialInfoComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='总道具数额汇总发放道具监控' key='5'>
            <HatKingDailyModeStatsNewComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='参与用户区间分布' key='6'>
            <HatKingDailyBetRangeStatsComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='分时段监控' key='7'>
            <HatKingDailyBetPeriodStatsComponent modelName={namespace} model={this.props.model} />
          </TabPane>
          <TabPane tab='结算明细监控' key='8'>
            <HatKingDailyRoundStatsComponent modelName={namespace} model={this.props.model} />
          </TabPane>
        </Tabs>

      </PageHeaderWrapper>
    )
  }
}

export default Index // 保证唯一
