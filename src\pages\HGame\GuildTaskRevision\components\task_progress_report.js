import {
  Card,
  Form,
  Table,
  // Typography,
  // message,
  Divider, Button, Input
} from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import exportExcel from '@/utils/exportExcel'
import moment from 'moment'
import { buildTingNumExport, buildTingNumSettlement } from './common'

const namespace = 'guildTaskRevision'
const getListUri = `${namespace}/getTaskProgress`

@connect(({ taskProgressReport }) => ({
  model: taskProgressReport
}))

class TaskProgressReport extends Component {
  constructor (props) {
    super(props)

    const { dataInfo } = props
    // console.log('TaskProgressReport dataInfo', dataInfo)
    this.state = { list: dataInfo, searchASID: 0 }
  }

  componentDidMount () {
    this.tick()
    this.timerID = setInterval(
      () => this.tick(),
      600000
    )
  }

  onClick = () => {
    this.tick()
  }

  // 定时刷新数据
  tick () {
    const { dispatch } = this.props
    const { searchASID } = this.state
    let data = { asid: searchASID }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  componentWillReceiveProps (nextProps) {
    const { dataInfo } = nextProps
    this.setState({ list: dataInfo })
  }

  state = {
    visible: false,
    isUpdate: false,
    isNotifyValue: true,
    searchInput: '',
    searchKeyword: '',
    channel: { key: -1 },
    gameType: { key: -1 }
  }

  tableInitValue = {}

  columns = [
    { title: '任务时间', width: 50, dataIndex: 'month' },
    { title: '日期', dataIndex: 'date' },
    { title: '短位ID', width: 50, dataIndex: 'asid' },
    { title: '公会礼物流水/元', width: 50, dataIndex: 'amount' },
    { title: '流水任务预估奖励/元', dataIndex: 'turnoverReward' },
    { title: '优质厅个数/个', dataIndex: 'highGradeTing' },
    { title: '各流水规模优质厅数; 预估奖励',
      dataIndex: 'tingIntervalList',
      render: (text, record) => {
        return buildTingNumSettlement(record.tingIntervalList !== null && record.tingIntervalList !== undefined ? record.tingIntervalList : [])
      }
    },
    { title: '优质厅任务预估奖励/元', width: 50, dataIndex: 'highGradeReward' },
    { title: '合计预估奖励/元', dataIndex: 'totalReward' }
  ].map(item => {
    item.align = 'center'
    item.ellipsis = true
    return item
  })

  pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  getFilterList = () => {
    const { list } = this.state
    const { searchASID } = this.state
    let filterList = list
    if (parseInt(searchASID) > 0) {
      filterList = filterList.filter((v) => { return v.asid === parseInt(searchASID) })
    }
    return filterList
  }

  showModal = (isUpdate, record) => () => {
    if (record == null) {
      record = this.tableInitValue
    }
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(record)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate })
  }

  onExport = () => {
    let list = this.getFilterList()

    let exportData = list.map(item => {
      let tingIntervalList = item.tingIntervalList
      if (tingIntervalList) {
        delete item.tingIntervalList
        item['tingIntervalList'] = buildTingNumExport(tingIntervalList)
      }

      let v = $.extend(true, {}, item)
      return v
    })
    let exportHeader = []
    this.columns.forEach((col) => {
      if (col.export === undefined || col.export) {
        exportHeader.push({ key: col.dataIndex, header: col.title })
      }
    })

    let fileName = '公会任务进度报表-' + moment().format('YYYYMM') + '.xlsx'
    exportExcel(exportHeader, exportData, fileName)
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  render () {
    return (
      <Card>
        <Form>
          <Divider type='vertical' />
          短位ID：
          <Input style={{ width: 100, marginRight: 10 }} placeholder='请输入' onChange={(e) => { this.setState({ 'searchASID': e.target.value }) }} />
          {/* <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button> */}
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onExport}>导出</Button>
          <Divider type='vertical' />
          <Table style={{ marginTop: 10 }} dataSource={this.getFilterList()} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' scroll={{ x: 'max-content' }} />
        </Form>
      </Card>
    )
  }
}

export default TaskProgressReport
