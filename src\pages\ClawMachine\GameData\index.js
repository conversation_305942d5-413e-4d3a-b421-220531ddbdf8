import React, { Component } from 'react'
// import dateString from '@/utils/dateString'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Input, Modal, DatePicker, Select } from 'antd'
import { connect } from 'dva'
import { onExportExcel } from '@/utils/common'
import { genColumnTooltip } from '@/components/SimpleComponents'

var moment = require('moment')
const namespace = 'clawMachineData'
const getListUri = `${namespace}/getList`
const addItemUri = `${namespace}/addItem`
// const updateItemUri = `${namespace}/updateItem`
const removeItemUri = `${namespace}/removeItem`
const FormItem = Form.Item
const Option = Select.Option

const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
const businessMap = {
  '': '默认(所有)',
  'all': '汇总',
  'yypc': 'PC交友',
  'dreamer': 'YO交友',
  'yomi': 'YO语音'
}

@connect(({ clawMachineData }) => ({
  model: clawMachineData
}))

// 抓娃娃-数据日报
class ClawMachineData extends Component {
  avgFormater = (a, b) => {
    if (b === 0) {
      return 0
    }
    return Number(a / b).toFixed(0)
  }
  // 列表结构
  columns = [
    { title: '日期', dataIndex: 'dateYMD', align: 'center' },
    { title: '使用道具类型',
      dataIndex: 'buyType',
      align: 'center',
      render: (text, record) => ['汇总', '紫水晶', '布料', '能量'][text] },
    { title: '渠道',
      dataIndex: 'businessType',
      align: 'center',
      render: (text, record) => { return businessMap[text] } },
    { title: '参与人数(去重)', dataIndex: 'clawPeople', align: 'center' },
    { title: '抓取次数',
      dataIndex: 'clawCount',
      align: 'center',
      render: (v) => { return v },
      ...genColumnTooltip('用户抓娃娃的总次数，用户点击一次”抓10次“键时，这里计入10个单位。') },
    { title: '模拟收入',
      dataIndex: 'aboutIncome',
      align: 'center',
      render: (v) => { return v },
      ...genColumnTooltip('* 用户使用紫水晶时，模拟收入的单位为紫水晶。\n' +
        '* 用户使用布料/能量时，按1布料/能量=900紫水晶，折算进模拟收入。') },
    { title: '人均支出', dataIndex: '_1', align: 'center', render: (_, r) => { return this.avgFormater(r.aboutIncome, r.clawPeople) }, ...genColumnTooltip('模拟收入 / 参与人数') },
    { title: '次均支出', dataIndex: '_2', align: 'center', render: (_, r) => { return this.avgFormater(r.aboutIncome, r.clawCount) }, ...genColumnTooltip('模拟收入 / 抽取道具次数') },
    { title: '总支出',
      dataIndex: 'totalPay',
      align: 'center',
      ...genColumnTooltip('礼物支出+装扮碎片流水/紫水晶') },
    { title: '礼物支出',
      dataIndex: 'giftPay',
      align: 'center',
      ...genColumnTooltip('送出娃娃的总价值/紫水晶') },
    { title: '装扮碎片流水',
      dataIndex: 'fragCount',
      align: 'center',
      render: (v) => { return v } },
    { title: '总偏移',
      dataIndex: 'totalOffset',
      align: 'center',
      ...genColumnTooltip('模拟收入-总支出/紫水晶') },
    { title: '粗偏移',
      dataIndex: 'aboutOffset',
      align: 'center',
      ...genColumnTooltip('模拟收入-礼物支出/紫水晶') },
    { title: '发放占比',
      dataIndex: 'totalRate',
      align: 'center',
      ...genColumnTooltip('总支出/模拟收入') },
    { title: '粗发放占比',
      dataIndex: 'aboutRate',
      align: 'center',
      ...genColumnTooltip('礼物支出/模拟收入') }
  ]

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '100', '500'],
    showSizeChanger: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = {
    visible: false,
    isUpdate: false,
    value: {},
    startValue: null,
    endValue: null,
    poolType: -1,
    businessType: null,
    dateRange: [moment().subtract(7, 'days'), moment().subtract(0, 'days')]
  }

  // 获取列表
  componentDidMount () {
    const { dispatch } = this.props
    const { dateRange, poolType, businessType } = this.state
    var data = {
      startDate: moment(dateRange[0]).format('YYYYMMDD'),
      endDate: moment(dateRange[1]).format('YYYYMMDD'),
      buyType: poolType,
      businessType: businessType }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // 添加 与 编辑
  onFinish = values => {
    const { dispatch } = this.props
    values.sinceTimestamp = values.sinceTimestamp.unix()
    var url = addItemUri
    console.log('submit:', values)
    dispatch({
      type: url,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  handlePoolTypeChange = (value) => {
    console.log('handlePoolTypeChange', value)
    this.state.poolType = value.key
  }

  handleChannelTypeChange = (value) => {
    console.log('handleChannelTypeChange', value)
    this.state.businessType = value.key
  }

  // onChange = (field, value) => {
  //   this.setState({
  //     [field]: value
  //   })
  // }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onClick = () => {
    const { dispatch } = this.props
    const { dateRange, poolType, businessType } = this.state
    // if (startValue <= 0 || endValue <= 0 || startValue > endValue) {
    //   message.error('时间范围错误，请选取正确的查询时间满园')
    //   return
    // }
    var data = {
      startDate: moment(dateRange[0]).format('YYYYMMDD'),
      endDate: moment(dateRange[1]).format('YYYYMMDD'),
      buyType: poolType,
      businessType: businessType
    }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  // 删除
  handleDel = key => () => {
    const { dispatch } = this.props
    const data = { uid: key }
    dispatch({
      type: removeItemUri,
      payload: data
    })
  }

  // 显示弹窗
  showModal = (isUpdate, record) => () => {
    var now = moment().unix()
    if (record == null) record = { actStart: now, actStop: now, oweYear: now }

    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.sinceTimestamp = moment.unix(v.actStart)
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, value: record, isUpdate: isUpdate, title: isUpdate ? '更新活动信息' : '添加活动信息' })
  }

  // 关闭弹窗
  hidModal = () => {
    this.setState({ visible: false })
  }

  saveFormRef = formRef => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list } } = this.props
    const { dateRange, visible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <span style={{ marginLeft: 10 }}>时间范围:</span>
            <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
            <span style={{ marginLeft: 10 }}>抽取道具类型:</span>
            <Select labelInValue defaultValue={{ key: '-1' }} style={{ width: 120, marginLeft: 10 }} onChange={this.handlePoolTypeChange}>
              <Option value='-1'>默认(所有)</Option>
              <Option value='0'>汇总</Option>
              <Option value='1'>紫水晶</Option>
              <Option value='2'>布料</Option>
              <Option value='3'>能量</Option>
            </Select>
            <span style={{ marginLeft: 10 }}>渠道:</span>
            <Select labelInValue defaultValue={{ key: '' }} style={{ width: 120, marginLeft: 10 }} onChange={this.handleChannelTypeChange}>
              <Option value=''>默认(所有)</Option>
              <Option value='all'>汇总</Option>
              <Option value='yypc'>PC交友</Option>
              <Option value='dreamer'>YO交友</Option>
              <Option value='yomi'>YO语音</Option>
            </Select>
            <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
            <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={() => { onExportExcel(this.columns, list, '抓娃娃-交友-数据日报.xlsx') }}>导出</Button>
            <Divider />
            <Table dataSource={list} scroll={{ x: 'max-content' }} columns={this.columns} rowKey={(record, index) => index} pagination={this.defaultPageValue} size='small' />
          </Form>
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hidModal} onOk={this.handleSubmit}>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem label='主持uid' name='uids' rules={[{ required: true, message: '格式不正确' }]}>
              <Input rows={12} placeholder='一行一个uid' />
            </FormItem>
            <FormItem label='开始时间' name='sinceTimestamp' rules={[{ required: true, message: '开始时间不能为空' }]}>
              <DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' style={{ width: '100%' }} />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default ClawMachineData
