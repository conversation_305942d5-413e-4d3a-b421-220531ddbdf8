import { getLists } from './api'
import { message } from 'antd'
// import { message } from 'antd'

export default {
  namespace: 'clawMachineData',

  state: {
    list: [],
    titleList: []
  },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {

    * getList ({ payload }, { call, put }) {
      try {
        const { data: { list } } = yield call(getLists, payload)
        yield put({
          type: 'updateList',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        message.error('exception', e)
      }
    }
  }
}
