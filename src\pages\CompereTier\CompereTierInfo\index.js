import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Button, Divider, Form, Card, Modal, Input, Select } from 'antd'
import { connect } from 'dva'
import { CSVLink } from 'react-csv'
const namespace = 'CompereTier'
const FormItem = Form.Item
const Search = Input.Search
const Option = Select.Option
var moment = require('moment')

@connect(({ CompereTier }) => ({
  model: CompereTier
}))

class Index extends Component {
  // column structs.
  columns = [
    { title: '排名', dataIndex: 'ranking', key: 'ranking', align: 'center' },
    { title: 'uid', dataIndex: 'uid', key: 'uid', align: 'center' },
    { title: '昵称', dataIndex: 'nick', key: 'nick', align: 'center' },
    { title: '段位',
      dataIndex: 'tier',
      key: 'tier',
      align: 'center',
      render: (text, record) => {
        switch (text) {
          case 1: return '倔强青铜'
          case 2: return '秩序白银'
          case 3: return '荣耀黄金'
          case 4: return '尊贵铂金'
          case 5: return '永恒钻石'
          case 6: return '至尊星耀'
          case 7: return '最强王者'
          case 8: return '荣耀王者'
        }
        return text
      }
    },
    { title: '段位星', dataIndex: 'stars', key: 'stars', align: 'center' },
    { title: '积分', dataIndex: 'power', key: 'power', align: 'center' },
    { title: '总的场次', dataIndex: 'season_num', key: 'season_num', align: 'center' },
    { title: '胜场', dataIndex: 'season_win_num', key: 'season_win_num', align: 'center' },
    { title: '目前连胜', dataIndex: 'streak', key: 'streak', align: 'center' },
    { title: '最后一次更新时间',
      dataIndex: 'last_update',
      key: 'last_update',
      align: 'center',
      render: text => {
        if (text > 0) {
          return moment.unix(text).format('YYYY-MM-DD HH:mm:ss')
        }
        return text
      }
    },
    { title: '总积分', dataIndex: 'total_power', key: 'total_power', align: 'center' },
    { title: '总流水', dataIndex: 'amount', key: 'amount', align: 'center' },
    { title: '同段PK场次', dataIndex: 'same_tier', key: 'same_tier', align: 'center' },
    { title: '夸段PK场次', dataIndex: 'diff_tier', key: 'diff_tier', align: 'center' },
    { title: '同段PK额外加成积分', dataIndex: 'same_tier_addition', key: 'same_tier_addition', align: 'center' },
    { title: '夸段PK额外加成积分', dataIndex: 'diff_tier_addition', key: 'diff_tier_addition', align: 'center' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <Button style={{ marginRight: 10 }} size='small' type='primary' onClick={this.showModal(record)}>修改</Button>
        </span>)
    }
  ]

  defaultPageValue = {
    defaultPageSize: 50,
    pageSizeOptions: ['50', '100', '500', '2000'],
    showSizeChanger: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = {
    visible: false,
    isUpdate: false,
    confirmVisible: false,
    deleteConfirmMsg: '',
    season: 0,
    value: {}
  }

  // show modal
  showModal = (record) => () => {
    if (record == null) record = { uid: '' }
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(record)
    }
    this.setState({ value: record, visible: true, title: '修改段位' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // add and update
  onFinish = values => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/addItem`,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  handleDelOne = record => e => {
    const { dispatch } = this.props
    const data = { id: record.id }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = { type: this.state.type }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  searchByCompereUid = (value) => {
    const { dispatch } = this.props
    const data = { uid: this.state.uid, season: this.state.season }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  handleSeasonChange = (value) => {
    const { dispatch } = this.props
    const data = { season: value.key, uid: this.state.uid }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })

    this.setState({ season: value.key })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  // content
  render () {
    const { route, model: { list } } = this.props
    const { visible, isUpdate, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    let headers = [
      { label: '排名', key: 'ranking' },
      { label: 'uid', key: 'uid' },
      { label: '段位', key: 'tier' },
      { label: '段位星', key: 'stars' },
      { label: '积分', key: 'power' },
      { label: '总的场次', key: 'season_num' },
      { label: '胜场', key: 'season_win_num' },
      { label: '目前连胜', key: 'streak' },
      { label: '最后一次更新时间', key: 'last_update' },
      { label: '总积分', key: 'total_power' },
      { label: '总流水', key: 'amount' },
      { label: '同段PK场次', key: 'same_tier' },
      { label: '夸段PK场次', key: 'diff_tier' },
      { label: '同段PK额外加成积分', key: 'same_tier_addition' },
      { label: '夸段PK额外加成积分', key: 'diff_tier_addition' }
    ]
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Search onSearch={value => this.searchByCompereUid(value)} placeholder='搜索主持uid' onChange={e => this.setState({ uid: e.target.value })} style={{ width: 200 }} /> {/* 搜索按钮 */}
            <Divider type='vertical' /> {/* 分割线 */}
            赛季
            <Divider type='vertical' /> {/* 分割线 */}
            <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 120 }} onChange={this.handleSeasonChange}>
              <Option key={0} >{'当前赛季'}</Option>
              <Option key={1} >{'上赛季'}</Option>
            </Select>
            <Divider type='vertical' /> {/* 分割线 */}
            <CSVLink data={list} filename={'output.csv'} headers={headers}>导出</CSVLink>
            <Table rowKey={(record, index) => index} dataSource={list} columns={this.columns} pagination={this.defaultPageValue} />
          </Form>
        </Card>

        <Modal visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit} forceRender>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <FormItem label='赛季' name='season' rules={[{ required: true, message: '赛季不能为空' }]}>
              <Select>
                <Option value={0}>当前赛季</Option>
                <Option value={1}>上赛季</Option>
              </Select>
            </FormItem>
            <FormItem label='uid' name='uid' rules={[{ required: true }]}>
              <Input readOnly={isUpdate} />
            </FormItem>
            <FormItem label='段位' name='tier' rules={[{ required: true, message: '段位不能为空' }]}>
              <Select>
                <Option value={1}>倔强青铜</Option>
                <Option value={2}>秩序白银</Option>
                <Option value={3}>荣耀黄金</Option>
                <Option value={4}>尊贵铂金</Option>
                <Option value={5}>永恒钻石</Option>
                <Option value={6}>至尊星耀</Option>
                <Option value={7}>最强王者</Option>
              </Select>
            </FormItem>
            <FormItem label='段位星' name='stars' rules={[{ required: true, message: 'stars can not be null' }]}>
              <Input readOnly={isUpdate} />
            </FormItem>
            <FormItem label='积分' name='power' rules={[{ required: true, message: 'power can not be null' }]}>
              <Input readOnly={isUpdate} />
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default Index
