import React, { Component } from 'react'
import { Card, Table, DatePicker, Form, Row, Col, Button } from 'antd'
import { connect } from 'dva'
import moment from 'moment'
import { SearchOutlined } from '@ant-design/icons'

const namespace = 'SynthesisReport' // model 的 namespace
const dateFormat = 'YYYYMMDD'
const { RangePicker } = DatePicker

@connect(({ SynthesisReport }) => ({ // model 的 namespace
  model: SynthesisReport // model 的 namespace
}))
class RFIReportComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      dateRange: [moment().subtract(7, 'days'), moment().subtract(0, 'days')],
      visible: false
    }
  }

  componentDidMount () {
    const { dispatch } = this.props

    const { dateRange } = this.state
    let data = { begin: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }

    dispatch({
      type: `${namespace}/getDailyReportList`,
      payload: data
    })
  }

  onFinish = values => {
    // console.log(values)
    const { dispatch } = this.props

    const { dateRange } = this.state
    let data = { begin: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }

    dispatch({
      type: `${namespace}/getDailyReportList`,
      payload: data
    })
  }

  // 需要修改
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '合成用户数', dataIndex: 'synthesisPeople', align: 'center' },
    { title: '合成次数', dataIndex: 'synthesisCount', align: 'center' },
    { title: '合成礼物总价值(紫水晶)', dataIndex: 'synthesisAmount', align: 'center' },
    { title: '消耗材料总价值(紫水晶)', dataIndex: 'consumePropsAmount', align: 'center' },
    { title: '消耗道具总数量', dataIndex: 'consumeGiftCount', align: 'center' }
  ]

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { dailyReportList } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form onFinish={this.onFinish}>
          <Row gutter={12}>
            <Col>
              <Form.Item name='dateRange'>
                <RangePicker defaultValue={dateRange} format={'YYYY-MM-DD'} onChange={(date, format) => this.setState({ dateRange: date })} />
              </Form.Item>
            </Col>
            <Col>
              <Form.Item>
                <Button type='primary' htmlType='submit'><SearchOutlined />Search</Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Table rowKey='index' dataSource={dailyReportList} columns={this.columns} size='small' pagination={{ pageSize: 50 }} /> {/* 显示的列表 */}
      </Card>
    )
  }
}

export default RFIReportComponent
