import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Row, Col, Table, message } from 'antd'
import SearchParams from '@/components/SimpleComponents/searchParams'
import { columns2, limitModeOptions } from '../../DropMain/components/list_common'

const namespace = 'dropReport'

// 发放数量监控-TAB
@connect(({ dropReport }) => ({
  model: dropReport
}))

class NumberMonitor extends Component {
  state = {
    searchParams: {}
  }

  componentDidMount = () => { }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // ======================

  // 刷新数据列表
  refreshList = (id) => {
    this.setState({ poolId: id })
    if (id === 'bundle_20000') {
      this.refreshBundlePool(20000)
      return
    }
    this.refreshNormalPool(id)
  }

  // 获取道具池数据
  refreshNormalPool = (id) => {
    this.callModel('getPoolList2', {
      params: { id },
      isDetailMode: true,
      cbFunc: (ret) => {
        let content = []
        if (ret !== undefined && ret.list !== undefined) {
          try {
            content = JSON.parse(ret.list.content)
          } catch (e) {
            message.error('json convert error ', e)
          }
        }
        this.setState({ list: content })
      }
    })
  }

  // 获取组合大礼道具池数据
  refreshBundlePool = (id) => {
    const cbFunc = (ret) => {
      const { status, msg, list } = ret
      if (status !== 0) {
        message.warn('获取数据失败: ' + msg)
        return
      }
      const fixCfg = this.parseBundleToList(list)
      this.setState({ list: fixCfg })
    }
    this.callModel('getBundleConfigProd', {
      params: { id, isProd: true },
      isJsonMode: true,
      isDetailMode: true,
      cbFunc
    })
  }

  // 当道具池为物资大战时使用特定的表头
  fixColumn = (pid) => {
    if (pid === 'bundle_20000') {
      return this.bundleColumn
    }
    return columns2
  }

  // 组合大礼配置转换
  parseBundleToList = (cfg) => {
    let { bundleA, bundleB, bundleC } = cfg
    if (bundleA === undefined) {
      return []
    }
    bundleA.key = 'bundleA'
    bundleB.key = 'bundleB'
    bundleC.key = 'bundleC'
    let list = [ bundleA, bundleB, bundleC ]
    return list
  }

  bundleColumn = [
    { title: '组合名称', dataIndex: 'name' },
    { title: '组合抽数', dataIndex: 'count' },
    { title: '投放上限/D', dataIndex: 'dailyLimit', render: (v) => { return v > 0 ? v : v === 0 ? '不发放' : '无上限' } },
    { title: '发放道具模式', dataIndex: 'limitSetting', render: (v) => { return v.isOpen ? limitModeOptions.find(item => item.value === v.mode)?.label : '基础模式' } },
    { title: '大道具动态新增上限', dataIndex: 'dynamicA' },
    { title: '道具池动态新增上限', dataIndex: 'dynamicB' },
    { title: '今日已发放数量', dataIndex: 'dynamicC' },
    { title: '今日剩余发放数量', dataIndex: 'dynamicD', render: (v, r) => { return r.dailyLimit < 0 ? '无限' : v } }
  ].map(item => {
    item.align = 'center'
    return item
  })

  render () {
    const { poolNameOptions } = this.props.model
    const { groupType } = this.props
    const bundleOptions = { label: '交友物资大战组合', value: 'bundle_20000' }
    let fixOptions = []
    if (poolNameOptions) {
      fixOptions = [...poolNameOptions]
      if (groupType !== 'vr') {
        fixOptions.push(bundleOptions)
      }
      fixOptions = fixOptions.filter(item => item.value !== 21000)
    }
    // console.debug('fixOptions==>', fixOptions)
    const searchConfig = [{ label: '道具池选择', fieldName: 'poolId', inputType: 'Selecter', extProps: { style: { minWidth: '10em' }, options: fixOptions }, defaultValue: groupType === 'vr' ? 9000 : 17000 }]
    const { list, poolId } = this.state
    // /drop/admin/get_bundle_reward_config?=&id=20000&isProd=true
    return (
      <Card>
        <Row>
          <Col span={24}>
            <SearchParams columns={searchConfig} onChange={(v) => { this.setState({ paramsVal: v }); this.refreshList(v.poolId) }} />
          </Col>
          <Col span={24}>
            <Table columns={this.fixColumn(poolId)} dataSource={list} pagination={false} size='small' rowKey='id' scroll={{ x: 'max-content' }} />
          </Col>
        </Row>
      </Card>
    )
  }
}

export default NumberMonitor
