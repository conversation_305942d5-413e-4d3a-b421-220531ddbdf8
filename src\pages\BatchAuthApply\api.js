import request from '@/utils/request'
// import { stringify } from 'qs'

export function getUnauthMenuAndItem () {
  return request('/admin_menu/query_un_auth_menu') // 未授权页面 fts_boss/handler/admin.go-QueryUnAuthMenu
}

export function authApply (data) {
  return request('/admin_menu/auth_apply_v2', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(data)
  })
}
