import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Row, Col, Table, Form, Space, Select, DatePicker, Divider, Button, Tooltip, InputNumber, message } from 'antd'
// import { CSVLink } from 'react-csv'
import moment from 'moment'
import { QuestionCircleOutlined } from '@ant-design/icons'

const { RangePicker } = DatePicker
const namespace = 'dropReport'
const defaultPageSize = 30
const queryModeOptions = [
  { label: '按天', value: 'daily' },
  { label: '按周', value: 'week' },
  { label: '按月', value: 'month' },
  { label: '历史累计', value: 'total' },
  { label: '自定义累计时间', value: 'range' }
]

const sortMode = [
  { label: '总偏移降序', value: '-totalOffset' },
  { label: '总偏移升序', value: 'totalOffset' },
  { label: '总收益降序', value: '-totalOut' },
  { label: '总收益升序', value: 'totalOut' },
  { label: '总消费降序', value: '-totalIn' },
  { label: '总消费升序', value: 'totalIn' }
]

// 盈亏监控-Tab
@connect(({ dropReport }) => ({
  model: dropReport
}))

class ProfitMonitor extends Component {
  defaultFormValue = {
    businessName: this.props.groupType === 'jybw' ? 'jy' : 'zw',
    uid: 0,
    queryMode: 'daily',
    selectTime: moment().subtract(6, 'days'),
    selectTimeRange: [moment(), moment()],
    sortMode: '-totalOffset'
  }

  state = {
    loading: false,
    selectTimeMode: 'daily',
    searchParams: this.defaultFormValue,
    page: 0,
    total: 0,
    profitTip: '',
    pageSize: defaultPageSize
  }

  componentDidMount = () => {
    const { page, pageSize } = this.state
    this.queryList(this.defaultFormValue, page, pageSize)
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 按条件查询
  onFinish = (v) => {
    console.debug('businessNameOptions===>', v)
    const { pageSize } = this.state
    this.setState({ page: 0 })
    this.queryList(v, 0, pageSize)
  }

  // 查询数据
  queryList = (value, page, pageSize) => {
    const params = this.fixparams(value, page, pageSize)
    this.setState({ loading: true, profitTip: value.businessName === 'jybox' ? '普通物资奖励+礼盒大道具奖励' : '道具池奖励+进度奖励+龙宫获得+补给箱获得' })
    this.callModel('getProfitData', {
      params: params,
      cbFunc: (ret) => {
        let { list: { total, list } } = ret
        if (!list) {
          message.warn('数据为空')
          list = []
        }
        this.setState({ loading: false, searchParams: value, total, pageSize: list.length > pageSize ? list.length : pageSize })
      }
    })
  }
  // 导出数据
  exportList = () => {
    const { searchParams } = this.state
    const params = this.fixparams(searchParams, 0, 0)
    this.callModel('exportProfitData', {
      params: params,
      isDownloadMode: true
    })
  }

  // 整合查询参数
  fixparams = (value, page, pageSize) => {
    let params = {
      queryMode: value.queryMode,
      business: value.businessName,
      sortMode: value.sortMode,
      uid: value.uid,
      startDay: '',
      endDay: '',
      page: page,
      pageSize: pageSize
    }
    if (value.queryMode === 'week') {
      params.startDay = value.selectTime.format('YYYYMMDD')
    }
    if (value.queryMode === 'month') {
      params.startDay = value.selectTimeRange[0].format('YYYYMM')
      params.endDay = value.selectTimeRange[1].format('YYYYMM')
    }
    if (value.queryMode === 'range' || value.queryMode === 'daily') {
      params.startDay = value.selectTimeRange[0].format('YYYYMMDD')
      params.endDay = value.selectTimeRange[1].format('YYYYMMDD')
    }
    if (value.queryMode === 'week') {
      let endTime = moment(value.selectTime).add(6, 'days')
      params.endDay = endTime.format('YYYYMMDD')
      params.queryMode = 'range'
    }
    return params
  }

  // 根据时间选择模式使用不同的组件
  getTimeSelectorByMode = (mode) => {
    if (mode === 'daily') { // 按天
      return <Form.Item name='selectTimeRange' label='日期范围' >
        <RangePicker showToday />
      </Form.Item>
    }
    if (mode === 'week') {
      return <Form.Item name='selectTime' label='时间' >
        <DatePicker format={(v) => {
          let endTime = moment(v)
          endTime.add(6, 'days')
          return `${v.format('YYYY-MM-DD')} ~ ${endTime.format('YYYY-MM-DD')}`
        }}
        />
      </Form.Item>
    }
    if (mode === 'month') {
      return <Form.Item name='selectTimeRange' label='月份范围' >
        <RangePicker picker='month' showToday allowClear />
      </Form.Item>
    }
    if (mode === 'total') {
      return <Form.Item name='selectTime' label='时间' >
        <DatePicker value={moment()} disabled format={(v) => { return '历史累计' }} />
      </Form.Item>
    }
    if (mode === 'range') {
      return <Form.Item name='selectTimeRange' label='时间' >
        <RangePicker showToday allowClear />
      </Form.Item>
    }
    return '???'
  }

  genColumnTooltip = (title) => {
    return {
      filterDropdown: (<span />),
      filterIcon: (
        <Tooltip placement='top' title={title}>
          <QuestionCircleOutlined style={{ fontSize: '16px' }} />
        </Tooltip>
      )
    }
  }

  render () {
    const { profitData: { list }, businessNameOptions } = this.props.model
    const { selectTimeMode, loading, total, page, pageSize, searchParams, profitTip } = this.state

    const columns = [
      { title: '时间', dataIndex: 'time' },
      { title: '业务', dataIndex: 'business' },
      { title: 'UID', dataIndex: 'uid' },
      { title: '昵称', dataIndex: 'nick' },
      { title: '总消费/元', dataIndex: 'consume', ...this.genColumnTooltip('抽取道具消费金额') },
      { title: '总收益/元', dataIndex: 'profit', ...this.genColumnTooltip(profitTip) },
      { title: '总偏移/元', dataIndex: 'offset', ...this.genColumnTooltip('总消费-总获得, 正数表示用户累计失败') }
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <Card>
        <Row>
          <Col span={24}>
            <Form ref={form => { this.formRef = form }} onFinish={this.onFinish} initialValues={this.defaultFormValue} >
              <Space direction='horizontal' size='middle' align='baseline' >

                <Form.Item name='queryMode' label='时间类型'>
                  <Select options={queryModeOptions} style={{ minWidth: '10em' }} onChange={(v) => { this.setState({ selectTimeMode: v }) }} />
                </Form.Item>

                {this.getTimeSelectorByMode(selectTimeMode)}

                <Form.Item name='businessName' label='业务' >
                  <Select options={businessNameOptions} style={{ minWidth: '8em' }} defaultValue='' />
                </Form.Item>

                <Form.Item name='sortMode' label='排序方式' >
                  <Select options={sortMode} style={{ minWidth: '8em' }} defaultValue='' />
                </Form.Item>

                <Form.Item name='uid' label='UID' >
                  <InputNumber style={{ minWidth: '8em' }} />
                </Form.Item>

                <Divider type='vertical' />
                <Button htmlType='submit' type='primary'>查询</Button>
                <Button onClick={this.exportList} >导出</Button>
              </Space>
            </Form>
          </Col>
          <Col span={24}>
            <Table loading={loading}
              columns={columns}
              rowKey='index'
              size='small'
              dataSource={list?.map((item, index) => { item.index = index; return item })}
              pagination={{
                pageSize: pageSize,
                total: total,
                current: page + 1,
                showSizeChanger: true,
                pageSizeOptions: [2, 10, 50, 100, 200, 500, 1000, 2000, 4000],
                showTotal: (total, range) => { return `共${total}条数据, ${Math.ceil(total / pageSize)}页` },
                onChange: (pageNo, pageSize) => { this.setState({ page: pageNo - 1, pageSize: pageSize }); this.queryList(searchParams, pageNo - 1, pageSize) }
              }}
            />
          </Col>
        </Row>
      </Card>
    )
  }
}

export default ProfitMonitor
