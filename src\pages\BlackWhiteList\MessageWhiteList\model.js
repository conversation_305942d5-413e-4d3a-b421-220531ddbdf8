/* eslint-disable eqeqeq */
import { getLists, whiteListAddMult, whiteListDel } from './api'
import { Modal, message } from 'antd'
import { checkUid } from '@/utils/common'

// 检查uid列表
function checkUidList (uidList) {
  if (typeof uidList !== 'string') {
    Modal.warn({ content: '发生错误，请查看控制台' })
    console.error('[添加消息中心白名单]: 类型错误， typeof uidList=', typeof uidList)
    return false
  }
  if (uidList.length === 0) {
    Modal.warn({ content: '请输入数据再提交' })
    return false
  }
  let tmpArray = uidList.split('\n')
  for (let i = 0; i < tmpArray.length; i++) {
    if (!checkUid(tmpArray[i])) {
      Modal.warn({ content: '格式不符合,请修改后再提交：' + tmpArray[i] })
      return false
    }
  }
  return true
}

export default {
  namespace: 'messageWhiteList',
  state: {
    updating: false, // 添加白名单事件是否在处理中
    displayData: [],
    selectUid: 0, // 删除的uid
    uidList: ''
  },

  reducers: {
    // 更新data到消息中心白名单数据列表
    displayList (state, { payload: data }) {
      if (!Array.isArray(data)) {
        console.error('unexpect data type: data=', data)
        return
      }
      return {
        ...state,
        displayData: data
      }
    },
    // 设置‘添加标签页-添加’按钮的状态，true为正在处理中
    setBtnStatus (state, { payload: status }) {
      if (status !== true && status !== false) {
        console.error('unexpect argument in setBtnStatus: status=', status)
        Modal.error('发生错误，请查看控制台')
        return
      }
      return {
        ...state,
        updating: status
      }
    }
  },

  effects: {
    // 请求并刷新消息中心白名单列表数据
    * getWhiteListData ({ params }, { select, call, put }) {
      let resp = yield call(getLists)
      let { data: { status } } = resp
      let uidList = resp.data.uid_list
      if (uidList === null) {
        message.warning('数据为空')
        yield put({
          type: 'displayList',
          payload: []
        })
        return
      }
      if (status !== 0 || !Array.isArray(uidList)) {
        console.error('getWhiteListData() get data error: response=', resp)
        Modal.error({ content: '获取消息中心白名单数据失败，请检查控制台' })
        return
      }
      for (let i = 0; i < uidList.length; i++) {
        uidList[i].idx = i + 1
      }
      yield put({
        type: 'displayList',
        payload: uidList
      })
    },
    // 批量添加消息中心白名单
    * addWhiteListByList ({ payload }, { call, put }) {
      const { uidList } = payload
      if (!checkUidList(uidList)) {
        return
      }
      yield put({
        type: 'setBtnStatus',
        payload: true
      })
      let resp = yield call(whiteListAddMult, uidList)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[添加白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 0) {
        message.success('添加成功')
      } else {
        console.error('addWhiteListBySid()：[添加消息中心白名单] 返回结果为：', resp)
        Modal.warn({ content: '添加失败, 请检查控制台' })
      }
      yield put({
        type: 'setBtnStatus',
        payload: false
      })
    },
    // 批量删除消息中心白名单
    * delWhiteListByList ({ payload }, { call, put }) {
      const { uidList } = payload
      if (!checkUidList(uidList)) {
        return
      }
      yield put({
        type: 'setBtnStatus',
        payload: true
      })
      let resp = yield call(whiteListDel, uidList)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 0) {
        message.success('删除成功')
      } else {
        console.error('[批量删除消息中心白名单] resp=', resp)
        Modal.error({ content: '删除失败，请查看控制台' })
      }
      yield put({
        type: 'setBtnStatus',
        payload: false
      })
    },
    // 单个删除消息中心白名单
    * delWhiteListByUids ({ payload }, { call, put }) {
      const uid = payload
      if (!checkUid(uid)) {
        return
      }
      let resp = yield call(whiteListDel, uid + '\n')
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 0) {
        message.success('删除成功')
        yield put({
          type: 'getWhiteListData'
        })
      } else {
        Modal.error({ content: '删除失败，请查看控制台' })
        console.error('delWhiteListByUid() resp=', resp)
      }
    }
  }
}
