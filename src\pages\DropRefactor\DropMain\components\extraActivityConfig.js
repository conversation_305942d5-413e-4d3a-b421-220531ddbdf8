import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, message, Table, Typography, Space, Popconfirm, Row, Col, Button, Modal, Form, Input } from 'antd'
import { timeFormater } from '@/utils/common'
import { SimpleTimePicker } from '@/components/SimpleComponents/timePicker'
import { ArrayEditor, ActStatus } from '@/components/SimpleComponents'
import moment from 'moment'
const { Link } = Typography
const namespace = 'dropMain'
const defaultCfg = { actId: moment().unix(), actName: '待命名', startTime: moment().unix(), endTime: moment().add(1, 'days').unix(), tag: '', graySid: [] }

@connect(({ dropMain }) => ({
  model: dropMain
}))

class ExtraActivityConfig extends Component {
  state = {
    modalVisible: false,
    opType: 'ADD',
    editingItem: {}
  }

  componentDidMount = () => {
    this.refreshList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  refreshList = () => {
    this.callModel('getExtraActivityList')
  }

  submitUpdate = (v) => {
    const { opType } = this.state
    v.opType = opType
    this.callModel('updateExtraActivityConfig', {
      params: v,
      isDetailMode: true,
      isJsonMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('更新失败:' + msg)
        } else {
          message.success('更新成功!')
        }
        this.setState({ modalVisible: false })
        this.refreshList()
      }
    })
  }

  submitDelete = (v) => {
    v.opType = 'DELETE'
    this.callModel('updateExtraActivityConfig', {
      params: v,
      isDetailMode: true,
      isJsonMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('删除活动失败:' + msg)
        } else {
          message.success('删除成功!')
        }
        this.refreshList()
      }
    })
  }

  render () {
    const { extraActivityList } = this.props.model || {}
    const { modalVisible, opType } = this.state

    const columns = [
      { dataIndex: 'actId', title: '活动ID' },
      { dataIndex: 'actName', title: '活动名称' },
      { dataIndex: 'startTime', title: '活动时间', render: (v, r) => { return `${timeFormater(r.startTime, 1)} ~ ${timeFormater(r.endTime, 1)}` } },
      { dataIndex: 'tag', title: '备注信息' },
      { dataIndex: 'graySid', title: '灰度频道', render: (v) => { return v?.lenght > 0 ? <ArrayEditor value={v || []} type='number' isEdit={false} /> : '全量' } },
      { dataIndex: 'actId', title: '活动状态', render: (v, r) => { return <ActStatus start={r.startTime} end={r.endTime} /> } },
      { dataIndex: 'opUid', title: '更新人UID' },
      { dataIndex: 'updateTime', title: '更新时间', render: (v) => timeFormater(v) },
      { dataIndex: 'actId',
        title: '操作',
        render: (v, r) => {
          const timenow = moment().unix()
          if (timenow > r.endTime) {
            return '-'
          }
          return <Space>
            <Link onClick={() => { this.formRef.setFieldsValue(r); this.setState({ modalVisible: true, editingItem: r, opType: 'UPDATE' }) }} >更新</Link>
            <Popconfirm title={`确认删除活动ID ${v} 吗?`} onConfirm={() => this.submitDelete(r)} >
              <Link type='danger'>删除</Link>
            </Popconfirm>
          </Space>
        } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <Card>
        <Row>
          <Col span={24} style={{ marginBottom: '1em' }}>
            <Button type='primary' onClick={() => { this.formRef.resetFields(); this.setState({ modalVisible: true, opType: 'ADD' }) }} >新增翻倍活动</Button>
          </Col>
          <Col span={24}>
            <Table columns={columns} dataSource={extraActivityList} />
          </Col>
        </Row>
        <Modal forceRender visible={modalVisible} title={opType === 'ADD' ? '新建活动' : '更新活动'}
          onCancel={() => this.setState({ modalVisible: false })} onOk={() => this.formRef.submit()} >
          <Form ref={from => { this.formRef = from }} initialValues={defaultCfg} onFinish={v => this.submitUpdate(v)}>
            <Form.Item label='活动ID' name='actId' >
              <Input disabled />
            </Form.Item>
            <Form.Item label='活动名称' name='actName' >
              <Input />
            </Form.Item>
            <Form.Item label='开始时间' name='startTime' >
              <SimpleTimePicker inputType='timestamp' outputType='timestamp' format='YYYY-MM-DD HH:mm:ss' showTime />
            </Form.Item>
            <Form.Item label='结束时间' name='endTime' >
              <SimpleTimePicker inputType='timestamp' outputType='timestamp' format='YYYY-MM-DD HH:mm:ss' showTime />
            </Form.Item>
            <Form.Item label='灰度频道' name='graySid' >
              <ArrayEditor type='number' isEdit />
            </Form.Item>
            <Form.Item label='备注信息' name='tag' >
              <Input />
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default ExtraActivityConfig
