import { getRecycleInfo } from './api.js'
import { message } from 'antd'

export default {
  namespace: 'cowBossDetail',
  state: {
    displayData: {
      'startTime': '',
      'endTime': '',
      'actID': '',
      'roundProfit': '',
      'recycleLimit': '',
      'delayGetReword': '',
      'timestamp': '',
      'todayRecyclingValue': '',
      'totalRecyclingValue': '',
      'todayRewardCount': '',
      'todayRewardValue': '',
      'totalRewardCount': '',
      'totalRewardValue': ''
    }
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },

  effects: {
    * queryList ({ params }, { select, call, put }) {
      let resp = yield call(getRecycleInfo)
      let { data: { status, act } } = resp
      if (status !== 0) {
        message.error('发生错误，请检查控制台： message=' + resp.data.message)
        console.error('queryList() bad response: resp=', resp)
        return
      }
      yield put({
        type: 'updateState',
        payload: { name: 'displayData', newValue: act }
      })
      message.success('数据获取成功')
    }
  }
}
