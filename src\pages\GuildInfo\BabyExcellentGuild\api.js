import request from '@/utils/request'
import { stringify } from 'qs'

export function listInfo (params) {
  return request(`/guild/boss/bbexcellentguild/list?${stringify(params)}`)
}

export function addInfo (params) {
  return request(`/guild/boss/bbexcellentguild/add?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

export function deleteInfo (params) {
  return request(`/guild/boss/bbexcellentguild/delete?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

export function updateInfo (params) {
  return request(`/guild/boss/bbexcellentguild/update?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}
