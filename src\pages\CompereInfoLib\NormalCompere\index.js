import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import ExportRemoteData from '@/components/ExportRemoteData'
import {
  Button,
  Card,
  Col,
  DatePicker,
  Divider,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Row,
  Select,
  Table,
  Tabs
} from 'antd'
import { stringify } from 'qs'
import CompereDetailInfo from '../CompereDetailInfo'

const FormItem = Form.Item
const Option = Select.Option
const namespace = 'normalCompere'
const { TextArea } = Input

var moment = require('moment')

const businessTypeJY = 1 // 交友
const businessTypePK = 2 // 约战
const businessTypeBABY = 3 // 宝贝

const sex = [
  { idx: -1, label: '全部' },
  { idx: 0, label: '女' },
  { idx: 1, label: '男' }
]

// const remarkSex = [
//   { idx: -1, label: '全部' },
//   { idx: 0, label: '女' },
//   { idx: 1, label: '男' },
//   { idx: 2, label: '无备注' }
// ]
//
// const contractType = [
//   { idx: -1, label: '全部' },
//   { idx: 0, label: '否' },
//   { idx: 1, label: '是' }
// ]

const mediaType = [
  { idx: '', label: '全部' },
  { idx: 'video', label: '视频' },
  { idx: 'audio', label: '音频' }
]

const silenceTypeMap = {
  0: '-',
  1: '近期活跃',
  2: '轻度沉默',
  3: '中度沉默',
  4: '重度沉默'
}

const jyCompereTypeOptions = [
  { idx: '', label: '全部' },
  { idx: 'OrdinaryJyCompere', label: '普通主持' },
  // { idx: 'OrdinaryJyTtt', label: '厅天团' },
  { idx: 'OrdinaryJyStar', label: '星光主持' }
]

@connect(({ normalCompere }) => ({
  model: normalCompere
}))

class NormalCompere extends Component {
  constructor (props) {
    super(props)

    this.refreshInfo(businessTypeJY, 1)
  }

  momentToDayStartMillis = (t) => {
    if (t) {
      return new Date(t.format('YYYY-MM-DD') + ' 00:00:00').getTime()
    }
    return 0
  }

  momentToDayEndMillis = (t) => {
    if (t) {
      return new Date(t.format('YYYY-MM-DD') + ' 23:59:59').getTime()
    }
    return 0
  }

  // business: 1-交友，2-约战，3-宝贝
  getSignTimeQuery = (business) => {
    return {
      begSignStartTime: this.momentToDayStartMillis(this.state['begSignStartTime' + business]),
      endSignStartTime: this.momentToDayEndMillis(this.state['endSignStartTime' + business]),
      begSignEndTime: this.momentToDayStartMillis(this.state['begSignEndTime' + business]),
      endSignEndTime: this.momentToDayEndMillis(this.state['endSignEndTime' + business])
    }
  }

  // 刷新信息
  onRefreshCompereInfoClick = (item) => {
    const { business } = this.state
    const { dispatch } = this.props
    let data = { uids: '' + item.uid, ds: this.getDsType(business) }
    console.log(data, item)
    dispatch({
      type: `${namespace}/refreshCompereList`,
      payload: data
    })
  }

  columns = [
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: 'YY号', dataIndex: 'yyno', align: 'center' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: '短位id', dataIndex: 'asid', align: 'center' },
    {
      title: '主持身份',
      dataIndex: 'compereType',
      align: 'center',
      render: (text, record) => {
        if (record.compereType === 'OrdinaryJyCompere') {
          return '普通主持'
        } else if (record.compereType === 'OrdinaryJyTtt') {
          return '厅天团'
        } else if (record.compereType === 'SuperJyCompere') {
          return '超级主持'
        } else if (record.compereType === 'SuperJyTingGroup') {
          return '超级天团'
        } else if (record.compereType === 'SuperJyInteractive') {
          return '帽子超级'
        } else if (record.compereType === 'SuperJyReady') {
          return '准超级'
        } else if (record.compereType === 'OrdinaryYzBbAnchor') {
          return '普通主播'
        } else if (record.compereType === 'SuperKing') {
          return '王牌主播'
        } else if (record.compereType === 'OrdinaryJyStar') {
          return '星光主持'
        }
      }
    },
    { title: '沉默主持', dataIndex: 'silenceType', align: 'center', render: (text, record) => (silenceTypeMap[record.silenceType]) },
    {
      title: '签约开始时间',
      dataIndex: 'signStartTime',
      align: 'center',
      render: (text, record) => {
        if (record.signStartTime > 0) {
          return moment.unix(record.signStartTime / 1000).format('YYYY-MM-DD')
        }
      }
    },
    {
      title: '签约结束时间',
      dataIndex: 'signEndTime',
      align: 'center',
      render: (text, record) => {
        if (record.signEndTime > 0) {
          return moment.unix(record.signEndTime / 1000).format('YYYY-MM-DD')
        }
      }
    },
    {
      title: '实名性别',
      dataIndex: 'gender',
      align: 'center',
      render: (text, record) => {
        if (record.gender === 0) {
          return '女'
        } else if (record.gender === 1) {
          return '男'
        }
      }
    },
    { title: '运营备注昵称', dataIndex: 'remarkNick', align: 'center' },
    {
      title: '音视频',
      dataIndex: 'tendencyCompereType',
      align: 'center',
      render: (text, record) => {
        if (text === 'video') {
          return '视频'
        } else if (text === 'audio') {
          return '音频'
        } else {
          return ''
        }
      }
    },
    { title: '运营备注', dataIndex: 'remarkOpt', align: 'center' },
    { title: '操作人', dataIndex: 'lastOperator', align: 'center' },
    {
      title: '详细信息',
      key: 'operation',
      align: 'center',
      fixed: 'right',
      exportIgnore: true,
      render: (text, record) => (<span><a onClick={this.showDetailModal(record)}>详情</a></span>)
    },
    {
      title: '历史记录',
      align: 'center',
      fixed: 'right',
      exportIgnore: true,
      render: (text, record) => (
        <span>
          <a onClick={this.showremarkHistoryModal(record)}>查询</a>
        </span>)
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      render: (text, record) => (
        <span>
          <a onClick={this.showremarkModal(record)}>备注</a>
          <Divider type='vertical' />
          <a onClick={() => this.onRefreshCompereInfoClick(record)}>刷新</a>
        </span>)
    },
    {
      title: '操作',
      align: 'center',
      key: 'jy',
      fixed: 'right',
      render: (text, record) => (
        <span>
          <a onClick={this.showRecoverSuperModal(record)}>恢复超主身份</a>
          <Divider type='vertical' />
          <a onClick={this.showremarkModal(record)}>备注</a>
          <Divider type='vertical' />
          <a onClick={() => this.onRefreshCompereInfoClick(record)}>刷新</a>
        </span>)
    }
  ]

  showDetailModal = (record) => () => {
    this.props.dispatch({
      type: `${namespace}/getCompereDetailInfo`,
      payload: { uid: record.uid, useSaveInfoIfNotContract: false }
    }).then(() => {
      this.setState({ visibleDetail: true })
    })
  }

  getTableColumns = () => {
    const { business } = this.state
    let tColumns = []
    this.columns.forEach(function (item) {
      if (business !== businessTypeJY && item.key === 'jy') return
      tColumns.push(item)
    })
    return tColumns
  }

  remarkHistoryColumns = [
    {
      title: '日期',
      dataIndex: 'opTime',
      align: 'left',
      render: (text, record) => {
        if (record.opTime > 0) {
          return moment.unix(record.opTime).format('YYYY-MM-DD HH:MM')
        }
      }
    },
    { title: '操作人', dataIndex: 'opPassport', align: 'left' },
    {
      title: '变更字段',
      dataIndex: 'field',
      align: 'left',
      render: (text, record) => {
        let content = []
        let arr = text.split(/\s+/)
        arr.forEach(function (item) {
          content.push(<div>{item}</div>)
        })
        return (<>{content}</>)
      }
    },
    {
      title: '变更描述',
      dataIndex: 'desc',
      align: 'left',
      render: (text, record) => {
        let content = []
        let arr = text.split(/;\s*【/)
        arr.forEach(function (item, index) {
          if (index > 0) {
            item = '【' + item.substr(0, item.length - 1)
          }
          item = item.substr(0, item.length - 1)
          let regexp = /(【[^】]+】)字段从:([^，]*)，变为:(.*)/
          if (regexp.test(item)) {
            item = item.replace(regexp, '$1 从 [$2] 变成 [$3]')
          }
          content.push(<div>{item}</div>)
        })
        return (<>{content}</>)
      }
    }
  ]

  state = {
    business: businessTypeJY,
    pageSize: 10,
    remarkModalVisible: false,
    currentBusiness: 1,
    currentIndex: 1,
    remarkHistoryModalVisible: false,
    remarkHistoryCurrentUID: 0,
    remarkHistoryCurrentNick: ''
  }

  pageValue = () => {
    return {
      pageSize: this.state.pageSize,
      total: this.props.model.totalSize,
      current: this.state.currentIndex,

      onChange: (page, pageSize) => {
        this.pageChange(page, pageSize)
      },
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    }
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100'],
    showSizeChanger: true,
    isUpdate: false,
    onChange: (page, size) => {
      this.setState({ selectedRowKeys: null })
    },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  pageChange = (page, pageSize) => {
    this.refreshInfo(this.state.currentBusiness, page, pageSize)
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(selectedRows)
      let uids = []
      selectedRows.forEach(v => {
        uids.push(v.uid)
      })
      this.setState({ selectRecoverSuperUids: uids })
    }
  }

  showRecoverSuperModal = (record) => () => {
    if (this.formRefRecoverSuper) this.formRefRecoverSuper.setFieldsValue({ uids: [record.uid], count: 1 })
    this.setState({ recoverSuperModalVisible: true })
  }

  showRecoverSuperSelectModal = () => () => {
    const { selectRecoverSuperUids } = this.state
    console.log(selectRecoverSuperUids)
    if (!Array.isArray(selectRecoverSuperUids) || selectRecoverSuperUids.length === 0) {
      message.warn('请选择要恢复的uid')
      return
    }
    if (this.formRefRecoverSuper) {
      this.formRefRecoverSuper.setFieldsValue({ uids: selectRecoverSuperUids, count: selectRecoverSuperUids.length })
    }
    this.setState({ recoverSuperModalVisible: true })
  }

  showremarkModal = (record) => () => {
    if (record == null) record = {}

    if (record.remarkGender !== 0 && record.remarkGender !== 1) {
      record.remarkGender = -1
    }
    if (record.remarkNewCompere !== 0 && record.remarkNewCompere !== 1) {
      record.remarkNewCompere = -1
    }

    this.formRefRemarkInfo.resetFields()
    this.formRefRemarkInfo.setFieldsValue(record)

    let self = this
    this.getRemarkInfo(record.uid, () => {
      self.setState({ remarkModalVisible: true })
    })
  }

  showremarkHistoryModal = (record) => () => {
    if (record == null) record = {}

    this.formRefRemarkHistory.resetFields()
    this.formRefRemarkHistory.setFieldsValue(record)
    this.getRemarkHistory(record.uid)
    this.setState({ remarkHistoryModalVisible: true, remarkHistoryCurrentUID: record.uid, remarkHistoryCurrentNick: record.nick })
  }

  handleUpdateRemarkInfoSubmit = e => {
    this.formRefRemarkInfo.submit()
  }

  saveUpdateRemarkInfoFormRef = (info) => {
    this.formRefRemarkInfo = info
  }

  saveRemarkHistoryFormRef = (info) => {
    this.formRefRemarkHistory = info
  }

  hideremarkModal = () => {
    this.setState({ remarkModalVisible: false })
  }

  hideremarkHistoryModal = () => {
    this.setState({ remarkHistoryModalVisible: false })
  }

  hideRecoverSuperModal = () => {
    if (this.formRefRecoverSuper) {
      this.formRefRecoverSuper.resetFields()
    }
    this.setState({ recoverSuperModalVisible: false })
  }

  saveRecoverSuperFormRef = (info) => {
    this.formRefRecoverSuper = info
  }

  handleRecoverSuperSubmit = e => {
    this.formRefRecoverSuper.submit()
  }

  getRemarkInfo = (uid, callback) => {
    const { dispatch } = this.props
    let cbFunc = (record) => {
      let info = {}
      if (record.id) {
        info = { remarkNick: record.nick, remarkOpt: record.remarkOpt, remarkGender: record.gender, remarkNewCompere: record.newCompere, remarkAVType: record.avType }
      }
      if (info.remarkGender !== 0 && info.remarkGender !== 1) {
        info.remarkGender = -1
      }
      if (info.remarkNewCompere !== 0 && info.remarkNewCompere !== 1) {
        info.remarkNewCompere = -1
      }
      this.formRefRemarkInfo.setFieldsValue(info)
      if (callback) {
        callback(info)
      }
    }

    var data = { from: 1, business: this.state.currentBusiness, uid: uid, cbFunc: cbFunc }
    dispatch({
      type: `${namespace}/getRemarkInfo`,
      payload: data
    })
  }

  getRemarkHistory = (uid) => {
    const { dispatch } = this.props

    var data = { from: 1, business: this.state.currentBusiness, uid: uid }
    dispatch({
      type: `${namespace}/getRemarkHistory`,
      payload: data
    })
  }

  setRemarkInfo = (v) => {
    const { dispatch } = this.props
    let cbFunc = () => {
      this.refreshInfo(this.state.currentBusiness, this.state.currentIndex)
    }

    let data = {
      uid: v.uid,
      business: this.state.currentBusiness,
      from: 1,
      gender: v.remarkGender,
      nick: v.remarkNick,
      remarkOpt: v.remarkOpt,
      avType: v.remarkAVType,
      newCompere: v.remarkNewCompere,
      cbFunc: cbFunc
    }

    dispatch({
      type: `${namespace}/updateRemarkInfo`,
      payload: data
    })
    this.setState({ remarkModalVisible: false })
  }

  onTagChange = (idx) => {
    console.log(idx)
    if (idx === '1') {
      this.refreshInfo(businessTypeJY, 1)
      this.setState({ business: businessTypeJY, currentBusiness: businessTypeJY })
    } else if (idx === '2') {
      this.refreshInfo(businessTypePK, 1)
      this.setState({ business: businessTypePK, currentBusiness: businessTypePK })
    } else if (idx === '3') {
      this.refreshInfo(businessTypeBABY, 1)
      this.setState({ business: businessTypeBABY, currentBusiness: businessTypeBABY })
    }
  }

  refreshInfo = (business, pageIndex, pageSize) => {
    let data = {}
    if (!pageIndex) {
      pageIndex = 1
    }
    if (!pageSize) {
      pageSize = this.state.pageSize
    }
    this.setState({ currentIndex: pageIndex, pageSize: pageSize })
    data = this.getQueryParams(business, pageIndex, pageSize)
    this.props.dispatch({
      type: `${namespace}/listCompere`,
      payload: data
    })
  }

  deleteHandle = (record) => () => {
    const { business } = this.state
    const { dispatch } = this.props
    let data = { id: record.id, business: business }
    console.log(data)
    dispatch({
      type: `${namespace}/deleteSettlementReport`,
      payload: data
    })
  }

  htmlJY = () => {
    const { model: { list } } = this.props

    let pagination = this.pageValue()
    return (
      <>
        <Row style={{ marginBottom: '1em' }}>
          <Col offset={0}>
            <span>
              <div style={{ marginBottom: 5 }}>
                <span style={{ marginLeft: 15 }}>UID</span>
                <Input placeholder='可输入多个,如：50016575 1621064462' onChange={e => this.setState({ searchUID1: e.target.value })} style={{ width: 550, marginLeft: 3 }} allowClear />
                <span style={{ marginLeft: 15 }}>YY号</span>
                <Input placeholder='可输入多个,如：909015575 1836993309' onChange={e => this.setState({ searchYY1: e.target.value })} style={{ width: 400, marginLeft: 3 }} allowClear />
                <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle1()}>查询</Button>
                <ExportRemoteData buttonStyle={{ marginLeft: 20 }} filename={'普通主持名单_交友'} columns={this.columns} uriBuilder={this.getQueryURI} />
                <Button style={{ marginLeft: 20 }} type='primary' onClick={this.showRecoverSuperSelectModal()}>批量恢复超主身份</Button>
              </div>
              <span style={{ marginLeft: 15 }}>短位ID</span>
              <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID1: e })} style={{ width: 120, marginLeft: 3 }} />
              <span style={{ marginLeft: 15 }}>主持身份</span>
              <Select style={{ width: 120, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchCompereType1: v })} allowClear>
                {jyCompereTypeOptions.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>
              <span style={{ marginLeft: 15 }}>沉默主持</span>
              <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchSilenceType: v })} allowClear>
                <Option key={0} value={0}>全部</Option>
                <Option key={1} value={1}>近期活跃</Option>
                <Option key={2} value={2}>轻度沉默</Option>
                <Option key={3} value={3}>中度沉默</Option>
                <Option key={4} value={4}>重度沉默</Option>
              </Select>
              <span style={{ marginLeft: 15 }}>实名性别</span>
              <Select style={{ width: 120, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchRealSex1: v })} allowClear>
                {sex.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>
              {/* <span style={{ marginLeft: 15 }}>备注性别</span> */}
              {/* <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchRemarkSex1: v })} allowClear> */}
              {/*  {remarkSex.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))} */}
              {/* </Select> */}
              <span style={{ marginLeft: 15 }}>音视频</span>
              <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchMediaType1: v })} allowClear>
                {mediaType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>
              {/* <span style={{ marginLeft: 15 }}>是否新签</span> */}
              {/* <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchIfNewContract1: v })} allowClear> */}
              {/*  {contractType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))} */}
              {/* </Select> */}
              <div style={{ marginTop: 5 }}>
                <span style={{ marginLeft: 15 }}>昵称</span>
                <Input placeholder='请输入' onChange={e => this.setState({ searchNick1: e.target.value })} style={{ width: 120, marginLeft: 3 }} allowClear />
                <span style={{ marginLeft: 15 }}>签约开始时间</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='开始时间'
                  value={this.state.begSignStartTime1}
                  onChange={(v) => {
                    this.setState({ begSignStartTime1: v })
                  }}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 5 }}>~</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='结束时间'
                  value={this.state.endSignStartTime1}
                  onChange={(v) => this.setState({ endSignStartTime1: v })}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 15 }}>签约结束时间</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='开始时间'
                  value={this.state.begSignEndTime1}
                  onChange={(v) => {
                    this.setState({ begSignEndTime1: v })
                  }}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 5 }}>~</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='结束时间'
                  value={this.state.endSignEndTime1}
                  onChange={(v) => this.setState({ endSignEndTime1: v })}
                  style={{ marginLeft: 5 }}
                />
              </div>
            </span>
          </Col>
        </Row>
        <Row style={{ marginBottom: '1em' }}>
          <Col offset={0}>
            <div>
              <div>说明：</div>
              <div>1、查询当前签约交友/约战/宝贝，且主持身份为普通的名单，，每10min更新一次（数据源：交友-Boss-主持全量信息库、约战-Zbase-签约记录tab-业务VIPPK、宝贝-游戏宝贝运营管理后台-游戏宝贝tab-宝贝签约信息）</div>
              <div>2、音视频：若主持在才艺主持名单，则为视频主持；否则为音频主持</div>
              <div>3、运营备注昵称：备注昵称用于精彩世界-热门直播/交友速配tab、hgame首页排行榜 的拉取名单，请谨慎填写</div>
            </div>
            <Table style={{ marginTop: 10 }} rowSelection={this.rowSelection} tabelLayout='fixed' rowKey={(record, index) => index} bordered dataSource={list} columns={this.getTableColumns()} pagination={pagination} scroll={{ x: 'max-content' }} />
          </Col>
        </Row>
      </>
    )
  }

  htmlPK = () => {
    const { model: { list } } = this.props

    let pagination = this.pageValue()
    return (
      <>
        <Row style={{ marginBottom: '1em' }}>
          <Col offset={0}>
            <span>
              <div style={{ marginBottom: 5 }}>
                <span style={{ marginLeft: 15 }}>UID</span>
                <Input placeholder='可输入多个,如：50016575 1621064462' onChange={e => this.setState({ searchUID2: e.target.value })} style={{ width: 550, marginLeft: 3 }} allowClear />
                <span style={{ marginLeft: 15 }}>YY号</span>
                <Input placeholder='可输入多个,如：909015575 1836993309' onChange={e => this.setState({ searchYY2: e.target.value })} style={{ width: 550, marginLeft: 3 }} allowClear />
                <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle2()}>查询</Button>
                <ExportRemoteData buttonStyle={{ marginLeft: 20 }} filename={'普通主持名单_约战'} columns={this.columns} uriBuilder={this.getQueryURI} />
              </div>
              <span style={{ marginLeft: 15 }}>短位ID</span>
              <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID2: e })} style={{ width: 120, marginLeft: 3 }} />
              <span style={{ marginLeft: 15 }}>实名性别</span>
              <Select style={{ width: 120, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchRealSex2: v })} allowClear>
                {sex.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>
              {/* <span style={{ marginLeft: 15 }}>备注性别</span> */}
              {/* <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchRemarkSex2: v })} allowClear> */}
              {/*  {remarkSex.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))} */}
              {/* </Select> */}
              <span style={{ marginLeft: 15 }}>音视频</span>
              <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchMediaType2: v })} allowClear>
                {mediaType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>
              {/* <span style={{ marginLeft: 15 }}>是否新签</span> */}
              {/* <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchIfNewContract2: v })} allowClear> */}
              {/*  {contractType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))} */}
              {/* </Select> */}
              <div style={{ marginTop: 5 }}>
                <span style={{ marginLeft: 15 }}>昵称</span>
                <Input placeholder='请输入' onChange={e => this.setState({ searchNick2: e.target.value })} style={{ width: 120, marginLeft: 3 }} allowClear />

                <span style={{ marginLeft: 15 }}>签约时间</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='开始时间'
                  value={this.state.begSignStartTime2}
                  onChange={(v) => {
                    this.setState({ begSignStartTime2: v })
                  }}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 5 }}>~</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='结束时间'
                  value={this.state.endSignEndTime2}
                  onChange={(v) => this.setState({ endSignEndTime2: v })}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 15 }}>签约结束时间</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='开始时间'
                  value={this.state.begSignEndTime2}
                  onChange={(v) => {
                    this.setState({ begSignEndTime2: v })
                  }}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 5 }}>~</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='结束时间'
                  value={this.state.endSignEndTime2}
                  onChange={(v) => this.setState({ endSignEndTime2: v })}
                  style={{ marginLeft: 5 }}
                />
              </div>
            </span>
          </Col>
        </Row>
        <Row style={{ marginBottom: '1em' }}>
          <Col offset={0}>
            <div>
              <div>说明：</div>
              <div>1、查询当前签约交友/约战/宝贝，且主持身份为普通的名单，，每10min更新一次（数据源：交友-Boss-主持全量信息库、约战-Zbase-签约记录tab-业务VIPPK、宝贝-游戏宝贝运营管理后台-游戏宝贝tab-宝贝签约信息）</div>
              <div>2、音视频：若主持在才艺主持名单，则为视频主持；否则为音频主持</div>
              <div>3、运营备注昵称：备注昵称用于精彩世界-热门直播/交友速配tab、hgame首页排行榜 的拉取名单，请谨慎填写</div>
            </div>
            <Table style={{ marginTop: 10 }} tabelLayout='fixed' rowKey={(record, index) => index} bordered dataSource={list} columns={this.getTableColumns()} pagination={pagination} scroll={{ x: 'max-content' }} />
          </Col>
        </Row>
      </>
    )
  }

  htmlBaby = () => {
    const { model: { list } } = this.props
    let pagination = this.pageValue()
    return (
      <>
        <Row style={{ marginBottom: '1em' }}>
          <Col offset={0}>
            <span>
              <div style={{ marginBottom: 5 }}>
                <span style={{ marginLeft: 15 }}>UID</span>
                <Input placeholder='可输入多个,如：50016575 1621064462' onChange={e => this.setState({ searchUID3: e.target.value })} style={{ width: 550, marginLeft: 3 }} allowClear />
                <span style={{ marginLeft: 15 }}>YY号</span>
                <Input placeholder='可输入多个,如：909015575 1836993309' onChange={e => this.setState({ searchYY3: e.target.value })} style={{ width: 550, marginLeft: 3 }} allowClear />
                <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle3()}>查询</Button>
                <ExportRemoteData buttonStyle={{ marginLeft: 20 }} filename={'普通主持名单_宝贝'} columns={this.columns} uriBuilder={this.getQueryURI} />
              </div>
              <span style={{ marginLeft: 15 }}>短位ID</span>
              <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchASID3: e })} style={{ width: 120, marginLeft: 3 }} />
              <span style={{ marginLeft: 15 }}>实名性别</span>
              <Select style={{ width: 120, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchRealSex3: v })} allowClear>
                {sex.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>
              {/* <span style={{ marginLeft: 15 }}>备注性别</span> */}
              {/* <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchRemarkSex3: v })} allowClear> */}
              {/*  {remarkSex.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))} */}
              {/* </Select> */}
              <span style={{ marginLeft: 15 }}>音视频</span>
              <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchMediaType3: v })} allowClear>
                {mediaType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))}
              </Select>
              {/* <span style={{ marginLeft: 15 }}>是否新签</span> */}
              {/* <Select style={{ width: 100, marginLeft: 5 }} placeholder='全部' onChange={(v) => this.setState({ searchIfNewContract3: v })} allowClear> */}
              {/*  {contractType.map((item, index) => (<Select.Option key={item.idx} value={item.idx}>{item.label}</Select.Option>))} */}
              {/* </Select> */}
              <div style={{ marginTop: 5 }}>
                <span style={{ marginLeft: 15 }}>昵称</span>
                <Input placeholder='请输入' onChange={e => this.setState({ searchNick3: e.target.value })} style={{ width: 120, marginLeft: 3 }} allowClear />
                <span style={{ marginLeft: 15 }}>签约开始时间</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='开始时间'
                  value={this.state.begSignStartTime3}
                  onChange={(v) => {
                    this.setState({ begSignStartTime3: v })
                  }}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 5 }}>~</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='结束时间'
                  value={this.state.endSignStartTime3}
                  onChange={(v) => this.setState({ endSignStartTime3: v })}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 15 }}>签约结束时间</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='开始时间'
                  value={this.state.begSignEndTime3}
                  onChange={(v) => {
                    this.setState({ begSignEndTime3: v })
                  }}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 5 }}>~</span>
                <DatePicker
                  format='YYYY-MM-DD'
                  placeholder='结束时间'
                  value={this.state.endSignEndTime3}
                  onChange={(v) => this.setState({ endSignEndTime3: v })}
                  style={{ marginLeft: 5 }}
                />
              </div>
            </span>
          </Col>
        </Row>
        <Row style={{ marginBottom: '1em' }}>
          <Col offset={0}>
            <div>
              <div>说明：</div>
              <div>1、查询当前签约交友/约战/宝贝，且主持身份为普通的名单，，每10min更新一次（数据源：交友-Boss-主持全量信息库、约战-Zbase-签约记录tab-业务VIPPK、宝贝-游戏宝贝运营管理后台-游戏宝贝tab-宝贝签约信息）</div>
              <div>2、音视频：若主持在才艺主持名单，则为视频主持；否则为音频主持</div>
              <div>3、运营备注昵称：备注昵称用于精彩世界-热门直播/交友速配tab、hgame首页排行榜 的拉取名单，请谨慎填写</div>
            </div>
            <Table style={{ marginTop: 10 }} tabelLayout='fixed' rowKey={(record, index) => index} bordered dataSource={list} columns={this.getTableColumns()} pagination={pagination} scroll={{ x: 'max-content' }} />
          </Col>
        </Row>
      </>
    )
  }

  searchHandle1 = () => () => {
    this.refreshInfo(businessTypeJY, 1)
  }

  searchHandle2 = () => () => {
    this.refreshInfo(businessTypePK, 1)
  }

  searchHandle3 = () => () => {
    this.refreshInfo(businessTypeBABY, 1)
  }

  getDsType = (businessType) => {
    switch (businessType) {
      case '1':
        return 'ordinary_jy'
      case '2':
        return 'ordinary_yz'
      case '3':
        return 'ordinary_bb'
      default:
        return 'ordinary_jy'
    }
  }

  filterNumberArray = (value) => {
    if (!value) {
      return ''
    }
    value = value.replace(/([^\d]|[\s，, ])+/g, ',')
    if (value.endsWith(',')) {
      value = value.substring(0, value.length - 1)
    }
    return value
  }

  getQueryParams = (business, page, size) => {
    let params

    if (business === businessTypeJY) {
      const { searchUID1, searchYY1, searchASID1, searchRealSex1, searchRemarkSex1, searchIfNewContract1, searchMediaType1, searchCompereType1, searchNick1 } = this.state
      params = { ds: 'ordinary_jy', page: page, size: size, uid: searchUID1, yyno: searchYY1, asid: searchASID1, gender: searchRealSex1, remarkGender: searchRemarkSex1, newCompere: searchIfNewContract1, tendencyCompereType: searchMediaType1, compereType: searchCompereType1, nick: searchNick1 }
    } else if (business === businessTypePK) {
      const { searchUID2, searchYY2, searchASID2, searchRealSex2, searchRemarkSex2, searchIfNewContract2, searchMediaType2, searchNick2 } = this.state
      params = { ds: 'ordinary_yz', page: page, size: size, uid: searchUID2, yyno: searchYY2, asid: searchASID2, gender: searchRealSex2, remarkGender: searchRemarkSex2, newCompere: searchIfNewContract2, tendencyCompereType: searchMediaType2, nick: searchNick2 }
    } else {
      const { searchUID3, searchYY3, searchASID3, searchRealSex3, searchRemarkSex3, searchIfNewContract3, searchMediaType3, searchNick3 } = this.state
      params = { ds: 'ordinary_bb', page: page, size: size, uid: searchUID3, yyno: searchYY3, asid: searchASID3, gender: searchRealSex3, remarkGender: searchRemarkSex3, newCompere: searchIfNewContract3, tendencyCompereType: searchMediaType3, nick: searchNick3 }
    }

    // 添加签约时间条件时间
    params = Object.assign(params, this.getSignTimeQuery(business))
    params.uid = this.filterNumberArray(params.uid)
    params.yyno = this.filterNumberArray(params.yyno)
    return params
  }

  getQueryURI = (page, size) => {
    let params = this.getQueryParams(this.state.currentBusiness, page, size)
    return `/compere_data_base/list_compere?${stringify(params)}`
  }

  handleCancelDetailNew = (v) => {
    this.setState({ visibleDetail: v })
  }

  recoverSuperHandler = (v) => {
    let data = { uids: v.uids, reason: v.reason, recoverSuperPrivileged: v.recoverSuperPrivileged }

    console.log(data)
    this.props.dispatch({
      type: `${namespace}/recoverSuperCompere`,
      payload: data
    })
    this.hideRecoverSuperModal()
  }

  render () {
    const { model: { totalSize, remarkHistoryRecords, compereDetailInfo } } = this.props
    const { remarkHistoryCurrentUID, remarkHistoryCurrentNick, visibleDetail } = this.state
    const { route } = this.props
    const { TabPane } = Tabs

    let jyTab = '交友'
    if (this.state.currentBusiness === businessTypeJY) {
      jyTab = '交友（' + totalSize + ')'
    }

    let pkTab = '约战'
    if (this.state.currentBusiness === businessTypePK) {
      pkTab = '约战（' + totalSize + ')'
    }

    let babyTab = '宝贝'
    if (this.state.currentBusiness === businessTypeBABY) {
      babyTab = '宝贝（' + totalSize + ')'
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='1' onChange={(idx) => this.onTagChange(idx)} type='card' size='large'>
            <TabPane tab={jyTab} key='1'>
              {this.htmlJY()}
            </TabPane>
            <TabPane tab={pkTab} key='2'>
              {this.htmlPK()}
            </TabPane>
            <TabPane tab={babyTab} key='3'>
              {this.htmlBaby()}
            </TabPane>
          </Tabs>
        </Card>

        <Modal forceRender width={400} visible={this.state.remarkModalVisible} title='修改备注' onCancel={this.hideremarkModal} onOk={this.handleUpdateRemarkInfoSubmit} okText='提交' cancelText='取消'>
          <Form ref={this.saveUpdateRemarkInfoFormRef} onFinish={(v) => {
            this.setRemarkInfo(v)
          }}>
            <FormItem label='YY  号' name='yyno'>
              <Input disabled='true' />
            </FormItem>
            <FormItem label='备注昵称' name='remarkNick'>
              <Input />
            </FormItem>
            <FormItem label='运营备注' name='remarkOpt'>
              <TextArea showCount allowClear />
            </FormItem>
            <FormItem name='uid' hidden>
              <Input />
            </FormItem>
          </Form>
        </Modal>

        <Modal forceRender footer={null} width={1300} visible={this.state.remarkHistoryModalVisible} title='历史记录' onCancel={this.hideremarkHistoryModal}>
          <Form ref={this.saveRemarkHistoryFormRef}>
            <span style={{ marginLeft: 15 }}>UID</span>
            <Input min={0} style={{ width: 120, marginLeft: 3 }} value={remarkHistoryCurrentUID} />
            <span style={{ marginLeft: 15 }}>昵称</span>
            <Input min={0} style={{ width: 240, marginLeft: 3 }} value={remarkHistoryCurrentNick} />

            <Table style={{ marginTop: 10 }} tabelLayout='fixed' rowKey={(record, index) => index} bordered dataSource={remarkHistoryRecords} columns={this.remarkHistoryColumns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
          </Form>
        </Modal>

        <Modal forceRender width={300} visible={this.state.recoverSuperModalVisible} title='恢复超主身份' onCancel={this.hideRecoverSuperModal} onOk={this.handleRecoverSuperSubmit} okText='确认并提交' cancelText='取消'>
          <Form ref={this.saveRecoverSuperFormRef} onFinish={(v) => {
            this.recoverSuperHandler(v)
          }}>
            <FormItem label='UID列表' name='uids' hidden >
              <Input disabled />
            </FormItem>
            <FormItem label='UID处理个数' name='count' >
              <Input disabled />
            </FormItem>
            <FormItem label='是否有特权' name='recoverSuperPrivileged' rules={[{ required: true }]}>
              <Select allowClear>
                <Option key={0} value={false}>无</Option>
                <Option key={1} value>有</Option>
              </Select>
            </FormItem>
            <FormItem label='恢复原因' name='reason' rules={[{ required: true }]}>
              <Input />
            </FormItem>
          </Form>
        </Modal>

        <CompereDetailInfo visibleDetail={visibleDetail} record={compereDetailInfo} cancelHandler={this.handleCancelDetailNew} />
      </PageHeaderWrapper>
    )
  }
}

export default NormalCompere
