import request from '@/utils/request'

export function getLists () {
  let url = `/white_list/videodating_recommend_list`
  return request(url, { jsonp: true })
}

export function whiteListAdd (sid, ssid) {
  let form = 'sid=' + sid + '&ssid=' + ssid
  return request(`/white_list/videodating_recommend_add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: form
  })
}

export function whiteListDel (sid, ssid) {
  let form = 'sid=' + sid + '&ssid=' + ssid
  return request(`/white_list/videodating_recommend_del`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: form
  })
}
