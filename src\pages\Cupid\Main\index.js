import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, message } from 'antd'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import CupidNormalPool from './tabs/normalPool'
import CupidCrazyPool from './tabs/crazyPool'

const namespace = 'cupid'
const cupitOriginID = 340235 // 丘比特礼物id

@connect(({ cupid }) => ({
  model: cupid
}))

class Cupid extends Component {
  state = {
    targetOriginID: cupitOriginID // 入口礼物ID
  }

  componentDidMount = () => {
    this.getAllPrizeList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // 获取可用道具列表 (不区分业务) TODO: 看看要不要换个接口
  getAllPrizeList = () => {
    this.callModel('getAllPrizeList', {
      cbFunc: (ok) => {
        if (!ok) {
          message.error('获取可选道具列表失败,请稍后再试')
        }
      }
    })
  }

  // 标签页发生切换
  onTagChange = (record) => {
    // message.info('record==>' + record)
  }

  render () {
    const { route } = this.props
    const { targetOriginID } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='normal' type='card' onChange={(record) => this.onTagChange(record)}>
            <TabPane tab='常规配置' key='normal'>
              <CupidNormalPool originID={targetOriginID} />
            </TabPane>
            <TabPane tab='狂欢时刻' key='crazy'>
              <CupidCrazyPool originID={targetOriginID} />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default Cupid
