import React, { Component } from 'react'
import { Table, Modal } from 'antd'

class HistoryRecord extends Component {
  handleCancelDetail = e => {
    this.props.cancelHandler(false)
  }

  render () {
    let { visible, list, loading, columns } = this.props

    return (
      <Modal footer={null} forceRender width={1800} visible={visible} title='变更历史' onCancel={this.handleCancelDetail}>
        <Table loading={loading} style={{ marginTop: 10 }} size='small' columns={columns} dataSource={list} />
      </Modal>
    )
  }
}

export default HistoryRecord
