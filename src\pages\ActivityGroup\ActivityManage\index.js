import { connect } from 'dva'
import React, { Component } from 'react'
import { <PERSON><PERSON>, <PERSON>, Col, DatePicker, Divider, Form, Input, message, Modal, Popconfirm, Row, Select, Table, Tooltip } from 'antd'
import { cacheModeOptions, configTypeOptions, DefaultAfterActCacheMode, DefaultInActCacheMode, DefaultPreActCacheMode, elementTypeWithoutObjectOptions, formatTimestamp, needConfirmFlowOptions, resolveCacheMode, ruleDataTypeOptions } from '../common'
import { getRuleTokensError, Kind, parseRule, parseRuleSourceTokens } from '../ruleengine'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { copyToClip, formatOptions, parseOptionsFromMultiLineString } from '@/utils/common'
import { CopyTwoTone, DeleteOutlined, EditOutlined, PlusSquareOutlined } from '@ant-design/icons'
import moment from 'moment'
import ActivityGroupManage from './components/group'

const namespace = 'ActivityManage'
const defaultPageSize = 10

@connect(({ ActivityManage }) => ({
  model: ActivityManage
}))

class ActivityManage extends Component { // 默认页面组件，不需要修改
  constructor (props) {
    super(props)
    this.initState()
    this.loadCommonSelectOptions()
    this.searchHandle()
  }

  loadCommonSelectOptions = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/listAllActivityOptions`
    })
    dispatch({
      type: `${namespace}/listAllBusiness`
    })
    dispatch({
      type: `${namespace}/listAllRuleData`
    })
  }

  initState = () => {
    this.state = {
      pagination: {
        pageSize: defaultPageSize,
        total: 0,
        current: 1,
        defaultCurrent: 1,
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        onChange: (page, pageSize) => {
          this.pageChange(page, pageSize)
        },
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      }
    }
  }

  updatePagination = (page, pageSize, total) => {
    const { pagination } = this.state
    pagination.current = page || 1
    pagination.pageSize = pageSize || defaultPageSize
    if (total !== undefined) {
      pagination.total = total
    }
    this.setState({ pagination: pagination })
    return pagination
  }

  // 分页信息变更
  pageChange = (page, pageSize, total) => {
    let pagination = this.updatePagination(page, pageSize, total)
    this.searchHandle(pagination.current, pagination.pageSize)
  }

  // 获取当前查询条件
  getQueryCondition = (page, size, cond) => {
    const { pagination } = this.state
    page = page || pagination.current || 1
    size = size || pagination.pageSize || defaultPageSize
    let pageInfo = {
      pageNo: page,
      pageSize: size
    }
    if (!cond) {
      return pageInfo
    }

    return Object.assign(cond, pageInfo)
  }

  // 处理查询事件。
  searchHandle = (page, size, cond) => {
    const { dispatch } = this.props

    let query = this.getQueryCondition(page, size, cond)
    if (!query) {
      return
    }
    let self = this
    dispatch({
      type: `${namespace}/pageListActivity`,
      payload: query,
      callback: (data) => {
        self.updatePagination(query.pageNo || 1, query.pageSize || defaultPageSize, data ? data.total : 0)
      }
    })
  }

  onQueryClick = (values) => {
    const { pagination } = this.state
    let size = pagination.pageSize || defaultPageSize
    this.searchHandle(1, size, values)
  }

  // 规则函数表头
  getColumns = () => {
    const { model: { businessList } } = this.props
    return [
      { title: '序号', dataIndex: 'index', align: 'left' },
      { title: '活动ID', dataIndex: 'id', align: 'left' },
      {
        title: '活动名称',
        dataIndex: 'name',
        align: 'left',
        render: (v, record) => {
          return <Tooltip placement={'bottomLeft'} title={!record.desc || record.desc === '-' ? v : record.desc}>
            <div>{v || record.desc}</div>
          </Tooltip>
        }
      },
      {
        title: '涉及业务',
        dataIndex: 'businessIds',
        align: 'left',
        render: (v, record) => {
          return (v || []).map(item => {
            return formatOptions(item, businessList, '-', 'id', 'name')
          }).join('、')
        }
      },
      {
        title: '分组角色',
        dataIndex: 'roleList',
        align: 'left',
        render: (v, record) => {
          let opts = []
          // 添加分组按钮
          opts.push(<PlusSquareOutlined key={'add'} onClick={() => this.showEditRoleModal(record, null, 'add')} />)
          // 配置的
          if (v && v.length > 0) {
            v.forEach(item => {
              item.businessIds.forEach(business => {
                opts.push(<div key={item.id} style={{ marginLeft: 10 }}>
                  <a size={'small'} onClick={() => this.showGroupResult(record, item, business, true)}>{formatOptions(business, businessList, '-', 'id', 'name')}-{item.name}</a>
                  <EditOutlined style={{ marginLeft: 3 }} onClick={() => this.showEditRoleModal(record, item, 'edit')} />
                  <Popconfirm title='确认删除?' onConfirm={() => this.onDeleteRole(record, item)}><DeleteOutlined style={{ marginLeft: 3, color: 'red' }} /></Popconfirm>
                </div>)
              })
            })
          }

          return opts
        }
      },
      {
        title: '活动时间',
        dataIndex: 'startTime',
        align: 'left',
        render: (v, item) => {
          return '[' + formatTimestamp(item.startTime) + ' ~ ' + formatTimestamp(item.endTime) + ']'
        }
      },
      { title: '更新时间', dataIndex: 'updateTime', align: 'left', render: (v) => formatTimestamp(v) },
      { title: '操作人', dataIndex: 'lastOperator', align: 'left' },
      {
        title: '操作',
        key: 'operation',
        align: 'left',
        render: (text, item) => {
          let now = parseInt(new Date().getTime() / 1000)
          let canDel = item.startTime > now
          return (
            <>
              <span>
                <a size='small' type='primary' onClick={() => this.showEditModal(item, 'edit')}>编辑</a>
              </span>
              <Divider type={'vertical'} />
              <span>
                {/* 分组结果进来, 不限制对应业务, 只看不导入 */}
                <a size='small' type='primary' onClick={() => this.showGroupResult(item)}>分组结果</a>
              </span>
              {
                canDel
                  ? <span>
                    <Divider type='vertical' />
                    <Popconfirm hidden={item.configType !== 'CUSTOM'} title='确认删除吗，删除后分组信息会级联删除?' onConfirm={() => this.onDelete(item)}><a style={{ color: 'red' }}>删除</a></Popconfirm>
                  </span>
                  : ''
              }
            </>
          )
        }
      }
    ]
  }

  onDeleteRole = (activity, item) => {
    let self = this
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/removeRoleInfo`,
      payload: { actID: activity.id, roleIDs: [item.id] },
      callback: (rsp) => {
        if (rsp && rsp.status === 0) {
          self.searchHandle()
        } else {
          message.error('删除失败: ' + rsp.msg)
        }
      }
    })
  }

  closeEditRoleModal = () => {
    this.setState({ editRoleModalVisible: false })
    if (this.formRefEditRole) {
      this.formRefEditRole.resetFields()
    }
  }

  showEditRoleModal = (editActivity, editRole, editRoleMode) => {
    const { model: { businessList } } = this.props

    if (editRoleMode === 'add') {
      if (!editRole) {
        editRole = {}
      }
      if (this.formRefEditRole) {
        this.formRefEditRole.resetFields()
      }
    }
    editRole.groupItemsText = ''
    if (editRole.groupItems && editRole.groupItems.length > 0) {
      editRole.groupItemsText = (editRole.groupItems || []).map(v => v.id + ':' + v.name).join('\n')
    }
    editRole.preActCacheMode = resolveCacheMode(editRole.preActCacheMode, DefaultPreActCacheMode)
    editRole.inActCacheMode = resolveCacheMode(editRole.inActCacheMode, DefaultInActCacheMode)
    editRole.afterActCacheMode = resolveCacheMode(editRole.afterActCacheMode, DefaultAfterActCacheMode)
    editRole.recvGiftTriggerTime = editRole.recvGiftTriggerTime ? moment.unix(editRole.recvGiftTriggerTime) : null

    let actBusinessListEditRole = []
    if (businessList && editActivity.businessIds && editActivity.businessIds.length > 0) {
      businessList.filter(v => {
        for (let i = 0; i < editActivity.businessIds.length; i++) {
          if (v.id === editActivity.businessIds[i]) {
            actBusinessListEditRole.push(v)
            break
          }
        }
      })
    }

    if (this.formRefEditRole) {
      this.formRefEditRole.setFieldsValue(editRole)
    }

    if (editRoleMode !== 'add' && editRole.ruleProgram) {
      try {
        editRole.theRule = parseRule(editRole.ruleProgram)
        console.log('规则： ', editRole.theRule)
      } catch (e) {
        console.log('解析分组规则失败:', e.message)
      }
    }

    this.setState({ editRoleModalVisible: true, editActivity: editActivity, editRole: editRole, editRoleMode: editRoleMode, submitRoleAction: false, actBusinessListEditRole: actBusinessListEditRole })
  }

  onEditRoleModalOk = () => {
    this.setState({ submitRoleAction: true })
    this.formRefEditRole.submit()
  }

  onEditRoleFormSubmit = (values) => {
    const { editActivity, editRole, editRoleMode } = this.state
    let submitItem = editRole
    if (editRoleMode === 'edit' && editRole.configType === 'INNER') { // 内置函数，只允许修改名称、介绍
      editRole.name = values.name
    } else {
      submitItem = Object.assign(editRole, values)
    }

    if (values.groupItemsText) {
      editRole.groupItems = parseOptionsFromMultiLineString(values.groupItemsText, 'id', 'name')
    }
    submitItem.actID = editActivity.id
    submitItem.roleID = submitItem.id

    if (values.recvGiftTriggerTime) {
      submitItem.recvGiftTriggerTime = values.recvGiftTriggerTime.unix()
    }

    const { dispatch } = this.props
    let self = this
    dispatch({
      type: `${namespace}/upsertRoleInfo`,
      payload: submitItem,
      callback: (rsp) => {
        if (rsp && rsp.status === 0) {
          self.setState({ editRoleModalVisible: false, editRole: undefined })
          if (editRoleMode === 'add') {
            self.searchHandle()
          }
        } else {
          message.error(editRoleMode + ' 失败: ' + rsp.msg)
        }
      }
    })
  }

  onHistoryUsedRoleClick = (roleID, roleName) => {
    if (this.formRefEditRole) {
      this.formRefEditRole.setFieldsValue({ id: roleID, name: roleName })
    }
  }

  getRenderContentForCompareDesc = (ruleData) => {
    let ref = '$[' + ruleData.id + ']'
    switch (ruleData.dataType) {
      case 'string':
        return (<>
          <div>== : 等值比较， 如 {ref} == 'A'</div>
          <div>in : 判断给定值在某个范围内，如 {ref} in ('a', 'b')</div>
        </>
        )
      case 'timestamp':
        return (<>
          <div>==, >, >=, &lt;, &lt;= : 等值比较，如 {ref} > '2021-01-02 00:00:00'</div>
        </>
        )
      case 'number':
        return (<>
          <div>==, >, >=, &lt;, &lt;= : 如 {ref} == 1， {ref} > 1</div>
        </>
        )
      case 'bool':
        return (<>
          <div>== : 等值比较，如 {ref} == true， {ref} == false</div>
        </>
        )
      default:
        return ''
    }
  }

  ruleProgramValidator = (ruleObject, value) => {
    const { model: { ruleDataMap, businessMap } } = this.props
    const { editRole } = this.state
    if (value) {
      value = value.trim()
    }
    if (!value) {
      return Promise.resolve()
    }
    try {
      let tokens = parseRuleSourceTokens(value)
      let len = tokens.length
      let errMsg = getRuleTokensError(tokens, (token, index) => {
        // 数字，字符串，bool，变量，检查前后 是不是 {}, 是的话，按照分组结果来检查
        if (token.kind === Kind.BOOL || token.kind === Kind.VAR || token.kind === Kind.NUMBER) {
          let group = '' + token.value
          let clauseMatched = false
          if (group && group !== '0') {
            if (index - 1 >= 0 && index + 1 < len) {
              let pre = tokens[index - 1]
              let next = tokens[index + 1]
              if (pre.kind === Kind.CLAUSE && pre.value === '{' && next.kind === Kind.CLAUSE && next.value === '}') {
                clauseMatched = true
                if (formatOptions(group, editRole.groupItems, '', 'id', 'name') === '') {
                  return '未识别的分组ID:' + group
                }
              }
            }
          }
          if (group !== '0' && token.kind === Kind.VAR && !clauseMatched) {
            return '非法变量: ' + token.value
          }
        }

        if (token.kind === Kind.REF) { // 引用类型
          let ruleData = ruleDataMap ? ruleDataMap[token.value] : null
          if (!ruleData) {
            return '不识别的数据引用 $[' + token.value + ']'
          }
          if (index + 1 >= len) { // 没有下一个 token
            return
          }
          // 获取下一个token，看看是不是比较类型
          let nextToken = tokens[index + 1]
          // 下一个是 逻辑操作， 或者 {, 那么验证该数据必须是 bool
          if (nextToken.kind === Kind.LOGIC || (nextToken.kind === Kind.CLAUSE && nextToken.value === '{')) {
            if (ruleData.dataType !== 'bool') {
              return '数据引用 $[' + token.value + ']放在表达式后面必须是 bool 类型'
            }
            return
          }
          // 下一个是 ==, 判断是否有 options
          if (nextToken.kind === Kind.COMPARE && nextToken.value === '==') {
            // 继续找下一个 token，值类型， NUMBER， string，bool，timestamp
            if (index + 2 >= len) {
              return '数据引用 $[' + token.value + '] 等值比较缺少右值'
            }
            let vToken = tokens[index + 2]
            if (vToken.kind !== Kind.STRING && vToken.kind !== Kind.NUMBER && vToken.kind !== Kind.BOOL) {
              return '数据引用 $[' + token.value + '] 等值比较缺少右值'
            }
            if (ruleData.dataType === 'bool' && vToken.kind !== Kind.BOOL) {
              return '数据引用 $[' + token.value + ']放在表达式后面必须是 bool 类型'
            }

            if (ruleData.id === 'QUERY_SOURCE') {
              if (!businessMap[vToken.value]) {
                return '数据引用 $[' + token.value + '] 值 [' + vToken.value + ']不是合法的业务ID'
              }
            }
            let options = ruleData.options || []
            if (options.length > 0) {
              if (formatOptions(vToken.value, options) === '') {
                console.log(vToken.value, options)
                return '数据引用 $[' + token.value + '] 值 [' + vToken.value + ']非法'
              }
            }

            return
          }
          // IN ，判断IN 后面的值
          if (nextToken.kind === Kind.IN) {
            for (let i = index + 2; i < len; i++) {
              let vToken = tokens[i]
              if (vToken.kind === Kind.CLAUSE && vToken.value === ')') {
                return
              }
              if (vToken.kind === Kind.SEP || (vToken.kind === Kind.CLAUSE && vToken.value === '(')) {
                continue
              }
              if (ruleData.id === 'QUERY_SOURCE') {
                if (!businessMap[vToken.value]) {
                  return '数据引用 $[' + token.value + '] 值 [' + vToken.value + ']不是合法的业务ID'
                }
              }
              let options = ruleData.options || []
              if (options.length > 0) {
                if (formatOptions(vToken.value, options) === '') {
                  console.log(vToken.value, options)
                  return '数据引用 $[' + token.value + '] 值 [' + vToken.value + ']非法'
                }
              }
            }
          }
        }
      })
      if (!errMsg) {
        return Promise.resolve()
      }
      console.log(tokens)
      return Promise.reject(new Error(errMsg))
    } catch (e) {
      return Promise.reject(e)
    }
  }

  showTranslateRuleProgram = (ruleProgram) => {
    const { model: { ruleDataMap, businessMap } } = this.props
    const { editRole } = this.state
    let groupItems = {}
    if (editRole && editRole.groupItems && editRole.groupItems.length > 0) {
      editRole.groupItems.forEach(item => {
        groupItems[item.id] = item
      })
    }
    let tokens = parseRuleSourceTokens(ruleProgram, false)
    let contents = []
    let skipBlank = (i, justFilter) => {
      i++
      for (; i < tokens.length; i++) {
        let token = tokens[i]
        if (token.kind !== Kind.BLANK) {
          return i
        }
        if (!justFilter) {
          contents.push(token.value)
        }
      }
      return i
    }
    for (let i = 0; i < tokens.length; i++) {
      let token = tokens[i]
      switch (token.kind) {
        case Kind.REF:
          let ruleData = ruleDataMap[token.value]
          if (ruleData) {
            contents.push(ruleData.name)
            if (ruleData.id === 'QUERY_SOURCE' || (ruleData.options && ruleData.options.length > 0)) {
              i = skipBlank(i)
              // 查找后续比较值
              let next = i < tokens.length ? tokens[i] : null
              if (!next || (next.kind !== Kind.IN && next.kind !== Kind.COMPARE)) {
                i--
                break
              }
              contents.push(next.value)
              i = skipBlank(i)
              for (; i < tokens.length; i++) {
                let vToken = tokens[i]
                if (vToken.kind === Kind.CLAUSE && vToken.value === ')') {
                  i--
                  break
                }
                if (vToken.kind === Kind.STRING || vToken.kind === Kind.NUMBER || vToken.kind === Kind.BOOL) {
                  let content = vToken.value
                  if (ruleData.id === 'QUERY_SOURCE') {
                    let business = businessMap[vToken.value]
                    if (business) {
                      content = business.name
                    }
                  }
                  if (ruleData.options && ruleData.options.length > 0) {
                    for (let k = 0; k < ruleData.options.length; k++) {
                      let o = ruleData.options[k]
                      if (o.value === vToken.value) {
                        content = o.label
                        break
                      }
                    }
                  }
                  contents.push(content)
                  if (next.kind !== Kind.IN) {
                    break
                  }
                }
                contents.push(vToken.value)
              }
            }
          }
          break
        case Kind.CLAUSE: // 子句 {}
          if (token.value === '{') {
            let nIndex = skipBlank(i, true)
            if (nIndex < tokens.length) {
              let next = tokens[nIndex]
              if (next.kind === Kind.STRING || next.kind === Kind.NUMBER || next.kind === Kind.BOOL || next.kind === Kind.VAR) {
                let nnIndex = skipBlank(nIndex, true)
                if (nnIndex < tokens.length) {
                  let close = tokens[nnIndex]
                  if (close.kind === Kind.CLAUSE && close.value === '}') {
                    let item = groupItems[next.value]
                    if (item) {
                      contents.push('返回分组 ==> [' + item.id + '/' + item.name + ']')
                    } else {
                      contents.push('不分组')
                    }
                    i = nnIndex
                    break
                  }
                }
              }
            }
          }
          contents.push(token.value)
          break
        default:
          contents.push(token.value)
      }
    }
    // console.log('token t- string \n', contents.join(''))
    ruleProgram = contents.join('')
    Modal.info({
      title: '规则翻译',
      content: <Input.TextArea rows={15} style={{ width: '100%', height: '100%' }} defaultValue={ruleProgram} readOnly />,
      centered: true,
      width: '50%'
    })
  }

  // 分组角色操作对话框
  renderEditRoleModal = () => {
    const { editRoleModalVisible, editActivity, editRole, editRoleMode, selectedRuleData, submitRoleAction, actBusinessListEditRole } = this.state
    const { model: { businessList, ruleDataList } } = this.props
    let titlePrefix = editActivity ? editActivity.id + '-' + editActivity.name + ' => ' : ''
    const ruleDataOptions = (ruleDataList || []).map(item => {
      let business = formatOptions(item.businessID, businessList, '-', 'id', 'name')
      return {
        label: business + ' | ' + item.type + ' | ' + item.name,
        value: business + ':' + item.type + ' | ' + item.id + ':' + item.name,
        data: item
      }
    })

    const cacheModeTooltip = { placement: 'rightTop', title: '不缓存：非白名单，每次都实时计算结果; 缓存优先：如果已经计算过，直接返回上一次计算结果；仅缓存：直接读取历史计算结果，如从未计算过则返回空' }

    return (
      <Modal width={'100%'} title={editRoleMode === 'add' || !editRole ? titlePrefix + '添加分组' : titlePrefix + '编辑 【' + editRole.id + ':' + editRole.name + '】'} visible={editRoleModalVisible} onCancel={this.closeEditRoleModal} onOk={this.onEditRoleModalOk}>
        <Form labelCol={{ span: 4 }} ref={(form) => {
          if (!submitRoleAction && !this.formRefEditRole && editRoleMode === 'edit') {
            form.setFieldsValue(editRole)
          }
          this.formRefEditRole = form
        }} onFinish={this.onEditRoleFormSubmit}>
          <Row gutter={24}>
            <Col span={5}>

              {/* 只能从涉及业务中选择 */}
              <Form.Item labelCol={{ span: 7 }} name='businessIds' label={'业务'} required>
                <Select mode='multiple' placeholder='请选择' options={(actBusinessListEditRole || []).map(v => {
                  return { label: v.id + ':' + v.name, value: v.id }
                })} filterOption allowClear />
              </Form.Item>

              <Form.Item labelCol={{ span: 7 }} label={'角色ID'} name='id' required rules={[{ required: true, min: 1, max: 256, message: '请输入' }]}>
                <Input disabled={editRoleMode === 'edit'} placeholder={'输入角色ID'} />
              </Form.Item>

              <Form.Item labelCol={{ span: 7 }} hidden={editRoleMode === 'edit'} label={'常见角色'}>
                <ul>
                  <li onClick={() => this.onHistoryUsedRoleClick('COMPERE', '主持分组')}>主持分组 ==> COMPERE</li>
                  <li onClick={() => this.onHistoryUsedRoleClick('GUILD', '工会分组')}>工会分组 ==> GUILD</li>
                  <li onClick={() => this.onHistoryUsedRoleClick('CHANNEL', '厅分组')}>厅分组 ==> CHANNEL</li>
                  <li onClick={() => this.onHistoryUsedRoleClick('TING', '厅天团')}>厅天团 ==> TING</li>
                  <li onClick={() => this.onHistoryUsedRoleClick('PWTUAN', '陪玩团')}>陪玩团 ==> PWTUAN</li>
                </ul>
              </Form.Item>

              <Form.Item labelCol={{ span: 7 }} name='name' label={'角色名称'} required rules={[{ required: true, min: 1, max: 256, message: '请输入' }]}>
                <Input placeholder={'请输入'} />
              </Form.Item>

              <Form.Item labelCol={{ span: 7 }} name='defaultResult' label={'默认分组'}>
                <Input placeholder={'请输入'} />
              </Form.Item>

              <Form.Item labelCol={{ span: 7 }} name='needConfirmFlow' label={'主持修改审批'}>
                <Select options={needConfirmFlowOptions} filterOption onChange={(v) => {
                  if (editRole) {
                    editRole.needConfirmFlow = v
                  }
                }} />
              </Form.Item>

              <Form.Item labelCol={{ span: 8 }} name='preActCacheMode' label={'活动前缓存模式'} tooltip={cacheModeTooltip}>
                <Select options={cacheModeOptions} filterOption onChange={(v) => {
                  if (editRole) {
                    editRole.preActCacheMode = v
                  }
                }} />
              </Form.Item>
              <Form.Item labelCol={{ span: 8 }} name='inActCacheMode' label={'活动中缓存模式'} tooltip={cacheModeTooltip}>
                <Select options={cacheModeOptions} filterOption onChange={(v) => {
                  if (editRole) {
                    editRole.inActCacheMode = v
                  }
                }} />
              </Form.Item>
              <Form.Item labelCol={{ span: 8 }} name='afterActCacheMode' label={'活动后缓存模式'} tooltip={cacheModeTooltip}>
                <Select options={cacheModeOptions} filterOption onChange={(v) => {
                  if (editRole) {
                    editRole.afterActCacheMode = v
                  }
                }} />
              </Form.Item>

              <Form.Item labelCol={{ span: 11 }} name='recvGiftTriggerTime' label={'在此之前收礼触发分组'}
                hidden={!editRole || editRole.id !== 'COMPERE'}
                tooltip={{ placement: 'rightTop', title: '用户收到礼物的时候，如果收礼的时间在当前设置时间之前，那么会触发分组规则计算，如过不需要预先计算分组，不用填写' }} rules={[{
                  validator: (_, value) => {
                    if (value && value.unix() >= editActivity.startTime) {
                      return Promise.reject(new Error('必须在活动开始之前'))
                    }
                    return Promise.resolve()
                  }
                }]}>
                <DatePicker disabled={!editActivity || editActivity.startTime <= new Date().getTime() / 1000} showTime='true' format='YYYY-MM-DD HH:mm:ss' disabledDate={(current) => {
                  if (!editRole) {
                    return true
                  }
                  return current.unix() >= editActivity.startTime
                }} />
              </Form.Item>

              <Form.Item labelCol={{ span: 7 }} name='groupItemsText' required label={'分组映射'} tooltip={{ placement: 'rightTop', title: '格式：分组ID:分组名称，多个的话换行分隔' }} rules={[{
                validator: (_, value) => {
                  let opts = parseOptionsFromMultiLineString(value, 'id', 'name')
                  if (opts === false || opts.length < 1) {
                    return Promise.reject(new Error('分组映射不能为空，格式为：分组ID:分组名称，多个的话换行分隔'))
                  }
                  editRole.groupItems = opts
                  editRole.groupItemsText = value
                  this.setState({ editRole: editRole })
                  return Promise.resolve()
                }
              }]}>
                {/* <Input.TextArea rows={editRoleMode === 'edit' ? 20 : 14} placeholder='输入分组映射' /> */}
                <Input.TextArea rows={10} placeholder='输入分组映射' />
              </Form.Item>
            </Col>
            <Col span={11}>
              <Form.Item labelCol={{ span: 2 }} name='ruleProgram' label={'规则'} tooltip={{ placement: 'leftTop', title: '使用 $[规则数据引用ID] 来引用特定的数据，具体可用数据在右侧查询' }} rules={[{
                validator: (o, value) => {
                  let ret = this.ruleProgramValidator(o, value)
                  editRole.ruleProgram = value
                  this.setState({ editRole: editRole })
                  return ret
                }
              }]}>
                <Input.TextArea rows={28} placeholder='输入分组规则代码' />
              </Form.Item>
              <Button style={{ float: 'right' }} size={'small'} type={'default'}
                onClick={() => this.showTranslateRuleProgram(this.formRefEditRole.getFieldValue('ruleProgram'))}>翻译规则</Button>
              {/* {<Tree height={600} treeData={editRole ? (editRole.theRule ? [editRole.theRule] : []) : []} titleRender={ruleNodeRender} defaultExpandAll />} */}
            </Col>
            <Col span={8}>
              <label style={{ width: 200, marginRight: 10 }}>可用数据</label>
              <Select placeholder={'搜索可用数据'}
                style={{ width: '80%' }}
                options={ruleDataOptions}
                showSearch
                onChange={(item, v) => {
                  if (v.data.id === 'QUERY_SOURCE') {
                    v.data.options = businessList
                  }
                  this.setState({ selectedRuleData: v.data })
                }} />
              <div style={{ marginLeft: 70, marginTop: 5, color: 'red' }}>如果没有对应数据，请联系开发</div>

              {
                !selectedRuleData || !selectedRuleData.id
                  ? ''
                  : <div style={{ marginTop: 20 }}>
                    <Card style={{ width: '100%' }}>
                      <Row style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>引用方式：</label>
                        <div style={{ float: 'right' }}>
                          {'$[' + selectedRuleData.id + ']'} <CopyTwoTone size={'small'} onClick={() => copyToClip('$[' + selectedRuleData.id + ']', '$[' + selectedRuleData.id + ']')} />
                        </div>
                      </Row>
                      <Row style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>归属业务：</label>
                        <div style={{ float: 'right' }}>{formatOptions(selectedRuleData.businessID, businessList, '-', 'id', 'name')}</div>
                      </Row>
                      <Row style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>数据说明：</label>
                        <div style={{ float: 'right' }}>{selectedRuleData.desc || selectedRuleData.name}</div>
                      </Row>
                      <Row style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>配置类型：</label>
                        <div style={{ float: 'right' }}>{formatOptions(selectedRuleData.configType, configTypeOptions)}</div>
                      </Row>
                      <Row style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>数据类型：</label>
                        <div style={{ float: 'right' }}>{formatOptions(selectedRuleData.dataType, elementTypeWithoutObjectOptions)}</div>
                      </Row>
                      <Row style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>比较类型：</label>
                        <div style={{ float: 'right' }}>{this.getRenderContentForCompareDesc(selectedRuleData)}</div>
                      </Row>
                      <Row style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>来源类型：</label>
                        <div style={{ float: 'right' }}>{formatOptions(selectedRuleData.type, ruleDataTypeOptions)}</div>
                      </Row>
                      <Row hidden={!selectedRuleData.options || selectedRuleData.options.length < 1} style={{ marginBottom: 10 }}>
                        <label style={{ float: 'left', color: 'red' }}>可选项：</label>
                        <div style={{ float: 'right' }}>{selectedRuleData.options.map((item, index) => {
                          let value = item.value || item.id
                          return <div key={selectedRuleData.id + '_' + value + '_' + index}>{(item.label || item.name) + ' : ' + value}
                            <CopyTwoTone size={'small'} style={{ marginLeft: 5 }} onClick={() => copyToClip(value, value)} />
                          </div>
                        })}</div>
                      </Row>
                    </Card>
                  </div>
              }
            </Col>
          </Row>
        </Form>
      </Modal>
    )
  }

  showEditModal = (
    editItem, editMode
  ) => {
    if (editMode === 'add') {
      if (this.formRefEdit) {
        this.formRefEdit.resetFields()
      }
    }
    editItem.managersText = ''
    if (editItem.managers && editItem.managers.length > 0) {
      editItem.managersText = editItem.managers.join('\n')
    }

    // v.chTimeLimit = v.chTimeLimit?.map(item => { return moment.unix(item) })
    let activityTime = []
    if (editItem.startTime && editItem.startTime > 0) {
      activityTime[0] = moment.unix(editItem.startTime)
    }
    if (editItem.endTime && editItem.endTime > 0) {
      activityTime[1] = moment.unix(editItem.endTime)
    }
    editItem.activityTime = activityTime
    editItem.bypassActIds = editItem.bypassActIds || []

    if (this.formRefEdit) {
      this.formRefEdit.setFieldsValue(editItem)
    }
    this.setState({ editModalVisible: true, editItem: editItem, editMode: editMode })
  }

  closeEditModal = () => {
    if (this.formRefEdit) {
      this.formRefEdit.resetFields()
    }
    this.setState({ editModalVisible: false })
  }

  onEditModalOk = () => {
    this.formRefEdit.submit()
  }

  onEditFormSubmit = (values) => {
    const { editItem, editMode } = this.state
    let submitItem = editItem
    if (editMode === 'edit' && editItem.configType === 'INNER') { // 内置函数，只允许修改名称、介绍
      editItem.name = values.name
    } else {
      submitItem = Object.assign(editItem, values)
    }

    if (values.managersText) {
      editItem.managers = editItem.managersText.split(/[\s\n\r,，；]+/)
    }

    let activityTime = values.activityTime
    if (activityTime[0]) {
      editItem.startTime = activityTime[0].unix()
    }
    if (activityTime[1]) {
      editItem.endTime = activityTime[1].unix()
    }

    console.log('待提交的数据: ', submitItem, values)
    const reqType = editMode === 'add' ? `${namespace}/addActivity` : `${namespace}/updateBasicInfo`
    const { dispatch } = this.props
    let self = this
    dispatch({
      type: reqType,
      payload: submitItem,
      callback: (rsp) => {
        if (rsp && rsp.status === 0) {
          self.setState({ editModalVisible: false, editItem: undefined })
          if (editMode === 'add') {
            self.searchHandle()
          }
        } else {
          message.error(editMode + ' 失败: ' + (rsp ? rsp.msg : '未知错误'))
        }
      }
    })
  }

  onDelete = (item) => {
    console.log('准备删除：', item)
    let self = this
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/removeActivity`,
      payload: { id: item.id },
      callback: (rsp) => {
        if (rsp && rsp.status === 0) {
          self.searchHandle()
        } else {
          message.error('删除失败: ' + rsp.msg)
        }
      }
    })
  }

  // 编辑活动配置对话框
  renderEditModal = () => {
    const { model: { businessList, activityOptions } } = this.props
    const { editModalVisible, editItem, editMode } = this.state
    return (
      <Modal width={700} title={editMode === 'add' || !editItem ? '添加' : '编辑 【' + editItem.id + ':' + editItem.name + '】'} visible={editModalVisible} onCancel={this.closeEditModal} onOk={this.onEditModalOk}>
        <Form labelCol={{ span: 4 }} ref={(form) => {
          if (!this.formRefEdit) {
            form.setFieldsValue(editItem)
          }
          this.formRefEdit = form
        }} onFinish={this.onEditFormSubmit}>
          <Form.Item name='id' label={'活动ID'} required rules={[{ required: true, min: 1, max: 256, message: '请输入' }]}>
            <Input disabled={editMode === 'edit'} placeholder={'输入活动ID'} />
          </Form.Item>

          <Form.Item name='name' label={'活动名称'} required rules={[{ required: true, min: 1, max: 256, message: '请输入' }]}>
            <Input placeholder={'请输入'} />
          </Form.Item>

          <Form.Item name='desc' label={'活动介绍'} required>
            <Input.TextArea placeholder={'请输入'} />
          </Form.Item>

          <Form.Item name='businessIds' label={'涉及业务'} required>
            <Select placeholder='请选择' mode={'multiple'} options={(businessList || []).map(v => {
              return { label: v.name, value: v.id }
            })} />
          </Form.Item>

          <Form.Item name='activityTime' label='活动时间'>
            <DatePicker.RangePicker format='YYYY-MM-DD HH:mm:ss' showTime />
          </Form.Item>

          <Form.Item name='bypassActIds' label={'旁路到活动'} tooltip={{ placement: 'leftTop', title: '进行分组的时候，会旁路一份请求到指定活动，按照其他规则分组' }}>
            <Select placeholder='请选择' mode={'multiple'} options={(activityOptions || []).filter(v => !editItem || v.value !== editItem.id)} />
          </Form.Item>
        </Form>
      </Modal>
    )
  }

  // 分组角色进来, 限定对应业务/角色
  // 分组结果进来, 不限制业务, 只看不导入
  showGroupResult = (activity, role, business, fromGroupRole) => {
    const { model: { businessList } } = this.props
    let currentRole = role
    if (!currentRole && activity.roleList && activity.roleList.length > 0) {
      currentRole = activity.roleList[0]
    }

    console.log(businessList)
    let activityBusinessList = []
    if (businessList && activity.businessIds && activity.businessIds.length > 0) {
      businessList.filter(v => {
        for (let i = 0; i < activity.businessIds.length; i++) {
          if (fromGroupRole && business) {
            if (v.id === business) {
              activityBusinessList.push(v)
              break
            }
          } else {
            if (v.id === activity.businessIds[i]) {
              activityBusinessList.push(v)
              break
            }
          }
        }
      })
    }
    this.setState({ groupResultVisible: true, currentActivity: activity, currentRole: currentRole, activityBusinessList: activityBusinessList, fromGroupRole: fromGroupRole })
  }

  // 渲染分组结果
  renderGroupResult = () => {
    const { groupResultVisible, currentActivity, currentRole, activityBusinessList, fromGroupRole } = this.state
    return <Modal width={'98%'} title={currentActivity ? '活动【' + currentActivity.id + ':' + currentActivity.name + '】分组结果' : '分组结果'}
      visible={groupResultVisible} onCancel={() => this.setState({ groupResultVisible: false })}
      onOk={() => this.setState({ groupResultVisible: false })}>
      <ActivityGroupManage activity={currentActivity} role={currentRole} businessList={activityBusinessList} fromGroupRole={fromGroupRole} />
    </Modal>
  }

  // 渲染函数
  render () {
    const { route, model: { dataList, businessList } } = this.props
    const { pagination, groupResultVisible } = this.state
    const columns = this.getColumns()

    return (
      <>
        <PageHeaderWrapper title={route.name}>
          <Row style={{ marginBottom: '1em' }}>
            <Form layout={'inline'} ref={form => {
              this.formRefQuery = form
            }} onFinish={this.onQueryClick}>
              <Form.Item name={'id'} label={'活动ID'}>
                <Input placeholder='活动ID' style={{ width: 150 }} allowClear />
              </Form.Item>

              <Form.Item name={'name'} label={'活动名称'}>
                <Input placeholder='name' style={{ width: 150 }} allowClear />
              </Form.Item>

              <Form.Item name={'businessIds'} label={'涉及业务'}>
                <Select placeholder='请选择' style={{ width: 120 }} options={(businessList || []).map(v => {
                  return { label: v.name, value: v.id }
                })} onChange={() => this.formRefQuery.submit()} filterOption allowClear />
              </Form.Item>

              <Button type='primary' htmlType='submit'>查询</Button>
              <Divider type={'vertical'} />
              <Button type='primary' onClick={() => {
                this.formRefQuery.resetFields()
                this.formRefQuery.submit()
              }}>重置</Button>
              <Divider type={'vertical'} />
              <Button type={'primary'} onClick={() => this.showEditModal({}, 'add')}>添加</Button>
            </Form>
          </Row>
          <Row style={{ marginBottom: '1em' }}>
            <Col span={24}>
              <Table columns={columns}
                dataSource={dataList}
                size='small'
                pagination={pagination}
                showSorterTooltip={false}
                rowKey={record => record.id}
              />
            </Col>
          </Row>

          {/* 渲染编辑对话框 */}
          {this.renderEditModal()}

          {/* 渲染编辑角色对话框 */}
          {this.renderEditRoleModal()}

          {/* 渲染分组结果对话框 */}
          {groupResultVisible ? this.renderGroupResult() : ''}
        </PageHeaderWrapper>
      </>
    )
  }
}

export default ActivityManage
