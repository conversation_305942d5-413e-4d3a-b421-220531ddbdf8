import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import dateString from '@/utils/dateString'
import { Card, Table, Input, Modal, Form, Button, InputNumber, DatePicker, message, Select, Divider, Popconfirm } from 'antd'
import { stringify } from 'qs'
import moment from 'moment/moment'

const { RangePicker } = DatePicker

const namespace = 'babyExcellentCompere'

const typeMap = { 0: '-', 1: '电子签约', 2: '运营添加' }

@connect(({ babyExcellentCompere }) => ({
  model: babyExcellentCompere
}))

class BabyExcellentCompere extends Component {
  constructor (props) {
    super(props)

    this.refreshInfo()
  }

  columns = [
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: 'YY号', dataIndex: 'yy', align: 'center' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: '签约频道', dataIndex: 'sid', align: 'center' },
    { title: '签约短号频道', dataIndex: 'asid', align: 'center' },
    { title: '签约开始', dataIndex: 'startDate', align: 'center', render: (text, record) => (record.startDate === 0 ? '' : moment.unix(record.startDate).format('YYYY-MM-DD')) },
    { title: '签约结束', dataIndex: 'endDate', align: 'center', render: (text, record) => (record.endDate === 0 ? '' : moment.unix(record.endDate).format('YYYY-MM-DD')) },
    { title: '结算类型', dataIndex: 'settlementType', align: 'center', render: (text, record) => (record.settlementType === 0 ? '对私结算' : '对公结算') },
    { title: '身份证', dataIndex: 'idCard', align: 'center', render: (text, record) => (record.idCard === '0' ? '' : record.idCard) },
    { title: '添加类型', dataIndex: 'type', align: 'center', render: (text, record) => (typeMap[record.type]) },
    { title: '签约状态', dataIndex: 'contractStatus', align: 'center', render: (text, record) => (record.contractStatus ? '签约中' : '未签约') },
    { title: '操作人', dataIndex: 'operator', align: 'center' },
    { title: '添加时间', dataIndex: 'createDate', align: 'center', render: (text, record) => (record.createDate === 0 ? '' : dateString(record.createDate)) },
    { title: '更新时间', dataIndex: 'updateDate', align: 'center', render: (text, record) => (record.updateDate === 0 ? '' : dateString(record.updateDate)) },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (<span>
        <a onClick={this.showModalUpdate(record)}>更新</a>
        <Divider type='vertical' />
        <Popconfirm title='是否删除?' onConfirm={this.deleteHandle(record)} okText='是的' cancelText='暂不'>
          <a style={{ color: 'red' }} >删除</a>
        </Popconfirm></span>)
    }
  ]

  state = {
    visible: false,
    visibleUpdate: false,
    hadClickCheck: false,
    conformUidNum: 0,
    unConformUidNum: 0
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  refreshInfo = () => {
    let data = this.getParam()
    console.log(data)
    this.props.dispatch({
      type: `${namespace}/listInfo`,
      payload: data
    })
  }

  renderContent = (record) => {
    return (
      <div>
        {/* <Input.TextArea onChange={this.onTextChange} row={5} placeholder={'删除原因选填，最多100字符'} /> */}
        <Button onClick={this.deleteHandle(record)} style={{ marginLeft: 120, marginTop: 5 }} type='primary'>确定</Button>
      </div>
    )
  }

  onTextChange = e => {
    this.setState({ removeReason: e.target.value })
  }

  deleteHandle = (record) => e => {
    console.log('------------')
    const { removeReason } = this.state
    const { dispatch } = this.props
    let data = { uid: record.uid, removeReason: removeReason }
    console.log(data)
    dispatch({
      type: `${namespace}/deleteInfo`,
      payload: data
    })
  }

  showModel = () => () => {
    this.setState({ visible: true })
  }

  searchHandle = () => () => {
    this.refreshInfo()
  }

  exportURL = () => {
    let params = this.getParam()
    params['export'] = 1

    return `/supercompere/boss/bbexcellentcompere/list?${stringify(params)}`
  }

  getParam = () => {
    const { searchUID, searchYY, searchSID, searchASID, searchIDCard, searchStatus } = this.state

    let params = { uid: searchUID, yy: searchYY, sid: searchSID, asid: searchASID, idCard: searchIDCard, status: searchStatus }
    return params
  }

  hiddenModal = () => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.setState({ visible: false })
  }

  handleCancel = e => {
    this.hiddenModal()
  }

  handleSubmit = e => {
    if (this.formRef) {
      this.formRef.submit()
    }
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onFinish = values => {
    const { dispatch } = this.props

    if (values.contractDate === null || values.contractDate === undefined) {
      message.error('请输入签约时间')
      return
    }

    let data = { uid: Number(values.uid), sid: Number(values.sid), startDate: values.contractDate[0].unix(), endDate: values.contractDate[1].unix(), idCard: values.idCard }
    console.log(data)
    dispatch({
      type: `${namespace}/addInfo`,
      payload: data
    })

    this.hiddenModal()
  }

  showModalUpdate = (record) => () => {
    if (this.formRefUpdate) {
      this.formRefUpdate.resetFields()

      let startTime = moment(record.startDate * 1000)
      let endTime = moment(record.endDate * 1000)
      this.formRefUpdate.setFieldsValue({ uid: record.uid, contractDate: [startTime, endTime] })
    }
    this.setState({ visibleUpdate: true })
  }

  hiddenModalUpdate = () => {
    if (this.formRefUpdate) {
      this.formRefUpdate.resetFields()
    }
    this.setState({ visibleUpdate: false })
  }

  handleCancelUpdate = e => {
    this.hiddenModalUpdate()
  }

  handleSubmitUpdate = e => {
    if (this.formRefUpdate) {
      this.formRefUpdate.submit()
    }
  }

  saveFormRefUpdate = (formRef) => {
    this.formRefUpdate = formRef
  }

  onFinishUpdate = values => {
    const { dispatch } = this.props

    if (values.contractDate === null) {
      message.error('请输入签约时间')
      return
    }

    let data = { uid: Number(values.uid), startDate: values.contractDate[0].unix(), endDate: values.contractDate[1].unix() }
    console.log(data)
    dispatch({
      type: `${namespace}/updateInfo`,
      payload: data
    })

    this.hiddenModalUpdate()
  }

  rowSelection = {
    onChange: (selectedRowKeys, selectedRows) => {
      var value = selectedRows.map(item => item.familyId).join(',')
      this.setState({ removeKey: value })
      this.setState({ exportKey: selectedRows })
    },
    getCheckboxProps: record => ({
      disabled: record.name === 'Disabled User',
      name: record.name
    })
  }

  render () {
    const { visible, visibleUpdate } = this.state
    const { route, model: { list } } = this.props

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 10 },
        sm: { span: 20 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <div><Button type='primary' onClick={this.showModel()}>添加</Button></div>
          <div style={{ marginTop: 10 }} />
          <span>UID</span>
          <InputNumber min={0} onChange={e => this.setState({ searchUID: e })} style={{ width: 120, marginLeft: 3 }} />

          <span style={{ marginLeft: 15 }}>SID</span>
          <InputNumber min={0} onChange={e => this.setState({ searchSID: e })} style={{ width: 120, marginLeft: 3 }} />

          <span style={{ marginLeft: 15 }}>YY</span>
          <InputNumber min={0} onChange={e => this.setState({ searchYY: e })} style={{ width: 120, marginLeft: 3 }} />

          <span style={{ marginLeft: 15 }}>短位频道</span>
          <InputNumber min={0} onChange={e => this.setState({ searchASID: e })} style={{ width: 120, marginLeft: 3 }} />

          <span style={{ marginLeft: 15 }}>身份证</span>
          <Input onChange={e => this.setState({ searchIDCard: e.target.value })} style={{ width: 200, marginLeft: 3 }} />

          <span style={{ marginLeft: 15 }}>签约状态</span>
          <Select allowClear onChange={(v) => this.setState({ searchStatus: v })} style={{ width: 120, marginLeft: 3 }}>
            <Select.Option key={1} value={1}>签约中</Select.Option>
            <Select.Option key={2} value={2}>未签约</Select.Option>
          </Select>

          <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
          <Button style={{ marginLeft: 20 }} type='primary'><a href={this.exportURL()} target='_blank'>导出</a></Button>
          <Table style={{ marginTop: 10 }} tabelLayout='fixed' rowSelection={this.rowSelection} rowKey={(record, index) => index} bordered dataSource={list} columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
        </Card>

        <Modal forceRender visible={visible} title='添加独家主播' onCancel={this.handleCancel} onOk={this.handleSubmit} okText='提交'>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <Form.Item label='主播uid' name='uid' rules={[{ required: true }]}>
              <Input style={{ width: 200 }} />
            </Form.Item>
            <Form.Item label='签约公会' name='sid' rules={[{ required: true }]}>
              <Input style={{ width: 200 }} />
            </Form.Item>
            <Form.Item label='签约时间' name='contractDate' rules={[{ required: true }]}>
              <RangePicker format={'YYYY-MM-DD'} />
            </Form.Item>
            <Form.Item label='身份证' name='idCard' rules={[{ required: true }]}>
              <Input style={{ width: 200 }} />
            </Form.Item>
          </Form>
        </Modal>

        <Modal forceRender visible={visibleUpdate} title='更新独家主播' onCancel={this.handleCancelUpdate} onOk={this.handleSubmitUpdate} okText='提交'>
          <Form {...formItemLayout} ref={this.saveFormRefUpdate} onFinish={this.onFinishUpdate}>
            <Form.Item label='主播uid' name='uid' rules={[{ required: true }]}>
              <Input style={{ width: 200 }} />
            </Form.Item>
            <Form.Item label='签约时间' name='contractDate' rules={[{ required: true }]}>
              <RangePicker format={'YYYY-MM-DD'} />
            </Form.Item>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default BabyExcellentCompere
