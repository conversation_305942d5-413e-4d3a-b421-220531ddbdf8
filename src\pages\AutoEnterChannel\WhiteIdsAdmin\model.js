import { getAdmin, getCompere, getWhiteIds, getBlackIds, addCompere, addWhiteIds, addBlackIds, removeCompere, removeWhiteIds, removeBlackIds, updateAdminOpen, updateCompere, updateWhiteIds, updateBlackIds } from './api'
import { message } from 'antd'

export default {
  namespace: 'whiteIdsAdmin', // 只有这里需要修改

  state: {
    list: []
  },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      console.log('getList payload:', payload)
      let uri = getAdmin
      if (payload.tabType === '1') {
        uri = getCompere
      } else if (payload.tabType === '2') {
        uri = getWhiteIds
      } else if (payload.tabType === '3') {
        uri = getBlackIds
      }
      const { data: { list } } = yield call(uri, payload)

      yield put({
        type: 'updateList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * addItem ({ payload }, { call, put }) {
      console.log('addItem:', payload)
      let uri = addCompere
      if (payload.tabType === '1') {
        uri = addCompere
      } else if (payload.tabType === '2') {
        uri = addWhiteIds
      } else if (payload.tabType === '3') {
        uri = addBlackIds
      }
      const { data: { status, msg } } = yield call(uri, payload)
      if (status === 0) {
        message.success('add success')
        yield put({
          type: 'getList',
          payload: { tabType: payload.tabType }
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * updateItem ({ payload }, { call, put }) {
      let uri = updateAdminOpen
      if (payload.tabType === '1') {
        uri = updateCompere
      } else if (payload.tabType === '2') {
        uri = updateWhiteIds
      } else if (payload.tabType === '3') {
        uri = updateBlackIds
      }
      const { data: { status, msg } } = yield call(uri, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getList',
          payload: { tabType: payload.tabType }
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * removeItem ({ payload }, { call, put }) {
      let uri = removeCompere
      if (payload.tabType === '1') {
        uri = removeCompere
      } else if (payload.tabType === '2') {
        uri = removeWhiteIds
      } else if (payload.tabType === '3') {
        uri = removeBlackIds
      }
      const { data: { status, msg } } = yield call(uri, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getList',
          payload: { tabType: payload.tabType }
        })
      } else {
        message.error('failed' + msg)
      }
    }
  }
}
