import React, { Component } from 'react'
import { Upload, Modal, Popover, message } from 'antd'
import { PlusOutlined } from '@ant-design/icons'

class GuildInfoManageImage extends Component {
  constructor (props) {
    super(props)

    this.state = {
      fileList: props.value ? [urlValue(props.value)] : [],
      previewImage: '',
      previewVisible: false
    }
  }

  componentWillReceiveProps (nextProps) {
    // 受控组件， 用于更新
    const { value } = nextProps
    this.setState({
      fileList: value ? [urlValue(value)] : []
    })
  }

  beforeUpload (file) {
    var ret = true
    if (file.type !== 'image/png' && file.type !== 'image/jpeg') {
      ret = false
      message.error(`${file.name} 不支持文档类型`)
    }

    const isLt7M = file.size < 7 * 1024 * 1024
    if (!isLt7M) {
      ret = false
      message.error('文件要小于7M')
    }
    return ret === true ? true : Upload.LIST_IGNORE
  }

  handlePreview = file => {
    this.setState({
      previewImage: file.url || file.thumbUrl,
      previewVisible: true
    })
  }

  handleCancel = () => this.setState({ previewVisible: false })

  handleChange = ({ fileList }) => {
    if (fileList.length === 0) {
      this.props.onChange()
    }

    if (fileList.length > 0 && fileList[fileList.length - 1].status === 'done' && fileList[fileList.length - 1].response.status === 0) {
      var url = fileList[fileList.length - 1].response.urls[0]
      // let startIndex = String(url).lastIndexOf('/')
      // let endIndex = String(url).lastIndexOf('?token')
      // let filename = String(url).substring(startIndex + 1, endIndex)
      this.props.onChange(url) // update getFiledDecorator
    }
    this.setState({ fileList })
  }

  renderImage = () => {
    const { value } = this.props
    const content = (
      <div>
        <img src={value} style={{ maxHeight: 200, maxWidth: 200 }} />
      </div>
    )
    return (
      <div>
        <Popover placement='right' content={content} title={null}>
          <img width='102' height='102' src={value} />
        </Popover>
      </div>
    )
  }

  render () {
    const { previewVisible, previewImage, fileList } = this.state
    const uploadButton = (
      <div>
        <PlusOutlined />
        <div className='ant-upload-text'>Upload</div>
      </div>
    )

    return (
      <div className='clearfix'>
        <Upload
          action='https://fts-test.yy.com/fs/uploadfiles'
          listType='picture-card'
          fileList={fileList}
          beforeUpload={this.beforeUpload}
          onPreview={this.handlePreview}
          onChange={this.handleChange}
          // data={file => ({ bucket: 'makefriends', files: file, urlKeepFileName: 'true' })}
          data={file => ({ bucket: 'jyesign', files: file, urlKeepFileName: 'true', addToken: true })}
          accept='image/png, image/jpeg'
        >
          {fileList.length > 0 ? null : uploadButton}
        </Upload>
        <Modal visible={previewVisible} footer={null} onCancel={this.handleCancel}>
          <img alt='example' style={{ width: '100%' }} src={previewImage} />
        </Modal>
      </div>
    )
  }
}

function urlValue (url) {
  return { uid: -1, status: 'done', url: url, thumbUrl: url }
}

export default GuildInfoManageImage
