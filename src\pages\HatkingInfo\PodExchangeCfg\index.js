/* eslint-disable eqeqeq */
import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Typography, Divider, Table, Row, Col, Input, message, Button, Descriptions } from 'antd'
import { timeFormater, checkIsNumber } from '@/utils/common'

const namespace = 'podExchangeCfg'

@connect(({ podExchangeCfg }) => ({
  model: podExchangeCfg
}))

class PodExchangeCfg extends Component {
  state = {
    editingRecord: null,
    podLimitValue: 0,
    podLimitValueOld: 0,
    exchangeVirtOrTeamLimitValue: 0,
    exchangeVirtOrTeamLimitValueOld: 0,
    podLimitEditable: false
  }
  componentDidMount = () => {
    this.callModel('queryAccessCfg')
    this.callModel('queryJadeCfg')
    this.queryPodCfg()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // 更新豆荚限制配置
  queryPodCfg = () => {
    this.callModel('queryPodCfg', {
      cbFunc: (v, v1) => {
        this.setState({
          podLimitValue: v,
          podLimitValueOld: v,
          exchangeVirtOrTeamLimitValue: v1,
          exchangeVirtOrTeamLimitValueOld: v1
        })
      }
    })
  }
  // 生成table中的编辑框
  createTableCollEditer = (rawValue, record, dataIndex) => {
    let { editingRecord } = this.state
    if (editingRecord == null || editingRecord.key != record.key) {
      return rawValue
    }
    return <Input width='15em' defaultValue={editingRecord[dataIndex]} onChange={(e) => { editingRecord[dataIndex] = e.target.value; this.setState({ editingRecord: editingRecord }) }} />
  }
  // 生成table中的编辑框
  createTableCollEditer1 = (rawValue, record, dataIndex) => {
    let { editingRecord1 } = this.state
    if (editingRecord1 == null || editingRecord1.key != record.key) {
      return rawValue
    }
    return <Input width='15em' defaultValue={editingRecord1[dataIndex]} onChange={(e) => { editingRecord1[dataIndex] = e.target.value; this.setState({ editingRecord1: editingRecord1 }) }} />
  }
  // 提交入口限制修改请求
  onSubmitAccessCfg = (params) => {
    const { editingRecord: { id, yyLevelLimit, beanLimit } } = this.state

    if (!checkIsNumber(yyLevelLimit) || !checkIsNumber(beanLimit)) {
      message.warn('输入格式有误，请检查：' + yyLevelLimit + ',' + beanLimit)
      return
    }
    this.callModel('submitAccessCfg', {
      params: { id: id, yyLevelLimit: yyLevelLimit, beanLimit: beanLimit },
      cbFunc: (success) => {
        if (success) {
          message.success('已提交Boss审核，请等待管理员审批', 10)
          this.callModel('queryAccessCfg')
          this.setState({ editingRecord: null })
        } else {
          message.error('更新失败，请检查控制台')
        }
      }
    })
  }
  // 提交入口限制修改请求
  onSubmitJadeAccessCfg = (params) => {
    const { editingRecord1: { jadeLimit } } = this.state

    if (!checkIsNumber(jadeLimit)) {
      message.warn('输入格式有误，请检查：' + jadeLimit)
      return
    }
    this.callModel('submitJadeAccessCfg', {
      params: { jadeLimit: jadeLimit },
      isRawRespMode: true,
      cbFunc: (ret) => {
        // console.debug('ret===>', ret)
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('操作失败：' + msg)
          return
        }
        message.success('已提交Boss审核，请等待管理员审批', 10)
        this.callModel('queryJadeCfg')
        this.setState({ editingRecord1: null })
      }
    })
  }
  // 提交豆荚限制修改请求
  onSubmitPodLimitCfg = () => {
    const { podLimitValue, podLimitValueOld, exchangeVirtOrTeamLimitValue, exchangeVirtOrTeamLimitValueOld } = this.state
    if (!checkIsNumber(podLimitValue) || !checkIsNumber(exchangeVirtOrTeamLimitValue) || podLimitValue < -1 || exchangeVirtOrTeamLimitValue < -1) {
      message.warn('限制金额输入有误，请检查：' + podLimitValue + ',' + exchangeVirtOrTeamLimitValue)
      return
    }

    if (parseInt(exchangeVirtOrTeamLimitValue) > parseInt(podLimitValue) && parseInt(podLimitValue) != -1) {
      message.warn('兑换紫水晶券限额不能超过总限额：' + podLimitValue + ',' + exchangeVirtOrTeamLimitValue)
      return
    }

    if (podLimitValue === podLimitValueOld && exchangeVirtOrTeamLimitValue === exchangeVirtOrTeamLimitValueOld) {
      message.warn('配置没有改变')
      return
    }

    this.callModel('submitPodLimitValue', {
      params: { podLimitValue: podLimitValue, exchangeVirtOrTeamLimitValue: exchangeVirtOrTeamLimitValue },
      cbFunc: (success) => {
        if (success) {
          message.success('已提交boss审核，请等待管理员审批', 10)
          this.setState({ podLimitEditable: false })
          this.queryPodCfg()
        } else {
          message.warn('配置修改失败，请检查控制台')
        }
      }
    })
  }

  creteTableOperateHtml = (r) => {
    const { editingRecord } = this.state
    if (editingRecord == null || editingRecord.key != r.key) {
      return <a onClick={() => this.setState({ editingRecord: r })}>修改</a>
    }
    return <>
      <a style={{ marginRight: '1em' }} onClick={() => { this.onSubmitAccessCfg() }}>保存</a>
      <a onClick={() => { this.setState({ editingRecord: null }); this.callModel('queryAccessCfg') }}>取消</a>
    </>
  }

  creteTableOperateJadeHtml = (r) => {
    const { editingRecord1 } = this.state
    if (editingRecord1 == null || editingRecord1.key != r.key) {
      return <a onClick={() => this.setState({ editingRecord1: r })}>修改</a>
    }
    return <>
      <a style={{ marginRight: '1em' }} onClick={() => { this.onSubmitJadeAccessCfg() }}>保存</a>
      <a onClick={() => { this.setState({ editingRecord1: null }); this.callModel('queryJadeCfg') }}>取消</a>
    </>
  }

  render () {
    const { podLimitValue, podLimitEditable, podLimitValueOld, exchangeVirtOrTeamLimitValue, exchangeVirtOrTeamLimitValueOld } = this.state
    const { route } = this.props
    const { Title, Paragraph, Text } = Typography
    const { accessCfg } = this.props.model
    // const { jadeCfg } = this.props.model

    const columns = [
      { title: '功能名称', dataIndex: 'name' },
      { title: '商品列表可见-用户YY等级', dataIndex: 'yyLevelLimit', width: '12em', render: (v, r) => { return this.createTableCollEditer(v, r, 'yyLevelLimit') } },
      { title: '兑换布料可用玉石数量上限', dataIndex: 'beanLimit', width: '12em', render: (v, r) => { return this.createTableCollEditer(v, r, 'beanLimit') } },
      { title: '修改人', dataIndex: 'operator' },
      { title: '修改时间', dataIndex: 'timestamp', render: (v) => { return timeFormater(v) } },
      { title: '操作', render: (_, r) => { return this.creteTableOperateHtml(r) } }
    ]

    // const powerColumns = [
    //   { title: '功能名称',
    //     dataIndex: 'id',
    //     align: 'center',
    //     render: (text, record) => {
    //       switch (text) {
    //         case 0: return '玉石兑换战力票'
    //         case 1: return '玉石兑换战力票'
    //       }
    //       return ''
    //     }
    //   },
    //   { title: '兑换战力票限额', dataIndex: 'jadeLimit', width: '12em', render: (v, r) => { return this.createTableCollEditer1(v, r, 'jadeLimit') } },
    //   { title: '修改人', dataIndex: 'operator', render: (v) => { return v === '' ? '-' : v } },
    //   { title: '修改时间', dataIndex: 'timestamp', render: (v) => { return v > 0 ? timeFormater(v) : '-' } },
    //   { title: '操作', render: (_, r) => { return this.creteTableOperateJadeHtml(r) } }
    // ]

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Paragraph>
            <Title level={4}>豆荚兑换配置</Title>
            <Text type='secondary'>说明: "商品列表可见-用户YY等级"用于根据用户YY等级设置福利商店商品列表对用户的可见范围。用户等级设计为负数，表示所有用户可见; 设置为0，表示所用用户不可见； 设置大于0，表示大于该等级可见；</Text>
            <br />
            <Text type='secondary'>说明: "兑换玉石数量上限"用于根据用户每日兑换布料可使用的玉石上限。-1，表示不限制; 设置大于等于0，表示每日可使用该数值的玉石；</Text>
          </Paragraph>
          <Row>
            <Col span={12}>
              <Table columns={columns} dataSource={accessCfg} bordered pagination={false} size='small' />
            </Col>
          </Row>
          <Divider />
          <Row>
            <Col span={12} style={{ marginTop: '5px' }}>
              <Descriptions
                bordered
                title='豆荚兑换道具配置'
                size='default'
                column={{ sm: 2 }}
                extra={!podLimitEditable
                  ? <Button type='default' onClick={() => { this.setState({ podLimitEditable: true }) }}>修改</Button>
                  : <> <Button type='primary' disabled={!podLimitEditable} onClick={() => this.onSubmitPodLimitCfg()}>保存</Button>
                    <Button style={{ marginLeft: 5 }} danger onClick={() => { this.setState({ podLimitEditable: false, podLimitValue: podLimitValueOld, exchangeVirtOrTeamLimitValue: exchangeVirtOrTeamLimitValueOld }) }}>取消</Button></>
                }
              >
                <Descriptions.Item label='兑换总限额'>
                  <Input key={podLimitEditable + podLimitValueOld} disabled={!podLimitEditable} defaultValue={podLimitValue} onChange={(e) => { this.setState({ podLimitValue: e.target.value }) }} />
                </Descriptions.Item>
                <Descriptions.Item label='兑换紫水晶券限额'>
                  <Input key={podLimitEditable + exchangeVirtOrTeamLimitValueOld} disabled={!podLimitEditable} defaultValue={exchangeVirtOrTeamLimitValue} onChange={(e) => { this.setState({ exchangeVirtOrTeamLimitValue: e.target.value }) }} />
                </Descriptions.Item>
                <Descriptions.Item label='说明' span={2}>用户当日使用豆荚兑换礼物限制金额/紫水晶, 输入-1表示不限制。 (1豆荚=100紫水晶)</Descriptions.Item>
              </Descriptions>
            </Col>
          </Row>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default PodExchangeCfg
