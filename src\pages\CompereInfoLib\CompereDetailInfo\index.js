import React, { Component } from 'react'
import { Modal, Row, Col, Divider } from 'antd'
import PopImage from '@/components/PopImage'

const moment = require('moment')

const compereRoleMap = { 1: '到期解约', 2: '互动超级', 3: '普通主持', 4: '超级主持', 5: '超级天团', 6: '厅天团', 7: '准超主', 10: '星光主持' }
const identityMap = { 1: '普通个人', 2: '企业', 3: ' 工作室（工作室类型）', 4: '工作室（个体户类型）' }
const sexMap = { 0: '女', 1: '男', 2: '-' }
const yesNoMap = { 0: '-', 1: '是', 2: '否' }

function dateStringYMD (timestamp) {
  return moment.unix(timestamp).format('YYYY-MM-DD')
}

class CompereDetailInfo extends Component {
  handleCancelDetail = e => {
    this.props.cancelHandler(false)
    // this.setState({ visibleDetail: false })
  }

  render () {
    const { visibleDetail = false, record = {} } = this.props

    return (
      <Modal footer={null} forceRender width={900} visible={visibleDetail} title='主持详细信息' onCancel={this.handleCancelDetail}>
        <div><font style={{ marginLeft: 280, fontSize: '35px' }}>主持详细信息</font></div>
        <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px' }}>基本信息</font></div>
        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>UID:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.uid : ''}</font>
          </Col>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>YY号:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.yy : '' }</font>
          </Col>
        </Row>

        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>昵称:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.nick : ''}</font>
          </Col>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>短位ID:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.asid : '' }</font>
          </Col>
        </Row>

        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>主持身份:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? compereRoleMap[record.compereRole] : ''}</font>
          </Col>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>是否有特权:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? yesNoMap[record.hadPrivileged] : ''}</font>
          </Col>
        </Row>

        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>公会抽成比例:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.guildWeight : '' }</font>
          </Col>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>签约起始时间:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? dateStringYMD(record.signStartTime) + '至' + dateStringYMD(record.signEndTime) : ''}</font>
          </Col>
        </Row>

        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>绑定房管UID:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined && record.roomMgrUid > 0 ? record.roomMgrUid : '' }</font>
          </Col>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>绑定SSID:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined && record.roomMgrSsid > 0 ? record.roomMgrSsid : ''}</font>
          </Col>
        </Row>
        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>绑定厅名:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.roomMgrRoomName : '' }</font>
          </Col>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>绑定SSID时间:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined && record.roomMgrBindTime > 0 ? dateStringYMD(record.roomMgrBindTime) : ''}</font>
          </Col>
        </Row>

        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>主持等级:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.compereLevel : ''}</font>
          </Col>
        </Row>
        <Divider />

        <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px' }}>实名信息</font></div>
        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>真实姓名:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.realName : ''}</font>
          </Col>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>手机号:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.phone : '' }</font>
          </Col>
        </Row>

        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>身份证:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.idCard : ''}</font>
          </Col>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>实名性别:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? sexMap[record.realSex] : '' }</font>
          </Col>
        </Row>
        <Divider />

        <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px' }}>财务结算信息</font></div>
        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>结算身份:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? identityMap[record.identity] : '-'}</font>
          </Col>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>是否半月提:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined && record.settlementType === 3 ? yesNoMap[1] : '否' }</font>
          </Col>
        </Row>

        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>是否对公:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? yesNoMap[record.isPublic] : '-'}</font>
          </Col>
        </Row>
        <Divider />

        <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px' }}>独家主持信息</font></div>
        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>联系地址:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.address : ''}</font>
          </Col>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>电子邮箱:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.email : '' }</font>
          </Col>
        </Row>

        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>微信号:</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.wechat : ''}</font>
          </Col>
          <Col span={12}>
            <font style={{ fontSize: '16px' }}><b>昵称(艺名):</b>&nbsp;</font><font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.artNick : '' }</font>
          </Col>
        </Row>
        <Divider />

        <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px' }}>独家签约认证</font></div>
        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ marginLeft: 10, fontSize: '16px' }}><b>刷脸认证:</b>&nbsp;</font>
            <PopImage value={record !== null && record !== undefined ? record.faceImage : ''} />
          </Col>
          <Col span={12}>
            <font style={{ marginLeft: 10, fontSize: '16px' }}><b>开播截图:</b>&nbsp;</font>
            <PopImage value={record !== null && record !== undefined ? record.screenShot : ''} />
          </Col>
        </Row>

        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ marginLeft: 10, fontSize: '16px' }}><b>身份证正面:</b>&nbsp;</font>
            <PopImage value={record !== null && record !== undefined ? record.idCardFront : ''} />
          </Col>
          <Col span={12}>
            <font style={{ marginLeft: 10, fontSize: '16px' }}><b>身份证反面:</b>&nbsp;</font>
            <PopImage value={record !== null && record !== undefined ? record.idCardBack : ''} />
          </Col>
        </Row>

        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ marginLeft: 10, fontSize: '16px' }}><b>手持身份证照:</b>&nbsp;</font>
            <PopImage value={record !== null && record !== undefined ? record.holdFront : ''} />
          </Col>
          <Col span={12}>
            <font style={{ marginLeft: 10, fontSize: '16px' }}><b>签约视频:</b>&nbsp;</font>
            <a target='_black' href={record !== null && record !== undefined && record.signVideo !== '' ? record.signVideo : ''}>{record !== null && record !== undefined && record.signVideo !== '' ? '签约视频.mp4' : ''}</a>
          </Col>
        </Row>
        <Divider />

        <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px' }}>独家合同信息</font></div>
        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ marginLeft: 10, fontSize: '16px' }}><b>两方合同编号:</b>&nbsp;</font>
            <font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.twoContractNo : '' }</font>
          </Col>
          <Col span={12}>
            <font style={{ marginLeft: 10, fontSize: '16px' }}><b>三方合同编号:</b>&nbsp;</font>
            <font style={{ fontSize: '16px' }}>{record !== null && record !== undefined ? record.threeContractNo : '' }</font>
          </Col>
        </Row>

        <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
          <Col span={12}>
            <font style={{ marginLeft: 10, fontSize: '16px' }}><b>两方合约:</b>&nbsp;</font>
            <a target='_black' href={record !== null && record !== undefined ? record.twoContract : ''}>{record !== null && record !== undefined && record.twoContract !== '' ? '查看' : ''}</a>
          </Col>
          <Col span={12}>
            <font style={{ marginLeft: 10, fontSize: '16px' }}><b>三方合约:</b>&nbsp;</font>
            <a target='_black' href={record !== null && record !== undefined ? record.threeContract : ''}>{record !== null && record !== undefined && record.threeContract !== '' ? '查看' : ''}</a>
          </Col>
        </Row>
      </Modal>
    )
  }
}

export default CompereDetailInfo
