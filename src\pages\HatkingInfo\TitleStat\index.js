import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card } from 'antd'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
// import UserStat from './component/userStat'
import CurrentTitle from './component/currentTitle'

@connect(({ hatkingInfoTitleInfo }) => ({
  model: hatkingInfoTitleInfo
}))

class HeroDailyStat extends Component {
  state = {}
  componentDidMount = () => {}
  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs id='tts' defaultActiveKey='2' type='card' >
            <TabPane tab='勇士达人等级' key='2'>
              <CurrentTitle />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default HeroDailyStat
