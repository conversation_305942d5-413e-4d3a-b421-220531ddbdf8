import { message } from 'antd'
import { addOneConfigInfo, deleteOneConfigInfo, getAllConfigLists, getAllConfirmLists, getOneConfigLists, updateAllConfigInfo, updateOneConfigInfo, getSinglePlayerLists, updateSinglePlayerInfo } from './api'

export default {
  namespace: 'magicBeanControl',

  state: {
    oneConfigList: [], // 单频道限制
    allConfigList: [], // 全频道限制
    allConfirmList: [], // 全频道审批
    singlePlayerList: [] // 用户参与限制
  },

  reducers: {
    updateOneConfigList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        oneConfigList: payload
      }
    },

    updateAllConfigList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        allConfigList: payload
      }
    },

    updateAllConfirmList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        allConfirmList: payload
      }
    },

    updateSinglePlayerList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        singlePlayerList: payload
      }
    }
  },

  effects: {
    * getOneConfigList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getOneConfigLists, payload)

      yield put({
        type: 'updateOneConfigList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * getAllConfigList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getAllConfigLists, payload)

      yield put({
        type: 'updateAllConfigList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * getSinglePlayerList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getSinglePlayerLists, payload)

      yield put({
        type: 'updateSinglePlayerList',
        payload: Array.isArray(list) ? list : []
      })
    },
    * getAllConfirmList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getAllConfirmLists, payload)

      yield put({
        type: 'updateAllConfirmList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * addOneConfigItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(addOneConfigInfo, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getOneConfigLists'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * updateOneConfigItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updateOneConfigInfo, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getOneConfigLists'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * deleteOneConfigItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(deleteOneConfigInfo, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getOneConfigLists'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * updateAllConfigItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updateAllConfigInfo, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getAllConfigLists'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * updateSinglePlayerItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(updateSinglePlayerInfo, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getSinglePlayerLists'
        })
      } else {
        message.error('failed' + msg)
      }
    }
  }
}
