import { connect } from 'dva'
import React, { Component } from 'react'
import { Row, Col, Table, Typography } from 'antd'
import { timeFormater } from '@/utils/common'
import SearchParams from '@/components/SimpleComponents/searchParams'

const namespace = 'hgameTingTools'

const { Text } = Typography

@connect(({ hgameTingTools }) => ({
  model: hgameTingTools
}))

class EventLogs extends Component {
  state = {
    paramsVal: {}
  }

  // 查询条件
  parmasColumn = [
    { label: '数量限制', fieldName: 'limit', inputType: 'Selecter', defaultValue: 100, extProps: { style: { width: '7em' }, options: [50, 100, 200, 500, 1000].map(item => { return { label: item, value: item } }) } },
    { label: '活动ID', fieldName: 'actId', inputType: 'Input', defaultValue: '' },
    { label: 'SID', fieldName: 'sid', inputType: 'InputNumber', defaultValue: null },
    { label: 'UID', fieldName: 'uid', inputType: 'InputNumber', defaultValue: null },
    { label: '查询', inputType: 'Button', extProps: { onClick: () => { this.getEventLogList(this.state.paramsVal) } } }
  ]

  componentDidMount = () => {
    this.getEventLogList(this.state.paramsVal)
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  getEventLogList = (params) => {
    console.debug('params==>', params)
    this.callModel('getEventLogList', { params: {
      limit: params.limit || 100,
      actId: params.actId || '',
      sid: params.sid || 0,
      uid: params.uid || 0
    } })
  }

  render () {
    const { eventLogs } = this.props.model

    const columns = [
      { title: '时间', dataIndex: 'timestamp', render: (v, r) => { return timeFormater(v) } },
      { title: '活动id', dataIndex: 'actId' },
      { title: '相关sid', dataIndex: 'sid' },
      { title: '相关uid', dataIndex: 'uid' },
      { title: '事件', dataIndex: 'desc', width: '40em', render: (v) => { return <div style={{ textAlign: 'center', maxWidth: '40em' }}><Text type='secondary' >{v}</Text></div> } }
    ].map((item) => {
      item.align = 'center'
      return item
    })

    return (
      <Row>
        <Col>
          <SearchParams formatTimeRange columns={this.parmasColumn} onChange={(v) => { this.setState({ paramsVal: v }) }} />
        </Col>

        <Col span={24}>
          <Table columns={columns} dataSource={eventLogs} size='small' pagination={{ pageSize: 50 }} />
        </Col>

      </Row>
    )
  }
}

export default EventLogs
