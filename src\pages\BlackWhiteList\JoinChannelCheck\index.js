import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs, message, Card } from 'antd'
import JoinChannelCheckRuleList from './TabPage/table'
import Join<PERSON>hanneleRuleTest from './TabPage/ruleTest'

const namespace = 'joinChannelCheck'

@connect(({ joinChannelCheck }) => ({
  model: joinChannelCheck
}))

class JoinChannelCheck extends Component {
  state = {
    title: 'SUCCESS'
  }
  componentDidMount = () => {
    this.callModel('getPlayModeList')
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }
  // update示例
  updateConcat = (f, opType) => {
    this.callModel('addOrUpdateActive', {
      params: { uid: f.uid },
      cbFunc: (ok) => {
        if (ok) message.info('操作成功～')
      }
    })
  }

  render () {
    const { route } = this.props
    const { TabPane } = Tabs

    return (
      <PageHeaderWrapper title={route.name} >
        <Card>
          <Tabs defaultActiveKey='1' type='card' size='middle'>
            <TabPane tab='规则列表' key='1'>
              <JoinChannelCheckRuleList />
            </TabPane>
            <TabPane tab='效果测试' key='2'>
              <JoinChanneleRuleTest />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default JoinChannelCheck
