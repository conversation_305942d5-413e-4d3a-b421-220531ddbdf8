import React, { Component } from 'react'
import dateString from '@/utils/dateString'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import PicturesWall from '@/components/PicturesWall'
import PopImage from '@/components/PopImage'
import { Table, Divider, Button, Form, Card, Popconfirm, Input, Modal, DatePicker, Select } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const namespace = 'quarterlyActManager'
const getListUri = `${namespace}/getList`
const addItemUri = `${namespace}/addItem`
const updateItemUri = `${namespace}/updateItem`
const removeItemUri = `${namespace}/removeItem`
const FormItem = Form.Item
const { Option } = Select

@connect(({ quarterlyActManager }) => ({
  model: quarterlyActManager
}))
class QuarterlyActManager extends Component {
  // 列表结构
  columns = [
    { title: '编号', dataIndex: 'seqId', align: 'center' },
    { title: '年份', dataIndex: 'oweYear', align: 'center' },
    { title: '季度', dataIndex: 'oweQuarter', align: 'center' },
    { title: '背景图', dataIndex: 'actImage', align: 'center', render: text => <PopImage value={text} /> },
    { title: '开始时间', dataIndex: 'actStart', align: 'center', render: text => dateString(text) },
    { title: '结束时间', dataIndex: 'actStop', align: 'center', render: text => dateString(text) },
    { title: '操作',
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(true, record)}>更新</a><Divider type='vertical' />
          <Popconfirm title='确认删除?' onConfirm={this.handleDel(record.seqId)}>
            <a href=''>删除</a>
          </Popconfirm>
        </span>)
    }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {} }

  // 获取列表
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: getListUri
    })
  }

  // 添加 与 编辑
  handleSubmit = () => {
    const form = this.formRef.props.form
    const { dispatch } = this.props
    form.validateFields((err, values) => {
      if (!err) {
        values.actStart = values.actStart.unix()
        values.actStop = values.actStop.unix()
        var url = this.state.isUpdate ? updateItemUri : addItemUri
        console.log('submit:', values)
        dispatch({
          type: url,
          payload: values
        })
        form.resetFields()
        this.setState({ visible: false })
      }
    })
  }

  // 删除
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { seqId: key }
    dispatch({
      type: removeItemUri,
      payload: data
    })
  }

  // 显示弹窗
  showModal = (isUpdate, record) => e => {
    var now = moment().unix()
    if (record == null) record = { actStart: now, actStop: now, oweYear: now }
    this.setState({ visible: true, value: record, isUpdate: isUpdate, title: isUpdate ? '更新活动信息' : '添加活动信息' })
  }

  // 关闭弹窗
  hidModal = () => {
    this.setState({ visible: false })
  }

  saveFormRef = formRef => {
    this.formRef = formRef
  }

  render () {
    const { route, model: { list } } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Button type='primary' onClick={this.showModal(false)}>添加</Button>
            <Divider />
            <Table dataSource={list} columns={this.columns} rowKey={(record, index) => index} pagination={this.defaultPageValue} size='small' />
          </Form>
        </Card>
        <ItemInfo wrappedComponentRef={this.saveFormRef} {...this.state} onCancel={this.hidModal} onSubmit={this.handleSubmit} />
      </PageHeaderWrapper>
    )
  }
}

@connect(({ QuarterlyActManager }) => ({
  model: QuarterlyActManager
}))
@Form.create()
class ItemInfo extends Component {
  render () {
    const { value, visible, title, onCancel, onSubmit, form } = this.props
    const { getFieldDecorator } = form
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <Modal visible={visible} title={title} onCancel={onCancel} onOk={onSubmit}>
        <Form >
          <FormItem {...formItemLayout} label='年份'>
            {getFieldDecorator('oweYear', {
              rules: [{ required: true, pattern: '^[12][0-9]{3}$', message: '年份格式不正确(1000-2999)' }]
            })(<Input />)}
          </FormItem>
          <FormItem {...formItemLayout} label='季度'>
            {getFieldDecorator('oweQuarter', {
              initialValue: value.oweQuarter,
              rules: [{ required: true, message: '季度不能为空' }]
            })(<Select initialValue='1'>
              <Option value='1'>1季度</Option>
              <Option value='2'>2季度</Option>
              <Option value='3'>3季度</Option>
              <Option value='4'>4季度</Option>
            </Select>)}
          </FormItem>
          <FormItem {...formItemLayout} label='开始时间'>
            {getFieldDecorator('actStart', {
              initialValue: moment.unix(value.actStart),
              rules: [{ required: true, message: '开始时间不能为空' }]
            })(<DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' style={{ width: '100%' }} />)}
          </FormItem>
          <FormItem {...formItemLayout} label='结束时间'>
            {getFieldDecorator('actStop', {
              initialValue: moment.unix(value.actStop),
              rules: [{ required: true, message: '结束时间不能为空' }]
            })(<DatePicker showTime='true' format='YYYY-MM-DD HH:mm:ss' style={{ width: '100%' }} />)}
          </FormItem>
          <FormItem {...formItemLayout} label='背景图'>
            {getFieldDecorator('actImage', {
              initialValue: value.actImage,
              rules: [{ required: true, message: '活动背景不能为空' }]
            })(<PicturesWall />)}
          </FormItem>
          <FormItem>
            {getFieldDecorator('seqId', {
              initialValue: value.seqId
            })(<Input hidden />)}
          </FormItem>
        </Form>
      </Modal>
    )
  }
}

export default QuarterlyActManager
