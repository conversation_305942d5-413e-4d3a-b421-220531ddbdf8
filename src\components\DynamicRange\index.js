import React, { Component } from 'react'
import { DatePicker } from 'antd'
import { DeleteOutlined } from '@ant-design/icons'

const RangePicker = DatePicker.RangePicker

class DynamicRange extends Component {
  constructor (props) {
    super(props)

    this.state = {
      list: props.value || [],
      item: null
    }
  }

  componentWillReceiveProps (nextProps) {
    // 受控组件， 用于更新
    const { value } = nextProps
    this.setState({
      list: value || [],
      item: null
    })
  }

  handleOnChange = (value) => {
    const { list } = this.state
    Array.push(list, value.map(i => i.format('YYYY-MM-DD HH:mm:ss')).join('~'))

    console.log(list, value)
    if (this.props.onChange) {
      this.props.onChange(list)
    }

    this.setState({ list })
  }

  handleChange = (index) => () => {
    // console.log(index)
    var { list } = this.state
    if (index !== undefined) {
      Array.splice(list, index, 1)
    }

    if (this.props.onChange) {
      this.props.onChange(list) // update getFiledDecorator
    }

    // console.log(list)
    this.setState({ list: list, item: null })
  }

  render () {
    const { list, item } = this.state
    return (
      <div className='clearfix'>
        {list.map((i, index) => {
          return (
            <div key={index}>
              <span>{i}</span>
              <span style={{ marginLeft: 10 }}><a onClick={this.handleChange(index)} ><DeleteOutlined style={{ color: 'red' }} /></a></span>
            </div>
          )
        })}
        <div>
          <RangePicker value={item} defaultValue={null} allowClear showTime showToday onChange={this.handleOnChange} style={{ width: '100%' }} />
        </div>
      </div>
    )
  }
}

export default DynamicRange
