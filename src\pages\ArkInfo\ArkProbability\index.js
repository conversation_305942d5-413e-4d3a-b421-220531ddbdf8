import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { connect } from 'dva'
/* eslint-disable eqeqeq */
import { Card, Divider, Form, message, Button, Row, Col, InputNumber, Typography, Input, Table, Modal, Spin } from 'antd'
import { timeFormater, deepClone, getCookie } from '@/utils/common'
import DiffTable from '@/components/DiffTable'
import { screenshotByID } from '@/utils/screenShot'

const { TextArea } = Input
const namespace = 'arkProbabilitySetting'

// 概率配置数据长度对其
export function fixDataSource (before = [], after = []) {
  let allID = [] // 奖励ID去重升序
  let oldTotal = 0
  before.map(item => {
    oldTotal += item.probability
    if (item.entityId && allID.indexOf(item.entityId) < 0) {
      allID.push(item.entityId)
    }
  })
  let newTotal = 0
  after.map(item => {
    newTotal += item.probability
    if (item.entityId && allID.indexOf(item.entityId) < 0) {
      allID.push(item.entityId)
    }
  })
  allID = allID.sort((a, b) => { return a > b ? 1 : -1 })
  let newBefore = []
  let newAfter = []
  let mergedList = []
  for (let i = 0; i < allID.length; i++) {
    const id = allID[i]
    const defaultItem = {}
    const itemA = after.find(item => item.entityId === id) || defaultItem
    const itemB = before.find(item => item.entityId === id) || defaultItem
    itemA.probabilityRatio = (itemA.probability / newTotal * 100).toFixed(2) + '%'
    itemB.probabilityRatio = (itemB.probability / oldTotal * 100).toFixed(2) + '%'
    let mergedItem = { entityId: id }
    mergedItem.oldRatio = itemB.probabilityRatio
    mergedItem.newRatio = itemA.probabilityRatio
    newAfter.push(itemA)
    newBefore.push(itemB)
    mergedList.push(mergedItem)
  }
  return {
    listB: newBefore,
    listA: newAfter,
    listMerged: mergedList
  }
}

@connect(({ arkProbabilitySetting }) => ({
  model: arkProbabilitySetting
}))

class ArkProbabilitySetting extends Component {
  genTitle = (title) => {
    return <span>
      <font color='red'>{title}</font>
    </span>
  }

  columns = [
    { title: '编号', align: 'center', dataIndex: 'entityId' },
    { title: '怪物', align: 'center', dataIndex: 'monster' },
    { title: '武器', align: 'center', dataIndex: 'weaponry' },
    { title: '成功防守比例', align: 'center', dataIndex: 'entityOdds' },
    { title: '普通概率', align: 'center', dataIndex: 'probability', render: (v) => { return v }, dft_getText: (v) => { return v } },
    { title: '实际概率', align: 'center', dataIndex: 'probabilityRatio', render: (v, r) => { return v }, dft_getText: (v, r) => { return v } }
  ]

  confirmColumns = [
    { title: '编号', align: 'center', dataIndex: 'entityId' },
    { title: '原概率', align: 'center', dataIndex: 'oldRatio' },
    { title: this.genTitle('调整后概率'), align: 'center', dataIndex: 'newRatio', render: (v) => { return this.genTitle(v) } }
  ]

  state = {}

  componentDidMount = () => {
    this.getEditingPoolList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

defaultEditing = { timestamp: 0, hashCode: '', status: 0, operator: 0, passport: '', aprPassport: '', aprRemark: '', aprTimestamp: 0, aprId: 0, list: [] }

// 注意这个页面放在这里是方便一些代码复用，实际展示的位置是在 /DropRefactor/PoolApproval
  state = {
    pid: 1000, // 默认道具数额汇总ID
    visible: false,
    editing: false,
    approvalVisible: false,
    approvalRemark: '',
    updateRemark: '',
    updateComfirmVisible: false,
    screenShot: '',
    dataSourceUpdateCounter: 0,
    spanning: false
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  /** ************************************组件初始化**************************************************/

  // 刷新编辑成功池
  getEditingPoolList = (id = null) => {
    const { pid } = this.state
    const { dataSourceUpdateCounter } = this.state
    this.callModel('getEditingPool', {
      id: id || pid,
      cbFunc: () => {
        this.setState({ dataSourceUpdateCounter: dataSourceUpdateCounter + 1 })
      }
    })
  }

  // 获取待审批信息
  getApprovalInfo = (pid) => {
    if (pid === '') {
      message.warn('未创建审批流')
      return
    }
    this.callModel('getToApproval', {
      params: { pid: pid },
      cbFunc: (list) => {
        if (!Array.isArray(list)) {
          message.warn('当前非待审批状态~')
          return
        }
        let info = list[0]
        if (info == undefined || info.rule == undefined || info.rule.approvals == undefined) {
          message.warn('审批流信息异常，请稍后再试..')
          return
        }
        let uid = getCookie('yyuid')
        const { progress, rule } = info
        const aprList = progress === 0 ? rule.approvals : rule.secondary
        for (let i = 0; i < aprList.length; i++) {
          if (aprList[i].uid == uid) {
            this.setState({ approvalVisible: true })
            return
          }
        }
        message.warn('非指定审批人')
      }
    })
  }

  doApproval = (pass) => {
    const { toApproval } = this.props.model
    const { approvalRemark } = this.state
    let req = toApproval[0]
    if (req == undefined || req.rule == undefined || req.rule.approvals == undefined) {
      message.warn('审批流信息异常，请稍后再试..')
      return
    }
    req.reason = approvalRemark || '无'
    if (pass) {
      req.result = 'Passed'
    } else {
      req.result = 'Rejected'
    }
    this.callModel('doApproval', {
      params: req,
      cbFunc: (ok, msg) => {
        if (ok) {
          message.success('审批完成')
        } else {
          message.error(msg)
        }
        this.setState({ approvalVisible: false })
        this.getEditingPoolList()
      }
    })
  }
  /** ************************************完成道具数额汇总编辑，提交到服务器************************************************ */

  // 检查配置合法性然后弹出提交审批确认框
  beforeSubmitEditing = () => {
    const { editingConfig } = this.props.model

    let allowSubmit = 1
    editingConfig.list.map(item => {
      if (item['probability'] === undefined || item['probability'] === null ||
        item['probability'] === '' || parseInt(item['probability']) <= 0) {
        allowSubmit = 0
      }
      return item
    })

    if (allowSubmit === 0) {
      message.error('普通概率需要大于0')
      return
    }

    this.setState({ editing: false, spanning: true })
    setTimeout(() => {
      screenshotByID('poolTable', (isOk, url) => {
        if (!isOk) {
          message.warn('截图失败~')
        }
        console.debug('url===>', url)
        this.setState({ updateComfirmVisible: true, screenShot: url, spanning: false })
      })
    }, 200)
  }

  onSubmit = () => {
    const { updateRemark, screenShot } = this.state
    const { editingConfig, poolConfig } = this.props.model
    if (!updateRemark) {
      message.warn('请输入备注信息')
      return
    }

    let newList = editingConfig.list.map(item => {
      delete item['probabilityRatio']
      return item
    })

    editingConfig.content = JSON.stringify(newList)
    editingConfig.remark = updateRemark
    delete editingConfig['list']
    editingConfig.screenShot = screenShot

    poolConfig.temporary = editingConfig

    this.callModel('editPool', {
      params: poolConfig,
      cbFunc: (ok) => {
        if (ok) {
          message.success('已提交审批')
          this.setState({ editing: false, updateComfirmVisible: false })
          this.getEditingPoolList()
        } else {
          message.warn('发生错误，请联系管理员...')
        }
      }
    })
  }

  // 放弃更新
  onReset = () => {
    this.getEditingPoolList()
    this.editChange()
  }

  editChange = () => {
    const { model: { poolConfig } } = this.props
    if (poolConfig === undefined || poolConfig.id <= 0) {
      message.error('请选择配置')
      return
    }

    const { editing } = this.state
    this.setState({ editing: !editing })
  }

  /** **********************************table 渲染******************************************************************/

  onInputChange = (row, field) => value => {
    if (value === undefined || value === null ||
      parseInt(value) <= 0) {
      message.error('普通概率需要大于0')
      return
    }

    let { editingConfig } = this.props.model
    let v = $.extend([], true, editingConfig.list)
    let editList = $.extend([], true, editingConfig.list)
    let oldValue = v[row][field]
    v[row][field] = value
    editingConfig.list = v
    console.log('==========onInputChange', oldValue, value)
    this.changeState('editingConfig', editingConfig)
    this.changeState('editList', editList)
  }

  renderEditColumn = () => {
    const { editing } = this.state
    if (!editing) {
      return this.columns
    }

    let renderColumns = []
    let cp = deepClone(this.columns)
    for (let i = 0; i < cp.length; i++) {
      let column = cp[i]
      if (['probability'].indexOf(column.dataIndex) > -1) {
        column.render = (text, record, index) => {
          return <InputNumber style={{ width: '20%' }} onChange={this.onInputChange(index, column.dataIndex)} defaultValue={text} min={1} />
        }
      }

      renderColumns.push(column)
    }

    return renderColumns
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { route } = this.props
    const { editing, approvalVisible, updateComfirmVisible, dataSourceUpdateCounter, spanning } = this.state
    const { editingConfig, prodList, editingConfig: { list } } = this.props.model
    const { timestamp, passport, status, aprId, aprUid, aprPassport, aprTimestamp, remark, aprRemark } = editingConfig || this.defaultEditing
    const { Text } = Typography
    const fixList = fixDataSource(prodList, list)
    const { listA, listB, listMerged } = fixList // listA=当前待审批配置, listB=正式生效配置 listMerged=新旧对比

    return (
      <PageHeaderWrapper title={route.name} >
        <Card>
          <Form.Item>
            <div hidden={!editing}>
              <Button onClick={this.onReset} style={{ marginRight: 20 }}>放弃修改</Button>
              <Button type='dash' danger onClick={() => { this.beforeSubmitEditing() }}>提交修改</Button>
            </div>
            <div hidden={editing}>
              <Button type='primary' onClick={this.editChange}>编辑</Button>
              <Button style={{ marginLeft: 20 }} type='primary' disabled={aprId === '' || status != 1} onClick={() => this.getApprovalInfo(aprId)}>审批</Button>
            </div>
            {spanning ? <Spin /> : ''}
          </Form.Item>
          <Form.Item>
            <Row>
              <Col>
                <Row>提交：<Text type='secondary'> {`${passport}_(${timeFormater(timestamp)})`} <Divider type='vertical' /> </Text></Row>
                <Row hidden={status === 1}>审批：<Text type='secondary'> {aprUid === 0 ? '系统' : aprPassport}_({timeFormater(aprTimestamp)}) <Divider type='vertical' /></Text></Row>
              </Col>
              <Col>
                <Row>申请理由：<Text type='secondary'> {remark || '(空)'} <Divider type='vertical' /></Text></Row>
                <Row hidden={status === 1}>审批备注：<Text type='secondary'> {aprRemark || '(空)'} <Divider type='vertical' /></Text></Row>
              </Col>
              <Col>
                <Row>审批状态：<Text type={['warning', 'warning', 'success', 'danger'][status]}> {['未创建', '待审批', '已通过', '不通过'][status]} <Divider type='vertical' /></Text></Row>
              </Col>
            </Row>
          </Form.Item>

          <Divider />

          <div id='poolTable'>
            {
              editing
                ? <Table key={list} pagination={false} size='small' rowKey='entityId' columns={this.renderEditColumn()} dataSource={list} />
                : <DiffTable key={dataSourceUpdateCounter} oldProps={{ pagination: false, size: 'small' }} columns={this.columns} after={listA} before={listB} />
            }
          </div>

          {/* 提交修改确认模态框 */}
          <Modal visible={updateComfirmVisible} title='确认提交审批么？' onCancel={() => { this.setState({ updateComfirmVisible: false, editing: true }) }} footer={[
            <Button key='revoke' onClick={() => { this.setState({ updateComfirmVisible: false, editing: true }) }}>再看看</Button>,
            <Button key='submit' danger type='primary' onClick={() => { this.onSubmit() }}>提交审批</Button>
          ]}>
            <Table key={listMerged} rowKey='entityId' pagination={false} size='small' columns={this.confirmColumns} dataSource={listMerged} />
            <Input placeholder='请输入备注信息 (必填)' onChange={(e) => { this.setState({ updateRemark: e.target.value }) }} />
          </Modal>

          {/* 审批模态框 */}
          <Modal visible={approvalVisible} title='配置审批' onCancel={() => { this.setState({ approvalVisible: false }) }} footer={[
            <Button key='cancel' onClick={() => { this.setState({ approvalVisible: false }) }}>取消</Button>,
            <Button key='reject' danger type='primary' onClick={() => { this.doApproval(false) }}>驳回</Button>,
            <Button key='accept' type='primary' onClick={() => { this.doApproval(true) }}>通过</Button>
          ]}>
            <TextArea rows={2} placeholder='请输入备注信息 (通过或驳回的原因,选填)' onChange={(e) => { this.setState({ approvalRemark: e.target.value }) }} />
          </Modal>

        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default ArkProbabilitySetting // 要改
