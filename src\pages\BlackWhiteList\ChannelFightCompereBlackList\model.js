﻿/* eslint-disable eqeqeq */
import { getChannelFightUidBlackLists, AddChannelFightUidBlack, DelChannelFightUidBlack } from './api'
import { Modal, message } from 'antd'

export default {
  namespace: 'channelFightCompereBlackList',
  state: {
    updating: false, // 添加黑名单事件是否在处理中
    displayData: [],
    selectUid: 0, // 删除黑名单选中的uid
    confirmMsg: '确认要删除吗？', // 删除白名单确认框提示信息
    newUid: ''
  },

  reducers: {
    // 更新data到黑名单数据列表
    displayList (state, { payload: data }) {
      if (!Array.isArray(data)) {
        console.error('unexpect data type: data=', data)
        return
      }
      return {
        ...state,
        displayData: data
      }
    }
  },

  effects: {
    // 请求并刷新黑名单列表数据
    * getBlackListData ({ params }, { select, call, put }) {
      let resp = yield call(getChannelFightUidBlackLists)
      let { data: { status, data } } = resp
      if (data === null) {
        message.warning('数据为空')
        yield put({
          type: 'displayList',
          payload: []
        })
        return
      }
      if (status !== 0 || !Array.isArray(data)) {
        console.error('getBlackListData() get data error: response=', resp)
        Modal.error({ content: '获取黑名单数据失败，请检查控制台' })
        return
      }
      for (let i = 0; i < data.length; i++) {
        data[i].idx = i + 1
      }
      yield put({
        type: 'displayList',
        payload: data
      })
    },
    // 添加黑名单
    * addBlackListByUid ({ payload }, { call, put }) {
      const { uidList, remark, callback } = payload
      let resp = yield call(AddChannelFightUidBlack, { uidList: uidList, remark: remark, whiteListType: 2 })
      const { data } = resp
      console.log('-->', data)
      if (data === undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[添加黑名单错误]: response=', resp)
        return
      }
      const { status } = data
      if (status === 0) {
        message.success('添加成功')
        if (callback) {
          callback()
        }
      } else {
        console.error('addBlackListByUid()：[添加黑名单] 返回结果为：', resp)
        Modal.warn({ content: '添加失败, 请检查控制台' })
      }
    },
    // 删除黑名单
    * delBlackListByUid ({ payload }, { call, put }) {
      const params = payload
      let resp = yield call(DelChannelFightUidBlack, params)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除黑名单错误]: response=', resp)
        return
      }
      const { status } = data
      if (status === 0) {
        message.success('删除成功')
        yield put({ // 更新列表
          type: 'getBlackListData'
        })
      } else {
        Modal.error({ content: '删除失败：status=' + status })
        console.error('delBlackListByUid() resp=', resp)
      }
    }
  }
}
