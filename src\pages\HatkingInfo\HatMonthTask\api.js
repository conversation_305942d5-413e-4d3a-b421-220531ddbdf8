import request from '@/utils/request'
import { stringify } from 'qs'

// ------------------------任务配置-----------------------------------//
// 获取任务配置列表
export function listTaskConfig (params) {
  return request(`/fts_hgame/hat_month_task/boss/list_task_config?${stringify(params)}`)
}

// 刷新任务配置确认信息
export function flushTaskConfig (params) {
  return request(`/fts_hgame/hat_month_task/boss/flush_task_config?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 提交审核
export function approvalTaskConfig (params) {
  return request(`/fts_hgame/hat_month_task/boss/approval_task_config?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}
// ------------------------任务配置-----------------------------------//

// ------------------------发奖确认-----------------------------------//
// 发奖确认列表
export function listRewardConfirm (params) {
  return request(`/fts_hgame/hat_month_task/boss/list_reward_confirm?${stringify(params)}`)
}

// 发奖确认提交审批
export function approvalRewardConfirm (params) {
  return request(`/fts_hgame/hat_month_task/boss/approval_reward_confirm?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 发奖确认刷新
export function flushRewardConfirm (params) {
  return request(`/fts_hgame/hat_month_task/boss/flush_reward_confirm?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}
// ------------------------发奖确认-----------------------------------//

//  任务完成进度报表
export function listTaskProgress (params) {
  return request(`/fts_hgame/hat_month_task/boss/list_task_progress?${stringify(params)}`)
}

// 发奖明细
export function listRewardHistory (params) {
  return request(`/fts_hgame/hat_month_task/boss/list_reward_history?${stringify(params)}`)
}
