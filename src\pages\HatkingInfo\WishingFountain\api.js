﻿import request from '@/utils/request'
import { stringify } from 'qs'

let badResp = {
  data: {
    status: -1,
    message: '参数错误'
  }
}

export function getCurWishActConfig () {
  let url = `/fountain/wish_boss/get_cur_act_config`
  return request(url)
}

export function delWishActConfig (actID) {
  if (typeof actID !== 'string') {
    console.error('delActConfig() get unexpect actID: ', actID)
    return badResp
  }
  let url = `/fountain/wish_boss/del_act_config`
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify({ actID: actID })
  })
}

export function addAupdateWishActConfig (params) {
  let url = `/fountain/wish_boss/add_update_act_config`
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function getStatInfo (params) {
  let url = `/fountain/wish_boss/get_stat_info?${stringify(params)}`
  console.log('param:', params)
  return request(url, { jsonp: true })
}

export function getAccountRemain (params) {
  let url = `/fountain/wish_boss/get_account_remain`
  return request(url, { jsonp: true })
}
