import { genGetListTemplate, genUpdateTemplate } from '@/utils/common'

const getWhitelistReq = genGetListTemplate('/white_list/esign_whitelist/get', 'whiteList', (raw) => {
  return raw
})

const addWhitelistReq = genUpdateTemplate('/white_list/esign_whitelist/add')

export default {
  namespace: 'broadcastEffects',
  state: {
    whiteList: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getWhitelistReq,
    addWhitelistReq
  }
}
