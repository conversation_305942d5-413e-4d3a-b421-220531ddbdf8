import React, { Component } from 'react'
import { Table, Form, Card, Modal, Popconfirm, InputNumber, Upload, Tabs, Button, message, Divider, Input } from 'antd'
import { connect } from 'dva'
import { UploadOutlined } from '@ant-design/icons'
var moment = require('moment')
const namespace = 'RecommendBlacklist'
const FormItem = Form.Item
const { confirm } = Modal
const TabPane = Tabs.TabPane

const sidExemptList = 1
const channelExemptList = 2
const uidExemptList = 3

@connect(({ RecommendBlacklist }) => ({
  model: RecommendBlacklist
}))

class RecommendExemptList extends Component {
  getApprovalDesc = (val) => {
    if (val === 0) {
      return '-'
    }
    if (val === 1) {
      return '审批中'
    }
    if (val === 2) {
      return '审批通过'
    }
    if (val === 3) {
      return '审批拒绝'
    }
    return '-'
  }

  // column structs.
  sidBlackListColumns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: 'sid', dataIndex: 'sid', key: 'sid', align: 'center' },
    { title: '频道名称', dataIndex: 'sidName', key: 'sidName', align: 'center' },
    { title: 'asid', dataIndex: 'asid', key: 'asid', align: 'center' },
    { title: '备注', dataIndex: 'remark', align: 'center' },
    { title: '操作人', dataIndex: 'creator', align: 'center' },
    { title: '操作时间', dataIndex: 'createAt', align: 'center', render: text => this.dateString(text) },
    { title: '审批状态', dataIndex: 'status', render: (v) => this.getApprovalDesc(v) },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <Popconfirm title='确认删除?' onConfirm={this.handleDel(record, sidExemptList)}><a>删除</a></Popconfirm>
        </span>)
    }
  ]

  channelBlackListColumns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: 'sid', dataIndex: 'sid', key: 'sid', align: 'center' },
    { title: 'ssid', dataIndex: 'ssid', key: 'ssid', align: 'center' },
    { title: '厅名称', dataIndex: 'ssidName', key: 'ssidName', align: 'center' },
    { title: 'asid', dataIndex: 'asid', key: 'asid', align: 'center' },
    { title: '备注', dataIndex: 'remark', align: 'center' },
    { title: '操作人', dataIndex: 'creator', align: 'center' },
    { title: '操作时间', dataIndex: 'createAt', align: 'center', render: text => this.dateString(text) },
    { title: '审批状态', dataIndex: 'status', render: (v) => this.getApprovalDesc(v) },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <Popconfirm title='确认删除?' onConfirm={this.handleDel(record, channelExemptList)}><a>删除</a></Popconfirm>
        </span>)
    }
  ]

  uidBlackListColumns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: 'uid', dataIndex: 'uid', key: 'uid', align: 'center' },
    { title: 'YY号', dataIndex: 'yy', align: 'center' },
    { title: 'YY昵称', dataIndex: 'nick', align: 'center' },
    { title: '签约asid', dataIndex: 'asid', align: 'center' },
    { title: '添加时间', dataIndex: 'createAt', align: 'center', render: text => this.dateString(text) },
    { title: '操作人', dataIndex: 'creator', align: 'center' },
    { title: '备注', dataIndex: 'remark', align: 'center' },
    { title: '审批状态', dataIndex: 'status', render: (v) => this.getApprovalDesc(v) },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <Popconfirm title='确认删除?' onConfirm={this.handleDel(record, uidExemptList)}><a>删除</a></Popconfirm>
        </span>)
    }
  ]

  defaultPageValue = {
    defaultPageSize: 50,
    pageSizeOptions: ['50', '100', '500', '2000'],
    showSizeChanger: true,
    onChange: (page, size) => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = {
    visible: false,
    confirmVisible: false,
    deleteConfirmMsg: '',
    selectedRowKeys: [],
    value: {},
    type: 1
  }

  dateString (timestamp) {
    if (timestamp === 0 || timestamp === undefined) {
      return '-'
    }
    return moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss')
  }

  // show modal
  showModal = (record, type) => () => {
    if (record == null) record = { sid: '' }
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.setState({ visible: true, type: type, title: '添加(低质豁免)' })
  }

  // hide modal
  hideModal = () => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.setState({ visible: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // add and update
  onFinish = values => {
    const { dispatch } = this.props
    values.type = this.state.type
    console.log(values)
    dispatch({
      type: `${namespace}/addExemptItem`,
      payload: values,
      getListParam: { type: this.state.type }
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  handleDel = (record, type) => e => {
    const { dispatch } = this.props
    const data = { id: record.id }
    dispatch({
      type: `${namespace}/removeExemptItem`,
      payload: data,
      getListParam: { type: type }
    })
  }

  batchHandleDel = (type) => e => {
    const { dispatch } = this.props
    const { removeKey } = this.state
    const data = { id: removeKey }
    confirm({
      title: '确认删除?',
      onOk () {
        dispatch({
          type: `${namespace}/removeExemptItem`,
          payload: data,
          getListParam: { type: type }
        })
      }
    })
    this.setState({ selectedRowKeys: [] })
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = { type: sidExemptList }
    dispatch({
      type: `${namespace}/getExemptList`,
      payload: data
    })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onSelectChange = (selectedRowKeys, selectedRows) => {
    console.log(selectedRowKeys)
    console.log(selectedRows)
    var value = selectedRows.map(item => item.id).join(',')
    this.setState({ selectedRowKeys })
    this.setState({ removeKey: value })
  }

  onChangeTab = type => {
    const { dispatch } = this.props
    var data = { type: type }
    dispatch({
      type: `${namespace}/getExemptList`,
      payload: data
    })
    this.setState({ type: type })
    this.setState({ selectedRowKeys: [] })
    this.setState({ removeKey: '' })
  }

  importOnChange = info => {
    if (info.file.status !== 'done') {
      return
    }
    if (info.file.response.status === 0) {
      message.success(`${info.file.name} 上传成功`)
    } else {
      message.error(info.file.response.msg)
    }
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getExemptList`,
      payload: { type: this.state.type }
    })
  }

  // content
  render () {
    const { model: { exemptList } } = this.props
    const { visible, title, type, selectedRowKeys } = this.state
    const importUri = '/recommend_filter/import_exempt_list?type=' + type
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange
    }

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <Card>
        <Tabs defaultActiveKey={sidExemptList} onChange={this.onChangeTab}>

          <TabPane tab='sid' key={sidExemptList}>
            <Form>
              <Button type='primary' onClick={this.showModal(null, sidExemptList)}>添加</Button>
              <Divider type='vertical' /> {/* 分割线 */}
              <Button type='primary' onClick={this.batchHandleDel(sidExemptList)}>批量删除</Button>
              <Divider type='vertical' />
              <Button type='primary'>
                <a href='https://makefriends.bs2dl.yy.com/sid.xlsx' type='primary' onClick={this.downLoad}>模板</a>
              </Button>
              <Divider type='vertical' />
              <Upload action={importUri} onChange={this.importOnChange} >
                <Button type='primary'>
                  <UploadOutlined /> 导入
                </Button>
              </Upload>
              <Table rowKey={(record, index) => index} rowSelection={rowSelection} dataSource={exemptList} columns={this.sidBlackListColumns} pagination={this.defaultPageValue} />
            </Form>
          </TabPane>

          <TabPane tab='sid&ssid' key={channelExemptList}>
            <Form>
              <Button type='primary' onClick={this.showModal(null, channelExemptList)}>添加</Button>
              <Divider type='vertical' /> {/* 分割线 */}
              <Button type='primary' onClick={this.batchHandleDel(channelExemptList)}>批量删除</Button>
              <Divider type='vertical' />
              <Button type='primary'>
                <a href='https://makefriends.bs2dl.yy.com/sidssid.xlsx' type='primary' onClick={this.downLoad}>模板</a>
              </Button>
              <Divider type='vertical' />
              <Upload action={importUri} onChange={this.importOnChange} >
                <Button type='primary'>
                  <UploadOutlined /> 导入
                </Button>
              </Upload>
              <Table rowKey={(record, index) => index} rowSelection={rowSelection} dataSource={exemptList} columns={this.channelBlackListColumns} pagination={this.defaultPageValue} />
            </Form>
          </TabPane>
          <TabPane tab='uid' key={uidExemptList}>
            <Form>
              <Button type='primary' onClick={this.showModal(null, uidExemptList)}>添加</Button>
              <Divider type='vertical' /> {/* 分割线 */}
              <Button type='primary' onClick={this.batchHandleDel(uidExemptList)}>批量删除</Button>
              <Divider type='vertical' />
              <Button type='primary'>
                <a href='https://makefriends.bs2dl.yy.com/uid.xlsx' type='primary' onClick={this.downLoad}>模板</a>
              </Button>
              <Divider type='vertical' />
              <Upload action={importUri} onChange={this.importOnChange} >
                <Button type='primary'>
                  <UploadOutlined /> 导入
                </Button>
              </Upload>
              <Table rowKey={(record, index) => index} rowSelection={rowSelection} dataSource={exemptList} columns={this.uidBlackListColumns} pagination={this.defaultPageValue} />
            </Form>
          </TabPane>
        </Tabs>

        <Modal visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit} forceRender>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            {
              type === sidExemptList
                ? <div>
                  <FormItem label='sid' name='sid' rules={[
                    { required: true, message: '请输入sid(长位)!' },
                    ({ getFieldValue }) => ({
                      validator (rule, value) {
                        if (getFieldValue('sid') <= 0) {
                          return Promise.reject(new Error('sid不能小于或等于0!'))
                        }
                        return Promise.resolve()
                      }
                    })
                  ]}>
                    <InputNumber placeholder='请输入长位频道' style={{ width: '100%' }} />
                  </FormItem>
                  <FormItem label='备注' name='remark' rules={[
                    { required: true, message: '备注不能为空!' }
                  ]} >
                    <Input placeholder='请输入备注' style={{ width: '100%' }} />
                  </FormItem>
                </div>
                : type === channelExemptList
                  ? <div>
                    <FormItem label='sid' name='sid' rules={[
                      { required: true, message: '请输入sid!' },
                      ({ getFieldValue }) => ({
                        validator (rule, value) {
                          if (getFieldValue('sid') <= 0) {
                            return Promise.reject(new Error('sid不能小于或等于0!'))
                          }
                          return Promise.resolve()
                        }
                      })
                    ]}>
                      <InputNumber placeholder='请输入长位频道' style={{ width: '100%' }} />
                    </FormItem>
                    <FormItem label='ssid' name='ssid' rules={[
                      { required: true, message: '请输入ssid!' },
                      ({ getFieldValue }) => ({
                        validator (rule, value) {
                          if (getFieldValue('ssid') <= 0) {
                            return Promise.reject(new Error('ssid不能小于或等于0!'))
                          }
                          return Promise.resolve()
                        }
                      })
                    ]}>
                      <InputNumber placeholder='请输入ssid' style={{ width: '100%' }} />
                    </FormItem>
                    <FormItem label='备注' name='remark' rules={[{ required: true, message: '备注不能为空!' }]}>
                      <Input placeholder='请输入备注' style={{ width: '100%' }} />
                    </FormItem>
                  </div>
                  : <div>
                    <FormItem label='uid' name='uid' rules={[
                      { required: true, message: '请输入uid!' },
                      ({ getFieldValue }) => ({
                        validator (rule, value) {
                          if (getFieldValue('uid') <= 0) {
                            return Promise.reject(new Error('uid不能小于或等于0!'))
                          }
                          return Promise.resolve()
                        }
                      })
                    ]}>
                      <InputNumber placeholder='请输入uid' style={{ width: '100%' }} />
                    </FormItem>
                    <FormItem label='备注' name='remark' rules={[{ required: true, message: '备注不能为空!' }]}>
                      <Input placeholder='请输入备注' style={{ width: '100%' }} />
                    </FormItem>
                  </div>
            }
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default RecommendExemptList
