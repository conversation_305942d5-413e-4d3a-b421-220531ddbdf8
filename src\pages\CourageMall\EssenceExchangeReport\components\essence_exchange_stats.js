import React, { Component } from 'react'
import { Table, Divider, But<PERSON>, Card, Form, DatePicker } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker

@connect(({ essenceExchange }) => ({ // model 的 namespace
  model: essenceExchange // model 的 namespace
}))
class EssenceExchangeStatsComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
    this.setState()
  }

  // 日期 豆荚兑换总流水  豆荚兑换魔豆流水 豆荚兑换道具流水  豆荚兑换免费礼物流水 豆荚兑换非免费礼物流水
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '兑换总流水', dataIndex: 'totalAmount', align: 'center' },
    { title: '兑换总用户数', dataIndex: 'totalUsers', align: 'center' },
    { title: '兑换能量流水', dataIndex: 'energyAmount', align: 'center' },
    { title: '兑换能量用户数', dataIndex: 'energyUsers', align: 'center' },
    { title: '兑换道具流水', dataIndex: 'friendAmount', align: 'center' },
    { title: '兑换道具用户数', dataIndex: 'friendUsers', align: 'center' },
    { title: '兑换免费礼物流水', dataIndex: 'freePropsAmount', align: 'center' },
    { title: '兑换免费礼物用户数', dataIndex: 'freePropsUsers', align: 'center' },
    { title: '兑换非免费礼物流水', dataIndex: 'paidPropsAmount', align: 'center' },
    { title: '兑换非免费礼物用户数', dataIndex: 'paidPropsUsers', align: 'center' }
  ]

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getPodExchangeStatsList`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { statsList } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form>
          <span style={{ marginLeft: 10 }}>时间范围:</span>
          <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Divider />
          <Table dataSource={statsList} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default EssenceExchangeStatsComponent
