import { But<PERSON>, Card, DatePicker, Divider, Form, Table } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
const gameMap = { 0: 'ALL', 1: '武器大师', 2: '热血屠龙', 3: '星球漫步', 4: '明星衣橱', 5: '活动玩法' }
const chanMap = { all: 'ALL', pc: 'PC端', zhuiwan: 'YO交友', yomi: 'YO语音' }
// const namespace = 'hatkingPk' // model 的 namespace

@connect(({ hatkingJy }) => ({ // model 的 namespace
  model1: hatkingJy // model 的 namespace
}))

class HatKingCrucialInfoNewComponent extends Component {
  state = {
    isLoading: false,
    value: {},
    visible: false,
    list: [],
    dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
  }

  componentDidMount () {
    this.loadData()
  }

  // 日期 渠道类型  魔豆模拟流水 付费用户数 剩余发放监控汇总  成功总流水 成功总人数 累计成功人数  累计失败人数  连胜总发放金额 频道广播数量  全服广播数量
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '玩法类型', dataIndex: 'arenaId', align: 'center', render: (text, record, index) => { return gameMap[text] }, filters: [{ text: 'ALL', value: 0 }, { text: '武器大师', value: 1 }, { text: '热血屠龙', value: 2 }, { text: '星球漫步', value: 3 }, { text: '明星衣橱', value: 4 }, { text: '活动玩法', value: 5 }], defaultFilteredValue: ['0'], onFilter: (value, record) => record.arenaId === value },
    { title: '渠道类型', dataIndex: 'platform', align: 'center', render: (text, record, index) => { return chanMap[text] }, filters: [{ text: 'ALL', value: 'all' }, { text: 'PC端', value: 'pc' }, { text: 'YO交友', value: 'zhuiwan' }, { text: 'YO语音', value: 'yomi' }], defaultFilteredValue: ['all'], onFilter: (value, record) => record.platform.includes(value) },
    { title: '魔豆模拟流水', dataIndex: 'betAmethyst', align: 'center' },
    { title: '付费用户数', dataIndex: 'betUser', align: 'center' },
    { title: '发放监控汇总', dataIndex: 'jackpotLeft', align: 'center' },
    { title: '成功总流水', dataIndex: 'betRewardAmethyst', align: 'center' },
    { title: '装扮碎片流水', dataIndex: 'fragCount', align: 'center' },
    { title: '模拟发放占比', dataIndex: 'betRewardRatio', align: 'center' },
    { title: '成功总人数', dataIndex: 'betRewardUser', align: 'center' },
    { title: '累计成功人数', dataIndex: 'profitUser', align: 'center' },
    { title: '累计失败人数', dataIndex: 'lossUser', align: 'center' },
    { title: '连胜总发放金额', dataIndex: 'comboRewardAmethyst', align: 'center' },
    { title: '扭转乾坤发放金额', dataIndex: 'cowRecycleAmethyst', align: 'center' },
    { title: '许愿喷泉发放金额', dataIndex: 'wishRecycleAmethyst', align: 'center' },
    { title: '神秘宝箱额外发放', dataIndex: 'mysteryBoxAmethyst', align: 'center' },
    { title: '魔豆奖励发放金额', dataIndex: 'podCustomerAward', align: 'center' },
    { title: '礼物流光爆豆', dataIndex: 'giftAddAmethyst', align: 'center' },
    { title: '频道广播数量', dataIndex: 'subsidCount', align: 'center' },
    { title: '全服广播数量', dataIndex: 'allsidCount', align: 'center' }
  ]

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    const { modelName } = this.props
    this.setState({ isLoading: true })
    dispatch({
      type: `${modelName}/getCrucialInfoListNew2`,
      payload: data,
      cbFunc: () => { this.setState({ isLoading: false }) }
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  handleSelectChange = (value) => {
    console.log(value)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { crucialListNew2 } } = this.props
    const { dateRange, isLoading } = this.state
    return (
      <Card>
        <Form>
          <span style={{ marginLeft: 10 }}>时间范围:</span>
          <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Divider />
          <Table loading={isLoading} scroll={{ x: 'max-content' }} dataSource={crucialListNew2} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default HatKingCrucialInfoNewComponent
