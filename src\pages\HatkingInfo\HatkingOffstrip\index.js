import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs, Card } from 'antd'
// import DropPoolCfgComponent from './components/config'
import OffstripMainComponent from './components/list'
import OffstripReleaseMainComponent from './components/release_list'
import { connect } from 'dva'
// import DropChannelComponent from './components/rule'
import OffstripBlacklist from './components/blacklist'
// import ProgressConfigComponent from './components/progress'
// import DropWarnComponent from './components/warn'

const TabPane = Tabs.TabPane

@connect(({ offstripMain }) => ({ // model 的 namespace
  model: offstripMain // model 的 namespace
}))
class HatkingOffstrip extends Component { // 默认页面组件，不需要修改
  state = { activeKey: '1' }
  onTabClick = key => {
    this.setState({ activeKey: key })
  }

  render () {
    const { route } = this.props
    const { activeKey } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs type='card' defaultActiveKey='1' onTabClick={this.onTabClick}>
            <TabPane tab='线上配置' key='1'>
              {activeKey === '1' ? <OffstripReleaseMainComponent /> : ''}
            </TabPane>

            <TabPane tab='奖池配置' key='2'>
              {activeKey === '2' ? <OffstripMainComponent /> : ''}
            </TabPane>

            <TabPane tab='黑名单配置' key='3'>
              {activeKey === '3' ? <OffstripBlacklist /> : ''}
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default HatkingOffstrip // 保证唯一
