import { connect } from 'dva'
import React, { Component } from 'react'
import { Button, Col, Form, Input, message, Modal, Row, Space, Spin, Table } from 'antd'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { fetchAllRemoteData, formatOptions, parseStringToChannel } from '@/utils/common'
import { SearchOutlined } from '@ant-design/icons'
import ExportRemoteData from '@/components/ExportRemoteData'

// const namespace = 'ChannelDetailInfo'

const templateOptions = [
  { value: 268435460, label: '交友模板' },
  { value: 33554522, label: '约战模板' },
  { value: 33554530, label: '追玩语音房模板' },
  { value: 268435466, label: '宝贝模板' },
  { value: 0, label: '基础模板' },
  { value: -1, label: '频道不存在' }
]

const isLimitTxtOptions = [
  { value: true, label: '限制' },
  { value: false, label: '不限制' }
]

// const channelTypeOptions = [
//   { value: 'top', label: '顶级频道' },
//   { value: 'sub', label: '子频道' }
// ]

@connect(({ ChannelDetailInfo }) => ({
  model: ChannelDetailInfo
}))

class ChannelDetailInfo extends Component {
  constructor (props) {
    super(props)
    this.state = {
      list: [], loadingVisible: false, currentPage: 1, perPage: 1000, currentFetch: 0, pagination: { current: 1, pageSize: 10 }
    }
  }

  columns = [
    { title: 'template', dataIndex: 'template_id', align: 'left', ellipsis: true, options: templateOptions },
    { title: 'templateId', dataIndex: 'template_id', align: 'left', ellipsis: true, sorter: { compare: (a, b) => a.template_id - b.template_id } },
    { title: 'sid', dataIndex: 'sid', align: 'left', ellipsis: true, sorter: { compare: (a, b) => a.sid - b.sid } },
    { title: 'ssid', dataIndex: 'ssid', align: 'left', ellipsis: true, sorter: { compare: (a, b) => a.ssid - b.ssid } },
    { title: 'asid', dataIndex: 'asid', align: 'left', ellipsis: true, sorter: { compare: (a, b) => a.asid - b.asid } },
    { title: 'name', dataIndex: 'name', align: 'left', ellipsis: true },
    { title: 'createTime', dataIndex: 'create_time', align: 'left', ellipsis: true, disabledSearch: true, sorter: { compare: (a, b) => a.create_time < b.create_time ? -1 : (a.create_time === b.create_time ? 0 : 1) } },
    { title: '限制文字聊天速度', dataIndex: 'is_limit_txt', align: 'left', ellipsis: true, options: isLimitTxtOptions },
    { title: '文字聊天速度每句间隔秒数', dataIndex: 'txt_limittime', align: 'left', ellipsis: true, disabledSearch: true },
    { title: '频道人数限制', dataIndex: 'user_limit', align: 'left', ellipsis: true, disabledSearch: true, render: (val, record) => record.channelType === 'top' ? record.user_limit : record.maxman, exportRender: (val, record) => record.channelType === 'top' ? record.user_limit : record.maxman }
  ]

  exportRenderColumns = (columns) => {
    return this.renderColumns(columns)
  }

  renderColumns = (columns) => {
    columns = columns || this.columns
    let getLocalFilter = (column) => {
      return (v, rec) => {
        return String(v).localeCompare(rec[column.dataIndex]) === 0
      }
    }
    let getOptionRender = (options) => {
      return (val, record) => formatOptions(val, options)
    }

    return columns.map(v => {
      if (v.disabledSearch) {
        return v
      }
      if (v.options) {
        v.options.forEach(opt => { opt.text = opt.label })
        v.filters = v.options
        v.onFilter = getLocalFilter(v)
        v.filterIcon = <SearchOutlined />
        v.filterMode = 'tree'
        v.filterSearch = true

        if (!v.render) {
          v.render = getOptionRender(v.options)
        }

        return v
      }
      v = Object.assign(this.getColumnSearchTextProps(v.dataIndex), v)
      return v
    })
  }

  // 搜索文本
  getColumnSearchTextProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={node => {
            this.searchInput = node
          }}
          placeholder={`Search ...`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => this.handleSearch(selectedKeys, confirm, dataIndex)}
          style={{ marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button size='small' onClick={() => this.handleReset(clearFilters)}>重置</Button>
          <Button type='primary' size='small' onClick={() => this.handleSearch(selectedKeys, confirm, dataIndex)}>确定</Button>
        </Space>
      </div>
    ),
    filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
        : ''
  })

  handleSearch = (selectedKeys, confirm, dataIndex, table) => {
    confirm()
    this.setState({
      searchText: '', searchedColumn: dataIndex
    })
  }

  handleReset = (clearFilters, table) => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  // 点击查询
  onQueryClick = (values) => {
    // let sValues = '5180550,**********\n' +
    //   '74012719,**********\n' +
    //   '1494930456\n' +
    //   '87814665,2795936894'

    let sValues = ''
    sValues = values.channels || sValues

    let channelList = parseStringToChannel(sValues)
    if (!channelList || channelList.length < 1) {
      message.warn('当前没有输入频道')
      return false
    }

    let mode = 'sid'
    let apiURI = `/assist_tools/webdb/batch_get_top_channel_info`
    if (typeof channelList[0] === 'object') {
      mode = 'ssid'
      apiURI = `/assist_tools/webdb/batch_get_sub_channel_info`
    }

    const self = this
    fetchAllRemoteData({
      pageSize: 1000,
      showLoading: (page, pageSize, total) => {
        self.setState({ loadingVisible: true, currentPage: page, perPage: pageSize, currentFetch: total })
      },
      closeLoading: (page, pageSize, total) => {
        self.setState({ loadingVisible: false })
      },
      uriBuilder: (page, pageSize) => {
        let startIndex = (page - 1) * pageSize
        let endIndex = startIndex + pageSize
        if (endIndex > channelList.length) {
          endIndex = channelList.length
        }
        let subList = []
        for (let i = startIndex; i < endIndex; i++) {
          subList.push(channelList[i])
        }

        let body = ''
        if (mode === 'ssid') {
          body = JSON.stringify({ channelList: subList })
        } else {
          body = JSON.stringify({ sidList: subList })
        }

        return {
          fetchURI: apiURI,
          fetchBody: body
        }
      },
      method: 'POST',
      callback: (dataList) => {
        dataList.forEach((item, index) => { item.index = index })
        self.setState({ list: dataList })
      }
    })
  }

  render () {
    const { route } = this.props
    const { loadingVisible, currentPage, perPage, currentFetch, list } = this.state

    const pagination = {
      pageSize: this.state.pagination.pageSize,
      current: this.state.pagination.current,
      pageSizeOptions: [10, 20, 50, 100],
      showSizeChanger: true,
      onChange: (page, pageSize) => {
        this.setState({ pagination: { current: page, pageSize: pageSize } })
      },
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    }

    const columns = this.renderColumns(this.columns)

    return (
      <>
        <PageHeaderWrapper title={route.name}>
          <Row style={{ marginBottom: '1em' }}>
            <Form labelCol={{ span: 12 }} layout={'inline'} ref={form => {
              this.formRefQuery = form
            }} onFinish={this.onQueryClick}>
              <Form.Item name={'channels'} label={'channels'} required>
                <Input.TextArea style={{ height: 100, width: 300 }} placeholder='channels' allowClear />
              </Form.Item>

              <Button type='primary' htmlType='submit'>查询</Button>
              <ExportRemoteData buttonStyle={{ marginLeft: 20 }} filename={'channel_info'} dataProvider={() => list} />
              <div style={{ marginLeft: 20 }}>
                <span style={{ color: 'red', fontWeight: 'bold' }}>查询sid单列,每个 sid 都是一列即可：</span><br />
                5180550<br />
                74012719<br />
                <span style={{ color: 'red', fontWeight: 'bold' }}>查询sid/ssid 组合（每行是一个sid/ssid组合，sid,ssid 之间可以用非数字分隔）：</span><br />
                5180550 **********<br />
                74012719 **********<br />
              </div>
            </Form>
          </Row>
          <Row style={{ marginBottom: '1em' }}>
            <Col span={24}>
              <Table columns={columns}
                dataSource={list}
                size='small'
                scroll={{ x: 'max-content' }}
                pagination={pagination}
                showSorterTooltip={false}
                rowKey={record => record.index}
                expandable={{
                  expandedRowRender: record => <pre style={{ margin: 0 }}>{JSON.stringify(record, null, 4)}</pre>
                }}
              />
            </Col>
          </Row>

          <Modal visible={loadingVisible} footer={null} closable={false} centered>
            <Spin
              tip={'正在加载第[' + currentPage + ']页数据（每页[' + perPage + ']条）, 当前已加载[' + currentFetch + ']......'} />
          </Modal>
        </PageHeaderWrapper>
      </>
    )
  }
}

export default ChannelDetailInfo
