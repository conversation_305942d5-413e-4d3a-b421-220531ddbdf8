import React, { Component } from 'react'
import { Typography, Modal, Input, Form, Popover, message } from 'antd'
import { InfoBoard } from '@/components/SimpleComponents'
import { limitModeOptions } from '../../DropMain/components/list_common'
import { checkIsNumber } from '@/utils/common'

const { Link, Text } = Typography
const { Item } = Form
const defaultValue = { name: '未命名', limitA: 0, limitB: 0, limitC: 0 }

class BoxWarSettingButton extends Component {
  state = {
    editValue: {},
    modalVisible: false
  }

  componentDidMount = () => {
    const { value } = this.props
    this.formRef.setFieldsValue(value)
    this.fromRefMonitor(value)
  }

  // 弹出编辑模态框, 会重置数据
  showEditerModal = () => {
    const { value } = this.props
    this.fromRefMonitor(value || defaultValue)
    this.setState({ modalVisible: true })
  }

  // 关闭编辑模态框
  closeEditerModal = () => {
    this.setState({ modalVisible: false })
  }

  // 表单每次更新时调用, 维护临时配置值
  fromRefMonitor = (v) => {
    if (!v) return ''
    this.setState({ editValue: v })
  }

  // 更新外层配置值
  comfirmUpdateOuterValue = () => {
    const { onChange } = this.props
    const { editValue } = this.state
    let reason = this.checkFromValue(editValue)

    if (reason) {
      message.warn(reason)
      return
    }
    editValue.limitA = parseInt(editValue.limitA)
    editValue.limitB = parseInt(editValue.limitB)
    editValue.limitC = parseInt(editValue.limitC)
    this.setState({ modalVisible: false })
    onChange(editValue)
  }

  // 检查参数
  checkFromValue = (v) => {
    if (!checkIsNumber(v.limitA) || !checkIsNumber(v.limitB) || !checkIsNumber(v.limitC)) {
      return '组合上限填写有误,请检查~'
    }
    if (v.name === '') {
      return '奖励名称未填写'
    }
    return ''
  }

  compereValue = (before, after) => {
    const { diffMode } = this.props
    if (!diffMode) return after
    if (before === undefined) before = ''
    if (before === after) return <Text>{after}</Text>
    return <Text type='danger'><Text type='secondary'>{`${before} => `}</Text>{after}</Text>
  }

  modeFormater = (mode) => {
    return limitModeOptions.find(item => item.value === mode)?.label
  }

  render () {
    const { value: initvalValue, cmpValue, isEdit, diffMode } = this.props
    const { modalVisible } = this.state

    const infoColumns = [
      { label: '奖励名称', dataIndex: 'name', span: 24, render: (v) => this.compereValue(cmpValue?.name || '未命名', v || '未命名') },
      { label: '钻石组合上限', dataIndex: 'limitA', span: 24, render: (v) => this.compereValue(cmpValue?.limitA, v) },
      { label: '至尊组合上限', dataIndex: 'limitB', span: 24, render: (v) => this.compereValue(cmpValue?.limitB, v) },
      { label: '王者组合上限', dataIndex: 'limitC', span: 24, render: (v) => this.compereValue(cmpValue?.limitC, v) }
    ]

    const infoBoxContent = <div style={{ width: '20em' }}><InfoBoard key={initvalValue} columns={infoColumns} dataSource={initvalValue || {}} divider={false} /></div>

    let placeholder = ''
    let isChangeTip = ''
    if (diffMode && JSON.stringify(initvalValue) !== JSON.stringify(cmpValue)) {
      isChangeTip = <Text type='danger'>*</Text>
    }

    if (!isEdit) { // 查看模式
      placeholder = <Popover placement='leftTop' trigger='hover' content={infoBoxContent}>{isChangeTip}<Text type='success'>{initvalValue?.name || '未命名'}</Text></Popover>
    }
    if (isEdit) { // 编辑模式
      placeholder = <Link onClick={() => this.showEditerModal()}>{initvalValue?.name || '待命名'}</Link>
    }

    return (
      <div>
        {placeholder}
        <Modal forceRender title='组合上限配置' visible={modalVisible}
          onCancel={() => this.closeEditerModal()}
          onOk={() => { this.comfirmUpdateOuterValue() }} >
          <Form labelCol={{ span: 9 }} forceRender ref={from => { this.formRef = from }} initialValues={defaultValue} onValuesChange={(v, r) => this.fromRefMonitor(r)} >
            <Item label='名称' name='name'>
              <Input />
            </Item>
            <Item label='钻石组合上限' name='limitA'>
              <Input />
            </Item>
            <Item label='至尊组合上限' name='limitB'>
              <Input />
            </Item>
            <Item label='王者组合上限' name='limitC'>
              <Input />
            </Item>
          </Form>
        </Modal>
      </div>
    )
  }
}
export default BoxWarSettingButton
