import React, { Component } from 'react'
import { Table, Divider, Form, Card, Popconfirm, Modal, message, Button, InputNumber, Select, AutoComplete } from 'antd'
import { connect } from 'dva'
import { propTypeOptions } from '../../dropCommon'
import PrizeSelector from '../../DropMain/components/prizeSelector'
import { broadcastOptionsJY } from '../../DropMain/components/list_common'

const appID = 36

const namespace = 'dropDragon'
const FormItem = Form.Item
const channel = 200 // 宝贝渠道

@connect(({ dropDragon }) => ({
  model: dropDragon
}))
class DropDragonBabyComponent extends Component {
  // column structs.
  columns = [
    { title: '奖励道具ID', dataIndex: 'id', key: 'id', align: 'center' },
    { title: '医疗包进度', dataIndex: 'progress', align: 'center' },
    { title: '礼物ID', dataIndex: 'propId', align: 'center' },
    { title: '奖励道具名称', dataIndex: 'name', align: 'center' },
    { title: '稀有度', dataIndex: 'propType', align: 'center', render: text => { let v = propTypeOptions.find(i => i.value === text); return v !== undefined ? v.label : text } },
    { title: '数量', dataIndex: 'count', align: 'center' },
    { title: '总价值(紫宝石)', dataIndex: 'price', align: 'center', render: (_, rec) => (rec.price * rec.count).toLocaleString() },
    { title: '需要抽取道具次数N', dataIndex: 'rewardN', align: 'center', render: v => v > 0 ? v : '-' },
    { title: '每日投放上限', dataIndex: 'dailyLimit', align: 'center', render: (v, rec) => v > 0 ? v : rec.progress < 100 ? '-' : ['无上限', '不发放'][v + 1] },
    { title: '广播类型', dataIndex: 'bcType', align: 'center', render: v => { return broadcastOptionsJY.find(item => item.value === v).label } },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(true, record)}>更新</a><Divider type='vertical' />
          <Popconfirm title='确认?' onConfirm={this.handleDel(record.id)}>
            <a href=''>删除</a>
          </Popconfirm>
        </span>)
    }
  ]

  state = { visible: false }

  // show modal
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      if (v.propId > 0) {
        v.prize = v.propId + '-' + v.name
      }

      if (!isUpdate) {
        v.dailyLimit = -1 // 无上限
      }
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? '更新' : '添加' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }
  // add and update
  onFinish = values => {
    const { dispatch, model: { globalPrizeList, listBB } } = this.props
    const { isUpdate } = this.state
    const url = isUpdate ? `${namespace}/updateItem` : `${namespace}/addItem`

    values.channel = channel

    if (values.prize === undefined || values.prize.length === 0 || values.prize.split('-') === 0) {
      console.log(values.prize)
      message.error('无效礼物')
      return
    }

    let propId = parseInt(values.prize.split('-')[0])

    let prize = globalPrizeList.find(item => item.appId === appID && item.id === propId) // 礼物类型宝贝 && 礼物id为选中ID
    if (prize === undefined) {
      message.error('礼物查找失败')
      return
    }

    values.appId = appID
    values.propId = prize.id
    values.name = prize.name
    values.price = prize.price

    if (!isUpdate) {
      // 进度 30-60-100-...-100
      let progressList = [30, 60, 100]
      let idx = 0
      listBB.map(item => item.progress).sort().forEach(val => {
      // console.log(idx, progressList[idx], val)
        if (idx < progressList.length && val >= progressList[idx]) {
          idx++
        }
      })

      // console.log(idx)
      values.progress = idx >= progressList.length ? 100 : progressList[idx]
    }

    // rewardN 只有一个能大于0
    if (values.rewardN > 0 && values.progress < 100) {
      message.error('只有进度100才能指定抽取道具次数')
      return
    }

    let exist = -1
    listBB.map(item => item.rewardN).forEach((val, index) => {
      if (val > 0) {
        exist = index
      }
    })

    if (exist > -1 && listBB[exist].id !== values.id && values.rewardN > 0) {
      message.error('只能指定一个抽取道具次数')
      return
    }

    values.count = parseInt(values.count)

    dispatch({
      type: url,
      payload: values
    })

    this.setState({ visible: false })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/removeItem`,
      payload: { prizeId: key, channel }
    })
  }

  // get list from server.
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`,
      payload: { channel }
    })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  handleModeChange = e => {
    const mode = e.target.value
    this.setState({ tab: mode })
  };

  onChange = e => {
    console.log('value', e.target.value)
    this.setState({ size: e.target.value })
  }

  getNumbers = () => {
    let options = []
    let list = [1, 10, 22, 30, 33, 66, 88, 99, 188, 520, 999, 1314, 1990, 9999, 13140, 52000, 99999]
    list.forEach(v => {
      options.push({ label: v, value: v })
    })
    return options
  }

  // content
  render () {
    const { model: { listBB, globalPrizeList } } = this.props
    const { visible, title } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 6 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 16 },
        sm: { span: 16 }
      }
    }

    return (
      <Card>
        <Button onClick={this.showModal(false)}>新增配置</Button>
        <div>
          <div>1. 需要抽取道具次数N 只有医疗包进度为100的礼物可以配置，且只能配置一个</div>
          <div>2. 每日投放上限仅对进度100的奖励道具生效 -1表示无上限， 0表示不发放此奖励道具 大于0表示发放上限</div>
        </div>
        <Divider />
        <Table rowKey='id' dataSource={listBB} columns={this.columns} />

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem name='id' hidden>
              <InputNumber />
            </FormItem>
            <FormItem hidden name='progress'>
              <InputNumber />
            </FormItem>
            <FormItem label='宝贝ID' name='prize' rules={[{ required: true, message: '必须非负整数(0-白水晶 1-紫水晶)' }]}>
              <PrizeSelector
                type='select'
                prizeList={globalPrizeList}
                appIDLimit={appID}
                onComfirm={(v) => {
                  const { id, name } = v.raw
                  this.formRef.setFieldsValue({ prize: [id, name].join('-') })
                }}
              />
            </FormItem>
            <FormItem label='稀有度' name='propType' rules={[{ required: true, message: '不能为空' }]}>
              <Select options={propTypeOptions} style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='数量' name='count' rules={[{ required: true, message: '不能为空' }]}>
              <AutoComplete options={this.getNumbers()} showSearch style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='抽取道具次数N' name='rewardN'>
              <InputNumber min={0} style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='每日上限' name='dailyLimit'>
              <InputNumber style={{ width: '100%' }} />
            </FormItem>
            <FormItem label='广播类型' name='bcType' >
              <Select options={broadcastOptionsJY} style={{ width: '100%' }} />
            </FormItem>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default DropDragonBabyComponent
