import request from '@/utils/request'

export function getGameTypeWhiteLists (play) {
  let url = `/white_list/videodating_channel_list?play=` + play
  return request(url, { jsonp: true })
}

export function addGameTypeWhiteList (params) {
  let url = '/white_list/white_playmode_channel_add'
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function delGameTypeWhiteList (sid, ssid, play) {
  let form = 'sid=' + sid + '&ssid=' + ssid + '&play=' + play
  let url = '/white_list/white_playmode_channel_del'
  if (play === 'multilive_forbidden' || play === 'multilive_audio') {
    url = '/white_list/videodating_channel_del'
  }
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: form
  })
}

// 拉取玩法黑名单
export function getGameTypeBlackLists (play) {
  let url = `/white_list/game_type_black_ch_list?play=` + play
  return request(url, { jsonp: true })
}

// 增加玩法黑名单
export function addGameTypeBlackList (params) {
  let url = '/white_list/add_game_type_black_ch_list'
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

// 删除玩法黑名单
export function delGameTypeBlackList (sid, ssid, play) {
  let form = 'sid=' + sid + '&ssid=' + ssid + '&play=' + play
  let url = '/white_list/del_game_type_black_ch_list'
  return request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: form
  })
}
