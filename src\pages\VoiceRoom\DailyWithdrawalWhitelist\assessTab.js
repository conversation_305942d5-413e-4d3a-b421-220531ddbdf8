import React, { Component } from 'react'
import { connect } from 'dva'
import { Modal, Form, Input, Divider, Select, Button, Table, Row, Col, Typography, Space, message, DatePicker, Tooltip } from 'antd'
import { InputNumberArray } from '@/components/SimpleComponents'
import { getCookie, onExportExcel } from '@/utils/common'
import moment from 'moment'

const FormItem = Form.Item 
const { Text } = Typography

const namespace = 'dailyWithdrawalWhitelist'

const opTypeOptions = [
  { label: '全部', value: -1 },
  { label: '新增', value: 0 },
  { label: '移除', value: 1 }
]

@connect(({ dailyWithdrawalWhitelist }) => ({
  model: dailyWithdrawalWhitelist
}))

class DailyWithdrawalAssess extends Component {
  state = {
    assessmentModelVisible: false, 
    intervalHour: 0,
    toAssessList: [], // 完整复核列表
    showAmtModal: false,
    // 待复核列表查询参数
    searchParamsV2: {
      uid: '', 
      yy: '', 
      opType: -1
    },
    // 复核历史查询参数
    searchParams: {
      uid: '', 
      yy: '',
      timeRange: [moment().add(-1, 'month'), moment()],
      opType: -1
    }
  }

  componentDidMount () {
    this.queryAssessStatus()
    this.searchAssessHistory(this.state.searchParams)
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // 查询复核状态
  queryAssessStatus = () => {
    this.callModel('queryAssessStatus')
  }

  // 查询复核记录 
  searchAssessHistory = (v) => { 
    const { uid, yy, opType, timeRange } = v
    if (!timeRange || timeRange[0] == null || timeRange[1] == null) {
      message.warn('请填写时间范围')
      return
    }
    let params = {
      uidList: uid,
      yyList: yy,
      opType: opType,
      startDate: Number(timeRange[0].format('YYYYMM')),
      endDate: Number(timeRange[1].format('YYYYMM'))
    }
    this.callModel('queryAssessHistory', {
      params: params
    })
  }

  // 查询待复核列表
  queryToAssessList = (progress, params) => { 
    const { month, disableTip } = progress
    if (!month) {
      message.warn('查询考核状态失败！')
      return
    }
    if (disableTip) {
      message.warn(disableTip) 
    }
    // 固定查当月的全部，避免提交时因为有参数过滤漏掉
    let fixedParams = {
      month: month,
      opType: -1
    }
    this.callModel('queryToAssess', {
      params: fixedParams,
      isDetailMode: true,
      isSlientMode: true,
      cbFunc: (ret) => { 
        const { status, msg, list } = ret
        if (status !== 0) {
          message.warn('获取数据失败:' + msg)
          return
        }
        this.setState({ toAssessList: list, assessmentModelVisible: true })
      }
    })
  }

  // 查询并展示复核门槛配置
  queryAmtConfig = () => {
    this.callModel('queryAmtConfig', {
      isDetailMode: true,
      isSlientMode: true,
      cbFunc: (ret) => {
        const { status, msg, list } = ret
        if (status !== 0) {
          message.warn('查询配置失败：' + msg)
          return
        }
        this.saveLeastAmtFormRef.setFieldsValue(list)
        this.setState({ showAmtModal: true })
      }
    })
  }

  // 更新自动复核门槛金额
  onLeastAmtFinish = (v) => {
    const { leastAmt } = v
    this.callModel('updateAmtConfig', {
      params: { value: Number(leastAmt) },
      isDetailMode: true,
      isJsonMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('更新失败: ' + msg)
          return
        }
        this.setState({ showAmtModal: false })
        message.success('更新成功~')
      }
    })
  }

  // 推送复核结果到送审
  pushToReviewToApproval = (list, assessStatus) => { 
    if (!list?.length) {
      message.warn('列表为空')
      return
    }
    let nick = getCookie('username')
    let { month } = assessStatus
    let usedList = []
    let ignoreList = [] 
    let fixList = list.map(item => {
      if (item.isIgnore) {
        ignoreList.push(item.id)
      } else {
        usedList.push(item.id)
      }
      item.opBefore = this.optTypeFormater(item.optType)
      item.opAfter = this.opAftreFormater(item, true)
      return item
    })
    fixList = fixList.sort((a, b) => {
      if (a.isIgnore !== b.isIgnore) {
        return b.isIgnore
      }
      return false
    })
    let wcText = `${nick}申请复核日提资格数据。
    处理主持数：${usedList.length};
    不处理主持数: ${ignoreList.length};
    请到审批后台查看详情并审批。
    `
    let params = {
      month: month,
      wcText: wcText,
      rawData: JSON.stringify(fixList),
      usedList: usedList,
      ignoreList: ignoreList
    } 
    this.callModel('pushAssessApproval', {
      params: params,
      isDetailMode: true,
      isJsonMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('送审失败: ' + msg)
          return
        }
        message.success('送审成功~')
        this.setState({ assessmentModelVisible: false })
        this.queryAssessStatus()
      }
    })
  }

  showAssessmentModel = () => () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getAssessmentList`,
      payload: { his: false }
    })
    this.setState({ assessmentModelVisible: true })
  }

  // 变动类型
  optTypeFormater = (v) => {
    return v === 0 ? '新增资格' : '移除资格'
  }

  // 复核结果
  opAftreFormater = (r, isHtml) => {
    const { optType, isIgnore } = r
    if (isIgnore) {
      return isHtml ? '<span style="color: red;" >不处理</span>' : '不处理'
    }
    return this.optTypeFormater(optType)
  }

  // 复核结果
  fixOpTypeFormater = (r) => {
    const { optType, isIgnore } = r
    const { toAssessList } = this.state
    let options = [
      { label: this.optTypeFormater(optType), value: false },
      { label: '不处理', value: true }
    ]
    let newList = [...toAssessList]
    return <Select options={options} value={isIgnore || false} style={{ width: '8em' }} 
      onChange={(v) => {
        const { id } = r
        for (let i = 0; i < newList.length; i++) {
          if (newList[i].id === id) {
            newList[i].isIgnore = v
            break
          }
        }
        this.setState({ toAssessList: newList })
      }} /> 
  }

  // 厘=>元
  rmbFormater = (v) => {
    return Number(v) / 1000.0
  }

  // 对公模式渲染
  settleModeFmt = (v) => {
    return v === 1 ? '对公' : '对私'
  }

  toAssessColumns = [
    { title: 'UID', dataIndex: 'anchorUid', key: 'anchorUid' },
    { title: 'YY号', dataIndex: 'yyno', key: 'yyno' },
    { title: '用户名称', dataIndex: 'nick', key: 'nick' },
    { title: '签约家族id', dataIndex: 'familyId', key: 'familyId' },
    { title: '上上月流水（元）', dataIndex: 'lastMonthIncome2', key: 'lastMonthIncome2', render: (v) => { return this.rmbFormater(v) } },
    { title: '上月流水（元）', dataIndex: 'lastMonthIncome', key: 'lastMonthIncome', render: (v) => { return this.rmbFormater(v) } }, 
    { title: '对公模式', dataIndex: 'settleMode', key: 'settleMode', render: (v) => { return this.settleModeFmt(v) } }, 
    { title: '变动类型', dataIndex: 'optType', key: 'optType', render: (v) => { return this.optTypeFormater(v) } },
    { title: '修改类型', dataIndex: '_1', key: 'optType', render: (_, r, i) => { return this.fixOpTypeFormater(r, i) }, exportRender: (v, r) => { return this.opAftreFormater(r, false) } }
  ].map(item => {
    item.align = 'center'
    return item
  })

  historyColumns = [
    { title: '考核时间', dataIndex: 'statDate', key: 'statDate' },
    { title: 'UID', dataIndex: 'anchorUid', key: 'anchorUid' },
    { title: 'YY号', dataIndex: 'yyno', key: 'yyno' },
    { title: '用户名称', dataIndex: 'nick', key: 'nick' },
    { title: '签约家族id', dataIndex: 'familyId', key: 'familyId' },
    { title: '上上月流水（元）', dataIndex: 'lastMonthIncome2', key: 'lastMonthIncome2', render: (v) => { return this.rmbFormater(v) } },
    { title: '上月流水（元）', dataIndex: 'lastMonthIncome', key: 'lastMonthIncome', render: (v) => { return this.rmbFormater(v) } }, 
    { title: '修改类型',
      dataIndex: 'optType',
      key: 'optType',
      align: 'center',
      render: text => {
        if (text === 0) {
          return '日提(72h)'
        } else if (text === 1) {
          return '周提'
        }
        return text
      }
    },
    { title: '变动后提现周期',
      dataIndex: 'optPeriod',
      key: 'optPeriod',
      align: 'center',
      render: text => {
        if (text === 0) {
          return '新增资格'
        } else if (text === 1) {
          return '移除资格'
        }
        return text
      }
    }
  ].map(item => {
    item.align = 'center'
    return item
  })

  updateSearchParams = (before, field, value) => {
    let after = { ...before }
    after[field] = value
    this.setState({ searchParams: after })
  }

  updateSearchParamsV2 = (before, field, value) => {
    let after = { ...before }
    after[field] = value
    this.setState({ searchParamsV2: after })
  }

  // 按参数过滤列表
  filterToAssessList = (list, params) => { 
    let filterList = []
    const { opType, uid, yy } = params
    console.log('params', params)
    list.forEach(item => {
      if (opType >= 0 && item.optType !== opType) {
        return
      }
      if (uid?.length > 0 && !uid.includes(item.anchorUid)) {
        return
      }
      if (yy?.length > 0 && !yy.includes(item.yyno)) {
        return
      }
      filterList.push(item)
    })
    
    return filterList
  }

  render () {
    const { assessmentModelVisible, searchParams, searchParamsV2, toAssessList, showAmtModal } = this.state
    const { assessHistory, assessStatus } = this.props.model

    return (
      <div>
        <Row>
          <Col span={24}>
            <Space>
              <Button onClick={() => { this.queryAmtConfig() }}>设定自动考核门槛</Button>
              <Tooltip title={assessStatus.disableTip || '可正常复核'}>
                <Button disabled={assessStatus.disableTip} onClick={() => { this.queryToAssessList(assessStatus, searchParamsV2) }}>复核</Button>
              </Tooltip>
            </Space>
          </Col> 
          <Col span={24}>
            <Space>
              <Text>UID:</Text>
              <InputNumberArray value={searchParams.uid} onChange={(v) => { this.updateSearchParams(searchParams, 'uid', v) }} style={{ width: '10em' }} placeholder='双击批量输入' />
              <Divider />
              <Text>YY号:</Text>
              <InputNumberArray value={searchParams.yy} onChange={(v) => { this.updateSearchParams(searchParams, 'yy', v) }} style={{ width: '10em' }} placeholder='双击批量输入' />
              <Divider />
              <Text>时间范围:</Text>
              <DatePicker.RangePicker picker='month' format='YYYY-MM' value={searchParams.timeRange} onChange={(v) => { this.updateSearchParams(searchParams, 'timeRange', v) }} style={{ width: '18em' }} />
              <Text>变动类型:</Text>  
              <Select options={opTypeOptions} value={searchParams.opType} onChange={(v) => this.updateSearchParams(searchParams, 'opType', v)} style={{ width: '6em' }} />
              <Divider />
              <Button type='primary' onClick={() => this.searchAssessHistory(searchParams)}>搜索</Button>
              <Button onClick={() => { onExportExcel(this.historyColumns, assessHistory, '复核记录导出.xlsx') }}>导出</Button>
            </Space>
          </Col>
          <Col span={24}>
            <Table rowKey={(record, index) => index} dataSource={assessHistory} columns={this.historyColumns} pagination={this.defaultPageValue} />
          </Col>
        </Row>

        <Modal visible={showAmtModal} title={'设定自动考核门槛'} onCancel={() => this.setState({ showAmtModal: false })} onOk={() => this.saveLeastAmtFormRef.submit()} forceRender>
          <Form ref={r => { this.saveLeastAmtFormRef = r }} onFinish={(v) => { this.onLeastAmtFinish(v) }}>
            <FormItem label='上月流水门槛' name='leastAmt' rules={[{ required: true, message: '上月流水门槛不能为空' }]} >
              <Input style={{ width: '50%' }} placeholder='元' />
            </FormItem>
            <div style={{ marginLeft: 80 }}><font color='red'>主持上月流水达到门槛后，于下月1号自动添加即使提现，不满足即回收资格</font></div>
          </Form>
        </Modal>
        
        {/* 日提复核模态框 */}
        <Modal forceRender width={1600} visible={assessmentModelVisible} title='日提复核' onCancel={() => this.setState({ assessmentModelVisible: false })} onOk={() => this.pushToReviewToApproval(toAssessList, assessStatus)} okText='提交审批'>
          <Row>
            <Col span={24}>
              <Space>
                <Text>UID:</Text>
                <InputNumberArray value={searchParamsV2.uid} onChange={(v) => { this.updateSearchParamsV2(searchParamsV2, 'uid', v) }} style={{ width: '10em' }} placeholder='双击批量输入' />
                <Divider />
                <Text>YY号:</Text>
                <InputNumberArray value={searchParamsV2.yy} onChange={(v) => { this.updateSearchParamsV2(searchParamsV2, 'yy', v) }} style={{ width: '10em' }} placeholder='双击批量输入' />
                <Divider />
                <Text>变动类型:</Text>
                <Select options={opTypeOptions} value={searchParamsV2.opType} onChange={(v) => this.updateSearchParamsV2(searchParamsV2, 'opType', v)} style={{ width: '6em' }} />
                <Divider /> 
                <Button onClick={() => { onExportExcel(this.toAssessColumns, toAssessList, '复核列表导出.xlsx') }}>导出</Button>
              </Space>
            </Col>
            <Col span={24}>
              <Table rowKey={(r, i) => r.id} columns={this.toAssessColumns} dataSource={this.filterToAssessList(toAssessList, searchParamsV2)} pagination={{
                hideOnSinglePage: true,
                showSizeChanger: true,
                pageSizeOptions: [10, 20, 30, 40, 50, 100],
                size: 'small'
              }} />
            </Col>
          </Row>
        </Modal>
      </div>
    )
  }
}

export default DailyWithdrawalAssess
