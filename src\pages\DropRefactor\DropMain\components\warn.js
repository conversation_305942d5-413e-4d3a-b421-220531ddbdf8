import React, { Component } from 'react'
import { Card, Divider, Button, Modal, Form, Table, Select, InputNumber } from 'antd'
import { connect } from 'dva'
import { getPoolNameByPoolID } from '../../globalConfig'

const namespace = 'dropMain' // model 的 namespace
const FormItem = Form.Item

@connect(({ dropMain }) => ({ // model 的 namespace
  model: dropMain // model 的 namespace
}))
class DropWarnComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false
    }
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getWarnList`
    })
  }

  // 需要修改
  columns = [
    { title: '道具池ID', dataIndex: 'id' },
    { title: '道具池名称', dataIndex: 'id', align: 'center', render: v => { return getPoolNameByPoolID(v) } },
    { title: '每日支出阈值', dataIndex: 'dailyOut', align: 'center', render: v => v.toLocaleString() + ' YB' },
    // { title: '每日物资流水支出阈值', dataIndex: 'dailyFlowOut', align: 'center', render: v => v.toLocaleString() + ' YB' },
    { title: '大额礼物价值', dataIndex: 'bigPrize', align: 'center', render: v => v.toLocaleString() + ' YB' },
    { title: '操作', align: 'center', render: (text, record) => <a style={{ marginRight: 10 }} onClick={this.showModal(true, record)}>编辑</a> }
  ]

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()

      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, isUpdate: isUpdate, title: isUpdate ? '更新规则' : '新建规则' })
  }

  // 隐藏弹窗，不需要修改
  hideModal = () => {
    this.setState({ visible: false })
  }

  onFinish = values => {
    const { dispatch } = this.props

    let uri = `${namespace}/upsetWarn`
    console.log('values=', values)
    dispatch({
      type: uri,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 更新与删除
  handleSubmit = () => {
    this.formRef.submit()
  }

  getOptions = () => {
    const { model: { poolNameOptions } } = this.props
    let options = []
    poolNameOptions.forEach(item => {
      if ([1000, 2000, 3000, 4000, 5000, 7000, 9000, 11000, 13000, 15000, 17000].indexOf(item.value) > -1) { // 过滤超级空投
        options.push(item)
      }
    })
    return options
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  renderCol = () => {
    let opts = this.getOptions()
    return this.columns.map(i => {
      if (i.dataIndex === 'id') {
        i.render = v => opts.find(vv => vv.value === v).label
      }
      return i
    })
  }

  // 列表过滤
  listFilter = (before) => {
    const offlinePoolID = [11000]
    const { poolNameOptions } = this.props.model
    const after = before.filter(item => {
      if (offlinePoolID.indexOf(item.id) >= 0) {
        return false
      }
      return poolNameOptions.some(entry => { return entry.value === item.id })
    })
    return after
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { warnList } } = this.props
    const { visible, title, isUpdate } = this.state
    const formItemLayout = { // 不需要修改
      labelCol: {
        xs: { span: 8 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 16 },
        sm: { span: 16 }
      }
    }

    return (
      <Card>
        <Button onClick={this.showModal(false, this.defaultValue)}>新建预警规则</Button>
        <div>
          <div>{'1. 每日支出 = 普通道具池支出 + 疯狂道具池支出 + 龙宫支出'}</div>
          <div>{'2. 触发条件 = 统计范围内支出 >= 预警阈值 '}</div>
        </div>
        <Divider />

        {/* <Table rowKey={(record, index) => index} dataSource={Array.isArray(warnList) ? warnList.filter(v => v.id !== 2000 && v.id !== 4000) : []} columns={this.columns} pagination={false} /> 显示的列表 */}
        <Table rowKey={(record, index) => index} dataSource={this.listFilter(warnList)} columns={this.columns} pagination={false} />

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} ref={form => { this.formRef = form }} {...formItemLayout}>
            <FormItem name='id' label='道具池'>
              {/* 只显示指定列表中未添加 */}
              <Select options={this.getOptions().filter(v => warnList.map(i => i.id).indexOf(v.value) === -1)} disabled={isUpdate} />
            </FormItem>
            <FormItem label='每日支出阈值/YB' name='dailyOut' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </FormItem>
            {/* <FormItem label='每日物资支出阈值/YB' name='dailyFlowOut' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </FormItem> */}
            <FormItem label='大额礼物价值/YB' name='bigPrize' rules={[{ required: true }]}>
              <InputNumber style={{ width: '100%' }} />
            </FormItem>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default DropWarnComponent
