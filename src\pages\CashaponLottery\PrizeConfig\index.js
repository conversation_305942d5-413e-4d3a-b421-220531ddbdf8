import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
// import dateString from '@/utils/dateString'
import { Table, Form, Card, Modal, Input, Popconfirm, Divider, Button } from 'antd'
import { connect } from 'dva'

const namespace = 'cashaponPrizeConfig'
const FormItem = Form.Item

const getListUri = `${namespace}/getList`
const addItemUri = `${namespace}/addItem`
const updateItemUri = `${namespace}/updateItem`
const removeItemUri = `${namespace}/removeItem`

@connect(({ cashaponPrizeConfig }) => ({
  model: cashaponPrizeConfig
}))
class CashaponPrizeConfig extends Component {
  // column structs.
  columns = [
    { title: '编号', dataIndex: 'sortId', align: 'center' },
    { title: '奖品ID', dataIndex: 'prizeId', align: 'center' },
    { title: '营收ID', dataIndex: 'propId', align: 'center' },
    { title: '奖品名称', dataIndex: 'name', align: 'center' },
    { title: '数量', dataIndex: 'count', align: 'center' },
    { title: '扭蛋种类', dataIndex: 'propType', align: 'center' },
    { title: '价值(紫水晶)', dataIndex: 'price', align: 'center' },
    { title: '概率（扭1次）', dataIndex: 'rate1', align: 'center' },
    { title: '概率（扭10次）', dataIndex: 'rate10', align: 'center' },
    { title: '概率（扭100次）', dataIndex: 'rate100', align: 'center' },
    { title: '概率（扭500次）', dataIndex: 'rate500', align: 'center' },
    { title: '操作选项',
      align: 'center',
      export: false,
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(true, record)}>更新</a><Divider type='vertical' />
          <Popconfirm title='确认删除?' onConfirm={this.handleDel(record.prizeId)} >
            <a href=''>删除</a>
          </Popconfirm>
        </span>)
    }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {} }

  // show modal
  showModal = (isUpdate, record) => () => {
    this.formRef.props.form.resetFields()
    if (record == null) record = {}
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  // add and update
  handleSubmit = () => {
    const form = this.formRef.props.form
    const { dispatch } = this.props
    form.validateFields((err, values) => {
      if (!err) {
        var url = this.state.isUpdate ? updateItemUri : addItemUri
        dispatch({
          type: url,
          payload: values
        })
        form.resetFields()
        this.setState({ visible: false })
      }
    })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { prizeId: key }
    dispatch({
      type: removeItemUri,
      payload: data
    })
  }

  // get list from server.
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: getListUri
    })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  // content
  render () {
    const { route, model: { list } } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Button type='primary' onClick={this.showModal(false)}>添加</Button>
            <Divider />
            <Table dataSource={list} rowKey={(record, index) => index} columns={this.columns} pagination={this.defaultPageValue} size='small' />
          </Form>
        </Card>
        <ItemInfo wrappedComponentRef={this.saveFormRef} {...this.state} onCancel={this.hideModal} onSubmit={this.handleSubmit} />
      </PageHeaderWrapper>
    )
  }
}

@Form.create()
class ItemInfo extends Component {
  render () {
    const { value, isUpdate, visible, title, onCancel, onSubmit, form } = this.props
    const { getFieldDecorator } = form
    const formItemLayout = {
      labelCol: {
        xs: { span: 6 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 13 },
        sm: { span: 17 }
      }
    }

    return (
      <Modal visible={visible} title={title} onCancel={onCancel} onOk={onSubmit}>
        <Form >
          <FormItem {...formItemLayout} label='编号'>
            {getFieldDecorator('sortId', {
              initialValue: value.sortId,
              rules: [{ required: true, message: '编号不能为空' }]
            })(<Input placeholder='必须为非负整数' readOnly={isUpdate} />)}
          </FormItem>
          <FormItem {...formItemLayout} label='奖品ID'>
            {getFieldDecorator('prizeId', {
              initialValue: value.prizeId
            })(<Input readOnly disabled />)}
          </FormItem>
          <FormItem {...formItemLayout} label='营收ID'>
            {getFieldDecorator('propId', {
              initialValue: value.propId,
              rules: [{ required: true, message: '营收ID不能为空' }]
            })(<Input placeholder='必须非负整数(0-白水晶 1-紫水晶 24-魔豆)' />)}
          </FormItem>
          <FormItem {...formItemLayout} label='奖品名称'>
            {getFieldDecorator('name', {
              initialValue: value.name,
              rules: [{ required: true, message: '奖品名称不能为空' }]
            })(<Input placeholder='不能为空' />)}
          </FormItem>
          <FormItem {...formItemLayout} label='数量'>
            {getFieldDecorator('count', {
              initialValue: value.count,
              rules: [{ required: true, message: '数量不能为空' }]
            })(<Input placeholder='必须正整数' />)}
          </FormItem>
          <FormItem {...formItemLayout} label='扭蛋种类'>
            {getFieldDecorator('propType', {
              initialValue: value.propType,
              rules: [{ required: true, message: '扭蛋种类不能为空' }]
            })(<Input placeholder='1：蓝 2：红 3：黄 4：紫' />)}
          </FormItem>
          <FormItem {...formItemLayout} label='价值(紫水晶)'>
            {getFieldDecorator('price', {
              initialValue: value.price,
              rules: [{ required: true, message: '价值不能为空' }]
            })(<Input placeholder='必须非负整数' />)}
          </FormItem>
          <FormItem {...formItemLayout} label='概率（扭1次）'>
            {getFieldDecorator('rate1', {
              initialValue: value.rate1,
              rules: [{ required: true, message: '概率不能为空' }]
            })(<Input placeholder='必须非负整数(0表示当前概率下不发放)' />)}
          </FormItem>
          <FormItem {...formItemLayout} label='概率（扭10次）'>
            {getFieldDecorator('rate10', {
              initialValue: value.rate10,
              rules: [{ required: true, message: '概率不能为空' }]
            })(<Input placeholder='必须非负整数(0表示当前概率下不发放)' />)}
          </FormItem>
          <FormItem {...formItemLayout} label='概率（扭100次）'>
            {getFieldDecorator('rate100', {
              initialValue: value.rate100,
              rules: [{ required: true, message: '概率不能为空' }]
            })(<Input placeholder='必须非负整数(0表示当前概率下不发放)' />)}
          </FormItem>
          <FormItem {...formItemLayout} label='概率（扭500次）'>
            {getFieldDecorator('rate500', {
              initialValue: value.rate500,
              rules: [{ required: true, message: '概率不能为空' }]
            })(<Input placeholder='必须非负整数(0表示当前概率下不发放)' />)}
          </FormItem>
        </Form>
      </Modal>
    )
  }
}

export default CashaponPrizeConfig
