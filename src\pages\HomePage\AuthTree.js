import React, { Component } from 'react'
import { Tree } from 'antd'

const TreeNode = Tree.TreeNode

class AuthTree extends Component {
   state = {}

  componentDidMount = () => {} 

  onCheck = (checkedKeys) => {
    const filteredCheckedKeys = checkedKeys 
    const onChange = this.props.onChange
    // if (filteredCheckedKeys.length > 20) {
    //   message.warn('单次申请限制最多20个')
    //   return
    // }
    if (onChange) {
      onChange(filteredCheckedKeys)
    }
  }

  renderTreeNodes = (data) => {
    return data.map((item, index) => {
      const { uri } = item
      let realUri = item.id
      if (uri) {
        realUri = (uri.indexOf('/') === 0 ? uri : '/' + uri).replace('.html', '')
      }
      const props = {
        title: item.name,
        key: realUri,
        dataRef: item,
        selectable: false
      }
      if (item.children) {
        return (
          <TreeNode {...props}>
            {this.renderTreeNodes(item.children)}
          </TreeNode>
        )
      }
      return <TreeNode {...props} />
    })
  }

  render () {
    const { treeData, selectURI } = this.props 
    return (
      <Tree
        defaultExpandAll
        checkable
        autoExpandParent
        // onExpand={this.onExpand}
        // expandedKeys={expandedKeys}
        defaultExpandedKeys={selectURI}
        // autoExpandParent={this.state.autoExpandParent}
        onCheck={this.onCheck}
        checkedKeys={selectURI}
        // onSelect={(v, v2) => { console.debug('v===>', v, v2) }}
        // selectedKeys={this.state.selectedKeys}
      >
        {this.renderTreeNodes(treeData)}
      </Tree>
    )
  }
}

export default AuthTree
