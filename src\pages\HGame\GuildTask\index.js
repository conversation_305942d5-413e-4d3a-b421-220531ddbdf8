import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Table, Tabs, Input, Select, Button, Modal, InputNumber, message, DatePicker, Divider, Popconfirm, Form, Row, Col } from 'antd'
import { connect } from 'dva'
import { exportExcel } from 'xlsx-oc'

const namespace = 'guildTaskRewardApproval'
const getRewardApprovalListURL = `${namespace}/getRewardApprovalList`
const getRewardApprovalListReviewURL = `${namespace}/getRewardApprovalListReview`
const getRewardRealSendListURL = `${namespace}/getRewardRealSendList`
const rewardApproval1URL = `${namespace}/rewardApproval1`
const rewardApproval3URL = `${namespace}/rewardApproval3`
const upsertReferMonthURL = `${namespace}/upsertReferMonth`
const getReferMonthListURL = `${namespace}/getReferMonthList`
const deleteReferMonthURL = `${namespace}/deleteReferMonth`

const listReferPeriodURL = `${namespace}/listReferPeriod`
const addReferPeriodURL = `${namespace}/addReferPeriod`
const updateReferPeriodURL = `${namespace}/updateReferPeriod`
const deleteReferPeriodURL = `${namespace}/deleteReferPeriod`

const listRewardSummaryURL = `${namespace}/listRewardSummary`

const Option = Select.Option

// 审批状态
// const approvalStatusNotDeal = 1
// const approvalStatusPass = 2
const approvalStatusReject = 3
// const approvalFirstGoing = 4
// const approvalSecondGoing = 5
const approvalStatusMap = { 1: '未审批', 2: '审批通过', 3: '审批不通过', 4: '一审中', 5: '一审通过，二审中' }
const approvalStatusList = [{ index: 0, name: '全部' }, { index: 1, name: '未审批' }, { index: 2, name: '审批通过' }, { index: 3, name: '审批不通过' }, { index: 4, name: '一审中' }, { index: 5, name: '一审通过，二审中' }]

// 是否下调奖励
const isDownRewardNotDeal = 1
const isDownRewardYes = 2
const isDownRewardNo = 3
const isDownRewardMap = { 0: '全部', 1: '未处理', 2: '是', 3: '否' }
const isDownRewardList = [{ index: 0, name: '全部' }, { index: 1, name: '未处理' }, { index: 2, name: '是' }, { index: 3, name: '否' }]
const isDownRewardUpdateList = [{ index: 2, name: '是' }, { index: 3, name: '否' }]

// 复核状态
const reviewStatusMap = { 0: '全部', 1: '未处理', 2: '已处理' }
const reviewStatusList = [{ index: 0, name: '全部' }, { index: 1, name: '未处理' }, { index: 2, name: '已处理' }]

const taskTypeMap = { 0: '-', 1: '阶梯任务', 2: '进步任务', 3: '连续进步任务' }

const pageSize = 20

var moment = require('moment')
var dateFormat = 'YYYY-MM-DD hh:mm:ss'

@connect(({ guildTaskRewardApproval }) => ({
  model: guildTaskRewardApproval
}))

// 2022-01-17 应产品要求, 将出现的关键字"奖励"改成"扶持"
class GuildTaskRewardApproval extends Component {
  constructor (props) {
    super(props)

    this.refreshCurRewardApprovalList()
  }

  // 当月复核页面
  configColumns = [
    { title: '任务时间', dataIndex: 'month', align: 'center', fixed: 'left' },
    { title: '短位ID', dataIndex: 'asid', align: 'center', fixed: 'left', render: (text, record) => (text = text === 0 ? '' : text) },
    { title: '当月礼物流水/元', dataIndex: 'monthAmount', align: 'center' },
    { title: '参考月份礼物流水/元', dataIndex: 'referAmount', align: 'center' },
    { title: '阶梯任务最低流水值/元', dataIndex: 'stepMinAmount', align: 'center' },
    { title: '当月公会星级', dataIndex: 'star', align: 'center' },
    { title: '任务达标类型', dataIndex: 'taskType', align: 'center', render: (text, record) => (taskTypeMap[text]) },
    { title: '达标任务', dataIndex: 'reachStandardStr', align: 'center' },
    { title: '系统结算-发放扶持/元', dataIndex: 'expectReward', align: 'center' },
    { title: '是否下调扶持', dataIndex: 'isDownReward', align: 'center', render: (text, record) => (text === isDownRewardNotDeal ? <font color='red'>{isDownRewardMap[text]}</font> : <font>{isDownRewardMap[text]}</font>) },
    { title: '下调后发放 - 发放扶持/元', dataIndex: 'realReward', align: 'center', render: (text, record) => (text = text === 0 ? '' : text) },
    { title: '下调金额/元', dataIndex: 'downReward', align: 'center', render: (text, record) => (text = text === 0 ? '' : text) },
    { title: '下调原因', dataIndex: 'downReason', align: 'center' },
    { title: '复核状态', dataIndex: 'reviewStatus', align: 'center', render: (text, record) => (reviewStatusMap[text]) },
    { title: '审批状态', dataIndex: 'approvalStatus', align: 'center', render: (text, record) => (text === approvalStatusReject ? <font color='red'>{approvalStatusMap[text]}</font> : <font>{approvalStatusMap[text]}</font>) },
    { title: '驳回原因', dataIndex: 'approvalRejectReason', align: 'center' }
  ]

  // 修改复核页面
  configUpdateColumns1 = [
    { title: '序号', dataIndex: 'idx', align: 'center', fixed: 'left' },
    { title: '任务时间', dataIndex: 'month', align: 'center', fixed: 'left' },
    { title: '短位ID', dataIndex: 'asid', align: 'center', fixed: 'left', render: (text, record) => (text = text === 0 ? '' : text) },
    { title: '当月礼物流水/元', dataIndex: 'monthAmount', align: 'center' },
    { title: '参考月份礼物流水/元', dataIndex: 'referAmount', align: 'center' },
    { title: '阶梯任务最低流水值/元', dataIndex: 'stepMinAmount', align: 'center' },
    { title: '当月公会星级', dataIndex: 'star', align: 'center' },
    { title: '任务达标类型', dataIndex: 'taskType', align: 'center', render: (text, record) => (taskTypeMap[text]) },
    { title: '达标任务', dataIndex: 'reachStandardStr', align: 'center' },
    { title: '系统结算-发放扶持/元', dataIndex: 'expectReward', align: 'center' },
    { title: '是否下调扶持', dataIndex: 'isDownReward', align: 'center', render: (text, record) => (<Select onChange={this.handleRowChange11(record, 'isDownReward')} defaultValue={isDownRewardMap[record.isDownReward]} value={isDownRewardMap[record.isDownReward]} placeholder='请选择' style={{ width: 100 }}>{isDownRewardUpdateList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}</Select>) },
    {
      title: '下调后发放 - 发放扶持/元',
      dataIndex: 'realReward',
      align: 'center',
      render: (text, record) => {
        if (record.isDownReward === isDownRewardNo) {
          record.realReward = record.expectReward
          text = record.realReward
          return <span><InputNumber min={0} max={record.expectReward} disabled={record.isDownReward === isDownRewardNo} defaultValue={record.realReward} value={record.realReward} onChange={this.handleRowChange11(record, 'realReward')} /><font color='red' style={{ marginLeft: 5 }}>元</font></span>
        }
        return <span><InputNumber min={0} max={record.expectReward} disabled={record.isDownReward === isDownRewardNo} defaultValue={record.realReward} value={record.realReward} onChange={this.handleRowChange11(record, 'realReward')} /><font color='red' style={{ marginLeft: 5 }}>元</font></span>
      }
    },
    { title: '下调金额/元', dataIndex: 'downReward', align: 'center', render: (text, record) => (text = record.isDownReward === isDownRewardNo ? 0 : record.isDownReward === isDownRewardYes && record.realReward > 0 ? record.expectReward - record.realReward : text) },
    { title: '下调原因', dataIndex: 'downReason', align: 'center', render: (text, record) => (<Input defaultValue={record.downReason} value={record.downReason} onChange={this.handleRowChange12(record, 'downReason')} />) }
  ]

  // 历史页面
  configUpdateColumns3 = [
    { title: '任务时间', dataIndex: 'month', align: 'center', fixed: 'left' },
    { title: '短位ID', dataIndex: 'asid', align: 'center', fixed: 'left', render: (text, record) => (text = text === 0 ? '' : text) },
    { title: '当月礼物流水/元', dataIndex: 'monthAmount', align: 'center' },
    { title: '参考月份礼物流水/元', dataIndex: 'referAmount', align: 'center' },
    { title: '阶梯任务最低流水值/元', dataIndex: 'stepMinAmount', align: 'center' },
    { title: '当月公会星级', dataIndex: 'star', align: 'center' },
    { title: '任务达标类型', dataIndex: 'taskType', align: 'center', render: (text, record) => (taskTypeMap[text]) },
    { title: '达标任务', dataIndex: 'reachStandardStr', align: 'center' },
    { title: '系统结算-发放扶持/元', dataIndex: 'expectReward', align: 'center' },
    { title: '是否下调扶持', dataIndex: 'isDownReward', align: 'center', render: (text, record) => (<Select onChange={this.handleRowChange13(record, 'isDownReward')} defaultValue={isDownRewardMap[record.isDownReward]} value={isDownRewardMap[record.isDownReward]} placeholder='请选择' style={{ width: 100 }}>{isDownRewardUpdateList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}</Select>) },
    {
      title: '下调后发放 - 发放扶持/元',
      dataIndex: 'realReward',
      align: 'center',
      render: (text, record) => {
        if (record.isDownReward === isDownRewardNo) {
          record.realReward = record.expectReward
          text = record.realReward
          return <span><InputNumber min={0} max={record.expectReward} disabled={record.isDownReward === isDownRewardNo} defaultValue={record.realReward} value={record.realReward} onChange={this.handleRowChange13(record, 'realReward')} /><font color='red' style={{ marginLeft: 5 }}>元</font></span>
        }
        return <span><InputNumber min={0} max={record.expectReward} disabled={record.isDownReward === isDownRewardNo} defaultValue={record.realReward} value={record.realReward} onChange={this.handleRowChange13(record, 'realReward')} /><font color='red' style={{ marginLeft: 5 }}>元</font></span>
      }
    },
    { title: '下调金额/元', dataIndex: 'downReward', align: 'center', render: (text, record) => (text = record.isDownReward === isDownRewardNo ? 0 : record.isDownReward === isDownRewardYes && record.realReward > 0 ? record.expectReward - record.realReward : text) },
    { title: '下调原因', dataIndex: 'downReason', align: 'center', render: (text, record) => (<Input defaultValue={record.downReason} value={record.downReason} onChange={this.handleRowChange23(record, 'downReason')} />) }
  ]

  // 最终发奖记录
  lastRewardColumns = [
    { title: '任务时间', dataIndex: 'month', align: 'center', fixed: 'left' },
    { title: '短位ID', dataIndex: 'asid', align: 'center', fixed: 'left' },
    { title: '当月礼物流水/元', dataIndex: 'monthAmount', align: 'center' },
    { title: '参考月份礼物流水/元', dataIndex: 'referAmount', align: 'center' },
    { title: '阶梯任务最低流水值/元', dataIndex: 'stepMinAmount', align: 'center' },
    { title: '当月公会星级', dataIndex: 'star', align: 'center' },
    { title: '任务达标类型', dataIndex: 'taskType', align: 'center', render: (text, record) => (taskTypeMap[text]) },
    { title: '达标任务', dataIndex: 'reachStandardStr', align: 'center' },
    { title: '系统结算-发放扶持/元', dataIndex: 'expectReward', align: 'center' },
    { title: '是否下调扶持', dataIndex: 'isDownReward', align: 'center', render: (text, record) => (text === isDownRewardNotDeal ? <font color='red'>{isDownRewardMap[text]}</font> : <font>{isDownRewardMap[text]}</font>) },
    { title: '下调后发放 - 发放扶持/元', dataIndex: 'realReward', align: 'center', render: (text, record) => (text = text === 0 ? '' : text) },
    { title: '下调金额/元', dataIndex: 'downReward', align: 'center', render: (text, record) => (text = text === 0 ? '' : text) },
    { title: '下调原因', dataIndex: 'downReason', align: 'center' },
    { title: '最终发放扶持/元', dataIndex: 'lastRealReward', align: 'center' },
    { title: '复核状态', dataIndex: 'reviewStatus', align: 'center', render: (text, record) => (reviewStatusMap[text]) },
    { title: '审批状态', dataIndex: 'approvalStatus', align: 'center', render: (text, record) => (text === approvalStatusReject ? <font color='red'>{approvalStatusMap[text]}</font> : <font>{approvalStatusMap[text]}</font>) }
  ]

  // 指定参考月份页面
  referMonthColumns = [
    { title: '指定月份', dataIndex: 'month', align: 'center' },
    { title: '参考月份', dataIndex: 'referMonth', align: 'center' },
    { title: '操作人', dataIndex: 'username', align: 'center' },
    {
      title: '更新时间',
      dataIndex: 'timestamp',
      align: 'center',
      render: (text, record) => {
        if (record.timestamp > 0) {
          return moment(record.timestamp * 1000).format(dateFormat)
        }
        return ''
      }
    },
    { title: '操作', key: 'operation', align: 'center', width: 120, render: (text, record) => (<span><a size='small' type='primary' onClick={this.showModal(record)}>修改</a><Divider type='vertical' /><Popconfirm title='确认删除?' onConfirm={this.deleteReferMonth(record.id)}><a >删除</a></Popconfirm></span>)
    }
  ]

  // 指定参考周期页面
  referMonthColumns = [
    { title: '任务时间', dataIndex: 'month', align: 'center' },
    { title: '周期一', dataIndex: 'period', align: 'center', render: (text, record) => (this.updatePeriodDesc(record.period[0])) },
    { title: '周期二', dataIndex: 'period', align: 'center', render: (text, record) => (this.updatePeriodDesc(record.period[1])) },
    { title: '周期三', dataIndex: 'period', align: 'center', render: (text, record) => (this.updatePeriodDesc(record.period[2])) },
    { title: '审批状态', dataIndex: 'approvalStatus', align: 'center', render: (text, record) => (text === approvalStatusReject ? <font color='red'>{approvalStatusMap[text]}</font> : <font>{approvalStatusMap[text]}</font>) },
    {
      title: '配置时间',
      dataIndex: 'timestamp',
      align: 'center',
      render: (text, record) => {
        if (record.timestamp > 0) {
          return moment(record.timestamp * 1000).format(dateFormat)
        }
        return ''
      }
    },
    { title: '备注', dataIndex: 'approvalReason', align: 'center' },
    { title: '操作人', dataIndex: 'optUser', align: 'center' },
    { title: '操作', key: 'operation', align: 'center', width: 120, render: (text, record) => (<span><a size='small' type='primary' onClick={this.showReferPeriodModal(record, false)}>修改</a><Divider type='vertical' /><Popconfirm title='确认删除?' onConfirm={this.deleteReferperiod(record.month)}><a >删除</a></Popconfirm></span>)
    }
  ]

  // 发奖汇总页面
  rewardSummaryColumns = [
    { title: '月份', dataIndex: 'month', align: 'center' },
    { title: '交友全月礼物流水/元', dataIndex: 'jyMonthAmount', align: 'center', render: (text, record) => (this.formatNumberQian(parseInt(record.jyMonthAmount))) },
    { title: '公会任务礼物流水/元', dataIndex: 'guildTaskPeriodAmount', align: 'center', render: (text, record) => (this.formatNumberQian(parseInt(record.guildTaskPeriodAmount))) },
    { title: '阶梯扶持/万', dataIndex: 'stepTaskReward', align: 'center', render: (text, record) => (record.stepTaskReward / 10000) },
    { title: '进步扶持/万', dataIndex: 'progressTaskReward', align: 'center', render: (text, record) => (record.progressTaskReward / 10000) },
    { title: '连续进步扶持/万', dataIndex: 'continueTaskReward', align: 'center', render: (text, record) => (record.continueTaskReward / 10000) },
    { title: '合计/万', dataIndex: 'taskReward', align: 'center', render: (text, record) => (record.taskReward / 10000) }
  ]

  defaultPageValue = {
    defaultPageSize: pageSize,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  state = {
    visible1: false,
    visible3: false,
    isUpdateReferPeriod: false,
    referPeriod1: null,
    referPeriod2: null,
    referPeriod3: null,
    referMonth: ''
  }

  onTagChange = (record) => {
    if (record === '1') {
      this.refreshCurRewardApprovalList()
    } else if (record === '2') {
      this.refreshRewardRealSendList()
    } else if (record === '3') {
      this.refreshHistoryRewardApprovalList()
    } else if (record === '4') {
      this.refreshReferMonthList()
    } else if (record === '5') {
      this.refreshRefePeriodList()
    } else if (record === '6') {
      this.refreshRewardSummaryList()
    }
  }

  formatNumberQian = (num) => {
    var reg = /\d{1,3}(?=(\d{3})+$)/g
    return (num + '').replace(reg, '$&,')
  }

  refreshCurRewardApprovalList = () => {
    const { searchReviewSid1, searchReviewSSid1, searchReviewIsDownReward1, searchReviewReviewStatus1, searchReviewApprovalStatus1 } = this.state
    let data = { tabIdx: 1, sid: searchReviewSid1, ssid: searchReviewSSid1, isDownReward: searchReviewIsDownReward1, reviewStatus: searchReviewReviewStatus1, approvalStatus: searchReviewApprovalStatus1 }

    this.props.dispatch({
      type: getRewardApprovalListURL,
      payload: data
    })
  }

  refreshCurRewardApprovalListReview = () => {
    this.props.dispatch({
      type: getRewardApprovalListReviewURL,
      payload: { tabIdx: 1 }
    })
  }

  refreshHistoryRewardApprovalList = () => {
    const { searchReviewSid3, searchReviewSSid3, searchReviewIsDownReward3, searchReviewReviewStatus3, searchReviewApprovalStatus3 } = this.state
    let data = { tabIdx: 3, sid: searchReviewSid3, ssid: searchReviewSSid3, isDownReward: searchReviewIsDownReward3, reviewStatus: searchReviewReviewStatus3, approvalStatus: searchReviewApprovalStatus3 }

    this.props.dispatch({
      type: getRewardApprovalListURL,
      payload: data
    })
  }

  refreshHistoryRewardApprovalListReview = () => {
    this.props.dispatch({
      type: getRewardApprovalListReviewURL,
      payload: { tabIdx: 3 }
    })
  }

  refreshRewardRealSendList = () => {
    const { searchMonth2, searchReviewSid2, searchReviewSSid2, searchReviewIsDownReward2, searchReviewReviewStatus2, searchReviewApprovalStatus2 } = this.state
    let searchMonthTmp = ''
    if (searchMonth2 !== undefined && searchMonth2 !== null && searchMonth2 !== '') {
      searchMonthTmp = searchMonth2.format('YYYYMM')
    }

    let data = { month: searchMonthTmp, sid: searchReviewSid2, ssid: searchReviewSSid2, isDownReward: searchReviewIsDownReward2, reviewStatus: searchReviewReviewStatus2, approvalStatus: searchReviewApprovalStatus2 }

    this.props.dispatch({
      type: getRewardRealSendListURL,
      payload: data
    })
  }

  refreshReferMonthList = () => {
    this.props.dispatch({
      type: getReferMonthListURL,
      payload: {}
    })
  }

  refreshRefePeriodList = () => {
    const { searchMonth } = this.state
    let month = ''
    if (searchMonth) {
      month = moment(searchMonth, 'YYYY-MM').format('YYYY-MM')
    }

    this.props.dispatch({
      type: listReferPeriodURL,
      payload: { month: month }
    })
  }

  refreshRewardSummaryList = () => {
    this.props.dispatch({
      type: listRewardSummaryURL,
      payload: {}
    })
  }

  updatePeriodDesc = (period) => {
    let startTime = moment(period, 'YYYY-MM').add(-1, 'months').format('YYYY-MM') + '-28'
    let endTime = moment(period, 'YYYY-MM-DD').add(0, 'months').format('YYYY-MM') + '-27'
    return <span><div>{period}</div><div>({startTime} 至 {endTime})</div></span>
  }

  // 编辑复核信息
  handleRowChange11 = (record, field) => value => {
    const { visible1 } = this.state
    if (!visible1) {
      console.log('not update page')
      return
    }
    var { model: { list } } = this.props
    let index = record.idx - 1
    if (index < 0) {
      return
    }
    console.log(index, field, value)

    if (field === 'realReward' && record['isDownReward'] === isDownRewardYes && value > record['expectReward']) {
      message.warn('已超过系统结算金额')
      return
    }

    list[index][field] = value
    record[field] = value

    // 调整金额&&下调-修正数据
    if (field === 'realReward' && record['isDownReward'] === isDownRewardYes) {
      if (record['realReward'] > record['expectReward']) {
        record['realReward'] = record['expectReward']
      }
      if (record['realReward'] < 0) {
        record['realReward'] = 0
      }
      let downReward = record['expectReward'] - record['realReward']
      record['downReward'] = downReward
      list[index]['downReward'] = downReward
    }

    // 下调-系统结算为0
    if (field === 'isDownReward' && value === isDownRewardYes) {
      if (record['expectReward'] === 0) {
        list[index]['realReward'] = 0
        record['realReward'] = 0

        record['downReward'] = 0
        list[index]['downReward'] = 0

        record['downReason'] = ''
        list[index]['downReason'] = ''
      }
    }

    // 不下调
    if (field === 'isDownReward' && value === isDownRewardNo) {
      let expectReward = record['expectReward']
      record['realReward'] = expectReward
      list[index]['realReward'] = expectReward

      let downReward = record['expectReward'] - record['realReward']
      record['downReward'] = downReward
      list[index]['downReward'] = downReward

      record['downReason'] = ''
      list[index]['downReason'] = ''
    }
    this.forceUpdate() // 强制刷新
  }

  handleRowChange12 = (record, field) => e => {
    const { visible1 } = this.state
    if (!visible1) {
      console.log('not update page')
      return
    }
    var { model: { list } } = this.props
    let index = record.idx - 1
    if (index < 0) {
      return
    }
    list[index][field] = e.target.value
    record[field] = e.target.value
    this.forceUpdate() // 强制刷新
  }

  // 编辑复核信息
  handleRowChange13 = (record, field) => value => {
    const { visible3 } = this.state
    if (!visible3) {
      console.log('not update page')
      return
    }
    var { model: { list } } = this.props
    let index = record.idx - 1
    if (index < 0) {
      return
    }
    console.log(index, field, value)

    if (field === 'realReward' && record['isDownReward'] === isDownRewardYes && value > record['expectReward']) {
      message.warn('已超过系统结算金额')
      return
    }

    list[index][field] = value
    record[field] = value

    // 调整金额&&下调-修正数据
    if (field === 'realReward' && record['isDownReward'] === isDownRewardYes) {
      if (record['realReward'] > record['expectReward']) {
        record['realReward'] = record['expectReward']
      }
      if (record['realReward'] < 0) {
        record['realReward'] = 0
      }
      let downReward = record['expectReward'] - record['realReward']
      record['downReward'] = downReward
      list[index]['downReward'] = downReward
    }

    // 下调-系统结算为0
    if (field === 'isDownReward' && value === isDownRewardYes) {
      if (record['expectReward'] === 0) {
        list[index]['realReward'] = 0
        record['realReward'] = 0

        record['downReward'] = 0
        list[index]['downReward'] = 0

        record['downReason'] = ''
        list[index]['downReason'] = ''
      }
    }

    // 不下调
    if (field === 'isDownReward' && value === isDownRewardNo) {
      let expectReward = record['expectReward']
      record['realReward'] = expectReward
      list[index]['realReward'] = expectReward

      let downReward = record['expectReward'] - record['realReward']
      record['downReward'] = downReward
      list[index]['downReward'] = downReward

      record['downReason'] = ''
      list[index]['downReason'] = ''
    }
    this.forceUpdate() // 强制刷新
  }

  handleRowChange23 = (record, field) => e => {
    const { visible3 } = this.state
    if (!visible3) {
      console.log('not update page')
      return
    }
    var { model: { list } } = this.props
    let index = record.idx - 1
    if (index < 0) {
      return
    }
    list[index][field] = e.target.value
    record[field] = e.target.value
    this.forceUpdate() // 强制刷新
  }

  showModal = (record) => () => {
    if (this.formRefReferMonth) {
      record.monthNew = moment(record.month, 'YYYYMM')
      record.referMonthNew = moment(record.referMonth, 'YYYYMM')
      this.formRefReferMonth.setFieldsValue(record)
    }
    this.setState({ visibleModel: true })
  }

  showReferPeriodModal = (record, isAdd) => () => {
    let month = ''
    let referPeriod1 = ''
    let referPeriod2 = ''
    let referPeriod3 = ''
    let referMonth = ''
    if (isAdd) {
      let d = new Date()
      month = moment(d, 'YYYY-MM').add(1, 'months')
      referPeriod1 = moment(month, 'YYYY-MM').add(-3, 'months')
      referPeriod2 = moment(month, 'YYYY-MM').add(-2, 'months')
      referPeriod3 = moment(month, 'YYYY-MM').add(-1, 'months')
    } else {
      month = moment(record.month, 'YYYY-MM')
      referPeriod1 = moment(record.period[0], 'YYYY-MM')
      referPeriod2 = moment(record.period[1], 'YYYY-MM')
      referPeriod3 = moment(record.period[2], 'YYYY-MM')
    }

    referMonth = month
    if (this.formRefReferPeriod) {
      this.formRefReferPeriod.setFieldsValue({ month: month, period1: referPeriod1, period2: referPeriod2, period3: referPeriod3 })
    }

    this.setState({ visibleReferPeriodModel: true, isUpdateReferPeriod: !isAdd, referPeriod1: referPeriod1, referPeriod2: referPeriod2, referPeriod3: referPeriod3, referMonth: referMonth })
  }

  deleteReferMonth = (id) => () => {
    console.log(id)
    this.props.dispatch({
      type: deleteReferMonthURL,
      payload: { id: id }
    })
  }

  deleteReferperiod = (month) => () => {
    console.log(month)
    this.props.dispatch({
      type: deleteReferPeriodURL,
      payload: { month: month }
    })
  }

  // 表格处理函数
  // 查询当月发奖复核管理
  searchRewardHandle1 = () => () => {
    this.refreshCurRewardApprovalList()
  }

  searchRewardHandle3 = () => () => {
    this.refreshHistoryRewardApprovalList()
  }

  // 查询最终奖励发放记录
  searchRewardRealSendHandle = () => () => {
    this.refreshRewardRealSendList()
  }

  onExportHandle = (idx) => () => {
    const { selectedRowKeys } = this.state
    var selectMap = {}
    var size = 0
    for (let i = 0; i < selectedRowKeys.length; i++) {
      size += 1
      selectMap[selectedRowKeys[i]] = true
    }

    const { model: { list } } = this.props
    var listNew = []
    if (size === pageSize) {
      listNew = list
    } else {
      for (let i = 0; i < list.length; i++) {
        if (selectMap[list[i].idx - 1]) {
          listNew.push(list[i])
        }
      }
    }

    let exportData = listNew.map(item => {
      item.taskType = taskTypeMap[item.taskType]
      item.isDownReward = isDownRewardMap[item.isDownReward]
      item.reviewStatus = reviewStatusMap[item.reviewStatus]
      item.approvalStatus = approvalStatusMap[item.approvalStatus]
      return item
    })

    let exportHeader = []
    if (idx === 1) {
      this.configColumns.forEach((col) => {
        if (col.export === undefined || col.export) {
          exportHeader.push({ k: col.dataIndex, v: col.title })
        }
      })
      exportExcel(exportHeader, exportData, '星级公会任务-本周期发奖复核管理.xlsx')
    } else if (idx === 2) {
      this.lastRewardColumns.forEach((col) => {
        if (col.export === undefined || col.export) {
          exportHeader.push({ k: col.dataIndex, v: col.title })
        }
      })
      exportExcel(exportHeader, exportData, '星级公会任务-最终奖励发放记录.xlsx')
    } else if (idx === 3) {
      this.configUpdateColumns3.forEach((col) => {
        if (col.export === undefined || col.export) {
          exportHeader.push({ k: col.dataIndex, v: col.title })
        }
      })
      exportExcel(exportHeader, exportData, '星级公会任务-历史发奖复核管理.xlsx')
    }
  }

  // 当月发奖复核
  showModel1 = () => () => {
    this.refreshCurRewardApprovalListReview()
    const { model: { list } } = this.props
    if (list.length === 0) {
      message.info('无审批记录')
      return
    }
    this.forceUpdate() // 强制刷新
    this.setState({ visible1: true, editable: true })
  }

  hiddenModal1 = () => {
    // window.location.reload(true)
  }

  // 历史发奖复核
  showModel3 = () => () => {
    this.refreshHistoryRewardApprovalListReview()
    const { model: { list } } = this.props
    if (list.length === 0) {
      message.info('无审批记录')
      return
    }
    this.forceUpdate() // 强制刷新
    this.setState({ visible3: true, editable: true })
  }

  hiddenModal3 = () => {
    // window.location.reload(true)
  }

  handleCancel1 = e => {
    // this.hiddenModal1()
    this.refreshCurRewardApprovalList()
    this.forceUpdate() // 强制刷新
    this.setState({ visible1: false, editable: false })
  }

  handleSubmit1 = e => {
    const { dispatch, model: { list } } = this.props
    var listTmp = JSON.parse(JSON.stringify(list))
    for (let i = 0; i < listTmp.length; i++) {
      const element = listTmp[i]
      let idx = i + 1
      if (element.isDownReward === isDownRewardNotDeal) {
        message.warn('序号:' + idx + '未选择是否调奖励')
        return
      }

      if (element.isDownReward === isDownRewardNo) {
        if (element.downReason !== '') {
          message.warn('序号:' + idx + '不下调, 下调原因必须为空')
          return
        }
      }

      if (element.isDownReward === isDownRewardYes) {
        if (element.realReward > element.expectReward) {
          message.warn('序号:' + idx + '下调发放金额比结算金额大')
          return
        }

        if (element.realReward < 0) {
          message.warn('序号:' + idx + '选择下调奖励, 但没下调')
          return
        }

        if (element.downReason === '') {
          message.warn('序号:' + idx + '没有填写下调原因')
          return
        }
      }
    }
    dispatch({
      type: rewardApproval1URL,
      payload: listTmp
    })
    console.log(listTmp)
    this.refreshCurRewardApprovalList()
    this.forceUpdate() // 强制刷新
    this.setState({ visible1: false, editable: false })
  }

  handleCancel3 = e => {
    // this.hiddenModal3()
    this.refreshHistoryRewardApprovalList()
    this.forceUpdate() // 强制刷新
    this.setState({ visible3: false, editable: false })
  }

  handleSubmit3 = e => {
    const { dispatch, model: { list } } = this.props
    var listTmp = JSON.parse(JSON.stringify(list))
    for (let i = 0; i < listTmp.length; i++) {
      const element = listTmp[i]
      let idx = i + 1
      if (element.isDownReward === isDownRewardNotDeal) {
        message.warn('序号:' + idx + '未选择是否调奖励')
        return
      }

      if (element.isDownReward === isDownRewardYes) {
        if (element.realReward > element.expectReward) {
          message.warn('序号:' + idx + '下调发放金额比结算金额大')
          return
        }

        if (element.realReward < 0) {
          message.warn('序号:' + idx + '选择下调奖励, 但没下调')
          return
        }

        if (element.downReason === '') {
          message.warn('序号:' + idx + '没有填写下调原因')
          return
        }
      }
    }
    dispatch({
      type: rewardApproval3URL,
      payload: listTmp
    })
    console.log(listTmp)
    this.refreshHistoryRewardApprovalList()
    this.forceUpdate() // 强制刷新
    this.setState({ visible3: false, editable: false })
  }

  saveFormRefReferMonth = (formRefReferMonth) => {
    this.formRefReferMonth = formRefReferMonth
  }

  handleCancelReferMonth = e => {
    if (this.formRefReferMonth) {
      this.formRefReferMonth.resetFields()
    }
    this.hiddenModalReferMonth()
  }

  handleSubmitReferMonth = e => {
    if (this.formRefReferMonth) {
      this.formRefReferMonth.submit()
    }
    this.hiddenModalReferMonth()
  }

  hiddenModalReferMonth = () => {
    this.setState({ visibleModel: false })
  }

  onFinish = values => {
    console.log(values)
    let data = values
    if (data === undefined || data === null || data.monthNew === undefined || data.referMonthNew === undefined) {
      message.warn('必须成对选择')
      return
    }

    if (data.monthNew.unix() <= data.referMonthNew.unix()) {
      message.warn('参考月份必须小于月份')
      return
    }

    let addmonthTmp = data.monthNew.format('YYYYMM')
    let addReferMonthTmp = data.referMonthNew.format('YYYYMM')
    console.log({ id: data.id, month: addmonthTmp, referMonth: addReferMonthTmp })
    this.props.dispatch({
      type: upsertReferMonthURL,
      payload: { id: data.id, month: addmonthTmp, referMonth: addReferMonthTmp }
    })
    this.forceUpdate() // 强制刷新
  }

  saveFormRefReferPeriod = (formRefReferPeriod) => {
    this.formRefReferPeriod = formRefReferPeriod
  }

  handleCancelReferPeriod = e => {
    if (this.formRefReferPeriod) {
      this.formRefReferPeriod.resetFields()
    }
    this.hiddenModalReferPeriod()
  }

  handleSubmitReferPeriod = e => {
    if (this.formRefReferPeriod) {
      this.formRefReferPeriod.submit()
    }
  }

  hiddenModalReferPeriod = () => {
    this.setState({ visibleReferPeriodModel: false })
  }

  onFinishReferPeriod = values => {
    const { isUpdateReferPeriod } = this.state
    console.log(values)
    let data = values
    if (data === undefined || data === null || data.period1 === null || data.period2 === null || data.period3 === null) {
      message.warn('选择3个周期')
      return
    }

    console.log(data)
    let month = data.month.format('YYYY-MM')
    let period = []
    let period1 = data.period1.format('YYYY-MM')
    let period2 = data.period2.format('YYYY-MM')
    let period3 = data.period3.format('YYYY-MM')

    // 限制 只能选当前任务时间及往前的月份，不能选往后的
    if (period1 > month || period2 > month || period3 > month) {
      message.warn('只能选当前任务时间及往前的月份，不能选往后的')
      return
    }

    period.push(period1)
    period.push(period2)
    period.push(period3)

    let url = ''
    if (isUpdateReferPeriod) {
      url = updateReferPeriodURL
    } else {
      url = addReferPeriodURL
    }

    let info = { month: month, period: period }
    console.log(info, url)
    this.props.dispatch({
      type: url,
      payload: info
    })
    this.handleCancelReferPeriod()
    this.forceUpdate() // 强制刷新
  }

  // 新增指定参考月份
  addReferMonthHandler = () => () => {
    const { dispatch } = this.props
    const { addmonth, addReferMonth } = this.state

    if (addmonth === undefined || addReferMonth === undefined) {
      message.warn('必须成对选择')
      return
    }

    if (addmonth === null || addReferMonth === null) {
      message.warn('必须选择时间')
      return
    }

    if (addmonth.unix() <= addReferMonth.unix()) {
      message.warn('参考月份必须小于月份')
      return
    }

    let addmonthTmp = addmonth.format('YYYYMM')
    let addReferMonthTmp = addReferMonth.format('YYYYMM')
    dispatch({
      type: upsertReferMonthURL,
      payload: { month: addmonthTmp, referMonth: addReferMonthTmp }
    })
    this.forceUpdate() // 强制刷新
  }

  updatePeriodDescModel = (period) => {
    if (period === null || period === '') {
      return ''
    }
    let startTime = moment(period, 'YYYY-MM').add(-1, 'months').format('YYYY-MM') + '-28 0点'
    let endTime = moment(period, 'YYYY-MM-DD').add(0, 'months').format('YYYY-MM') + '-27 24点'
    return <span><div>({startTime} 至 {endTime})</div></span>
  }

  updatePeriodMonthDescModel = (month) => {
    if (month && month !== '') {
      let startTime = moment(month, 'YYYY-MM').add(-1, 'months').format('YYYY-MM') + '-28 0点'
      let endTime = moment(month, 'YYYY-MM-DD').add(0, 'months').format('YYYY-MM') + '-27 24点'
      return <span><div>({startTime} 至 {endTime})</div></span>
    }
  }

  searchReferPeriodHandle = () => () => {
    this.refreshRefePeriodList()
  }

  onSelectChange = (selectedRowKeys, selectedRows) => {
    var value = selectedRows.map(item => item.id).join(',')
    this.setState({ selectedRowKeys })
    this.setState({ removeKey: value })
  }

  render () {
    const { visible1, visible3, visibleModel, visibleReferPeriodModel, referPeriod1, referPeriod2, referPeriod3, referMonth } = this.state
    const { TabPane } = Tabs
    const { route, model: { tableLoading, list, notDealSum, historyNotDealSum, taskTime, reachTaskGuildSum, rewardSum, listReferPeriod, listRewardSummary } } = this.props
    let optLimitTimeNew = '每月28号0点-当月最后一天23点'

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 2 },
        sm: { span: 15 }
      }
    }

    const { selectedRowKeys } = this.state
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='1' onChange={(record) => this.onTagChange(record)} type='card' size='large'>
            <TabPane tab={notDealSum === 0 || notDealSum === undefined ? <span><font>当前任务周期发奖复核</font></span> : <span><font>当前任务周期发奖复核</font><font color='red'>({notDealSum})</font></span>} key='1'>

              <span style={{ marginLeft: 15 }}>短位ID</span>
              <Input min={0} placeholder='请输入' onChange={e => this.setState({ searchReviewSSid1: e.target.value })} style={{ width: 100, marginLeft: 3 }} />

              <span style={{ marginLeft: 15 }}>是否下调扶持</span>
              <Select placeholder='请选择' style={{ width: 120, marginLeft: 3 }} onChange={(v) => this.setState({ searchReviewIsDownReward1: v })}>
                {isDownRewardList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}
              </Select>

              <span style={{ marginLeft: 15 }}>复核状态</span>
              <Select placeholder='请选择' style={{ width: 120, marginLeft: 3 }} onChange={(v) => this.setState({ searchReviewReviewStatus1: v })}>
                {reviewStatusList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}
              </Select>

              <span style={{ marginLeft: 15 }}>审批状态</span>
              <Select placeholder='请选择' style={{ width: 120, marginLeft: 3 }} onChange={(v) => this.setState({ searchReviewApprovalStatus1: v })}>
                {approvalStatusList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}
              </Select>
              <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchRewardHandle1()}>查询</Button>
              <Button style={{ marginLeft: 20 }} type='primary' onClick={this.onExportHandle(1)}>选择导出</Button>

              <div style={{ marginTop: 10 }}>任务时间：{taskTime}</div>
              <div>系统结算达标任务公会：{reachTaskGuildSum}家</div>
              <div>系统结算合计发放：{rewardSum / 10000}万元 （该数值仅供参考，最终以实际发放为准）</div>
              <div>当月发奖复核及审批操作期限：<font color='red'>{optLimitTimeNew}</font></div>
              <div><font color='red'>注：超过期限后系统自动锁定，无法进行复核/审批操作，当月扶持将无法发放，请及时处理！</font></div>
              <Button type='primary' onClick={this.showModel1(1)}>修改</Button>
              <Table rowSelection={rowSelection} rowKey={(record, index) => index} bordered dataSource={list} columns={this.configColumns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
            </TabPane>
            <TabPane tab={historyNotDealSum === 0 || historyNotDealSum === undefined ? <span><font>历史任务周期发奖复核</font></span> : <span><font>历史任务周期发奖复核</font><font color='red'>({historyNotDealSum})</font></span>} key='3'>

              <span style={{ marginLeft: 15 }}>短位ID</span>
              <Input min={0} placeholder='请输入' onChange={e => this.setState({ searchReviewSSid3: e.target.value })} style={{ width: 100, marginLeft: 3 }} />

              <span style={{ marginLeft: 15 }}>是否下调扶持</span>
              <Select placeholder='请选择' style={{ width: 120, marginLeft: 3 }} onChange={(v) => this.setState({ searchReviewIsDownReward3: v })}>
                {isDownRewardList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}
              </Select>

              <span style={{ marginLeft: 15 }}>复核状态</span>
              <Select placeholder='请选择' style={{ width: 120, marginLeft: 3 }} onChange={(v) => this.setState({ searchReviewReviewStatus3: v })}>
                {reviewStatusList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}
              </Select>

              <span style={{ marginLeft: 15 }}>审批状态</span>
              <Select placeholder='请选择' style={{ width: 120, marginLeft: 3 }} onChange={(v) => this.setState({ searchReviewApprovalStatus3: v })}>
                {approvalStatusList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}
              </Select>
              <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchRewardHandle3()}>查询</Button>
              <Button style={{ marginLeft: 20 }} type='primary' onClick={this.onExportHandle(3)}>选择导出</Button>
              <div style={{ marginTop: 10 }}>任务时间：{taskTime}</div>
              <div>系统结算达标任务公会：{reachTaskGuildSum}家</div>
              <div>系统结算合计发放：{rewardSum / 10000}万元 （该数值仅供参考，最终以实际发放为准）</div>
              <div>当月发奖复核及审批操作期限：<font color='red'>{optLimitTimeNew}</font></div>
              <div><font color='red'>注：超过期限后系统自动锁定，无法进行复核/审批操作，当月扶持将无法发放，请及时处理！</font></div>
              <Button type='primary' onClick={this.showModel3(3)}>修改</Button>
              <Table rowSelection={rowSelection} rowKey={(record, index) => index} bordered dataSource={list} columns={this.configColumns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
            </TabPane>
            <TabPane tab='参考周期配置' key='5'>
              <font>任务时间: </font>
              <DatePicker
                format='YYYY-MM'
                picker='month'
                placeholder='任务时间'
                onChange={(v) => this.setState({ searchMonth: v })}
              />
              <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchReferPeriodHandle()}>查询</Button>
              <Button style={{ marginLeft: 20 }} type='primary' onClick={this.showReferPeriodModal(null, true)}>添加</Button>
              <Table rowKey={(record, index) => index} bordered dataSource={listReferPeriod} columns={this.referMonthColumns} pagination={this.defaultPageValue} />
            </TabPane>
            <TabPane tab='发奖金额汇总' key='6'>
              <div>1、月份：指任务月份</div>
              <div>2、交友全月礼物流水：交友所有频道自然月内，频道礼物流水总和</div>
              <div>3、公会任务礼物流水：交友参与公会任务的频道在任务周期内（上月28号-本月27号），频道礼物流水总和</div>
              <div>4、阶梯扶持/进步扶持：最终发奖金额</div>
              <Table rowKey={(record, index) => index} bordered dataSource={listRewardSummary} columns={this.rewardSummaryColumns} pagination={this.defaultPageValue} />
            </TabPane>
            <TabPane tab='发奖明细' key='2'>
              <span style={{ marginLeft: 10 }}>任务时间</span>
              <DatePicker picker='month' onChange={(v) => this.setState({ searchMonth2: v })} style={{ marginLeft: 3 }} />

              <span style={{ marginLeft: 15 }}>短位ID</span>
              <Input min={0} placeholder='请输入' onChange={e => this.setState({ searchReviewSSid2: e.target.value })} style={{ width: 100, marginLeft: 3 }} />

              <span style={{ marginLeft: 15 }}>是否下调扶持</span>
              <Select placeholder='请选择' style={{ width: 120, marginLeft: 3 }} onChange={(v) => this.setState({ searchReviewIsDownReward2: v })}>
                {isDownRewardList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}
              </Select>

              <span style={{ marginLeft: 15 }}>复核状态</span>
              <Select placeholder='请选择' style={{ width: 120, marginLeft: 3 }} onChange={(v) => this.setState({ searchReviewReviewStatus2: v })}>
                {reviewStatusList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}
              </Select>

              <span style={{ marginLeft: 15 }}>审批状态</span>
              <Select placeholder='请选择' style={{ width: 120, marginLeft: 3 }} onChange={(v) => this.setState({ searchReviewApprovalStatus2: v })}>
                {approvalStatusList.map((item, index) => (<Option key={item.index} value={item.index}>{item.name}</Option>))}
              </Select>
              <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchRewardRealSendHandle()}>查询</Button>
              <Button style={{ marginLeft: 20 }} type='primary' onClick={this.onExportHandle(2)}>选择导出</Button>
              <Table rowSelection={rowSelection} rowKey={(record, index) => index} bordered dataSource={list} columns={this.lastRewardColumns} pagination={this.defaultPageValue} scroll={{ x: 'max-content' }} />
            </TabPane>
          </Tabs>
        </Card>

        <Modal keyboard={false} maskClosable={false} destroyOnClose forceRender width={1600} visible={visible1} title='当月发奖复核' onCancel={this.handleCancel1} onOk={this.handleSubmit1} okText='保存并提交审批'>
          <PageHeaderWrapper>
            <Table loading={tableLoading} rowKey='idx' bordered dataSource={list} columns={this.configUpdateColumns1} pagination={false} scroll={{ x: 'max-content' }} />
            <div><font color='red'>{'1、【下调后发放 - 发放扶持/元】：需0≤下调后发放金额<系统结算金额。注意核算输入的金额！'}</font></div>
            <div><font color='red'>{'2、【下调金额】无需填写，系统自动计算。可根据下调金额二次复核输入金额是否正确'}</font></div>
          </PageHeaderWrapper>
        </Modal>

        <Modal keyboard={false} maskClosable={false} destroyOnClose forceRender width={1600} visible={visible3} title='历史发奖复核' onCancel={this.handleCancel3} onOk={this.handleSubmit3} okText='保存并提交审批'>
          <PageHeaderWrapper>
            <Table loading={tableLoading} rowKey='idx' bordered dataSource={list} columns={this.configUpdateColumns3} pagination={false} scroll={{ x: 'max-content' }} />
            <div><font color='red'>{'1、【下调后发放 - 发放扶持/元】：需0≤下调后发放金额<系统结算金额。注意核算输入的金额！'}</font></div>
            <div><font color='red'>{'2、【下调金额】无需填写，系统自动计算。可根据下调金额二次复核输入金额是否正确'}</font></div>
          </PageHeaderWrapper>
        </Modal>

        <Modal forceRender width={400} visible={visibleModel} title='修改指定月份' onCancel={this.handleCancelReferMonth} onOk={this.handleSubmitReferMonth}>
          <Form {...formItemLayout} ref={this.saveFormRefReferMonth} onFinish={this.onFinish}>
            <Form.Item name='id' hidden>
              <Input hidden />
            </Form.Item>
            <Form.Item label='指定月份' name='monthNew' rules={[{ required: true }]}>
              <DatePicker picker='month' />
            </Form.Item>
            <Form.Item label='参考月份' name='referMonthNew' rules={[{ required: true }]}>
              <DatePicker picker='month' />
            </Form.Item>
          </Form>
        </Modal>

        <Modal forceRender width={600} visible={visibleReferPeriodModel} title='配置礼物流水参考周期' onCancel={this.handleCancelReferPeriod} onOk={this.handleSubmitReferPeriod} okText='确认并提交审批'>
          <Form {...formItemLayout} ref={this.saveFormRefReferPeriod} onFinish={this.onFinishReferPeriod}>
            <Row>
              <Col sm={24} md={12}>
                <Form.Item label='任务时间' name='month' rules={[{ required: true }]}>
                  <DatePicker disabled='true' picker='month' onChange={(v) => this.setState({ referMonth: v })} />
                </Form.Item>
              </Col>
              <Col sm={24} md={12}>
                <div>{this.updatePeriodMonthDescModel(referMonth)}</div>
              </Col>
            </Row>
            <Row>
              <Col sm={24} md={12}>
                <Form.Item label='周期一' name='period1' rules={[{ required: true }]}>
                  <DatePicker picker='month' onChange={(v) => this.setState({ referPeriod1: v })} />
                </Form.Item>
              </Col>
              <Col sm={24} md={12}>
                <div>{this.updatePeriodDescModel(referPeriod1)}</div>
              </Col>
            </Row>
            <Row>
              <Col sm={24} md={12}>
                <Form.Item label='周期二' name='period2' rules={[{ required: true }]}>
                  <DatePicker picker='month' onChange={(v) => this.setState({ referPeriod2: v })} />
                </Form.Item>
              </Col>
              <Col sm={24} md={12}>
                <div>{this.updatePeriodDescModel(referPeriod2)}</div>
              </Col>
            </Row>
            <Row>
              <Col sm={24} md={12}>
                <Form.Item label='周期三' name='period3' rules={[{ required: true }]}>
                  <DatePicker picker='month' onChange={(v) => this.setState({ referPeriod3: v })} />
                </Form.Item>
              </Col>
              <Col sm={24} md={12}>
                <div>{this.updatePeriodDescModel(referPeriod3)}</div>
              </Col>
            </Row>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default GuildTaskRewardApproval
