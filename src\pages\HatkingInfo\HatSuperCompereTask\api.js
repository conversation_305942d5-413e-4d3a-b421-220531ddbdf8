import request from '@/utils/request'
import { stringify } from 'qs'

// ------------------------任务配置-----------------------------------//
// 获取任务配置列表
export function listTaskConfig (params) {
  return request(`/fts_hgame/hat_super_task/boss/list_task_config?${stringify(params)}`)
}

// 获取单个任务确认配置信息
export function getConfirmTaskConfig (params) {
  return request(`/fts_hgame/hat_super_task/boss/get_confirm_task_config?${stringify(params)}`)
}

// 获取全量任务确认配置信息
export function getAllConfirmTaskConfig (params) {
  return request(`/fts_hgame/hat_super_task/boss/get_all_confirm_task_config?${stringify(params)}`)
}

// 刷新任务配置确认信息
export function flushConfirmTaskConfig (params) {
  return request(`/fts_hgame/hat_super_task/boss/flush_confirm_task_config?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 获取任务配置失效历史
export function geTaskConfigInvalid (params) {
  return request(`/fts_hgame/hat_super_task/boss/get_task_config_invalid?${stringify(params)}`)
}

// 提交审核
export function approvalTaskConfig (params) {
  return request(`/fts_hgame/hat_super_task/boss/approval_task_config?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

// 系统初始化任务配置
export function debugInitSystemTaskConfig (params) {
  return request(`/fts_hgame/hat_super_task/boss/debug_init_system_task_config?${stringify(params)}`)
}

// 设置虚拟时间
// export function debugHatKingTaskVirtualTime (params) {
//   return request(`/fts_hgame/hat_super_task/boss/debug_virtual_time?${stringify(params)}`)
// }

// export function debugGetHatKingTaskVirtualTime (params) {
//   return request(`/fts_hgame/hat_super_task/boss/debug_get_virtual_time?${stringify(params)}`)
// }

// ------------------------任务配置-----------------------------------//

//  任务完成进度报表
export function getTaskProgress (params) {
  return request(`/fts_hgame/hat_super_task/boss/progress_list?${stringify(params)}`)
}
// 发奖明细
export function listAwardDetail (params) {
  return request(`/fts_hgame/hat_super_task/boss/list_award_detail?${stringify(params)}`)
}

export function listRewardConfirm (params) {
  return request(`/fts_hgame/hat_super_task/boss/list_reward_confirm?${stringify(params)}`)
}

export function approvalRewardConfirm (params) {
  return request(`/fts_hgame/hat_super_task/boss/approval_reward_confirm?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}

export function flushRewardConfirm (params) {
  return request(`/fts_hgame/hat_super_task/boss/flush_reward_confirm?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: JSON.stringify(params)
  })
}
