import { getCompereSummaryLists, getChannelSummaryLists, getAllChannelTypeSummaryLists } from './api'

export default {
  namespace: 'compereSummary',

  state: {
    compereSummaryList: [],
    channelSummaryList: [],
    channelTypeSummaryList: []
  },

  reducers: {
    updateCompereSummaryList (state, { payload }) {
      payload.sort(function (a, b) {
        if (b.date === a.date) {
          return a.kind - b.kind
        }

        return b.date - a.date
      })
      for (var i = 0; i < payload.length; i++) {
        payload[i].percent = 0
        if (payload[i].hatking === undefined) {
          payload[i].hatking = 0
        }

        if (payload[i].hatking > 0 && payload[i].total > 0) {
          payload[i].percent = payload[i].hatking / payload[i].total * 100
          payload[i].percent = payload[i].percent.toFixed(2) + '%'
        }
      }
      return {
        ...state,
        compereSummaryList: payload
      }
    },
    updateChannelSummaryList (state, { payload }) {
      payload.sort(function (a, b) {
        if (b.date === a.date) {
          return a.kind - b.kind
        }

        return b.date - a.date
      })
      for (var i = 0; i < payload.length; i++) {
        payload[i].percent = 0
        if (payload[i].hatking === undefined) {
          payload[i].hatking = 0
        }

        if (payload[i].hatking > 0 && payload[i].total > 0) {
          payload[i].percent = payload[i].hatking / payload[i].total * 100
          payload[i].percent = payload[i].percent.toFixed(2) + '%'
        }
      }
      return {
        ...state,
        channelSummaryList: payload
      }
    },
    updateChannelTypeSummaryList (state, { payload }) {
      payload.sort(function (a, b) {
        if (b.date === a.date) {
          return a.kind - b.kind
        }

        return b.date - a.date
      })
      for (var i = 0; i < payload.length; i++) {
        payload[i].percent0 = 0
        if (payload[i].guildTypeAmount0 > 0 && payload[i].totalAmount > 0) {
          payload[i].percent0 = payload[i].guildTypeAmount0 / payload[i].totalAmount * 100
          payload[i].percent0 = payload[i].percent0.toFixed(2) + '%'
        }

        payload[i].percent1 = 0
        if (payload[i].guildTypeAmount1 > 0 && payload[i].totalAmount > 0) {
          payload[i].percent1 = payload[i].guildTypeAmount1 / payload[i].totalAmount * 100
          payload[i].percent1 = payload[i].percent1.toFixed(2) + '%'
        }

        // payload[i].percent2 = 0
        // if (payload[i].guildTypeAmount2 > 0 && payload[i].totalAmount > 0) {
        //   payload[i].percent2 = payload[i].guildTypeAmount2 / payload[i].totalAmount * 100
        //   payload[i].percent2 = payload[i].percent2.toFixed(2) + '%'
        // }

        // payload[i].percent3 = 0
        // if (payload[i].guildTypeAmount3 > 0 && payload[i].totalAmount > 0) {
        //   payload[i].percent3 = payload[i].guildTypeAmount3 / payload[i].totalAmount * 100
        //   payload[i].percent3 = payload[i].percent3.toFixed(2) + '%'
        // }

        payload[i].percent4 = 0
        if (payload[i].guildTypeAmount4 > 0 && payload[i].totalAmount > 0) {
          payload[i].percent4 = payload[i].guildTypeAmount4 / payload[i].totalAmount * 100
          payload[i].percent4 = payload[i].percent4.toFixed(2) + '%'
        }

        payload[i].percent5 = 0
        if (payload[i].guildTypeAmount5 > 0 && payload[i].totalAmount > 0) {
          payload[i].percent5 = payload[i].guildTypeAmount5 / payload[i].totalAmount * 100
          payload[i].percent5 = payload[i].percent5.toFixed(2) + '%'
        }
      }
      return {
        ...state,
        channelTypeSummaryList: payload
      }
    }
  },

  effects: {
    * getCompereSummaryList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getCompereSummaryLists, payload)
      yield put({
        type: 'updateCompereSummaryList',
        payload: Array.isArray(list) ? list : []
      })
    },
    * getChannelSummaryList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getChannelSummaryLists, payload)
      yield put({
        type: 'updateChannelSummaryList',
        payload: Array.isArray(list) ? list : []
      })
    },
    * getChannelTypeSummaryList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getAllChannelTypeSummaryLists, payload)
      yield put({
        type: 'updateChannelTypeSummaryList',
        payload: Array.isArray(list) ? list : []
      })
    }
  }
}
