/* eslint-disable eqeqeq */
import React, { Component } from 'react'
import { Card, Divider, Select, Form, message, Button, Row, Col, InputNumber, TimePicker, Typography, Input, Space, Table, Modal, Spin } from 'antd'
import { connect } from 'dva'
import moment from 'moment'
import { columns, TipDIv, PoolChangeDesc, hotOptions, broadcastOptionsJY, broadcastOptionsVR, newNoticeOptions, countSURT, fixDataSource, countEL } from './list_common'
import { timeFormater, deepClone, getCookie } from '@/utils/common'
import { propTypeOptions } from '../../dropCommon'
import DiffTable from '@/components/DiffTable'
import { getFieldsValueByPoolID } from '../../globalConfig'
import DynamicCfgButton from './dynamicCfgBtn'
import ExtraCfgButton from './extraSetting'
import PrizeSelector from './prizeSelector'
import { screenshotByID } from '../../../../utils/screenShot'
// import { globalBossConfig } from '../../globalConfig'

const namespace = 'dropMain'

const defaultPropItem = { id: 1, prizeId: 101, prizeType: 1, propsId: 12, propsName: '爱心', hot: 0, propsType: 0, count: 1, value: 100, rate: 1, dailyLimit: -1, hoursLimit: -1, valueLimit: 0, timeStart: 0, timeStop: 0, broadCastType: 0 }
const defaultEditing = { timestamp: 0, hashCode: '', status: 0, operator: 0, passport: '', aprPassport: '', aprRemark: '', aprTimestamp: 0, aprId: 0, list: [] }

@connect(({ dropMain }) => ({
  model: dropMain
}))

// 注意这个页面放在这里是方便一些代码复用，实际展示的位置是在 /DropRefactor/PoolApproval
class DropMainPoolEditor extends Component {
  state = {
    pid: this.props.groupType === 'vr' ? 13000 : 17000, // 默认道具池ID
    visible: false,
    prizeSelectorVisible: false,
    editing: false,
    approvalVisible: false,
    approvalRemark: '',
    updateRemark: '',
    updateComfirmVisible: false,
    screenShot: '',
    poolChangeDataSource: { rta: 0, rtb: 0, smb: 0, sma: 0, ela: 0, elb: 0 },
    dataSourceUpdateCounter: 0,
    spanning: false
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  /** ************************************组件初始化**************************************************/

  componentDidMount () {
    this.getEditingPoolList()
  }

  // 刷新编辑成功池
  getEditingPoolList = (id = null) => {
    const { pid } = this.state
    const { dataSourceUpdateCounter } = this.state
    this.callModel('getEditingPool', {
      id: id || pid,
      cbFunc: () => {
        this.updateChangeDesc()
        this.setState({ dataSourceUpdateCounter: dataSourceUpdateCounter + 1 })
      } })
  }

  // 获取待审批信息
  getApprovalInfo = (pid) => {
    if (pid === '') {
      message.warn('未创建审批流')
      return
    }
    this.callModel('getToApproval', {
      params: { pid: pid },
      cbFunc: (list) => {
        if (!Array.isArray(list)) {
          message.warn('当前非待审批状态~')
          return
        }
        let info = list[0]
        if (info == undefined || info.rule == undefined || info.rule.approvals == undefined) {
          message.warn('审批流信息异常，请稍后再试..')
          return
        }
        let uid = getCookie('yyuid')
        const { progress, rule } = info
        const aprList = progress === 0 ? rule.approvals : rule.secondary
        for (let i = 0; i < aprList.length; i++) {
          if (aprList[i].uid == uid) {
            this.setState({ approvalVisible: true })
            return
          }
        }
        message.warn('非指定审批人')
      } })
  }

  doApproval = (pass) => {
    const { toApproval } = this.props.model
    const { approvalRemark } = this.state
    let req = toApproval[0]
    if (req == undefined || req.rule == undefined || req.rule.approvals == undefined) {
      message.warn('审批流信息异常，请稍后再试..')
      return
    }
    req.reason = approvalRemark || '无'
    if (pass) {
      req.result = 'Passed'
    } else {
      req.result = 'Rejected'
    }
    this.callModel('doApproval', {
      params: req,
      cbFunc: (ok, msg) => {
        if (ok) {
          message.success('审批完成')
        } else {
          message.error(msg)
        }
        this.setState({ approvalVisible: false })
        this.getEditingPoolList()
      }
    })
  }
  /** ************************************完成道具池编辑，提交到服务器************************************************ */

  // 检查配置合法性然后弹出提交审批确认框
  beforeSubmitEditing = () => {
    let { editingConfig } = this.props.model
    if (!this.checkSubmitData(editingConfig)) {
      return
    }
    this.updateChangeDesc()
    this.setState({ editing: false, spanning: true })
    setTimeout(() => {
      screenshotByID('poolTable', (isOk, url) => {
        if (!isOk) {
          message.warn('截图失败~')
        }
        this.setState({ updateComfirmVisible: true, screenShot: url, spanning: false })
      })
    }, 200)
  }

  // 检查道具池配置合法性, 返回false表示不合法
  checkSubmitData = (editingConfig) => {
    return true
  }

  onSubmit = () => {
    const { updateRemark, poolChangeDataSource, screenShot } = this.state
    const { editingConfig, poolConfig } = this.props.model
    // 数据检验
    if (!this.checkSubmitData(editingConfig)) {
      return
    }
    if (!updateRemark) {
      message.warn('请填写备注')
      return
    }
    editingConfig.content = JSON.stringify(editingConfig.list)
    editingConfig.remark = updateRemark || '空'
    delete editingConfig['list']
    editingConfig.changeInfo = poolChangeDataSource
    editingConfig.screenShot = screenShot

    poolConfig.temporary = editingConfig

    this.callModel('editPool', {
      params: poolConfig,
      cbFunc: (ok) => {
        if (ok) {
          message.success('已提交审批')
          this.setState({ editing: false, updateComfirmVisible: false })
          this.getEditingPoolList()
        } else {
          message.warn('发生错误，请联系管理员...')
        }
      }
    })
  }

  // 放弃更新
  onReset = () => {
    this.getEditingPoolList()
    this.editChange()
  }

  /** **********************************切换道具池******************************************************************/

  onSelectChange = cid => {
    this.setState({ pid: cid })
    this.getEditingPoolList(cid)
  }

  editChange = () => {
    const { model: { poolConfig } } = this.props
    if (poolConfig === undefined || poolConfig.id <= 0) {
      message.error('请选择道具池')
      return
    }
    const { editing } = this.state
    this.setState({ editing: !editing })
  }

  // 根据道具池ID获取appID
  getAppIDByPoolID = (pid) => {
    return getFieldsValueByPoolID(pid, 'AppID')
  }

  /** **********************************list 增删改查******************************************************************/
  addItem = () => {
    let v = $.extend({}, true, defaultPropItem)
    // 自动生成奖励道具ID，递增
    const { poolConfig } = this.props.model
    let base = 1000
    if (poolConfig !== undefined && poolConfig.id !== undefined) {
      base = poolConfig.id
    }
    v.prizeId = base * 100 + v.prizeId

    // 奖励道具ID 与 编号 自增
    let { editingConfig } = this.props.model

    if (Array.isArray(editingConfig.list)) {
      editingConfig.list.forEach(item => {
        if (item.id >= v.id) {
          v.id = item.id + 1
        }
        if (item.prizeId >= v.prizeId) {
          v.prizeId = item.prizeId + 1
        }
      })
    }
    // 覆盖旧数据
    editingConfig.list.push(v)
    this.changeState('editingConfig', editingConfig)
    this.updateChangeDesc()
  }

  removeItem = id => () => {
    let { editingConfig } = this.props.model
    let cl = []
    editingConfig.list.forEach(item => {
      if (item.id !== id) {
        cl.push(item)
      }
    })
    editingConfig.list = cl
    this.changeState('editingConfig', editingConfig)
    this.updateChangeDesc()
  }

  /** **********************************table 渲染******************************************************************/

  onInputChange = (row, field) => value => {
    let { editingConfig } = this.props.model
    let v = $.extend([], true, editingConfig.list)
    v[row][field] = value
    editingConfig.list = v
    this.changeState('editingConfig', editingConfig)
    this.updateChangeDesc()
  }

  // 时间更新
  onTimeChange = (row, field) => value => {
    let { editingConfig } = this.props.model
    let v = $.extend([], true, editingConfig.list)
    v[row][field] = value ? value.unix() % 86400 : 0
    editingConfig.list = v
    this.changeState('editingConfig', editingConfig)
  }

  // 行选择
  onBtnClick = row => () => {
    this.setState({ row, prizeSelectorVisible: true })
  }

  onPropChangeNew = (prizeInfo) => {
    const { row } = this.state
    const { appId, id, name, url, price, prizeType } = prizeInfo
    let { editingConfig } = this.props.model
    if (row === undefined) {
      message.error('unfined row' + row)
      return
    }

    let v = $.extend([], true, editingConfig.list)
    v[row].appId = appId // appId
    v[row].propsId = id // 礼物ID
    v[row].propsName = name // 礼物名称
    v[row].propsUrl = url // 礼物图片
    v[row].value = price // 礼物加个
    v[row].prizeType = prizeType // 礼物渠道
    editingConfig.list = v
    this.changeState('editingConfig', editingConfig)
    this.setState({ prizeSelectorVisible: false })
    this.updateChangeDesc()
  }

  renderEditColumn = () => {
    const { editing } = this.state
    if (!editing) {
      return columns
    }
    let renderColumns = []
    let broadcastOptions = []
    if (this.props.groupType === 'vr') {
      broadcastOptions = broadcastOptionsVR
    } else {
      broadcastOptions = broadcastOptionsJY
    }

    let cp = deepClone(columns)
    for (let i = 0; i < cp.length; i++) {
      let column = cp[i]
      if (['count', 'rate', 'dailyLimit', 'hoursLimit', 'valueLimit', 'extA'].indexOf(column.dataIndex) > -1) {
        column.render = (text, record, index) => {
          return <InputNumber onChange={this.onInputChange(index, column.dataIndex)} defaultValue={text} />
        }
      }

      if (['hot', 'broadCastType', 'propsType', 'newNotice'].indexOf(column.dataIndex) > -1) {
        column.render = (text, record, index) => {
          let options = [hotOptions, broadcastOptions, propTypeOptions, newNoticeOptions][['hot', 'broadCastType', 'propsType', 'newNotice'].indexOf(column.dataIndex)]
          return <Select onChange={this.onInputChange(index, column.dataIndex)} options={options} defaultValue={text} />
        }
      }

      if (['timeStart', 'timeStop'].indexOf(column.dataIndex) > -1) {
        column.render = (text, record, index) => {
          return <TimePicker onChange={this.onTimeChange(index, column.dataIndex)} defaultValue={moment.unix(text)} />
        }
      }

      if (column.dataIndex === 'propsId') {
        column.render = (text, record, index) => {
          return <Button onClick={this.onBtnClick(index, column.dataIndex)} type='link'>{text}</Button>
        }
      }

      if (column.dataIndex === 'limitSetting') {
        column.render = (value, r, i) => {
          return <DynamicCfgButton value={value} record={r} isEdit cmpValue={r.cmpLimitSetting} limit={r.dailyLimit} id={r.id} propsName={r.propsName}
            onChange={(v) => {
              let { editingConfig } = this.props.model
              let cp = [...editingConfig.list]
              cp[i].limitSetting = v
              editingConfig.list = cp
              this.changeState('editingConfig', editingConfig)
            }} />
        }
      }

      if (column.dataIndex === 'extraSetting') {
        column.render = (value, r, i) => {
          return <ExtraCfgButton value={value} record={r} isEdit cmpValue={r.cmpExtraSetting} propsName={r.propsName} broadcastOptions={broadcastOptions}
            onChange={(v) => {
              let { editingConfig } = this.props.model
              let cp = [...editingConfig.list]
              cp[i].extraSetting = v
              editingConfig.list = cp
              this.updateChangeDesc()
              this.changeState('editingConfig', editingConfig)
            }} />
        }
      }

      renderColumns.push(column)
    }

    renderColumns.push({
      title: '操作',
      align: 'center',
      render: (text, record) => (
        <div>
          <Button onClick={this.removeItem(record.id)} type='link' danger>删除</Button>
        </div>
      )
    })
    return renderColumns
  }

  renderDiffColumn = () => {
    // 奖励道具名称、数量、概率、投放上限/D、投放上限/2H 展示变化，其他不展示
    let cp = [
      ...deepClone(columns),
      { title: null, dft_hidden: true, dataIndex: 'cmpLimitSetting' },
      { title: null, dft_hidden: true, dataIndex: 'cmpBoxwarSetting' },
      { title: null, dft_hidden: true, dataIndex: 'cmpExtraSetting' },
      { title: null, dft_hidden: true, dataIndex: 'totalRate' }
    ]
    let broadcastOptions = []
    if (this.props.groupType === 'vr') {
      broadcastOptions = broadcastOptionsVR
    } else {
      broadcastOptions = broadcastOptionsJY
    }

    // FIXME: 产品要求的临时改动，后面需要还原
    if (this.props.groupType === 'vr') {
      cp = cp.filter(item => {
        if (item.title === '礼物渠道' || item.title === '投放上限/D' || item.title === '投放上限/2H' || item.title === '生效时间' || item.title === '失效时间' || item.title === '动态发放道具' || item.title === '翻倍' || item.title === '金额限制') {
          return false
        }
        return true
      })
    }
    const newColumns = cp.map(item => {
      if (['propsName', 'count', 'rate', 'hoursLimit', 'dailyLimit', 'fixRate'].indexOf(item.dataIndex) < 0) { // 非指定的字段不显示对比
        item.dft_hidden = true
      }
      if (item.dataIndex === 'limitSetting') {
        item.render = (v, r) => { return <DynamicCfgButton key={v} value={v} diffMode cmpValue={r.cmpLimitSetting} limit={r.dailyLimit} propsName={r.propsName} isEdit={false} /> }
      }
      if (item.dataIndex === 'extraSetting') {
        item.render = (v, r) => { return <ExtraCfgButton key={v} value={v} diffMode cmpValue={r.cmpExtraSetting} propsName={r.propsName} isEdit={false} broadcastOptions={broadcastOptions} /> }
      }
      if (item.dataIndex === 'broadCastType') {
        item.render = (v, r) => { return broadcastOptions.find(item => item.value === v)?.label }
      }
      if (item.title === '价值/紫水晶' && this.props.groupType === 'vr') {
        item.title = '价值/金钻'
      }
      return item
    })
    return newColumns
  }

  /** ********************************** 统计数值变化 ******************************************************************/

  // 传入道具池列表，计算非设限部分发放占比和设限部分总金额
  countSURT = (dataSource, pid) => {
    let result = { rt: 0, sm: 0 }
    if (!Array.isArray(dataSource)) {
      return result
    }
    let cost = 2000 // 单次抽取道具金额
    if (pid === 12000 || pid === 14000) { // 抢物资下的超级模式每次20元
      cost = 20000
    }
    let proSum = 0 // 总概率,所有物品的概率之和
    let nProSum = 0 // sum(日投放无上限的礼物价值×概率)
    let lproSum = 0 // sum(日投放有上限礼物价值×日投放数量)
    for (let i = 0; i < dataSource.length; i++) {
      const item = dataSource[i]
      const { rate, dailyLimit, value, count } = item
      proSum += rate
      if (dailyLimit >= 0) { // 有上限的礼物
        lproSum += (value * count * dailyLimit)
      } else { // 无上限的礼物
        nProSum += (value * count * rate)
      }
    }
    // console.info('nProSum=>', nProSum, 'lproSum=>', lproSum, 'rateSum=>', proSum, 'cost=>', cost)
    const tmpRT = nProSum / (proSum * cost) // 非设限部分发放占比: 日投放无上限的礼物价值×概率 /（总概率×单次抽取道具金额)
    result.rt = parseInt((tmpRT * 10000).toFixed(4))
    // console.info('非设限部分发放占比: ', nProSum, '/ (', proSum, '*', cost, ') = ', tmpRT, '=>', result.rt)

    const tmpSM = lproSum / 1000 // 设限部分总金额: 日投放有上限礼物价值×日投放数量/1000
    result.sm = parseInt(tmpSM * 100)
    // console.info('设限部分总金额: ', lproSum, '/ 1000 = ', tmpSM, '=>', result.sm)
    return result
  }

  updateChangeDesc = () => {
    const { editingConfig, prodList } = this.props.model
    this.refreshPoolChangeDesc(prodList, editingConfig.list)
  }

  // 更新 PoolChangeDesc 显示的数值
  refreshPoolChangeDesc = (dataSourceb, dataSourcea) => {
    const { pid } = this.state
    const resultb = countSURT(dataSourceb, pid)
    const resulta = countSURT(dataSourcea, pid)
    const ela = countEL(dataSourcea)
    const elb = countEL(dataSourceb)
    const newDataSource = { rtb: resultb.rt, smb: resultb.sm, rta: resulta.rt, sma: resulta.sm, ela: ela, elb: elb }
    this.setState({ poolChangeDataSource: newDataSource })
  }

  poolOptionsFilter = (before) => {
    const offlineList = [11000, 12000, 9000, 10000]
    let after = before.filter(item => { return offlineList.indexOf(item.value) === -1 })
    // FIXME: 产品要求的临时改动，后面需要还原
    after = after.map(item => {
      item.label = item.label.replace('追玩', 'Yo交友')
      return item
    })
    console.debug('before===>', after)
    return after
  }

  /* *******************************页面布局***************************************************************/

  render () {
    const { editing, pid, approvalVisible, updateComfirmVisible, poolChangeDataSource, dataSourceUpdateCounter, prizeSelectorVisible, spanning } = this.state
    const { editingConfig, prodList, poolNameOptions, globalPrizeList, editingConfig: { list } } = this.props.model
    const { timestamp, passport, status, aprId, aprUid, aprPassport, aprTimestamp, remark, aprRemark } = editingConfig || defaultEditing
    // const { operator, aprRemark } = editingConfig || defaultEditing
    const { Text } = Typography
    const fixList = fixDataSource(prodList, list)
    const { listA, listB } = fixList // listA=当前待审批配置, listB=正式生效配置
    return (
      <Card>
        <Row gutter={8}>
          <Col span={4}>
            <Form>
              <Form.Item label='道具池选择'>
                <Select defaultValue={pid} disabled={editing} onChange={this.onSelectChange} options={this.poolOptionsFilter(poolNameOptions)} />
              </Form.Item>
            </Form>
          </Col>
          <Col span={6} hidden={!editing}>
            <Form.Item>
              <Button style={{ marginRight: 20 }} onClick={() => this.addItem()}>新增奖励道具</Button>
              <Button onClick={this.onReset} style={{ marginRight: 20 }}>放弃修改</Button>
              <Button type='dash' danger onClick={() => { this.beforeSubmitEditing() }}>提交修改</Button>
            </Form.Item>
          </Col>
          <Col span={2} hidden={editing}>
            <Button type='primary' onClick={this.editChange}>编辑道具池</Button>
          </Col>
          <Col span={2} hidden={editing}>
            <Button type='primary' disabled={aprId === '' || status != 1} onClick={() => this.getApprovalInfo(aprId)}>审批</Button>
          </Col>
          { spanning ? <Spin /> : ''}
          <Col>
            {
              // FIXME: 产品要求的临时改动，后面需要还原
              this.props.groupType === 'vr'
                ? ''
                : <PoolChangeDesc {...poolChangeDataSource} isEdit />
            }
          </Col>
        </Row>

        {
          // FIXME: 产品要求的临时改动，后面需要还原
          this.props.groupType === 'vr'
            ? ''
            : <Row>
              <Col>
                <Row>提交：<Text type='secondary'> {`${passport}_(${timeFormater(timestamp)})`} <Divider type='vertical' /> </Text></Row>
                <Row hidden={status === 1}>审批：<Text type='secondary'> {aprUid === 0 ? '系统' : aprPassport}_({timeFormater(aprTimestamp)}) <Divider type='vertical' /></Text></Row>
              </Col>
              <Col>
                <Row>申请理由：<Text type='secondary'> { remark || '(空)'} <Divider type='vertical' /></Text></Row>
                <Row hidden={status === 1}>审批备注：<Text type='secondary'> { aprRemark || '(空)'} <Divider type='vertical' /></Text></Row>
              </Col>
              <Col>
                <Row>审批状态：<Text type={['warning', 'warning', 'success', 'danger'][status]}> { ['未创建', '待审批', '已通过', '不通过'][status]} <Divider type='vertical' /></Text></Row>
              </Col>
            </Row>
        }

        <Row>
          {
            // FIXME: 产品要求的临时改动，后面需要还原
            this.props.groupType === 'vr'
              ? <Text type='danger' style={{ marginTop: '1em' }}>道具实际概率=该道具普通概率/全部道具普通概率之和</Text>
              : <TipDIv />
          }
        </Row>
        <Divider />

        <div id='poolTable'>
          {
            editing
              ? <Table key={list} pagination={false} size='small' rowKey='id' scroll={{ x: 'max-content' }} columns={this.renderEditColumn()} dataSource={list} />
              : <DiffTable key={dataSourceUpdateCounter} oldProps={{ pagination: false, size: 'small' }} columns={this.renderDiffColumn()} after={listA} before={listB} />
          }
        </div>

        <PrizeSelector
          type='modal'
          appIDLimit={this.getAppIDByPoolID(pid)}
          visible={prizeSelectorVisible} prizeList={globalPrizeList}
          onCancel={() => { this.setState({ prizeSelectorVisible: false }) }}
          onComfirm={(v) => { this.onPropChangeNew(v) }}
        />

        {/* 提交修改确认模态框 */}
        <Modal visible={updateComfirmVisible} title='确认提交审批么？' footer={null} onCancel={() => { this.setState({ updateComfirmVisible: false, editing: true }) }}>
          <Row>
            <Text style={{ fontSize: '1.1em' }}>非设限部分发放占比: {(poolChangeDataSource.rtb / 100)}%➞<Text type='danger'>{(poolChangeDataSource.rta / 100)}%</Text></Text>
          </Row>
          <Row>
            <Text style={{ fontSize: '1.1em' }}>设限部分总金额(元): {poolChangeDataSource.smb / 100}➞<Text type='danger'>{poolChangeDataSource.sma / 100}</Text></Text>
          </Row>
          <Row>
            <Text style={{ fontSize: '1.1em' }}>翻倍上限总金额(元): {poolChangeDataSource.elb / 1000}➞<Text type='danger'>{poolChangeDataSource.ela / 1000}</Text></Text>
          </Row>
          <Row style={{ marginBottom: '1em' }}>
            <Input placeholder='请输入备注信息 (必填)' onChange={(e) => { this.setState({ updateRemark: e.target.value }) }} />
          </Row>
          <Space>
            <Button onClick={() => { this.setState({ updateComfirmVisible: false, editing: true }) }}>再看看</Button>
            <Button danger type='primary' onClick={() => { this.onSubmit() }}>提交审批</Button>
          </Space>
        </Modal>

        {/* 审批模态框 */}
        <Modal visible={approvalVisible} title='道具池配置审批' footer={null}
          onCancel={() => { this.setState({ approvalVisible: false }) }}>
          <Row style={{ marginBottom: '1em' }}>
            <Input placeholder='请输入备注信息 (通过或驳回的原因,选填)' onChange={(e) => { this.setState({ approvalRemark: e.target.value }) }} />
          </Row>
          <Space>
            <Button onClick={() => { this.setState({ approvalVisible: false }) }}>取消</Button>
            <Button danger type='primary' onClick={() => { this.doApproval(false) }}>驳回</Button>
            <Button type='primary' onClick={() => { this.doApproval(true) }}>通过</Button>
          </Space>
        </Modal>

      </Card>
    )
  }
}

export default DropMainPoolEditor
