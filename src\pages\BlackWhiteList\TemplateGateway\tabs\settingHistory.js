import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Table, Row, Col, Button, Select, InputNumber } from 'antd'
import { timeFormater } from '@/utils/common'

const namespace = 'templateGateWay'

@connect(({ templateGateWay }) => ({
  model: templateGateWay
}))

// 已下线的Tab,可以铲掉了
class SettingHistory extends Component {
  state = {
    isStaffOnly: true,
    sid: null,
    ssid: null
  }

  componentDidMount = () => {
    this.refreshData()
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  refreshData = () => {
    const { sid, ssid, isStaffOnly } = this.state
    this.callModel('getChanUpdateHistory', {
      params: {
        isStaffOnly: isStaffOnly,
        sid: sid || 0,
        ssid: ssid || 0,
        limit: 100
      }
    })
  }

  render () {
    const { updateHistory } = this.props.model
    const typeOptions = [
      { label: '运营', value: true },
      { label: '主持', value: false }
    ]
    const columns = [
      { dataIndex: 'sid', title: 'sid' },
      { dataIndex: 'ssid', title: 'ssid' },
      { dataIndex: 'timestamp', title: '设置时间', render: (v) => { return timeFormater(v) } },
      { dataIndex: 'uid', title: '更新人uid' },
      { dataIndex: 'templateType', title: '更新后设置', render: (v) => { return v === 1 ? '旧' : '新' } },
      { dataIndex: 'note', title: '备注', render: (v) => { return v || '-' } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    const { isStaffOnly, sid, ssid } = this.state

    return (
      <Card>
        <Row>
          <Col span={24}>
            类型: <Select value={isStaffOnly} options={typeOptions} onChange={(v) => { this.setState({ isStaffOnly: v }) }} />
            SID: <InputNumber style={{ width: '10em' }} placeholder='默认全部' value={sid} onChange={v => { this.setState({ sid: v }) }} />
            SSID: <InputNumber style={{ width: '10em' }} placeholder='默认全部' value={ssid} onChange={v => { this.setState({ ssid: v }) }} />
            <Button onClick={() => { this.refreshData() }}>刷新</Button>
          </Col>
          <Col span={24} style={{ marginBottom: '1em' }}>
            <Table size='small' columns={columns} dataSource={updateHistory} pagination={false} />
          </Col>
        </Row>
      </Card>
    )
  }
}

export default SettingHistory
