import React, { Component } from 'react'
import { Button, message, Input } from 'antd'
import PopImage from '../PopImage'
import { UploadOutlined, SyncOutlined } from '@ant-design/icons'

// 已废弃，请勿使用
class UploadFile extends Component {
  constructor (props) {
    super(props)

    const { value, onChange } = props

    this.state = { value, hidden: false, onChange: onChange }
  }

  onInputChange = e => {
    if (this.props.onChange) {
      this.props.onChange(this.ipt.state.value)
    }
    this.onStateChange(this.ipt.state.value)
  }

  setInput = value => {
    console.log(this.ipt)
    this.ipt.setState({ value })
    this.ipt.props.onChange(this.ipt)
  }

  componentWillReceiveProps (nextProps) {
    const { value, onChange } = nextProps
    this.setState({ value, onChange })
  }

  onStateChange = url => {
    this.setState({ value: url })
  }

  onButtonClick = e => {
    document.getElementById('component_jy_upload_file_upload').click()
  }

  onFileChange = e => {
    console.log('file', this.state)
    let onStateChange = this.onStateChange
    let setInput = this.setInput

    let files = document.getElementById('component_jy_upload_file_upload').files
    if (files.length === 0) {
      return
    }

    // eslint-disable-next-line no-undef
    let fd = new FormData()
    fd.append('bucket', document.getElementById('component_jy_upload_file_bucket').value)
    for (var i = 0; i < files.length; i++) {
      fd.append('files', files[i])
    }

    // eslint-disable-next-line no-undef
    var xhr = new XMLHttpRequest()
    xhr.open('POST', '//fts.yy.com/fs/uploadfiles')
    xhr.send(fd)
    xhr.onreadystatechange = function () {
      // console.log('change', xhr.readyState, xhr.responseText)
      if (this.readyState === 4 && this.status === 200) {
        let result = JSON.parse(xhr.responseText)
        // console.log(result)
        if (result.status === 0 && result.urls.length > 0) {
          console.log(result.urls[0])

          onStateChange(result.urls[0])
          setInput(result.urls[0])
        } else {
          message.error(result.status, result.msg)
        }
      }
    }
  }

  render () {
    const { value, hidden } = this.state

    return (
      <div>
        <Button onClick={e => { const { hidden } = this.state; this.setState({ hidden: !hidden }) }} style={{ marginRight: 10 }}><SyncOutlined /></Button>
        <Input hidden={!hidden} ref={ipt => { this.ipt = ipt }} defaultValue={value} style={{ width: 350, marginRight: 20 }} onChange={this.onInputChange} />
        <Button hidden={hidden} onClick={this.onButtonClick} style={{ marginRight: 20 }}><UploadOutlined />上传文件</Button>
        <a target='_black' href={value}>{value ? /png|jpg|jfif|jpeg|bmp+/.test(value) ? <PopImage value={value} /> : value.substring(value.lastIndexOf('/') + 1) : '' }</a>
        <Input ref={ipt => { this.fileIpt = ipt }} hidden onChange={this.onFileChange} id='component_jy_upload_file_upload' type='file' />
        <Input hidden readOnly id='component_jy_upload_file_bucket' name='bucket' value='makefriends' />
      </div>
    )
  }
}

export default UploadFile
