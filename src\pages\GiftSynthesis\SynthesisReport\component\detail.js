import React, { Component } from 'react'
import { Card, Table, DatePicker, Form, Row, Col, Button, Input } from 'antd'
import { connect } from 'dva'
import moment from 'moment'
import { SearchOutlined } from '@ant-design/icons'
import { CSVLink } from 'react-csv'

const namespace = 'SynthesisReport' // model 的 namespace
const dateFormat = 'YYYYMMDD'
const { RangePicker } = DatePicker

@connect(({ SynthesisReport }) => ({ // model 的 namespace
  model: SynthesisReport // model 的 namespace
}))

class DetailReportComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      searchUID: 0,
      targetID: 0,
      supportUid: 0,
      dateRange: [moment().subtract(60, 'days'), moment().subtract(0, 'days')],
      visible: false
    }
  }

  componentDidMount () {
    const { dispatch } = this.props
    const { dateRange, searchUID } = this.state
    let data = { begin: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat), searchUID: searchUID }

    dispatch({
      type: `${namespace}/getDetailReportList`,
      payload: data
    })
  }

  onFinish = values => {
    const { dispatch } = this.props
    const { dateRange, searchUID, targetID } = this.state
    let data = { begin: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat), searchUID: searchUID, targetID: targetID }
    dispatch({
      type: `${namespace}/getDetailReportList`,
      payload: data
    })
  }

  // 需要修改
  columns = [
    { title: '日期', dataIndex: 'timestamp', align: 'center', render: timestamp => moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss') },
    { title: 'UID', dataIndex: 'user', align: 'center' },
    { title: '合成礼物ID', dataIndex: 'targetId', align: 'center' },
    { title: '合成礼物名', dataIndex: 'targetName', align: 'center' },
    { title: '合成数量', dataIndex: 'synthesisCount', align: 'center' },
    { title: '合成礼物价值(紫水晶)', dataIndex: 'targetAmount', align: 'center' },
    { title: '返还礼物ID', dataIndex: 'compensateId', align: 'center' },
    { title: '返还礼物名称', dataIndex: 'compensateName', align: 'center' },
    { title: '返还礼物数量', dataIndex: 'compensateCount', align: 'center' },
    { title: '返还礼物价值', dataIndex: 'compensateAmount', align: 'center' },
    { title: '消耗材料单价(紫水晶)', dataIndex: 'propsAmount', align: 'center' },
    { title: '消耗材料数量', dataIndex: 'propsCount', align: 'center' },
    { title: '消耗材料名称', dataIndex: 'propsName', align: 'center' },
    { title: '消耗道具数量', dataIndex: 'giftCount', align: 'center' },
    { title: '消耗道具名称', dataIndex: 'giftName', align: 'center' },
    { title: '消耗道具ID', dataIndex: 'giftId', align: 'center' },
    { title: '紫水晶总价值', dataIndex: 'totalAmount', align: 'center' }
  ]

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { detailReportList } } = this.props
    const { dateRange } = this.state
    let headers = [
      { label: 'uid', key: 'user' },
      { label: '日期', key: 'date' },
      { label: '合成礼物ID', key: 'targetId' },
      { label: '合成礼物ID', key: 'targetId' },
      { label: '合成礼物名', key: 'targetName' },
      { label: '合成数量', key: 'synthesisCount' },
      { label: '合成礼物价值(紫水晶)', key: 'targetAmount' },
      { label: '返还礼物ID', key: 'compensateId' },
      { label: '返还礼物名称', key: 'compensateName' },
      { label: '返还礼物数量', key: 'compensateCount' },
      { label: '返还礼物价值', key: 'compensateAmount' },
      { label: '消耗材料单价(紫水晶)', key: 'propsAmount' },
      { label: '消耗材料数量', key: 'propsCount' },
      { label: '消耗材料名称', key: 'propsName' },
      { label: '消耗道具数量', key: 'giftCount' },
      { label: '消耗道具名称', key: 'giftName' },
      { label: '消耗道具ID', key: 'giftId' },
      { label: '紫水晶总价值', key: 'totalAmount' }
    ]
    return (
      <Card>
        <Form onFinish={this.onFinish}>
          <Row gutter={12}>
            <Col>
              <Form.Item name='dateRange' >
                <RangePicker defaultValue={dateRange} onChange={(date, format) => this.setState({ dateRange: date })} format={'YYYY-MM-DD'} />
              </Form.Item>
            </Col>
            <Col>
              用户uid
              <Input onChange={e => this.setState({ searchUID: e.target.value })} style={{ marginLeft: 10, width: 150 }} /> {/* 搜索按钮 */}
            </Col>
            <Col>
              合成礼物ID
              <Input onChange={e => this.setState({ targetID: e.target.value })} style={{ marginLeft: 10, width: 150 }} /> {/* 搜索按钮 */}
            </Col>
            <Col>
              <Form.Item>
                <Button type='primary' htmlType='submit'><SearchOutlined />Search</Button>
              </Form.Item>
            </Col>
            <Col>
              <Form.Item>
                <CSVLink data={detailReportList} filename={'兑换明细.csv'} headers={headers}>导出</CSVLink>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Table rowKey='index' dataSource={detailReportList} columns={this.columns} size='small' pagination={{ pageSize: 50 }} /> {/* 显示的列表 */}
      </Card>
    )
  }
}

export default DetailReportComponent
