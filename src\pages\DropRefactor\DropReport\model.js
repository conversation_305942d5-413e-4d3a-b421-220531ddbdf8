import { getMonitorFromServer } from './api'
import { genGetListTemplate, genGetRequireTemplate } from '@/utils/common'

const getCompensatConfig = genGetListTemplate('/drop/admin/compensate_query_all', 'compensatConfig')
const getMonitorBigPrize = genGetRequireTemplate('/drop_stat/monitor_big_prize2', 'monitorList')
const getSummaryListLuckDog = genGetListTemplate('/drop_stat/summary_list', 'luckDogSummaryList')
const getSummaryListZuiwan = genGetListTemplate('/drop_stat/summary_list', 'zuiwanSummaryList')
const getSummaryListBaby = genGetListTemplate('/drop_stat/summary_list', 'babySummaryList')
const getSummaryList = genGetListTemplate('/drop_stat/summary_list', 'summaryList') // 空投日报
const getDailySummary = genGetListTemplate('/drop_stat/daily_summary', 'dailysummary') // 空投日报-新
const getDailySummaryBb = genGetListTemplate('/drop_stat/daily_summary', 'dailysummarybb')
const getDailySummaryZw = genGetListTemplate('/drop_stat/daily_summary', 'dailysummaryzw')
const getPoolList2 = genGetRequireTemplate('/drop/admin/query_pool', 'poolList2')
const getProfitData = genGetRequireTemplate('/drop_stat/get_profit_summary_list', 'profitData')
const exportProfitData = genGetRequireTemplate('/drop_stat/export_profit_summary_list')
const getBundleConfigProd = genGetRequireTemplate('/drop/admin/get_bundle_reward_config')

export default {
  namespace: 'dropReport',

  state: {
    businessNameOptions: [],
    appNameOptions: [],
    monitorList: [], // 大道具监控
    compensatConfig: [], // 补足配置
    summaryList: [], // 实时信息
    dailysummary: [], // 日报数据
    luckDogSummaryList: [],
    babySummaryList: [],
    zuiwanSummaryList: [], // 追玩语音房日报
    dailysummarybb: [],
    dailysummaryzw: [],
    poolList2: [],
    profitData: { list: [], total: 0, page: 0 } // 盈亏监控数据
  },

  reducers: {
    updateMonitor (state, { payload }) { return { ...state, monitorList: payload } },
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },

  effects: {
    getDailySummary,
    getDailySummaryBb,
    getDailySummaryZw,
    getCompensatConfig,
    getMonitorBigPrize,
    getSummaryListLuckDog,
    getSummaryListZuiwan,
    getSummaryListBaby,
    getSummaryList,
    getPoolList2,
    getProfitData,
    exportProfitData,
    getBundleConfigProd,
    * getMonitor ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getMonitorFromServer, payload)

      yield put({
        type: 'updateMonitor',
        payload: Array.isArray(list) ? list : []
      })
    }
  }
}
