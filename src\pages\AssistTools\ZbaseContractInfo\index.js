import { connect } from 'dva'
import React, { Component } from 'react'
import { Button, Col, Form, Input, message, Modal, Row, Select, Space, Spin, Table } from 'antd'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { fetchAllRemoteData, parseStringToNumberList, formatOptions, timeFormater } from '@/utils/common'
import { SearchOutlined } from '@ant-design/icons'
import ExportRemoteData from '@/components/ExportRemoteData'

// const namespace = 'ZbaseContractInfo'
const appidOptions = [
  { value: 2, label: '交友' },
  { value: 14, label: '约战' },
  { value: 36, label: '宝贝' }
]

const compereRoleOptions = [
  { value: 0, label: '-' },
  { value: 1, label: '未签约[合同解约]' },
  { value: 2, label: '帽子超主/互动超级(已废弃)' },
  { value: 3, label: '普通主持' },
  { value: 4, label: '超级主持/超级个人' },
  { value: 7, label: '准超主' },
  { value: 8, label: '约战&宝贝普通主持' },
  { value: 9, label: '约战&宝贝独家主持' },
  { value: 10, label: '星光主持' }
]

const sexOptions = [
  { value: 0, label: '女' },
  { value: 1, label: '男' },
  { value: 2, label: '未知' }
]

@connect(({ ZbaseContractInfo }) => ({
  model: ZbaseContractInfo
}))

class ZbaseContractInfo extends Component {
  state = {
    list: [], loadingVisible: false, currentPage: 1, perPage: 1000, currentFetch: 0, pagination: { current: 1, pageSize: 10 }
  }

  columns = [
    { title: 'uid', dataIndex: 'liveUid', align: 'left', ellipsis: true },
    { title: 'sid', dataIndex: 'sid', align: 'left', ellipsis: true },
    { title: 'asid', dataIndex: 'asid', align: 'left', ellipsis: true },
    { title: 'owUid', dataIndex: 'owUid', align: 'left', ellipsis: true },
    { title: 'nick', dataIndex: 'nick', align: 'left', ellipsis: true },
    { title: 'yyno', dataIndex: 'yyno', align: 'left', ellipsis: true },
    { title: 'passport', dataIndex: 'passport', align: 'left', ellipsis: true },
    { title: 'sex', dataIndex: 'sex', align: 'left', ellipsis: true, options: sexOptions },
    { title: 'birthday', dataIndex: 'birthday', align: 'left', ellipsis: true },
    { title: 'signTime', dataIndex: 'signTime', align: 'left', ellipsis: true, disabledSearch: true, render: (val, record) => !val ? '-' : timeFormater(val / 1000, 1) },
    { title: 'finishTime', dataIndex: 'finishTime', align: 'left', ellipsis: true, disabledSearch: true, render: (val, record) => !val ? '-' : timeFormater(val / 1000, 1) },
    { title: 'appid', dataIndex: 'appid', align: 'left', ellipsis: true, options: appidOptions },
    { title: 'groupName', dataIndex: 'groupName', align: 'left', ellipsis: true },
    { title: 'compereRole', dataIndex: 'compereRole', align: 'left', ellipsis: true, options: compereRoleOptions },
    { title: 'superAnchorSign', dataIndex: 'superAnchorSign', align: 'left', ellipsis: true },
    { title: 'companySign', dataIndex: 'companySign', align: 'left', ellipsis: true },
    { title: 'weight', dataIndex: 'weight', align: 'left', ellipsis: true },
    { title: 'months', dataIndex: 'months', align: 'left', ellipsis: true },
    { title: 'templateId', dataIndex: 'templateId', align: 'left', ellipsis: true },
    { title: 'settleMode', dataIndex: 'settleMode', align: 'left', ellipsis: true }
  ]

  exportRenderColumns = (columns) => {
    return columns
  }

  renderColumns = (columns) => {
    columns = columns || this.columns
    let getLocalFilter = (column) => {
      return (v, rec) => {
        return String(v).localeCompare(rec[column.dataIndex]) === 0
      }
    }
    let getOptionRender = (options) => {
      return (val, record) => formatOptions(val, options)
    }
    return columns.map(v => {
      if (v.disabledSearch) {
        return v
      }
      if (v.options) {
        v.options.forEach(opt => { opt.text = opt.label })
        v.filters = v.options
        v.onFilter = getLocalFilter(v)
        v.filterIcon = <SearchOutlined />
        v.filterMode = 'tree'
        v.filterSearch = true

        if (!v.render) {
          v.render = getOptionRender(v.options)
        }

        return v
      }
      v = Object.assign(this.getColumnSearchTextProps(v.dataIndex), v)
      return v
    })
  }

  // 搜索文本
  getColumnSearchTextProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={node => {
            this.searchInput = node
          }}
          placeholder={`Search ...`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => this.handleSearch(selectedKeys, confirm, dataIndex)}
          style={{ marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button size='small' onClick={() => this.handleReset(clearFilters)}>重置</Button>
          <Button type='primary' size='small' onClick={() => this.handleSearch(selectedKeys, confirm, dataIndex)}>确定</Button>
        </Space>
      </div>
    ),
    filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
        : ''
  })

  handleSearch = (selectedKeys, confirm, dataIndex, table) => {
    confirm()
    this.setState({
      searchText: '', searchedColumn: dataIndex
    })
  }

  handleReset = (clearFilters, table) => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  // 点击查询
  onQueryClick = (values) => {
    let sUids = values.uids
    let appID = values.appID || 2

    let uids = parseStringToNumberList(sUids)
    if (!uids || uids.length < 1) {
      message.warn('当前没有输入UID')
      return false
    }

    const self = this
    fetchAllRemoteData({
      pageSize: 100,
      showLoading: (page, pageSize, total) => {
        self.setState({ loadingVisible: true, currentPage: page, perPage: pageSize, currentFetch: total })
      },
      closeLoading: (page, pageSize, total) => {
        self.setState({ loadingVisible: false })
      },
      uriBuilder: (page, pageSize) => {
        let startIndex = (page - 1) * pageSize
        let endIndex = startIndex + pageSize
        if (endIndex > uids.length) {
          endIndex = uids.length
        }
        let subUidList = []
        for (let i = startIndex; i < endIndex; i++) {
          subUidList.push(uids[i])
        }
        return {
          fetchURI: `/assist_tools/compere/batch_get_contract_info`,
          fetchBody: JSON.stringify({
            appID: appID,
            uidList: subUidList
          })
        }
      },
      method: 'POST',
      callback: (dataList) => {
        dataList.forEach((item, index) => { item.index = index })
        self.setState({ list: dataList })
      }
    })
  }

  render () {
    const { route } = this.props
    const { loadingVisible, currentPage, perPage, currentFetch, list } = this.state

    const pagination = {
      pageSize: this.state.pagination.pageSize,
      current: this.state.pagination.current,
      pageSizeOptions: [10, 20, 50, 100],
      showSizeChanger: true,
      onChange: (page, pageSize) => {
        this.setState({ pagination: { current: page, pageSize: pageSize } })
      },
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    }

    const columns = this.renderColumns(this.columns)
    const exportColumns = this.exportRenderColumns(this.columns)

    return (
      <>
        <PageHeaderWrapper title={route.name}>
          <Row style={{ marginBottom: '1em' }}>
            <Form labelCol={{ span: 12 }} layout={'inline'} ref={form => {
              this.formRefQuery = form
            }} onFinish={this.onQueryClick}>
              <Form.Item name={'uids'} label={'UID'} required>
                <Input.TextArea style={{ height: 100 }} placeholder='UID' allowClear />
              </Form.Item>

              <Form.Item name={'appID'} label={'业务'} initialValue={2}>
                <Select options={appidOptions} filterOption />
              </Form.Item>

              <Button type='primary' htmlType='submit'>查询</Button>
              <ExportRemoteData buttonStyle={{ marginLeft: 20 }} filename={'zbase_compere_contract_info'} columns={exportColumns} dataProvider={() => list} />
            </Form>
          </Row>
          <Row style={{ marginBottom: '1em' }}>
            <Col span={24}>
              <Table columns={columns}
                dataSource={list}
                size='small'
                scroll={{ x: 'max-content' }}
                pagination={pagination}
                showSorterTooltip={false}
                rowKey={record => record.uid}
                expandable={{
                  expandedRowRender: record => <pre style={{ margin: 0 }}>{JSON.stringify(record, null, 4)}</pre>
                }}
              />
            </Col>
          </Row>

          <Modal visible={loadingVisible} footer={null} closable={false} centered>
            <Spin
              tip={'正在加载第[' + currentPage + ']页数据（每页[' + perPage + ']条）, 当前已加载[' + currentFetch + ']......'} />
          </Modal>
        </PageHeaderWrapper>
      </>
    )
  }
}

export default ZbaseContractInfo
