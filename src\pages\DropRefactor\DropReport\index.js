import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card } from 'antd'
import DropReportSummaryComponent from './components/summary'
import DropReportBigPrizeComponent from './components/big_prize'
import DropReportBBSummaryComponentNew from './components/bb_summary_new'
import DropReportZWSummaryComponentNew from './components/zw_summary_new'
import NumberMonitor from './components/numberMonitor'
import ProfitMonitor from './components/profitMonitor'
import { connect } from 'dva'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import { initGlobalBossConfig } from '../globalConfig'
import { isVoiceRoomPath } from '../dropCommon'

const namespace = 'dropReport'

@connect(({ dropReport }) => ({
  model: dropReport
}))

// 抢空投-空投日报
class DropRefactoryQuery extends Component {
  state = {
    groupType: isVoiceRoomPath(this.props.route.path) ? 'vr' : 'jybw'
  }

  componentDidMount () {
    initGlobalBossConfig(this.changeState, isVoiceRoomPath(this.props.route.path) ? 'vr' : 'jybw')
    this.callModel('getCompensatConfig')
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  render () {
    const { route } = this.props
    const { groupType } = this.state
    const isVoiceRoom = isVoiceRoomPath(route.path)

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs id='drfq' type='card' defaultActiveKey={isVoiceRoom ? '5' : '1'} >

            {
              isVoiceRoom
                ? <> 
                  <TabPane tab='语音房日报' key='5'>
                    <DropReportZWSummaryComponentNew sel={4} />
                  </TabPane>
                </>
                : <>
                  <TabPane tab='交友日报' key='1'>
                    <DropReportSummaryComponent />
                  </TabPane>  
                  <TabPane tab='宝贝日报' key='6'>
                    <DropReportBBSummaryComponentNew />
                  </TabPane>
                </>
            }
            <TabPane tab='大礼发放监控' key='3'>
              <DropReportBigPrizeComponent groupType={groupType} />
            </TabPane>

            <TabPane tab='发放数量监控' key='7'>
              <NumberMonitor groupType={groupType} />
            </TabPane>

            <TabPane tab='盈亏监控' key='8'>
              <ProfitMonitor groupType={groupType} />
            </TabPane>

          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default DropRefactoryQuery
