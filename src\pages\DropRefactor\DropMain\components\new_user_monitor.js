import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, DatePicker, Button, Divider, Table, Row, Col, Space, Tooltip, InputNumber } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
var moment = require('moment')

const namespace = 'dropMain'

@connect(({ dropMain }) => ({
  model: dropMain
}))

class NewUserMonitor extends Component {
  state = {
    selectTimeRange: [moment().startOf('day'), moment().endOf('day')],
    exportTimeRange: null
  }
  componentDidMount = () => {
    this.refreshUserList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // 查询目标用户列表
  refreshUserList = () => {
    const { selectTimeRange } = this.state
    this.setState({ exportTimeRange: selectTimeRange })
    this.callModel('getNewPlayerHitList', {
      params: {
        start: selectTimeRange[0].unix(),
        end: selectTimeRange[1].unix(),
        hit: this.state.hit,
        page: 0
      }
    })
  }
  // 表头显示提示语
 genColumnTooltip = (title) => {
   return {
     filterDropdown: (<span />),
     filterIcon: (
       <Tooltip placement='top' title={title}>
         <QuestionCircleOutlined style={{ fontSize: '16px' }} />
       </Tooltip>
     )
   }
 }
 // 导出报表-服务器导出
 exportTable2 = () => {
   const { selectTimeRange: [startTime, endTime] } = this.state
   let url = `/drop_stat/new_player_hit_list?start=${startTime.unix()}&end=${endTime.unix()}&type=xlsx`
   window.open(url)
 }
 columnsFixer = (before) => {
   for (let i = 0; i < before.length; i++) {
     before[i].align = 'center'
   }
   return before
 }

 render () {
   const { newPlayerHitList } = this.props.model
   const { selectTimeRange } = this.state
   const columns = [
     { title: '序号', dataIndex: 'index' },
     { title: '日期', dataIndex: 'timeFormat' },
     { title: 'UID', dataIndex: 'uid' },
     { title: '渠道', dataIndex: 'chName' },
     { title: '风控', dataIndex: 'riskCode', render: (v, r) => { return v === 0 ? 'PASS' : `风控拦截: (${r.reason})` } },
     //  { title: '原因', dataIndex: 'reason' },
     { title: '分组', dataIndex: 'group', render: (v) => { return ['未知', '实验组', '对照组'][v] } },
     { title: '策略任务', dataIndex: 'hit' },
     { title: '策略奖励道具', dataIndex: 'prize', render: (v) => v.name || '-' },
     { title: '单价', dataIndex: 'prize', render: (v) => v.value },
     { title: '数量', dataIndex: 'prize', render: (v) => v.count },
     { title: 'SID', dataIndex: 'sid' },
     { title: 'SSID', dataIndex: 'ssid' }
   ]

   return (
     <Card>
       <Row>
         <Space>
           <DatePicker.RangePicker showTime value={selectTimeRange} format='YYYY-MM-DD HH:mm:ss' onChange={(v) => this.setState({ selectTimeRange: v })} />
           <InputNumber placeholder='请输入策略任务' style={{ width: '10em' }} onChange={(v) => { this.setState({ hit: v }) }} />
           <Button type='primary' onClick={e => this.refreshUserList()}>查询</Button>
           <Button type='dashed' onClick={e => this.exportTable2()}>导出</Button>
         </Space>
       </Row>
       <Divider />
       <Row>
         <Col span={24}>
           <Table columns={this.columnsFixer(columns)} dataSource={newPlayerHitList} />
         </Col>
       </Row>
     </Card>
   )
 }
}

export default NewUserMonitor
