import { genGetListTemplate, genUpdateTemplate } from '@/utils/common'

const getPlayModeList = genGetListTemplate('/util_info/play_mode_list', 'palyModeList')
const getPlayModeRuleList = genGetListTemplate('/ch_protection/admin/get_play_mode_rule', 'ruleList')

const updatePlayModeRule = genUpdateTemplate('/ch_protection/admin/update_play_mode_rule')
const deletePlayModeRule = genUpdateTemplate('/ch_protection/admin/delete_play_mode_rule')
const playModeRuleEntry = genUpdateTemplate('/ch_protection/admin/play_mode_rule_entry')

export default {
  namespace: 'joinChannelCheck',
  state: {
    palyModeList: [],
    ruleList: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getPlayModeList,
    getPlayModeRuleList,
    updatePlayModeRule,
    deletePlayModeRule,
    playModeRuleEntry
  }
}
