import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs, Card } from 'antd'
import { connect } from 'dva'
import ChannelUidBlackList from './TabPage/uidBlackList'
import OpLogDisplayer from './TabPage/opLogDisplay'
import ChannelStatusDisplayer from './TabPage/channelStatus'
// import AddSidToBlackList from './TabPage/addSidToBlackList'

const TabPane = Tabs.TabPane

@connect(({ channelEnterControl }) => ({
  model: channelEnterControl
}))

class ChannelEnterControl extends Component {
  state = { activeKey: '1' }
  onTabClick = key => {
    this.setState({ activeKey: key })
  }

  render () {
    const { route } = this.props
    const { activeKey } = this.state

    return (
      <PageHeaderWrapper title={route.name} >
        <Card>
          <Tabs type='card' defaultActiveKey='1' size='small' onTabClick={this.onTabClick}>
            <TabPane tab='UID白名单管理' key='1'>
              { activeKey === '1' ? <ChannelUidBlackList /> : ''}
            </TabPane>
            <TabPane tab='事件日志' key='2'>
              { activeKey === '2' ? <OpLogDisplayer /> : ''}
            </TabPane>
            <TabPane tab='限制状态' key='3'>
              { activeKey === '3' ? <ChannelStatusDisplayer /> : ''}
            </TabPane>
            {/* <TabPane tab='手动添加限制规则' key='4'>
              { activeKey === '4' ? <AddSidToBlackList /> : ''}
            </TabPane> */}
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default ChannelEnterControl
