import request from '@/utils/request'
import { stringify } from 'qs'

export function getListFromServer () {
  return request('/drop/admin/get_flow_config_list')
}

export function removeListFromServer (params) {
  return request(`/drop/admin/remove_flow_config?${stringify(params)}`)
}

export function upsetListFromServer (params) {
  return request(`/drop/admin/add_flow_config`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function approvalConfigFromServer (params) {
  return request(`/drop/admin/approval_flow_config?${stringify(params)}`)
}

export function getConfigListFromServer () {
  return request('/drop/admin/get_flow_operator_list')
}

export function getReportListFromServer (params) {
  return request(`/drop/admin/get_flow_report_list?${stringify(params)}`)
}
