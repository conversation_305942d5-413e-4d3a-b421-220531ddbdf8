import { connect } from 'dva'
import React, { Component } from 'react'
import { Button, Col, Form, Input, message, Modal, Row, Space, Spin, Table, Tooltip } from 'antd'
import PopImage from '@/components/PopImage'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { fetchAllRemoteData, parseStringToNumberList, formatOptions } from '@/utils/common'
import { SearchOutlined } from '@ant-design/icons'
import ExportRemoteData from '@/components/ExportRemoteData'

// const namespace = 'FriendUserInfo'

const sexOptions = [
  { value: 0, label: '女' },
  { value: 1, label: '男' },
  { value: 2, label: '未知' }
]

@connect(({ FriendUserInfo }) => ({
  model: FriendUserInfo
}))

class FriendUserInfo extends Component {
  state = {
    list: [], loadingVisible: false, currentPage: 1, perPage: 1000, currentFetch: 0, pagination: { current: 1, pageSize: 10 }
  }

  columns = [
    { title: 'uid', dataIndex: 'uid', align: 'left', ellipsis: true },
    { title: 'nick', dataIndex: 'nick', align: 'left', ellipsis: true },
    { title: 'sex', dataIndex: 'sex', align: 'left', ellipsis: true, options: sexOptions },
    { title: 'birthday', dataIndex: 'birthday', align: 'left', ellipsis: true },
    { title: 'sign', dataIndex: 'sign', align: 'left', ellipsis: true },
    {
      title: 'tag',
      dataIndex: 'tag',
      align: 'left',
      ellipsis: true,
      render: (val, record) => val && val.length > 0 ? <Tooltip title={val.join(',')}>{val.join(',')}</Tooltip> : '',
      exportRender: (val, record) => val
    },
    {
      title: 'avatar_url',
      dataIndex: 'avatar_info',
      align: 'left',
      ellipsis: true,
      disabledSearch: true,
      render: (val, record) => val && val.url ? <PopImage value={val.url} /> : '',
      exportRender: (val, record) => val
    },
    { title: 'latest_login_time', dataIndex: 'latest_login_time', align: 'left', ellipsis: true }
  ]

  exportRenderColumns = (columns) => {
    return columns
  }

  renderColumns = (columns) => {
    columns = columns || this.columns
    let getLocalFilter = (column) => {
      return (v, rec) => {
        return String(v).localeCompare(rec[column.dataIndex]) === 0
      }
    }
    let getOptionRender = (options) => {
      return (val, record) => formatOptions(val, options)
    }
    return columns.map(v => {
      if (v.options) {
        v.options.forEach(opt => { opt.text = opt.label })
        v.filters = v.options
        v.onFilter = getLocalFilter(v)
        v.filterIcon = <SearchOutlined />
        v.filterMode = 'tree'
        v.filterSearch = true

        if (!v.render) {
          v.render = getOptionRender(v.options)
        }

        return v
      }
      v = Object.assign(this.getColumnSearchTextProps(v.dataIndex), v)
      return v
    })
  }

  // 搜索文本
  getColumnSearchTextProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={node => {
            this.searchInput = node
          }}
          placeholder={`Search ...`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => this.handleSearch(selectedKeys, confirm, dataIndex)}
          style={{ marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button size='small' onClick={() => this.handleReset(clearFilters)}>重置</Button>
          <Button type='primary' size='small' onClick={() => this.handleSearch(selectedKeys, confirm, dataIndex)}>确定</Button>
        </Space>
      </div>
    ),
    filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
        : ''
  })

  handleSearch = (selectedKeys, confirm, dataIndex, table) => {
    confirm()
    this.setState({
      searchText: '', searchedColumn: dataIndex
    })
  }

  handleReset = (clearFilters, table) => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  // 点击查询
  onQueryClick = (values) => {
    // let sUids = '1595730809\n' +
    //   '2565070967\n' +
    //   '2912407659\n' +
    //   '59964976\n' +
    //   '2459981888\n' +
    //   '63002105\n' +
    //   '193195501\n' +
    //   '77214953\n' +
    //   '22381751\n' +
    //   '2779806654\n' +
    //   '2902048438\n' +
    //   '4836596\n' +
    //   '2912019473\n' +
    //   '12892785\n' +
    //   '66378256\n' +
    //   '996592353\n' +
    //   '1444841306\n' +
    //   '2657093728\n' +
    //   '4724218\n' +
    //   '500676508\n' +
    //   '95309201\n' +
    //   '495177249'

    let sUids = ''
    sUids = values.uids || sUids

    let uids = parseStringToNumberList(sUids)
    if (!uids || uids.length < 1) {
      message.warn('当前没有输入UID')
      return false
    }

    const self = this
    fetchAllRemoteData({
      pageSize: 1000,
      showLoading: (page, pageSize, total) => {
        self.setState({ loadingVisible: true, currentPage: page, perPage: pageSize, currentFetch: total })
      },
      closeLoading: (page, pageSize, total) => {
        self.setState({ loadingVisible: false })
      },
      uriBuilder: (page, pageSize) => {
        let startIndex = (page - 1) * pageSize
        let endIndex = startIndex + pageSize
        if (endIndex > uids.length) {
          endIndex = uids.length
        }
        let subUidList = []
        for (let i = startIndex; i < endIndex; i++) {
          subUidList.push(uids[i])
        }
        return {
          fetchURI: `/assist_tools/friend/batch_get_userinfo`,
          fetchBody: JSON.stringify({
            uidList: subUidList
          })
        }
      },
      method: 'POST',
      callback: (dataList) => {
        dataList.forEach((item, index) => { item.index = index })
        self.setState({ list: dataList })
      }
    })
  }

  render () {
    const { route } = this.props
    const { loadingVisible, currentPage, perPage, currentFetch, list } = this.state

    const pagination = {
      pageSize: this.state.pagination.pageSize,
      current: this.state.pagination.current,
      pageSizeOptions: [10, 20, 50, 100],
      showSizeChanger: true,
      onChange: (page, pageSize) => {
        this.setState({ pagination: { current: page, pageSize: pageSize } })
      },
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    }

    const columns = this.renderColumns(this.columns)
    const exportColumns = this.exportRenderColumns(this.columns)

    return (
      <>
        <PageHeaderWrapper title={route.name}>
          <Row style={{ marginBottom: '1em' }}>
            <Form labelCol={{ span: 12 }} layout={'inline'} ref={form => {
              this.formRefQuery = form
            }} onFinish={this.onQueryClick}>
              <Form.Item name={'uids'} label={'UID'} required>
                <Input.TextArea style={{ height: 100 }} placeholder='UID' allowClear />
              </Form.Item>

              <Button type='primary' htmlType='submit'>查询</Button>
              <ExportRemoteData buttonStyle={{ marginLeft: 20 }} filename={'webdb_userinfo'} columns={exportColumns} dataProvider={() => list} />
            </Form>
          </Row>
          <Row style={{ marginBottom: '1em' }}>
            <Col span={24}>
              <Table columns={columns}
                dataSource={list}
                size='small'
                scroll={{ x: 'max-content' }}
                pagination={pagination}
                showSorterTooltip={false}
                rowKey={record => record.index}
                expandable={{
                  expandedRowRender: record => <pre style={{ margin: 0 }}>{JSON.stringify(record, null, 4)}</pre>
                }}
              />
            </Col>
          </Row>

          <Modal visible={loadingVisible} footer={null} closable={false} centered>
            <Spin
              tip={'正在加载第[' + currentPage + ']页数据（每页[' + perPage + ']条）, 当前已加载[' + currentFetch + ']......'} />
          </Modal>
        </PageHeaderWrapper>
      </>
    )
  }
}

export default FriendUserInfo
