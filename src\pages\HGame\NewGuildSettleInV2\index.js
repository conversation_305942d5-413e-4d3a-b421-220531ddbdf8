import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs, Card } from 'antd'

import NewGuildSettleInSuperCrysta from './components/superCrysta'

class NewGuildSettleIn extends Component {
  state = { activeKey: '1' }
  onTabClick = key => {
    this.setState({ activeKey: key })
  }

  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs type='card' onTabClick={this.onTabClick}>
            <NewGuildSettleInSuperCrysta />
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default NewGuildSettleIn
