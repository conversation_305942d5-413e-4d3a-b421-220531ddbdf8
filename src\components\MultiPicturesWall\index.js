import React, { Component } from 'react'
import { Upload, Modal, Popover } from 'antd'
import { PlusOutlined } from '@ant-design/icons'

class MultiPicturesWall extends Component {
  constructor (props) {
    super(props)

    // console.log(props)

    this.state = {
      fileList: Array.isArray(props.value) ? props.value.map((i, index) => { return { uid: index, status: 'done', url: i, thumbUrl: i, response: { urls: [i], status: 0 } } }) : [],
      previewImage: '',
      previewVisible: false
    }
  }

  componentWillReceiveProps (nextProps) {
    // 受控组件， 用于更新
    const { value } = nextProps
    // console.log(value)
    this.setState({
      fileList: Array.isArray(value) ? value.map((i, index) => { return { uid: index, status: 'done', url: i, thumbUrl: i, response: { urls: [i], status: 0 } } }) : []
    })
  }

  handlePreview = file => {
    this.setState({
      previewImage: file.url || file.thumbUrl,
      previewVisible: true
    })
  }

  handleCancel = () => this.setState({ previewVisible: false })

  handleChange = ({ fileList }) => {
    // console.log(fileList)
    // this.props.onChange(url) // update getFiledDecorator
    var urls = fileList.map(i => {
      if (i.status === 'done' && i.response && i.response.status === 0) {
        return i.response.urls[0]
      }
    })
    // console.log(urls)
    this.props.onChange(urls)
    this.setState({ fileList })
  }

  renderImage = () => {
    const { value } = this.props
    const content = (
      <div>
        <img src={value} style={{ maxHeight: 200, maxWidth: 200 }} />
      </div>
    )
    return (
      <Popover placement='right' content={content} title={null}>
        <img width='102' height='102' src={value} />
        <i className='anticon-delete' />
      </Popover>
    )
  }

  render () {
    const { previewVisible, previewImage, fileList } = this.state
    const uploadButton = (
      <div>
        <PlusOutlined />
        <div className='ant-upload-text'>Upload</div>
      </div>
    )

    return (
      <div className='clearfix'>
        <Upload
          action='https://fts.yy.com/fs/uploadfiles'
          listType='picture-card'
          fileList={fileList}
          onPreview={this.handlePreview}
          onChange={this.handleChange}
          // showUploadList={false}
          data={file => ({ bucket: 'makefriends', files: file })}
        >
          {uploadButton }
        </Upload>
        <Modal visible={previewVisible} footer={null} onCancel={this.handleCancel}>
          <img alt='example' style={{ width: '100%' }} src={previewImage} />
        </Modal>
      </div>
    )
  }
}

export default MultiPicturesWall
