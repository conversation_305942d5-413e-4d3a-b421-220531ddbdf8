import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card } from 'antd'
import { connect } from 'dva'
import AdminMenuMenu from './components/menu'
import AdminMenuAuthExport from './components/export'
import AdminMenuAuthProposer from './components/proposer'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'

@connect(({ adminMenu }) => ({
  model: adminMenu
}))

class AdminMenu extends Component { // 默认页面组件，不需要修改
  render () {
    const { route } = this.props 

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs type='card' defaultActiveKey='1' >
            <TabPane tab='菜单配置' key='1'>
              <AdminMenuMenu />
            </TabPane>
            <TabPane tab='权限管理' key='2'>
              <AdminMenuAuthExport />
            </TabPane>
            <TabPane tab='申请记录' key='3'>
              <AdminMenuAuthProposer />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default AdminMenu // 保证唯一
