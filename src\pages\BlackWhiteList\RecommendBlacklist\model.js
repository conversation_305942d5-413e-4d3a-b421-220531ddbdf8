import { getLists, add, remove, getExemptLists, addExempt, removeExempt } from './api'
import { message } from 'antd'

export default {
  namespace: 'RecommendBlacklist',

  state: {
    list: [],
    exemptList: []
  },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    },

    updateExemptList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        exemptList: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      try {
        const { data: { list } } = yield call(getLists, payload)
        yield put({
          type: 'updateList',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        message.error('exception:', e)
      }
    },

    * addItem ({ payload, getListParam }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(add, payload)
        if (status === 0) {
          message.success('add success')
          yield put({
            type: 'getList',
            payload: getListParam
          })
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('exception:', e)
      }
    },

    * removeItem ({ payload, getListParam }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(remove, payload)
        if (status === 0) {
          message.success('remove success')
          yield put({
            type: 'getList',
            payload: getListParam
          })
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('exception:', e)
      }
    },

    * getExemptList ({ payload }, { call, put }) {
      try {
        const { data: { list } } = yield call(getExemptLists, payload)
        yield put({
          type: 'updateExemptList',
          payload: Array.isArray(list) ? list : []
        })
      } catch (e) {
        message.error('exception:', e)
      }
    },

    * addExemptItem ({ payload, getListParam }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(addExempt, payload)
        if (status === 0) {
          if (msg !== undefined && msg.length > 0) {
            message.success(msg)
          } else {
            message.success('已成功提交审核，待审核完毕后生效')
          }

          yield put({
            type: 'getExemptList',
            payload: getListParam
          })
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('exception:', e)
      }
    },

    * removeExemptItem ({ payload, getListParam }, { call, put }) {
      try {
        const { data: { status, msg } } = yield call(removeExempt, payload)
        if (status === 0) {
          if (msg !== undefined && msg.length > 0) {
            message.success(msg)
          } else {
            message.success('删除成功')
          }
          yield put({
            type: 'getExemptList',
            payload: getListParam
          })
        } else {
          message.error('failed' + msg)
        }
      } catch (e) {
        message.error('exception:', e)
      }
    }
  }
}

// https://jyboss-test.yy.com/n/black_white_list/recommend_blacklist
