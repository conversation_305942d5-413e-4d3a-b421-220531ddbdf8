import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Button, Divider, Select, Card, Tabs, Input, DatePicker, message, Modal } from 'antd'
import { Form } from '@ant-design/compatible'
import PopImage from '@/components/PopImage'
import { connect } from 'dva'

const namespace = 'JobHopping'
const TabPane = Tabs.TabPane
const Option = Select.Option
var moment = require('moment')

@connect(({ JobHopping }) => ({
  JobHopping
}))

class Index extends Component {
  // column structs.
  columns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center', fixed: 'left', width: 60 },
    { title: '业务',
      dataIndex: 'business',
      key: 'business',
      align: 'center',
      fixed: 'left',
      width: 60,
      render: (text, record) => {
        switch (text) {
          case 1: return '交友'
          case 2: return '约战'
          case 3: return '宝贝'
        }
        return text
      }
    },
    { title: '主播UID', dataIndex: 'uid', key: 'uid', align: 'center', fixed: 'left', width: 100 },
    { title: '报备次数', dataIndex: 'count', key: 'count', align: 'center', width: 100 },
    { title: '主播YY号', dataIndex: 'imid', key: 'imid', align: 'center', width: 100 },
    { title: '签约频道ID', dataIndex: 'asid', key: 'asid', align: 'center', width: 100 },
    { title: '签约身份',
      dataIndex: 'contract_type',
      key: 'contract_type',
      align: 'center',
      width: 100,
      render: (text, record) => {
        switch (text) {
          case 1: return '普通主持'
          case 2: return '超级主持'
          case 3: return '超级天团'
          case 4: return '帽子超级'
        }
      }
    },
    { title: '跳槽模板/平台', dataIndex: 'platform_name', key: 'platform_name', align: 'center', width: 130 },
    { title: '跳槽UID', dataIndex: 'job_hopping_uid', key: 'job_hopping_uid', align: 'center', width: 100 },
    { title: '跳槽YY号/ID', dataIndex: 'job_hopping_id', key: 'asijob_hopping_id', align: 'center', width: 120 },
    { title: '跳槽身份',
      dataIndex: 'job_hopping_type',
      key: 'job_hopping_type',
      align: 'center',
      width: 100,
      render: (text, record) => {
        switch (text) {
          case 1: return '普通主持'
          case 2: return '超级主持'
          case 3: return '超级天团'
          case 4: return '帽子超级'
        }
      }
    },
    { title: '跳槽频道ID', dataIndex: 'job_hopping_asid', key: 'job_hopping_asid', align: 'center', width: 100 },
    { title: '提交时间', dataIndex: 'submit_date', key: 'submit_date', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD'), width: 120 },
    { title: '处理时间',
      dataIndex: 'audit_date',
      key: 'audit_date',
      align: 'center',
      width: 120,
      render: (text, record) => {
        if (text > 0) {
          return moment.unix(text).format('YYYY-MM-DD')
        }
        return ''
      }
    },
    { title: '处理状态',
      dataIndex: 'audit_date',
      key: 'audit_date',
      align: 'center',
      width: 100,
      render: (text, record) => {
        if (text > 0) {
          return '已处理'
        }
        return '未处理'
      }
    },
    { title: '详情',
      align: 'center',
      width: 120,
      render: (text, record) => (
        <span>
          <a onClick={this.showInfoModal(record)}>查看</a>
        </span>
      )
    }
  ]

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, infoVisible: false, selectedRowKeys: [], info: {} }

  showInfoModal = (record) => () => {
    this.setState({ infoVisible: true, info: record })
  }

  hideInfoModal = () => {
    this.setState({ infoVisible: false })
  }

  handleBusinessChange = (value) => {
    this.setState({ business: value.key })
  }

  handleCountChange = (value) => {
    this.setState({ count: value.key })
  }

  handleContractTypeChange = (value) => {
    this.setState({ contractType: value.key })
  }

  handlePlatformChange = (value) => {
    this.setState({ platform: value.key })
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = {}
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  handleDateChange = (date, dateString) => {
    var t = 0
    if (dateString) {
      t = new Date(dateString).getTime() / 1000
    }
    this.setState({ submitDate: t })
  }

  saveInfoFormRef = (formRef) => {
    this.InfoFormRef = formRef
  }

  handleSearch = () => {
    if (this.state.uid && !/^\d+$/.test(this.state.uid)) {
      message.error('请准确输入主播uid')
      return
    }
    if (this.state.asid && !/^\d+$/.test(this.state.asid)) {
      message.error('请准确输入签约频道ID')
      return
    }

    const { dispatch } = this.props
    const data = {
      uid: this.state.uid,
      business: this.state.business,
      count: this.state.count,
      asid: this.state.asid,
      contract_type: this.state.contractType,
      platform: this.state.platform,
      submit_date: this.state.submitDate
    }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  onSelectChange = (selectedRowKeys, selectedRows) => {
    var value = selectedRows.map(item => item.id).join('%2C')
    this.setState({ selectedRowKeys })
    this.setState({ ids: value })
    // this.setState({ exportKey: selectedRows })
  }

  handleExport = () => {
    // this.setState({ selectedRowKeys: null })
    console.log(this.state.ids)
    return '/ecology_compere_manage_boss/export_job_hopping_list?ids=' + this.state.ids
  }

  // content
  render () {
    const { selectedRowKeys } = this.state
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange
    }

    const { route, JobHopping: { list } } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='1'>
            <TabPane tab='全部' key='1'>
              <Form>
                业务
                <Divider type='vertical' /> {/* 分割线 */}
                <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 80 }} onChange={this.handleBusinessChange}>
                  <Option value='0'>全部</Option>
                  <Option value='1'>交友</Option>
                  <Option value='2'>约战</Option>
                  <Option value='3'>宝贝</Option>
                </Select>
                <Divider type='vertical' /> {/* 分割线 */}
                <Input placeholder='搜索主播 uid' onChange={e => this.setState({ uid: e.target.value })} style={{ width: 110 }} />
                <Divider type='vertical' />
                报备次数
                <Divider type='vertical' /> {/* 分割线 */}
                <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 100 }} onChange={this.handleCountChange}>
                  <Option value='0'>全部</Option>
                  <Option value='1'>首次</Option>
                  <Option value='2'>非首次</Option>
                </Select>
                <Divider type='vertical' /> {/* 分割线 */}
                <Input placeholder='签约频道ID' onChange={e => this.setState({ asid: e.target.value })} style={{ width: 110 }} />
                <Divider type='vertical' />
                签约身份
                <Divider type='vertical' /> {/* 分割线 */}
                <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 120 }} onChange={this.handleContractTypeChange}>
                  <Option value='0'>全部</Option>
                  <Option value='1'>普通主持</Option>
                  <Option value='2'>超级主持</Option>
                  <Option value='4'>帽子超级</Option>
                </Select>
                <Divider type='vertical' />
                跳槽模板/平台
                <Divider type='vertical' /> {/* 分割线 */}
                <Select labelInValue defaultValue={{ key: '0' }} style={{ width: 80 }} onChange={this.handlePlatformChange}>
                  <Option value='0'>全部</Option>
                  <Option value='1'>交友</Option>
                  <Option value='2'>约战</Option>
                  <Option value='3'>宝贝</Option>
                  <Option value='4'>外站</Option>
                </Select>
                <Divider type='vertical' /> {/* 分割线 */}
                提交时间
                <Divider type='vertical' /> {/* 分割线 */}
                <DatePicker onChange={this.handleDateChange} format={'YYYY/MM/DD'} />
                <Divider type='vertical' /> {/* 分割线 */}
                <Button type='primary' size='small' onClick={this.handleSearch}>搜索</Button>
                <Divider type='vertical' /> {/* 分割线 */}
                <Button type='primary' size='small' href={this.handleExport()}>导出</Button>
                <Table rowKey={(record, index) => index} rowSelection={rowSelection} dataSource={list} bordered columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 1960, y: 640 }} />
              </Form>
            </TabPane>
          </Tabs>
        </Card>
        <InfoModal wrappedComponentRef={this.saveInfoFormRef} {...this.state} onCancel={this.hideInfoModal} onOk={this.hideInfoModal} />
      </PageHeaderWrapper>
    )
  }
}

class InfoModal extends Component {
  getBusiness = (business) => {
    switch (business) {
      case 1: return '交友'
      case 2: return '约战'
      case 3: return '宝贝'
      default: return '未知'
    }
  }
  getContractType = (contractType) => {
    switch (contractType) {
      case 1: return '普通主持'
      case 2: return '超级主持'
      case 3: return '超级天团'
      case 4: return '帽子超级'
    }
  }
  getLiveTerminal = (liveTerminal) => {
    switch (liveTerminal) {
      case 1: return '手机'
      case 2: return 'PC'
      default: return '未知'
    }
  }
  getAuditState = (auditDate) => {
    if (auditDate > 0) {
      return '已处理'
    }
    return '未处理'
  }
  getAuditDate = (auditDate) => {
    if (auditDate > 0) {
      return moment.unix(auditDate).format('YYYY-MM-DD')
    }
    return ''
  }
  render () {
    const { info, infoVisible, onOk, onCancel } = this.props
    console.log('info: ' + info)
    return (
      <Modal visible={infoVisible} title='详情信息' cancelText='关闭' okText='知道了' onOk={onOk} onCancel={onCancel} >
        <p><font color='red'><strong>处理进度：</strong></font></p>
        <p>业务: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{this.getBusiness(info.business)}</p>
        <p>处理状态: &nbsp;&nbsp;{this.getAuditState(info.audit_date)}</p>
        <p>报备次数: &nbsp;&nbsp;{info.count}</p>
        <hr />
        <p><font color='red'><strong>主持与频道签约信息：</strong></font></p>
        <p>主播UID: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{info.uid}</p>
        <p>主播YY号: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{info.imid}</p>
        <p>签约频道ID: &nbsp;&nbsp;{info.asid}</p>
        <p>签约身份: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{this.getContractType(info.contract_type)}</p>
        <p>直播截图: &nbsp;&nbsp;<PopImage value={info.live_image} />&nbsp;&nbsp;&nbsp;&nbsp;<a href={info.live_image}>下载</a></p>
        <p>直播视频: &nbsp;&nbsp;&nbsp;&nbsp;<a href={info.live_video ? info.live_video.replace('http://', 'https://') : info.live_video}>下载</a></p>
        <hr />
        <p><font color='red'><strong>主持跳槽信息：</strong></font></p>
        <p>跳槽模板/平台: &nbsp;{info.platform_name}</p>
        <p>跳槽UID: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{info.job_hopping_uid}</p>
        <p>跳槽YY号/ID: &nbsp;&nbsp;&nbsp;{info.job_hopping_id}</p>
        <p>跳槽身份: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{this.getContractType(info.job_hopping_type)}</p>
        <p>跳槽频道ID: &nbsp;&nbsp;&nbsp;&nbsp;{info.job_hopping_asid}</p>
        <p>直播截图: &nbsp;&nbsp;<PopImage value={info.job_hopping_image} />&nbsp;&nbsp;&nbsp;&nbsp;<a href={info.job_hopping_image}>下载</a></p>
        <p>直播视频: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href={info.job_hopping_video ? info.job_hopping_video.replace('http://', 'https://') : info.job_hopping_video}>下载</a></p>
        <p>跳槽开播端: &nbsp;&nbsp;&nbsp;{this.getLiveTerminal(info.job_hopping_live_terminal)}</p>
        <p>跳槽开播链接: <a href={info.job_hopping_link} target='_blank'>{info.job_hopping_link}</a></p>
        <p>备注: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{info.remarks}</p>
        <hr />
        <p><font color='red'><strong>处理记录：</strong></font></p>
        <p>提交时间: &nbsp;&nbsp;{moment.unix(info.submit_date).format('YYYY-MM-DD')}</p>
        <p>处理时间: &nbsp;&nbsp;{this.getAuditDate(info.audit_date)}</p>
      </Modal>
    )
  }
}

export default Index
