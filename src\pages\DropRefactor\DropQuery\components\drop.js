import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Table, Button, Form, Divider, Row, Col, DatePicker, Input, Select } from 'antd'
import { CSVLink } from 'react-csv'
import { getFieldsValueByChannelID } from '../../globalConfig'

const namespace = 'dropQuery'

const channelSel = [
  { label: '交友', value: 0, group: 'jy' },
  { label: '追玩', value: 11, group: 'jy' },
  { label: 'YY', value: 100, group: 'jy' },
  { label: '追看', value: 120, group: 'jy' },
  { label: '追玩语音房(已下线)', value: 133, group: 'vr' },
  { label: '语音房物资', value: 34013301, group: 'vr' },
  { label: '宝贝', value: 1000, group: 'jy' },
  { label: '语音房幸运小狗', value: 13302, group: 'vr' },
  { label: '物资大战', value: 2000003, group: 'jy' }
]

@connect(({ dropQuery }) => ({
  model: dropQuery
}))

class DropQueryDrop extends Component {
  prizeColumns = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: '配置ID', dataIndex: 'prizeId', align: 'center' },
    { title: '奖励道具ID', dataIndex: 'propsId', align: 'center' },
    { title: '奖励道具名称', dataIndex: 'propsName', align: 'center' },
    { title: '奖励道具数量', dataIndex: 'count', align: 'center' },
    { title: '价值', align: 'center', render: (_, record) => (record.value * record.count).toLocaleString() }
  ]

  componentDidMount () {
    this.formRef.setFieldsValue({ channel: this.props.groupType === 'jy' ? 0 : 133 })
  }

  // 点击 添加标签页-添加按钮
  onFinish = values => {
    const { dispatch } = this.props

    let params = {}
    params.begin = values.range ? values.range[0].unix() : 0
    params.end = values.range ? values.range[1].unix() : 0
    params.uid = values.uid ? parseInt(values.uid) : 0
    params.channel = values.channel

    dispatch({
      type: `${namespace}/getDrop`,
      payload: params
    })
  }

  expand = record => {
    let len = Math.ceil(record.prizeList.length / 3)
    record.prizeList.forEach((elem, index) => {
      elem.index = index + 1
    })

    let list = record.prizeList
    let a = list.slice(0, len)
    let b = list.slice(len, len * 2)
    let c = list.slice(len * 2, list.length)

    return (
      <Row gutter={4}>
        <Col span={8}>
          <Table size='small' rowKey={(_, index) => index} dataSource={a} pagination={false} columns={this.prizeColumns} />
        </Col>
        <Col span={8}>
          <Table size='small' rowKey={(_, index) => index} dataSource={b} pagination={false} columns={this.prizeColumns} />
        </Col>
        <Col span={8}>
          <Table size='small' rowKey={(_, index) => index} dataSource={c} pagination={false} columns={this.prizeColumns} />
        </Col>
      </Row>
    )
  }

  onExport = () => {
    const { model: { dropList } } = this.props

    let list = []
    dropList.forEach(i => {
      i.prizeList.forEach(prize => {
        list.push({
          '时间': i.time,
          'UID': i.uid,
          '频道': i.sid,
          '子频道': i.ssid,
          '主持UID': i.compereUid,
          '渠道': getFieldsValueByChannelID(i.channel, 'Name'),
          '类型': ['普通', '疯狂', '医疗包'][i.crazyMode],
          '配置ID': prize.prizeId,
          '奖励道具ID': prize.propsId,
          '奖励道具名称': prize.propsName,
          '奖励道具数量': prize.count,
          '奖励道具单价': prize.value,
          '奖励道具总价': prize.value * prize.count
        })
      })
    })

    return list
  }

  channelFilter = (before) => {
    const { groupType } = this.props
    return before.filter(item => {
      return item.group === groupType
    })
  }

  render () {
    const { model: { dropList } } = this.props
    const columns = [
      { title: '序号', align: 'center', render: (_, record, index) => index + 1 },
      { title: '序列号', dataIndex: 'id', align: 'center', width: '100px' },
      { title: '时间', dataIndex: 'time', align: 'center' },
      { title: 'UID', dataIndex: 'uid', align: 'center' },
      { title: '频道', dataIndex: 'sid', align: 'center' },
      { title: '子频道', dataIndex: 'ssid', align: 'center' },
      { title: '主持人UID', dataIndex: 'compereUid', align: 'center' },
      { title: '渠道', dataIndex: 'channel', align: 'center', render: v => getFieldsValueByChannelID(v, 'Name') },
      { title: '类型', dataIndex: 'crazyMode', align: 'center', render: v => ['普通', '疯狂', '医疗包'][v] },
      { title: '进度', dataIndex: 'progress', align: 'center', render: (v, record) => record.crazyMode === 2 ? v : '-' },
      { title: '奖励道具数量', align: 'center', render: (_, record) => record.prizeList.length },
      { title: '奖励道具ID', align: 'center', render: (_, record) => record.prizeList.length > 1 ? '-' : record.prizeList.map(i => i.propsId).join('') },
      { title: '奖励道具名称', align: 'center', render: (_, record) => record.prizeList.length > 1 ? '-' : record.prizeList.map(i => i.propsName).join('') },
      { title: '奖励道具数量', align: 'center', render: (_, record) => record.prizeList.length > 1 ? '-' : record.prizeList.map(i => i.count).join('') }
    ]

    return (
      <Card>
        <Form ref={form => { this.formRef = form }} onFinish={this.onFinish}>
          <Row gutter={4}>
            <Col>
              <Form.Item name='range'>
                <DatePicker.RangePicker showTime style={{ width: '330px' }} />
              </Form.Item>
            </Col>
            <Col>
              <Form.Item name='channel' rules={[{ required: true, message: '渠道不能为空' }]}>
                <Select style={{ width: '10em' }} options={this.channelFilter(channelSel)} />
              </Form.Item>
            </Col>
            <Col>
              <Form.Item name='uid'>
                <Input placeholder='UID' />
              </Form.Item>
            </Col>
            <Form.Item>
              <Button type='primary' htmlType='submit'>查询</Button>
            </Form.Item>
            <Form.Item>
              <CSVLink data={this.onExport()} filename='空投记录.csv' target='_blank'><Button type='primary' style={{ marginLeft: 5 }}>导出</Button></CSVLink>
            </Form.Item>
            <Col />
          </Row>
        </Form>
        <Divider />
        <Table size='small' expandable={{ rowExpandable: r => r.prizeList.length > 1, expandedRowRender: this.expand, expandRowByClick: true }} rowKey={(_, index) => index} pagination={{ pageSize: 100 }} columns={columns} dataSource={dropList} />
      </Card>
    )
  }
}

export default DropQueryDrop
