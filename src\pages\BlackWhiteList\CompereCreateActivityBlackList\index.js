import React, { Component } from 'react'
import { connect } from 'dva'
import { Tabs, Card, Input, Form, Row, Col, Button, Table, Popconfirm } from 'antd'
import { tableStyle, Inputlayout } from '@/utils/common'
import { DeleteOutlined } from '@ant-design/icons'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'

const namespace = 'compereCreateActivityBlackList'

@connect(({ compereCreateActivityBlackList }) => ({
  model: compereCreateActivityBlackList
}))

class CompereCreateActivityBlackList extends Component {
  constructor (props) {
    super(props)
    this.refreshVidioComperList()
  }

  // 标签页发生切换
  onTagChange = (record) => {
    if (record === '1') { // 切换到'添加标签页'
      this.initForm()
    }
    if (record === '2') { // 切换到'主持视频黑名单标签页'
      this.refreshVidioComperList()
    }
  }

  // 获取/刷新黑名单列表数据
  refreshVidioComperList = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getBlackListData`,
      payload: null
    })
  }

  // 清空输入表单
  initForm = () => {
    this.setState({ newUid: '' })
    if (this.formRef) {
      this.formRef.resetFields()
    }
  }

  // 点击 添加标签页-添加按钮
  onAddBtnClick = () => {
    const { newUid } = this.state
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/addBlackListByUid`,
      payload: { newUid: newUid, callback: this.initForm }
    })
  }
  // '添加'标签页html代码
  addBlackLIstHtml = () => {
    const { updating } = this.props.model
    return (
      <div>
        <Row>
          <Col span='24'>
            <Form {...Inputlayout}
              initialValues={{ uid: '' }}
              ref={form => { this.formRef = form }}
            >
              <Form.Item label='用户ID [uid]:' name='uid'>
                <Input id='addBlackListInput' placeholder='如50041789' allowClear
                  onChange={e => this.setState({ newUid: e.target.value })}
                  maxLength={20}
                />
              </Form.Item>
              <Form.Item>
                <Button type='primary' htmlType='submit' loading={updating} onClick={() => this.onAddBtnClick()}>
                  添加
                </Button>
              </Form.Item>
            </Form>
          </Col>
        </Row>
      </div>
    )
  }

  // 确认删除选中的主持开播黑名单
  onComfirmDel = (uid) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/delBlackListByUid`,
      payload: uid
    })
  }

  // 主持开播黑名单列表-删除操作html代码
  deleteBlackListHtml = (record) => {
    let tmpStr = `确定要将 [uid=${record.uid}] 从主持开播黑名单中删除吗？`
    return (
      <Popconfirm placement='bottom' title={tmpStr}
        okType='danger' okText='删除' cancelText='取消' onConfirm={() => this.onComfirmDel(record.uid)}>
        <a href='#'><DeleteOutlined style={{ color: '#ff3535', fontSize: '1.2em' }} /></a>
      </Popconfirm>
    )
  }

  // ‘主持开播黑名单展示列表标签页’html代码
  displayBlackListHtml = () => {
    const columns = [
      { title: '#', dataIndex: 'idx' },
      { title: '主持uid', dataIndex: 'uid' },
      { title: '操作', render: (record) => this.deleteBlackListHtml(record) }
    ]
    const { displayData } = this.props.model
    return (
      <Row >
        <Col span={24}>
          <Table columns={columns} dataSource={displayData} size='small' pagination={tableStyle} />
        </Col>
      </Row>
    )
  }

  render () {
    const { TabPane } = Tabs
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='2' onChange={(record) => this.onTagChange(record)} type='card' size='large'>
            <TabPane tab='添加' key='1'>
              {this.addBlackLIstHtml()}
            </TabPane>
            <TabPane tab='主持开播黑名单' key='2'>
              {this.displayBlackListHtml()}
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default CompereCreateActivityBlackList
