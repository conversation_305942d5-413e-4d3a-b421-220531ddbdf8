import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import { Card } from 'antd'
import CrossChLiveList from './crossChLiveList'
import DetailList from './detailList'

const namespace = 'crossLive'

@connect(({ crossLive }) => ({
  model: crossLive
}))

class CrossChannelLive extends Component {
  state = {}
  componentDidMount = () => {}

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 标签页发生切换
  onTagChange = (record) => {}

  render () {
    const { route } = this.props
    const { roomMgrList, starCompereList } = this.props.model

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey='crossChLiveList' type='card' onChange={(record) => this.onTagChange(record)}>
            <TabPane tab='多频道经营白名单' key='crossChLiveList'>
              <CrossChLiveList />
            </TabPane>
            <TabPane tab='签约房管明细' key='roomMgrList' >
              <DetailList key='roomMgr' type='mgr' name='签约房管明细' getDataFunc='listRoomMgr' dataSource={roomMgrList} />
            </TabPane>
            <TabPane tab='星光主持明细' key='starCompere'>
              <DetailList key='starCompere' type='compere' name='星光主持明细' getDataFunc='listStarCompere' dataSource={starCompereList} />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default CrossChannelLive
