import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import RFIReportComponent from './component/daily'
import DetailReportComponent from './component/detail'

const TabPane = Tabs.TabPane

class RewardFlowReportComponent extends Component { // 默认页面组件，不需要修改
  onTabChange = key => {
    this.setState({ key }) // render tab pane
  }

  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs onChange={this.onTabChange} type='card'>

          <TabPane tab='日报' key='1'>
            <RFIReportComponent {...this.state} />
          </TabPane>

          <TabPane tab='发放道具明细' key='2'>
            <DetailReportComponent {...this.state} />
          </TabPane>

        </Tabs>
      </PageHeaderWrapper>
    )
  }
}

export default RewardFlowReportComponent // 保证唯一
