import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Button, Divider, Form, Card, Modal, Input, Select, Popconfirm, InputNumber } from 'antd'
import { connect } from 'dva'
const { Option } = Select
const namespace = 'videodatingConfigList'
const FormItem = Form.Item
// const Option = Select.Option
var moment = require('moment')

// 业务类型
const statusAll = '' // 否
const statusNo = 0 // 否
const statusYES = 1 // 是
const statusTypeMap = { 0: '否', 1: '是' }

@connect(({ videodatingConfigList }) => ({
  model: videodatingConfigList
}))

class Index extends Component {
  // column structs.
  columns = [
    {
      title: '添加白名单时间', dataIndex: 'addTime', key: 'addTime', align: 'center', render: text => moment.unix(text).format('YYYY-MM-DD HH:mm')
    },
    { title: '短位频道', dataIndex: 'asid', key: 'asid', align: 'center' },
    { title: 'SID', dataIndex: 'sid', key: 'sid', align: 'center' },
    { title: 'SSID', dataIndex: 'ssid', key: 'ssid', align: 'center' },
    { title: '公会名称', dataIndex: 'guildName', key: 'guildName', align: 'center' },
    { title: '是否防跳槽', dataIndex: 'antiHopping', key: 'antiHopping', align: 'center', render: text => statusTypeMap[text] },
    { title: '是否允许音频连线', dataIndex: 'voiceLink', key: 'voiceLink', align: 'center', render: text => statusTypeMap[text] },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(record)}>更新</a><Divider type='vertical' />
          <Popconfirm title={'确认要删除?'} type='primary' onConfirm={this.handleDelOne(record)} okText='是的' cancelText='暂不'>
            <a href=''>移除白名单</a>
          </Popconfirm>
        </span>
      )
    }
  ]

  defaultPageValue = {
    defaultPageSize: 100,
    pageSizeOptions: ['100', '200', '500', '2000'],
    showSizeChanger: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  state = {
    visible: false,
    isUpdate: false,
    confirmVisible: false,
    deleteConfirmMsg: '',
    value: {}
  }

  // show modal
  showModal = (record) => () => {
    var title = '修改多人视频白名单'
    var opType = 1
    var isUpdate = false
    if (record == null) {
      title = '添加多人视频白名单'
      isUpdate = false
      record = { opType: 1, compereRecvGift: 0, antiHopping: 0, voiceLink: 0 }
    } else {
      opType = 3
      isUpdate = true
      // record.opType = 3
    }
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.opType = opType
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, title: title, isUpdate: isUpdate })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // add and update
  onFinish = values => {
    const { dispatch } = this.props
    if (values.opType === 1) {
      dispatch({
        type: `${namespace}/addItem`,
        payload: values
      })
    } else {
      dispatch({
        type: `${namespace}/updateItem`,
        payload: values
      })
    }

    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  handleDelOne = record => e => {
    const { dispatch } = this.props
    const data = { sid: record.sid, ssid: record.ssid }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data
    })
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = { type: this.state.type }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  searchBy = (value) => {
    const { dispatch } = this.props
    const data = { sid: this.state.sid, ssid: this.state.ssid, antiHopping: this.state.antiHopping, voiceLink: this.state.voiceLink }
    console.log(data)
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  handleCategoryChange = (value) => {
    const { dispatch } = this.props
    const data = { uid: this.state.searchUid, type: value.key }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
    console.log(value.key)

    this.setState({ type: value.key })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  // content
  render () {
    const { route, model: { list } } = this.props
    const { visible, title, isUpdate } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Button type='primary' onClick={this.showModal(null)}>新增白名单</Button>
        <Card>
          <Form>
            SID:
            <InputNumber min={1} onChange={(e) => this.setState({ sid: e })} />
            <Divider type='vertical' /> {/* 分割线 */}
            SSID:
            <InputNumber min={1} onChange={(e) => this.setState({ ssid: e })} />
            <Divider type='vertical' /> {/* 分割线 */}
            是否防跳槽:
            <Select defaultValue={statusAll} style={{ width: 120 }} onChange={(e) => this.setState({ antiHopping: e })}>
              <Option value={statusAll}>全部</Option>
              <Option value={statusYES}>是</Option>
              <Option value={statusNo}>否</Option>
            </Select>
            <Divider type='vertical' /> {/* 分割线 */}
            是否允许音频连线:
            <Select defaultValue={statusAll} style={{ width: 120 }} onChange={e => this.setState({ voiceLink: e })}>
              <Option value={statusAll}>全部</Option>
              <Option value={statusYES}>是</Option>
              <Option value={statusNo}>否</Option>
            </Select>
            <Divider type='vertical' /> {/* 分割线 */}
            <Button type='primary' onClick={this.searchBy}>查询</Button>
            <Table rowKey={(record, index) => index} dataSource={list} columns={this.columns} pagination={this.defaultPageValue} />
          </Form>
        </Card>

        <Modal visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit} forceRender>
          <Form {...formItemLayout} ref={form => { this.formRef = form }} onFinish={this.onFinish}>
            <FormItem label='opType' name='opType' rules={[{ required: true }]} hidden='true' >
              <Input readOnly={isUpdate} defaultValue='1' />
            </FormItem>
            <FormItem label='SID' name='sid' rules={[{ required: true }]}>
              <Input disabled={isUpdate} />
            </FormItem>
            <FormItem label='SSID' name='ssid' rules={[{ required: true }]}>
              <Input disabled={isUpdate} />
            </FormItem>
            <FormItem label='防跳槽' name='antiHopping' rules={[{ required: true }]}>
              <Select defaultValue={statusNo}>
                <Option value={statusYES}>是</Option>
                <Option value={statusNo}>否</Option>
              </Select>
            </FormItem>
            <FormItem label='允许音频连线' name='voiceLink' rules={[{ required: true }]}>
              <Select defaultValue={statusNo}>
                <Option value={statusYES}>是</Option>
                <Option value={statusNo}>否</Option>
              </Select>
            </FormItem>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default Index
