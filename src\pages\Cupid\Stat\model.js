import { genGetRequireTemplate } from '@/utils/common'

const getMonitorData = genGetRequireTemplate('/drop_stat/cupid/date_monitor', 'monitorData')
const getHourMonitorData = genGetRequireTemplate('/drop_stat/cupid/hour_monitor', 'hourStatList')

// const getUserInData = genGetRequireTemplate('/drop_stat/cupid/user_in_monitor', 'userInList')
const getUserInData = genGetRequireTemplate('/drop_stat/cupid/user_range_monitor', 'userInList')

const getTopUserList = genGetRequireTemplate('/drop_stat/cupid/top_user_monitor', 'topUserList')
const getNewUserList = genGetRequireTemplate('/drop_stat/cupid/new_user_monitor', 'newUserList')

export default {
  namespace: 'cupidStat',
  state: {
    monitorData: [],
    hourStatList: [],
    userInList: [],
    topUserList: [],
    newUserList: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },
  effects: {
    getMonitorData,
    getHourMonitorData,
    getUserInData,
    getTopUserList,
    getNewUserList
  }
}
