import React, { Component } from 'react'
import { Popover, Tooltip } from 'antd'
import './style.css'

class PopImage2 extends Component {
  render () {
    const { value,
      width = 320,
      height = 200,
      style = {},
      tooltip = '',
      trigger = 'hover', // 触发预览方式: hover | focus | click
      note = '' // 提示语,可传组件
    } = this.props

    const popContent = (
      <div>
        <img src={value ? value.replace('http://', 'https://') : value} style={{ maxHeight: '50vh', maxWidth: '50vw' }} />
      </div>
    )

    return (
      <Popover content={popContent} title={null} trigger={trigger} >
        <div style={{ position: 'relative', display: 'inline-block', ...style }}>
          <Tooltip title={tooltip} >
            <img src={value ? value.replace('http://', 'https://') : value} style={{ maxHeight: height, maxWidth: width }} />
          </Tooltip>
          {
            note
              ? <div className='overlay' >
                <div className='text'>{note}</div>
              </div>
              : ''
          }
        </div>
      </Popover>
    )
  }
}

export default PopImage2
