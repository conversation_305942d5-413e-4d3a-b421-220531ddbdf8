import * as api from './api'
import { message } from 'antd'

export default {
  namespace: 'guildMonitorReport',

  state: {
    list: []
  },

  reducers: {
    displayList (state, { payload: data }) {
      if (!Array.isArray(data)) {
        console.error('unexpect data type: data=', data)
        return
      }
      return {
        ...state,
        list: data
      }
    }
  },

  effects: {
    * getReportDataList ({ payload }, { call, put }) {
      let { data: { data, status } } = yield call(api.getReportDataList, payload)
      if (status !== 0) {
        message.error({ content: '获取数据失败' })
        return
      }

      data = Array.isArray(data) ? data : []
      for (let i = 0; i < data.length; i++) {
        data[i].idx = i + 1
      }

      yield put({
        type: 'displayList',
        payload: data
      })
    }
  }
}
