import React, { Component } from 'react'
import { Card, Table, DatePicker, Row, Col, Button, Select, Form, Tooltip } from 'antd'
import { connect } from 'dva'
import moment from 'moment' 
import { GenTip, totalOutFormater, outRateFormater, totalOffsetFormater, outRateFormaterV2, rataFormater } from './common'
import { genColumnTooltip } from '@/components/SimpleComponents'
import { onExportExcel } from '@/utils/common'

const namespace = 'dropReport' // model 的 namespace

const defaultDropType = 1000
const defaultStatType = 1000

// 统计渠道: 汇总，PC，Yo语音， 追玩，手Y
const statTypeOptions = [
  { label: '汇总', value: 1000 },
  { label: 'PC', value: 0 },
  { label: '手Y', value: 1 },
  { label: '追玩', value: 2 },
  { label: 'Yomi', value: 3 },
  { label: 'Web', value: 4 },
  { label: 'h5', value: 7 },
  { label: '好看', value: 8 },
  { label: '贴吧', value: 9 }
]

// 玩法
const dropTypeOptions = [
  { label: '汇总', value: 1000 },
  { label: '抢空投', value: 0 },
  { label: '抢物资', value: 1 },
  { label: '幸运小狗', value: 2 }
  // { label: '物资大战', value: 3 }
]

@connect(({ dropReport }) => ({ // model 的 namespace
  model: dropReport // model 的 namespace
}))

// 幸运小狗-空投日报
class DropReportZWSummaryComponentNew extends Component {
  componentDidMount () {
    this.queryData()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  queryData () {
    let begin = moment().add(-7, 'days')
    let end = moment()
    const params = { appID: 34, dropType: defaultDropType, statType: defaultStatType, begin: begin.format('YYYYMMDD'), end: end.format('YYYYMMDD') }
    this.formRef.setFieldsValue({ range: [begin, end], dropType: defaultDropType, statType: defaultStatType })
    this.callModel('getDailySummaryZw', { params })
  }

  // 查询数据
  onFinish = values => {
    let begin = values.range[0].format('YYYYMMDD')
    let end = values.range[1].format('YYYYMMDD')
    const dropType = parseInt(values.dropType)
    const statType = parseInt(values.statType)
    let params = { appID: 34, dropType: dropType, statType: statType, begin: begin, end: end }
    this.callModel('getDailySummaryZw', { params })
  }

  numberFormater = (v) => { 
    return v || 0
  }

  avgFormater = (a, b) => {
    if (b === 0) {
      return 0
    }
    return Number(a / b).toFixed(0)
  }
 
  columns = [
    { title: '日期', dataIndex: 'id' },
    { title: '普通模拟收入', dataIndex: 'normalIn', render: v => this.numberFormater(v) },
    { title: '普通支出', dataIndex: 'normalOut', ...genColumnTooltip('普通道具池支出+普通进度支出'), render: v => this.numberFormater(v) },
    { title: '普通发放占比', dataIndex: '_6', ...genColumnTooltip('普通支出/普通模拟收入'), render: (v, r) => rataFormater(r.normalOut, r.normalIn, 1) },
    { title: '普通人数', dataIndex: 'normalCount', render: v => this.numberFormater(v) },
    { title: '疯狂模拟收入', dataIndex: 'crazyIn', render: v => this.numberFormater(v) },
    { title: '疯狂支出', dataIndex: 'crazyOut', ...genColumnTooltip('疯狂道具池支出+疯狂进度支出'), render: v => this.numberFormater(v) },
    { title: '疯狂发放占比', dataIndex: '_7', ...genColumnTooltip('疯狂支出/疯狂模拟收入'), render: (v, r) => rataFormater(r.crazyOut, r.crazyIn, 1) },
    { title: '疯狂人数', dataIndex: 'crazyCount', render: v => this.numberFormater(v) },
    { title: '模拟收入', dataIndex: 'totalIn', render: v => this.numberFormater(v) },
    { title: '人均支出', dataIndex: '_5', align: 'center', render: (_, r) => { return this.avgFormater(r.totalIn, r.normalCount) }, ...genColumnTooltip('模拟收入 / 抽取人数') },
    { title: '总支出', dataIndex: '_1', ...genColumnTooltip('礼物支出+装扮碎片流水'), render: (v, r) => totalOutFormater(r) },
    { title: '礼物支出', dataIndex: 'totalOut', ...genColumnTooltip('只含抽出奖品，不含装扮碎片流水'), render: (v, r) => { return this.numberFormater(v) } }, 
    { title: '发放占比', dataIndex: '_2', render: (v, r) => outRateFormater(r), ...genColumnTooltip('总支出/模拟收入') },
    { title: '粗发放占比', dataIndex: '_3', render: (v, r) => outRateFormaterV2(r), ...genColumnTooltip('礼物支出/模拟收入') },
    { title: '装扮碎片流水', dataIndex: 'frags', render: v => this.numberFormater(v) },
    { title: '抽取道具人数', dataIndex: 'totalCount' },
    { title: '粗偏移', dataIndex: 'offset', ...genColumnTooltip('模拟收入-礼物支出') },
    { title: '总偏移', dataIndex: '_4', render: (v, r) => totalOffsetFormater(r), ...genColumnTooltip('模拟收入-礼物支出-装扮碎片流水') },
    { title: '龙宫支出', dataIndex: 'dragonOut', ...genColumnTooltip('龙宫支出'), render: v => this.numberFormater(v) },
    { title: '龙宫开启次数', dataIndex: 'dragonTimes', render: v => v || 0 },
    { title: '补给箱支出', dataIndex: 'compensateOut', ...genColumnTooltip('补给箱支出'), render: v => v || 0 } 
  ].map(item => {
    item.align = 'center'
    return item
  })

  onDropTypeChange = (v) => {
    this.setState({ dropType: v })
    this.formRef.submit()
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { dailysummaryzw } } = this.props

    return (
      <Card>
        <Form ref={form => { this.formRef = form }} onFinish={this.onFinish}>
          <Row gutter={6}>
            <Col>
              <Tooltip title='时间范围'>
                <Form.Item name='range'>
                  <DatePicker.RangePicker />
                </Form.Item>
              </Tooltip>
            </Col>
            <Col>
              <Tooltip title='玩法'>
                <Form.Item name='dropType' >
                  <Select defaultValue={0} options={dropTypeOptions} style={{ width: '11em' }} onChange={(v) => this.onDropTypeChange(v)} />
                </Form.Item>
              </Tooltip>
            </Col>
            <Col>
              <Tooltip title='渠道'>
                <Form.Item name='statType' >
                  <Select defaultValue={0} options={statTypeOptions} style={{ width: '10em' }} />
                </Form.Item>
              </Tooltip>
            </Col>
            <Col>
              <Button htmlType='submit' type='primary'>查询</Button>
            </Col>
            <Col>
              <Button onClick={() => { onExportExcel(this.columns, dailysummaryzw, '幸运小狗-空投日报-语音房日报.xlsx') }}>导出</Button>
            </Col>
          </Row>
        </Form>

        <GenTip />
        <Table
          scroll={{ x: 'max-content' }}
          rowKey={(record, index) => index}
          bordered dataSource={dailysummaryzw}
          columns={this.columns}
        /> {/* 显示的列表 */}
      </Card>
    )
  }
}

export default DropReportZWSummaryComponentNew
