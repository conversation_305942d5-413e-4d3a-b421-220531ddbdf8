import * as api from './api'
import { message } from 'antd'

export default {
  namespace: 'starCompereInfo',

  state: {
    list: []
  },

  reducers: {
    displayList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        list: payload
      }
    }
  },

  effects: {
    * listInfo ({ payload }, { call, put }) {
      // let data = []
      let { data: { data } } = yield call(api.listInfo, payload)
      data = Array.isArray(data) ? data : []
      // if (data.length === 0) {
      //   data.push({ uid: 50046756, yy: 909045756, nick: 'dw_liangjiefan', asid: 86, roomMgrUid: 2849402207, roomMgrYy: 2848037033, bindSid: 87814665, bindSsid: 2721503270, ssidName: '测试', optUid: 123, timestamp: 1666600400 })
      // }
      for (let i = 0; i < data.length; i++) {
        data[i].idx = i + 1
      }
      console.log(data)
      yield put({
        type: 'displayList',
        payload: data
      })
    },

    * addInfo ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.addInfo, payload)
      if (status !== 0) {
        message.error(msg)
      } else {
        message.success('已成功提交审核，待运营人员审核完毕后将入库')
      }
    },

    * deleteInfo ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.deleteInfo, payload)
      if (status !== 0) {
        message.error(msg)
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listInfo',
        payload: {}
      })
    },

    * checkInfo ({ payload }, { call, put }) {
      if (payload === null || payload === undefined) {
        return
      }
      const { play, func } = payload
      let { data: { data, status, msg } } = yield call(api.checkInfo, play)
      if (status !== 0) {
        message.warning(msg, data.descList)
        return
      }
      if (data.descList.length > 0) {
        for (let i = 0; i < data.descList.length; i++) {
          if (data.descList[i].indexOf('星光主持数已超出上限') > 0) {
            message.error('该房管UID的ssid所绑定星光主持数已超出上限，提交失败')
            return
          }
        }
      }
      func(data.conformUid, data.unConformUid)
    }
  }
}
