import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Table, Popconfirm, Button, Form, Input, Divider, Row, Col } from 'antd'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import PopImage from '@/components/PopImage'

const namespace = 'monthVoteWhite'

@connect(({ monthVoteWhite }) => ({
  model: monthVoteWhite
}))

class MonthVoteWhite extends Component {
  componentDidMount = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getWhiteListData`
    })
  }

  columns = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '签约频道', dataIndex: 'contractSid', align: 'center' },
    { title: '签约短位', dataIndex: 'contractAsid', align: 'center' },
    { title: '昵称', dataIndex: 'nick', align: 'center' },
    { title: '头像', dataIndex: 'avatarInfo', align: 'center', render: url => <PopImage value={url} /> },
    { title: '操作', align: 'center', render: (_, record) => <Popconfirm onConfirm={this.onComfirmDel(record.uid)} title='确认删除？'><Button type='danger'>删除</Button></Popconfirm> }
  ]

  // 点击 添加标签页-添加按钮
  onFinish = values => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/addWhiteListBySid`,
      payload: values
    })
  }

  // 确认删除选中的白名单
  onComfirmDel = uid => () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/delWhiteListByUid`,
      payload: { compereUid: uid }
    })
  }

  render () {
    const { route, model: { list } } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form onFinish={this.onFinish}>
            <Row gutter={4}>
              <Col>
                <Form.Item name='compereUid' label='UID' rules={[{ required: true }]}>
                  <Input />
                </Form.Item>
              </Col>
              <Form.Item>
                <Button type='primary' htmlType='submit'>新增</Button>
              </Form.Item>
              <Col />
            </Row>
          </Form>
          <Divider />
          <Table size='small' rowKey='index' pagination={false} columns={this.columns} dataSource={list} />
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default MonthVoteWhite
