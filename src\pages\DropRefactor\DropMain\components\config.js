import React, { Component } from 'react'
import { Card, Divider, Button, Modal, Form, Table, Input, InputNumber, Select, Tooltip, message } from 'antd'
import { connect } from 'dva'
import moment from 'moment'

const namespace = 'dropMain' // model 的 namespace
const FormItem = Form.Item

@connect(({ dropMain }) => ({ // model 的 namespace
  model: dropMain // model 的 namespace
}))
class DropPoolCfgComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false
    }
  }

  componentDidMount () {
    this.callModel('queryPoolList')
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 需要修改
  columns = [
    { title: '道具池ID', dataIndex: 'id', align: 'center' },
    { title: '道具池名', dataIndex: 'name', align: 'center' },
    { title: '版本号', dataIndex: 'version', align: 'center' },
    { title: '替换周星礼物', dataIndex: 'replace', align: 'center', render: text => ['否', '是'][text] },
    { title: '修改人', dataIndex: 'operator', align: 'center', render: text => text === 10101 ? '系统' : text },
    { title: '修改时间', dataIndex: 'timestamp', align: 'center', render: timestamp => moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss') },
    { title: '操作',
      align: 'center',
      render: (text, record) => (
        <div>
          <a style={{ marginRight: 10 }} onClick={this.showModal(true, record)}>编辑</a>
          {/* <Popconfirm onConfirm={this.handleRemove(record.id)} title='确认删除？'><a style={{ color: 'red' }}>删除</a></Popconfirm> */}
        </div>
      )
    }
  ]

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()

      if (!isUpdate) {
        const { poolListConfig } = this.props.model
        let maxID = 1000
        poolListConfig.forEach(item => {
          if (item.id >= maxID) {
            maxID = item.id + 1000
          }
        })

        v.id = maxID
      }

      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, isUpdate: isUpdate, title: isUpdate ? '更新道具池' : '新增道具池' })
  }

  // 隐藏弹窗，不需要修改
  hideModal = () => {
    this.setState({ visible: false })
  }

  onFinish = values => {
    const { dispatch } = this.props
    const { isUpdate } = this.state

    let uri = isUpdate ? `${namespace}/updatePoolDesc` : `${namespace}/addPool`
    if (values.content === undefined || values.content.length === 0) {
      values.content = '[]'
    }

    console.log(values)

    dispatch({
      type: uri,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 更新与删除
  handleSubmit = () => {
    this.formRef.submit()
  }

  handleRemove = id => () => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/removePool`,
      payload: { id: id }
    })
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  // 列表过滤
  listFilter = (before) => {
    const offlinePoolID = [11000]
    const { poolNameOptions } = this.props.model
    const after = before.filter(item => {
      if (offlinePoolID.indexOf(item.id) >= 0) {
        return false
      }
      return poolNameOptions.some(entry => { return entry.value === item.id })
    })
    return after
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { poolListConfig } } = this.props
    const { visible, title, isUpdate } = this.state
    const formItemLayout = { // 不需要修改
      labelCol: {
        xs: { span: 6 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 18 },
        sm: { span: 18 }
      }
    }

    return (
      <Card>
        <Tooltip title='不可随意新增'>
          {/* <Button type='danger' onClick={this.showModal(false, this.defaultValue)}>新增道具池</Button> 新增道具池需调整后端代码中的配置 */}
          <Button type='danger' onClick={() => message.warn('功能暂不可用,请联系开发人员')}>新增道具池</Button>
        </Tooltip>
        <Divider />
        <Table rowKey={(record, index) => index} dataSource={this.listFilter(poolListConfig)} columns={this.columns} pagination={false} /> {/* 显示的列表 */}

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} ref={form => { this.formRef = form }} {...formItemLayout}>
            <FormItem label='抽取道具ID' name='id' rules={[{ required: true, message: '道具池ID必填且不能重复' }]}>
              <InputNumber min={1000} style={{ width: '100%' }} disabled={isUpdate} />
            </FormItem>
            <FormItem label='道具池名' placeholder='PC抽取道具 ...' name='name' rules={[{ required: true, message: '配置名必填' }]}>
              <Input disabled={isUpdate} />
            </FormItem>
            <FormItem label='替换周星礼物' name='replace' rules={[{ required: true }]}>
              <Select options={[{ label: '是', value: 1 }, { label: '否', value: 0 }]} />
            </FormItem>
            <FormItem name='version' hidden>
              <Input />
            </FormItem>
            <FormItem name='operator' hidden>
              <Input />
            </FormItem>
            <FormItem name='timestamp' hidden>
              <Input />
            </FormItem>
          </Form>
        </Modal>
      </Card>

    )
  }
}

export default DropPoolCfgComponent
