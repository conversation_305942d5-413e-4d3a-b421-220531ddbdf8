/* eslint-disable eqeqeq */
import { getLists, whiteListAddOrDelete } from './api'
import { checkSid, checkSsid, checkPlay } from '@/utils/common'
import { Modal, message } from 'antd'

export default {
  namespace: 'h5GrayscaleWhiteList',
  state: {
    updating: false,
    displayData: [],
    newSid: 0,
    newSsid: 0,
    newPlay: 0,
    currentPage: 1,
    currentSize: 0,
    currentPage2: 1,
    currentSize2: 0
  },

  reducers: {
    // 更新单个state成员的值
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },
    // 更新currentSize和currentPage
    updatePageAndSize (state, { payload: data }) {
      const { page, size } = data
      if (page === undefined || size == undefined) {
        console.error('unexpect params page or size:', page, size)
        return
      }
      return {
        ...state,
        currentSize: size,
        currentPage: page
      }
    }
  },

  effects: {
    // 请求并刷新列表数据
    * getWhiteListData ({ payload }, { select, call, put }) {
      const { isWhiteList } = payload
      if (isWhiteList != true && isWhiteList != false) {
        console.error('unexpect params: isWhiteList=', isWhiteList)
        return
      }
      let resp = yield call(getLists, isWhiteList)
      const { data } = resp
      if (data == undefined) {
        Modal.error({ content: '获取数据失败，请检查控制台' })
        console.error('getWhiteListData() get data error: response=', resp)
        return
      }
      const { status, msg, payLoad } = data
      if (status != 0) {
        Modal.error({ content: '获取数据有误，请检查控制台' })
        console.error('getWhiteListData() status=' + status + ' msg=' + msg)
        return
      }
      if (payLoad === null) {
        message.warning('数据为空')
        yield put({
          type: 'updateState',
          payload: { name: (isWhiteList ? 'displayData' : 'displayData2'), newValue: [] }
        })
        return
      }
      if (!Array.isArray(payLoad)) {
        Modal.error({ content: '数据异常，请检查控制台' })
        console.error('getWhiteListData() error: payload is not a array, payload=', payLoad)
        return
      }
      let sidList = payLoad
      sidList.sort(function (a, b) { return (a.asid > b.asid ? 1 : -1) })
      yield put({
        type: 'updateState',
        payload: { name: (isWhiteList ? 'displayData' : 'displayData2'), newValue: sidList }
      })
    },
    // 添加灰度白名单
    * addWhiteList ({ payload }, { call, put }) {
      const { newSid, newSsid, newPlay, isWhiteList, callback } = payload // callback 是用于清空表单的回调函数
      if (!checkSid(newSid) || !checkSsid(newSsid) || !checkPlay(newPlay)) {
        return
      }
      if (!isWhiteList && newSsid === '0') { // 黑名单不设置ssid为0
        message.warn('ssid不能为0')
        return
      }
      if (isWhiteList !== true && isWhiteList !== false) {
        message.error('unexpect params: isWhiteList=', isWhiteList)
        return
      }
      if (newPlay != 0 && newPlay != 20) {
        console.error('unexpect play type: play=', newPlay)
        Modal.error({ content: '发生错误，请检查控制台' })
        return
      }
      yield put({
        type: 'updateState',
        payload: { name: 'updating', newValue: true }
      })
      let resp = yield call(whiteListAddOrDelete, 'ADD', isWhiteList, newSid, newSsid, newPlay)
      const { data } = resp
      if (data == undefined) {
        Modal.error({ content: '发生错误，请检查控制台' })
        console.error('[添加H5模板灰度白/黑名单错误] response=', resp)
        yield put({
          type: 'updateState',
          payload: { name: 'updating', newValue: false }
        })
        return
      }
      const { status, msg } = data
      if (status === 0) {
        message.success('添加成功')
        callback(isWhiteList)
      } else {
        Modal.error({ content: '操作失败： status=' + status + ' msg=' + msg })
        console.error('[添加H5模板灰度白/黑名单失败] response=', resp)
      }
      yield put({
        type: 'updateState',
        payload: { name: 'updating', newValue: false }
      })
    },
    // 删除灰度白名单
    * delWhiteList ({ payload }, { call, put }) {
      const { sid, ssid, play, isWhiteList } = payload
      if (!checkSid(sid) || !checkSsid(ssid) || !checkPlay(play)) {
        return
      }
      if (isWhiteList !== true && isWhiteList !== false) {
        message.error('unexpect params: isWhiteList=', isWhiteList)
        return
      }
      let resp = yield call(whiteListAddOrDelete, 'DELETE', isWhiteList, sid, ssid, play)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除H5模板灰度白名单错误]: response=', resp)
        yield put({
          type: 'updateState',
          payload: { name: 'updating', newValue: false }
        })
        return
      }
      const { status, msg } = data
      if (status === 0) {
        message.success('删除成功')
        yield put({ // 更新列表
          type: 'getWhiteListData',
          payload: { isWhiteList: isWhiteList }
        })
      } else {
        Modal.error({ content: '删除失败：status=' + status + ' msg=' + msg })
        console.error('delWhiteList() resp=', resp)
      }
    }
  }
}
