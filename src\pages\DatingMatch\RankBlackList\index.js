import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Table, Form, Card, Modal, Popconfirm, Tabs, Button, message, Divider, Input } from 'antd'
import { connect } from 'dva'
var moment = require('moment')
const namespace = 'RankBlackList'
const FormItem = Form.Item
const TabPane = Tabs.TabPane

const sidBlackList = '1'
const ssidBlackList = '2'
const uidBlackList = '3'
const { TextArea } = Input

@connect(({ RankBlackList }) => ({
  model: RankBlackList
}))

class Index extends Component {
  // column structs.
  sidBlackListColumns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: 'sid', dataIndex: 'sid', key: 'sid', align: 'center' },
    { title: '短位ID', dataIndex: 'asid', key: 'asid', align: 'center' },
    { title: '添加原因', dataIndex: 'reason', key: 'reason', align: 'center' },
    { title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (text, record) => {
        switch (text) {
          case 0: return '审批中'
          case 1: return '审批拒绝'
          case 2: return '审批通过'
        }
      }
    },
    { title: '添加时间', dataIndex: 'timestamp', key: 'timestamp', align: 'center', render: text => this.dateString(text) },
    { title: '操作人', dataIndex: 'operator', key: 'operator', align: 'center' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <Popconfirm title='确认删除?' onConfirm={this.handleDel(record, sidBlackList)}><a>删除</a></Popconfirm>
        </span>)
    }
  ]

  ssidBlackListColumns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: 'sid', dataIndex: 'sid', key: 'sid', align: 'center' },
    { title: '短位ID', dataIndex: 'asid', key: 'asid', align: 'center' },
    { title: 'ssid', dataIndex: 'ssid', key: 'ssid', align: 'center' },
    { title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (text, record) => {
        switch (text) {
          case 0: return '审批中'
          case 1: return '审批拒绝'
          case 2: return '审批通过'
        }
      }
    },
    { title: '添加时间', dataIndex: 'timestamp', key: 'timestamp', align: 'center', render: text => this.dateString(text) },
    { title: '操作人', dataIndex: 'operator', key: 'operator', align: 'center' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <Popconfirm title='确认删除?' onConfirm={this.handleDel(record, ssidBlackList)}><a>删除</a></Popconfirm>
        </span>)
    }
  ]

  uidBlackListColumns = [
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center' },
    { title: 'uid', dataIndex: 'uid', key: 'uid', align: 'center' },
    { title: 'YY号', dataIndex: 'yy', key: 'yy', align: 'center' },
    { title: '昵称', dataIndex: 'nick', key: 'nick', align: 'center' },
    { title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (text, record) => {
        switch (text) {
          case 0: return '审批中'
          case 1: return '审批拒绝'
          case 2: return '审批通过'
        }
      }
    },
    { title: '添加时间', dataIndex: 'timestamp', key: 'timestamp', align: 'center', render: text => this.dateString(text) },
    { title: '操作人', dataIndex: 'operator', key: 'operator', align: 'center' },
    { title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (
        <span>
          <Popconfirm title='确认删除?' onConfirm={this.handleDel(record, uidBlackList)}><a>删除</a></Popconfirm>
        </span>)
    }
  ]

  defaultPageValue = {
    defaultPageSize: 50,
    pageSizeOptions: ['50', '100', '500', '2000'],
    showSizeChanger: true,
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = {
    visible: false,
    confirmVisible: false,
    deleteConfirmMsg: '',
    selectedRowKeys: [],
    value: {},
    type: sidBlackList
  }

  dateString (timestamp) {
    if (timestamp === 0 || timestamp === undefined) {
      return '-'
    }
    return moment.unix(timestamp).format('YYYY-MM-DD')
  }

  // show modal
  showModal = (record, type) => () => {
    if (record == null) record = { sid: '' }
    this.setState({ visible: true, type: type, title: '添加' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  handleSubmit = e => {
    this.formRef.submit()
  }

  // add and update
  onFinish = values => {
    const { dispatch } = this.props
    values.type = this.state.type
    console.log(values)
    let sidList = []
    let ssidList = []
    let uidList = []
    if (values.sid) {
      sidList = values.sid.split('\n')
      if (sidList.length > 10) {
        message.error('最多同时添加10个sid')
        return
      }
      console.log(sidList)
    }
    if (values.ssid) {
      ssidList = values.ssid.split('\n')
      if (ssidList.length > 10) {
        message.error('最多同时添加10个ssid')
        return
      }
      console.log(ssidList)
    }
    if (values.uid) {
      uidList = values.uid.split('\n')
      if (uidList.length > 10) {
        message.error('最多同时添加10个uid')
        return
      }
      console.log(uidList)
    }
    let data = {
      type: parseInt(this.state.type),
      sidList: sidList,
      ssidList: ssidList,
      uidList: uidList,
      reason: values.reason
    }
    dispatch({
      type: `${namespace}/addItem`,
      payload: data,
      getListParam: { type: this.state.type }
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  handleDel = (record, type) => e => {
    const { dispatch } = this.props
    const data = { id: record.id }
    let params = { type: type }
    switch (type) {
      case sidBlackList:
        params.sid = this.state.sid
        params.asid = this.state.asid
        break
      case ssidBlackList:
        params.ssid = this.state.ssid
        break
      case uidBlackList:
        params.uid = this.state.uid
        params.yy = this.state.yy
        break
    }
    dispatch({
      type: `${namespace}/removeItem`,
      payload: data,
      getListParam: params
    })
  }

  // 从服务端获取数据，不需要修改
  componentDidMount () {
    const { dispatch } = this.props
    var data = { type: sidBlackList }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }
  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onChangeTab = type => {
    const { dispatch } = this.props
    let data = { type: type }
    switch (data.type) {
      case sidBlackList:
        data.sid = this.state.sid
        data.asid = this.state.asid
        break
      case ssidBlackList:
        data.ssid = this.state.ssid
        break
      case uidBlackList:
        data.uid = this.state.uid
        data.yy = this.state.yy
        break
    }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
    this.setState({ type: type })
  }

  handleSearch = () => {
    const { dispatch } = this.props
    let data = { type: this.state.type }
    switch (data.type) {
      case sidBlackList:
        data.sid = this.state.sid
        data.asid = this.state.asid
        break
      case ssidBlackList:
        data.ssid = this.state.ssid
        break
      case uidBlackList:
        data.uid = this.state.uid
        data.yy = this.state.yy
        break
    }
    dispatch({
      type: `${namespace}/getList`,
      payload: data
    })
  }

  // content
  render () {
    const { route, model: { list } } = this.props
    const { visible, title, type } = this.state

    const formItemLayout = {
      labelCol: {
        xs: { span: 5 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs defaultActiveKey={sidBlackList} onChange={this.onChangeTab}>
            <TabPane tab='sid' key={sidBlackList}>
              <Form>
                <p>SID配置针对公会榜单，SSID配置针对房管/多人厅榜单，UID针对主持榜单</p>
                <Input placeholder='搜索 sid' onChange={e => this.setState({ sid: e.target.value })} style={{ width: 100 }} /> {/* 搜索按钮 */}
                <Input placeholder='搜索短位ID' onChange={e => this.setState({ asid: e.target.value })} style={{ width: 100 }} /> {/* 搜索按钮 */}
                <Divider type='vertical' /> {/* 分割线 */}
                <Button type='primary' onClick={this.handleSearch}>搜索</Button>
                <Divider type='vertical' /> {/* 分割线 */}
                <Button type='primary' onClick={this.showModal(null, sidBlackList)}>添加</Button>
                <Table rowKey={(record, index) => index} dataSource={list} columns={this.sidBlackListColumns} pagination={this.defaultPageValue} />
              </Form>
            </TabPane>
            <TabPane tab='ssid' key={ssidBlackList}>
              <Form>
                <p>SID配置针对公会榜单，SSID配置针对房管/多人厅榜单，UID针对主持榜单</p>
                <Input placeholder='搜索 ssid' onChange={e => this.setState({ ssid: e.target.value })} style={{ width: 100 }} /> {/* 搜索按钮 */}
                <Divider type='vertical' /> {/* 分割线 */}
                <Button type='primary' onClick={this.handleSearch}>搜索</Button>
                <Divider type='vertical' /> {/* 分割线 */}
                <Button type='primary' onClick={this.showModal(null, ssidBlackList)}>添加</Button> 
                <Table rowKey={(record, index) => index} dataSource={list} columns={this.ssidBlackListColumns} pagination={this.defaultPageValue} />
              </Form>
            </TabPane>
            <TabPane tab='uid' key={uidBlackList}>
              <Form>
                <p>SID配置针对公会榜单，SSID配置针对房管/多人厅榜单，UID针对主持榜单</p>
                <Input placeholder='搜索 uid' onChange={e => this.setState({ uid: e.target.value })} style={{ width: 100 }} /> {/* 搜索按钮 */}
                <Input placeholder='搜索 yy' onChange={e => this.setState({ yy: e.target.value })} style={{ width: 100 }} /> {/* 搜索按钮 */}
                <Divider type='vertical' /> {/* 分割线 */}
                <Button type='primary' onClick={this.handleSearch}>搜索</Button>
                <Divider type='vertical' /> {/* 分割线 */}
                <Button type='primary' onClick={this.showModal(null, uidBlackList)}>添加</Button>
                <Table rowKey={(record, index) => index} dataSource={list} columns={this.uidBlackListColumns} pagination={this.defaultPageValue} />
              </Form>
            </TabPane>
          </Tabs>
        </Card>

        <Modal visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit} forceRender>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            {
              type === sidBlackList
                ? <div>
                  <FormItem label='sid' name='sid' rules={[{ required: true }]}>
                    <TextArea style={{ width: '100%' }} rows={6} />
                  </FormItem>
                </div>
                : type === ssidBlackList
                  ? <div>
                    <FormItem label='sid+ssid' name='ssid' rules={[{ required: true }]}>
                      <TextArea style={{ width: '100%' }} rows={6} />
                    </FormItem>
                  </div>
                  : <div>
                    <FormItem label='uid' name='uid' rules={[{ required: true }]}>
                      <TextArea style={{ width: '100%' }} rows={6} />
                    </FormItem>
                  </div>
            }
            <FormItem label='添加原因' name='reason'>
              <Input style={{ width: '100%' }} />
            </FormItem>
            <p><font style={{ marginLeft: 60 }}>注：最多可同时添加10个;</font></p>
            <p><font style={{ marginLeft: 60 }}>若黑名单选择UID，则需为交友签约主持</font></p>
            <p><font style={{ marginLeft: 60 }}>若黑名单选择SSID，<font color='red'>需同时输入SID和SSID，且用中文逗号隔开</font></font></p>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default Index
