// 抢空投公用逻辑

// 奖励道具稀有度枚举
export const propTypeOptions = [
  { label: '其他', value: -1 },
  { label: '普通', value: 0 },
  { label: '特别', value: 1 },
  { label: '稀有', value: 2 },
  { label: '史诗', value: 3 },
  { label: '传说', value: 4 }
]

// 渠道列表
// export const channelOptions = [
//   { label: '交友-PC', value: 0 },
//   { label: '交友-IOS', value: 1 },
//   { label: '交友-Andriod', value: 2 },
//   { label: '追玩-IOS', value: 11 },
//   { label: '追玩-Andriod', value: 12 },
//   { label: '追玩-IOS', value: 77 },
//   { label: '追玩-Andriod', value: 78 },
//   { label: 'YY-IOS', value: 100 },
//   { label: 'YY-Andriod', value: 101 },
//   { label: 'YY-IOS', value: 29 },
//   { label: 'YY-Andriod', value: 30 },
//   { label: '追看-IOS', value: 120 },
//   { label: '追看-Andriod', value: 121 },
//   { label: '追玩语音房-IOS', value: 133 },
//   { label: '追玩语音房-And', value: 134 },
//   { label: 'yomi交友 ios', value: 157 },
//   { label: 'yomi交友 android', value: 158 },
//   { label: 'yomi-ios-交友抢物资', value: 17101 },
//   { label: 'yomi-and-交友抢物资', value: 15801 },
//   { label: 'yomi语音房 ios', value: 171 },
//   { label: 'yomi语音房 android', value: 172 },
//   { label: '宝贝', value: 1000 },
//   { label: '追玩交友ios抢物资', value: 7701 },
//   { label: '追玩交友and抢物资', value: 7801 },
//   { label: '追玩语音房ios抢物资', value: 13301 },
//   { label: '追玩语音房and抢物资', value: 13401 },
//   { label: '追玩-ios-语音房幸运小狗', value: 13302 },
//   { label: '追玩-and-语音房幸运小狗', value: 13402 },
//   { label: 'yomi-ios-语音房幸运小狗', value: 17102 },
//   { label: 'yomi-and-语音房幸运小狗', value: 17202 },
//   { label: '技能卡-pc-幸运小狗', value: 34000002 },
//   { label: '技能卡-ios-幸运小狗', value: 34017102 },
//   { label: '技能卡-and-幸运小狗', value: 34017202 },
//   { label: '交友PC-物资大战', value: 2000003 },
//   { label: '手Y-ios-物资大战', value: 2002903 },
//   { label: '手Y-安卓-物资大战', value: 2003003 },
//   { label: '追玩交友-ios-物资大战', value: 2007703 },
//   { label: '追玩交友-安卓-物资大战', value: 2007803 },
//   { label: 'yomi交友-ios-物资大战', value: 2015703 },
//   { label: 'yomi交友-安卓-物资大战', value: 2015803 }
// ]

// 物质福利流水可配置渠道
export const rewardFlowSourceOptions = [
  { label: '追玩', value: 'zw' },
  { label: 'PC', value: 'pc' },
  { label: '手Y', value: 'sy' },
  { label: 'Yo语音', value: 'ym' },
  { label: '追看 (已下线)', value: 'zk', disabled: true }
]

// 解析福利流水配置渠道的名称
export const parseRewardFlowSourceOptions = (value) => {
  if (value === 'all') {
    return '汇总'
  }
  let item = rewardFlowSourceOptions.find(v => v.value === value)
  return item ? item.label : `${value} (未知)`
}

// 根据url判断当前的空投页面是否语音房专属
export const isVoiceRoomPath = (path = '') => {
  return path.indexOf('/DropRefactor/') > -1
}
