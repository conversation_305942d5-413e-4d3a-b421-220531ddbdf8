import React, { Component } from 'react'
import { <PERSON>, Divider, Button, Modal, Form, Table, Popconfirm, InputNumber, Input, Checkbox } from 'antd'
import { connect } from 'dva'
import { rewardFlowSourceOptions, parseRewardFlowSourceOptions } from '../../dropCommon'

const namespace = 'RFIConfig' // model 的 namespace
const FormItem = Form.Item
// const RadioGroup = Radio.Group

const stateTransfer = { 0: '提交人', 1: '审批人' }

@connect(({ RFIConfig }) => ({ // model 的 namespace
  model: RFIConfig // model 的 namespace
}))
class RFIConfigComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false
    }
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getList`
    })
  }

  // 需要修改
  columns = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: '主持UID', dataIndex: 'uid', align: 'center' },
    { title: '主持YY昵称', dataIndex: 'nick', align: 'center' },
    { title: '签约公会', dataIndex: 'sid', align: 'center' },
    { title: '比例', dataIndex: 'rate', align: 'center', render: text => `${text}%` },
    { title: '渠道', dataIndex: 'source', align: 'center', render: text => parseRewardFlowSourceOptions(text) },
    { title: '审批流', dataIndex: 'progress', align: 'center', render: text => Array.isArray(text) ? text.map(i => <div key={Math.random().toString(32)}>{stateTransfer[i.state] + '： ' + i.passport}</div>) : '' },
    { title: '状态', dataIndex: 'state', align: 'center', render: text => ['待审批', '通过', '不通过'][text] },
    { title: '操作',
      align: 'center',
      render: (text, record) => (
        <div>
          {
            text.state === 0 && this.props.model.author === 2
              ? <div>
                <Popconfirm onConfirm={this.handleApproval(record.uid, record.source, 1)} title='确认同意？'><a style={{ marginRight: 20 }}>同意</a></Popconfirm>
                <Popconfirm onConfirm={this.handleApproval(record.uid, record.source, 2)} title='确认拒绝？'><a style={{ marginRight: 20 }}>拒绝</a></Popconfirm>
              </div>
              : <Popconfirm onConfirm={this.handleRemove(record.id)} title='确认删除？'><a>删除</a></Popconfirm>
          }
        </div>
      )
    }
  ]

  defaultValue = {}

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : '添加' })
  }

  // 隐藏弹窗，不需要修改
  hideModal = () => {
    this.setState({ visible: false })
  }

  onFinish = values => {
    const { dispatch } = this.props

    values.uidList = values.uidList.split(/,|，/, -1).map(i => parseInt(i))
    // console.log(values.uids)

    // console.log(values, list)
    dispatch({
      type: `${namespace}/upsetList`,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 更新与删除
  handleSubmit = () => {
    this.formRef.submit()
  }

  handleRemove = id => () => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/removeList`,
      payload: { id }
    })
  }

  handleApproval = (uid, source, approval) => () => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/approvalList`,
      payload: { uid, source, approval }
    })
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { list } } = this.props
    const { visible, title } = this.state
    const formItemLayout = { // 不需要修改
      labelCol: {
        xs: { span: 4 },
        sm: { span: 4 }
      },
      wrapperCol: {
        xs: { span: 20 },
        sm: { span: 20 }
      }
    }

    return (
      <Card>
        <Button onClick={this.showModal(false, this.defaultValue)}>添加</Button>
        <Divider />
        <Table rowKey={record => record.index} dataSource={list} columns={this.columns} size='small' pagination={false} /> {/* 显示的列表 */}

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} ref={form => { this.formRef = form }} {...formItemLayout}>
            <FormItem label='主持UID' name='uidList' rules={[{ required: true }]}>
              <Input placeholder='输入多个账号可用,分割' />
            </FormItem>
            {/* <FormItem label='奖励比例' name='rate' rules={[{ required: true }, { type: 'enum', enum: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], message: '请输入1-10的整数' }]}> */}
            <FormItem label='比例' name='rate' rules={[{ required: true }, { type: 'enum', enum: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10], message: '请输入1-10的整数' }]}>
              <InputNumber style={{ width: '100%' }} placeholder='请输入1-10的整数' />
            </FormItem>
            <FormItem {...formItemLayout} label='渠道选择' name='source' rules={[{ required: true, message: '渠道选择 不能为空' }]}>
              <Checkbox.Group options={rewardFlowSourceOptions} />
            </FormItem>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default RFIConfigComponent
