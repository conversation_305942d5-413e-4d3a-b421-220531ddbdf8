import { But<PERSON>, Card, DatePicker, Divider, Form, Table } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import exportExcel from '@/utils/exportExcel'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
// const gameMap = { 0: 'ALL', 6: '超能运动会' }
const chanMap = { all: 'ALL', pc: 'PC端', dreamer: 'YO交友', yomi: 'YO语音' }
const newMap = { all: 'ALL', new: '新用户', old: '老用户' }
const profitMap = { all: 'ALL', profit: '累计成功', loss: '累计失败', new: '新用户', old: '老用户' }

@connect(({ arkJy }) => ({ // model 的 namespace
  model: arkJy // model 的 namespace
}))

class ArkDailyRetainUseInfoComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      list: [],
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
  }

    // 日期 渠道类型  魔豆模拟流水 付费用户数 剩余发放监控汇总  成功总流水 成功总人数 累计成功人数  累计失败人数
    columns = [
      { title: '日期', dataIndex: 'date', align: 'center' },
      { title: '用户类型', dataIndex: 'newType', align: 'center', render: text => { return newMap[text] }, filters: [{ text: 'ALL', value: 'all' }, { text: '新用户', value: 'new' }, { text: '老用户', value: 'old' }], defaultFilteredValue: ['all'], onFilter: (value, record) => record.newType.includes(value) },
      { title: '用户类型', dataIndex: 'profitType', align: 'center', render: text => { return profitMap[text] }, filters: [{ text: 'ALL', value: 'all' }, { text: '累计成功用户', value: 'profit' }, { text: '累计失败用户', value: 'loss' }], defaultFilteredValue: ['all'], onFilter: (value, record) => record.profitType.includes(value) },
      { title: '渠道类型', dataIndex: 'platform', align: 'center', render: text => { return chanMap[text] }, filters: [{ text: 'ALL', value: 'all' }, { text: 'PC端', value: 'pc' }, { text: 'YO交友', value: 'dreamer' }, { text: 'YO语音', value: 'yomi' }], defaultFilteredValue: ['all'], onFilter: (value, record) => record.platform.includes(value) },
      { title: '用户数', dataIndex: 'count', align: 'center' },
      { title: '参与流水(元)', dataIndex: 'amethyst', align: 'center' },
      { title: '次日留存', dataIndex: 'ration1', align: 'center' },
      { title: '7日留存', dataIndex: 'ration7', align: 'center' },
      { title: '30日留存', dataIndex: 'ration30', align: 'center' },
      { title: '次日参与流水留存', dataIndex: 'amountRation1', align: 'center' },
      { title: '7日参与流水留存', dataIndex: 'amountRation7', align: 'center' },
      { title: '30日参与流水留存', dataIndex: 'amountRation30', align: 'center' }
    ]

    loadData = () => {
      const { dispatch } = this.props
      const { dateRange } = this.state
      const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
      const { modelName } = this.props
      dispatch({
        type: `${modelName}/getRetainUserInfo`,
        payload: data
      })
    }

    onClick = () => {
      this.loadData()
    }

    onChange = (date, format) => {
      console.log('date', date)
      this.setState({ dateRange: date })
    }

    onStartChange = (value) => {
      this.onChange('startValue', value)
    }

    onEndChange = (value) => {
      this.onChange('endValue', value)
    }

    handleSelectChange = (value) => {
      console.log(value)
    }

    onExport = () => {
      let headers = []
      let columns = this.columns
      columns.forEach(function (item) {
        headers.push({ key: item.dataIndex, header: item.title })
      })

      const { model: { retainUserInfoList } } = this.props
      var exportData = retainUserInfoList.map(item => {
        let v = $.extend(true, {}, item)
        // v.arenaId = gameMap[v.arenaId]
        v.newType = newMap[v.newType]
        v.profitType = profitMap[v.profitType]
        v.platform = chanMap[v.platform]
        return v
      })

      exportExcel(headers, exportData)
    }

    /* *******************************页面布局***************************************************************/
    render () {
      const { model: { retainUserInfoList } } = this.props
      const { dateRange } = this.state
      return (
        <Card>
          <Form>
            <span style={{ marginLeft: 10 }}>时间范围:</span>
            <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
            <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
            <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
            <Divider />
            <Table dataSource={retainUserInfoList} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
          </Form>
        </Card>
      )
    }
}

export default ArkDailyRetainUseInfoComponent
