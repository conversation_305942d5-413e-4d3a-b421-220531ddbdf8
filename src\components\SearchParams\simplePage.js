import React, { Component } from 'react'
import { Row, Col, Skeleton, Space, Table, message, Tooltip } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import SimplePageParams from './index'
import { simpleRequire3 } from '@/utils/common'

/* 参数概述
  uri: string  // 请求路径
  hideExport: boolean  // 是否隐藏导出按钮
  tableProps: {} // 附加到 <Table/> 的属性
*/
class SimplePage extends Component {
    state={
      uri: '',
      config: [],
      isLoading: false,
      fromValue: {},
      list: []
    }

    componentDidMount = () => {
      const { uri } = this.props
      this.setState({ uri })
      this.getConfig(uri)
    }

    // 获取搜索栏配置
    getConfig = (path) => {
      let uri = `${path}/config`
      getMethodRequire(uri, null,
        (ret) => {
          const { status, msg, list } = ret
          if (status !== 0) {
            message.error('生成搜索栏错误:' + msg)
            return
          }
          this.setState({ config: list })
        }
      )
    }

    // 请求列表数据
    onQueryTable = (params) => {
      const { uri } = this.props
      let path = `${uri}/query`
      this.setState({ isLoading: true })
      getMethodRequire(path,
        params,
        (ret) => {
          const { status, msg, list } = ret
          this.setState({ isLoading: false })
          if (status !== 0) {
            message.warn('请求数据失败,请检查:' + msg)
            return
          }
          this.setState({ list: list })
        })
    }

    // 下载报表
    onExport = (params) => {
      const { uri } = this.props
      let path = `${uri}/export`
      let link = `${path}?${urlEncode(params)}`
      console.info('download: url=', link)
      window.open(link)
    }

    // 解析列表数据
    parseDataSource = (table) => {
      if (!Array.isArray(table) || table.length === 0) {
        return []
      }
      let dataSource = []
      for (let i = 1; i < table.length; i++) {
        let row = table[i]
        let item = {}
        row.map((value, index) => {
          item[`f${index}`] = value
        })
        dataSource.push(item)
      }
      return dataSource
    }

    genColumnTooltip = (title) => {
      if (!title) {
        return null
      }
      return {
        filterDropdown: (<span />),
        filterIcon: (
          <Tooltip placement='top' title={title}>
            <QuestionCircleOutlined style={{ fontSize: '16px' }} />
          </Tooltip>
        )
      }
    }

    // 解析表头
    parseHeader = (table) => {
      if (!Array.isArray(table) || table.length === 0) {
        return []
      }
      const header = table[0]
      return header.map((title, index) => {
        let matchRes = title.match('^(.+)\\$tooltip{(.+)}$')
        let tooltip = ''
        if (matchRes != null && matchRes.length === 3) {
          title = matchRes[1]
          tooltip = matchRes[2]
        }
        return {
          title: title,
          dataIndex: `f${index}`,
          align: 'center',
          ...this.genColumnTooltip(tooltip)
        }
      })
    }

    render () {
      const { config, list, isLoading } = this.state
      const { hideExport, tableProps, formProps } = this.props

      return (
        <Row>
          {
            config.length > 0
              ? <div style={{ width: '100%' }}>
                <Col span={24}>
                  <SimplePageParams
                    config={config}
                    onChange={v => { this.setState({ fromValue: v }) }}
                    onQuery={v => { this.onQueryTable(v) }}
                    onExport={hideExport ? null : (v) => { this.onExport(v) }}
                    formProps={formProps} />
                </Col>
                <Col span={24}>
                  <Table loading={isLoading} {...tableProps} columns={this.parseHeader(list)} dataSource={this.parseDataSource(list)} />
                </Col>
              </div>
              : <Col span={24}>
                <Space style={{ float: 'right' }}>
                  <Skeleton.Button active />
                  <Skeleton.Button active />
                </Space>
                <Skeleton active paragraph={{ rows: 6 }} />
              </Col>
          }
        </Row>
      )
    }
}

export default SimplePage

// ==========================================

async function getMethodRequire (uri, params, cbFunc) {
  let resp = null
  resp = await simpleRequire3(uri, params)
  cbFunc(resp)
}

// 格式化url参数
let urlEncode = function (param, key, encode) {
  if (param == null) return ''
  var paramStr = ''
  var t = typeof param
  if (t === 'string' || t === 'number' || t === 'boolean') {
    paramStr += '&' + key + '=' + (encode == null || encode ? encodeURIComponent(param) : param)
  } else {
    for (var i in param) {
      var k = key == null ? i : key + (param instanceof Array ? '[' + i + ']' : '.' + i)
      paramStr += urlEncode(param[i], k, encode)
    }
  }
  return paramStr
}
