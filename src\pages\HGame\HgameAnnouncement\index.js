import React, { Component } from 'react'
import { connect } from 'dva'
import { Space, Modal, Card, Select, Table, Divider, Form, Input, Button, DatePicker, message, Popconfirm } from 'antd'
import TinymceEditer from '@/components/TinymceEditer'
import { timeFormater } from '@/utils/common'
import './style.css'
import moment from 'moment/moment'

const namespace = 'hgameAnnouncement'

// const tagIdList = [{ idx: 1, name: '首页' }, { idx: 2, name: '运营规则' }, { idx: 3, name: '违规处罚' }, { idx: 4, name: '应用玩法' }]
const tagIdList = [{ idx: 2, name: '运营规则' }, { idx: 3, name: '违规处罚' }, { idx: 4, name: '应用玩法' }]
const childIdList = {
  0: [],
  1: [],
  2: [{ idx: 201, name: '平台规则' }, { idx: 202, name: '超级主持' }, { idx: 203, name: '水晶公会' }, { idx: 204, name: '扶持招募' }, { idx: 205, name: '推荐申请' }],
  3: [{ idx: 301, name: '主持违规处罚' }, { idx: 302, name: '其他违规处罚' }],
  4: [{ idx: 401, name: '新玩法' }, { idx: 402, name: '新功能' }]
}

const roleList = [{ idx: 1, name: '房管' }, { idx: 2, name: '厅管' }, { idx: 3, name: '主持' }, { idx: 4, name: '公会' }, { idx: 5, name: '全部' }]

const validOpts = [{ idx: 1, name: '是' }, { idx: 2, name: '否' }]
const topOpts = [{ idx: 1, name: '是' }, { idx: 0, name: '否' }]
const showNewOpts = [{ idx: 1, name: '是' }, { idx: 2, name: '否' }]

function isValid (validBegin, validEnd) {
  if (validBegin === 0 || validEnd === 0) return false
  const now = Math.floor(new Date().getTime() / 1000)
  return now >= validBegin && now < validEnd
}

function findName (nav, navList) {
  if (navList && Array.isArray(navList)) {
    let v = navList.find(e => e.idx === nav)
    if (v) {
      return v.name
    }
  }
  return ''
}

@connect(({ hgameAnnouncement }) => ({
  model: hgameAnnouncement
}))

class HgameAnnouncement extends Component {
  columns = [
    { title: '序号', dataIndex: 'idx', align: 'center' },
    { title: '标题', dataIndex: 'title', align: 'center' },
    { title: '生效时间', dataIndex: 'validBegin', align: 'center', render: (text, record) => (record.validBegin > 0 ? timeFormater(record.validBegin) : '') },
    { title: '失效时间', dataIndex: 'validEnd', align: 'center', render: (text, record) => (record.validEnd > 0 ? timeFormater(record.validEnd) : '') },
    { title: '是否生效', dataIndex: 'isValid', align: 'center', render: (text, record) => (isValid(record.validBegin, record.validEnd) ? <font color={'green'}>是</font> : <font color={'red'}>否</font>) },
    { title: '是否展示new标签', dataIndex: 'showNew', align: 'center', render: (text, record) => (record.showNew === 1 ? <font color={'green'}>是</font> : <font color={'red'}>否</font>) },
    { title: '是否置顶', dataIndex: 'postOrder', align: 'center', render: (text, record) => (record.postOrder === 1 ? <font color={'green'}>是</font> : <font color={'red'}>否</font>) },
    {
      title: '对应公告导航',
      dataIndex: 'nav',
      align: 'center',
      render: (text, record) => (findName(record.tagId, tagIdList) + '-' + findName(record.childId, childIdList[record.tagId]))
    },
    { title: '展示对象', dataIndex: 'displayRole', align: 'center', render: (text, record) => (findName(record.displayRole, roleList)) },
    { title: '展示对象2', dataIndex: 'displayRole2', align: 'center', render: (text, record) => (findName(record.displayRole2, roleList)) },
    { title: '发布人', dataIndex: 'operator', align: 'center' },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (<span><Popconfirm title='请确认是否删除?' onConfirm={this.deleteHandler(record.postId)}><a href=''>删除</a></Popconfirm>
        <Divider type='vertical' /><a onClick={this.addUpdateHandler(record)}>修改</a></span>)
    }
  ]

  state = {
    visible: false,
    tagId: 1,
    tagIdForm: 0,
    value: ''
  }

  componentDidMount = () => {
    this.getList()
  }

  deleteHandler = (postId) => () => {
    this.props.dispatch({
      type: `${namespace}/deleteAnnouncement`,
      payload: { postId: postId }
    })
  }

  getList = (tagId, childId, isValid, postOrder) => {
    console.log(tagId, childId, isValid, postOrder)
    let param = {}
    if (tagId) {
      param.tagId = tagId
    }
    if (childId) {
      param.childId = childId
    }
    if (isValid) {
      param.isValid = isValid
    }
    if (postOrder !== undefined) {
      param.postOrder = postOrder === 0 ? 2 : 1
    }
    console.log('getList====', param)

    this.props.dispatch({
      type: `${namespace}/listAnnouncement`,
      payload: param
    })
  }

  addUpdateHandler = (record) => () => {
    const isUpdate = record !== null && record !== undefined
    if (isUpdate && this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue({
        postId: record.postId,
        title: record.title,
        tagId: record.tagId,
        childId: record.childId > 0 ? record.childId : null,
        validBegin: record.validBegin ? moment.unix(record.validBegin) : null,
        validEnd: record.validEnd ? moment.unix(record.validEnd) : null,
        postOrder: record.postOrder,
        displayRole: record.displayRole,
        displayRole2: record.displayRole2 > 0 ? record.displayRole2 : null,
        showNew: record.showNew !== 0 ? record.showNew : null,
        content: record.content
      })
      this.setState({ tagIdForm: record.tagId })
    }

    this.setState({ visible: true, isUpdate: isUpdate })
  }

  hiddenModal = () => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.setState({ visible: false, isUpdate: null, tagIdForm: 0 })
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  handleCancel = e => {
    this.hiddenModal()
  }

  handleSubmit = e => {
    if (this.formRef) {
      this.formRef.submit()
    }
  }

  onFinish = values => {
    const { isUpdate } = this.state
    console.log(values, isUpdate)

    if (values === null || values === undefined) {
      message.warn('填写有误')
      return
    }

    if (values.tagId === undefined) {
      message.warn('导航选择有误')
      return
    }
    if (values.tagId !== 1 && (values.childId === undefined || values.childId <= 0)) {
      message.warn('导航选择有误')
      return
    }

    if (values.tagId !== 1 && values.childId <= 0) {
      message.warn('导航选择有误')
      return
    }

    let content = values.content
    if (content.length === 0) {
      message.warn('请填写内容')
      return
    }

    if (values.validBegin.unix() >= values.validEnd.unix()) {
      message.warn('生效时间不能大于失效时间')
      return
    }

    let data = {
      postId: values.postId,
      title: values.title,
      tagId: values.tagId,
      tagName: findName(values.tagId, tagIdList),
      childId: values.childId,
      childName: findName(values.childId, childIdList[values.tagId]),
      validBegin: values.validBegin.unix(),
      validEnd: values.validEnd.unix(),
      postOrder: values.postOrder,
      displayRole: values.displayRole,
      displayRole2: values.displayRole2,
      showNew: values.showNew,
      content: content
    }

    console.log(data)

    this.props.dispatch({
      type: isUpdate ? `${namespace}/updateAnnouncement` : `${namespace}/addAnnouncement`,
      payload: data
    })

    this.hiddenModal()
  }

  saveFormRefSearch = (formRef) => {
    this.formRefSearch = formRef
  }

  onFinishSearch = values => {
    console.log('onFinishSearch====', values)
    this.getList(values.tagId, values.childId, values.isValid, values.postOrder)
  }

  handleTagIdChange = (e) => {
    console.log(e)
    this.setState({ tagId: e })
    if (this.formRefSearch) {
      this.formRefSearch.setFieldsValue({ childId: null })
    }
  }

  searchHandler = () => () => {
    if (this.formRefSearch) {
      this.formRefSearch.submit()
    }
  }

  handleTagIdChangeForm = (e) => {
    console.log(e)
    this.setState({ tagIdForm: e })
    if (this.formRef) {
      this.formRef.setFieldsValue({ childId: null })
    }
  }

  render () {
    const { visible, isUpdate, tagId, tagIdForm } = this.state
    const { model: { list } } = this.props
    console.log(list)
    const formItemLayout = {
      labelCol: {
        xs: { span: 4 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 2 },
        sm: { span: 15 }
      }
    }

    return (
      <Card>
        <Form ref={this.saveFormRefSearch} onFinish={this.onFinishSearch} >
          <Space>
            <Form.Item label='对应公告导航' name='tagId' >
              <Select
                allowClear
                style={{ width: 120 }}
                onChange={this.handleTagIdChange}
                options={tagIdList.map((item) => ({ label: item.name, value: item.idx }))}
              />
            </Form.Item>

            <Form.Item name='childId' >
              <Select
                allowClear
                style={{ width: 150 }}
                options={tagId ? childIdList[tagId].map((item) => ({ label: item.name, value: item.idx })) : null}
              />
            </Form.Item>

            <Form.Item label='是否生效' name='isValid' >
              <Select
                allowClear
                style={{ width: 80 }}
                options={validOpts.map((item) => ({ label: item.name, value: item.idx }))}
              />
            </Form.Item>

            <Form.Item label='是否置顶' name='postOrder' >
              <Select
                allowClear
                style={{ width: 80 }}
                options={topOpts.map((item) => ({ label: item.name, value: item.idx }))}
              />
            </Form.Item>

            <Form.Item >
              <Button type='primary' onClick={this.searchHandler()}>查询</Button>
            </Form.Item>
          </Space>
        </Form>

        <Button type='primary' onClick={this.addUpdateHandler()}>新增</Button>

        <Table rowKey='idx' pagination={this.defaultPageValue} columns={this.columns} dataSource={list} />

        <Modal forceRender maskClosable={false} visible={visible} width={1400} title={isUpdate ? '更新公告' : '新增公告'} onCancel={this.handleCancel} onOk={this.handleSubmit} okText={'确认并提交'}>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <Form.Item name='postId' hidden>
              <Input hidden />
            </Form.Item>

            <Form.Item name='title' label='标题' rules={[{ required: true, message: '必填' }]}>
              <Input style={{ width: 247 }} maxLength={20} />
            </Form.Item>

            <Form.Item label='对应公告导航' rules={[{ required: true, message: '必填' }]}>
              <Space>
                <Form.Item name='tagId' >
                  <Select
                    allowClear
                    placeholder={'一级导航'}
                    style={{ width: 100 }}
                    onChange={this.handleTagIdChangeForm}
                    options={tagIdList.map((item) => ({ label: item.name, value: item.idx }))}
                  />
                </Form.Item>

                <Form.Item name='childId' >
                  <Select
                    allowClear
                    placeholder={'二级导航'}
                    style={{ width: 140 }}
                    options={tagIdForm ? childIdList[tagIdForm].map((item) => ({ label: item.name, value: item.idx })) : null}
                  />
                </Form.Item>
              </Space>
            </Form.Item>

            <Form.Item label='生效时间' name='validBegin' rules={[{ required: true, message: '必填' }]} extra='注: 必填. 公告会再配置的时间自动生效'>
              <DatePicker style={{ width: 220 }} showTime='true' format='YYYY-MM-DD HH:mm' />
            </Form.Item>

            <Form.Item label='失效时间' name='validEnd' rules={[{ required: true, message: '必填' }]} extra='注: 必填. 公告会再配置的时间自动失效'>
              <DatePicker style={{ width: 220 }} showTime='true' format='YYYY-MM-DD HH:mm' />
            </Form.Item>

            <Form.Item name='postOrder' label='是否置顶' rules={[{ required: true, message: '必填' }]}>
              <Select
                allowClear
                style={{ width: 220 }}
                options={topOpts.map((item) => ({ label: item.name, value: item.idx }))}
              />
            </Form.Item>

            <Form.Item name='displayRole' label='展示对象' rules={[{ required: true, message: '必填' }]}>
              <Select
                allowClear
                style={{ width: 220 }}
                options={roleList.map((item) => ({ label: item.name, value: item.idx }))}
              />
            </Form.Item>

            <Form.Item name='displayRole2' label='展示对象2'>
              <Select
                allowClear
                style={{ width: 220 }}
                options={roleList.map((item) => ({ label: item.name, value: item.idx }))}
              />
            </Form.Item>

            <Form.Item name='showNew' label='是否展示new标签' rules={[{ required: true, message: '必填' }]}>
              <Select
                allowClear
                style={{ width: 220 }}
                options={showNewOpts.map((item) => ({ label: item.name, value: item.idx }))}
              />
            </Form.Item>

            <Form.Item name='content' getValueFromEvent={(e) => { return e.target.getContent() }} label='内容'>
              <TinymceEditer />
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default HgameAnnouncement
