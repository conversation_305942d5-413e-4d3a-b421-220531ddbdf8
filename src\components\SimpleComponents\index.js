import React, { useState } from 'react'
import { Row, Col, Divider, Typography, Space, Tooltip, Image, message, Button, Popover, Radio, Input, Modal, Select, Collapse, Tag } from 'antd'
import { checkIsNumber, checkIsNumberList, copyToClip, timeFormater } from '@/utils/common'
import { PlusOutlined, FormatPainterOutlined, ScissorOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import moment from 'moment'
const { Text, Title, Link, Paragraph } = Typography
const { TextArea } = Input

/* 数据展示面板，使用示例：
const baseInfoList = [
    { label: '会长昵称', dataIndex: 'familyId' },
    { label: '家族名称', dataIndex: 'familyName', span: 24, render: (v, r)=>{return v ? v: '-'} },
]
dataSource = {familyId: 123, familyName: 'xxx'}
<InfoBoard columns={baseInfoList} dataSource={info} title='基本信息' />
*/
export const InfoBoard = (props) => {
  const { columns, dataSource, title, divider } = props
  const defaultSpan = props.colSpan || 12
  return (
    <>
      <Row>
        <Col span={24}><Title level={5}>{title}</Title></Col>
        {
          columns.map(item => {
            const { span, dataIndex, render } = item
            const value = dataSource[dataIndex]

            return <Col span={span || defaultSpan} style={{ marginBottom: '10px' }}>
              <Text>{item.label} <Text type='secondary' style={{ marginRight: '5px' }}>:</Text> </Text>
              {
                typeof render === 'function'
                  ? render(value, dataSource)
                  : <Text>{value}</Text>
              }
            </Col>
          })
        }
        {
          divider === false
            ? ''
            : <Divider />
        }

      </Row>
    </>
  )
}

const defaultFallBackURL = 'https://makefriends.bs2dl.yy.com/1678169414_ce92ce805877c85b39f826adb7f37738.png'

// 非图片类型不能预览时使用对应格式的图标
const fixDisplayImgURL = (url) => {
  if (/^https?:\/\/.+\.swf(\?.+)?$/.test(url)) {
    return 'https://makefriends.bs2dl.yy.com/1678159696_54720d61b9c236830f3c027f2a4ca06e.png'
  }
  if (/^https?:\/\/.+\.mp4(\?.+)?$/.test(url)) {
    return 'https://makefriends.bs2dl.yy.com/makefriends_50071797_1678170807505'
  }
  if (/^https?:\/\/.+\.svga(\?.+)?$/.test(url)) {
    return 'https://makefriends.bs2dl.yy.com/makefriends_50071797_1678171692208'
  }
  return url
}

/* 图片列表展示 (适配mp4\svg等格式链接)，使用示例：
imgList: array or string
<ImagesList imgList=['http://xxx'] width: 100, height: 100 />
*/
export const ImagesList = (props) => {
  const {
    imgList,
    width = 60,
    height = 60
  } = props

  let fixList = imgList

  if (imgList === null || imgList === undefined) {
    fixList = []
  } else if (typeof imgList === 'string') {
    fixList = [imgList]
  }

  if (!Array.isArray(fixList)) {
    message.warn(`传参错误, 请检查代码! (ImagesList) fixList=${fixList}`)
    return <Text type='danger'>{fixList}</Text>
  }

  return (
    <Space size='small' key={imgList}>
      {
        fixList.map(url => {
          const fixUrl = fixDisplayImgURL(url)
          if (fixUrl !== url) {
            return <DownloadURL src={url} icon={fixUrl} />
          }
          return <Tooltip title={url} autoAdjustOverflow={false} mouseEnterDelay={0.5} >
            <Image src={fixUrl} width={width} height={height} style={{ margin: '2px' }}
              fallback={defaultFallBackURL} />
          </Tooltip>
        })
      }
    </Space>
  )
}

/* 下载链接, 使用示例
<DownloadURL src='http:/xxxxx/name.pdf' icon='http://xxx' />
*/
export const DownloadURL = (props) => {
  const { src, icon, height } = props
  let link = ''
  if (src) link = src
  const items = link.split('?')[0].split('/')
  const fileName = items[items.length - 1]
  return (
    <>
      {
        link === ''
          ? <Text code>空</Text>
          : <Tooltip title={link} mouseEnterDelay={1}>
            <Popover trigger='click' content={<Button type='primary' onClick={() => { window.open(link) }}>点我下载</Button>} mouseLeaveDelay={0.5} title={null}>
              {
                icon
                  ? <Image src={icon} preview={false} height={height || 40} />
                  : <Link>{fileName}</Link>
              }
            </Popover>
          </Tooltip>
      }
    </>
  )
}

/* 渲染带有图片和链接的文本
const demo = '这是图片image{http://xxx.jpg},这是文件链接link{http://download.zip}。
<ElementText value={demo} />
*/
export const ElementText = (props) => {
  const imgOrLinkReg = new RegExp(/(image|link){http[^\\}]+}/)
  const { value } = props
  let remain = value
  let eles = []
  while (remain) {
    let matchRes = remain.match(imgOrLinkReg)
    if (matchRes === null) {
      eles.push(<Text>{remain}</Text>)
      remain = ''
      break
    }
    let [ele, matchType] = matchRes
    let posi = remain.indexOf(ele)
    let text = remain.substring(0, posi)
    let url = ele.substring(matchType === 'link' ? 5 : 6, ele.length - 1)
    eles.push(<Text>{text}</Text>)
    eles.push(matchType === 'link'
      ? <DownloadURL src={url} />
      : <Tooltip title={url}>
        <Image height='3em' src={url} alt={url} placeholder={url} style={{ overflow: 'hidden' }} />
      </Tooltip>)
    remain = remain.substring(posi + ele.length, remain.length)
  }
  return <Text>{eles}</Text>
}

/* 单选组合， 参数如下:
defaultValue: any,
width: string,
onChange: (v)=>{...}
columns: array, [label: string, value: any, inputType: string, placeholder: string, ... ]
*/
export const ReasonSelector = (props) => {
  const { width, columns, onChange, defaultValue } = props
  const [selectVal, setSelectVal] = useState(defaultValue)
  const contentStyle = { display: 'inline-grid', width: width }
  if (!Array.isArray(columns) || typeof onChange !== 'function') {
    message.warn('用发错误,请检查代码~ (ReasonSelector)')
    return ''
  }
  return (
    <Radio.Group onChange={(e) => { setSelectVal(e.target.value); onChange(e.target.value) }} defaultValue={defaultValue}>
      <Space direction='vertical'>
        {
          columns.map((item) => {
            switch (item.inputType) {
              case 'TextArea':
                return <Radio value={item.value} style={{ whiteSpace: 'break-spaces' }}>
                  <div style={contentStyle}>
                    {item.label || item.value || '???'}<br />
                    <TextArea hidden={selectVal !== item.value} autoSize={{ minRows: 2 }} onChange={(e) => onChange(e.target.value)} />
                  </div>
                </Radio >
              default:
                return <Radio value={item.value} style={{ whiteSpace: 'break-spaces' }}>
                  <div style={contentStyle}>{item.label || item.value || '???'}</div>
                </Radio >
            }
          })
        }
      </Space>
    </Radio.Group>
  )
}

/* 改进版数字输入框, 特点：
1. 去掉右边的步进按钮
2. 保证onChange的返回值为number类型或空符号
*/
export const InputNumber = (props) => {
  const { onChange, value, style, placeholder } = props
  const onChangeOnlyNumber = (e) => {
    let v = e.target.value
    if (typeof onChange !== 'function') return
    if (v && !checkIsNumber(v)) return // 输入非数字类型时不更新
    if (v === '') {
      onChange(v)
    } else {
      onChange(parseInt(v))
    }
  }
  return <Input value={value} onChange={onChangeOnlyNumber} placeholder={placeholder} style={style} />
}

/* 整数数组输入框, 特点:
1. 保证只能填写整数、整数数组, 并返回的字符串可以逗号分隔得到对应的整数数组
2. 支持双击弹出文本框批量输入
*/
export const InputNumberArray = (props) => {
  const { value, onChange, style, placeholder } = props
  if (typeof onChange !== 'function') {
    message.error('使用方法错误,请检查代码 (InputNumberArray)')
    return <></>
  }
  const [visible, setVisible] = useState(false)
  const [textAreaInput, setTextAreaInput] = useState('')
  const onChangeOnlyNumber = (e) => {
    let v = e.target.value
    if (v && !checkIsNumber(v)) return // 输入非数字类型时不更新
    onChange(v)
  }
  const updateValueFromTextare = () => {
    if (textAreaInput && !(checkIsNumberList(textAreaInput))) { // 批量输入时保证格式正确
      message.warn('请以正确的格式输入！')
      return
    }
    onChange(textAreaInput.replaceAll('\n', ','))
    setTextAreaInput('')
    setVisible(false)
  }
  return (
    <>
      <Tooltip title='双击可批量输入'><Input value={value} allowClear onChange={onChangeOnlyNumber} placeholder={placeholder} style={style} onDoubleClick={() => { setVisible(true); setTextAreaInput(value) }} /></Tooltip>
      <Modal title='批量输入' visible={visible} onCancel={() => setVisible(false)} onOk={updateValueFromTextare} >
        <Input.TextArea value={textAreaInput} onChange={e => setTextAreaInput(e.target.value)} autoSize={{ minRows: 4 }} placeholder='请以逗号‘,’或换行来隔开每个整数' />
      </Modal>
    </>
  )
}

/* 封装Select, 增加默认带关键字搜索的特性。 注意事项
1. 选项必须通过 option 属性指定
2. option传值中key字段会被自动补充
*/
export const SearchSelect = (props) => {
  const { options } = props
  if (!Array.isArray(options)) {
    message.error('使用方法错误,请检查代码: (SearchSelect，option参数必须传)')
    return ''
  }
  let fixOptions = [...options]
  fixOptions.forEach(item => {
    if (!item.key) item.key = `${item.value}_${item.label}`
  })
  return (
    <Select
      {...props}
      showSearch
      options={fixOptions}
      filterOption={(input, option) => { return option.label?.includes(input) }} />
  )
}

// 展示各种格式的文字
export const GrandText = (props) => {
  let { type, value, width } = props
  if (!type) {
    type = 'tooltip'
  }
  const centerContent = <Text style={{ overflow: 'hidden', display: 'inline-block', textOverflow: 'ellipsis', whiteSpace: 'nowrap', width: width }}>{value}</Text>
  switch (type) {
    case 'tooltip':
      return <Tooltip title={value}>
        {centerContent}
      </Tooltip>
    case 'popover':
      return <Popover content={<WrapParagraph value={value} />} >
        {centerContent}
      </Popover>
    default:
      return '???'
  }
}

// 文本自动换行
const WrapParagraph = (props) => {
  const { value } = props
  if (!value) {
    return ''
  }
  let res = value.split('\n', -1)
  // console.debug('res===>', res)
  return <Paragraph>
    {
      res.map(item => {
        return <><Text>{item}</Text><br /></>
      })
    }
  </Paragraph>
}

// 收缩的使用提示区域
export const SimpleCollapse = (props) => {
  const { title, value } = props
  return <Collapse ghost>
    <Collapse.Panel header={<a>{title}</a>} style={{ position: 'relative', top: '-1em', left: '-1em' }} showArrow={false} >
      <div style={{ backgroundColor: 'aliceblue', padding: '10px' }} >
        {value.split('\n', -1).map(row => { return <div>{row}</div> })}
      </div>
    </Collapse.Panel>
  </Collapse>
}

// 带下拉框选项的文字输入框
export const SelectInput = (props) => {
  const { options } = props
  const [ keyword, setKeyword ] = useState('')
  return (
    <Select {...props}
      options={keyword ? [{ label: keyword, key: keyword, value: keyword }] : options}
      showSearch
      onSearch={(v) => { setKeyword(v) }}
    />
  )
}

// 任意结构体编辑器
export const ValueMap = (props) => {
  const {
    value = null, // 结构体或map
    isEdit = false, // 是否编辑模式
    onChange = (v) => {}
  } = props

  const [visible, setVisible] = useState(false)
  const [tmpKey, setTmpKey] = useState('')
  const [tmpVal, setTmpVal] = useState('')

  if (typeof value !== 'object') {
    message.warn('使用方法错误,请检查: (ValueMap)')
    return '???'
  }

  let items = []
  for (const i in value) {
    items.push({ key: i, value: value[i] })
  }

  const removeTag = (key) => {
    let cp = { ...value }
    delete cp[key]
    onChange(cp)
  }

  return (
    <>
      {
        items.map((item, i) => {
          return <Tag closable={isEdit} onClose={() => removeTag(item.key)} >
            <Text type='secondary'>{item.key}: </Text><Text>{item.value}</Text>
          </Tag>
        })
      }
      {
        !isEdit
          ? ''
          : <Tag onClick={() => { setVisible(true); setTmpKey(''); setTmpVal('') }} >
            <PlusOutlined /> 新增
          </Tag>
      }
      <Modal title='新增键值对' visible={visible}
        onCancel={() => { setVisible(false) }}
        onOk={() => {
          if (tmpKey) {
            let cp = { ...value }
            cp[tmpKey] = tmpVal
            onChange(cp)
            setVisible(false)
          } else {
            message.warn('key不能为空')
          }
        }}
      >
        <Row>
          <Col span={10}>
            <Input placeholder='key' value={tmpKey} onChange={(e) => { setTmpKey(e.target.value) }} />
          </Col>
          <Col span={1}>
            👉
          </Col>
          <Col span={10}>
            <Input placeholder='value' value={tmpVal} onChange={(e) => { setTmpVal(e.target.value) }} />
          </Col>
        </Row>
      </Modal>
    </>
  )
}

// 数组编辑器
export const ArrayEditor = (props) => {
  const {
    value = null, // 字符串数组或number数组
    isEdit = false, // 编辑还是查看
    type = 'string', // 数字类型: [string|number]
    onChange = (v) => {}
  } = props

  const [visible, setVisible] = useState(false)
  const [tmpVal, setTmpVal] = useState('')

  if (!Array.isArray(value)) {
    message.warn('使用方法错误,请检查: (ArrayEditor)')
    return '???'
  }

  const removeTag = (index) => {
    let cp = [...value]
    cp.splice(index, 1)
    onChange(cp)
  }

  return (
    <>
      {
        value.map((val, i) => {
          return <Tag key={val} style={{ marginBottom: '5px' }} closable={isEdit} onClose={() => removeTag(i)} >
            <Text type='secondary'>{val}</Text>
          </Tag>
        })
      }
      {
        !isEdit
          ? ''
          : <Tag onClick={() => { setVisible(true); setTmpVal('') }} >
            <PlusOutlined /> 新增
          </Tag>
      }
      <Modal title='新增元素' visible={visible}
        onCancel={() => { setVisible(false) }}
        onOk={() => {
          if (tmpVal) {
            let cp = [...value, tmpVal]
            onChange(cp)
            setVisible(false)
          } else {
            message.warn('值不能为空')
          }
        }}
      >
        <Row>
          <Col span={24}>
            {
              type === 'string'
                ? <Input placeholder='value' value={tmpVal} onChange={(e) => { setTmpVal(e.target.value) }} />
                : <InputNumber placeholder='value' value={tmpVal} onChange={(v) => { setTmpVal(v) }} />
            }
          </Col>
        </Row>
      </Modal>
    </>
  )
}

/* 复制粘贴按钮
参数：
1. getCopyVal：()=>{return val},  函数，返回需要复制的内容(结构体)
2. pasteVal: (val)=>{...},  函数, 控制得到剪切板内容后如何赋值
*/
export const CopyPasteButton = (props) => {
  const {
    getCopyVal = () => { return null },
    pasteVal = (newVal) => {}
  } = props

  const onCopy = () => {
    copyToClip(JSON.stringify(getCopyVal()))
  }

  const onPaste = () => {
    if (navigator.clipboard == null || navigator.clipboard.readText == null) {
      message.warn('浏览器不支持')
      return
    }
    navigator.clipboard.readText().then(text => {
      let obj = {}
      try {
        obj = JSON.parse(text)
      } catch (err) {
        message.warn(`解析剪切板内容失败, 请检查! err=${err}`)
        return
      }
      pasteVal(obj)
    })
  }

  return (
    <Popover content={
      <Space>
        <Button onClick={onCopy}><ScissorOutlined />复制</Button>
        <Button onClick={onPaste}><FormatPainterOutlined />粘贴</Button>
      </Space>}
    >
      <Button type='link' style={{ color: '#44ca40' }}>复制 or 粘贴</Button>
    </Popover >
  )
}

/* 活动状态格式化
参数: start, end: 开始时间和结束时间
*/
export function ActStatus (props) {
  const { start, end } = props
  const timenow = moment().unix()
  if (timenow < start) {
    return <Text type='secondary'>未开始</Text>
  }
  if (timenow > end) {
    return <Text type='secondary'>已结束</Text>
  }
  return <Text type='success'>生效中</Text>
}

// 展示审批信息
export const AprInfoDesc = (props) => {
  const { value } = props
  const { aprUid, aprNick, aprRemark, aprResult, aprTime } = value || {}
  const { opUid, opNick, opRemark, opTime } = value || {}
  let statusColor = ['warning', 'danger', 'success'][['OnGoing', 'Rejected', 'Passed'].indexOf(aprResult)]
  let statusTip = ['待审批', '被驳回', '已通过'][['OnGoing', 'Rejected', 'Passed'].indexOf(aprResult)]
  return (
    <Row>
      <Col>
        <Row>提交人：
          <Text type='secondary'>
            <Tooltip title={opUid}>{`${opNick}_(${timeFormater(opTime)})`}</Tooltip> <Divider type='vertical' />
          </Text>
        </Row>
        <Row>审批人：<Text type='secondary'> {aprUid === 0 ? '系统自动审批' : aprNick}_({timeFormater(aprTime)}) <Divider type='vertical' /></Text> </Row>
      </Col>
      <Col>
        <Row>申请理由：<Text type='secondary'> {opRemark || '(空)'} <Divider type='vertical' /></Text></Row>
        <Row>审批备注：<Text type='secondary'> {aprRemark || '(空)'}</Text><Divider type='vertical' /></Row>
      </Col>
      <Col>
        <Row><Text style={{ height: '1.5715em', width: '1em' }} /></Row>
        <Row>审批结果：<Text type={statusColor}> {statusTip}</Text></Row>
      </Col>
    </Row>
  )
}

/*  颜色编辑器
*/
export const ColorPicker = (props) => {
  const { value, onChange, width = '8em' } = props
  return <div style={{ display: 'inline-block', position: 'relative' }}>
    <Input value={value} style={{ display: 'inline-block', width: width }} onChange={(e) => onChange(e.target.value)} />
    <div style={{ display: 'inline-block', position: 'absolute', width: '3em', height: '100%', background: value }} />
  </div>
}

// 表头显示提示语
// 用在columns上，例如:  { title: '模拟收入', dataIndex: 'betAmethyst', align: 'center', ...genColumnTooltip('果实消耗流水') },
export const genColumnTooltip = (title) => {
  return {
    filterDropdown: (<span />),
    filterIcon: (
      <Tooltip placement='top' title={title}>
        <QuestionCircleOutlined style={{ fontSize: '16px' }} />
      </Tooltip>
    )
  }
}
