import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
// import { Card, Typography, Row, Col, Space, Input, Button, Table, Divider, message } from 'antd'
import { Card, Typography, Table, Divider, message } from 'antd'
import dateString from '@/utils/dateString'
import { connect } from 'dva'
import { screenshotByID } from '@/utils/screenShot'

const namespace = 'hatkingProbability'
const getGrandPeriodProbabilityURI = `${namespace}/getGrandPeriodProbability`
const getGrandPeriodProbabilityHistoryURI = `${namespace}/getGrandPeriodProbabilityHistory`
const updateGrandPeriodProbabilityURI = `${namespace}/updateGrandPeriodProbability`

const sharedOnCell = (value, row, _) => {
  const obj = {
    children: value,
    props: {}
  }
  if (row.key === 1) {
    obj.props.rowSpan = 2
  } else {
    obj.props.rowSpan = 0
  }

  return obj
}

@connect(({ hatkingProbability }) => ({ // model 的 namespace
  model: hatkingProbability // model 的 namespace
}))
class HatkingProbability extends Component { // 默认页面组件，不需要修改
  columns = [
    {
      title: '成功标记表',
      children: [
        {
          title: '',
          dataIndex: 'name',
          align: 'center',
          width: 80,
          render: sharedOnCell
        },
        {
          title: '兜底期1',
          dataIndex: 'stage1',
          align: 'center',
          width: 120
        },
        {
          title: '成功标记',
          dataIndex: 'strategy1',
          align: 'center',
          width: 200,
          render: sharedOnCell
        },
        {
          title: '兜底期2',
          dataIndex: 'stage2',
          align: 'center',
          width: 120
        },
        {
          title: '成功标记',
          dataIndex: 'strategy2',
          align: 'center',
          width: 200,
          render: sharedOnCell
        },
        {
          title: '平稳期',
          dataIndex: 'stage3',
          align: 'center',
          width: 120
        },
        {
          title: '成功标记',
          dataIndex: 'strategy3',
          align: 'center',
          width: 200,
          render: sharedOnCell
        }
      ]
    }
  ]

  historyColumns = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    { title: '大额概率', dataIndex: 'probability', align: 'center' },
    { title: '生效时间', dataIndex: 'reviewTime', align: 'center', render: (text, record) => (text === 0 ? '无' : dateString(text)) },
    { title: '修改人', dataIndex: 'editorName', align: 'center' },
    { title: '审批人', dataIndex: 'reviewerName', align: 'center' }
  ]

  pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

state = { value: {}, loading: false }

// 获取列表
componentDidMount () {
  this.loadData()
}

loadData = () => {
  const { dispatch } = this.props

  dispatch({
    type: `${namespace}/listen`,
    payload: this.listenPoolConfigChange
  })

  dispatch({
    type: getGrandPeriodProbabilityURI
  })

  dispatch({
    type: getGrandPeriodProbabilityHistoryURI
  })
}

listenPoolConfigChange = poolConfig => {
  let probabilityInUse = ''
  if (poolConfig !== undefined && poolConfig.probability !== undefined) {
    probabilityInUse = poolConfig.probability
  }
  this.setState({ probabilityInUse: probabilityInUse, probabilityOrigin: probabilityInUse })
}

isInteger (obj) {
  return typeof obj === 'number' && obj % 1 === 0
}

  // 提交豆荚限制修改请求
  onSubmitPodLimitCfg = () => {
    const { dispatch } = this.props
    const { probabilityInUse, probabilityOrigin } = this.state
    if (probabilityInUse === probabilityOrigin) {
      message.warn('配置没有改变')
      return
    }

    var digits = probabilityInUse.split('/')
    if (digits.length !== 2) {
      message.warn('必须输入分数')
      return
    }

    let numerator = parseInt(digits[0])
    let denominator = parseInt(digits[1])
    if (!this.isInteger(numerator) || !this.isInteger(denominator)) {
      message.warn('分数的分子、分母必须为整数')
      return
    }

    if (numerator >= denominator) {
      message.warn('分数的分子必须小于分母')
      return
    }

    console.log(probabilityInUse, probabilityOrigin)
    this.setState({ loading: true })
    screenshotByID('screenShotDiv', (isOk, url) => {
      if (!isOk) {
        message.warn('截图失败~')
      }
      console.debug('url===>', url)
      dispatch({
        type: updateGrandPeriodProbabilityURI,
        payload: { probability: probabilityInUse, screenShot: url }
      })
      this.setState({ probabilityEdit: false, loading: false })
    })
  }

  render () {
    // const { route, model: { oldProbability, probability, historyList } } = this.props
    // const { probabilityEdit, probabilityInUse, loading } = this.state
    const { route, model: { probability } } = this.props
    const { Title } = Typography
    // const { accessCfg } = this.props.model

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>

          <div id='screenShotDiv' style={{ padding: '1em' }}>
            <Title level={4}>当前配置</Title>
            <Table dataSource={probability} bordered columns={this.columns} pagination={false} size='small' />
            <Divider />
          </div>

          <Divider />

        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default HatkingProbability // 保证唯一
