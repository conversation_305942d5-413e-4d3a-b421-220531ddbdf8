/* eslint-disable eqeqeq */
import { getLists, whiteListAddMult, whiteListDel, whiteListDelMultMult } from './api'
import { Modal, message } from 'antd'
import { checkSid, checkSsid } from '@/utils/common'

// 检查sid-ssid列表
function checkSidSSidList (sidSsidList) {
  if (typeof sidSsidList !== 'string') {
    Modal.warn({ content: '发生错误，请查看控制台' })
    console.error('[添加频道跳转白名单]: 类型错误， typeof sidSsid=', typeof sidSsidList)
    return false
  }
  if (sidSsidList.length === 0) {
    Modal.warn({ content: '请输入数据再提交' })
    return false
  }
  if (sidSsidList.indexOf('：') >= 0) {
    Modal.warn({ content: '请使用英文符号“:”' })
    return false
  }
  let tmpArray = sidSsidList.split('\n')
  for (let i = 0; i < tmpArray.length; i++) {
    let sidSsid = tmpArray[i].split(':')
    if (sidSsid.length != 2) {
      Modal.warn({ content: '格式不符合,请修改后再提交：' + tmpArray[i] })
      return false
    }
    if (!checkSid(sidSsid[0]) || !checkSsid(sidSsid[1])) {
      Modal.warn({ content: '格式不符合,请修改后再提交：' + tmpArray[i] })
      return false
    }
  }
  return true
}

export default {
  namespace: 'jumpChannelWhiteList',
  state: {
    updating: false, // 添加白名单事件是否在处理中
    displayData: [],
    selectUid: 0, // 删除的uid
    selectSid: 0, // 删除的sid
    sidSsidList: ''
  },

  reducers: {
    // 更新data到频道跳转白名单数据列表
    displayList (state, { payload: data }) {
      if (!Array.isArray(data)) {
        console.error('unexpect data type: data=', data)
        return
      }
      return {
        ...state,
        displayData: data
      }
    },
    // 设置‘添加标签页-添加’按钮的状态，true为正在处理中
    setBtnStatus (state, { payload: status }) {
      if (status !== true && status !== false) {
        console.error('unexpect argument in setBtnStatus: status=', status)
        Modal.error('发生错误，请查看控制台')
        return
      }
      return {
        ...state,
        updating: status
      }
    }
  },

  effects: {
    // 请求并刷新频道跳转白名单列表数据
    * getWhiteListData ({ params }, { select, call, put }) {
      let resp = yield call(getLists)
      let { data: { status, sidList } } = resp
      if (sidList === null) {
        message.warning('数据为空')
        yield put({
          type: 'displayList',
          payload: []
        })
        return
      }
      if (status !== 0 || !Array.isArray(sidList)) {
        console.error('getWhiteListData() get data error: response=', resp)
        Modal.error({ content: '获取频道跳转白名单数据失败，请检查控制台' })
        return
      }
      for (let i = 0; i < sidList.length; i++) {
        sidList[i].idx = i + 1
      }
      yield put({
        type: 'displayList',
        payload: sidList
      })
    },
    // 批量添加频道跳转白名单
    * addWhiteListByList ({ payload }, { call, put }) {
      const { sidSsidList } = payload
      if (!checkSidSSidList(sidSsidList)) {
        return
      }
      yield put({
        type: 'setBtnStatus',
        payload: true
      })
      let resp = yield call(whiteListAddMult, sidSsidList)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 0) {
        message.success('添加成功')
      } else {
        console.error('addWhiteListBySid()：[添加频道跳转白名单] 返回结果为：', resp)
        Modal.warn({ content: '添加失败, 请检查控制台' })
      }
      yield put({
        type: 'setBtnStatus',
        payload: false
      })
    },
    // 批量删除频道跳转白名单
    * delWhiteListByList ({ payload }, { call, put }) {
      const { sidSsidList } = payload
      if (!checkSidSSidList(sidSsidList)) {
        return
      }
      yield put({
        type: 'setBtnStatus',
        payload: true
      })
      let resp = yield call(whiteListDelMultMult, sidSsidList)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 0) {
        message.success('删除成功')
      } else {
        console.error('[批量频道跳转白名单] resp=', resp)
        Modal.error({ content: '删除失败，请查看控制台' })
      }
      yield put({
        type: 'setBtnStatus',
        payload: false
      })
    },
    // 单个删除频道转跳白名单
    * delWhiteListBySidSsid ({ payload }, { call, put }) {
      const { sid, ssid } = payload
      if (!checkSid(sid) || !checkSsid(ssid)) {
        return
      }
      let resp = yield call(whiteListDel, sid, ssid)
      const { data } = resp
      if (data == undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除白名单错误]: response=', resp)
        yield put({
          type: 'setBtnStatus',
          payload: false
        })
        return
      }
      const { status } = data
      if (status === 1) {
        message.success('删除成功')
        yield put({ // 更新列表
          type: 'getWhiteListData'
        })
      } else {
        Modal.error({ content: '删除失败，请查看控制台' })
        console.error('delWhiteListByUid() resp=', resp)
      }
    }
  }
}
