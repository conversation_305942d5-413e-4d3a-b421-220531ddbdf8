import request from '@/utils/request'
import { timeFormater } from '@/utils/common'

export const formatTimestamp = (timestamp, fmt) => {
  if (!timestamp) {
    return '-'
  }
  return timeFormater(parseInt(timestamp), fmt || 1)
}

export const doPost = function (apiPath, params) {
  return request(apiPath, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function listSsidList (sid) {
  return request(`/cross_business_play/boss/dating_yyf_guild/get_sid_ssid_list?sid=${sid}`)
}
