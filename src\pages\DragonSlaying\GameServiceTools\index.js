import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import dateString from '@/utils/dateString'
import { But<PERSON>, Card, DatePicker, Divider, Form, Input, message, Table } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'

var moment = require('moment')
const namespace = 'dragonSlayingBetDetail'
const getListUri = `${namespace}/getList`

const armoryOptions = [
  { position: 1, value: '喷火枪' },
  { position: 2, value: '双刃斧' },
  { position: 3, value: '榴弹枪' },
  { position: 4, value: '名刀' },
  { position: 5, value: '灭世' },
  { position: 6, value: '穿云箭' },
  { position: 7, value: '电枪' },
  { position: 8, value: '碎星锤' },
  { position: 9, value: '速机枪' }
]

@connect(({ dragonSlayingBetDetail }) => ({
  model: dragonSlayingBetDetail
}))
class DragonSlayingBetDetail extends Component {
  // 定义列表结构，
  columns = [
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '竞猜模式', dataIndex: 'mode', align: 'center', render: (text, record) => (text === 1 ? '单个(X8)' : text === 2 ? '相邻2个(X4)' : '一列3个(X2)') },
    { title: '竞猜位置', dataIndex: 'betPosition', align: 'center', render: text => this.renderArmory(text) },
    { title: '投注道具', dataIndex: 'propsId', align: 'center' },
    { title: '投注数量(个)', dataIndex: 'count', align: 'center' },
    { title: '竞猜花费(紫水晶)', dataIndex: 'amethyst', align: 'center' },
    { title: '竞猜时间', dataIndex: 'betTime', align: 'center', render: text => dateString(text) },
    { title: '竞猜轮次', dataIndex: 'roundStart', align: 'center', render: text => dateString(text) },
    { title: '获奖位置', dataIndex: 'winPosition', align: 'center', render: text => this.renderArmory(text) },
    { title: '是否猜中', dataIndex: 'isWinner', align: 'center', render: (text, record) => (text === 0 ? '否' : '是') },
    { title: '奖品发放', dataIndex: 'hasIssued', align: 'center', render: (text, record) => (text === 0 ? '否' : '是') }
  ]

  renderArmory (armory) {
    let names = []
    const arms = armory.split(',')
    console.log(arms, armory)
    if (Array.isArray(arms) && arms.length) {
      arms.forEach((position) => {
        for (let i = 0; i < armoryOptions.length; i++) {
          console.log(position, armoryOptions[i].position.toString())
          if (armoryOptions[i].position.toString() === position) {
            names.push(armoryOptions[i].value)
            break
          }
        }
      })
    }
    return names.join('|') + '(' + armory + ')'
  }

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  pagination = { pageSizeOptions: ['20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, startValue: null, endValue: null, uid: 0 }

  // 获取列表
  componentDidMount () {
    const { dispatch, model: { list } } = this.props
    dispatch({
      type: getListUri
    })

    this.setState({ list })
  }

  disabledStartDate = (startValue) => {
    const endValue = this.state.endValue
    if (!startValue || !endValue) {
      return false
    }
    return startValue.valueOf() > endValue.valueOf()
  }

  disabledEndDate = (endValue) => {
    const startValue = this.state.startValue
    if (!endValue || !startValue) {
      return false
    }
    return endValue.valueOf() <= startValue.valueOf()
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  onClick = () => {
    const { dispatch } = this.props
    const { startValue, endValue, uid } = this.state
    if (uid <= 0) {
      message.error('uid不能为空且大于0')
      return
    }
    var data = { startDate: moment(startValue).format('YYYY-MM-DD HH:mm:00'), endDate: moment(endValue).format('YYYY-MM-DD HH:mm:00'), uid: uid }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  render () {
    const { route, model: { list } } = this.props
    const { startValue, endValue } = this.state
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <div>
            UID
            <Input onChange={e => this.setState({ uid: e.target.value })} style={{ marginLeft: 10, width: 150 }} /> {/* 搜索按钮 */}
            <span style={{ marginLeft: 10 }}>时间范围</span>
            <DatePicker
              disabledDate={this.disabledStartDate}
              showTime={{ format: 'HH:mm' }}
              format='YYYY-MM-DD HH:mm'
              value={startValue}
              placeholder='开始时间'
              onChange={this.onStartChange}
              style={{ marginLeft: 10 }}
            />
            <span style={{ marginLeft: 10 }}>~</span>
            <DatePicker
              disabledDate={this.disabledEndDate}
              showTime={{ format: 'HH:mm' }}
              format='YYYY-MM-DD HH:mm'
              value={endValue}
              placeholder='结束时间'
              onChange={this.onEndChange}
              style={{ marginLeft: 10 }}
            />
            <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
            <font style={{ marginLeft: 100 }} color='red'>竞猜位置对应的武器 1：喷火枪 2：双刃斧 3：榴弹枪 4：名刀 5：灭世 6：穿云箭 7：电枪 8：碎星锤 9：速机枪</font>
          </div>
          <Divider />
          <Form>
            <Table dataSource={list} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
          </Form>
        </Card>
      </PageHeaderWrapper>
    )
  }
}
export default DragonSlayingBetDetail
