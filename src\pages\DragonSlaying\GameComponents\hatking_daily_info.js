import { But<PERSON>, Card, DatePicker, Divider, Form, Table } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker

@connect(({ dragonSlaying }) => ({ // model 的 namespace
  model1: dragonSlaying // model 的 namespace
}))
class DSDailyInfoComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      list: [],
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
  }

  // 日期 单压中奖金额  单压人数  双压中奖金额  双压人数  一列中奖金额  一列人数
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '单压中奖金额', dataIndex: 'bet_amethyst1', align: 'center' },
    { title: '单压人数', dataIndex: 'bet_user1', align: 'center' },
    { title: '双压中奖金额', dataIndex: 'bet_amethyst2', align: 'center' },
    { title: '双压人数', dataIndex: 'bet_user2', align: 'center' },
    { title: '一列中奖金额', dataIndex: 'bet_amethyst3', align: 'center' },
    { title: '一列人数', dataIndex: 'bet_user3', align: 'center' }
  ]

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getDailyInfoList`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { dailyList } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form>
          <span style={{ marginLeft: 10 }}>时间范围:</span>
          <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Divider />
          <Table dataSource={dailyList} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default DSDailyInfoComponent
