import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import { connect } from 'dva'
// import InteractChannelSummary from './components/interact_channel_monitor'
// import InteractCompereSummary from './components/interact_compere_monitor'
import AllChannelTypeSummary from './components/all_channel_type_monitor'

const namespace = 'compereSummary' // model 的 namespace
const TabPane = Tabs.TabPane

@connect(({ compereSummary }) => ({ // model 的 namespace
  model: compereSummary // model 的 namespace
}))
class Index extends Component { // 默认页面组件，不需要修改
  /** *****************************非活动流程配置文件更新与获取****************************************************************/
  tabOnChange = type => activityKey => {
    if (type !== undefined || type != null) {
      activityKey = type
    }
  }

  /** *******************************页面布局*************************************************************/
  render () {
    const { route } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs onChange={this.tabOnChange()} type='card'>
          <TabPane tab='流水监控' key='3'>
            <AllChannelTypeSummary modelName={namespace} model={this.props.model} />
          </TabPane>

        </Tabs>

      </PageHeaderWrapper>
    )
  }
}

export default Index // 保证唯一
