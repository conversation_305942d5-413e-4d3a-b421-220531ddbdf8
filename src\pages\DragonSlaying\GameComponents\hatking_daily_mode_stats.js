import { Button, Card, DatePicker, Divider, Form, Table } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
// const chanMap = { all: 'ALL', pc: 'PC端', android: '手机交友', xiaomi: '小米渠道', zhuiwan: '追玩渠道', zhuikan: '追看渠道' }

@connect(({ dragonSlaying }) => ({ // model 的 namespace
  model1: dragonSlaying // model 的 namespace
}))
class DSDailyModeStatsComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      list: [],
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
  }

  // 日期 渠道类型 单压流水 单压中奖金额 单压返奖比 双压流水 双压中奖金额 双压返奖比 一列流水 一列中奖金额 一列返奖比
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    // { title: '渠道类型', dataIndex: 'platform', align: 'center', render: (text, record, index) => { return chanMap[text] }, filters: [{ text: 'ALL', value: 'all' }, { text: 'PC端', value: 'pc' }, { text: '手机交友', value: 'android' }, { text: '小米渠道', value: 'xiaomi' }, { text: '追玩渠道', value: 'zhuiwan' }, { text: '追看渠道', value: 'zhuikan' }], onFilter: (value, record) => record.platform.includes(value) },
    { title: '单压流水', dataIndex: 'betAmethyst1', align: 'center' },
    { title: '单压中奖金额', dataIndex: 'betRewardAmethyst1', align: 'center' },
    { title: '单压返奖比', dataIndex: 'betRebateRatio1', align: 'center' },
    { title: '双压流水', dataIndex: 'betAmethyst2', align: 'center' },
    { title: '双压中奖金额', dataIndex: 'betRewardAmethyst2', align: 'center' },
    { title: '双压返奖比', dataIndex: 'betRebateRatio2', align: 'center' },
    { title: '一列流水', dataIndex: 'betAmethyst3', align: 'center' },
    { title: '一列中奖金额', dataIndex: 'betRewardAmethyst3', align: 'center' },
    { title: '一列返奖比', dataIndex: 'betRebateRatio3', align: 'center' }
  ]

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    console.log(data)
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getModeStatsList`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { modeStatsList } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form>
          <span style={{ marginLeft: 10 }}>时间范围:</span>
          <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Divider />
          <Table dataSource={modeStatsList} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default DSDailyModeStatsComponent
