import { getLists, exportLists, getTagLists, whiteListAdd, whiteListDel, whiteListUpdate } from './api'
import { message } from 'antd'

export default {
  namespace: 'talentCompere',

  state: {
    list: [],
    tagList: []
  },

  reducers: {
    refreshList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    },

    refreshTagList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        var tmpTagList = []
        payload[i].index = i + 1
        tmpTagList.push({
          label: payload[i].tagName,
          value: payload[i].tagID
        })
      }
      return {
        ...state,
        list: payload,
        tagList: tmpTagList
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { data } } = yield call(getLists, payload)
      yield put({
        type: 'refreshList',
        payload: Array.isArray(data) ? data : []
      })
    },

    * exportList ({ payload }, { call, put }) {
      yield call(exportLists, payload)
    },

    * getTagList ({ payload }, { call, put }) {
      const { data: { data } } = yield call(getTagLists, payload)
      yield put({
        type: 'refreshTagList',
        payload: Array.isArray(data) ? data : []
      })
    },

    * addItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(whiteListAdd, payload)
      if (status === 0) {
        message.success('添加成功')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('添加失败：' + msg)
      }
    },

    * updateItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(whiteListUpdate, payload)
      if (status === 0) {
        message.success('更新成功')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('更新失败：' + msg)
      }
    },

    * removeItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(whiteListDel, payload)
      if (status === 0) {
        message.success('删除成功')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('删除失败：' + msg)
      }
    }
  }
}
