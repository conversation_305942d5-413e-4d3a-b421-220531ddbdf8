import React, { Component } from 'react'
import { But<PERSON>, Col, Divider, Form, Input, message, Modal, Popconfirm, Row, Select, Spin, Table } from 'antd'
import { formatTimestamp, numberYesNoOptions, pushConfirmStatusOptions, visibleOptions } from '../../common'
import { fetchAllRemoteData, formatOptions, parseOptionsFromMultiLineString } from '@/utils/common'
import { connect } from 'dva'
import ExportRemoteData from '@/components/ExportRemoteData'
import { stringify } from 'qs'

const namespace = 'ActivityManage'
const defaultPageSize = 10

@connect(({ ActivityManage }) => ({
  model: ActivityManage
}))

class ActivityGroupManage extends Component { // 默认页面组件，不需要修改
  constructor (props) {
    super(props)
    this.initState(props.activity, props.role, props.fromGroupRole)
    this.searchHandle()
  }

  initState = (act, selectedRole, fromGroupRole) => {
    const prodEnv = window.location.host === 'jyboss.yy.com'
    const compereLink = prodEnv ? 'https://hgame.yy.com/admin/#/anchorcenter/index' : 'https://hgame-test.yy.com/admin/#/anchorcenter/index'
    this.state = {
      messageContent: '',
      messagePointer: 0,
      compereLink: compereLink,
      activity: act,
      roleList: act.roleList,
      role: selectedRole,
      fromGroupRole: fromGroupRole,
      pagination: {
        pageSize: defaultPageSize,
        total: 0,
        current: 1,
        defaultCurrent: 1,
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        onChange: (page, pageSize) => {
          this.pageChange(page, pageSize)
        },
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      }
    }

    const { role, activity } = this.state
    let roleGroupOptions = [{ label: '无分组', value: '' }]
    if (role && role.groupItems) {
      role.groupItems.forEach(v => {
        roleGroupOptions.push({ label: v.id + ':' + v.name, value: v.id })
      })
    }
    this.state.roleGroupOptions = roleGroupOptions
    // 所有分组选项

    let allRoleGroupOptions = [
      { label: '无分组', value: '' }
    ]
    let exists = {}
    if (activity && activity.roleList) {
      activity.roleList.forEach(role => {
        console.log('123', this.state.role, role)
        if (role && role === this.state.role && role.groupItems) {
          role.groupItems.forEach(v => {
            if (!exists[v.id]) {
              allRoleGroupOptions.push({ label: v.id + ':' + v.name, value: v.id })
              exists[v.id] = true
            }
          })
        }
      })
    }
    this.state.allRoleGroupOptions = allRoleGroupOptions

    let allRoleGroupOptionsByRole = {}
    if (activity && activity.roleList) {
      activity.roleList.forEach(role => {
        let exists = {}

        allRoleGroupOptionsByRole[role.id] = []
        allRoleGroupOptionsByRole[role.id].push({ label: '无分组', value: '' })
        console.log('110011', role.id, allRoleGroupOptionsByRole)
        if (role && role.groupItems) {
          role.groupItems.forEach(v => {
            if (!exists[v.id]) {
              allRoleGroupOptionsByRole[role.id].push({ label: v.id + ':' + v.name, value: v.id })
              exists[v.id] = true
            }
          })
        }
      })
    }
    console.log('110011', allRoleGroupOptionsByRole)
    this.state.allRoleGroupOptionsByRole = allRoleGroupOptionsByRole
  }

  onPaste = (e) => {
    const items = e.clipboardData && e.clipboardData.items

    if (items && items.length) {
      // 检索剪切板items
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
          let tmpFile = items[i].getAsFile()
          if (tmpFile) {
            // let filename = new Date().getTime() + '_' + tmpFile.name
            // 更换文件名
            // tmpFile = new window.File([tmpFile], filename, { type: tmpFile.type })
            // 上传
            const { dispatch } = this.props
            const self = this
            dispatch({
              type: `${namespace}/uploadFile`,
              file: tmpFile,
              callback: (imgUrl, status, msg) => {
                if (status !== 0 || !imgUrl) {
                  message.error('图片上传失败:' + msg)
                }

                // 上传成功
                self.insertMessage(imgUrl)
              }
            })
          }
          break
        }
      }
    }
  }

  insertMessage = (msg) => {
    const { messagePointer } = this.state
    const messageContent = this.formRefSCM.getFieldValue('yyMessage') || ''
    const before = messageContent.substring(0, messagePointer)
    const after = messageContent.substring(messagePointer)
    this.formRefSCM.setFieldsValue({ yyMessage: `${before}${msg}${after}` })
    if (this.messageTextarea) {
      this.messageTextarea.focus()
    }
  }

  updatePagination = (page, pageSize, total) => {
    const { pagination } = this.state
    pagination.current = page || 1
    pagination.pageSize = pageSize || defaultPageSize
    if (total !== undefined) {
      pagination.total = total
    }
    this.setState({ pagination: pagination })
    return pagination
  }

  // 分页信息变更
  pageChange = (page, pageSize, total) => {
    let pagination = this.updatePagination(page, pageSize, total)
    let cond = this.formRefQuery ? this.formRefQuery.getFieldsValue() : null
    this.searchHandle(pagination.current, pagination.pageSize, cond)
  }

  // 获取当前查询条件
  getQueryCondition = (page, size, cond, ignoreCond) => {
    const { businessList } = this.props
    const { pagination, activity, fromGroupRole } = this.state
    page = page || pagination.current || 1
    size = size || pagination.pageSize || defaultPageSize
    let pageInfo = {
      pageNo: page,
      pageSize: size
    }

    pageInfo.actID = activity.id

    // 从分组角色进来, 选中默认默认对应业务
    if (fromGroupRole && businessList && businessList.length > 0) {
      pageInfo.businessID = businessList[0].id
    }

    // 默认选中第一个角色
    if (!cond || !cond.roleID) {
      pageInfo.roleID = this.getDefaultQueryRoleID()
    }

    if (ignoreCond) {
      return pageInfo
    }

    if (!cond) {
      cond = this.formRefQuery ? this.formRefQuery.getFieldsValue() : {}
    }

    let query = Object.assign(cond || {}, pageInfo)
    if (query.member && !Array.isArray(query.member)) {
      query.member = query.member.split(/[\s,，；]+/).filter(v => v && v.length > 0).join(',')
    }
    return query
  }

  getDefaultQueryRoleID = () => {
    const { activity, role } = this.state
    if (role) {
      return role.id
    }
    if (activity && activity.roleList && activity.roleList.length > 0) {
      return activity.roleList[0].id
    }
    return ''
  }

  // 处理查询事件
  searchHandle = (page, size, cond) => {
    const { dispatch } = this.props
    let query = this.getQueryCondition(page, size, cond)
    if (!query) {
      return
    }
    let self = this
    dispatch({
      type: `${namespace}/pageListGroupResult`,
      payload: query,
      callback: (data) => {
        self.updatePagination(query.pageNo || 1, query.pageSize || defaultPageSize, data ? data.total : 0)
      }
    })
  }

  onQueryClick = (values) => {
    const { pagination } = this.state
    let size = pagination.pageSize || defaultPageSize
    this.searchHandle(1, size, values)
  }

  getExportColumns = () => {
    const { model: { businessList } } = this.props
    const { activity, allRoleGroupOptionsByRole } = this.state

    return [
      { title: '分组角色', dataIndex: 'roleID', render: v => formatOptions(v, activity ? activity.roleList : [], v, 'id', 'name') },
      { title: '业务', dataIndex: 'businessID', render: v => formatOptions(v, businessList || [], v, 'id', 'name') },
      { title: '成员ID', dataIndex: 'member' },
      { title: '分组ID', dataIndex: 'groupID' },
      { title: '分组名称', dataIndex: 'groupName', render: (v, item) => formatOptions(item.groupID, allRoleGroupOptionsByRole[item.roleID] || [], v).replace(/^.*:/, '') },
      { title: '数据类型', dataIndex: 'visible', render: v => formatOptions(v, visibleOptions) },
      { title: '白名单？', dataIndex: 'whiteList', render: v => formatOptions(v, numberYesNoOptions) },
      { title: '更新时间', dataIndex: 'updateTime', render: (v) => formatTimestamp(v) },
      { title: '操作人', dataIndex: 'operator' }
    ]
  }

  // 规则函数表头
  getColumns = () => {
    const { model: { businessList } } = this.props
    const { activity, role, allRoleGroupOptionsByRole } = this.state
    const needConfirmFlow = role && role.needConfirmFlow === 1
    let columns = [
      { title: '序号', dataIndex: 'index', align: 'left' },
      { title: '分组角色', dataIndex: 'roleID', align: 'left', render: v => formatOptions(v, activity ? activity.roleList : [], v, 'id', 'name') },
      { title: '业务', dataIndex: 'businessID', align: 'left', render: v => formatOptions(v, businessList || [], v, 'id', 'name') },
      { title: '成员ID', dataIndex: 'member', align: 'left' },
      { title: '分组', dataIndex: 'groupID', align: 'left', render: (v, item) => formatOptions(v, allRoleGroupOptionsByRole[item.roleID] || [], v) },
      { title: '数据类型', dataIndex: 'visible', align: 'left', render: v => formatOptions(v, visibleOptions) },
      { title: '白名单？', dataIndex: 'whiteList', align: 'left', render: v => formatOptions(v, numberYesNoOptions) }
    ]
    if (needConfirmFlow) {
      columns.push({
        title: '通知状态',
        dataIndex: 'pushStatus',
        ellipsis: true,
        align: 'left',
        render: (v, rec) => {
          v = v || 0
          if (v === 2) {
            return <span style={{ color: 'red' }}>{'失败：' + rec.pushErrMsg || ''}</span>
          }
          let option = formatOptions(v, pushConfirmStatusOptions)
          if (rec.pushErrMsg && rec.pushErrMsg.length > 0) {
            return option + ':' + rec.pushErrMsg
          }
          return option
        }
      })
      columns.push({ title: '通知时间', dataIndex: 'pushTime', align: 'left', width: 150, render: (v) => v ? formatTimestamp(v) : '' })
    }
    columns.push({ title: '更新时间', dataIndex: 'updateTime', align: 'left', width: 150, render: (v) => formatTimestamp(v) })
    columns.push({ title: '操作人', dataIndex: 'operator', align: 'left' })
    columns.push({
      title: '操作',
      key: 'operation',
      align: 'left',
      render: (text, item) => {
        let now = parseInt(new Date().getTime() / 1000)
        if (activity.endTime <= now) {
          return ''
        }
        return <>
          <span>
            <a size='small' type='primary' onClick={() => this.showEditGroupResultModal(item)}>编辑</a>
          </span>
          <Divider type={'vertical'} />
          <Popconfirm title='确认删除?' onConfirm={() => this.onGroupResultDelete([item.id], 'ids')}><a size='small' type='primary' style={{ color: 'red' }}>删除</a></Popconfirm>
        </>
      }
    })
    return columns
  }

  onGroupResultDelete = (item, type) => {
    const { dispatch } = this.props
    const { activity } = this.state
    let self = this

    let query = { actID: activity.id }
    if (type === 'currentCond') { // 删除当前条件
      query = Object.assign(query, this.formRefQuery.getFieldsValue())
      if (query.member && query.member.length > 0) {
        query.member = query.member.split(/[,，；\s\r\n]+/)
      }
    } else if (type === 'all') { // 删除所有

    } else if (type === 'ids') { // 删除给定ID列表
      if (!item || item.length < 1) {
        message.warn('没有选中要删除的ID')
        return
      }
      query.idList = item
    } else {
      message.warn('删除类型不合法:' + type)
      return
    }

    dispatch({
      type: `${namespace}/removeGroupResult`,
      payload: query,
      callback: () => {
        let q = this.formRefQuery ? this.formRefQuery.getFieldsValue() : null
        if (q) {
          delete q.visible // 删除该属性，后台如果有这个的话，会实时查询分组信息，有可能删除后又被实时查询到了
        }
        self.onQueryClick(q)
      }
    })
  }

  showImportGroupResult = () => {
    this.setState({ importGroupResultVisible: true })
  }

  onImportGroupResultFinish = (values) => {
    values.members = parseOptionsFromMultiLineString(values.memberGroup, 'member', 'group', /[,，\s]+/)

    const { dispatch, activity } = this.props

    this.setState({ loadingVisible: true })
    values.actID = activity.id
    let self = this
    let upsert = () => {
      dispatch({
        type: `${namespace}/upsertGroupResult`,
        payload: values,
        callback: () => {
          self.searchHandle()
          self.setState({ importGroupResultVisible: false })
          window.setTimeout(() => {
            self.setState({ loadingVisible: false })
          }, 1000)
        }
      })
    }

    if (values.roleID !== 'CHANNEL') {
      upsert()
      return
    }
    // 如果是 CHANNEL 分组，检查频道是否正确
    let channelList = []
    values.members.forEach(item => {
      let arr = item.member.split(/\D+/)
      let sid = parseInt(arr[0])
      let ssid = parseInt(arr[1])
      channelList.push({ sid: sid, ssid: ssid })
    })

    // 检查
    fetchAllRemoteData({
      pageSize: 1000,
      showLoading: () => { },
      closeLoading: () => { },
      uriBuilder: (page, pageSize) => {
        let startIndex = (page - 1) * pageSize
        let endIndex = startIndex + pageSize
        if (endIndex > channelList.length) {
          endIndex = channelList.length
        }
        let subList = []
        for (let i = startIndex; i < endIndex; i++) {
          subList.push(channelList[i])
        }

        let body = JSON.stringify({ channelList: subList })
        return {
          fetchURI: `/assist_tools/webdb/batch_get_sub_channel_info`,
          fetchBody: body
        }
      },
      method: 'POST',
      callback: (dataList) => {
        // 此处校验一下频道正确性
        console.log('查询频道列表：', dataList)
        let invalidChannelMap = {}
        dataList.forEach(item => {
          if (item['template_id'] === -1) {
            invalidChannelMap[item.sid + '_' + item.ssid] = true
          }
        })
        console.log(invalidChannelMap)
        for (let i = 0; i < values.members.length; i++) {
          if (invalidChannelMap[values.members[i].member]) {
            self.setState({ loadingVisible: false })
            message.warn('第[' + (i + 1) + '] 个频道不合法: [' + values.members[i].member + ']', 2)
            return
          }
        }
        // 全部验证通过了
        upsert()
      }
    })
  }

  renderImportGroupResult = () => {
    const { activity, businessList } = this.props
    const { importGroupResultVisible, roleGroupOptions } = this.state
    return <Modal width={600} title={'导入分组'} visible={importGroupResultVisible}
      onCancel={() => this.setState({ importGroupResultVisible: false })}
      onOk={() => this.formRefImport.submit()}>
      <Form ref={form => {
        if (!this.formRefImport) {
          let query = this.formRefQuery.getFieldsValue()
          form.setFieldsValue({ roleID: query.roleID, businessID: query.businessID })
        }
        this.formRefImport = form
      }} labelCol={{ span: 6 }} onFinish={this.onImportGroupResultFinish}>

        <Form.Item name={'businessID'} label={'业务'} required rules={[{ required: true, min: 1, message: '业务必选' }]}>
          <Select disabled placeholder='请选择' options={(businessList || []).map(v => {
            return { label: v.id + ':' + v.name, value: v.id }
          })} filterOption allowClear />
        </Form.Item>

        <Form.Item name={'roleID'} label={'分组角色'} required rules={[{ required: true, min: 1, message: '角色必选' }]}>
          <Select disabled placeholder='请选择' options={(activity ? activity.roleList || [] : []).map(v => {
            return { label: v.name, value: v.id }
          })} filterOption />
        </Form.Item>

        <Form.Item name={'visible'} label={'数据类型'} required rules={[{ required: true, min: 1, message: '数据类型必选' }]}>
          <Select placeholder='请选择' options={visibleOptions} filterOption allowClear />
        </Form.Item>

        <Form.Item name={'memberGroup'} label={'成员ID,分组ID'} required
          tooltip={{
            placement: 'left',
            title: <div><span>格式：【成员ID,分组ID】，多个的话，换行</span>{roleGroupOptions.map(v => {
              return <span key={v.value}><br /> {v.label}</span>
            })}</div>
          }}
          rules={[
            {
              validator: (_, value) => {
                let opts = parseOptionsFromMultiLineString(value, 'member', 'group', /[,，\s]+/)
                if (opts === false || opts.length < 1) {
                  return Promise.reject(new Error('成员ID:分组ID 不能为空，格式为：【成员ID,分组ID】，多个的话换行分隔'))
                }
                // 校验分组
                if (opts.length > 0) {
                  for (let i = 0; i < opts.length; i++) {
                    let group = opts[i].group
                    let invalidGroup = true
                    for (let j = 0; j < roleGroupOptions.length; j++) {
                      if (roleGroupOptions[j].value === group) {
                        invalidGroup = false
                        break
                      }
                    }
                    if (invalidGroup) {
                      return Promise.reject(new Error('[' + opts[i].member + ' ' + opts[i].group + '] 分组ID未配置，请先配置分组'))
                    }
                  }
                }
                return Promise.resolve()
              }
            }
          ]}>
          <Input.TextArea placeholder='成员ID,分组ID' rows={5} allowClear />
        </Form.Item>
      </Form>
    </Modal>
  }

  showEditGroupResultModal = (item) => {
    if (this.formRefEdit) {
      this.formRefEdit.setFieldsValue(item)
    }
    this.setState({ editGroupResult: item, editGroupResultModalVisible: true, editSelectRole: item.roleID })
  }

  onEditGroupResultFinish = (values) => {
    const { activity, dispatch } = this.props
    const { editGroupResult } = this.state
    let updateInfo = Object.assign(editGroupResult, values)
    updateInfo.actID = activity.id
    values.actID = activity.id
    let self = this
    dispatch({
      type: `${namespace}/updateGroupResult`,
      payload: updateInfo,
      callback: () => {
        self.setState({ editGroupResultModalVisible: false })
      }
    })
  }

  renderEditGroupResultModal = () => {
    const { businessList, activity } = this.props
    const { editGroupResultModalVisible, editGroupResult, allRoleGroupOptionsByRole, editSelectRole } = this.state
    console.log(activity.roleList, allRoleGroupOptionsByRole, editSelectRole)
    return <Modal title={'编辑分组'} visible={editGroupResultModalVisible} onCancel={() => this.setState({ editGroupResultModalVisible: false })} onOk={() => this.formRefEdit.submit()}>
      <Form labelCol={{ span: 4 }} ref={form => {
        if (!this.formRefEdit) {
          form.setFieldsValue(editGroupResult)
        }
        this.formRefEdit = form
      }} onFinish={this.onEditGroupResultFinish}>
        <Form.Item name={'roleID'} label={'分组角色'} required rules={[{ required: true, min: 1, message: '角色必选' }]}>
          <Select placeholder='请选择' onChange={value => this.setState({ editSelectRole: value })} options={(activity ? activity.roleList || [] : []).map(v => {
            return { label: v.name, value: v.id }
          })} filterOption />
        </Form.Item>

        <Form.Item name={'businessID'} label={'业务'} required rules={[{ required: true, min: 1, message: '业务必选' }]}>
          <Select placeholder='请选择' options={(businessList || []).map(v => {
            return { label: v.id + ':' + v.name, value: v.id }
          })} filterOption />
        </Form.Item>

        <Form.Item name={'visible'} label={'数据类型'} required rules={[{ required: true, min: 1, message: '数据类型必选' }]}>
          <Select placeholder='请选择' options={visibleOptions} filterOption />
        </Form.Item>

        <Form.Item name={'member'} label={'成员ID'} rules={[{ required: true, min: 1, message: '成员ID必填' }]}>
          <Input placeholder='member' />
        </Form.Item>

        <Form.Item name={'groupID'} label={'分组'} required>
          <Select placeholder='请选择' options={allRoleGroupOptionsByRole[editSelectRole] || []} filterOption />
        </Form.Item>
      </Form>
    </Modal>
  }

  onSelectGroupResultChange = (selectedGroupResultIds, selectedGroupResultRows) => {
    this.setState({ selectedGroupResultIds, selectedGroupResultRows })
  }

  showSendConfirmMessageModal = () => {
    const { activity } = this.props
    const { compereLink } = this.state

    const curYear = formatTimestamp(new Date().getTime() / 1000, 7)
    const startYear = formatTimestamp(activity.startTime, 7)
    const endYear = formatTimestamp(activity.endTime, 7)

    const actName = activity.name
    let startTime = formatTimestamp(activity.startTime, 3)
    let endTime = formatTimestamp(activity.endTime, 3)
    let confirmTime = formatTimestamp(activity.startTime - 86400, 2) + '24点'

    // 相同情况下才需要替换
    if (curYear === startYear && startYear === endYear) {
      startTime = startTime.replace(curYear, '')
      endTime = endTime.replace(curYear, '')
      confirmTime = confirmTime.replace(curYear, '')
    }

    let message = `${actName}将于${startTime} ~ ${endTime}开启，请于${confirmTime}前登陆【交友主持中心】查看性别、音视频信息是否有误，以免影响后续参加活动。如有问题，请及时提交修改申请，审批通过后生效。\n\n交友主持中心地址：${compereLink}`
    if (this.formRefSCM) {
      this.formRefSCM.setFieldsValue({
        hgameMessage: message, yyMessage: message, senderUID: 1101265322
      })
    }
    this.setState({ sendConfirmMsgVisible: true })
  }

  onSendConfirmMessage = (values) => {
    const { activity, dispatch } = this.props

    let query = { actID: activity.id }
    query = Object.assign(query, this.formRefQuery.getFieldsValue())
    if (query.member && query.member.length > 0) {
      query.member = query.member.split(/[,，；\s\r\n]+/)
    }
    if (!query.member) {
      delete query.member
    }

    let payload = Object.assign(values, { condition: query })
    payload.senderUID = parseInt(payload.senderUID + '')
    // 使用 [dyimg]{imgUrl}[/dyimg] 将图片包起来，那么就可以在 PC端展示
    const imgRegex = /((http)|(https)):\/\/[\da-f]\.((dx)|(wt))imscreenshot[\da-f]?\.yy\.((yystatic)|(duowan))\.com\/([a-zA-Z\d\-._/]+)*/ig
    payload.yyMessage = payload.yyMessage.replace(imgRegex, function (url) {
      return '[dyimg]' + url + '[/dyimg]'
    })
    dispatch({
      type: `${namespace}/pushConfirmMessage`,
      payload: payload
    })
    this.setState({ sendConfirmMsgVisible: false })
  }

  renderSendConfirmMessageModal = () => {
    const { sendConfirmMsgVisible } = this.state

    return <Modal forceRender width={1000} visible={sendConfirmMsgVisible} onCancel={() => this.setState({ sendConfirmMsgVisible: false })} onOk={() => this.formRefSCM.submit()} title='用户信息确认通知'>
      <Form onFinish={this.onSendConfirmMessage} ref={form => {
        this.formRefSCM = form
      }}>
        <Form.Item name='senderUID' label={'关联80号UID推送'} tooltip={{ placement: 'rightTop', title: '关联80号UID，如：1101265322' }} required rules={[{
          required: true,
          validator: (ruleObject, value) => {
            if (!value || !/^[1-9]\d*$/g.test('' + value)) {
              return Promise.reject(new Error('必须填写数字uid'))
            }
            return Promise.resolve()
          }
        }]}>
          <Input />
        </Form.Item>

        <Form.Item name='hgameMessage' label={'Hgame通知文案'} required rules={[{
          required: true
        }]}>
          <Input.TextArea rows={5} />
        </Form.Item>

        <Form.Item name='yyMessage' label={'YY消息通知文案'} required rules={[{
          required: true
        }]}>
          <Input.TextArea rows={5}
            onPaste={this.onPaste}
            onSelect={e => {
              this.setState({ messagePointer: e.target.selectionStart })
            }} ref={r => {
              this.messageTextarea = r
            }} />
        </Form.Item>
      </Form>
    </Modal>
  }

  getQueryURI = (page, size, ignoreCond) => {
    let query = this.getQueryCondition(page, size, null, ignoreCond)
    return `/group_svr/activity/list_group_result?${stringify(query)}`
  }

  // 渲染函数
  render () {
    const { model: { groupResultList }, activity, businessList } = this.props
    const { role, pagination, roleGroupOptions, allRoleGroupOptions, selectedGroupResultIds, loadingVisible, loadingMsg, fromGroupRole } = this.state
    const columns = this.getColumns()

    const rowSelection = {
      selectedGroupResultIds,
      onChange: this.onSelectGroupResultChange
    }

    // 活动是否结束
    const now = parseInt(new Date().getTime() / 1000)
    const isActEnd = activity && activity.endTime > now
    const isActNotStarted = activity && activity.startTime >= now

    const exportFilename = '活动-' + activity.id + '_' + activity.name + '_分组结果'
    const exportColumns = this.getExportColumns()

    return (
      <div style={{ height: 580 }}>
        <Row style={{ marginBottom: '1em' }}>
          <Form layout={'inline'} ref={form => {
            if (form) {
              if (!this.formRefQuery) {
                if (!form.getFieldValue('roleID')) {
                  form.setFieldsValue({ roleID: this.getDefaultQueryRoleID() })
                  if (businessList && businessList.length === 1) {
                    form.setFieldsValue({ businessID: businessList[0].id })
                  }
                }
              }
              this.formRefQuery = form
            }
          }} onFinish={this.onQueryClick}>

            <Form.Item name={'roleID'} label={'分组角色'}>
              <Select disabled={fromGroupRole} placeholder='请选择' style={{ width: 150 }} options={(activity ? activity.roleList || [] : []).map(v => {
                return { label: v.name, value: v.id }
              })} onChange={(v) => {
                this.formRefQuery.submit()
                if (!v) {
                  this.setState({ roleGroupOptions: allRoleGroupOptions })
                  return
                }
                let roleGroupOptions = [{ label: '无分组', value: '' }]

                let role = this.state.role
                if (activity && activity.roleList) {
                  for (let i = 0; i < activity.roleList.length; ++i) {
                    role = activity.roleList[i]
                    if (role.id === v || !v) {
                      if (role && role.groupItems) {
                        role.groupItems.forEach(v => {
                          roleGroupOptions.push({ label: v.id + ':' + v.name, value: v.id })
                        })
                      }
                      break
                    }
                  }
                }
                this.setState({ roleGroupOptions: roleGroupOptions, role: role })
              }} filterOption allowClear />
            </Form.Item>

            <Form.Item name={'businessID'} label={'业务'}>
              <Select disabled={fromGroupRole} placeholder='请选择' style={{ width: 150 }} options={(businessList || []).map(v => {
                return { label: v.name, value: v.id }
              })} onChange={() => this.formRefQuery.submit()} filterOption allowClear />
            </Form.Item>

            <Form.Item name={'member'} label={'成员ID'}>
              <Input placeholder='member1,member2...' style={{ width: 250 }} allowClear />
            </Form.Item>

            <Form.Item name={'groupID'} label={'分组'}>
              <Select placeholder='请选择' style={{ width: 200 }} options={roleGroupOptions} onChange={() => this.formRefQuery.submit()} filterOption allowClear />
            </Form.Item>

            <Form.Item name={'visible'} label={'数据类型'}>
              <Select placeholder='请选择' style={{ width: 100 }} options={visibleOptions} onChange={() => this.formRefQuery.submit()} filterOption allowClear />
            </Form.Item>

            <Form.Item name={'whiteList'} label={'白名单？'}>
              <Select placeholder='请选择' style={{ width: 80 }} options={numberYesNoOptions} onChange={() => this.formRefQuery.submit()} filterOption allowClear />
            </Form.Item>

            <Form.Item name={'pushStatus'} label={'通知状态'} hidden={!role || role.needConfirmFlow !== 1}>
              <Select placeholder='请选择' style={{ width: 80 }} options={pushConfirmStatusOptions} onChange={() => this.formRefQuery.submit()} filterOption allowClear />
            </Form.Item>

            <Divider type={'horizontal'} />
            <Button size={'small'} type='primary' htmlType='submit'>查询</Button>

            <Divider type={'vertical'} />
            <Button size={'small'} type='primary' onClick={() => {
              this.formRefQuery.resetFields()
              this.formRefQuery.submit()
            }}>重置</Button>

            <div hidden={!isActEnd}>
              <Divider type={'vertical'} />
              <Button disabled={!(fromGroupRole !== undefined && fromGroupRole !== null && fromGroupRole)} size={'small'} type={'primary'} onClick={() => this.showImportGroupResult()}>导入</Button>

              {
                role && role.needConfirmFlow === 1 && isActNotStarted
                  ? <>
                    <Divider type={'vertical'} />
                    <Button size={'small'} type={'primary'} onClick={this.showSendConfirmMessageModal}>一键通知当前条件</Button>
                  </>
                  : ''
              }

              <Divider type={'vertical'} />
              <Popconfirm title='确认删除?' onConfirm={() => {
                const { selectedGroupResultIds } = this.state
                this.onGroupResultDelete(selectedGroupResultIds, 'ids')
              }}><Button danger size={'small'} type={'primary'}>删除选中项</Button></Popconfirm>

              <Divider type={'vertical'} />
              <Popconfirm title='确认删除?' onConfirm={() => this.onGroupResultDelete(null, 'currentCond')}><Button danger size={'small'} type={'primary'}>删除当前条件</Button></Popconfirm>

              <Divider type={'vertical'} />
              <Popconfirm title='确认删除?' onConfirm={() => this.onGroupResultDelete(null, 'all')}><Button danger size={'small'} type={'primary'}>删除所有</Button></Popconfirm>
            </div>

            <Divider type={'vertical'} />
            <ExportRemoteData title={'导出当前条件'} buttonSize={'small'} filename={exportFilename} columns={exportColumns} uriBuilder={(page, size) => this.getQueryURI(page, size, false)} />
            <Divider type={'vertical'} />
            <ExportRemoteData title={'导出所有'} buttonSize={'small'} filename={exportFilename} columns={exportColumns} uriBuilder={(page, size) => this.getQueryURI(page, size, true)} />
          </Form>
        </Row>
        <Row style={{ marginBottom: '1em' }}>
          <Col span={24}>
            <Table columns={columns}
              dataSource={groupResultList}
              size='small'
              pagination={pagination}
              showSorterTooltip={false}
              rowSelection={rowSelection}
              rowKey={record => record.id}
              scroll={{ y: 400 }}
            />
          </Col>
        </Row>

        {this.renderImportGroupResult()}

        {this.renderEditGroupResultModal()}

        {this.renderSendConfirmMessageModal()}

        <Modal visible={loadingVisible} footer={null} closable={false} centered>
          <Spin
            tip={loadingMsg || '正在导入数据，请耐心等待......'} />
        </Modal>
      </div>
    )
  }
}

export default ActivityGroupManage
