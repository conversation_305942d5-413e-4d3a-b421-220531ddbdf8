import React, { Component } from 'react'
import { Card, Button, Modal, Form, Table, Select, InputNumber, Popconfirm, Input, Space } from 'antd'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { connect } from 'dva'
var moment = require('moment')

const namespace = 'offstripMain' // model 的 namespace
const FormItem = Form.Item
const Option = Select.Option

const channelConfig = [
  { value: 1, label: 'sid' },
  { value: 2, label: 'ssid' },
  { value: 3, label: '主持uid' }
]
const channelConfigMap = { 1: 'sid', 2: 'ssid', 3: '主持uid' }

@connect(({ offstripMain }) => ({ // model 的 namespace
  model: offstripMain // model 的 namespace
}))
class OffstripBlacklist extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      category: 1
    }
  }

  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getWarnList`
    })
  }

  // 需要修改
  columns = [
    { title: '序号', dataIndex: 'index', align: 'center' },
    {
      title: '类型',
      dataIndex: 'category',
      align: 'center',
      render: (text, record) => {
        return channelConfigMap[record.category]
      }
    },
    { title: '黑名单', dataIndex: 'value', align: 'center' },
    { title: '描述', dataIndex: 'desc', align: 'center' },
    { title: '限制金额', dataIndex: 'threshold', align: 'center' },
    { title: '操作时间', dataIndex: 'timestamp', align: 'center', render: text => this.dateString(text) },
    { title: '操作人', dataIndex: 'operator', align: 'center' },
    { title: '操作',
      align: 'center',
      render: (text, record) => (
        <span>
          <a><EditOutlined style={{ marginRight: 10 }} onClick={this.showModal(true, record)} /></a>
          <Popconfirm title='Sure to delete?' onConfirm={this.handleDel(record.id)}>
            <a><DeleteOutlined style={{ color: 'red' }} /></a>
          </Popconfirm>
        </span>)
    }
  ]

  pagination = { pageSizeOptions: ['10', '20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  // 显示弹窗，不需要修改
  showModal = (isUpdate, record) => () => {
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue(v)
    }
    this.setState({ visible: true, isUpdate: isUpdate, title: isUpdate ? '更新配置' : '新建配置' })
  }

  dateString (timestamp) {
    if (timestamp === 0) {
      return '-'
    }
    return moment.unix(timestamp).format('YYYY-MM-DD HH:mm:ss')
  }

  // 隐藏弹窗，不需要修改
  hideModal = () => {
    this.setState({ visible: false, category: 0 })
  }

  handleDel = key => e => {
    const { dispatch } = this.props

    const data = { id: key }
    dispatch({
      type: `${namespace}/removeWarn`,
      payload: data
    })
  }

  onFinish = values => {
    const { dispatch } = this.props

    let uri = `${namespace}/upsetWarn`

    console.log(values)
    dispatch({
      type: uri,
      payload: values
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  // 更新与删除
  handleSubmit = () => {
    this.formRef.submit()
  }

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  changeCategory = (value) => () => {
    console.log(value)
    this.setState({ category: value })
    if (this.formRef) {
      this.formRef.resetFields(['value'])
    }
    this.forceUpdate()
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { warnList } } = this.props
    const { visible, title, isUpdate, category } = this.state

    const formItemLayout = {
      labelCol: {
        xs: { span: 8 },
        sm: { span: 8 }
      },
      wrapperCol: {
        xs: { span: 10 },
        sm: { span: 14 }
      }
    }

    return (
      <Card>
        <Space align='center'>
          <Form>
            <Form.Item>
              <Button type='primary' onClick={this.showModal(false, this.defaultValue)}>添加</Button>
            </Form.Item>
          </Form>
          <Form.Item>
            <div>1. 黑名单类型sid、ssid、主持uid，仅支持单选</div>
            <div>2. 限制金额，单位紫水晶，黑名单频道不发放大于限制金额的奖品</div>
          </Form.Item>
        </Space>

        <Table rowKey={(record, index) => index} dataSource={Array.isArray(warnList) ? warnList : []} columns={this.columns} size='small' pagination={this.pagination} />
        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit}>
          <Form onFinish={this.onFinish} ref={form => { this.formRef = form }} {...formItemLayout}>
            <FormItem name='category' label='类型' rules={[{ required: true }]}>
              <Select style={{ width: '100%' }} disabled={isUpdate} onChange={(v) => { this.changeCategory(); this.setState({ category: v }) }}>
                {channelConfig.map((item, index) => (
                  <Option key={item.value} value={item.value}>{item.label}</Option>
                ))}
              </Select>
            </FormItem>
            <FormItem label='黑名单' name='value' rules={[{ required: true }]}>
              <InputNumber placeholder='请输入限制名单' style={{ width: '100%' }} disabled={isUpdate} />
            </FormItem>
            <FormItem label='sid' name='ssidSid' hidden={category !== 2}>
              <InputNumber placeholder='选择ssid类型, 需要输入sid' style={{ width: '100%' }} disabled={isUpdate} />
            </FormItem>
            <FormItem label='限制金额/紫水晶' name='threshold' rules={[{ required: true }]}>
              <InputNumber placeholder='礼物价值大于输入的不发放' style={{ width: '100%' }} />
            </FormItem>
            <div hidden>
              <FormItem name='id'>
                <Input />
              </FormItem>
              <FormItem name='desc'>
                <Input />
              </FormItem>
            </div>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default OffstripBlacklist
