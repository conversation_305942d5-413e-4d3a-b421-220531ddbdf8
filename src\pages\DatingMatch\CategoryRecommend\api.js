import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/dating_match_bosssvr/v3/category_recommend/get_list?${stringify(params)}`)
}

export function add (params) {
  return request(`/dating_match_bosssvr/v3/category_recommend/insert?${stringify(params)}`)
}

export function remove (params) {
  return request(`/dating_match_bosssvr/v3/category_recommend/remove`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: stringify(params)
  })
}

export function update (params) {
  return request(`/dating_match_bosssvr/v3/category_recommend/update?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
