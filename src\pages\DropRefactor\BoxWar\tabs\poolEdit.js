/* eslint-disable eqeqeq */
import React, { Component } from 'react'
import { Card, Divider, Select, Form, message, Button, Row, Col, InputNumber, TimePicker, Typography, Input, Space, Table, Modal } from 'antd'
import { connect } from 'dva'
import moment from 'moment'
import { columns, PoolChangeDesc, hotOptions, broadcastOptionsJY, newNoticeOptions, countSURT, fixDataSource } from '../../DropMain/components/list_common'
import { timeFormater, deepClone, getCookie } from '@/utils/common'
import DiffTable from '@/components/DiffTable'
import { propTypeOptions } from '../../dropCommon'
import { getFieldsValueByPoolID } from '../../globalConfig'
import DynamicCfgButton from '../../DropMain/components/dynamicCfgBtn'
import PrizeSelector from '../../DropMain/components/prizeSelector'
import { BowWarTipDiv } from '../common'
import { screenshotByID } from '../../../../utils/screenShot'

const namespace = 'dropBoxWar'

const defaultPropItem = { id: 1, prizeId: 101, prizeType: 1, propsId: 12, propsName: '爱心', hot: 0, propsType: 0, count: 1, value: 100, rate: 1, dailyLimit: -1, hoursLimit: -1, dailyCount: 0, valueLimit: 0, timeStart: 0, timeStop: 0, broadCastType: 0 }
const defaultEditing = { timestamp: 0, hashCode: '', status: 0, operator: 0, passport: '', aprPassport: '', aprRemark: '', aprTimestamp: 0, aprId: 0, list: [] }

@connect(({ dropBoxWar }) => ({
  model: dropBoxWar
}))

class PoolEditor extends Component {
  state = {
    pid: 20000,
    visible: false,
    prizeSelectorVisible: false,
    editing: false,
    approvalVisible: false,
    approvalRemark: '',
    updateRemark: '',
    screenShot: '',
    updateComfirmVisible: false,
    poolChangeDataSource: { rta: 0, rtb: 0, smb: 0, sma: 0 },
    dataSourceUpdateCounter: 0,
    spinning: false
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  /** ************************************组件初始化**************************************************/

  componentDidMount () {
    this.getEditingPoolList()
  }

  // 获取道具池配置
  getEditingPoolList = (id = null) => {
    const { pid } = this.state
    const { dataSourceUpdateCounter } = this.state

    this.callModel('getPoolConfigByID', {
      params: { id: pid },
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg, list } = ret
        if (status !== 0) {
          message.error('获取道具池失败,请稍后再试:' + msg)
          return
        }

        this.parsePoolConfig(list, pid)
        this.updateChangeDesc()
        this.setState({ dataSourceUpdateCounter: dataSourceUpdateCounter + 1 })
      }
    })
  }

  // 解析道具池配置
  parsePoolConfig = (cfg, pid) => {
    let content = cfg.temporary
    let templist = []
    let prodList = []
    try {
      prodList = JSON.parse(cfg.content) // 正式奖励列表
      templist = JSON.parse(cfg.temporary.content) // 暂存奖励列表
    } catch (e) {
      message.error('json convert error ' + e)
      console.error('cfg=', cfg)
    }
    delete content['content']
    prodList.forEach((item, index, ary) => { if (item.limitSetting) { item.limitSetting.poolID = pid } })
    templist.forEach((item, index, ary) => { if (item.limitSetting) { item.limitSetting.poolID = pid } })
    content.list = templist

    this.changeState('poolConfig', cfg)
    this.changeState('editingConfig', content)
    this.changeState('prodList', prodList)
  }

  // 获取待审批信息
  getApprovalInfo = (pid) => {
    if (pid === '') {
      message.warn('未创建审批流')
      return
    }
    this.callModel('getToApproval', {
      params: { pid: pid },
      cbFunc: (ret) => {
        const { status, msg, list } = ret
        if (status !== 0) {
          message.error('获取审批状态失败:' + msg)
          return
        }
        if (!Array.isArray(list)) {
          message.warn('当前非待审批状态~')
          return
        }
        let info = list[0]
        if (info == undefined || info.rule == undefined || info.rule.approvals == undefined) {
          message.warn('审批流信息异常，请稍后再试..')
          return
        }
        let uid = getCookie('yyuid')
        const approvals = info.rule.approvals
        for (let i = 0; i < approvals.length; i++) {
          if (approvals[i].uid == uid) {
            this.setState({ approvalVisible: true })
            return
          }
        }
        message.warn('非指定审批人')
      } })
  }

  // 道具池审批
  doApproval = (pass) => {
    const { toApproval } = this.props.model
    const { approvalRemark } = this.state
    let req = toApproval[0]
    if (req == undefined || req.rule == undefined || req.rule.approvals == undefined) {
      message.warn('审批流信息异常，请稍后再试..')
      return
    }
    req.reason = approvalRemark || '无'
    if (pass) {
      req.result = 'Passed'
    } else {
      req.result = 'Rejected'
    }
    this.callModel('doApproval', {
      params: req,
      isDetailMode: true,
      isJsonMode: true,
      useJsonHeader: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('审批失败,请联系管理员:' + msg)
          return
        }
        message.success('审批完成')

        this.setState({ approvalVisible: false })
        this.getEditingPoolList()
      }
    })
  }

  /** ************************************完成道具池编辑，提交到服务器************************************************ */

  // 检查配置合法性然后弹出提交审批确认框
  beforeSubmitEditing = () => {
    let { editingConfig } = this.props.model
    if (!this.checkSubmitData(editingConfig)) {
      return
    }
    this.updateChangeDesc()

    this.setState({ editing: false })
    setTimeout(() => {
      screenshotByID('poolTable', (isOk, url) => {
        if (!isOk) {
          message.warn('截图失败~')
        }
        console.debug('url===>', url)
        this.setState({ updateComfirmVisible: true, screenShot: url })
      })
    }, 200)
  }

  // 检查道具池配置合法性, 返回false表示不合法
  checkSubmitData = (editingConfig) => {
    console.debug('editingConfig===>', editingConfig)
    const { list } = editingConfig
    if (!list || list.length === 0) {
      message.warn('配置有误，请检查～')
      return false
    }
    return true
  }

  // 提交道具池配置更新
  onSubmit = () => {
    const { updateRemark, poolChangeDataSource, screenShot } = this.state
    const { editingConfig, poolConfig } = this.props.model

    // 数据检验
    if (!this.checkSubmitData(editingConfig)) {
      return
    }
    editingConfig.content = JSON.stringify(editingConfig.list)
    editingConfig.remark = updateRemark || '空'
    delete editingConfig['list']
    editingConfig.changeInfo = poolChangeDataSource
    editingConfig.screenShot = screenShot
    poolConfig.temporary = editingConfig

    this.callModel('editPoolConfig', {
      params: poolConfig,
      isJsonMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('发生错误，请联系管理员:' + msg)
          return
        }
        message.success('已提交审批')
        this.setState({ editing: false, updateComfirmVisible: false })
        this.getEditingPoolList()
      }
    })
  }

  // 放弃更新
  onReset = () => {
    this.getEditingPoolList()
    this.editChange()
  }

  /** **********************************切换道具池******************************************************************/

  editChange = () => {
    const { model: { poolConfig } } = this.props
    if (poolConfig === undefined || poolConfig.id <= 0) {
      message.error('请选择道具池')
      return
    }
    const { editing } = this.state
    this.setState({ editing: !editing })
  }

  // 根据道具池ID获取appID
  getAppIDByPoolID = (pid) => {
    return getFieldsValueByPoolID(pid, 'AppID')
  }

  /** **********************************list 增删改查******************************************************************/

  addItem = () => {
    let v = $.extend({}, true, defaultPropItem)
    // 自动生成奖励道具ID，递增
    const { poolConfig } = this.props.model
    let base = 1000
    if (poolConfig !== undefined && poolConfig.id !== undefined) {
      base = poolConfig.id
    }
    v.prizeId = base * 100 + v.prizeId

    // 奖励道具ID 与 编号 自增
    let { editingConfig } = this.props.model

    if (Array.isArray(editingConfig.list)) {
      editingConfig.list.forEach(item => {
        if (item.id >= v.id) {
          v.id = item.id + 1
        }
        if (item.prizeId >= v.prizeId) {
          v.prizeId = item.prizeId + 1
        }
      })
    }
    // 覆盖旧数据
    editingConfig.list.push(v)
    this.changeState('editingConfig', editingConfig)
    this.updateChangeDesc()
  }

  removeItem = id => () => {
    let { editingConfig } = this.props.model
    let cl = []
    editingConfig.list.forEach(item => {
      if (item.id !== id) {
        cl.push(item)
      }
    })
    editingConfig.list = cl
    this.changeState('editingConfig', editingConfig)
    this.updateChangeDesc()
  }

  /** **********************************table 渲染******************************************************************/

  onInputChange = (row, field) => value => {
    let { editingConfig } = this.props.model
    let v = $.extend([], true, editingConfig.list)
    v[row][field] = value
    editingConfig.list = v
    this.changeState('editingConfig', editingConfig)
    this.updateChangeDesc()
  }

  // 时间更新
  onTimeChange = (row, field) => value => {
    let { editingConfig } = this.props.model
    let v = $.extend([], true, editingConfig.list)
    v[row][field] = value ? value.unix() % 86400 : 0
    editingConfig.list = v
    this.changeState('editingConfig', editingConfig)
  }

  // 行选择
  onBtnClick = row => () => {
    this.setState({ row, prizeSelectorVisible: true })
  }

  onPropChangeNew = (prizeInfo) => {
    const { row } = this.state
    const { appId, id, name, url, price, prizeType } = prizeInfo
    let { editingConfig } = this.props.model
    if (row === undefined) {
      message.error('unfined row' + row)
      return
    }

    let v = $.extend([], true, editingConfig.list)
    v[row].appId = appId // appId
    v[row].propsId = id // 礼物ID
    v[row].propsName = name // 礼物名称
    v[row].propsUrl = url // 礼物图片
    v[row].value = price // 礼物加个
    v[row].prizeType = prizeType // 礼物渠道
    editingConfig.list = v
    this.changeState('editingConfig', editingConfig)
    this.setState({ prizeSelectorVisible: false })
    this.updateChangeDesc()
  }

  newColumns = () => {
    let cp = deepClone(columns)
    return cp.filter(item => { return [].indexOf(item.dataIndex) == -1 })
  }

  renderEditColumn = () => {
    const { editing } = this.state
    let cp = this.newColumns()
    if (!editing) {
      return cp
    }
    let renderColumns = []
    for (let i = 0; i < cp.length; i++) {
      let column = cp[i]
      if (['count', 'rate', 'dailyCount', 'valueLimit', 'extA', 'hoursLimit', 'dailyLimit'].indexOf(column.dataIndex) > -1) {
        column.render = (text, record, index) => {
          return <InputNumber onChange={this.onInputChange(index, column.dataIndex)} defaultValue={text} />
        }
      }

      if (['hot', 'broadCastType', 'propsType', 'newNotice'].indexOf(column.dataIndex) > -1) {
        column.render = (text, record, index) => {
          let options = [hotOptions, broadcastOptionsJY, propTypeOptions, newNoticeOptions][['hot', 'broadCastType', 'propsType', 'newNotice'].indexOf(column.dataIndex)]
          return <Select onChange={this.onInputChange(index, column.dataIndex)} options={options} defaultValue={text} />
        }
      }

      if (['timeStart', 'timeStop'].indexOf(column.dataIndex) > -1) {
        column.render = (text, record, index) => {
          return <TimePicker onChange={this.onTimeChange(index, column.dataIndex)} defaultValue={moment.unix(text)} />
        }
      }

      if (column.dataIndex === 'propsId') {
        column.render = (text, record, index) => {
          return <Button onClick={this.onBtnClick(index, column.dataIndex)} type='link'>{text}</Button>
        }
      }

      if (column.dataIndex === 'limitSetting') {
        column.render = (value, r, i) => {
          return <DynamicCfgButton value={value} record={r} isEdit globalModeOnly cmpValue={r.cmpLimitSetting} limit={r.dailyLimit} id={r.id} propsName={r.propsName}
            onChange={(v) => {
              let { editingConfig } = this.props.model
              let cp = [...editingConfig.list]
              cp[i].limitSetting = v
              editingConfig.list = cp
              this.changeState('editingConfig', editingConfig)
            }} />
        }
      }

      renderColumns.push(column)
    }

    renderColumns.push({
      title: '操作',
      align: 'center',
      render: (text, record) => (
        <div>
          <Button onClick={this.removeItem(record.id)} type='link' danger>删除</Button>
        </div>
      )
    })

    return renderColumns
  }

  renderDiffColumn = () => {
    // 奖励道具名称、数量、概率、投放上限/D、投放上限/2H 展示变化，其他不展示
    let cp = this.newColumns()
    const newColumns = cp.map(item => {
      if (['propsName', 'count', 'rate', 'hoursLimit', 'dailyLimit'].indexOf(item.dataIndex) < 0) { // 非指定的字段不显示对比
        item.dft_hidden = true
      }
      if (item.dataIndex === 'limitSetting') {
        item.render = (v, r) => { return <DynamicCfgButton key={v} value={v} diffMode cmpValue={r.cmpLimitSetting} limit={r.dailyLimit} propsName={r.propsName} isEdit={false} /> }
      }
      return item
    })

    return newColumns
  }

  /** ********************************** 统计数值变化 ******************************************************************/

  updateChangeDesc = () => {
    const { editingConfig, prodList } = this.props.model
    this.refreshPoolChangeDesc(prodList, editingConfig.list)
  }

  // 更新 PoolChangeDesc 显示的数值
  refreshPoolChangeDesc = (dataSourceb, dataSourcea) => {
    const { pid } = this.state
    const resultb = countSURT(dataSourceb, pid, 10)
    const resulta = countSURT(dataSourcea, pid, 10)
    const newDataSource = { rtb: resultb.rt, smb: resultb.sm, rta: resulta.rt, sma: resulta.sm }
    this.setState({ poolChangeDataSource: newDataSource })
  }

  /* *******************************页面布局***************************************************************/

  render () {
    const { editing, pid, approvalVisible, updateComfirmVisible, poolChangeDataSource, dataSourceUpdateCounter, prizeSelectorVisible } = this.state
    const { editingConfig, prodList, globalPrizeList, editingConfig: { list } } = this.props.model
    const { timestamp, passport, status, aprId, aprUid, aprPassport, aprTimestamp, remark, aprRemark } = editingConfig || defaultEditing
    // const { operator, aprRemark } = editingConfig || defaultEditing
    const { Text } = Typography
    const fixList = fixDataSource(prodList, list)
    const { listA, listB } = fixList // listA=当前待审批配置, listB=正式生效配置
    return (
      <Card>
        <Row gutter={8} style={{ marginBottom: '1em' }}>
          <Col span={6} hidden={!editing}>
            <Form.Item>
              <Button style={{ marginRight: 20 }} onClick={() => this.addItem()}>新增奖励道具</Button>
              <Button onClick={this.onReset} style={{ marginRight: 20 }}>放弃修改</Button>
              <Button type='dash' danger onClick={() => { this.beforeSubmitEditing() }} >提交修改</Button>
            </Form.Item>
          </Col>
          <Col span={2} hidden={editing}>
            <Button type='primary' onClick={this.editChange}>编辑道具池</Button>
          </Col>
          <Col span={2} hidden={editing}>
            <Button type='primary' disabled={aprId === '' || status != 1} onClick={() => this.getApprovalInfo(aprId)}>审批</Button>
          </Col>
          <Col>
            <PoolChangeDesc {...poolChangeDataSource} isEdit multi={10} />
          </Col>
        </Row>

        <Row>
          <Col>
            <Row>提交：<Text type='secondary'> {`${passport}_(${timeFormater(timestamp)})`} <Divider type='vertical' /> </Text></Row>
            <Row hidden={status === 1}>审批：<Text type='secondary'> {aprUid === 0 ? '系统' : aprPassport}_({timeFormater(aprTimestamp)}) <Divider type='vertical' /></Text></Row>
          </Col>
          <Col>
            <Row>申请理由：<Text type='secondary'> { remark || '(空)'} <Divider type='vertical' /></Text></Row>
            <Row hidden={status === 1}>审批备注：<Text type='secondary'> { aprRemark || '(空)'} <Divider type='vertical' /></Text></Row>
          </Col>
          <Col>
            <Row>审批状态：<Text type={['warning', 'warning', 'success', 'danger'][status]}> { ['未创建', '待审批', '已通过', '不通过'][status]} <Divider type='vertical' /></Text></Row>
          </Col>
        </Row>

        <Row>
          <BowWarTipDiv />
        </Row>
        <Divider />

        <div id='poolTable'>
          {
            editing
              ? <Table key={list} pagination={false} size='small' rowKey='id' scroll={{ x: 'max-content' }} columns={this.renderEditColumn()} dataSource={list} />
              : <DiffTable key={dataSourceUpdateCounter} oldProps={{ pagination: false, size: 'small' }} columns={this.renderDiffColumn()} after={listA} before={listB} />
          }
        </div>

        <PrizeSelector
          type='modal'
          appIDLimit={this.getAppIDByPoolID(pid)}
          visible={prizeSelectorVisible} prizeList={globalPrizeList}
          onCancel={() => { this.setState({ prizeSelectorVisible: false }) }}
          onComfirm={(v) => { this.onPropChangeNew(v) }}
        />

        {/* 提交修改确认模态框 */}
        <Modal visible={updateComfirmVisible} title='确认提交审批么？' footer={null} onCancel={() => { this.setState({ updateComfirmVisible: false, editing: true }) }}>
          <Row>
            <Text style={{ fontSize: '1.2em' }}>非设限部分发放占比: {(poolChangeDataSource.rtb / 100)}%➞<Text type='danger'>{(poolChangeDataSource.rta / 100)}%</Text></Text>
          </Row>
          <Row>
            <Text style={{ fontSize: '1.2em' }}>设限部分总金额(元): {poolChangeDataSource.smb / 100}➞<Text type='danger'>{poolChangeDataSource.sma / 100}</Text></Text>
          </Row>
          <Row style={{ marginBottom: '1em' }}>
            <Input placeholder='请输入备注信息 (选填)' onChange={(e) => { this.setState({ updateRemark: e.target.value }) }} />
          </Row>
          <Space>
            <Button onClick={() => { this.setState({ updateComfirmVisible: false, editing: true }) }}>再看看</Button>
            <Button danger type='primary' onClick={() => { this.onSubmit() }}>提交审批</Button>
          </Space>
        </Modal>

        {/* 审批模态框 */}
        <Modal visible={approvalVisible} title='道具池配置审批' footer={null}
          onCancel={() => { this.setState({ approvalVisible: false }) }}>
          <Row style={{ marginBottom: '1em' }}>
            <Input placeholder='请输入备注信息 (通过或驳回的原因,选填)' onChange={(e) => { this.setState({ approvalRemark: e.target.value }) }} />
          </Row>
          <Space>
            <Button onClick={() => { this.setState({ approvalVisible: false }) }}>取消</Button>
            <Button danger type='primary' onClick={() => { this.doApproval(false) }}>驳回</Button>
            <Button type='primary' onClick={() => { this.doApproval(true) }}>通过</Button>
          </Space>
        </Modal>

      </Card>
    )
  }
}

export default PoolEditor
