import React, { Component } from 'react'
import { connect } from 'dva'
import { Space, Modal, Card, Select, Table, Divider, Form, Input, Button, DatePicker, message, Popconfirm } from 'antd'
import TinymceEditer from '@/components/TinymceEditer'
import { timeFormater } from '@/utils/common'
import './style.css'
import moment from 'moment/moment'

const namespace = 'yoBackAnnouncement'

// const tagIdList = [{ idx: 1, name: '首页' }, { idx: 2, name: '运营规则' }, { idx: 3, name: '功能玩法' }]
const tagIdList = [{ idx: 2, name: '运营规则' }, { idx: 3, name: '功能玩法' }]
const childIdList = {
  0: [],
  1: [],
  2: [{ idx: 201, name: '平台规则' }, { idx: 202, name: '流程规范' }, { idx: 203, name: '扶持政策' }, { idx: 204, name: '推荐申请' }, { idx: 205, name: '主持违规处罚' }, { idx: 206, name: '其他违规处罚' }],
  3: [{ idx: 301, name: '新功能及玩法' }]
}

const validOpts = [{ idx: 1, name: '是' }, { idx: 2, name: '否' }]
const topOpts = [{ idx: 1, name: '是' }, { idx: 2, name: '否' }]
const showNewOpts = [{ idx: 1, name: '是' }, { idx: 2, name: '否' }]

function isValid (validBegin, validEnd) {
  const now = Math.floor(new Date().getTime() / 1000)
  return now >= validBegin && now < validEnd
}

function findNavName (nav, navList) {
  let v = navList.find(e => e.idx === nav)
  if (v) {
    return v.name
  }
  return ''
}

@connect(({ yoBackAnnouncement }) => ({
  model: yoBackAnnouncement
}))

class YoBackAnnouncement extends Component {
  columns = [
    { title: '序号', dataIndex: 'idx', align: 'center' },
    { title: '标题', dataIndex: 'title', align: 'center' },
    { title: '生效时间', dataIndex: 'validBegin', align: 'center', render: (text, record) => (timeFormater(record.validBegin)) },
    { title: '失效时间', dataIndex: 'validEnd', align: 'center', render: (text, record) => (timeFormater(record.validEnd)) },
    { title: '是否生效', dataIndex: 'isValid', align: 'center', render: (text, record) => (isValid(record.validBegin, record.validEnd) ? <font color={'green'}>是</font> : <font color={'red'}>否</font>) },
    { title: '是否展示new标签', dataIndex: 'showNew', align: 'center', render: (text, record) => (record.showNew === 1 ? <font color={'green'}>是</font> : <font color={'red'}>否</font>) },
    { title: '是否置顶', dataIndex: 'postOrder', align: 'center', render: (text, record) => (record.postOrder === 1 ? <font color={'green'}>是</font> : <font color={'red'}>否</font>) },
    {
      title: '对应公告导航',
      dataIndex: 'nav',
      align: 'center',
      render: (text, record) => (findNavName(record.tagId, tagIdList) + '-' + findNavName(record.childId, childIdList[record.tagId]))
    },
    { title: '发布人', dataIndex: 'optUid', align: 'center' },
    {
      title: '操作',
      key: 'operation',
      align: 'center',
      render: (text, record) => (<span><Popconfirm title='请确认是否删除?' onConfirm={this.deleteHandler(record.id)}><a href=''>删除</a></Popconfirm>
        <Divider type='vertical' /><a onClick={this.addUpdateHandler(record)}>修改</a></span>)
    }
  ]

  state = {
    visible: false,
    tagId: 1,
    tagIdForm: 0,
    value: ''
  }

  componentDidMount = () => {
    this.getList()
  }

  deleteHandler = (id) => () => {
    this.props.dispatch({
      type: `${namespace}/deleteAnnouncement`,
      payload: { id: id }
    })
  }

  getList = (tagId, childId, isValid, postOrder) => {
    let param = {}
    if (tagId) {
      param.tagId = tagId
    }
    if (childId) {
      param.childId = childId
    }
    if (isValid) {
      param.isValid = isValid
    }
    if (postOrder) {
      param.postOrder = postOrder
    }
    console.log('getList====', param)

    this.props.dispatch({
      type: `${namespace}/listAnnouncement`,
      payload: param
    })
  }

  addUpdateHandler = (record) => () => {
    const isUpdate = record !== null && record !== undefined
    if (isUpdate && this.formRef) {
      this.formRef.resetFields()
      this.formRef.setFieldsValue({
        id: record.id,
        title: record.title,
        tagId: record.tagId,
        childId: record.childId > 0 ? record.childId : null,
        validBegin: moment.unix(record.validBegin),
        validEnd: moment.unix(record.validEnd),
        postOrder: record.postOrder,
        showNew: record.showNew,
        content: record.content
      })
      this.setState({ tagIdForm: record.tagId })
    }

    this.setState({ visible: true, isUpdate: isUpdate })
  }

  hiddenModal = () => {
    if (this.formRef) {
      this.formRef.resetFields()
    }
    this.setState({ visible: false, isUpdate: null, tagIdForm: 0 })
  }

  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  handleCancel = e => {
    this.hiddenModal()
  }

  handleSubmit = e => {
    if (this.formRef) {
      this.formRef.submit()
    }
  }

  onFinish = values => {
    const { isUpdate } = this.state
    console.log(values, isUpdate)

    if (values === null || values === undefined) {
      message.warn('填写有误')
      return
    }

    if (values.tagId === undefined) {
      message.warn('导航选择有误')
      return
    }
    if (values.tagId !== 1 && (values.childId === undefined || values.childId <= 0)) {
      message.warn('导航选择有误')
      return
    }

    let content = values.content
    if (content.length === 0) {
      message.warn('请填写内容')
      return
    }

    if (values.validBegin.unix() >= values.validEnd.unix()) {
      message.warn('生效时间不能大于失效时间')
      return
    }

    let data = {
      id: values.id,
      title: values.title,
      tagId: values.tagId,
      tagName: findNavName(values.tagId, tagIdList),
      childId: values.childId,
      childName: findNavName(values.childId, childIdList[values.tagId]),
      validBegin: values.validBegin.unix(),
      validEnd: values.validEnd.unix(),
      postOrder: values.postOrder,
      showNew: values.showNew,
      content: content
    }

    console.log(data)

    this.props.dispatch({
      type: isUpdate ? `${namespace}/updateAnnouncement` : `${namespace}/addAnnouncement`,
      payload: data
    })

    this.hiddenModal()
  }

  saveFormRefSearch = (formRef) => {
    this.formRefSearch = formRef
  }

  onFinishSearch = values => {
    console.log('onFinishSearch====', values)
    this.getList(values.tagId, values.childId, values.isValid, values.postOrder)
  }

  handleTagIdChange = (e) => {
    console.log(e)
    this.setState({ tagId: e })
    if (this.formRefSearch) {
      this.formRefSearch.setFieldsValue({ childId: null })
    }
  }

  searchHandler = () => () => {
    if (this.formRefSearch) {
      this.formRefSearch.submit()
    }
  }

  handleTagIdChangeForm = (e) => {
    console.log(e)
    this.setState({ tagIdForm: e })
    if (this.formRef) {
      this.formRef.setFieldsValue({ childId: null })
    }
  }

  render () {
    const { visible, isUpdate, tagId, tagIdForm } = this.state
    const { model: { list } } = this.props

    const formItemLayout = {
      labelCol: {
        xs: { span: 4 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 2 },
        sm: { span: 15 }
      }
    }

    return (
      <Card>
        <Form ref={this.saveFormRefSearch} onFinish={this.onFinishSearch} >
          <Space>
            <Form.Item label='对应公告导航' name='tagId' >
              <Select
                allowClear
                style={{ width: 120 }}
                onChange={this.handleTagIdChange}
                options={tagIdList.map((item) => ({ label: item.name, value: item.idx }))}
              />
            </Form.Item>

            <Form.Item name='childId' >
              <Select
                allowClear
                style={{ width: 150 }}
                options={tagId ? childIdList[tagId].map((item) => ({ label: item.name, value: item.idx })) : null}
              />
            </Form.Item>

            <Form.Item label='是否生效' name='isValid' >
              <Select
                allowClear
                style={{ width: 80 }}
                options={validOpts.map((item) => ({ label: item.name, value: item.idx }))}
              />
            </Form.Item>

            <Form.Item label='是否置顶' name='postOrder' >
              <Select
                allowClear
                style={{ width: 80 }}
                options={topOpts.map((item) => ({ label: item.name, value: item.idx }))}
              />
            </Form.Item>

            <Form.Item >
              <Button type='primary' onClick={this.searchHandler()}>查询</Button>
            </Form.Item>
          </Space>
        </Form>

        <Button type='primary' onClick={this.addUpdateHandler()}>新增</Button>

        <Table rowKey='idx' pagination={this.defaultPageValue} columns={this.columns} dataSource={list} />

        <Modal forceRender maskClosable={false} visible={visible} width={1400} title={isUpdate ? '更新公告' : '新增公告'} onCancel={this.handleCancel} onOk={this.handleSubmit} okText={'确认并提交'}>
          <Form {...formItemLayout} ref={this.saveFormRef} onFinish={this.onFinish}>
            <Form.Item name='id' hidden>
              <Input hidden />
            </Form.Item>

            <Form.Item name='title' label='标题' rules={[{ required: true, message: '必填' }]}>
              <Input placeholder={'最大输入20个字'} style={{ width: 247 }} maxLength={20} />
            </Form.Item>

            <Form.Item label='对应公告导航' rules={[{ required: true, message: '必填' }]}>
              <Space>
                <Form.Item name='tagId' >
                  <Select
                    allowClear
                    style={{ width: 100 }}
                    placeholder={'一级导航'}
                    onChange={this.handleTagIdChangeForm}
                    options={tagIdList.map((item) => ({ label: item.name, value: item.idx }))}
                  />
                </Form.Item>

                <Form.Item name='childId' >
                  <Select
                    allowClear
                    placeholder={'二级导航'}
                    style={{ width: 140 }}
                    options={tagIdForm ? childIdList[tagIdForm].map((item) => ({ label: item.name, value: item.idx })) : null}
                  />
                </Form.Item>
              </Space>
            </Form.Item>

            <Form.Item label='生效时间' name='validBegin' rules={[{ required: true, message: '必填' }]} extra='注: 必填. 公告会再配置的时间自动生效'>
              <DatePicker style={{ width: 220 }} showTime='true' format='YYYY-MM-DD HH:mm' />
            </Form.Item>

            <Form.Item label='失效时间' name='validEnd' rules={[{ required: true, message: '必填' }]} extra='注: 必填. 公告会再配置的时间自动失效'>
              <DatePicker style={{ width: 220 }} showTime='true' format='YYYY-MM-DD HH:mm' />
            </Form.Item>

            <Form.Item name='postOrder' label='是否置顶' rules={[{ required: true, message: '必填' }]}>
              <Select
                allowClear
                style={{ width: 220 }}
                options={topOpts.map((item) => ({ label: item.name, value: item.idx }))}
              />
            </Form.Item>

            <Form.Item name='showNew' label='是否展示new标签' rules={[{ required: true, message: '必填' }]}>
              <Select
                allowClear
                style={{ width: 220 }}
                options={showNewOpts.map((item) => ({ label: item.name, value: item.idx }))}
              />
            </Form.Item>

            <Form.Item name='content' getValueFromEvent={(e) => { return e.target.getContent() }} label='内容'>
              <TinymceEditer />
            </Form.Item>
          </Form>
        </Modal>
      </Card>
    )
  }
}

export default YoBackAnnouncement
