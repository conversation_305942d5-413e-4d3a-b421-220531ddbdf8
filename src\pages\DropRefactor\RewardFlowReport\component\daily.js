import React, { Component } from 'react'
import { Card, Table, DatePicker, Form, Row, Col, Button, Select, Tooltip } from 'antd'
import { connect } from 'dva'
import moment from 'moment'
import { SearchOutlined, QuestionCircleOutlined } from '@ant-design/icons'
import { rewardFlowSourceOptions, parseRewardFlowSourceOptions } from '../../dropCommon'

const namespace = 'RewardFlowReport' // model 的 namespace
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker

const Option = Select.Option

@connect(({ RewardFlowReport }) => ({ // model 的 namespace
  model: RewardFlowReport // model 的 namespace
}))
class RFIReportComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      channel: '',
      dateRange: [moment().subtract(7, 'days'), moment().subtract(0, 'days')],
      visible: false
    }
  }

  componentDidMount () {
    const { dispatch } = this.props

    const { dateRange, channel } = this.state
    let data = { begin: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat), channel: channel }

    dispatch({
      type: `${namespace}/getDailyReportList`,
      payload: data
    })
  }

  onFinish = values => {
    // console.log(values)
    const { dispatch } = this.props

    const { dateRange, channel } = this.state
    let data = { begin: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat), channel: channel }

    dispatch({
      type: `${namespace}/getDailyReportList`,
      payload: data
    })
  }

 // 表头显示提示语
 genColumnTooltip = (title) => {
   return {
     filterDropdown: (<span />),
     filterIcon: (
       <Tooltip placement='top' title={title}>
         <QuestionCircleOutlined style={{ fontSize: '16px' }} />
       </Tooltip>
     )
   }
 }

 // 创建tooltip
 createTooltip = (v, width = 10) => {
   return !v ? '-' : <Tooltip title={v}>
     <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', width: `${width}em` }}>{v}</div>
   </Tooltip>
 }

  // 需要修改
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '渠道', dataIndex: 'source', align: 'center', render: text => parseRewardFlowSourceOptions(text) },
    { title: '物资流水(紫水晶)', dataIndex: 'dropOut', align: 'center', ...this.genColumnTooltip('用户获得礼物流水（普通道具池，超级道具池、进度奖励、专属医疗包奖励、补给箱奖励）') },
    { title: '发放道具主持人数', dataIndex: 'compereCount', align: 'center' },
    { title: '金额(紫金币)', dataIndex: 'flowOut', align: 'center' },
    { title: '流水占比(发放金额/空投流水)', dataIndex: 'percent', align: 'center', render: text => `${text}`, ...this.genColumnTooltip('发放紫金币价值/空投日总收入') }
  ]

  // 不需要修改
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { dailyReportList } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form onFinish={this.onFinish}>
          <Row gutter={12}>
            <Col>
              <Form.Item name='dateRange'>
                <RangePicker defaultValue={dateRange} format={'YYYY-MM-DD'} onChange={(date, format) => this.setState({ dateRange: date })} />
              </Form.Item>
            </Col>
            <Col>
              渠道:
              <Select labelInValue defaultValue={{ key: '' }} style={{ width: 155, marginLeft: 10 }} onChange={(value) => this.setState({ channel: value.key })}>
                <Option value=''>默认(All)</Option>
                {
                  rewardFlowSourceOptions.map((item) => {
                    return <Option value={item.value}>{item.label}</Option>
                  })
                }
              </Select>
            </Col>
            <Col>
              <Form.Item>
                <Button type='primary' htmlType='submit'><SearchOutlined />Search</Button>
              </Form.Item>
            </Col>
          </Row>
        </Form>
        <Table rowKey='index' dataSource={dailyReportList} columns={this.columns} size='small' pagination={{ pageSize: 500 }} /> {/* 显示的列表 */}
      </Card>
    )
  }
}

export default RFIReportComponent
