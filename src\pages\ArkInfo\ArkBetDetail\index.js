import React, { Component } from 'react'
import dateString from '@/utils/dateString'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Button, Table, Card, Form, Divider, DatePicker, Input, message, Space, BackTop } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const namespace = 'arkBetDetail'
const getListUri = `${namespace}/getList`

// const dateFormat = 'YYYY-MM-DD HH:mm'
// const { RangePicker } = DatePicker

const gameMap = { 5: '诺亚方舟' }

// 建筑
const armoryOptions = [
  { position: 1, value: '激光塔' },
  { position: 2, value: '机关枪' },
  { position: 3, value: '喷火器' },
  { position: 4, value: '冰冻塔' },
  { position: 5, value: '加特林' },
  { position: 6, value: '坦克' },
  { position: 7, value: '飞艇' },
  { position: 8, value: '高达' }
]

// 怪物
const monsterOptions = [
  { position: 1, value: '蝙蝠' },
  { position: 2, value: '恶狼' },
  { position: 3, value: '水怪' },
  { position: 4, value: '火犬' },
  { position: 5, value: '剑虎' },
  { position: 6, value: '冰熊' },
  { position: 7, value: '泰坦' },
  { position: 8, value: '巨龙' }
]

@connect(({ arkBetDetail }) => ({
  model: arkBetDetail
}))
class ArkBetDetail extends Component {
  // 定义列表结构，
  columns = [
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '玩法类型', dataIndex: 'arenaId', align: 'center', render: (text, record, index) => { return gameMap[text] } },
    { title: '参与武器', dataIndex: 'betPosition', align: 'center', render: (text, record) => this.renderArmory(text, record) },
    { title: '投注道具', dataIndex: 'propsId', align: 'center' },
    { title: '投注数量(个)', dataIndex: 'count', align: 'center' },
    { title: '参与花费(紫水晶)', dataIndex: 'amethyst', align: 'center' },
    { title: '参与时间', dataIndex: 'betTime', align: 'center', render: text => dateString(text) },
    { title: '参与轮次', dataIndex: 'roundStart', align: 'center', render: text => dateString(text) },
    { title: '进攻怪物', dataIndex: 'winPosition', align: 'center', render: (text, record) => this.renderMonster(text, record) },
    { title: '是否猜中', dataIndex: 'isWinner', align: 'center', render: (text, record) => (text === 0 ? '否' : '是') },
    { title: '奖励道具发放', dataIndex: 'hasIssued', align: 'center', render: (text, record) => (text === 0 ? '否' : '是') }
  ]

  renderArmory (armory, record) {
    if (record.arenaId !== 5) {
      return '-'
    }

    let names = []
    const arms = armory.split(',')
    let positionOpts = armoryOptions
    if (Array.isArray(arms) && arms.length) {
      arms.forEach((position) => {
        for (let i = 0; i < positionOpts.length; i++) {
          console.log(position, positionOpts[i].position.toString())
          if (positionOpts[i].position.toString() === position) {
            names.push(positionOpts[i].value)
            break
          }
        }
      })
    }
    return names.join('|') + '(' + armory + ')'
  }

  renderMonster (armory, record) {
    if (record.arenaId !== 5) {
      return '-'
    }

    let names = []
    const arms = armory.split(',')
    let positionOpts = monsterOptions
    if (Array.isArray(arms) && arms.length) {
      arms.forEach((position) => {
        for (let i = 0; i < positionOpts.length; i++) {
          console.log(position, positionOpts[i].position.toString())
          if (positionOpts[i].position.toString() === position) {
            names.push(positionOpts[i].value)
            break
          }
        }
      })
    }
    return names.join('|') + '(' + armory + ')'
  }

  pagination = { pageSizeOptions: ['20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, startValue: null, endValue: null, uid: 0 }

  // 获取列表
  componentDidMount () {
    // const { dispatch, model: { list } } = this.props
    // dispatch({
    //   type: getListUri
    // })

    // this.setState({ list })
  }

  disabledStartDate = (startValue) => {
    const endValue = this.state.endValue
    if (!startValue || !endValue) {
      return false
    }
    return startValue.valueOf() > endValue.valueOf()
  }

  disabledEndDate = (endValue) => {
    const startValue = this.state.startValue
    if (!endValue || !startValue) {
      return false
    }
    return endValue.valueOf() <= startValue.valueOf()
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  onClick = () => {
    const { dispatch } = this.props
    const { startValue, endValue, uid } = this.state
    if (uid <= 0) {
      message.error('uid不能为空且大于0')
      return
    }
    var data = { startDate: moment(startValue).format('YYYY-MM-DD HH:mm:00'), endDate: moment(endValue).format('YYYY-MM-DD HH:mm:00'), uid: uid }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  render () {
    const { route, model: { list } } = this.props
    const { startValue, endValue } = this.state
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Space align='center'>
            <div>
              UID
              <Input onChange={e => this.setState({ uid: e.target.value })} style={{ marginLeft: 10, width: 150 }} /> {/* 搜索按钮 */}
              <span style={{ marginLeft: 10 }}>时间范围</span>
              <DatePicker
                disabledDate={this.disabledStartDate}
                showTime={{ format: 'HH:mm' }}
                format='YYYY-MM-DD HH:mm'
                value={startValue}
                placeholder='开始时间'
                onChange={this.onStartChange}
                style={{ marginLeft: 10 }}
              />
              <span style={{ marginLeft: 10 }}>~</span>
              <DatePicker
                disabledDate={this.disabledEndDate}
                showTime={{ format: 'HH:mm' }}
                format='YYYY-MM-DD HH:mm'
                value={endValue}
                placeholder='结束时间'
                onChange={this.onEndChange}
                style={{ marginLeft: 10 }}
              />
              <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
            </div>
            <div>
              <div><font color='red'>编号映射:1 蝙蝠→激光塔 2 恶狼→机关枪 3 水怪→喷火器 4 火犬→冰冻塔 5 剑虎→加特林 6 冰熊→坦克 7 泰坦→飞艇 8 巨龙→高达</font></div>
            </div>
          </Space>
          <Divider />
          <Form>
            <BackTop />
            <Table dataSource={list} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
          </Form>
        </Card>
      </PageHeaderWrapper>
    )
  }
}
export default ArkBetDetail
