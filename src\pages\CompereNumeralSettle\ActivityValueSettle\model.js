import { getLists, add, update, getDetailLists, publish, removeDetailLists } from './api'
import { message } from 'antd'

export default {
  namespace: 'activityValueSettle',

  state: { list: [], detailList: [] },

  reducers: {
    updateList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    },
    updateDetailList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        detailList: payload
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getLists)
      yield put({
        type: 'updateList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * getDetailList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getDetailLists, payload)
      yield put({
        type: 'updateDetailList',
        payload: Array.isArray(list) ? list : []
      })
    },

    * addItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(add, payload)
      if (status === 0) {
        message.success('add success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * updateItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(update, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * removeItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(removeDetailLists, payload)
      if (status === 0) {
        message.success('remove success')
        yield put({
          type: 'getDetailList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * publishItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(publish, payload)
      if (status === 0) {
        message.success('publish success')
        yield put({
          type: 'getDetailList',
          payload: payload
        })
      } else {
        message.error('failed' + msg)
      }
    }
  }
}
