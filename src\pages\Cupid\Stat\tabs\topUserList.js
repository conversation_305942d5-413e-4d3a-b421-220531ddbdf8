/* eslint-disable no-template-curly-in-string */
import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Row, Typography, Col, Table, Space, Divider, DatePicker, Button } from 'antd'
import { onExportExcel } from '@/utils/common'
import { ballTypeOptions, valueFormater, optionsFormater } from '../statCommon'
import moment from 'moment'

const { Text } = Typography
const namespace = 'cupidStat'

@connect(({ cupidStat }) => ({
  model: cupidStat
}))

class TopUserList extends Component {
  state = {
    timeRange: [moment().subtract(7, 'day'), moment()]
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  componentDidMount = () => {
    this.queryTopUserList()
  }

  // 查询数据
  queryTopUserList = () => {
    const { timeRange } = this.state
    const { originID } = this.props
    const [ t1, t2 ] = timeRange

    this.callModel('getTopUserList', {
      params: {
        start: t1.format('YYYYMMDD'),
        end: t2.format('YYYYMMDD'),
        originId: originID
      }
    })
  }

  render () {
    const { timeRange } = this.state
    const { topUserList } = this.props.model

    const columns = [
      { title: '日期', dataIndex: 'date' },
      { title: 'UID', dataIndex: 'uid' },
      { title: '昵称', dataIndex: 'nick' },
      { title: '用户投入', dataIndex: 'totalIn', render: (v, r) => { return valueFormater(v) } },
      { title: '奖池产出', dataIndex: 'totalOut', render: (v, r) => { return valueFormater(v) } },
      { title: '发放占比', dataIndex: '_1', render: (v, r) => { return `${Number(r.totalOut * 100.0 / r.totalIn).toFixed(2)}%` } },
      { title: '总盈余', dataIndex: '_2', render: (v, r) => { return valueFormater(r.totalIn - r.totalOut) } },
      { title: '参与流水最高奖池', dataIndex: 'topBall', render: (v) => { return optionsFormater(ballTypeOptions, v) } },
      { title: '奖池流水占比', dataIndex: 'ballVal', render: (v, r) => { return `${Number(r.ballVal * 100.0 / r.totalIn).toFixed(2)}%` } },
      { title: '参与流水最高房间', dataIndex: 'roomName' },
      { title: '房间流水占比', dataIndex: 'roomVal', render: (v, r) => { return `${Number(r.roomVal * 100.0 / r.totalIn).toFixed(2)}%` } }
    ].map(item => {
      item.align = 'center'
      return item
    })

    return (
      <Card>
        <Row>
          <Col span={24} style={{ marginBottom: '1em' }}>
            {/* 筛选项 */}
            <Space>
              <Text>时间范围:</Text>
              <DatePicker.RangePicker format='YYYY-MM-DD' value={timeRange} onChange={v => { this.setState({ timeRange: v }) }} />
              <Divider type='vertical' />

              <Button type='primary' onClick={() => this.queryTopUserList()}>查询</Button>
              <Button disabled={topUserList.length === 0}
                onClick={() => { onExportExcel(columns, topUserList, 'TOP10用户.xlsx') }}>导出</Button>
            </Space>
          </Col>
          <Col span={24}>
            <Table columns={columns} dataSource={topUserList} />
          </Col>
        </Row>
      </Card>
    )
  }
}

export default TopUserList
