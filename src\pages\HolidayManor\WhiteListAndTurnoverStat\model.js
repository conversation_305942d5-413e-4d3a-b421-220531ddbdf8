﻿import { getLists, getStatLists, whiteListAdd, whiteListDel, whiteListUpdate } from './api'
import { message } from 'antd'

export default {
  namespace: 'holidayManor',

  state: {
    list: [],
    statList: []
  },

  reducers: {
    refreshList (state, { payload }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload
      }
    },

    refreshStatList (state, { payload }) {
      var stateList = payload != null && Array.isArray(payload.turnoverStatInfo) ? payload.turnoverStatInfo : []
      console.log('refreshStatList', payload)
      for (var i = 0; i < stateList.length; i++) {
        stateList[i].index = i + 1
      }
      return {
        ...state,
        statList: stateList
      }
    }
  },

  effects: {
    * getList ({ payload, callback }, { call, put }) {
      const { data: { data } } = yield call(getLists, payload)
      if (callback) {
        callback(data)
      }
      yield put({
        type: 'refreshList',
        payload: data != null && Array.isArray(data.gardenConf) ? data.gardenConf : []
      })
    },

    * getStatList ({ payload, callback }, { call, put }) {
      const { data: { data } } = yield call(getStatLists, payload)
      if (callback) {
        callback(data)
      }
      yield put({
        type: 'refreshStatList',
        payload: data
      })
    },

    * addItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(whiteListAdd, payload)
      if (status === 0) {
        message.success('新增成功，审批通过后生效！')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('添加失败：' + msg)
      }
    },

    * updateItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(whiteListUpdate, payload)
      if (status === 0) {
        message.success('更新成功，审批通过后生效')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('更新失败：' + msg)
      }
    },

    * removeItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(whiteListDel, payload)
      if (status === 0) {
        message.success('删除成功')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('删除失败：' + msg)
      }
    }
  }
}
