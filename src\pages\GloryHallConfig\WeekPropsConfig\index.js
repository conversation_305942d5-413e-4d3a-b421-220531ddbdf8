import React, { Component } from 'react'
import dateString from '@/utils/dateString'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Button, Table, Card, Divider, Input, Modal, Popconfirm, message } from 'antd'
import { Form } from '@ant-design/compatible'
import { SearchOutlined } from '@ant-design/icons'
import { connect } from 'dva'

var moment = require('moment')
const namespace = 'weekProosConfig'
const getListUri = `${namespace}/getList`
const updateItemUri = `${namespace}/updateItem`

const EditableContext = React.createContext()

const EditableRow = ({ form, index, ...props }) => (
  <EditableContext.Provider value={form}>
    <tr {...props} />
  </EditableContext.Provider>
)

const EditableFormRow = Form.create()(EditableRow)
class EditableCell extends React.Component {
  state = { editing: false }

  toggleEdit = () => {
    const editing = !this.state.editing
    this.setState({ editing }, () => {
      if (editing) {
        this.input.focus()
      }
    })
  }

  save = e => {
    const { record, handleSave } = this.props
    this.form.validateFields((error, values) => {
      if (error && error[e.currentTarget.id]) {
        return
      }
      this.toggleEdit()
      handleSave({ ...record, ...values })
    })
  }

  renderCell = form => {
    this.form = form
    const { children, dataIndex, record, title } = this.props
    const { editing } = this.state
    return editing ? (
      <Form.Item style={{ margin: 0 }}>
        {form.getFieldDecorator(dataIndex, {
          rules: [
            {
              required: true,
              message: `${title} is required.`
            }
          ],
          initialValue: record[dataIndex]
        })(<Input ref={node => (this.input = node)} onPressEnter={this.save} onBlur={this.save} />)}
      </Form.Item>
    ) : (
      <div
        className='editable-cell-value-wrap'
        style={{ paddingRight: 24 }}
        onClick={this.toggleEdit}
      >
        {children}
      </div>
    )
  }

  render () {
    const {
      editable,
      dataIndex,
      title,
      record,
      index,
      handleSave,
      children,
      ...restProps
    } = this.props
    return (
      <td {...restProps}>
        {editable ? (
          <EditableContext.Consumer>{this.renderCell}</EditableContext.Consumer>
        ) : (
          children
        )}
      </td>
    )
  }
}

// 2022-01-17, 因产品要求, 调整页面中出现的关键字"奖励"
@connect(({ weekProosConfig }) => ({
  model: weekProosConfig
}))
class WeekProosConfig extends Component {
  // 定义列表结构，
  columns = [
    { title: '配置id', dataIndex: 'configId', align: 'center' },
    { title: '开始时间', dataIndex: 'startTimeStamp', align: 'center', render: (text, record) => this.getActTimeDisplay(text, record) },
    { title: '结束时间', dataIndex: 'endTimeStamp', align: 'center', render: (text, record) => this.getActTimeDisplay(text, record) },
    { title: '礼物类型', dataIndex: 'propsListType', align: 'center', render: (text, record) => (text === 0 ? '周星礼物' : text === 1 ? '普通礼物' : '高阶礼物') },
    { title: '配置状态', dataIndex: 'displayState', align: 'center', render: (text, record) => (text === 3 ? '本周配置' : text === 2 ? '待生效配置' : '过期配置') },
    { title: '操作时间', dataIndex: 'updateTime', align: 'center', width: 180, render: text => dateString(text) },
    { title: '操作人', dataIndex: 'updatePassport', align: 'center' },
    { title: '操作',
      align: 'center',
      render: (text, record) => this.renderBriefColumn(record.startDate, record.endDate, record) }
  ]

  detailColumns = [
    {
      title: '道具ID',
      dataIndex: 'propId',
      align: 'center',
      render: (text, record) => this.isEditing(record) ? <Button onClick={e => this.setState({ propsVisible: true, editRow: record, selectedRowKeys: [] })}>{text}</Button> : text
    },
    { title: '道具名称', dataIndex: 'propName', align: 'center' },
    { title: '道具价格', dataIndex: 'propPrice', align: 'center' },
    {
      title: '操作',
      align: 'center',
      render: (text, record) => this.renderDetailColumn(record.startDate, record.endDate, record)
    }
  ]

  isEditing = record => record.index === this.state.editingKey

  getActTimeDisplay = (text, record, width = 100) => {
    if (record.displayState === 3) {
      let d = moment.unix(text).format('YYYY-MM-DD')
      return <div style={{ color: 'red' }}>{d}</div>
    }
    if (record.displayState === 2) {
      let d = moment.unix(text).format('YYYY-MM-DD')
      return <div style={{ color: 'blue' }}>{d}</div>
    }
    if (record.displayState === 1) {
      let d = moment.unix(text).format('YYYY-MM-DD')
      return <div style={{ color: 'darkgray' }}>{d}</div>
    }
  }

  edit (record) {
    this.toPropsEdit()
    this.setState({ editingKey: record.index, editRow: record, isPropsUpdate: true })
  }

  save (form, record) {
    form.validateFields((err, values) => {
      if (!err) {
        form.resetFields()
        this.setState({ editingKey: '', editRow: null, shouldSubmit: true })
      }
    })
  }

  cancel = (key) => {
    const { detailList } = this.state
    let newList = $.extend([], true, detailList)
    for (var i = 0; i < newList.length; i++) {
      if (newList[i].index === key && newList[i].isAdd === 1) {
        newList.splice(i, 1)
        break
      }
    }

    this.setState({ editingKey: '', editRow: null, detailList: newList })
  }

  delete = (key) => {
    const { detailList } = this.state
    let newList = $.extend([], true, detailList)
    for (var i = 0; i < newList.length; i++) {
      if (newList[i].index === key) {
        newList.splice(i, 1)
        break
      }
    }

    this.setState({ editingKey: '', editRow: null, detailList: newList, shouldSubmit: true })
  }

  propsColumns = [
    {
      title: '道具ID',
      align: 'center',
      width: 100,
      dataIndex: 'id',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ width: 300, padding: 8, borderRadius: 4, background: '#FFF', boxShadow: '0 1px 6px' }}>
          <Input style={{ width: 140, marginRight: 10 }} value={selectedKeys[0]} onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])} onPressEnter={this.handleSearchPropsId(selectedKeys, confirm)} />
          <Button style={{ marginRight: 5 }} type='primary' onClick={this.handleSearchPropsId(selectedKeys, confirm)}>搜索</Button>
          <Button onClick={this.handleResetPropsId(clearFilters)}>重置</Button>
        </div>
      ),
      onFilter: (value, record) => record.id.toString().toLowerCase().includes((value.toLowerCase())),
      filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#108ee9' : '#aaa' }} />,
      render: text => {
        text = text.toString()
        const { searchTextPropsId } = this.state
        return searchTextPropsId ? (
          <span>
            {text.split(new RegExp(`(?<=${searchTextPropsId})|(?=${searchTextPropsId})`, 'i')).map((fragment, i) => (
              fragment.toLowerCase() === searchTextPropsId.toLowerCase() ? <span key={i} style={{ color: '#f50' }}>{fragment}</span> : fragment
            ))}</span>) : text
      }
    },
    {
      title: '名称',
      align: 'center',
      width: 100,
      dataIndex: 'name',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <div style={{ width: 300, padding: 8, borderRadius: 4, background: '#FFF', boxShadow: '0 1px 6px' }}>
          <Input style={{ width: 140, marginRight: 10 }} value={selectedKeys[0]} onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])} onPressEnter={this.handleSearch(selectedKeys, confirm)} />
          <Button style={{ marginRight: 5 }} type='primary' onClick={this.handleSearch(selectedKeys, confirm)}>搜索</Button>
          <Button onClick={this.handleReset(clearFilters)}>重置</Button>
        </div>
      ),
      onFilter: (value, record) => record.name.toLowerCase().includes((value.toLowerCase())),
      filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#108ee9' : '#aaa' }} />,
      render: text => {
        const { searchText } = this.state
        return searchText ? (
          <span>
            {text.split(new RegExp(`(?<=${searchText})|(?=${searchText})`, 'i')).map((fragment, i) => (
              fragment.toLowerCase() === searchText.toLowerCase() ? <span key={i} style={{ color: '#f50' }}>{fragment}</span> : fragment
            ))}</span>) : text
      }
    },
    { title: '单价', align: 'center', width: 100, dataIndex: 'price' },
    { title: '货币类型', align: 'center', width: 100, dataIndex: 'priceType' },
    { title: '道具类型', align: 'center', width: 150, dataIndex: 'type' },
    { title: '生效日期', align: 'center', width: 180, dataIndex: 'startTime', render: text => moment.unix(text / 1000).format('YYYY-MM-DD HH:mm:ss') },
    { title: '失效日期', align: 'center', dataIndex: 'endTime', render: text => moment.unix(text / 1000).format('YYYY-MM-DD HH:mm:ss') }
  ]

  // 本地搜索
  handleSearch = (selectedKeys, confirm) => () => {
    const { dispatch, model: { localPropsList } } = this.props
    let list = []
    // 根据条件筛选
    for (var i = 0; i < localPropsList.length; i++) {
      if (localPropsList[i].name.toString().toLowerCase().includes(selectedKeys[0].toLowerCase())) {
        list = [...list, localPropsList[i]]
      }
    }
    dispatch({
      type: `${namespace}/updatePropsList`,
      payload: Array.isArray(list) ? list : []
    })
    confirm()
    this.setState({ searchText: selectedKeys[0] })
  }
  // 重置搜索
  handleReset = clearFilters => () => {
    const { dispatch, model: { localPropsList } } = this.props
    dispatch({
      type: `${namespace}/updatePropsList`,
      payload: localPropsList
    })
    clearFilters()
    this.setState({ searchText: '' })
  }
  // 根据PropsId搜索
  handleSearchPropsId = (selectedKeys, confirm) => () => {
    const { dispatch, model: { localPropsList } } = this.props
    let list = []
    for (var i = 0; i < localPropsList.length; i++) {
      if (localPropsList[i].id.toString().toLowerCase().includes(selectedKeys[0].toLowerCase())) {
        list = [...list, localPropsList[i]]
      }
    }
    dispatch({
      type: `${namespace}/updatePropsList`,
      payload: Array.isArray(list) ? list : []
    })
    confirm()
    this.setState({ searchTextPropsId: selectedKeys[0] })
  }
  // 重置 PropsId搜索条件
  handleResetPropsId = clearFilters => () => {
    const { dispatch, model: { localPropsList } } = this.props
    dispatch({
      type: `${namespace}/updatePropsList`,
      payload: localPropsList
    })
    clearFilters()
    this.setState({ searchTextPropsId: '' })
  }

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  pagination = { pageSizeOptions: ['20', '50', '100'], showSizeChanger: true, pageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, startValue: null, endValue: null, searchResult: [], searchDone: false, selectedRowKeys: [], selectedProps: null, editingKey: '', shouldSubmit: false }

  // 获取列表
  componentDidMount () {
    const { dispatch, model: { list } } = this.props
    dispatch({
      type: getListUri
    })

    this.toPropsEdit()
    this.setState({ list })
  }

  renderBriefColumn = (start, end, record) => {
    if (record.configState === 1) {
      return (<span>
        <a onClick={this.showDetailModal(false, record)}>查看详情</a>
      </span>)
    }

    return (<span>
      <a onClick={this.showDetailModal(true, record)}>编辑</a>
    </span>)
  }

  renderDetailColumn = (start, end, record) => {
    const { isPropsUpdate, editingKey } = this.state
    if (!isPropsUpdate) {
      return (<span>
        <a disabled onClick={this.showPropsModal(true, record)}>编辑</a>
      </span>)
    }

    const editable = this.isEditing(record)
    return editable ? (
      <span>
        <EditableContext.Consumer>
          {form => (<a onClick={() => this.save(form, record)} style={{ marginRight: 8 }}>保存</a>)}
        </EditableContext.Consumer>
        <Popconfirm title='Sure to cancel?' onConfirm={() => this.cancel(record.index)}><a>取消</a></Popconfirm>
      </span>
    ) : (
      <span>
        <a disabled={editingKey !== ''} onClick={() => this.edit(record)} style={{ marginRight: 8 }}>编辑</a>
        <Popconfirm title='Sure to delete?' onConfirm={() => this.delete(record.index)}><a disabled={editingKey !== ''}>删除</a></Popconfirm>
      </span>
    )
  }

  // show modal
  showDetailModal = (isUpdate, record) => () => {
    let index = 0
    let v = $.extend(true, {}, record)
    let newList = v.propsList.map(item => {
      item.index = index
      index++
      return item
    })

    this.setState({ value: record, detailList: newList, visible: true, isPropsUpdate: isUpdate })
  }

  showPropsModal = (isUpdate, record) => () => {
    this.setState({ propsVisible: isUpdate })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false, editingKey: '' })
  }

  handleSubmit = e => {
    const { dispatch } = this.props
    const { shouldSubmit, value, detailList, editingKey } = this.state

    if (editingKey !== '') {
      message.warn('编辑的内容需要保存或取消后才可提交')
      return
    }

    if (!shouldSubmit) {
      this.setState({ visible: false })
      return
    }

    value.propsList = detailList
    dispatch({
      type: updateItemUri,
      payload: value
    })
    this.setState({ visible: false, editingKey: '' })
  }

  handleAdd = () => {
    let maxIndex = 0
    var { detailList } = this.state
    let cpList = $.extend([], true, detailList)
    detailList.forEach(function (item) {
      if (item.index > maxIndex) {
        maxIndex = item.index
      }
    })
    const newRow = {
      index: maxIndex + 10,
      propId: 0,
      isAdd: 1
    }
    cpList = [...cpList, newRow]
    this.setState({ editingKey: newRow.index, editRow: newRow, detailList: cpList })
  }

  handleSelectPropsChange = (selectedRowKeys, record) => {
    this.setState({ selectedRowKeys: selectedRowKeys, selectedProps: record[0] })
  }

  // 更新营收礼物配置
  handlePropsSelect = () => {
    // const { dispatch } = this.props
    const { selectedProps, editRow, detailList } = this.state
    if (selectedProps == null || editRow == null) {
      return
    }

    let newList = detailList.map(item => {
      if (item.index === editRow.index) {
        item.propId = selectedProps.id
        item.propName = selectedProps.name
        item.propPrice = selectedProps.price
        editRow.propId = selectedProps.id
        editRow.propName = selectedProps.name
        editRow.propPrice = selectedProps.price
      }
      return item
    })

    this.setState({ editRow: editRow, propsVisible: false, selectedProps: null, selectedRowKeys: [], detailList: newList })
    this.forceUpdate() // 强制刷新
  }

  toPropsEdit = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getPropsConfigList`
    })
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  onSearch = () => {
    const { model: { list } } = this.props
    const { startValue, endValue } = this.state

    var dataSource = list
    const start = startValue.unix()
    const end = endValue.unix()
    if (start > 0 && end > 0) {
      dataSource = dataSource.filter(data => (data.startDate <= start && data.endDate >= start) || (data.startDate <= end && data.endDate >= end))
    }

    this.setState({ searchResult: dataSource, searchDone: true })
  }

  render () {
    const { route, model: { list, propsList } } = this.props
    const { searchResult, searchDone, visible, propsVisible, detailList, selectedRowKeys, isPropsUpdate } = this.state

    const components = {
      body: {
        row: EditableFormRow,
        cell: EditableCell
      }
    }

    const detailColumns = this.detailColumns.map(col => {
      if (!col.editable) {
        return col
      }
      return {
        ...col,
        onCell: record => ({
          record,
          editable: col.editable,
          dataIndex: col.dataIndex,
          title: col.title,
          handleSave: this.handleSave
        })
      }
    })

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Form>
            <Table dataSource={searchDone ? searchResult : list} columns={this.columns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
          </Form>
        </Card>
        <Modal visible={visible} onCancel={this.hideModal} onOk={this.handleSubmit} width='50%'>
          <Button disabled={!isPropsUpdate} style={{ marginLeft: 5 }} onClick={this.handleAdd} type='primary'>新增</Button>
          <Divider />
          <Table components={components} rowClassName={() => 'editable-row'} pagination={false} dataSource={detailList} size='small' columns={detailColumns} rowKey={(record, index) => index} />
        </Modal>
        <Modal onOk={this.handlePropsSelect} okText='确定' cancelText='取消' okButtonProps={{ disabled: selectedRowKeys.length === 0 }} width='50%' title='选择营收礼物' visible={propsVisible} onCancel={e => this.setState({ propsVisible: false })}>
          <Table bordered rowSelection={{ selectedRowKeys, onChange: this.handleSelectPropsChange, type: 'radio' }} pagination={false} scroll={{ y: 340 }} dataSource={propsList} size='small' columns={this.propsColumns} rowKey={(record, index) => index} />
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

export default WeekProosConfig
