/* eslint-disable no-template-curly-in-string */
import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Table, Tag } from 'antd'
import SimplePageParams from '@/components/SearchParams'
const namespace = 'dropQuery'

const paramsConfig = [
  { label: '开始时间', field: 'begin', type: 'datePicker', valType: 'number', showTime: true, defaultVal: '${now-1days}' },
  { label: '结束时间', field: 'end', type: 'datePicker', valType: 'number', showTime: true, defaultVal: '${now}' },
  { label: 'UID', field: 'uid', type: 'inputNumber', defaultVal: '0' },
  { label: '业务', field: 'appID', type: 'select', defaultVal: '2', valType: 'number', options: '交友=>2,语音房=>34,宝贝=>36' },
  { label: '玩法', field: 'dropType', type: 'select', defaultVal: '1000', valType: 'number', options: '汇总=>1000,抢空投=>0,抢物资=>1,幸运小狗=>2,物资大战=>3' },
  { label: '渠道', field: 'statType', type: 'select', defaultVal: '1000', valType: 'number', options: '汇总=>1000,PC=>0,手Y=>1,追玩=>2,YOMI=>3,WEB=>4,H5=>7' }
]

@connect(({ dropQuery }) => ({
  model: dropQuery
}))

class QueryUserSummary extends Component {
  state = {
    params: {}
  }
  componentDidMount = () => {}

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 查询数据
  querySummaryStat = (params) => {
    console.debug('params==>', params)
    this.callModel('getSummaryStat', {
      params: params
    })
  }

  // 格式化礼物列表
  prizeListFormater = (list) => {
    if (list === null || !Array.isArray(list)) {
      return '空'
    }
    return <div>
      {
        list.map(prize => {
          return <Tag style={{ marginRight: '10px', marginBottom: '5px' }}>{`${prize.name}[${prize.propsId}] * ${prize.count}`}</Tag>
        })
      }
    </div>
  }

  // 格式化总价值
  prizeFormater = (list) => {
    let result = 0
    if (!Array.isArray(list)) {
      return 0
    }
    list.forEach(item => {
      result += item.price * item.count
    })
    return result
  }

  column = [
    // { title: '业务类型', dataIndex: 'AppID', render: (v) => { return appIdOptions.find(item => { return item.value === v })?.label } },
    // { title: '玩法', dataIndex: 'DropType', render: (v) => { return dropTypeOptions.find(item => { return item.value === v })?.label } },
    // { title: '渠道', dataIndex: 'StatType', render: (v) => { return statTypeOptions.find(item => { return item.value === v })?.label } },
    // { title: '模式', dataIndex: 'CrazyMode', render: (v) => { return crayzModeOptions.find(item => { return item.value === v })?.label } },
    { title: '统计维度', dataIndex: 'name' },
    { title: '礼物列表', dataIndex: 'prizeList', width: '40em', render: (list) => { return this.prizeListFormater(list) } },
    { title: '总价值(紫水晶)', dataIndex: 'prizeList', render: (list) => { return this.prizeFormater(list) } }
  ].map(item => {
    item.align = 'center'
    return item
  })

  render () {
    const { summaryStat } = this.props.model || {}
    return (
      <Card>
        <SimplePageParams
          config={paramsConfig}
          formProps={{ layout: 'vertical' }}
          onChange={(v) => { this.setState({ params: v }) }}
          onQuery={(v) => { this.querySummaryStat(v) }} />
        <Table columns={this.column} dataSource={summaryStat} pagination={false} scroll={{ x: 'max-content' }} />
      </Card>
    )
  }
}

export default QueryUserSummary
