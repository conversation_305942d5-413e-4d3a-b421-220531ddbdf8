import { Table, message, Typography } from 'antd'
import React, { Component } from 'react'

/*  ================= 参数说明 =================
oldProps: 结构体, 用在<Table/>的属性都可放到里面

diffRender: 字符串, 使用哪种对比方式: ['Git', 'Normal']

columns: 数组, 列的配置描述, 成员属性如下
columns = [{
  dft_getText: function, 将列的内容转换成字符串, 默认为 (value, record)=> {return value }
  dft_hidden: bool, 控制哪些列不显示前后对比
}]

after: 数组, 新数据
before: 数组, 旧数据,用做对比
============================================ */

const { Text } = Typography

// 对比显示方式: Git diff
const diffRenderRedAndGreen = (item, record) => {
  const { before, after } = item
  const newStyle = { backgroundColor: '#ddfbe6', padding: '3px' }
  const delStyle = { backgroundColor: '#f9d7dc', padding: '3px' }

  if (before === after) {
    return <Text>{before}</Text>
  }
  if (before === '' || before === undefined) { // 新增
    return <Text style={newStyle}>{after}</Text>
  }
  if (after === '' || after === undefined) { // 删除
    return <Text style={delStyle}>{before}</Text>
  }
  return <div><Text style={delStyle}>{before}</Text><Text style={newStyle}>{after}</Text></div> // 修改
}

// 对比显示方式：产品要求的
const diffRenderNormal = (item, record) => {
  const { before, after } = item
  if (before === after) {
    return <Text>{before}</Text>
  }
  if (before === '' || before === undefined) { // 新增
    return <Text><Text type='danger'>{after} </Text>(-)</Text>
  }
  if (after === '' || after === undefined) { // 删除
    return <Text><Text type='danger'>- </Text>({before})</Text>
  }
  return <div><Text><Text type='danger'>{after} </Text> ({before})</Text></div> // 修改
}

// ==========================================

class DiffTable extends Component {
  state = {
    columns: [],
    dataSource: []
  }

  componentDidUpdate () {
    // this.makeDataSource()
  }

  componentDidMount () {
    this.makeDataSource()
  }

  makeDataSource = () => {
    const { before, after, columns, diffRender } = this.props
    let renderType = 'Normal'
    if (['Normal', 'Git'].indexOf(diffRender) >= 0) {
      renderType = diffRender
    }
    // console.debug('before==>', before)
    // console.debug('after==>', after)

    // 检查参数
    if (!Array.isArray(before) || !Array.isArray(after) || !Array.isArray(columns)) {
      message.warn('传参错误, 请检查代码~')
      return
    }

    // 重构 columns
    let newColumns = []
    for (let i = 0; i < columns.length; i++) {
      let dataIndex = columns[i].dataIndex
      let item = { ...columns[i] }
      item.key = dataIndex

      if (typeof item.dft_getText !== 'function') {
        item.dft_getText = item.render
      }
      if (!item.dft_getText) {
        item.dft_getText = (v) => { return v }
      }

      if (item.dft_hidden !== true) { // 替换render
        item.render = [diffRenderNormal, diffRenderRedAndGreen][['Normal', 'Git'].indexOf(renderType)]
        item['dataIndex'] = `dft_${dataIndex}`
      }

      newColumns.push(item)
    }

    // 重构 dataSource
    let newDataSource = []
    for (let i = 0; i < before.length || i < after.length; i++) {
      let beforeR = (i < before.length ? before[i] : {})
      let afterR = (i < after.length ? after[i] : {})
      let item = { key: i }

      for (let j = 0; j < newColumns.length; j++) {
        let dataIndex = newColumns[j].key

        item[dataIndex] = afterR[dataIndex]
        if (newColumns[j].dft_hidden === true) { // 按照新数据和原render渲染
          continue
        }

        let beforeV = '?'
        let afterV = '?'
        if (newColumns[j].dft_getText) {
          beforeV = newColumns[j].dft_getText(beforeR[dataIndex], beforeR, i)
          afterV = newColumns[j].dft_getText(afterR[dataIndex], afterR, i)
        }
        item[`dft_${dataIndex}`] = {
          before: beforeV,
          after: afterV
        }
      }

      newDataSource.push(item)
    }

    // console.debug('newDataSource===>', newDataSource)

    this.setState({
      columns: newColumns.filter(item => { return !(item.dft_hidden === true && !item.title) }),
      dataSource: newDataSource
    })
  }

  render () {
    const { oldProps } = this.props
    const { columns, dataSource } = this.state

    // console.debug('columns==>', columns)
    // console.debug('dataSource==>', dataSource)

    return (
      <Table rowKey={r => r.id} {...oldProps} columns={columns} dataSource={dataSource} />
    )
  }
}

export default DiffTable
