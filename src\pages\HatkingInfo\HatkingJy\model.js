// eslint-disable-next-line no-unused-vars
import { getHatkingComboInfo, getHatkingCrucialInfo, getHatkingCrucialInfoNew, getHatkingCrucialInfoNew2, getHatkingDailyInfo, getHatkingModeStatsInfo, getHatkingModeStatsInfoNew, getHatkingModeStatsInfoNew2, getHatkingModouInfo, getHatkingUpgradeCrucialInfo, getHatkingPaidRangeInfo, getHatkingPeriodRangeInfo, getHatkingRoundStatInfo } from './api'

export default {
  namespace: 'hatkingJy',

  state: {
    crucialList: [],
    crucialListNew: [],
    crucialListNew2: [],
    dailyList: [],
    comboList: [],
    modouList: [],
    modeStatsList: [],
    modeStatsListNew: [],
    modeStatsListNew2: [],
    upgradeCrucialList: [],
    paidRangeList: [],
    periodRangeList: [],
    roundStatList: []
  },

  reducers: {
    updateCrucialInfoList (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      return {
        ...state,
        crucialList: payload
      }
    },
    updateCrucialInfoListNew (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      return {
        ...state,
        crucialListNew: payload
      }
    },
    updateCrucialInfoListNew2 (state, { payload, cbFunc }) {
      payload.sort((a, b) => b.date - a.date)
      for (var i = 0; i < payload.length; i++) {
        payload[i].betRewardRatio = 0
        payload[i].betRewardAmethyst += payload[i].fragCount
        if (payload[i].betRewardAmethyst > 0 && payload[i].betAmethyst > 0) {
          payload[i].betRewardRatio = payload[i].betRewardAmethyst / payload[i].betAmethyst * 100
          payload[i].betRewardRatio = payload[i].betRewardRatio.toFixed(2) + '%'
        }
      }
      cbFunc()
      return {
        ...state,
        crucialListNew2: payload
      }
    },
    updateDailyInfoList (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      return {
        ...state,
        dailyList: payload
      }
    },
    updateComboInfoList (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      return {
        ...state,
        comboList: payload
      }
    },
    updateModouInfoList (state, { payload }) {
      payload.sort(function (a, b) {
        if (b.date === a.date) {
          return a.itemType - b.itemType
        }

        return b.date - a.date
      })
      return {
        ...state,
        modouList: payload
      }
    },
    updateModeStatsList (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      for (var i = 0; i < payload.length; i++) {
        payload[i].betRebateRatio1 = 0
        payload[i].betRebateRatio2 = 0
        payload[i].betRebateRatio3 = 0
        if (payload[i].betAmethyst1 > 0 && payload[i].betRewardAmethyst1 > 0) {
          payload[i].betRebateRatio1 = payload[i].betRewardAmethyst1 / payload[i].betAmethyst1 * 100
          payload[i].betRebateRatio1 = payload[i].betRebateRatio1.toFixed(2) + '%'
        }
        if (payload[i].betAmethyst2 > 0 && payload[i].betRewardAmethyst2 > 0) {
          payload[i].betRebateRatio2 = payload[i].betRewardAmethyst2 / payload[i].betAmethyst2 * 100
          payload[i].betRebateRatio2 = payload[i].betRebateRatio2.toFixed(2) + '%'
        }
        if (payload[i].betAmethyst3 > 0 && payload[i].betRewardAmethyst3 > 0) {
          payload[i].betRebateRatio3 = payload[i].betRewardAmethyst3 / payload[i].betAmethyst3 * 100
          payload[i].betRebateRatio3 = payload[i].betRebateRatio3.toFixed(2) + '%'
        }
      }
      return {
        ...state,
        modeStatsList: payload
      }
    },
    updateModeStatsListNew (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      for (var i = 0; i < payload.length; i++) {
        payload[i].betRebateRatio1 = 0
        payload[i].betRebateRatio2 = 0
        payload[i].betRebateRatio3 = 0
        if (payload[i].betAmethyst1 > 0 && payload[i].winAmethyst1 > 0) {
          payload[i].betRebateRatio1 = payload[i].winAmethyst1 / payload[i].betAmethyst1 * 100
          payload[i].betRebateRatio1 = payload[i].betRebateRatio1.toFixed(2) + '%'
        }
        if (payload[i].betAmethyst2 > 0 && payload[i].winAmethyst2 > 0) {
          payload[i].betRebateRatio2 = payload[i].winAmethyst2 / payload[i].betAmethyst2 * 100
          payload[i].betRebateRatio2 = payload[i].betRebateRatio2.toFixed(2) + '%'
        }
        if (payload[i].betAmethyst3 > 0 && payload[i].winAmethyst3 > 0) {
          payload[i].betRebateRatio3 = payload[i].winAmethyst3 / payload[i].betAmethyst3 * 100
          payload[i].betRebateRatio3 = payload[i].betRebateRatio3.toFixed(2) + '%'
        }
      }
      return {
        ...state,
        modeStatsListNew: payload
      }
    },
    updateModeStatsListNew2 (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      for (var i = 0; i < payload.length; i++) {
        payload[i].betRebateRatio1 = 0
        payload[i].betRebateRatio2 = 0
        payload[i].betRebateRatio3 = 0
        if (payload[i].betAmethyst1 > 0 && payload[i].winAmethyst1 > 0) {
          payload[i].betRebateRatio1 = payload[i].winAmethyst1 / payload[i].betAmethyst1 * 100
          payload[i].betRebateRatio1 = payload[i].betRebateRatio1.toFixed(2) + '%'
        }
        if (payload[i].betAmethyst2 > 0 && payload[i].winAmethyst2 > 0) {
          payload[i].betRebateRatio2 = payload[i].winAmethyst2 / payload[i].betAmethyst2 * 100
          payload[i].betRebateRatio2 = payload[i].betRebateRatio2.toFixed(2) + '%'
        }
        if (payload[i].betAmethyst3 > 0 && payload[i].winAmethyst3 > 0) {
          payload[i].betRebateRatio3 = payload[i].winAmethyst3 / payload[i].betAmethyst3 * 100
          payload[i].betRebateRatio3 = payload[i].betRebateRatio3.toFixed(2) + '%'
        }
      }
      return {
        ...state,
        modeStatsListNew2: payload
      }
    },
    updateUpgradeCrucialList (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      for (var i = 0; i < payload.length; i++) {
        payload[i].rebateRatio = 0
        if (payload[i].betAmethyst > 0 && payload[i].betRewardAmethyst > 0) {
          payload[i].rebateRatio = payload[i].betRewardAmethyst / payload[i].betAmethyst * 100
          payload[i].rebateRatio = payload[i].rebateRatio.toFixed(2) + '%'
        }
      }
      return {
        ...state,
        upgradeCrucialList: payload
      }
    },
    updatePaidRangeList (state, { payload }) {
      payload.sort(function (a, b) {
        if (a.date === b.date) { return a.rangeId - b.rangeId }
        return b.date - a.date
      })
      for (var i = 0; i < payload.length; i++) {
        payload[i].betRewardRatio = 0
        if (payload[i].betAmethyst > 0 && payload[i].betRewardAmethyst > 0) {
          payload[i].betRewardRatio = payload[i].betRewardAmethyst / payload[i].betAmethyst * 100
          payload[i].betRewardRatio = payload[i].betRewardRatio.toFixed(2) + '%'
        }

        payload[i].betAmethyst = payload[i].betAmethyst / 1000
        payload[i].betRewardAmethyst = payload[i].betRewardAmethyst / 1000
      }

      return {
        ...state,
        paidRangeList: payload
      }
    },
    updatePeriodRangeList (state, { payload }) {
      payload.sort(function (a, b) {
        if (a.date === b.date) { return a.rangeId - b.rangeId }
        return b.date - a.date
      })
      for (var i = 0; i < payload.length; i++) {
        payload[i].betRewardRatio = 0
        if (payload[i].betAmethyst > 0 && payload[i].betRewardAmethyst > 0) {
          payload[i].betRewardRatio = payload[i].betRewardAmethyst / payload[i].betAmethyst * 100
          payload[i].betRewardRatio = payload[i].betRewardRatio.toFixed(2) + '%'
        }

        payload[i].betAmethyst = payload[i].betAmethyst / 1000
        payload[i].betRewardAmethyst = payload[i].betRewardAmethyst / 1000
      }
      return {
        ...state,
        periodRangeList: payload
      }
    },
    updateRoundStatList (state, { payload }) {
      payload.sort((a, b) => b.roundStart - a.roundStart)
      for (var i = 0; i < payload.length; i++) {
        payload[i].betAmethyst = payload[i].betAmethyst / 1000
        payload[i].winAmethyst = payload[i].winAmethyst / 1000
        payload[i].payAmethyst1 = payload[i].payAmethyst1 / 1000
        payload[i].expectAmethyst1 = payload[i].expectAmethyst1 / 1000
        payload[i].payAmethyst2 = payload[i].payAmethyst2 / 1000
        payload[i].expectAmethyst2 = payload[i].expectAmethyst2 / 1000
        payload[i].payAmethyst3 = payload[i].payAmethyst3 / 1000
        payload[i].expectAmethyst3 = payload[i].expectAmethyst3 / 1000
        payload[i].payAmethyst4 = payload[i].payAmethyst4 / 1000
        payload[i].expectAmethyst4 = payload[i].expectAmethyst4 / 1000
        payload[i].payAmethyst5 = payload[i].payAmethyst5 / 1000
        payload[i].expectAmethyst5 = payload[i].expectAmethyst5 / 1000
        payload[i].payAmethyst6 = payload[i].payAmethyst6 / 1000
        payload[i].expectAmethyst6 = payload[i].expectAmethyst6 / 1000
        payload[i].payAmethyst7 = payload[i].payAmethyst7 / 1000
        payload[i].expectAmethyst7 = payload[i].expectAmethyst7 / 1000
        payload[i].payAmethyst8 = payload[i].payAmethyst8 / 1000
        payload[i].expectAmethyst8 = payload[i].expectAmethyst8 / 1000
        payload[i].payAmethyst9 = payload[i].payAmethyst9 / 1000
        payload[i].expectAmethyst9 = payload[i].expectAmethyst9 / 1000
      }
      return {
        ...state,
        roundStatList: payload
      }
    }
  },

  effects: {
    * getCrucialInfoList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getHatkingCrucialInfo, payload)
      yield put({
        type: 'updateCrucialInfoList',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getCrucialInfoListNew ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getHatkingCrucialInfoNew, payload)
      yield put({
        type: 'updateCrucialInfoListNew',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getCrucialInfoListNew2 ({ payload, cbFunc }, { call, put }) {
      const { data: { ret } } = yield call(getHatkingCrucialInfoNew2, payload)
      yield put({
        type: 'updateCrucialInfoListNew2',
        payload: Array.isArray(ret) ? ret : [],
        cbFunc: cbFunc
      })
    },
    * getDailyInfoList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getHatkingDailyInfo, payload)
      yield put({
        type: 'updateDailyInfoList',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getComboInfoList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getHatkingComboInfo, payload)
      yield put({
        type: 'updateComboInfoList',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getModouInfoList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getHatkingModouInfo, payload)
      yield put({
        type: 'updateModouInfoList',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getModeStatsList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getHatkingModeStatsInfo, payload)
      yield put({
        type: 'updateModeStatsList',
        payload: Array.isArray(list) ? list : []
      })
    },
    * getModeStatsListNew ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getHatkingModeStatsInfoNew, payload)
      yield put({
        type: 'updateModeStatsListNew',
        payload: Array.isArray(list) ? list : []
      })
    },
    * getModeStatsListNew2 ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getHatkingModeStatsInfoNew2, payload)
      yield put({
        type: 'updateModeStatsListNew2',
        payload: Array.isArray(list) ? list : []
      })
    },
    * getUpgradeCrucialList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getHatkingUpgradeCrucialInfo, payload)
      yield put({
        type: 'updateUpgradeCrucialList',
        payload: Array.isArray(list) ? list : []
      })
    },
    * getPaidRangeList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getHatkingPaidRangeInfo, payload)
      yield put({
        type: 'updatePaidRangeList',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getPeriodRangeList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getHatkingPeriodRangeInfo, payload)
      yield put({
        type: 'updatePeriodRangeList',
        payload: Array.isArray(ret) ? ret : []
      })
    },
    * getRoundStatList ({ payload }, { call, put }) {
      const { data: { ret } } = yield call(getHatkingRoundStatInfo, payload)
      yield put({
        type: 'updateRoundStatList',
        payload: Array.isArray(ret) ? ret : []
      })
    }
  }
}
