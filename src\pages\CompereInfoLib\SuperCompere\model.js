import { message, Modal } from 'antd'
import {
  getLists, modSuperCompereWhite, whiteListAddOrDelete, getRemarkInfo
  , updateRemarkInfo, listCompere, refreshCompereList, getRemarkHistory, getCompereDetailInfo, cancelSuperCompere
} from './api'
import { checkUid } from '@/utils/common'

export default {
  namespace: 'supperCompere',

  state: {
    list: [],
    remarkInfo: {},
    totalSize: 10,
    exportTotalList: [],
    remarkHistoryRecords: [],
    currentPage2: 1,
    currentSize2: 0,
    displayData: [],
    readyDisplayDataLength: 0,
    compereDetailInfo: {}
  },

  reducers: {
    displayList (state, { payload, total }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        list: payload,
        totalSize: total
      }
    },

    remarkRecords (state, { payload }) {
      console.log(payload)
      return {
        ...state,
        remarkHistoryRecords: payload
      }
    },

    getRemark (state, { payload }) {
      return {
        ...state,
        remarkInfo: payload
      }
    },

    // 更新单个state成员的值
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return {
        ...state,
        [name]: newValue,
        readyDisplayDataLength: newValue.length
      }
    }
  },

  effects: {
    * getRemarkInfo ({ payload }, { call, put }) {
      console.log('payload=', payload)
      const { cbFunc } = payload
      // eslint-disable-next-line camelcase
      let { data: { code, msg, remark_info } } = yield call(getRemarkInfo, payload)
      console.log(code, msg, remark_info, cbFunc)
      if (code !== 0) {
        message.error({ content: '查询备注失败' })
      }
      cbFunc(remark_info)
      yield put({
        type: 'getRemark',
        payload: remark_info
      })
    },

    * updateRemarkInfo ({ payload }, { call, put }) {
      const { cbFunc } = payload

      let { data: { code, msg } } = yield call(updateRemarkInfo, payload)
      console.log(code, msg)
      if (code !== 0) {
        message.error({ content: '备注更新失败' })
      }

      message.success({ content: '修改备注成功' })
      cbFunc()
    },

    * listCompere ({ payload }, { call, put }) {
      if (!payload.page) {
        payload.page = 1
      }
      if (!payload.size) {
        payload.size = 10
      }
      let { data: { list, total, code, msg } } = yield call(listCompere, payload)
      if (code !== 0) {
        message.warning(msg)
      }
      let dataNew = Array.isArray(list) ? list : []
      for (let i = 0; i < dataNew.length; i++) {
        dataNew[i].idx = i + 1
      }
      yield put({
        type: 'displayList',
        payload: dataNew,
        total
      })
    },
    * refreshCompereList ({ payload }, { call, put }) {
      let { data: { code, msg } } = yield call(refreshCompereList, payload)
      if (code !== 0) {
        message.warning(msg)
        return
      }
      yield put({
        type: 'listCompere',
        payload: { uid: payload.uids, ds: payload.ds }
      })
    },

    * getRemarkHistory ({ payload }, { call, put }) {
      console.log('payload=', payload)

      // eslint-disable-next-line camelcase
      let { data: { code, msg, records } } = yield call(getRemarkHistory, payload)
      console.log(code, msg, records)
      if (code !== 0) {
        message.error({ content: '查询备注历史失败' })
      }

      yield put({
        type: 'remarkRecords',
        payload: records
      })
    },

    // 请求并刷新列表数据
    * getReadySuperCompereList ({ payload }, { select, call, put }) {
      let resp = yield call(getLists, true)

      const dataName = 'readyDisplayData'

      const { data } = resp
      if (data === undefined) {
        Modal.error({ content: '获取数据失败，请检查控制台' })
        console.error('getReadySuperCompereList() get data error: response=', resp)
        return
      }
      const { status, msg, list } = { status: 0, msg: '', list: data }
      if (status !== 0) {
        Modal.error({ content: '获取数据有误，请检查控制台' })
        console.error('getReadySuperCompereList() status=' + status + ' msg=' + msg)
        return
      }

      if (list === null) {
        message.warning('数据为空')
        yield put({
          type: 'updateState',
          payload: { name: dataName, newValue: [] }
        })
        return
      }

      let dataList = list
      if (!Array.isArray(dataList)) {
        let tList = []
        for (let uidKey in dataList) {
          let item = dataList[uidKey]
          // 计算成为超主天数
          item.start_time = item['approaching_super_start']
          item.live_days = Math.round(((new Date().valueOf() / 1000) - item.start_time) / 86400)
          tList.push(item)
        }
        dataList = tList
      }
      let sidList = dataList
      sidList.sort(function (a, b) { return (a.uid > b.uid ? 1 : -1) })

      yield put({
        type: 'updateState',
        payload: { name: dataName, newValue: sidList }
      })
    },

    // 添加灰度白名单
    * addWhiteList ({ payload }, { call, put }) {
      const { uid, readySuper, callback } = payload // callback 是用于清空表单的回调函数
      console.log('AddWhiteList, uid:', uid, ', readySuper: ', readySuper)
      if (!checkUid(uid)) {
        return
      }
      yield put({
        type: 'updateState',
        payload: { name: 'updating', newValue: true }
      })
      let resp = yield call(whiteListAddOrDelete, 'ADD', readySuper, uid)

      console.log('添加准超级主持白名单结果：', resp)

      const { data } = resp
      if (data === undefined) {
        Modal.error({ content: '发生错误，请检查控制台' })
        console.error('[添加超级主持白名单错误] response=', resp)
        yield put({
          type: 'updateState',
          payload: { name: 'updating', newValue: false }
        })
        return
      }
      const { status, msg } = data
      if (status === 0) {
        message.success('添加成功')
        callback(readySuper)

        yield put({ // 更新列表
          type: 'getReadySuperCompereList',
          payload: { readySuper: readySuper }
        })
      } else {
        Modal.error({ content: '操作失败： status=' + status + ' msg=' + msg })
        console.error('[添加超级主持白名单失败] response=', resp)
      }
      yield put({
        type: 'updateState',
        payload: { name: 'updating', newValue: false }
      })
    },
    // 删除灰度白名单
    * delWhiteList ({ payload }, { call, put }) {
      const { uid, readySuper, callback } = payload
      if (!checkUid(uid)) {
        return
      }
      if (typeof readySuper !== 'boolean') {
        message.error('unexpect params: readySuper=', readySuper)
        return
      }
      let resp = yield call(whiteListAddOrDelete, 'DELETE', readySuper, uid)
      const { data } = resp
      if (data === undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[删除白名单错误]: response=', resp)
        yield put({
          type: 'updateState',
          payload: { name: 'updating', newValue: false }
        })
        return
      }
      const { status, msg } = data
      if (status === 0) {
        message.success('删除成功')
        yield put({ // 更新列表
          type: 'getReadySuperCompereList',
          payload: { readySuper: readySuper }
        })
        if (callback) {
          callback()
        }
      } else {
        Modal.error({ content: '删除失败：status=' + status + ' msg=' + msg })
        console.error('delWhiteList() resp=', resp)
      }
    },

    // 升级为正式的超级主持
    * upgradeAsSuperCompere ({ payload }, { call, put }) {
      const { uid, readySuper } = payload
      if (!checkUid(uid)) {
        return
      }
      let resp = yield call(whiteListAddOrDelete, 'UPGRADE', readySuper, uid)
      const { data } = resp
      if (data === undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[添加准超级主持白名单错误]: response=', resp)
        yield put({
          type: 'updateState',
          payload: { name: 'updating', newValue: false }
        })
        return
      }
      const { status, msg } = data
      if (status === 0) {
        message.success('添加成功成功')
        yield put({ // 更新列表
          type: 'getReadySuperCompereList',
          payload: { readySuper: true }
        })
      } else {
        Modal.error({ content: '添加失败：status=' + status + ' msg=' + msg })
        console.error('upgradeAsSuperCompere() resp=', resp)
      }
    },

    // 修改特权
    * modSuperCompereWhite ({ payload }, { call, put }) {
      const { uid, privilege } = payload
      const { cbFunc } = payload

      if (!checkUid(uid)) {
        return
      }
      let resp = yield call(modSuperCompereWhite, uid, privilege)
      const { data } = resp
      if (data === undefined) {
        Modal.warn({ content: '发生错误, 请检查控制台' })
        console.error('[修改超级主持白名单错误]: response=', resp)
        yield put({
          type: 'updateState',
          payload: { name: 'updating', newValue: false }
        })
        return
      }
      const { status, msg } = data
      if (status === 0) {
        message.success('操作成功')
        cbFunc()

        yield put({ // 更新列表
          type: 'getReadySuperCompereList',
          payload: { readySuper: false }
        })
      } else {
        Modal.error({ content: '更新失败：status=' + status + ' msg=' + msg })
        console.error('modSuperCompereWhite() resp=', resp)
      }
    },

    * getCompereDetailInfo ({ payload }, { call, put }) {
      try {
        let { data: { data, status, msg } } = yield call(getCompereDetailInfo, payload)

        if (status !== 0) {
          message.error(msg)
          return
        }
        yield put({
          type: 'updateState',
          payload: { name: 'compereDetailInfo', newValue: data }
        })
      } catch (e) {
        message.error('exception: ', e)
      }
    },

    * cancelSuperCompere ({ payload }, { call, put }) {
      try {
        let { data: { status, msg } } = yield call(cancelSuperCompere, payload)

        if (status !== 0) {
          message.error(msg)
        } else {
          message.info('已发起申请, 请到通用审批页面审批')
        }
      } catch (e) {
        message.error('exception: ', e)
      }
    }
  }
}
