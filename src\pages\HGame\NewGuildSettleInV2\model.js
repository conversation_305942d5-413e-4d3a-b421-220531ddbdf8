import * as api from './api'
import { message } from 'antd'

export default {
  namespace: 'newGuildSettleInV2',

  state: {
    listSuperCrystal: [],
    listSuperCrystalHistory: [],
    tableLoadingSuperCrystalHistory: false,
    detailSuperCrysta: {},
    detailLoadingSuperCrysta: false
  },

  reducers: {
    // 单个修改某个state成员
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },

    displaySuperCrystalList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        listSuperCrystal: payload
      }
    },

    displaySuperCrystalHistoryList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        listSuperCrystalHistory: payload
      }
    },

    updateDetailSuper<PERSON>rysta (state, { payload }) {
      return {
        ...state,
        detailSuperCrysta: payload
      }
    }
  },

  effects: {
    * listSuperCrystal ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listSuperCrystal, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      for (let i = 0; i < data.length; i++) {
        data[i].idx = i + 1
        data[i].guildCooperationAgreementURL = data[i].guildCooperationAgreementURL.replace('http://', 'https://')
        data[i].threePartiesAgreementURL = data[i].threePartiesAgreementURL.replace('http://', 'https://')
        data[i].guildManagerURL = data[i].guildManagerURL.replace('http://', 'https://')
      }
      yield put({
        type: 'displaySuperCrystalList',
        payload: data
      })
    },

    * listSuperCrystalHistory ({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingSuperCrystalHistory', newValue: true }
      })
      let { data: { data, status, msg } } = yield call(api.listSuperCrystalHistory, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      yield put({
        type: 'displaySuperCrystalHistoryList',
        payload: data
      })
      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingSuperCrystalHistory', newValue: false }
      })
    },

    * detailSuperCrysta ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.detailSuperCrysta, payload)
      console.log(status, msg)
      data = data || {}
      data.guildCooperationAgreementURL = data.guildCooperationAgreementURL?.replace('http://', 'https://') || ''
      data.threePartiesAgreementURL = data.threePartiesAgreementURL?.replace('http://', 'https://') || ''
      data.guildManagerURL = data.guildManagerURL?.replace('http://', 'https://') || ''
      
      console.log(data)
      yield put({
        type: 'updateDetailSuperCrysta',
        payload: data
      })
    },

    * approvalSuperCrysta ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.approvalSuperCrysta, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listSuperCrystal',
        payload: {}
      })
    },

    * uploadSuperSrysta ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.uploadSuperSrysta, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listSuperCrystal',
        payload: {}
      })
    },

    * inviteSuperCrysta ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.inviteSuperCrysta, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        message.success('操作成功')
      }
    },

    * getOwInfo ({ payload, cbFn }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.getOwInfo, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        if (cbFn && data) {
          cbFn(data)
        }
      }
    }
  }
}
