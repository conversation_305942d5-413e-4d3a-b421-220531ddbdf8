import { connect } from 'dva'
import React, { Component } from 'react'
import { <PERSON><PERSON>, Col, Divider, Form, Input, message, Modal, Popconfirm, Row, Select, Table, Tooltip, Tree } from 'antd'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import MonacoEditor from 'react-monaco-editor'
import { copyToClip, formatOptions, optionsToMultiLineString, parseOptionsFromMultiLineString } from '@/utils/common'
import { CodeTwoTone, CopyTwoTone, DeleteTwoTone, EditTwoTone, PlusSquareTwoTone } from '@ant-design/icons'
import { configTypeOptions, elementTypeOptions, elementTypeOptionsValidator, elementTypeValidator, formatTimestamp, functionTypeOptions, functionTypeWithoutInnerOptions, validatorTagValidator, varNameValidator } from '../common'

const namespace = 'RuleFunctionManage'
const defaultPageSize = 10

@connect(({ RuleFunctionManage }) => ({
  model: RuleFunctionManage
}))

class RuleFunctionManage extends Component { // 默认页面组件，不需要修改
  constructor (props) {
    super(props)
    this.initState()
    this.searchHandle()
    this.loadCommonSelectOptions()
  }

  initState = () => {
    this.state = {
      pagination: {
        pageSize: defaultPageSize,
        total: 0,
        current: 1,
        defaultCurrent: 1,
        pageSizeOptions: [10, 20, 50, 100],
        showSizeChanger: true,
        onChange: (page, pageSize) => {
          this.pageChange(page, pageSize)
        },
        showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
      }
    }
  }

  updatePagination = (page, pageSize, total) => {
    const { pagination } = this.state
    pagination.current = page || 1
    pagination.pageSize = pageSize || defaultPageSize
    if (total !== undefined) {
      pagination.total = total
    }
    this.setState({ pagination: pagination })
    return pagination
  }

  // 分页信息变更
  pageChange = (page, pageSize, total) => {
    let pagination = this.updatePagination(page, pageSize, total)
    this.searchHandle(pagination.current, pagination.pageSize)
  }

  // 获取当前查询条件
  getQueryCondition = (page, size, cond) => {
    const { pagination } = this.state
    page = page || pagination.current || 1
    size = size || pagination.pageSize || defaultPageSize
    let pageInfo = {
      pageNo: page,
      pageSize: size
    }
    if (!cond) {
      return pageInfo
    }

    return Object.assign(cond, pageInfo)
  }

  loadCommonSelectOptions = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/listAllBusiness`
    })
  }

  // 处理查询事件
  searchHandle = (page, size, cond) => {
    const { dispatch } = this.props

    let query = this.getQueryCondition(page, size, cond)
    if (!query) {
      return
    }
    let self = this
    dispatch({
      type: `${namespace}/pageListRuleFunction`,
      payload: query,
      callback: (data) => {
        self.updatePagination(query.pageNo || 1, query.pageSize || defaultPageSize, data ? data.total : 0)
      }
    })
  }

  onQueryClick = (values) => {
    const { pagination } = this.state
    let size = pagination.pageSize || defaultPageSize
    this.searchHandle(1, size, values)
  }

  // 规则函数表头
  getRuleFunctionColumns = () => {
    const { model: { businessList } } = this.props
    return [
      { title: '序号', dataIndex: 'index', align: 'left' },
      { title: '业务', dataIndex: 'businessID', align: 'left', render: (v) => formatOptions(v, businessList, '-', 'id', 'name') },
      {
        title: '调用名',
        dataIndex: 'id',
        align: 'left',
        render: (v) => {
          let expression = v + '(ctx)'
          return (<><span>{v}</span><CopyTwoTone style={{ marginLeft: 5 }} size={'small'} onClick={() => copyToClip(expression, expression)} /></>)
        }
      },
      {
        title: '函数名',
        dataIndex: 'name',
        align: 'left',
        render: (v, record) => {
          return <Tooltip placement={'bottomLeft'} title={!record.desc || record.desc === '-' ? v : record.desc}>
            <div>{v || record.desc}</div>
          </Tooltip>
        }
      },
      { title: '配置类型', dataIndex: 'configType', align: 'left', render: (v) => formatOptions(v, configTypeOptions) },
      { title: '函数类型', dataIndex: 'type', align: 'left', render: (v) => formatOptions(v, functionTypeOptions) },
      { title: '返回值类型', dataIndex: 'returnType', align: 'left', render: (v) => formatOptions(v.dataType, elementTypeOptions) },
      { title: '更新时间', dataIndex: 'updateTime', align: 'left', render: (v) => formatTimestamp(v) },
      { title: '操作人', dataIndex: 'lastOperator', align: 'left' },
      {
        title: '操作',
        key: 'operation',
        align: 'left',
        render: (text, item) => (
          <span>
            <a size='small' type='primary' onClick={() => this.showEditRuleFunctionModal(item, 'edit')}>编辑</a>
            <span hidden={item.type !== 'JS'}>
              <Divider type='vertical' /> {/* 分割线 */}
              <a hidden={item.type !== 'JS'} size='small' type='primary' onClick={() => this.showEditJavascriptModal(item, 'direct')}>编辑脚本</a>
            </span>
            <span hidden={item.type !== 'JS'}>
              <Divider type='vertical' /> {/* 分割线 */}
              <Popconfirm hidden={item.configType !== 'CUSTOM'} title='确认删除?' onConfirm={() => this.onDeleteRuleFunction(item)}><a style={{ color: 'red' }}>删除</a></Popconfirm>
            </span>
          </span>)
      }
    ]
  }

  // 给元素添加key属性
  resolveElementTypeKey = (node, keyPrefix) => {
    if (!node) {
      return
    }
    let self = this
    keyPrefix = keyPrefix || ''
    if (!node.key) {
      node.key = keyPrefix ? keyPrefix + '.' + node.name : node.name
    }
    // item.title = item.desc
    node.title = ''
    if (node.fields && node.fields.length > 0) {
      node.fields.forEach((subItem) => {
        self.resolveElementTypeKey(subItem, node.key)
      })
    }
    node.children = node.fields

    let expression = node.key + '(ctx)'
    let idx = node.key.indexOf('.')
    if (idx > 0) {
      expression = 'ognl(' + node.key.substring(0, idx) + '(ctx), \'' + node.key.substring(idx + 1, node.key.length) + '\')'
    }
    node.expression = expression
  }

  // 展开行，显示返回值信息
  expandedRowRender = (item) => {
    // const fieldNames = { title: 'name', key: 'key', children: 'fields' }
    item.returnType.key = item.id
    this.resolveElementTypeKey(item.returnType)
    let self = this
    const treeData = [item.returnType]
    const titleRender = (node) => {
      let desc = node.desc
      if (desc && desc.length > 18) {
        desc = desc.substring(0, 18) + '... ...'
      }
      return (<div hidden={node.deleted}>
        <div style={{ width: '120px', marginLeft: '20', float: 'left' }}>
          <CopyTwoTone size={'small'} onClick={() => copyToClip(node.expression, node.expression)} />
          <EditTwoTone style={{ marginLeft: '10px' }} size={'small'} onClick={() => self.showEditFiledModal(node === item.returnType, node, item, 'edit')} />
          <PlusSquareTwoTone hidden={node.dataType !== 'object'} style={{ marginLeft: '10px' }} size={'small'} onClick={() => self.showEditFiledModal(node === item.returnType, node, item, 'add')} />
          <DeleteTwoTone hidden={node.configType !== 'CUSTOM' || node === item.returnType} style={{ marginLeft: '10px' }} size={'small'} onClick={() => self.deleteElementField(node === item.returnType, node, item)} />
          {/* <DeleteTwoTone style={{ marginLeft: '10px' }} size={'small'} onClick={() => self.deleteElementField(node === item.returnType, node, item)} /> */}
        </div>
        <div style={{ width: '200px', marginLeft: '20', float: 'left', color: !node.configType ? 'red' : 'black' }}>{node === item.returnType ? node.key : node.name}</div>
        <div style={{ width: '80px', marginLeft: '20', float: 'left' }}>{formatOptions(node.dataType, elementTypeOptions)}</div>
        <Tooltip placement='bottomLeft' title={node.desc}>
          <div style={{ width: '350px', marginLeft: '20', float: 'left' }}>{desc}</div>
        </Tooltip>
        <div style={{ width: '300px', marginLeft: '20', float: 'left' }} hidden={!node.options || node.options.length === 0}>
          可选项：<Select size={'small'} style={{ width: 200 }} options={node.options || []} />
        </div>
      </div>)
    }

    return <>{<Tree treeData={treeData} titleRender={titleRender} />}</>
  }

  deleteElementField = (top, item, record) => {
    console.log('准备删除属性：', top, item, record)
    // 找到上级对象
    let keys = item.key.split('.')
    if (keys.length < 2) {
      message.error('顶级属性不允许删除')
      return
    }
    let field = record.returnType
    let fieldIndex = 0
    let parent = record.returnType
    for (let i = 1; i < keys.length; i++) {
      let key = keys[i]
      let fields = field.fields
      if (!fields || fields.length < 1) {
        message.error('属性不存在或者已被删除')
        return
      }
      let found = false
      for (let j = 0; j < fields.length; j++) {
        if (fields[j].name === key) {
          field = fields[j]
          fieldIndex = j
          found = true
          break
        }
      }
      if (!found) {
        message.error('属性不存在或者已被删除')
        return
      }

      if (i === keys.length - 2) {
        parent = field
      }
    }

    // 标记删除
    field.deleted = true
    parent.fields.splice(fieldIndex, 1)

    let self = this
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/updateRuleFunction`,
      payload: record,
      callback: (rsp) => {
        if (rsp && rsp.status === 0) {
          self.setState({ editFieldModalVisible: false, editField: undefined, editFieldTop: undefined, expandRecord: undefined })
        } else {
          message.error('更新失败: ' + rsp.msg)
        }
      }
    })
  }

  showEditFiledModal = (top, item, record, mode) => {
    console.log('showEditFieldModal', top, item)
    this.setState({ editFieldModalVisible: true, editField: item, editFieldTop: top, expandRecord: record, editFieldMode: mode || 'edit' })
  }

  closeEditFiledModal = () => {
    this.setState({ editFieldModalVisible: false })
  }

  onEditFieldOk = () => {
    this.formRefEditField.submit()
  }

  onEditFieldFormSubmitted = (values) => {
    const { editField, expandRecord, editFieldMode } = this.state

    let options = []
    if (values.dataType !== 'object') {
      options = parseOptionsFromMultiLineString(values.options)
    }

    if (editFieldMode === 'add') { // 新值子属性，追加到 editField 最后一个子节点中
      let field = {
        name: values.name.trim(),
        desc: values.desc ? values.desc.trim() : '',
        dataType: values.dataType,
        validatorTag: values.validatorTag ? values.validatorTag.trim() : '',
        options: options,
        configType: 'CUSTOM' // 添加都是自定义的
      }
      if (!editField.fields) {
        editField.fields = [field]
      } else {
        editField.fields.push(field)
      }
    } else { // 编辑模式
      editField.options = options
      editField.desc = values.desc
      editField.dataType = values.dataType
      editField.validatorTag = values.validatorTag
    }

    let self = this

    console.log('待更新： ', expandRecord)
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/updateRuleFunction`,
      payload: expandRecord,
      callback: (rsp) => {
        if (rsp && rsp.status === 0) {
          self.setState({ editFieldModalVisible: false, editField: undefined, editFieldTop: undefined, expandRecord: undefined })
        } else {
          message.error('更新失败: ' + rsp.msg)
        }
      }
    })
  }

  renderEditField = () => {
    const { editFieldModalVisible, editField, editFieldMode } = this.state
    let editFieldModalTitle = '编辑'
    if (editField) {
      editFieldModalTitle = '编辑属性 [' + editField.key + ']'
    }
    return (
      <Modal width={1000} centered title={editFieldModalTitle} visible={editFieldModalVisible} onCancel={this.closeEditFiledModal} onOk={this.onEditFieldOk}>
        <Form onFinish={this.onEditFieldFormSubmitted} labelCol={{ span: 3 }} ref={form => {
          this.formRefEditField = form
          if (form) {
            if (editFieldMode === 'edit' && editField) {
              form.setFieldsValue(editField)
              form.setFieldsValue({
                options: optionsToMultiLineString(editField.options)
              })
            }
            if (editFieldMode === 'add') {
              form.resetFields()
            }
          }
        }}>
          <Form.Item hidden={editFieldMode !== 'add'} name='name' label={'属性名称'} required rules={varNameValidator}>
            <Input placeholder='属性名称' />
          </Form.Item>
          <Form.Item hidden={editFieldMode === 'add'} name='key' label={'属性路径'} required>
            <Input placeholder='属性路径' disabled={editFieldMode !== 'add'} />
          </Form.Item>
          <Form.Item hidden={editFieldMode === 'add'} name='expression' label={'访问表达式'}>
            <Input placeholder='访问表达式' disabled={editFieldMode !== 'add'} />
          </Form.Item>
          <Form.Item name='desc' label={'说明'} rules={[{ required: true, type: 'string', min: 1, max: 100, message: '不允许为空' }]} required>
            <Input placeholder='属性说明' />
          </Form.Item>
          <Form.Item name='dataType' label={'数据类型'} required rules={elementTypeValidator}>
            <Select placeholder='请选中类型' options={elementTypeOptions} filterOption disabled={editFieldMode !== 'add' && editField && ((editField.fields && editField.fields.length > 0) || editField.configType !== 'CUSTOM')} />
          </Form.Item>

          <Form.Item name='options' label={'可选项'} rules={[{
            validator: (_, value) => {
              return parseOptionsFromMultiLineString(value) !== false ? Promise.resolve() : Promise.reject(new Error('可选项格式错误，请使用格式 [选项值:选项说明]'))
            }
          }]} tooltip={{ placement: 'right', title: '格式 [选项值:选项说明], 多个选项换行分隔' }}>
            <Input.TextArea rows={3} placeholder='可选项' />
          </Form.Item>

          <Form.Item name='validatorTag' label={'验证标签'} rules={validatorTagValidator} tooltip={{
            placement: 'right',
            title: <div style={{ marginTop: 5 }}>
              <li><span style={{ color: 'red' }}>示例(多个规则用空格分开):</span> vLen:"(1,10]" vRange:"[0,100]" vIn:"1,2,3" vRegex:"[0-9]+"</li>
              <li><span style={{ color: 'red' }}>vLen:</span> 属性长度限制，一般用于校验字符串、map、slice 等可以用 len 计算长度的属性：[a,b] => a &lt;= len(inputted) &lt;= b</li>
              <li><span style={{ color: 'red' }}>vRange:</span> 范围限制，用于校验数字类型属性：[a,b] => a &lt;= inputted &lt;= b</li>
              <li><span style={{ color: 'red' }}>vIn:</span> 有限值类型限制，限制属性只能是给定的值, 支持基本类型属性：vIn:"1,2,3" 表示输入值只能是 1,2,3</li>
              <li><span style={{ color: 'red' }}>vRegex:</span> 正则表达式限制，作用在string类型属性：vRegex:"[0-9]+"</li>
            </div>
          }}>
            <Input placeholder='验证标签' />
          </Form.Item>
        </Form>
      </Modal>
    )
  }

  onQueryClick = (values) => {
    const { pageSize } = this.state
    let size = pageSize || 10
    this.searchHandle(1, size, values)
  }

  resolveFormRuleFunction = (values) => {
    const { editRuleFunction, editRuleFunctionMode } = this.state
    let updateItem = editRuleFunction || values
    if (editRuleFunctionMode === 'add') { // 添加模式
      updateItem = Object.assign(updateItem, values)
      // 需要特殊处理一下返回值
      updateItem.returnType = {
        name: updateItem.id, desc: updateItem.name, configType: 'CUSTOM', dataType: values.returnDataType, validatorTag: values.returnValidatorTag, fields: []
      }
      updateItem.type = 'JS'
      updateItem.confitType = 'CUSTOM'
    } else { // 编辑模式, 只能修改部分信息
      updateItem.desc = values.desc
      updateItem.name = values.name
    }
    if (updateItem.dataType === 'object') {
      updateItem.returnType.options = []
    } else {
      updateItem.returnType.options = parseOptionsFromMultiLineString(values.returnOptions)
    }
    return updateItem
  }

  onEditRuleFunctionFormSubmitted = (values) => {
    const { editRuleFunctionMode } = this.state
    let updateItem = this.resolveFormRuleFunction(values)
    console.log('最终更新信息：', updateItem)

    let self = this
    const { dispatch } = this.props

    const reqType = editRuleFunctionMode === 'add' ? `${namespace}/addRuleFunction` : `${namespace}/updateRuleFunction`

    dispatch({
      type: reqType,
      payload: updateItem,
      callback: (rsp) => {
        if (rsp && rsp.status === 0) {
          self.setState({ editRuleFunctionVisible: false, editRuleFunction: undefined })
          self.searchHandle()
        } else {
          message.error(editRuleFunctionMode + ' 失败: ' + rsp.msg)
        }
      }
    })
  }

  showEditRuleFunctionModal = (record, mode) => {
    console.log('showEditRuleFunctionModal', mode, record)
    if (this.formRefEditRuleFunction) {
      this.formRefEditRuleFunction.resetFields()
    }

    if (mode === 'add') {
      record = {
        id: '', name: '', desc: '', businessID: '', configType: 'CUSTOM', type: 'JS', returnDataType: '', returnOptions: '', returnValidatorTag: '', returnType: {}
      }
    } else {
      if (record.returnType) {
        let rt = record.returnType
        record.returnOptions = optionsToMultiLineString(rt.options)
        record.returnDataType = rt.dataType
        record.returnValidatorTag = rt.validatorTag
      }
    }

    if (this.formRefEditRuleFunction) {
      this.formRefEditRuleFunction.setFieldsValue(record)
    }

    this.setState({ editRuleFunctionVisible: true, editRuleFunction: record, editRuleFunctionMode: mode || 'edit' })
  }

  // 渲染编辑规则函数基础信息
  renderEditRuleFunction = () => {
    const { model: { businessList } } = this.props
    const { editRuleFunctionVisible, editRuleFunction, editRuleFunctionMode } = this.state
    let title = '添加函数'
    let editItem = editRuleFunction || {}
    if (editRuleFunctionMode !== 'add' && editRuleFunction && editRuleFunction.id) {
      title = '编辑函数 [' + editRuleFunction.id + ']'
    }
    return (
      <Modal width={800} centered title={title} visible={editRuleFunctionVisible}
        onCancel={() => this.setState({ editRuleFunctionVisible: false })}
        onOk={() => this.formRefEditRuleFunction.submit()}>
        <Form onFinish={this.onEditRuleFunctionFormSubmitted} labelCol={{ span: 4 }} ref={form => {
          if (!this.formRefEditRuleFunction) {
            form.setFieldsValue(editRuleFunction)
          }
          this.formRefEditRuleFunction = form
        }}>

          <Form.Item name='businessID' label={'业务'}>
            <Select placeholder='请选择业务' options={(businessList || []).map(v => {
              return { label: v.name, value: v.id }
            })} filterOption disabled={editRuleFunction && editRuleFunction.configType !== 'CUSTOM'} />
          </Form.Item>
          <Form.Item name='id' label={'调用名'} required rules={varNameValidator}>
            <Input placeholder='函数调用名' disabled={editRuleFunctionMode === 'edit'} />
          </Form.Item>
          <Form.Item name='name' label={'函数名'} required rules={[{ required: true, type: 'string', min: 1, max: 100, message: '不允许为空' }]}>
            <Input placeholder='函数名' />
          </Form.Item>
          <Form.Item label={'函数类型'}>
            <Row>
              <Col>
                <Form.Item name='type' required>
                  <Select style={{ width: 550 }} placeholder='请选择类型' options={functionTypeWithoutInnerOptions} filterOption
                    disabled={editRuleFunctionMode !== 'add' && editRuleFunction && editRuleFunction.configType !== 'CUSTOM'} />
                </Form.Item>
              </Col>
              <Col>
                <CodeTwoTone size={'small'} style={{ marginLeft: 20, fontSize: '32px' }} hidden={editRuleFunction && editRuleFunction.type === 'INNER'} onClick={(c) => this.showEditJavascriptModal(editItem)} />
              </Col>
            </Row>
          </Form.Item>
          <Form.Item name='desc' label={'函数说明'}>
            <Input.TextArea placeholder='函数说明' />
          </Form.Item>
          <Form.Item name='returnDataType' label={'数据类型'} required rules={elementTypeValidator}>
            <Select placeholder='请选中类型' options={elementTypeOptions} filterOption
              disabled={editRuleFunctionMode !== 'add' && editRuleFunction && editRuleFunction.configType !== 'CUSTOM'} />
          </Form.Item>

          <Form.Item name='returnOptions' label={'返回值可选项'} rules={elementTypeOptionsValidator} tooltip={{ placement: 'topLeft', title: '格式 [选项值:选项说明], 多个选项换行分隔' }}>
            <Input.TextArea rows={3} placeholder='可选项' />
          </Form.Item>
        </Form>
      </Modal>
    )
  }

  showEditJavascriptModal = (record, mode) => {
    if (this.scriptEditor) {
      console.log('====> ', record, mode, this.scriptEditor)
      this.scriptEditor.setValue(record ? record.script || '' : '')
      this.scriptEditor.focus()
    }
    this.setState({ editScriptVisible: true, editRuleFunction: record, editJsMode: mode || 'edit' })
  }

  onDeleteRuleFunction = (item) => {
    console.log('准备删除：', item)
    let self = this
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/removeRuleFunction`,
      payload: { id: item.id },
      callback: (rsp) => {
        if (rsp && rsp.status === 0) {
          self.setState({ editScriptVisible: false, editRuleFunction: undefined })
          self.searchHandle()
        } else {
          message.error('更新失败: ' + rsp.msg)
        }
      }
    })
  }

  onEditJavascriptOk = () => {
    const { editRuleFunction, editJsMode } = this.state

    if (editJsMode === 'direct') { // 直接编辑代码模式，直接返回
      // 间接模式，提交表单
      let self = this
      const { dispatch } = this.props

      dispatch({
        type: `${namespace}/updateRuleFunction`,
        payload: editRuleFunction,
        callback: (rsp) => {
          if (rsp && rsp.status === 0) {
            self.setState({ editScriptVisible: false, editRuleFunction: undefined })
          } else {
            message.error('更新失败: ' + rsp.msg)
          }
        }
      })
      return
    }
    this.setState({ editScriptVisible: false })
    this.formRefEditRuleFunction.submit()
  }

  onEditJavascriptChange = (code) => {
    // console.log('code: \n', code)
    const { editRuleFunction } = this.state
    if (editRuleFunction) {
      editRuleFunction.script = code.trim()
    }
  }

  // 编辑JS 脚本
  renderEditJavascript = () => {
    const { editScriptVisible, editRuleFunction } = this.state

    const meOptions = {
      selectOnLineNumbers: true,
      renderSideBySide: true, // 对比, 需要 original(旧代码)，value（新代码）
      minimap: {
        enabled: false
      }
    }

    let self = this

    return (
      <Modal zIndex={10000} width={1200} title={'编辑代码'} visible={editScriptVisible} onCancel={() => {
        this.setState({ editScriptVisible: false })
      }} onOk={this.onEditJavascriptOk}>
        <div style={{ border: '1px solid #d1d1d1', borderRadius: 1, width: 'calc(100% - 10px)', height: 350 }}>
          <MonacoEditor
            height='100%'
            width='100%'
            language='javascript'
            theme='vs'
            value={editRuleFunction ? editRuleFunction.script : ''}
            defaultValue={''}
            options={meOptions}
            editorDidMount={(editor) => {
              self.scriptEditor = editor
              editor.focus()
            }}
            onChange={this.onEditJavascriptChange}
          />
        </div>
      </Modal>
    )
  }

  // 渲染函数
  render () {
    const { route, model: { dataList, businessList } } = this.props
    const columns = this.getRuleFunctionColumns()
    const { pagination } = this.state

    return (
      <>
        <PageHeaderWrapper title={route.name}>
          <Row style={{ marginBottom: '1em' }}>
            <Form layout={'inline'} ref={form => {
              this.formRefQuery = form
            }} onFinish={this.onQueryClick}>
              <Form.Item name={'keyword'} label={'关键字'}>
                <Input placeholder='keyword' style={{ width: 150 }} allowClear />
              </Form.Item>

              <Form.Item name={'businessID'} label={'归属业务'}>
                <Select placeholder='请选择归属业务' style={{ width: 120 }} options={(businessList || []).map(v => {
                  return { label: v.name, value: v.id }
                })} onChange={() => this.formRefQuery.submit()} filterOption allowClear />
              </Form.Item>

              <Form.Item name={'configType'} label={'配置类型'}>
                <Select placeholder='请选择配置类型' style={{ width: 120 }} options={configTypeOptions} onChange={() => this.formRefQuery.submit()} filterOption allowClear />
              </Form.Item>

              <Form.Item name={'type'} label={'函数类型'}>
                <Select placeholder='请选择函数类型' style={{ width: 120 }} options={functionTypeOptions} onChange={() => this.formRefQuery.submit()} filterOption allowClear />
              </Form.Item>

              <Form.Item name={'returnType'} label={'返回值类型'}>
                <Select placeholder='请选择返回值类型' style={{ width: 120 }} options={elementTypeOptions} onChange={() => this.formRefQuery.submit()} filterOption allowClear />
              </Form.Item>

              <Button type='primary' htmlType='submit'>查询</Button>
              <Divider type={'vertical'} />
              <Button type='primary' onClick={() => {
                this.formRefQuery.resetFields()
                this.formRefQuery.submit()
              }}>重置</Button>
              <Divider type={'vertical'} />
              <Button type={'primary'} onClick={() => this.showEditRuleFunctionModal({}, 'add')}>添加</Button>
            </Form>
          </Row>
          <Row style={{ marginBottom: '1em' }}>
            <Col span={24}>
              <Table columns={columns}
                dataSource={dataList}
                size='small'
                pagination={pagination}
                showSorterTooltip={false}
                expandedRowRender={this.expandedRowRender}
                rowKey={record => record.id}
              />
            </Col>
          </Row>
        </PageHeaderWrapper>

        {/* 编辑属性对话框 */}
        {this.renderEditField()}

        {/* 编辑规则函数信息 */}
        {this.renderEditRuleFunction()}

        {/* 编辑JS源代码 */}
        {this.renderEditJavascript()}
      </>
    )
  }
}

export default RuleFunctionManage
