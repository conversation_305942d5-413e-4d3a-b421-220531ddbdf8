import React, { Component } from 'react'
import { Table, Divider, But<PERSON>, Card, Form, Input } from 'antd'
import { connect } from 'dva'
import exportExcel from '@/utils/exportExcel'

@connect(({ gamePropsQuery, loading }) => ({ // model 的 namespace
  model: gamePropsQuery, // model 的 namespace
  loading: loading.effects['gamePropsQuery/getEnergyDetailLists']
}))
class EnergyBalanceDetailComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false
    }
  }

  componentDidMount () {
    this.loadData()
    this.setState()
  }

  columns = [
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '能量数量', dataIndex: 'count', align: 'center' },
    { title: '获得月份', dataIndex: 'month', align: 'center' }
    // { title: '最后更新时间', dataIndex: 'lastUpdate', align: 'center', render: (text, record) => (text = moment.unix(record.lastUpdate).format('YYYY-MM-DD HH:MM:ss')) }
  ]

  loadData = () => {
    const { dispatch } = this.props
    const { uid } = this.state
    const data = { uid: uid }
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getEnergyDetailLists`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onExport = () => {
    let headers = []
    this.columns.forEach(function (item) {
      headers.push({ key: item.dataIndex, header: item.title })
    })

    const { model: { energyDetailList } } = this.props
    var exportData = energyDetailList.map(item => {
      let v = $.extend(true, {}, item)
      return v
    })

    exportExcel(headers, exportData)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { energyDetailList } } = this.props
    return (
      <Card>
        <Form>
          <Input.Group compact>
            <span style={{ marginTop: 5 }}>UID:</span>
            <Input.TextArea placeholder='批量查询请以逗号分隔，如:1,2' rows={1} onChange={e => this.setState({ uid: e.target.value })} style={{ marginLeft: 10, width: 150 }} /> {/* 搜索按钮 */}
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onClick}>查询</Button>
            <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
          </Input.Group>
          <Divider />
          <Table dataSource={energyDetailList} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default EnergyBalanceDetailComponent
