// 道具池配置列表复用逻辑

import React from 'react'
import moment from 'moment'
import { propTypeOptions } from '../../dropCommon'
import DynamicCfgButton from './dynamicCfgBtn'
import ExtraCfgButton from './extraSetting'
import { Collapse, Typography, Divider, Tooltip } from 'antd'
import { timeFormater } from '@/utils/common'

const przeIDOptions = { 2: '交友', 34: '语音房', 36: '宝贝' }

export const columns = [
  { title: '编号', align: 'center', dataIndex: 'id' },
  { title: '奖励道具ID', align: 'center', dataIndex: 'prizeId' },
  // { title: '渠道', align: 'center', dataIndex: 'prizeType', render: v => { return ['营收', '交友', '宝贝', '追玩'][v] } },
  { title: '礼物渠道', align: 'center', dataIndex: 'appId', render: v => { return przeIDOptions[v] || '-' } },
  { title: '发放道具ID', align: 'center', dataIndex: 'propsId' },
  { title: '奖励道具名称', align: 'center', dataIndex: 'propsName' },
  { title: '稀有度', align: 'center', dataIndex: 'propsType', render: v => { return propTypeOptions.find(item => item.value === v)?.label } },
  { title: '热门', align: 'center', dataIndex: 'hot', render: (v, r) => { return hotOptions.map(i => i.label)[v] } },
  { title: '上新通知', align: 'center', dataIndex: 'newNotice', render: (v, r) => { return ['否', '是'][v] } },
  { title: '数量', align: 'center', dataIndex: 'count', render: (v, r) => { return countFormater(v, 'count') }, dft_getText: (v, r) => { return countGetText(v, 'count') } },
  { title: '价值/紫水晶', align: 'center', dataIndex: 'value', render: (v, r) => { return v ? (r.value * r.count).toLocaleString() : '' }, dft_getText: (v, r) => { return r ? (r.value * r.count).toLocaleString() : '' } },
  { title: '普通概率', align: 'center', dataIndex: 'rate', render: (v, r) => { return countFormater(v, 'rate') }, dft_getText: (v, r) => { return countGetText(v, 'rate') } },
  { title: '概率', align: 'center', dataIndex: 'fixRate', render: (v, r) => { return totalRateForamter(r) }, dft_getText: (v, r) => { return totalRateForamter(r) } },
  { title: '投放上限/D', align: 'center', dataIndex: 'dailyLimit', render: (v, r) => { return countFormater(v, 'dailyLimit') }, dft_getText: (v, r) => { return countGetText(v, 'dailyLimit') } },
  { title: '投放上限/2H', align: 'center', dataIndex: 'hoursLimit', render: (v, r) => { return countFormater(v, 'hoursLimit') }, dft_getText: (v, r) => { return countGetText(v, 'hoursLimit') } },
  { title: '金额限制', align: 'center', dataIndex: 'valueLimit' },
  { title: '生效时间', align: 'center', dataIndex: 'timeStart', render: (v) => { return v === undefined ? '' : moment.unix(v).format('HH:mm:ss') }, dft_getText: (v) => { return typeof v === 'number' ? moment.unix(v).format('HH:mm:ss') : '' } },
  { title: '失效时间', align: 'center', dataIndex: 'timeStop', render: (v) => { return v === undefined ? '' : moment.unix(v).format('HH:mm:ss') }, dft_getText: (v) => { return typeof v === 'number' ? moment.unix(v).format('HH:mm:ss') : '' } },
  { title: '广播类型', align: 'center', dataIndex: 'broadCastType', render: (v, r) => { return broadcastOptionsAll.map(i => i.label)[v] } },
  { title: '动态发放道具', align: 'center', dataIndex: 'limitSetting', render: (v, r) => { return <DynamicCfgButton key={v} value={r.limitSetting} diffMode={false} limit={r.dailyLimit} propsName={r.propsName} isEdit={false} /> } },
  { title: '翻倍', align: 'center', dataIndex: 'extraSetting', render: (v, r) => { return <ExtraCfgButton key={v} value={r.extraSetting} diffMode={false} propsName={r.propsName} isEdit={false} broadcastOptions={broadcastOptionsAll} /> } }
  // { title: 'A', align: 'center', dataIndex: 'extA', render: (v, r) => { return countFormater(v, 'extA') }, dft_getText: (v, r) => { return countGetText(v, 'extA') } },
  // { title: 'B', align: 'center', dataIndex: 'extB' },
  // { title: 'C', align: 'center', dataIndex: 'extC' },
  // { title: 'D', align: 'center', dataIndex: 'extD' }
]

function totalRateForamter (r) {
  const { rate, totalRate } = r
  return `${(rate * 100.0 / totalRate).toFixed(2)}%`
}

export const columns2 = [
  { title: '奖励道具名称', dataIndex: 'propsName' },
  { title: '数量', dataIndex: 'count' },
  { title: '奖励道具ID', dataIndex: 'prizeId' },
  { title: '发放道具ID', align: 'center', dataIndex: 'propsId' },
  { title: '价值/紫水晶', align: 'center', dataIndex: 'value', render: (v, r) => { return v ? (r.value * r.count).toLocaleString() : '' } },
  { title: '投放上限/D', dataIndex: 'dailyLimit', render: (v, r) => { return countFormater(v, 'dailyLimit') } },
  { title: '发放道具模式', dataIndex: 'limitSetting', render: (v) => { return v.isOpen ? limitModeOptions.find(item => item.value === v.mode)?.label : '基础模式' } },
  { title: '大盘动态新增上限', dataIndex: 'dynamicA' },
  { title: '道具池动态新增上限', dataIndex: 'dynamicB' },
  { title: '今日已发放数量', dataIndex: 'dynamicC' },
  { title: '今日剩余发放数量', dataIndex: 'dynamicD', render: (v, r) => { return r.dailyLimit < 0 ? '无限' : v } }
].map(item => {
  item.align = 'center'
  return item
})

// 组合大礼用
export const columns3 = [
  { title: 'ID', dataIndex: 'id' },
  { title: '组合名称', dataIndex: 'name', render: (v) => { return v || '未命名' } },
  { title: 'N抽', dataIndex: 'count' },
  { title: '概率', dataIndex: 'rate' },
  { title: '每日投放上限', dataIndex: 'dailyLimit', render: (v) => { return v === -1 ? '不限制' : v } },
  { title: '每2小时投放上限', dataIndex: 'hoursLimit', render: (v) => { return v === -1 ? '不限制' : v } },
  { title: '金额限制', dataIndex: 'valueLimit', render: (v) => { return v === -1 ? '不限制' : v } },
  { title: '开始时间', dataIndex: 'timeStart', render: (v) => { return v ? timeFormater(v, 8) : '不限制' } },
  { title: '结束时间', dataIndex: 'timeStop', render: (v) => { return v ? timeFormater(v, 8) : '不限制' } },
  { title: '广播类型', dataIndex: 'broadCastType', render: (v, r) => { return broadcastOptionsJY.find(item => item.value === v)?.label } },
  { title: null, dft_hidden: true, dataIndex: 'cmpLimitSetting' }, // 这行不能删除，否则DiffTable组件中的无该项数据
  { title: '动态发放道具', align: 'center', dataIndex: 'limitSetting', dft_hidden: true, render: (v, r) => { return <DynamicCfgButton key={v} value={v} poolID={21000} inComeType='交友物资大战全业务' diffMode cmpValue={r.cmpLimitSetting} limit={r.dailyLimit} propsName={r.name} isEdit={false} /> } },
  {}
].map(item => {
  item.align = 'center'
  return item
})

export const hotOptions = [
  { label: '否', value: 0 },
  { label: '是', value: 1 }
]

export const newNoticeOptions = [
  { label: '否', value: 0 },
  { label: '是', value: 1 }
]

// export const broadcastOptions = [
//   { label: '无广播', value: 0 },
//   { label: '全频道', value: 1 },
//   { label: '全服', value: 2 }
// ]

// 0 -无广播 1 -子频道 2 -全频道 3 -全服 4 -全平台 5 -家族广播
export const broadcastOptionsAll = [
  { label: '无广播', value: 0 },
  { label: '子频道/房间', value: 1 },
  { label: '全频道', value: 2 },
  { label: '全服', value: 3 },
  { label: '全平台', value: 4 },
  { label: '家族', value: 5 }
]
export const broadcastOptionsJY = [
  { label: '无广播', value: 0 },
  { label: '子频道', value: 1 },
  { label: '全频道', value: 2 },
  { label: '全服', value: 3 }
]
export const broadcastOptionsVR = [
  { label: '无广播', value: 0 },
  { label: '本房间', value: 1 },
  { label: '本家族', value: 5 },
  { label: '全服', value: 3 }
]

export const limitModeOptions = [
  { label: '大盘动态发放道具', value: 1, key: 1 },
  { label: '道具池动态发放道具', value: 2, key: 2 },
  { label: '基础模式', value: 0, key: 0, disabled: true }
]

function countFormater (text, dataIndex) {
  return ['dailyLimit', 'hoursLimit', 'rate', 'extA'].indexOf(dataIndex) === -1 || text > 0 ? text // 上限或概率大于0 发放
    : <font color={text === 0 ? 'red' : ''}>{['无上限', '不发放'][text + 1]}</font>
}

function countGetText (text, dataIndex) {
  return ['dailyLimit', 'hoursLimit', 'rate', 'extA'].indexOf(dataIndex) === -1 || text > 0 ? text
    : ['无上限', '不发放'][text + 1]
}

export function TipDIv ({ isShow }) {
  if (!isShow) { // TODO: 临时隐藏使用提示,后面可能要还原
    return ''
  }
  return (<Collapse ghost>
    <Collapse.Panel header={<a>点击展开使用提示</a>} showArrow={false}>
      <div style={{ backgroundColor: 'aliceblue' }} >
        <div>2. 金额限制 用户今日抢空投消耗的金额(今日已空投次数*2000)不低于限制金额才能抽中当前礼物，单位：紫水晶/红钻/红贝</div>
        <div>3. 投放上限 -1表示无上限 0表示不发放 大于0表示最多发放数量</div>
        <div>4. A-超预估后出现的1个金额/元 B-超预估后新增的数量 C-今日已发放数量 D-今日剩余发放数量</div>
        <div>5. 非设限部分发放占比：日投放无上限的礼物价值×概率÷（总概率×单次抽取道具金额）×100% </div>
        <div>6. 设限部分总金额：日投放有上限礼物价值×日投放数量/1000（注：单位为元）</div>
        <div>7. 字段更新说明：红色为审核更新后，括号内为审核更新前</div>
      </div>
    </Collapse.Panel>
  </Collapse>)
}

export function PoolChangeDesc (params) {
  const { rtb, rta, smb, sma, ela, elb, isEdit } = params
  const { Text } = Typography
  const styleBlue = { fontSize: '1.2em', color: '#009dd9' }
  const styleRed = { color: 'red' }
  let tip = `红色部分为当前列表实时计算数值, 括号内数值为 ${isEdit ? '当前正式配置计算数值' : '上次正式配置计算数值'}`
  return (
    <Typography>
      <Tooltip title={tip} ><Text style={styleBlue}>非设限部分发放占比:  <Text style={styleRed}> {rta / 100.0}%</Text> ({rtb / 100.0}%)</Text> <Divider type='vertical' /></Tooltip>
      <Tooltip title={tip} ><Text style={styleBlue}>设限部分总金额(元): <Text style={styleRed}> {sma / 100.0} </Text> ({smb / 100.0})</Text> <Divider type='vertical' /></Tooltip>
      <Tooltip title={tip} ><Text style={styleBlue}>翻倍上限总金额(元): <Text style={styleRed}> {ela / 1000.0} </Text> ({elb / 1000.0})</Text></Tooltip>
    </Typography>
  )
}

// 传入道具池列表，计算非设限部分发放占比和设限部分总金额
// note: 这里本应返回保留两位的浮点数，方便存储起见，乘以100转换到整数返回
export function countSURT (dataSource, pid, multi = 1) {
  let result = { rt: 0, sm: 0 }
  if (!Array.isArray(dataSource)) {
    return result
  }
  let cost = 2000 * multi // 单次抽取道具金额
  if (pid === 12000 || pid === 14000) { // 抢物资下的超级模式每次20元
    cost = 20000
  }
  let proSum = 0 // 总概率,所有物品的概率之和
  let nProSum = 0 // sum(日投放无上限的礼物价值×概率)
  let lproSum = 0 // sum(日投放有上限礼物价值×日投放数量)
  for (let i = 0; i < dataSource.length; i++) {
    const item = dataSource[i]
    const { rate, dailyLimit, value, count } = item
    proSum += rate
    if (dailyLimit >= 0) { // 有上限的礼物
      lproSum += (value * count * dailyLimit)
    } else { // 无上限的礼物
      nProSum += (value * count * rate)
    }
  }
  // console.info('nProSum=>', nProSum, 'lproSum=>', lproSum, 'rateSum=>', proSum, 'cost=>', cost)
  const tmpRT = nProSum / (proSum * cost) // 非设限部分发放占比: 日投放无上限的礼物价值×概率 /（总概率×单次抽取道具金额)
  result.rt = parseInt((tmpRT * 10000).toFixed(4))
  // console.info('非设限部分发放占比: ', nProSum, '/ (', proSum, '*', cost, ') = ', tmpRT, '=>', result.rt)

  const tmpSM = lproSum / 1000 // 设限部分总金额: 日投放有上限礼物价值×日投放数量/1000
  result.sm = parseInt(tmpSM * 100)
  // console.info('设限部分总金额: ', lproSum, '/ 1000 = ', tmpSM, '=>', result.sm)
  return result
}

// 计算翻倍上限总金额
export function countEL (dataSource) {
  let maxCost = 0
  if (!Array.isArray(dataSource)) {
    return 0
  }
  for (let i = 0; i < dataSource.length; i++) {
    const item = dataSource[i]
    const { extraSetting, count, value } = item
    if (!extraSetting || !extraSetting.isOpen || extraSetting.dailyLimit <= 0) {
      continue
    }
    maxCost += count * value * extraSetting.dailyLimit
  }
  return maxCost
}

// 空投道具池配置数据长度对其
export function fixDataSource (before = [], after = []) {
  let allID = [] // 奖励ID去重升序
  let totalA = 0
  before.map(item => {
    totalA += item.rate
    if (item.prizeId && allID.indexOf(item.prizeId) < 0) {
      allID.push(item.prizeId)
    }
  })
  let totalB = 0
  after.map(item => {
    totalB += item.rate
    if (item.prizeId && allID.indexOf(item.prizeId) < 0) {
      allID.push(item.prizeId)
    }
  })
  allID = allID.sort((a, b) => { return a > b ? 1 : -1 })
  let newBefore = []
  let newAfter = []

  for (let i = 0; i < allID.length; i++) {
    const id = allID[i]
    const defaultItem = { }
    const itemA = after.find(item => item.prizeId === id) || defaultItem
    const itemB = before.find(item => item.prizeId === id) || defaultItem
    itemA.cmpLimitSetting = itemB?.limitSetting
    itemA.cmpBoxwarSetting = itemB?.boxWarSetting
    itemA.cmpExtraSetting = itemB?.extraSetting
    itemA.totalRate = totalA
    itemB.totalRate = totalB
    newAfter.push(itemA)
    newBefore.push(itemB)
  }
  return {
    listB: newBefore,
    listA: newAfter
  }
}
