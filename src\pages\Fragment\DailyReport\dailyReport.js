import React, { Component } from 'react'
import { connect } from 'dva'
import { Row, Col, Space, Divider, Button, DatePicker, Table, Select, Tooltip } from 'antd'
import { businessTypeOptisons, businessTypeFormater } from './common'
import { onExportExcel } from '@/utils/common'
import moment from 'moment'
const namespace = 'fragmentReport'

@connect(({ fragmentReport }) => ({
  model: fragmentReport
}))

class DailyReportTab extends Component {
  state = {
    queryTimeRange: [moment().add(-7, 'days').startOf('days'), moment().endOf('days')],
    queryBusinessType: 1000
  }

  componentDidMount = () => {
    const { queryBusinessType, queryTimeRange } = this.state
    this.queryData(queryTimeRange, queryBusinessType)
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  queryData = (timeRange, businessType) => {
    let start = ''
    let end = ''
    if (timeRange[0] && timeRange[1]) {
      start = timeRange[0].format('YYYYMMDD')
      end = timeRange[1].format('YYYYMMDD')
    }
    this.callModel('queryDailyReport', {
      params: {
        startDate: start,
        endDate: end,
        businessType: businessType
      }
    })
  }

  mainColumns = [
    { title: '日期', dataIndex: 'date' },
    { title: '业务', dataIndex: 'businessType', render: (v) => { return businessTypeFormater(v) } },
    { title: '碎片发放流水/紫水晶', dataIndex: 'rewardValue' },
    { title: '碎片发放人数', dataIndex: 'rewardCount' },
    { title: '碎片消耗流水/紫水晶', dataIndex: 'exchangeValue' },
    { title: '碎片消耗人数', dataIndex: 'exchangeCount' }
  ].map(item => {
    item.align = 'center'
    return item
  })

  render () {
    const { queryBusinessType, queryTimeRange } = this.state
    const { dailyReportList } = this.props.model

    return (
      <div>
        <Row>
          <Col span={24}>
            <Space>
              <DatePicker.RangePicker format={'YYYY-MM-DD'} value={queryTimeRange} onChange={v => this.setState({ queryTimeRange: v })} />
              <Tooltip title='业务类型'>
                <Select options={businessTypeOptisons} value={queryBusinessType} onChange={v => this.setState({ queryBusinessType: v })} />
              </Tooltip>
              <Button onClick={() => { this.queryData(queryTimeRange, queryBusinessType) }} type='primary'>查询</Button>
              <Button onClick={() => { onExportExcel(this.mainColumns, dailyReportList, '装扮商城日报.xlsx') }} >导出</Button>
            </Space>
          </Col>
          <Divider />
          <Col span={24}>
            <Table columns={this.mainColumns} dataSource={dailyReportList} />
          </Col>
        </Row>
      </div>
    )
  }
}

export default DailyReportTab
