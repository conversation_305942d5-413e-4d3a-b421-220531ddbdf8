import React from 'react'
import { Row, Col, Typography, Select, Input } from 'antd'
import { bgTypeOptions, appIdOptions } from './common'
import InputPicture from '@/components/InputPicture' 
import { ColorPicker, CopyPasteButton } from '@/components/SimpleComponents' // ImagesList
const { Text, Title } = Typography
const allowType = ['image/jpeg', 'image/png', 'image/webp']
const allowType2 = ['image/gif']

export const UpdateBackgroundModalJY = (props) => {
  const { reocrd, opType, onUpdate, appId, onUpdateRecord, switchAppID } = props
  const isUpdate = opType === 'update'
  return (
    <Row>
      <Col span={24} style={{ textAlign: 'center' }}>
        <Title level={4}>{isUpdate ? '更新背景' : '新增背景'}</Title>
      </Col>
      {genInputItem('背景ID', false, <Input disabled value={isUpdate && reocrd.id <= 0 ? '自动生成' : reocrd.id} style={{ width: '10em' }} />)}
      <GenCenterTitle title='基础配置' />
      {genInputItem('所属业务', false, <Select disabled={isUpdate} value={appId} options={appIdOptions} onChange={v => { switchAppID(v) }} style={{ width: '10em' }} />)}
      {genInputItem('背景类型', false, <Select value={reocrd.bgType} options={bgTypeOptions} onChange={v => { onUpdate('bgType', v) }} style={{ width: '10em' }} />)}
      {genInputItem('背景名称', true, <Input value={reocrd.name} placeholder='限定6个中午字符' onChange={e => { onUpdate('name', e.target.value) }} style={{ width: '15em' }} />)}
      {genInputItem('背景角标', false, <Input value={reocrd.tag} placeholder='限定2个中文字符' onChange={e => { onUpdate('tag', e.target.value) }} style={{ width: '15em' }} />)}
      <GenCenterTitle title='PC端素材配置' />
      {genInputItem24('PC背景', true, <span style={{ display: 'inline-block' }}>
        <InputPicture key={reocrd.url} value={reocrd.url} width={150} sizeLimit={0.5} allowHeight={1308} allowWidth={1920} allowType={allowType} onChange={(v) => { onUpdate('url', v) }} />
      </span>)} 
      {genInputItem24('两侧底图', true, <span style={{ display: 'inline-block' }}>
        <InputPicture key={reocrd.sideUrl} value={reocrd.sideUrl} width={150} sizeLimit={0.2} allowHeight={524} allowWidth={300} allowType={allowType} onChange={(v) => { onUpdate('sideUrl', v) }} />
      </span>)}  
      {genInputItem('主题色', true, <ColorPicker value={reocrd.color} onChange={v => { onUpdate('color', v) }} />)}
      {genInputItem('排行色', true, <ColorPicker value={reocrd.rankColor} onChange={v => { onUpdate('rankColor', v) }} />)}
      {genInputItem24('充值背景色', true, <ColorPicker value={reocrd.giftBottomColor} width='32em' onChange={v => { onUpdate('giftBottomColor', v) }} />)}
      {genInputItem24('送礼按钮色', true, <ColorPicker value={reocrd.giftButtonColor} width='32em' onChange={v => { onUpdate('giftButtonColor', v) }} />)}
      {genInputItem24('礼物栏背景色', true, <ColorPicker value={reocrd.giftTopColor} width='32em' onChange={v => { onUpdate('giftTopColor', v) }} />)}
      <GenCenterTitle title='APP端素材配置' />
      {genInputItem('APP背景', true, <span style={{ display: 'inline-block' }}>
        <InputPicture key={reocrd.appUrl} value={reocrd.appUrl} width={150} sizeLimit={1} allowHeight={1624} allowWidth={750} allowType={allowType} onChange={(v) => { onUpdate('appUrl', v) }} />
      </span>)} 
      <Col span={24} style={{ marginTop: '1em', textAlign: 'right' }}>
        <CopyPasteButton getCopyVal={() => { return reocrd }} pasteVal={(val) => { onUpdateRecord(val) }} />
      </Col>
    </Row>
  )
}

export const UpdateBackgroundModalVR = (props) => {
  const { reocrd, opType, onUpdate, appId, onUpdateRecord, switchAppID } = props
  // const isProd = ENV_TAG === 'prod'
  const isUpdate = opType === 'update'
  return (
    <Row>
      <Col span={24} style={{ textAlign: 'center' }}>
        <Title level={4}>{isUpdate ? '更新背景' : '新增背景'}</Title>
      </Col> 
      {genInputItem('背景ID', false, <Input disabled value={isUpdate && reocrd.bgId < 0 ? '自动生成' : reocrd.bgId} style={{ width: '10em' }} />)}
      <GenCenterTitle title='基础配置' />
      {genInputItem('所属业务', false, <Select disabled={isUpdate} value={appId} options={appIdOptions} onChange={v => { switchAppID(v) }} style={{ width: '10em' }} />)}
      {genInputItem('背景类型', false, <Select value={reocrd?.fixedBgType} options={bgTypeOptions} onChange={v => { onUpdate('fixedBgType', v) }} style={{ width: '10em' }} />)}
      {genInputItem('背景名称', true, <Input value={reocrd.bgName} placeholder='限定6个中午字符' onChange={e => { onUpdate('bgName', e.target.value) }} style={{ width: '15em' }} />)}
      {genInputItem('背景角标', false, <Input value={reocrd.bgTag} placeholder='限定2个中文字符' onChange={e => { onUpdate('bgTag', e.target.value) }} style={{ width: '15em' }} />)}
      <GenCenterTitle title='PC端素材配置' />
      {genInputItem('PC插件背景', true, <span style={{ display: 'inline-block' }}>
        <InputPicture key={reocrd.pcPluginImageUrl} fileServer='jybossroombg' value={reocrd.pcPluginImageUrl} width={200} sizeLimit={0.5} allowHeight={490} allowWidth={875} allowType={allowType} onChange={(v) => { onUpdate('pcPluginImageUrl', v) }} />
      </span>)} 
      {genInputItem()}
      {genInputItem('模板背景', true, <span style={{ display: 'inline-block' }}>
        <InputPicture key={reocrd.pcTemplateImageUrl} fileServer='jybossroombg' value={reocrd.pcTemplateImageUrl} width={200} sizeLimit={0.5} allowHeight={1080} allowWidth={1920} allowType={allowType} onChange={(v) => { onUpdate('pcTemplateImageUrl', v) }} />
      </span>)} 
      {genInputItem()}
      {genInputItem('模板翅膀素材', false, <span style={{ display: 'inline-block' }}>
        <InputPicture key={reocrd.firstSeatImageUrl} fileServer='jybossroombg' value={reocrd.firstSeatImageUrl} width={200} sizeLimit={0.1} allowHeight={220} allowWidth={500} allowType={allowType} onChange={(v) => { onUpdate('firstSeatImageUrl', v) }} />
      </span>)} 
      <GenCenterTitle title='APP端素材配置' />
      {genInputItem('APP背景(静态)', true, <span style={{ display: 'inline-block' }}>
        <InputPicture key={reocrd.appImageUrl} fileServer='jybossroombg' value={reocrd.appImageUrl} width={150} sizeLimit={1} allowHeight={1624} allowWidth={750} allowType={allowType} onChange={(v) => { onUpdate('appImageUrl', v) }} />
      </span>)} 
      {genInputItem('APP背景(动态)', false, <span style={{ display: 'inline-block' }}>
        <InputPicture key={reocrd.appDynamicUrl} fileServer='jybossroombg' value={reocrd.appDynamicUrl} width={150} sizeLimit={30} allowType={allowType2} onChange={(v) => { onUpdate('appDynamicUrl', v) }} />
      </span>)}
      <Col span={24} style={{ marginTop: '1em', textAlign: 'right' }}>
        <CopyPasteButton getCopyVal={() => { return reocrd }} pasteVal={(val) => { onUpdateRecord(val) }} />
      </Col>
    </Row>
  )
}

const genInputItem = (title, notEmpty, element) => {
  if (!title) {
    return <Col span={12} style={{ marginBottom: '1em' }} />
  }
  return <Col span={12} style={{ marginBottom: '1em' }}>
    <span style={{ display: 'inline-block', width: '6em', marginRight: '10px' }}>
      {
        notEmpty
          ? <NotEmptyLabel />
          : ''
      }
      <Text>{title}: </Text>
    </span>
    <span>
      {element}
    </span> 
  </Col>
}

const genInputItem24 = (title, notEmpty, element) => {
  return <Col span={24} style={{ marginBottom: '1em' }}>
    <span style={{ display: 'inline-block', width: '8em', marginRight: '10px' }}>
      {
        notEmpty
          ? <NotEmptyLabel />
          : ''
      }
      <Text>{title}: </Text>
    </span>
    <span>
      {element}
    </span> 
  </Col>
}

const GenCenterTitle = ({ title }) => {
  return <Col span={24} style={{ textAlign: 'center', margin: '1em' }} > 
    <Text type='secondary'> --------- {title} --------- </Text>
  </Col>
}

export const NotEmptyLabel = () => {
  return <span style={{ marginRight: '5px', color: '#ff4d4f', 'fontSize': '14px' }}>*</span>
}
