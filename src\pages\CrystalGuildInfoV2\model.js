import { query, update, exportData } from './api'
import { Modal, message } from 'antd'

export default {
  namespace: 'crystalGuildInfoV2',

  state: {
    displayData: []
  },

  reducers: {
    displayList (state, { payload: data }) {
      if (!Array.isArray(data)) {
        console.error('unexpect data type: data=', data)
        return
      }
      return {
        ...state,
        displayData: data
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      let { data: { guildInfoList, status } } = yield call(query, payload)
      if (status !== 0) {
        Modal.error({ content: '获取水晶公会数据失败，请检查控制台' })
        return
      }

      guildInfoList = Array.isArray(guildInfoList) ? guildInfoList : []

      // guildInfoList = guildInfoList.filter((item) => { return item.level > 0 }) // 线上数据太多，因此不显示等级为0的公会数据；(公会等级=0的，不算水晶公会)

      if (guildInfoList !== []) {
        for (let i = 0; i < guildInfoList.length; i++) {
          guildInfoList[i].idx = i + 1
        }
      }

      yield put({
        type: 'displayList',
        payload: guildInfoList
      })
    },

    * getQueryList ({ payload }, { call, put }) {
      let { data: { queryGuildInfoList, status } } = yield call(query, payload)
      if (status !== 0) {
        Modal.error({ content: '获取水晶公会数据失败，请检查控制台' })
        return
      }

      queryGuildInfoList = Array.isArray(queryGuildInfoList) ? queryGuildInfoList : []

      // queryGuildInfoList = queryGuildInfoList.filter((item) => { return item.level > 0 }) // 线上数据太多，因此不显示等级为0的公会数据；(公会等级=0的，不算水晶公会)

      if (queryGuildInfoList !== []) {
        for (let i = 0; i < queryGuildInfoList.length; i++) {
          queryGuildInfoList[i].idx = i + 1
        }
      }

      yield put({
        type: 'displayList',
        payload: queryGuildInfoList
      })
    },

    * updateItem ({ payload }, { call, put }) {
      const { data: { status } } = yield call(update, payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('update failed')
      }
    },

    * exportItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(exportData, payload)
      if (status === 0) {
        message.success('exportData success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    }
  }
}
