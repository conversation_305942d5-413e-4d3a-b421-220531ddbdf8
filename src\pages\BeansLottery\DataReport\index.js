import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
// import dateString from '@/utils/dateString'
import { Table, Divider, Button, Form, Card, DatePicker } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const namespace = 'beansLotteryDataReport'
// const FormItem = Form.Item

@connect(({ beansLotteryDataReport }) => ({
  dataReport: beansLotteryDataReport
}))
class dataReport extends Component {
  // column structs.
  columns = [
    { title: '日期', dataIndex: 'time', key: 'time', align: 'center' },
    { title: '玩豆数额(扣豆)', dataIndex: 'lotteryMoney', key: 'lotteryMoney', align: 'center' },
    { title: '抽奖人数', dataIndex: 'lotteryPeople', key: 'lotteryPeople', align: 'center' },
    { title: '抽奖次数', dataIndex: 'lotteryCount', key: 'lotteryCount', align: 'center' },
    { title: '中奖金额(紫水晶)', dataIndex: 'winMoney', key: 'winMoney', align: 'center' },
    { title: '中奖人数', dataIndex: 'winPeople', key: 'winPeople', align: 'center' },
    { title: '上限人数', dataIndex: 'limitPeople', key: 'limitPeople', align: 'center' },
    { title: '首抽人数', dataIndex: 'firstLotteryPeople', key: 'firstLotteryPeople', align: 'center' }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {} }

  // show modal
  showModal = (isUpdate, record) => () => {
    var now = moment().unix()
    if (record == null) record = { key: '', desp: '', start: now, stop: now }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? 'Update' : 'Add' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  // get list from server.
  componentDidMount () {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/searchTime`
    })
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value.unix())
  }

  onEndChange = (value) => {
    this.onChange('endValue', value.unix())
  }

  searchTime = () => {
    // 这里要把开始和截止的时间传给后台
    console.log('1111111111')
    console.log(this.state.startValue)
    console.log(this.state.endValue)
    console.log('2222222222')
    console.log(this.state.startValue)
    console.log(this.state.endValue)
    const { dispatch } = this.props
    const data = { start: this.state.startValue, end: this.state.endValue }
    dispatch({
      type: `${namespace}/searchTime`,
      payload: data
    })
  }

  // content
  render () {
    const { route, dataReport: { list } } = this.props

    return (
      <PageHeaderWrapper title={route.name}>
        <div style={{ marginLeft: 10 }} color='red'>玩豆数额：当日扣费玩豆的数额</div>
        <div style={{ marginLeft: 10 }} color='red'>抽奖人数：抽奖人数算抽奖成功的人数 包含谢谢参与的</div>
        <div style={{ marginLeft: 10 }} color='red'>抽奖次数：是抽奖成功次数 连抽30次算30次 超出上限的不算</div>
        <div style={{ marginLeft: 10 }} color='red'>中奖金额（紫水晶）：礼物种类1*数量+礼物种类2*数量…之和</div>
        <div style={{ marginLeft: 10 }} color='red'>中奖人数：抽中奖品的人数</div>
        <div style={{ marginLeft: 10 }} color='red'>上限人数：当日上限达到后抽奖失败的人数</div>
        <div style={{ marginLeft: 10 }} color='red'>首抽人数：当日首次抽奖的人数</div>
        <Card>
          <Form>
            <DatePicker
              showTime
              format='YYYY-MM-DD'
              placeholder='开始时间'
              onChange={this.onStartChange}
            />
            <DatePicker
              showTime
              format='YYYY-MM-DD'
              placeholder='结束时间'
              onChange={this.onEndChange}
            />
            <Divider type='vertical' />
            <Button type='primary' onClick={this.searchTime}>查询</Button>
            <Divider />
            <Table dataSource={list} columns={this.columns} pagination={this.defaultPageValue} />
          </Form>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default dataReport
