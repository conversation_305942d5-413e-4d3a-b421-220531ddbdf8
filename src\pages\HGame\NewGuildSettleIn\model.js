import * as api from './api'
import { message } from 'antd'

export default {
  namespace: 'newGuildSettleIn',

  state: {
    listCrystal: [],
    listSuperCrystal: [],
    listWhite: [],
    listCrystalHistory: [],
    listSuperCrystalHistory: [],
    tableLoadingCrystalHistory: false,
    tableLoadingSuperCrystalHistory: false,
    detailCrysta: {}
  },

  reducers: {
    // 单个修改某个state成员
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },

    displayCrystalList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        listCrystal: payload
      }
    },

    displayCrystalHistoryList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        listCrystalHistory: payload
      }
    },

    displaySuperCrystalList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        listSuperCrystal: payload
      }
    },

    displaySuperCrystalHistoryList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        listSuperCrystalHistory: payload
      }
    },

    displayWhiteList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        listWhite: payload
      }
    },

    updateDetailCrysta (state, { payload }) {
      return {
        ...state,
        detailCrysta: payload
      }
    }
  },

  effects: {
    * listCrystal ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listCrystal, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      for (let i = 0; i < data.length; i++) {
        data[i].idx = i + 1
      }
      yield put({
        type: 'displayCrystalList',
        payload: data
      })
    },

    * listSuperCrystal ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listSuperCrystal, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      for (let i = 0; i < data.length; i++) {
        data[i].idx = i + 1
        data[i].guildCooperationAgreementURL = data[i].guildCooperationAgreementURL.replace('http://', 'https://')
        data[i].threePartiesAgreementURL = data[i].threePartiesAgreementURL.replace('http://', 'https://')
        data[i].guildManagerURL = data[i].guildManagerURL.replace('http://', 'https://')
      }
      yield put({
        type: 'displaySuperCrystalList',
        payload: data
      })
    },

    * listGreenChannelWhite ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listGreenChannelWhite, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      for (let i = 0; i < data.length; i++) {
        data[i].idx = i + 1
      }
      yield put({
        type: 'displayWhiteList',
        payload: data
      })
    },

    * listCrystalHistory ({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingCrystalHistory', newValue: true }
      })
      let { data: { data, status, msg } } = yield call(api.listCrystalHistory, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      yield put({
        type: 'displayCrystalHistoryList',
        payload: data
      })
      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingCrystalHistory', newValue: false }
      })
    },

    * listSuperCrystalHistory ({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingSuperCrystalHistory', newValue: true }
      })
      let { data: { data, status, msg } } = yield call(api.listSuperCrystalHistory, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      yield put({
        type: 'displaySuperCrystalHistoryList',
        payload: data
      })
      yield put({
        type: 'updateState',
        payload: { name: 'tableLoadingSuperCrystalHistory', newValue: false }
      })
    },

    * detailCrysta ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.detailCrysta, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      }
      yield put({
        type: 'updateDetailCrysta',
        payload: data
      })
    },

    * approvalCrysta ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.approvalCrysta, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listCrystal',
        payload: {}
      })
    },

    * approvalSuperCrysta ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.approvalSuperCrysta, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listSuperCrystal',
        payload: {}
      })
    },

    * uploadSuperSrysta ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.uploadSuperSrysta, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listSuperCrystal',
        payload: {}
      })
    },

    * addGreenChannelWhite ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.addGreenChannelWhite, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.error({ content: msg })
      } else {
        message.success('操作成功')
      }
      yield put({
        type: 'listGreenChannelWhite',
        payload: {}
      })
    },

    * getGuildRole ({ payload }, { call, put }) {
      if (payload === null || payload === undefined) {
        return
      }
      const { play, func } = payload
      console.log(payload)
      let { data: { data, status, msg } } = yield call(api.getGuildRole, play)
      console.log(data, status, msg)
      if (status !== 0) {
        message.error({ content: msg })
        return
      }
      func(data.curGuildRole, data.nextGuildRole)
    }
  }
}
