import * as api from './api'

export default {
  namespace: 'offstripDaily',

  state: {
    dailyList: [],
    detailList: []
  },

  reducers: {
    displayDailyList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      payload.sort((a, b) => b.date - a.date)
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        dailyList: payload
      }
    },
    displayDetailList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      payload.sort((a, b) => b.timestamp - a.timestamp)
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        detailList: payload
      }
    }
  },

  effects: {
    * listOffstripDaily ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listOffstripDaily, payload)
      console.log(status, msg)
      // let data = [{ id: 1, date: '20210420', reward: 2000, sidSum: 10, ssidSum: 20, compereSum: 5, userSum: 30 }, { id: 2, date: '20210419', reward: 3000, sidSum: 12, ssidSum: 23, compereSum: 4, userSum: 30 }]
      yield put({
        type: 'displayDailyList',
        payload: Array.isArray(data) ? data : []
      })
    },

    * listOffstripRewardHistory ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listOffstripRewardHistory, payload)
      console.log(status, msg)
      // let data = [{ id: 1, date: '20210420', asid: 1, guildNick: 'test1', ssid: 222, compereUid: 2247897, rewardUid: 7847593, prizeId: 2222, prizeName: '大头儿子1', prizeSum: 10, prizeValue: 20000, timestamp: 1619776247 }, { id: 2, date: '20210420', asid: 2, guildNick: 'test2', ssid: 333, compereUid: 2247897, rewardUid: 7847593, prizeId: 2222, prizeName: '大头儿子2', prizeSum: 10, prizeValue: 20000, timestamp: 1619776247 }, { id: 3, date: '20210420', asid: 3, guildNick: 'test3', ssid: 444, compereUid: 2247897, rewardUid: 7847593, prizeId: 2222, prizeName: '大头儿子3', prizeSum: 10, prizeValue: 20000, timestamp: 1619776247 }]
      yield put({
        type: 'displayDetailList',
        payload: Array.isArray(data) ? data : []
      })
    }
  }
}
