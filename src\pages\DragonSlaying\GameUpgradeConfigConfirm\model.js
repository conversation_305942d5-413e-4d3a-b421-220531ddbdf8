import { message } from 'antd'
import { confirm, getLists, refuse } from './api'

export default {
  namespace: 'dsUpgradeConfigConfirm', // 只有这里需要修改

  state: {
    list: [],
    extra: ''
  },

  reducers: {
    updateList (state, { payload, extra }) {
      for (var i = 0; i < payload.length; i++) {
        payload[i].index = i + 1
      }
      return {
        ...state,
        list: payload,
        extra: extra
      }
    }
  },

  effects: {
    * getList ({ payload }, { call, put }) {
      const { data: { list, extra } } = yield call(getLists, payload)

      yield put({
        type: 'updateList',
        payload: Array.isArray(list) ? list : [],
        extra: extra
      })
    },

    * updateItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(payload)
      if (status === 0) {
        message.success('update success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * removeItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(refuse, payload)
      if (status === 0) {
        message.success('refuse success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * confirmItem ({ payload }, { call, put }) {
      const { data: { status, msg } } = yield call(confirm, payload)
      if (status === 0) {
        message.success('confirm success')
        yield put({
          type: 'getList'
        })
      } else {
        message.error('failed' + msg)
      }
    },

    * getItemByKey ({ payload }, { call, put }) {
      const { data: { status, list } } = yield call(payload)
      if (status === 0) {
        yield put({
          type: 'updateList',
          payload: list
        })
      } else {
        message.warning('not found')
      }
    }
  }
}
