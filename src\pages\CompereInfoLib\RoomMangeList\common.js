import { Typography, Space } from 'antd'
import React, { Component } from 'react'
const { Text } = Typography

export class ReviewInfo extends Component {
  getDataList = () => {
    const { data } = this.props
    let dataList = [
      [<Text>调整前:</Text>, <Text>调整后：</Text>]
    ]

    let beforeList = []
    let afterList = []
    gradeOptionsV2.forEach(item => {
      beforeList[item.value] = 0
      afterList[item.value] = 0
    })
    data.forEach(item => {
      afterList[item.manualGrade]++
      beforeList[item.systemGrade]++
    })
    gradeOptionsV2.forEach(item => {
      const { value, label } = item
      let descA = `${label}级房管厅数: ${beforeList[value]}; `
      let descB = `${label}级房管厅数: ${afterList[value]}; `
      dataList.push([descA, descB])
    })
    return dataList
  }

  render () {
    const dataList = this.getDataList()
    return (
      <Space style={{ marginTop: '1em', marginBottom: '1em' }}>
        {
          dataList.map(item => {
            return (
              <Space direction='vertical'>
                {item[0]}
                {item[1]}
              </Space>
            )
          })
        }
      </Space>
    )
  }
}

export const gradeOptionsV2 = [
  { label: '无评级', value: 0 },
  { label: 'D', value: 1 },
  { label: 'C', value: 2 },
  { label: 'B', value: 3 },
  { label: 'A', value: 4 },
  { label: 'S', value: 5 }
]

export const gradeFormater = (v) => {
  let res = gradeOptionsV2.find(item => { return item.value === v })
  if (res) {
    return res.label
  }
  return `${v}_?`
}
