import React, { Component } from 'react'
import { Table, Divider, But<PERSON>, Card, Form, DatePicker } from 'antd'
import { connect } from 'dva'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
const gameMap = { 0: 'ALL', 1: '常驻玩法', 2: '活动玩法' }

@connect(({ hatkingJy }) => ({ // model 的 namespace
  model1: hatkingJy // model 的 namespace
}))
class HatKingModouInfoComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
  }

  // 日期 盖章流水(总) 紫水晶券盖章流水  盖章魔豆数 豆荚兑换魔豆数 魔术帽魔豆数  流光魔豆数 魔豆剩余量 活动魔豆剩余量 流光总条数 流光总流水
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '玩法类型', dataIndex: 'itemType', align: 'center', render: (text, record, index) => { return gameMap[text] }, filters: [{ text: 'ALL', value: 0 }, { text: '常驻玩法', value: 1 }, { text: '活动玩法', value: 2 }], defaultFilteredValue: ['0'], onFilter: (value, record) => record.itemType === value },
    { title: '盖章流水(总)', dataIndex: 'sealPrice', align: 'center' },
    { title: '紫水晶券盖章流水', dataIndex: 'periodSealPrice', align: 'center' },
    { title: '盖章魔豆数', dataIndex: 'sealRewardModou', align: 'center' },
    { title: '豆荚兑换魔豆数', dataIndex: 'doujiaRewardModou', align: 'center' },
    { title: '勇士达人发放数', dataIndex: 'customerRewardModou', align: 'center' },
    { title: '许愿喷泉发放数', dataIndex: 'fountainRewardModou', align: 'center' },
    { title: '魔术帽魔豆数', dataIndex: 'magicHatRewardModou', align: 'center' },
    { title: '流光魔豆数', dataIndex: 'giftRewardModou', align: 'center' },
    { title: '布料剩余量', dataIndex: 'remainModou', align: 'center' },
    { title: '活动布料剩余量', dataIndex: 'remainActModou', align: 'center' },
    { title: '流光总条数', dataIndex: 'giftEffectCount', align: 'center' },
    { title: '流光总流水', dataIndex: 'giftEffectPrice', align: 'center' }
  ]

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getModouInfoList`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { modouList } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form>
          <span style={{ marginLeft: 10 }}>时间范围:</span>
          <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Divider />
          <Table dataSource={modouList} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default HatKingModouInfoComponent
