import { But<PERSON>, Card, DatePicker, Divider, Form, Input, Table } from 'antd'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { connect } from 'dva'
import React, { Component } from 'react'
import exportExcel from '@/utils/exportExcel'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
const namespace = 'arkUserSummary'

@connect(({ arkUserSummary }) => ({ // model 的 namespace
  model: arkUserSummary // model 的 namespace
}))

class ArkDailyUserSummaryInfoComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      list: [],
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
  }

    // 日期 渠道类型  魔豆模拟流水 付费用户数 剩余发放监控汇总  成功总流水 成功总人数 累计成功人数  累计失败人数
    columns = [
      { title: '日期', dataIndex: 'date', align: 'center' },
      {
        title: 'uid',
        dataIndex: 'uid',
        key: 'uid',
        align: 'center',
        render: (text, record) => {
          switch (text) {
            case 0: return 'all'
            default: return text
          }
        }
      },
      { title: '昵称', dataIndex: 'nick', align: 'center' },
      { title: '盖章流水(元)', dataIndex: 'sealAmount', align: 'center' },
      { title: '参与流水(元)', dataIndex: 'amethyst', align: 'center' },
      { title: '发放流水(元)', dataIndex: 'awardAmount', align: 'center' },
      { title: '发放占比', dataIndex: 'awardPro', align: 'center' },
      { title: '参与次数', dataIndex: 'count', align: 'center' },
      { title: '成功次数', dataIndex: 'awardCount', align: 'center' },
      { title: '偏好位置', dataIndex: 'positionStr', align: 'center' },
      { title: '参与次数比', dataIndex: 'positionPro', align: 'center' },
      { title: '流水占比', dataIndex: 'positionAmountPro', align: 'center' },
      { title: '参与流水最高频道', dataIndex: 'sidListStr', align: 'center' },
      { title: '流水占比', dataIndex: 'sidAmountPro', align: 'center' },
      { title: '参与流水最高子频道', dataIndex: 'ssidListStr', align: 'center' },
      { title: '流水占比', dataIndex: 'ssidAmountPro', align: 'center' }
    ]

    loadData = () => {
      const { dispatch } = this.props
      const { dateRange, uid } = this.state
      const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat), uid: uid }
      console.log('uid=========', uid)
      dispatch({
        type: `${namespace}/getTopNRangeList`,
        payload: data
      })
    }

    onClick = () => {
      this.loadData()
    }

    onChange = (date, format) => {
      console.log('date', date)
      this.setState({ dateRange: date })
    }

    onStartChange = (value) => {
      this.onChange('startValue', value)
    }

    onEndChange = (value) => {
      this.onChange('endValue', value)
    }

    handleSelectChange = (value) => {
      console.log(value)
    }

    onExport = () => {
      let headers = []
      let columns = this.columns
      columns.forEach(function (item) {
        headers.push({ key: item.dataIndex, header: item.title })
      })

      const { model: { topnUserInfoList } } = this.props
      var exportData = topnUserInfoList.map(item => {
        let v = $.extend(true, {}, item)
        // v.arenaId = gameMap[v.arenaId]
        // v.platform = chanMap[v.platform]
        return v
      })

      exportExcel(headers, exportData)
    }

    /* *******************************页面布局***************************************************************/
    render () {
      const { route } = this.props
      const { model: { topnUserInfoList } } = this.props
      const { dateRange } = this.state
      return (
        <PageHeaderWrapper title={route.name} >
          <Card>
            <Form>
              UID
              <Input onChange={e => this.setState({ uid: e.target.value })} style={{ marginLeft: 10, width: 150 }} />
              <span style={{ marginLeft: 10 }}>时间范围:</span>
              <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
              <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
              <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
              <Divider />
              <Table dataSource={topnUserInfoList} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
            </Form>
          </Card>
        </PageHeaderWrapper>
      )
    }
}

export default ArkDailyUserSummaryInfoComponent
