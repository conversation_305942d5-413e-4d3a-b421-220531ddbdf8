import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs, Card } from 'antd'
import { connect } from 'dva'

import HelpRecord from './components/HelpRecord'

const TabPane = Tabs.TabPane

@connect(({ helpGroup }) => ({
  model: helpGroup
}))

class HelpReport extends Component {
  state = { 
    activeKey: '1'
  }

  onTabClick = key => {
    this.setState({ activeKey: key })
  }

  render () {
    const { route } = this.props
    const { activeKey } = this.state
    
    return (
      <PageHeaderWrapper title={route.name}>
        <Card style={{ marginTop: 20 }}>
          <Tabs type='card' defaultActiveKey='1' onTabClick={this.onTabClick}>
            <TabPane tab='数据统计' key='1'>
              { activeKey === '1' ? <HelpRecord /> : ''}
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default HelpReport
