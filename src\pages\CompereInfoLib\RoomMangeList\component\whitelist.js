import React, { Component } from 'react'
import { connect } from 'dva'
import {
  Card,
  message,
  Row,
  Col,
  Table,
  Form,
  Space,
  InputNumber,
  Button,
  Modal
} from 'antd'
const namespace = 'RoomMangeList'

@connect(({ RoomMangeList }) => ({
  model: RoomMangeList
}))

class RoomMgrWhitelist extends Component {
  constructor (props) {
    super(props)

    this.state = {
      searchParams: { uid: 0, imid: 0 },
      value: {},
      visible: false
    }
  }

  getFilterList = () => {
    const { whitelist } = this.props.model
    const { searchParams } = this.state
    console.log('searchParams', searchParams)
    let filterList = whitelist
    if (searchParams.uid !== undefined && searchParams.uid > 0) {
      filterList = filterList.filter((v) => { return v.uid === parseInt(searchParams.uid) })
    }
    if (searchParams.imid && searchParams.imid > 0) {
      filterList = filterList.filter((v) => { return v.imid === parseInt(searchParams.imid) })
    }
    return filterList
  }

  componentDidMount = () => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/getWhitelist`
    })
  }

  // 刷新配置列表数据
  refreshList = (params) => {
    const { dispatch } = this.props

    dispatch({
      type: `${namespace}/getWhitelist`
    })
  }

  // 更新或新增配置
  onFinish = values => {
    console.debug('values==>', values)

    const { searchParams } = this.state
    if (parseInt(values.uid) === 0) {
      message.error('房管不能为0')
      return
    }
    let data = { uid: values.uid }
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/addWhitelist`,
      payload: data
    })
    this.refreshList(searchParams)
    this.setState({ addModalVisible: false })
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '100', '500', '2000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  render () {
    const { addModalVisible, searchParams, opType } = this.state
    const columns = [
      { title: '序号', dataIndex: 'index' },
      { title: 'UID', dataIndex: 'uid' },
      { title: 'YY号', dataIndex: 'imid' },
      { title: '昵称', dataIndex: 'nick' },
      { title: '添加时间', dataIndex: 'date' },
      { title: '添加人', dataIndex: 'opPassport' }
    ].map(raw => {
      raw.align = 'center'
      return raw
    })

    return (
      <Card>
        <Row>

          <Col span={24} style={{ margin: '1em 0' }}>
            <Space>
              房管UID: <InputNumber style={{ width: '10em' }} placeholder='UID'
                onChange={(v) => { let cp = { ...searchParams }; cp.uid = v; this.setState({ searchParams: cp }) }} />
              房管YY号: <InputNumber style={{ width: '10em' }} placeholder='YY号'
                onChange={(v) => { let cp = { ...searchParams }; cp.imid = v; this.setState({ searchParams: cp }) }} />
              {/* <Button type='primary' onClick={() => { this.getFilterList() }}>查询</Button> */}
              <Button type='primary' onClick={() => { this.setState({ addModalVisible: true, opType: 'ADD' }); this.formRef.resetFields() }}>添加房管白名单</Button>
            </Space>
          </Col>
          {/* 列表数据 */}
          <Col span={24}>
            <Table columns={columns} size='small' pagination={this.defaultPageValue} dataSource={this.getFilterList()} />
          </Col>

          <Modal visible={addModalVisible} forceRender title={opType === 'ADD' ? '添加白名单' : '更新添加白名单'} onCancel={() => { this.setState({ addModalVisible: false }) }} footer={null} >
            <Form ref={from => { this.formRef = from }} initialValues={{ }} onFinish={this.onFinish} labelCol={{ span: 5 }}>
              <Form.Item style={{ width: '50em' }} label='房管UID' name='uid' rules={[{ required: true, message: '必填' }]}>
                <InputNumber style={{ width: '35%' }} />
              </Form.Item>
            </Form>
            <Row style={{ marginLeft: 200 }} >
              <Space>
                <Button onClick={() => { this.setState({ addModalVisible: false }) }}>取消</Button>
                <Button type='primary' onClick={() => this.formRef.submit()}>确定</Button>
              </Space>
            </Row>
          </Modal>
        </Row>
      </Card>
    )
  }
}

export default RoomMgrWhitelist
