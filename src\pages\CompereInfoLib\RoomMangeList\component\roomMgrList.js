import React, { Component } from 'react'
import { connect } from 'dva'
import {
  <PERSON><PERSON>,
  Card,
  Col, DatePicker, Divider,
  Form,
  Input,
  InputNumber,
  message,
  Modal,
  Popover,
  Radio,
  Row,
  Select,
  Space,
  Table, Typography
} from 'antd'
import exportExcel from '@/utils/exportExcel'
import { formatOptions, timeFormater, getCookie, deepClone, onExportExcel } from '@/utils/common'
import PopImage from '@/components/PopImage'
import PopImage2 from '@/components/PopImage2'
import { ReviewInfo, gradeFormater, gradeOptionsV2 } from './../common'

const Option = Select.Option
// const { RangePicker } = DatePicker
const { Text } = Typography

const moment = require('moment')
const namespace = 'RoomMangeList'
// const RadioGroup = Radio.Group

// const statusOptions = [
//   { label: '全部', value: -1 },
//   { label: '天团厅', value: 0 },
//   { label: '个播厅', value: 1 }
// ]

const settlementOptions = [
  { label: '全部', value: -1 },
  { label: '主持人模式', value: 0 },
  { label: '收礼人模式', value: 1 },
  { label: '房管模式', value: 2 }
]

const hadRenewOptions = [
  { label: '全部', value: null },
  { label: '是', value: true },
  { label: '否', value: false }
]

@connect(({ RoomMangeList }) => ({
  model: RoomMangeList
}))

class RoomMangeList extends Component {
  state = {
    searchParams: { uid: 0, imid: 0, asid: 0, ssid: 0, type: -1, settlement: -1, hadRenew: null },
    value: {},
    visible: false,
    reviewVisible: false,
    reviewList: [],
    reviewParams: { ssid: 0, mgrUID: 0, mgrIMID: 0, asid: 0, gradeID: -1 },
    assessInfo: {}
  }

  componentDidMount = () => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/getRoomMgrList`
    })
    this.queryAssessInfo()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  getLabelDesc = (record) => {
    if (record === undefined || record.label === undefined || record.label === 0) {
      return '-'
    }
    let labelDesc = ['', '新厅/回流厅', '高质量外站厅'][record.label]
    let skipMonth = ''
    if (record.skipStartMonth.length > 0 && record.skipEndMonth.length) {
      skipMonth = record.skipStartMonth + '~' + record.skipEndMonth
    }
    return <div><font color='blue'>{labelDesc} </font> <br />{skipMonth}</div>
  }

  getMainColumns = (canReview) => {
    return [
      // { title: '序号', dataIndex: 'index', fixed: 'left' },
      { title: '房管UID', dataIndex: 'uid', fixed: 'left' },
      { title: '房管YY号', dataIndex: 'imid', fixed: 'left' },
      { title: '房管昵称', width: 120, dataIndex: 'nick', fixed: 'left' },
      // { title: '经营类型', dataIndex: 'type', render: (v) => { return v === 1 ? '个播厅' : '天团厅' } },
      { title: '经营厅模式', dataIndex: 'settlement', render: (v) => formatOptions(v, settlementOptions) },
      { title: '签约sid', dataIndex: 'sid' },
      { title: '签约asid', dataIndex: 'asid' },
      { title: '经营sid', dataIndex: 'playSid' },
      { title: '经营ssid', dataIndex: 'ssid' },
      { title: '公会分成比例', dataIndex: 'weight', render: (v) => { return v + '%' } },
      { title: '厅名称',
        dataIndex: 'name',
        render: (text, record) => (record.ongoingName.length > 0 ? <span >{record.name}  <br />
          <span style={{ color: 'gray' }}>{'(审核中: ' + record.ongoingName + ')'} </span> </span> : record.name) },
      { title: '当前评级', dataIndex: 'gradeID', render: (v) => { return gradeFormater(v) } },
      { title: '房管厅标签', dataIndex: 'label', render: (text, record) => { return this.getLabelDesc(record) } },
      { title: '厅资料图',
        dataIndex: 'url',
        align: 'center',
        render: (text, record) => (record.ongoingURL.length > 0
          ? <span>
            <PopImage value={text} />
            <PopImage2 style={{ marginLeft: 8 }} value={record.ongoingURL} width='4.5em' height='4.5em' tooltip='i have note' note='审核中' />
          </span> : <PopImage value={text} />) },
      { title: '签约开始时间', dataIndex: 'startTime', render: (v) => timeFormater(v / 1000, 1) },
      { title: '签约结束时间', dataIndex: 'endTime', render: (v) => timeFormater(v / 1000, 1) },
      { title: 'owUID', dataIndex: 'owUID' },
      { title: '是否对公', dataIndex: 'isPublic', render: (v) => { return v ? '是' : '否' } },
      { title: '是否已续约', dataIndex: 'hadRenew', render: (v) => { return v ? '是' : '否' } },
      {
        title: '操作',
        key: 'operation',
        align: 'left',
        ignoreExport: true,
        render:
          (text, item) => (
            <span>
              <a style={{ marginRight: 4 }} size='small' type='primary' onClick={() => this.showEditModal(item, 'edit')}>编辑</a> |
              <a style={{ marginLeft: 4, marginRight: 4 }} disabled={canReview} size='small' type='primary' onClick={() => this.showEditGradeModal(item, 'grade')}>修改评级</a> |
              <a style={{ marginLeft: 4, marginRight: 4 }} disabled={canReview} size='small' type='primary' onClick={() => this.showEditGradeModal(item, 'label')}>修改标签</a> |
              <a style={{ marginLeft: 4, marginRight: 4 }} size='small' type='primary' onClick={() => this.showHistoryModal(item)}>查看评级历史</a> |
              <Divider type='vertical' />
              <Popover content={this.renderContent(item)} trigger='click'><a><font color={'red'}>回收</font></a></Popover>
            </span>)
      }
    ].map(raw => {
      raw.align = 'center'
      return raw
    })
  }

  getHistoryDesc = (text, record) => {
    return record !== undefined && record.opDesc === '手动修改' ? '-' : text
  }

  historyColumns =[
    { title: '考核月份', dataIndex: 'dateYM', fixed: 'left', render: (text, record) => this.getHistoryDesc(text, record) },
    { title: '评级等级', dataIndex: 'gradeID', fixed: 'left', render: (v) => { return gradeFormater(v) } },
    { title: '月厅礼物流水(元)', dataIndex: 'amount', fixed: 'left', render: (text, record) => this.getHistoryDesc(text, record) },
    { title: '月厅活跃主持数', dataIndex: 'activeCompereCount', fixed: 'left', render: (text, record) => this.getHistoryDesc(text, record) },
    { title: '月厅活跃嘉宾数', dataIndex: 'activeGuestCount', fixed: 'left', render: (text, record) => this.getHistoryDesc(text, record) },
    { title: '月厅有效运营天数', dataIndex: 'roomValidDays', fixed: 'left', render: (text, record) => this.getHistoryDesc(text, record) },
    { title: '评级类型', dataIndex: 'opDesc', fixed: 'left' },
    { title: '操作人', width: 80, dataIndex: 'opUser', fixed: 'left' },
    { title: '更新时间', dataIndex: 'updateDate', fixed: 'left' }

  ].map(raw => {
    raw.align = 'center'
    return raw
  })

  reviewColumn = [
    { dataIndex: 'month', title: '考核周期' },
    { dataIndex: 'ssid', title: '房管厅SSID' },
    { dataIndex: 'roomName', title: '房管厅名称' },
    { dataIndex: 'mgrUID', title: '房管uid' },
    { dataIndex: 'mgrIMID', title: '房管YY号' },
    { dataIndex: 'signSID', title: '签约公会' },
    { dataIndex: 'signASID', title: '签约公会asid' },
    { dataIndex: 'amount', title: '月厅礼物流水' },
    { dataIndex: 'activeCompereCount', title: '月活跃主持数' },
    { dataIndex: 'activeGuestCount', title: '月活跃嘉宾数' },
    { dataIndex: 'roomValidDays', title: '厅有效运营天数' },
    { dataIndex: 'systemGrade', title: '系统考核评级', render: (v, r) => { return r.isSkipAssess ? '-' : gradeFormater(v) } },
    { dataIndex: 'manualGrade',
      notExport: true,
      title: '人工修改评级',
      exportRender: (v, r) => { return gradeFormater(v) },
      render: (v, r) => {
        let isDiff = r?.systemGrade !== v
        if (r.isSkipAssess) {
          return <Select disabled />
        }
        return <Select value={v} options={gradeOptionsV2} style={{ width: '5em', color: isDiff ? 'red' : '', fontWeight: isDiff ? 'bold' : '' }} onChange={(v) => { this.updateReviewList(r.id, v) }} />
      } }
  ].map(item => {
    item.align = 'center'
    return item
  })

  renderContent = (record) => {
    return (
      <div>
        <Input.TextArea onChange={this.onTextChange} row={5} placeholder={'删除原因选填，最多100字符'} />
        <Button onClick={this.deleteHandle(record)} style={{ marginLeft: 120, marginTop: 5 }} type='primary'>确定</Button>
      </div>
    )
  }

  onTextChange = e => {
    this.setState({ removeReason: e.target.value })
  }

  deleteHandle = (record) => () => {
    const { removeReason } = this.state
    let data = Object.assign(record || {}, record)
    data.removeReason = removeReason
    console.log(data)

    this.props.dispatch({
      type: `${namespace}/deleteRoomMgr`,
      payload: data,
      callback: (rsp) => {
        if (rsp && rsp.status === 0) {
          message.success('success')
          this.props.dispatch({
            type: `${namespace}/getRoomMgrList`
          })
        } else {
          message.error(' 失败: ' + rsp.msg)
        }
      }
    })
    this.setState({ removeReason: '' })
  }

  onExport = (columns, roomMgrList) => {
    let exportData = roomMgrList.map(item => {
      let v = $.extend(true, {}, item)
      v.startTime = timeFormater(item.startTime / 1000, 1)
      v.endTime = timeFormater(item.endTime / 1000, 1)
      v.isPublic = (v.isPublic ? '是' : '否')
      v.settlement = formatOptions(v.settlement, settlementOptions)
      v.gradeID = gradeFormater(v.gradeID)
      if (v.label === 0) {
        v.label = 0
      } else {
        v.label = ['', '新厅/回流厅', '高质量外站厅'][v.label]
        if (v.skipStartMonth.length > 0 && v.skipEndMonth.length) {
          let skipMonth = v.skipStartMonth + '~' + v.skipEndMonth
          v.label += skipMonth
        }
      }
      return v
    })
    let exportHeader = []
    columns.forEach((col) => {
      if (col.ignoreExport) {
        return
      }
      exportHeader.push({ key: col.dataIndex, header: col.title })
    })
    let fileName = '房管名单(交友)-' + moment.unix(parseInt(new Date().getTime() / 1000)).format('YYYYMMDD') + '.xlsx'
    exportExcel(exportHeader, exportData, fileName)
  }

  getFilterList = () => {
    const { roomMgrList } = this.props.model
    const { searchParams } = this.state
    let filterList = roomMgrList
    if (searchParams.uid && searchParams.uid > 0) {
      filterList = filterList.filter((v) => { return v.uid === parseInt(searchParams.uid) })
    }
    if (searchParams.imid && searchParams.imid > 0) {
      filterList = filterList.filter((v) => { return v.imid === parseInt(searchParams.imid) })
    }
    if (searchParams.asid && searchParams.asid > 0) {
      filterList = filterList.filter((v) => { return v.asid === parseInt(searchParams.asid) })
    }
    if (searchParams.ssid && searchParams.ssid > 0) {
      filterList = filterList.filter((v) => { return v.ssid === parseInt(searchParams.ssid) })
    }
    if (searchParams.type >= 0) {
      filterList = filterList.filter((v) => { return v.type === parseInt(searchParams.type) })
    }
    if (searchParams.settlement >= 0) {
      filterList = filterList.filter((v) => { return v.settlement === parseInt(searchParams.settlement) })
    }
    console.log(searchParams)
    if (searchParams.hadRenew !== null && searchParams.hadRenew !== undefined) {
      filterList = filterList.filter((v) => { return v.hadRenew === searchParams.hadRenew })
    }
    if (searchParams.gradeID !== null && searchParams.gradeID !== undefined) {
      filterList = filterList.filter((v) => { return v.gradeID === searchParams.gradeID })
    }
    return filterList
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '100', '500', '2000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  showEditModal = (item, editMode) => {
    if (editMode === 'add') {
      if (this.formRefEdit) {
        this.formRefEdit.resetFields()
      }
    }
    let editItem = Object.assign({}, item)
    if (this.formRefEdit) {
      this.formRefEdit.setFieldsValue(editItem)
    }
    this.setState({ editModalVisible: true, editItem: editItem, editMode: editMode })
  }

  closeEditModal = () => {
    if (this.formRefEdit) {
      this.formRefEdit.resetFields()
    }
    this.setState({ editModalVisible: false })
  }

  onEditModalOk = () => {
    this.formRefEdit.submit()
  }

  onEditFormSubmit = (values) => {
    const { editItem, editMode } = this.state

    let submitItem = Object.assign(editItem || {}, values)
    if (submitItem.settlement !== 0 && submitItem.settlement !== 1 && submitItem.settlement !== 2) {
      message.error('经营厅模式错误，请检查！')
      return
    }
    if (submitItem.settlement === 2) {
      submitItem.type = 0
    } else {
      submitItem.type = 1
    }
    console.log('待提交的数据: ', submitItem, values)
    const reqType = `${namespace}/updateRoomMgr`
    const { dispatch } = this.props
    let self = this
    let close = message.loading('执行中，请稍候......')
    dispatch({
      type: reqType,
      payload: submitItem,
      callback: (rsp) => {
        close()
        if (rsp && rsp.status === 0) {
          self.setState({ editModalVisible: false, editItem: undefined })
          let msg = rsp.msg || ''
          if (msg.indexOf('审批') >= 0 || msg.indexOf('审核') >= 0) {
            message.success('成功: ' + msg, 2)
            return
          }
          message.success('success')
          dispatch({
            type: `${namespace}/getRoomMgrList`
          })
        } else {
          message.error(editMode + ' 失败: ' + rsp.msg)
        }
      }
    })
  }

  // 编辑数据对话框
  renderEditModal = () => {
    const { editModalVisible, editItem } = this.state
    let item = editItem || {}
    return (
      <Modal width={500} title={'编辑'} visible={editModalVisible} onCancel={this.closeEditModal} onOk={this.onEditModalOk}>
        <Form labelCol={{ span: 7 }} ref={(form) => {
          if (!this.formRefEdit) {
            form.setFieldsValue(editItem)
          }
          this.formRefEdit = form
        }} onFinish={this.onEditFormSubmit}>
          <Form.Item name='uid' label={'房管UID'} required>
            <InputNumber style={{ width: '300px' }} min={1} disabled />
          </Form.Item>

          <Form.Item label={'绑定ssid'} required>
            <Form.Item name='ssid' required style={{ marginBottom: '5px' }}>
              <InputNumber style={{ width: '300px' }} min={1} disabled />
            </Form.Item>
            <span style={{ color: 'gray' }}>*如需修改绑定ssid请与产品联系</span>
          </Form.Item>

          {/* <Form.Item name='type' label='经营模式'> */}
          {/*  <Radio.Group onChange={(e) => { */}
          {/*    item.type = e.target.value */}
          {/*    if (item.type === 0) { */}
          {/*      // 天团厅，默认是房管模式 2 */}
          {/*      item.settlement = 2 */}
          {/*    } else { */}
          {/*      item.settlement = 0 */}
          {/*    } */}
          {/*    this.setState({ editItem: item }) */}
          {/*  } */}
          {/*  }> */}
          {/*    <Radio value={1}>个播厅</Radio> */}
          {/*    <Radio value={0}>天团厅</Radio> */}
          {/*  </Radio.Group> */}
          {/* </Form.Item> */}

          <Form.Item name='settlement' label='经营厅模式'>
            <Radio.Group onChange={(e) => {
              item.settlement = e.target.value
              this.setState({ editItem: item })
            }}>
              <Radio value={0}>主持人模式</Radio>
              <Radio value={1}>收礼人模式</Radio>
              <Radio value={2}>房管模式</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal>
    )
  }

  showEditGradeModal = (item, from) => {
    let editItem = Object.assign({}, item)
    editItem.gradeID = 0
    editItem.labelType = 0
    if (from === 'label') {
      editItem.skipPeriod = [moment(), moment()]
    }
    if (this.formRefEdit) {
      this.formRefEdit.setFieldsValue(editItem)
    }
    this.setState({ editGradeVisible: true, editItem: editItem, editFrom: from, endValue: undefined })
  }

  closeEditGradeModal = () => {
    if (this.formRefEdit) {
      this.formRefEdit.resetFields()
    }
    this.setState({ editGradeVisible: false })
  }

  onGradeFormSubmit = (values) => {
    const { editItem, editMode, editFrom, endValue } = this.state
    // if (editFrom === 'grade' && editItem.gradeID === 0) {
    //   message.error('请输入房管厅评级!')
    //   return
    // }

    // if (Array.isArray(values.skipPeriod) && values.skipPeriod.length === 2) {
    //   startMonth = values.skipPeriod[0].format('YYYYMM')
    //   endMonth = values.skipPeriod[1].format('YYYYMM')
    // }

    // if (editFrom !== 'grade' && editItem.labelType === 0) {
    //   message.error('请输入房管厅标签!')
    //   return
    // }
    let startMonth = ''
    let endMonth = ''
    if (editFrom === 'label' && editItem.labelType > 0 && endValue !== undefined) {
      startMonth = moment().format('YYYYMM')
      endMonth = moment(endValue).format('YYYYMM')
      if (endMonth.length !== 6 || moment(startMonth, 'YYYYMM').unix() > moment(endMonth, 'YYYYMM').unix()) {
        console.log('startMonth', startMonth, endMonth)
        message.error('请输入不考核周期结束时间!')
        return
      }
    }
    if (editFrom === 'label' && editItem.labelType > 0 && (startMonth === '' || endMonth === '')) {
      console.log('', startMonth, endMonth)
      message.error('请输入不考核周期结束时间!')
      return
    }

    let submitItem = {}
    if (editFrom === 'grade') {
      submitItem = {
        editFrom: editFrom,
        sid: editItem.playSid,
        ssid: editItem.ssid,
        mgrUID: editItem.uid,
        gradeID: editItem.gradeID
      }
    } else if (editFrom === 'label') {
      submitItem = {
        editFrom: editFrom,
        sid: editItem.playSid,
        ssid: editItem.ssid,
        mgrUID: editItem.uid,
        label: editItem.labelType,
        startMonth: startMonth,
        endMonth: endMonth
      }
    }
    console.log('待提交的数据: ', submitItem, values)
    const reqType = `${namespace}/updateRoomMgrGrade`
    const { dispatch } = this.props
    let self = this
    // let close = message.loading('执行中，请稍候......')
    dispatch({
      type: reqType,
      payload: submitItem,
      callback: (rsp) => {
        // close()
        if (rsp && rsp.status === 0) {
          dispatch({
            type: `${namespace}/getRoomMgrList`
          })
          self.setState({ editGradeVisible: false, editItem: undefined })
        } else {
          message.error(editMode + ' 失败: ' + rsp.msg)
        }
      }
    })
    this.setState({ editGradeVisible: false, editItem: undefined })
  }

  onEndChange = (value) => {
    // this.onChange('endValue', value)
    this.setState({ 'endValue': value })
  }

  // 修改评级
  renderUpdateGradeModal = () => {
    const { editGradeVisible, editItem, editFrom } = this.state
    let item = editItem || {}
    console.log('renderUpdateGradeModal', editItem)

    function disabledDate (current) {
      // Can not select days before today
      return current && current < moment().subtract(1, 'days').endOf('day')
    }

    return (
      <Modal width={500} title={editFrom === 'grade' ? '修改房管厅评级' : '修改房管厅标签'} visible={editGradeVisible} onCancel={this.closeEditGradeModal} onOk={this.onEditModalOk}>
        <Form labelCol={{ span: 7 }} ref={(form) => {
          if (!this.formRefEdit) {
            form.setFieldsValue(editItem)
          }
          this.formRefEdit = form
        }} onFinish={this.onGradeFormSubmit}>

          <Form.Item label={'房管厅ssid'} required>
            <Form.Item name='ssid' required style={{ marginBottom: '5px' }}>
              <InputNumber style={{ width: '300px' }} min={1} disabled />
            </Form.Item>
          </Form.Item>

          <Form.Item label={'房管厅名称'} required>
            <Form.Item name='name' required style={{ marginBottom: '5px' }}>
              <InputNumber style={{ width: '300px' }} min={1} disabled />
            </Form.Item>
          </Form.Item>

          <Form.Item name='gradeID' label='房管厅评级' hidden={editFrom === 'label'}>
            <Select style={{ width: 120 }} placeholder='请选择' onChange={(v) => {
              editItem.gradeID = v
              this.setState({ editItem: editItem })
            }
            } options={gradeOptionsV2} />
            <br />
            <span style={{ color: 'red', marginTop: 12 }}>修改后评级将在审批通过后次日生效</span>
          </Form.Item>

          {/* <Form.Item name='labelType' label='房管厅标签' hidden={editFrom === 'label' ? true : editItem === undefined || editItem.grade === undefined || editItem.grade !== 'S'}> */}
          {/*  <Select style={{ width: 200 }} placeholder='请选择' onChange={(e) => { */}
          {/*    item.labelType = e */}
          {/*    this.setState({ editItem: item }) */}
          {/*  } */}
          {/*  }> */}
          {/*    <Option value={0}>无</Option> */}
          {/*    <Option value={2}>高质量外站厅</Option> */}
          {/*  </Select> */}
          {/* </Form.Item> */}

          {/* <Form.Item name='labelType' label='房管厅标签' hidden={editFrom === 'label' ? true : editItem === undefined || editItem.grade === undefined || editItem.grade !== 'B'}> */}
          {/*  <Select style={{ width: 200 }} placeholder='请选择' onChange={(e) => { */}
          {/*    item.labelType = e */}
          {/*    this.setState({ editItem: item }) */}
          {/*  } */}
          {/*  }> */}
          {/*    <Option value={0}>无</Option> */}
          {/*    <Option value={1}>新厅/回流厅</Option> */}
          {/*  </Select> */}
          {/* </Form.Item> */}

          <Form.Item name='labelType' label='房管厅标签' hidden={editFrom !== 'label'}>
            <Select style={{ width: 200 }} placeholder='请选择' onChange={(e) => {
              item.labelType = e
              this.setState({ editItem: item })
            }
            }>
              <Option value={0}>无</Option>
              <Option value={1}>新厅/回流厅</Option>
              <Option value={2}>高质量外站厅</Option>
            </Select>
          </Form.Item>

          <Form.Item name='skipPeriod' label='不考核周期' hidden={editItem === undefined || editItem.labelType === undefined || editItem.labelType === 0}>
            {/* <RangePicker disabledDate={disabledDate} style={{ width: 200 }} picker='month' showToday /> */}
            <DatePicker
              disabled
              picker='month'
              disabledDate={disabledDate}
              // showTime={{ format: 'HH:mm' }}
              format='YYYY-MM'
              value={moment()}
              // onChange={this.onStartChange}
              style={{ marginLeft: 10, width: 100 }}
            />
            <span style={{ marginLeft: 10 }}>~</span>
            <DatePicker
              disabledDate={disabledDate}
              // showTime={{ format: 'HH:mm' }}
              format='YYYY-MM'
              picker='month'
              // value={moment()}
              placeholder='结束时间'
              onChange={this.onEndChange}
              style={{ marginLeft: 10, width: 100 }}
            />
          </Form.Item>
          <Form.Item hidden={item.labelType !== 1 && item.labelType !== 2}>
            <span hidden={item.labelType !== 1} style={{ color: 'blue', marginLeft: 40 }}>修改后评级将自动变为<span style={{ color: 'red' }}>"B"</span>(在审批通过后次日生效)</span>
            <span hidden={item.labelType !== 2} style={{ color: 'blue', marginLeft: 40 }}>修改后评级将自动变为<span style={{ color: 'red' }}>"S"</span>(在审批通过后次日生效)</span>
          </Form.Item>
        </Form>
      </Modal>
    )
  }

  closeHistoryModal = () => {
    if (this.formRefEdit) {
      this.formRefEdit.resetFields()
    }
    this.setState({ historyVisible: false })
  }

  showHistoryModal = (item) => {
    let editItem = Object.assign({}, item)
    const { dispatch } = this.props
    let data = {
      sid: editItem.playSid,
      ssid: editItem.ssid,
      mgrUID: editItem.uid
    }
    dispatch({
      type: `${namespace}/getAssessHistory`,
      payload: data
    })
    this.setState({ historyVisible: true, editItem: editItem })
  }

  // 修改评级
  renderAssessHistoryModal = () => {
    const { historyVisible, editItem } = this.state
    const { assessHistory } = this.props.model
    console.log('renderAssessHistoryModal', editItem)
    let historyPageValue = {
      defaultPageSize: 10,
      pageSizeOptions: ['10', '100', '500'],
      showSizeChanger: true,
      onChange: () => { this.setState({ selectedRowKeysHistory: null }) },
      showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
    }
    return (
      <Modal width={1200} title={'历史评级记录'} visible={historyVisible}
        onCancel={this.closeHistoryModal} onOk={this.closeHistoryModal}>
        房管厅ssid: {editItem === undefined ? '' : editItem.ssid}
        <br />
        房管厅名称：{editItem === undefined ? '' : editItem.name}
        <br />
        <Text type='danger'>展示2024年6月(上线后)以来所有月份数据，可分页 </Text>
        <Table columns={this.historyColumns} size='small' pagination={historyPageValue}
          dataSource={assessHistory} rowKey={record => record.id} />
      </Modal>
    )
  }

  // ================= Formater ===================

 manualGradeForamter = (systemGrade, manualGrade) => {
   let desc = gradeFormater(manualGrade)
   if (systemGrade === manualGrade) {
     return desc
   }
   return `<p style="color: red">${desc}</p>`
 }

 parseAprItem = (before) => {
   let after = {
     month: before.month,
     ssid: before.ssid,
     roomName: before.roomName,
     mgrUID: before.mgrUID,
     mgrIMID: before.mgrIMID,
     signSID: before.signSID,
     signASID: before.signASID,
     amount: before.amount,
     activeCompereCount: before.activeCompereCount,
     activeGuestCount: before.activeGuestCount,
     roomValidDays: before.roomValidDays,
     systemGrade: before.isSkipAssess ? '-' : gradeFormater(before.systemGrade),
     manualGrade: before.isSkipAssess ? '-' : this.manualGradeForamter(before.systemGrade, before.manualGrade)
   }
   return after
 }

 // ================= query data ===================

 queryAssessInfo = () => {
   this.callModel('getAssessInfo', {
     params: {},
     isDetailMode: true,
     cbFunc: (ret) => {
       const { status, msg, list } = ret
       if (status !== 0) {
         message.warn('获取复核状态错误:' + msg)
         return
       }
       this.setState({ assessInfo: list })
     }
   })
 }

 // 获取复核数据并展示复核模态框
 onBeforeReview = (assessInfo) => {
   const { month, info } = assessInfo
   if (!month) {
     message.warn('考核数据未生成')
     return
   }
   if (info.aprResult === 'Passed') {
     message.warn('复核已完成')
     return
   }
   if (info.aprResult === 'OnGoing') {
     message.warn('当前复核正在审批中')
     return
   }
   this.callModel('getSystemAssessList', {
     params: {},
     isJsonMode: true,
     isDetailMode: true,
     cbFunc: (ret) => {
       const { status, msg, list } = ret
       if (status !== 0) {
         message.warn('查询复核列表失败:' + msg)
         return
       }
       let fixlist = list.map(item => {
         if (!item.manualGrade) {
           item.manualGrade = item.systemGrade
         }
         return item
       })
       this.setState({ reviewVisible: true, reviewList: fixlist })
     }
   })
 }

 // ================= post update ===================

 // 提交复核
 onSubmitReview = (list) => {
   let myNick = getCookie('username')
   let newGrade = {}
   let fixedList = list.map(item => {
     return this.parseAprItem(item)
   })
   let count = 0
   list.forEach(item => {
     if (item.isSkipAssess) {
       return
     }
     count++
     newGrade[item.gradeInfo.id] = item.manualGrade
   })
   let aprData = JSON.stringify(fixedList)
   let params = {
     wcText: `${myNick} 申请复核房管厅评级信息, 参与评级数量: ${count}。`,
     aprData: aprData,
     newGrade: newGrade
   }
   this.callModel('gradeReview', {
     params: params,
     isJsonMode: true,
     isDetailMode: true,
     isRawRespMode: true,
     cbFunc: (ret) => {
       const { status, msg } = ret
       if (status !== 0) {
         message.error('操作失败: ' + msg)
         return
       }
       this.setState({ reviewVisible: false })
       message.success('送审成功～')
       this.setState({ reviewVisible: false })
       setTimeout(() => {
         this.queryAssessInfo()
       }, 2000)
     }
   })
 }

 // ================= Other ===================

 updateReviewParams = (field, value) => {
   const { reviewParams } = this.state
   let cp = deepClone(reviewParams)
   cp[field] = value
   this.setState({ reviewParams: cp })
 }

 updateReviewList = (id, newGrade) => {
   const { reviewList } = this.state
   let newList = reviewList.map(item => {
     if (item.id === id) {
       item.manualGrade = newGrade
     }
     return item
   })
   this.setState({ reviewList: newList })
 }

 filterReviewList = (before, params) => {
   const { mgrUID, gradeID, asid, ssid, mgrIMID } = params
   let after = []
   before.forEach(item => {
     if (mgrUID > 0 && item.mgrUID !== mgrUID) {
       return
     }
     if (gradeID > 0 && item.systemGrade !== gradeID) {
       return
     }
     if (asid > 0 && item.signASID !== asid) {
       return
     }
     if (ssid > 0 && item.ssid !== ssid) {
       return
     }
     if (mgrIMID > 0 && item.mgrIMID !== mgrIMID) {
       return
     }
     after.push(item)
   })
   return after
 }

 // ================= Render ===================

 render () {
   const { addModalVisible, searchParams, opType } = this.state
   const { reviewVisible, reviewList, reviewParams } = this.state
   const { assessInfo } = this.state
   let isUpdate = !(opType === 'ADD')
   let canReview = assessInfo?.month !== '' && assessInfo?.info?.aprResult !== 'Passed' // 是否可点复核

   return (
     <Card>
       <Row>
         {/* 提示栏 */}
         <Col span={24} style={{ margin: '1em 0' }}>
           房管UID: <InputNumber style={{ width: '10em', marginLeft: 3, marginRight: 6 }} placeholder='房管UID'
             onChange={(v) => { let cp = { ...searchParams }; cp.uid = v; this.setState({ searchParams: cp }) }} />
           房管YY号: <InputNumber style={{ width: '8em', marginLeft: 3, marginRight: 6 }} placeholder='房管YY号'
             onChange={(v) => { let cp = { ...searchParams }; cp.imid = v; this.setState({ searchParams: cp }) }} />
           短位ID: <InputNumber style={{ width: '8em', marginLeft: 3, marginRight: 6 }} placeholder='短位ID'
             onChange={(v) => { let cp = { ...searchParams }; cp.asid = v; this.setState({ searchParams: cp }) }} />
           子频道ssid: <InputNumber style={{ width: '8em', marginLeft: 3, marginRight: 6 }} placeholder='子频道ssid'
             onChange={(v) => { let cp = { ...searchParams }; cp.ssid = v; this.setState({ searchParams: cp }) }} />
           {/* 经营类型: <Select value={searchParams.type} options={statusOptions} style={{ width: '6em' }} */}
           {/*  onChange={(v) => { let cp = { ...searchParams }; cp.type = v; this.setState({ searchParams: cp }) }} /> */}
           经营厅模式: <Select value={searchParams.settlement} options={settlementOptions} style={{ width: '8em', marginLeft: 3, marginRight: 6 }}
             onChange={(v) => { let cp = { ...searchParams }; cp.settlement = v; this.setState({ searchParams: cp }) }} />
           是否续约: <Select allowClear value={searchParams.hadRenew} options={hadRenewOptions} style={{ width: '6em', marginLeft: 3, marginRight: 6 }}
             onChange={(v) => { let cp = { ...searchParams }; cp.hadRenew = v; this.setState({ searchParams: cp }) }} />
           当前评级: <Select allowClear value={searchParams.gradeID} options={gradeOptionsV2} style={{ width: '6em', marginLeft: 3, marginRight: 6 }}
             onChange={(v) => { let cp = { ...searchParams }; cp.gradeID = v; this.setState({ searchParams: cp }) }} />
           <Button type='primary' onClick={() => { this.onExport(this.getMainColumns(canReview), this.getFilterList()) }}>导出</Button>
           <Button type='primary' disabled={!canReview} onClick={() => { this.onBeforeReview(assessInfo) }} style={{ marginLeft: '0.5em' }}>复核</Button>
         </Col>

         {/* 列表数据 */}
         <Col span={24}>
           <Table columns={this.getMainColumns(canReview)} size='small' pagination={this.defaultPageValue} dataSource={this.getFilterList()} rowKey={record => record.uid} scroll={{ x: 'max-content' }} />
         </Col>

         <Modal visible={addModalVisible} forceRender title={opType === 'ADD' ? '添加白名单' : '更新添加白名单'} onCancel={() => { this.setState({ addModalVisible: false }) }} footer={null} >
           <Form ref={from => { this.formRef = from }} initialValues={{ businessType: 1, monthLimit: 1000000, dailyLimit: 500000, sid: 0, startTime: moment().unix() }} onFinish={(f) => this.onSubmitUpdate(f, opType)} labelCol={{ span: 5 }}>
             <Form.Item style={{ width: '50em' }} label='公会短号' name='asid' rules={[{ required: true, message: '必填' }]}>
               <Input style={{ width: '35%' }} />
             </Form.Item>
             <Form.Item style={{ width: '50em' }} label='房管昵称' name='roomMgrName' rules={[{ required: true, message: '必填' }]}>
               <Input style={{ width: '35%' }} />
             </Form.Item>
             {/* <Form.Item style={{ width: '50em' }} label='经营厅类型' name='roomMgrType' rules={[{ required: true, message: '必填' }]}> */}
             {/*  <RadioGroup> */}
             {/*    <Radio value={1}>天团厅</Radio> */}
             {/*    <Radio value={2}>个播厅</Radio> */}
             {/*  </RadioGroup> */}
             {/* </Form.Item> */}
             <Form.Item style={{ width: '50em' }} label='签约owUID' name='owUID' rules={[{ required: true, message: '必填' }]}>
               <InputNumber disabled={isUpdate} placeholder='签约owUID' style={{ width: '35%' }} />
             </Form.Item>
             <Form.Item style={{ width: '50em' }} label='签约sid' name='sid' rules={[{ required: true, message: '必填' }]}>
               <InputNumber disabled={isUpdate} placeholder='签约sid' style={{ width: '35%' }} />
             </Form.Item>
             <Form.Item style={{ width: '50em' }} label='开播ssid' name='ssid' rules={[{ required: true, message: '必填' }]}>
               <InputNumber disabled={isUpdate} placeholder='开播ssid' style={{ width: '35%' }} />
             </Form.Item>
             <Form.Item style={{ width: '50em' }} label='房管UID' name='roomMgrUID' rules={[{ required: true, message: '必填' }]}>
               <InputNumber disabled={isUpdate} placeholder='房管UID' style={{ width: '35%' }} />
             </Form.Item>
             <Form.Item style={{ width: '50em' }} label='抽成比例(%)' name='ratio' rules={[{ required: true, message: '必填' }]}>
               <InputNumber disabled={isUpdate} placeholder='抽成比例(%)' style={{ width: '35%' }} />
             </Form.Item>
           </Form>
           <Row style={{ marginLeft: 200 }} >
             <Space>
               <Button onClick={() => { this.setState({ addModalVisible: false }) }}>取消</Button>
               <Button type='primary' onClick={() => this.formRef.submit()}>确定</Button>
             </Space>
           </Row>
         </Modal>

         <Modal visible={reviewVisible} title='房管厅评级复核' width='85vw' destroyOnClose
           onCancel={() => { this.setState({ reviewVisible: false }) }}
           okText='提交审核' cancelText='取消'
           onOk={() => this.onSubmitReview(reviewList)} >
           <Row>
             <Col span={24}>
               <Space>
                 <div>
                   SSID: <InputNumber style={{ width: '10em' }} value={reviewParams.ssid} onChange={v => { this.updateReviewParams('ssid', v) }} />
                 </div>
                 <div>
                   房管UID: <InputNumber style={{ width: '10em' }} value={reviewParams.mgrUID} onChange={v => { this.updateReviewParams('mgrUID', v) }} />
                 </div>
                 <div>
                   房管YY号: <InputNumber style={{ width: '10em' }} value={reviewParams.mgrIMID} onChange={v => { this.updateReviewParams('mgrIMID', v) }} />
                 </div>
                 <div>
                   签约公会asid: <InputNumber style={{ width: '10em' }} value={reviewParams.asid} onChange={v => { this.updateReviewParams('asid', v) }} />
                 </div>
                 <div>
                   系统评级: <Select style={{ width: '6em' }} options={[{ label: '所有', value: -1 }, ...gradeOptionsV2]} value={reviewParams.gradeID} onChange={v => { this.updateReviewParams('gradeID', v) }} />
                 </div>
                 <Button onClick={() => { onExportExcel(this.reviewColumn, reviewList, '房管厅等级复核.xlsx') }} type='primary'>导出</Button>
               </Space>
             </Col>
             <Col span={24}>
               <ReviewInfo data={reviewList} guildGradeList={['TODO']} />
             </Col>
             <Col span={24}>
               <Table columns={this.reviewColumn} dataSource={this.filterReviewList(reviewList, reviewParams)} />
             </Col>
           </Row>
         </Modal>

       </Row>

       {this.renderEditModal()}
       {this.renderUpdateGradeModal()}
       {this.renderAssessHistoryModal()}
     </Card>
   )
 }
}

export default RoomMangeList
