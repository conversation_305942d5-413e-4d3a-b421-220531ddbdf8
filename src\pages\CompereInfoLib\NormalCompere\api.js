import request from '@/utils/request'
import { stringify } from 'qs'

function filterNumberArray (value) {
  if (!value) {
    return ''
  }
  value = value.replace(/([^\d]|[\s，, ])+/g, ',')
  if (value.endsWith(',')) {
    value = value.substring(0, value.length - 1)
  }
  return value
}

// 获取列表
export function listCompere (params) {
  params.uid = filterNumberArray(params.uid)
  params.yyno = filterNumberArray(params.yyno)
  return request(`/compere_data_base/list_compere?${stringify(params)}`)
}

// 刷新
export function refreshCompereList (params) {
  params.uids = filterNumberArray(params.uids)
  return request(`/compere_data_base/refresh_compere_list?${stringify(params)}`)
}

// 查询备注信息
export function getRemarkInfo (params) {
  return request(`/compere_data_base/get_remark_info?${stringify(params)}`)
}

// 更新备注信息
export function updateRemarkInfo (params) {
  return request(`/compere_data_base/update_remark?${stringify(params)}`)
}

// 更新备注信息
export function getRemarkHistory (params) {
  return request(`/compere_data_base/get_remark_history?${stringify(params)}`)
}

export function getCompereDetailInfo (params) {
  return request(`/compere_data_base/get_compere_detail_info?${stringify(params)}`)
}

// 恢复超主身份
export function recoverSuperCompere (params) {
  return request(`/compere_data_base/recover_super_compere`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
