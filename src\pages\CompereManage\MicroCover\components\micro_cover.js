import React, { Component } from 'react'
import PageHeader<PERSON>rapper from '@/components/PageHeaderWrapper'
import { Table, Divider, Button, Form, Card, Modal, Input, Popconfirm, message, Upload, Row, Col } from 'antd'
import { connect } from 'dva'
import PopImage from '@/components/PopImage'
import PicturesWall from '@/components/PicturesWall'
import PicturesWallCheck from '@/components/PicturesWallCheck'
import { UploadOutlined } from '@ant-design/icons'
var moment = require('moment')
const namespace = 'microCover'
const FormItem = Form.Item

const getListUri = `${namespace}/getList`
const updateItemUri = `${namespace}/updateItem`
const removeItemUri = `${namespace}/deleteItem`

let from = 1
let pageIndex = 0 // todo
let pageSize = 100 // todo

@connect(({ microCover }) => ({
  model: microCover
}))

class MicroCover extends Component {
  constructor (props) {
    super(props)
    const { configList } = props
    this.state = { selectedRow: '', list: configList, visible: false, isUpdate: false, value: {}, selectedRowKeys: [], searchDone: false, searchUID: 0 }
  }
  // 取审核状态说明
  getStatusDesc = (status) => {
    switch (status) {
      case 0: return '审核中'
      case 1: return '审核通过'
      case 2: return '审核不通过'
    }
    return ''
  }
  renderCover = (record, index) => {
    if (!record || !record[index]) {
      return ''
    }
    return (
      <div>
        <PopImage value={record[index].url} /> 
        <br />
        <div>
          { !record[index].url || record[index].url.length === 0 ? <span><font>未上传</font></span> : <font>{this.getStatusDesc(record[index].status)}</font> }
          <br />
          { !record[index].auditTime || record[index].auditTime === 0 ? <span> </span> : <font>内审（{moment.unix(record[index].auditTime).format('YYYY-MM-DD HH:mm:ss')}）</font> }
        </div>
      </div>
    )
  }
  // column structs.
  columns = [
    { title: '序号', width: 40, fixed: 'left', dataIndex: 'index', align: 'center' },
    { title: 'uid', width: 80, fixed: 'left', dataIndex: 'uid', align: 'center' },
    { title: '主播YY号', width: 80, fixed: 'left', dataIndex: 'imid', align: 'center' },
    { title: '主播昵称', width: 80, dataIndex: 'nick', align: 'center' },
    { title: '16:9(1)', align: 'center', width: 100, render: (text, record) => this.renderCover(record.cover16x9, 0) },
    { title: '16:9(2)', align: 'center', width: 100, render: (text, record) => this.renderCover(record.cover16x9, 1) },
    { title: '16:9(3)', align: 'center', width: 100, render: (text, record) => this.renderCover(record.cover16x9, 2) },
    { title: '16:9(4)', align: 'center', width: 100, render: (text, record) => this.renderCover(record.cover16x9, 3) },
    { title: '16:9(5)', align: 'center', width: 100, render: (text, record) => this.renderCover(record.cover16x9, 4) },
    { title: '8:9(1)', align: 'center', width: 100, render: (text, record) => this.renderCover(record.cover8x9, 0) },
    { title: '8:9(2)', align: 'center', width: 100, render: (text, record) => this.renderCover(record.cover8x9, 1) },
    { title: '8:9(3)', align: 'center', width: 100, render: (text, record) => this.renderCover(record.cover8x9, 2) },
    { title: '8:9(4)', align: 'center', width: 100, render: (text, record) => this.renderCover(record.cover8x9, 3) },
    { title: '8:9(5)', align: 'center', width: 100, render: (text, record) => this.renderCover(record.cover8x9, 4) },
    { title: '操作选项',
      width: 80,
      align: 'center',
      export: false,
      render: (text, record) => (
        <span>
          <a onClick={this.showModal(true, record)}>更新</a><Divider type='vertical' />
          <Popconfirm title={showDelTitle(record.asid)} onConfirm={this.handleDel(record.uid)} okText='是的' cancelText='暂不'>
            <a href=''>删除</a>
          </Popconfirm>
        </span>)
    }
  ]

  // defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  pagination = { pageSizeOptions: ['10', '50', '200', '1000'], showSizeChanger: true, pageSize: 10, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }
  defaultPageValue = {
    defaultPageSize: 10,
    pageSizeOptions: ['10', '50', '100', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { businessType: 0, pageIndex: 0, pageSize: 1000, visible: false, isUpdate: false, value: {}, title: '', searchKey: '', searchResult: [], searchDone: false, searchUID: 0, searchAsid: 0, searchTitleStatus: -1, searchPictureStatus: -1, uploadTitleStatus: -1, uploadPictureStatus: -1, searchBusinessType: -1 }
  // show modal
  showModal = (isUpdate, record) => () => {
    if (record == null) record = {}
    let v = $.extend(true, {}, record)
    if (this.formRef) {
      this.formRef.resetFields()
      v.c0 = record.cover16x9 && record.cover16x9[0] ? record.cover16x9[0].url : ''
      v.c1 = record.cover16x9 && record.cover16x9[1] ? record.cover16x9[1].url : ''
      v.c2 = record.cover16x9 && record.cover16x9[2] ? record.cover16x9[2].url : ''
      v.c3 = record.cover16x9 && record.cover16x9[3] ? record.cover16x9[3].url : ''
      v.c4 = record.cover16x9 && record.cover16x9[4] ? record.cover16x9[4].url : ''
      v.c5 = record.cover8x9 && record.cover8x9[0] ? record.cover8x9[0].url : ''
      v.c6 = record.cover8x9 && record.cover8x9[1] ? record.cover8x9[1].url : ''
      v.c7 = record.cover8x9 && record.cover8x9[2] ? record.cover8x9[2].url : ''
      v.c8 = record.cover8x9 && record.cover8x9[3] ? record.cover8x9[3].url : ''
      v.c9 = record.cover8x9 && record.cover8x9[4] ? record.cover8x9[4].url : ''
      this.formRef.setFieldsValue(v)
    }
    this.setState({ value: record, visible: true, isUpdate: isUpdate, title: isUpdate ? '更新' : '添加' })
  }

  // hide modal
  hideModal = () => {
    this.setState({ visible: false })
  }

  // update
  handleSubmit = () => {
    this.formRef.submit() // 提交表单 触发 onFinish 事件
  }

  // reset search info
  handleReset = clearFilters => () => {
    clearFilters()
    this.setState({ searchText: '' })
  }

  newCover = (old) => {
    var cover = []
    if (old) {
      cover = [...old]
    }
    for (var i = cover.length; i < 5; i++) {
      cover.push({ url: '', index: i, status: 0 })
    }
    return cover
  }

  // 添加 与 编辑
  onFinish = field => {
    const { dispatch } = this.props
    if (parseInt(field.uid) === 0) {
      message.error('参数错误')
      return
    }
    console.log(field)
    const { value } = this.state
    var uid = parseInt(field.uid)
    var cover16x9 = this.newCover(value.cover16x9)
    cover16x9[0].url = field.c0
    cover16x9[1].url = field.c1
    cover16x9[2].url = field.c2
    cover16x9[3].url = field.c3
    cover16x9[4].url = field.c4
    
    var cover8x9 = this.newCover(value.cover8x9)
    cover8x9[0].url = field.c5
    cover8x9[1].url = field.c6
    cover8x9[2].url = field.c7
    cover8x9[3].url = field.c8
    cover8x9[4].url = field.c9

    let data = { uid: uid, cover16x9: cover16x9, cover8x9: cover8x9 }
    dispatch({
      type: updateItemUri,
      payload: data
    }).then(res => {
      this.getList()
    })
    this.formRef.resetFields()
    this.setState({ visible: false })
  }

  componentWillReceiveProps (nextProps) {
    const { configList } = nextProps
    this.setState({ list: configList })
  }

  getList () {
    const { dispatch } = this.props
    const { searchUID } = this.state
    let data = { uid: searchUID }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  onSearch = () => {
    const { dispatch } = this.props
    const { searchUID } = this.state
    let data = { uid: searchUID }
    dispatch({
      type: getListUri,
      payload: data
    })
  }

  // delete
  handleDel = key => e => {
    const { dispatch } = this.props
    const data = { ids: key + '' }
    dispatch({
      type: removeItemUri,
      payload: data
    }).then(res => {
      this.getList()
    })
  }

  // get list from server.
  componentDidMount () {
    const { dispatch, model: { list } } = this.props
    const { searchUID, searchAsid, searchTitleStatus, searchPictureStatus, businessType } = this.state
    let data = { from: from, pageIndex, pageSize, searchUID, searchAsid, searchTitleStatus, searchPictureStatus, businessType }
    dispatch({
      type: getListUri,
      payload: data
    })
    if (Array.isArray(list) && list.length > 0) {
      this.setState({ list })
    }
  }

  // save form info
  saveFormRef = (formRef) => {
    this.formRef = formRef
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  onExport = () => {
    let data = { uid: this.state.searchUID }
    var params = Object.keys(data).map(function (key) {
      if (!data[key]) {
        data[key] = 0
      }
      return encodeURIComponent(key) + '=' + encodeURIComponent(data[key])
    }).join('&')
    return '/new_compere_info/export_micro_cover_list?' + params
  }

  onSelectChange = (selectedRowKeys, selectedRows) => {
    let value = selectedRows.map(item => item.uid).join(',')
    this.setState({ selectedRowKeys })
    this.setState({ selectedRow: value })
  }

  // delete
  handleBatchDel = () => {
    const { dispatch } = this.props
    if (this.state.selectedRow === '') {
      message.error('请先选中再操作！')
      return
    }
    const data = { ids: this.state.selectedRow }
    // confirm({
    //   title: '确认删除?',
    //   onOk () {
    //     dispatch({
    //       type: removeItemUri,
    //       payload: data
    //     }).then(res => {
    //       this.setState({ selectedRowKeys: null })
    //       this.getList()
    //     })
    //   }
    // })
    dispatch({
      type: removeItemUri,
      payload: data
    }).then(res => {
      // this.setState({ selectedRowKeys: null })
      this.getList()
    })
  }

  // 导入配置回调
  // 导入配置回调
  onUploadFileStatusChange = (info) => {
    const { status, name, response } = info.file
    let success = false
    if (status === 'uploading') {
      return
    }
    do {
      if (status === 'error') {
        console.error('upload failed: info=', info)
        break
      }
      if (!response) {
        console.error('unexpect response: response=', response)
        break
      }
      if (response.status !== 0) {
        console.error('unexpect response: response=', response)
        break
      }
      if (response.count !== response.success) {
        message.error(`${name}上传结果异常，请检查控制台,成功:'` + response.success + ',失败:' + response.fail)
        // console.error('unexpect respones: response=', response)
        break
      }
      success = true
    } while (false)

    if (success) {
      message.info(`${name} 成功导入`)
    }
    this.getList()
  }
  // content
  render () {
    const { isUpdate, searchResult, searchDone, visible, title, selectedRowKeys, list } = this.state
    const formItemLayout = {
      labelCol: {
        xs: { span: 10 },
        sm: { span: 7 }
      },
      wrapperCol: {
        xs: { span: 12 },
        sm: { span: 16 }
      }
    }
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange
      // ,
      // getCheckboxProps: (record) => ({
      //   disabled: record.name === 'Disabled User'
      // Column configuration not to be checked
      // name: record.name
      // })
    }

    // const uploadFile1Props = {
    //   name: 'files',
    //   multiple: false,
    //   data: { bucket: 'makefriends' },
    //   action: 'https://fts.yy.com/fs/uploadfiles',
    //   maxCount: 1,
    //   showUploadList: false,
    //   accept: '.xlsx'
    // }
    return (
      <PageHeaderWrapper>
        <Card>
          <div>
            <Input placeholder='搜索uid' onChange={e => this.setState({ searchUID: e.target.value, searchDone: false })} style={{ width: 120 }} /> {/* 搜索按钮 */}
            <Divider type='vertical' /> {/* 分割线 */}
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onSearch}>搜索</Button>
            <Button style={{ marginLeft: 5 }} type='primary' onClick={this.showModal(false, this.defaultValue)}>添加</Button>
            <Popconfirm title='批量删除所选项' onConfirm={this.handleBatchDel} okText='是的' cancelText='暂不'>
              <Button style={{ marginLeft: 5 }} type='primary' >批量删除</Button>
            </Popconfirm>
            {/* <Button style={{ marginLeft: 5 }} type='primary' onClick={this.handleBatchDel}>批量删除</Button> */}
            <Button style={{ marginLeft: 5 }} type='primary' >
              <a href='http://makefriends.bs2dl.yy.com/1619753872_55d4c5273d62564021dbc9cc3d54e5f0.xlsx' type='primary' onClick={this.downLoad}>模板</a>
            </Button>
            <Button style={{ marginLeft: 5 }} type='primary' href={this.onExport()}>导出</Button>
            <Upload showUploadList={false} accept={'.xlsx'} action='/new_compere_info/import_micro_cover_list' onChange={(info) => this.onUploadFileStatusChange(info)} >
              <Button style={{ marginLeft: 5 }} type='primary' >
                <UploadOutlined /> 导入
              </Button>
            </Upload>
          </div>
          <Table rowKey={(record, index) => index} rowSelection={rowSelection} dataSource={searchDone ? searchResult : list} bordered columns={this.columns} pagination={this.defaultPageValue} scroll={{ x: 3100 }} />
        </Card>

        <Modal forceRender visible={visible} title={title} onCancel={this.hideModal} onOk={this.handleSubmit} maskClosable={false} width='25%'>
          <Form onFinish={this.onFinish} {...formItemLayout} ref={form => { this.formRef = form }}>
            <FormItem style={{ marginBottom: 10 }} label='主持uid' name='uid' rules={[{ required: true, message: '主持uid必填' }]}>
              <Input disabled={isUpdate} />
            </FormItem>

            <font style={{ marginBottom: 10 }}>推荐图配置：<font color='red'>注意 文件大小限制：50k-2M</font></font>
            <Row gutter={[16, 2]} style={{ marginBottom: 10 }} type='flex' justify='center' align='top' >
              <Col span={8} style={{ marginBottom: 10 }}>
                <FormItem name='c0' style={{ marginTop: 10, marginBottom: 0, marginLeft: 0, marginRight: 0 }} >
                  <PicturesWall minSize={50 * 1024} maxSize={2 * 1024 * 1024} minWidth={0} minHeight={0} title={'16:9(1)'} />
                </FormItem>
              </Col>
              <Col span={8} style={{ marginBottom: 10 }}>
                <FormItem name='c1' style={{ marginTop: 10, marginBottom: 0, marginLeft: 0, marginRight: 0 }} >
                  <PicturesWall minSize={50 * 1024} maxSize={2 * 1024 * 1024} minWidth={0} minHeight={0} title={'16:9(2)'} />
                </FormItem>
              </Col>
              <Col span={8} style={{ marginBottom: 10 }}>
                <FormItem name='c2' style={{ marginTop: 10, marginBottom: 0, marginLeft: 0, marginRight: 0 }} >
                  <PicturesWall minSize={50 * 1024} maxSize={2 * 1024 * 1024} minWidth={0} minHeight={0} title={'16:9(3)'} />
                </FormItem>
              </Col>
              <Col span={8} style={{ marginBottom: 10 }}>
                <FormItem name='c3' style={{ marginTop: 10, marginBottom: 0, marginLeft: 0, marginRight: 0 }} >
                  <PicturesWall minSize={50 * 1024} maxSize={2 * 1024 * 1024} minWidth={0} minHeight={0} title={'16:9(4)'} />
                </FormItem>
              </Col>
              <Col span={8} style={{ marginBottom: 10 }}>
                <FormItem name='c4' style={{ marginTop: 10, marginBottom: 0, marginLeft: 0, marginRight: 0 }} >
                  <PicturesWall minSize={50 * 1024} maxSize={2 * 1024 * 1024} minWidth={0} minHeight={0} title={'16:9(5)'} />
                </FormItem>
              </Col>
            </Row>
            <Row gutter={[16, 2]} style={{ marginBottom: 10 }} type='flex' justify='center' align='top' >
              <Col span={8}>
                <FormItem name='c5' style={{ marginTop: 10, marginBottom: 0, marginLeft: 0, marginRight: 0 }} >
                  <PicturesWallCheck minSize={50 * 1024} maxSize={2 * 1024 * 1024} minWidth={0} minHeight={0} title={'8:9(1)'} />
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem name='c6' style={{ marginTop: 10, marginBottom: 0, marginLeft: 0, marginRight: 0 }} >
                  <PicturesWallCheck minSize={50 * 1024} maxSize={2 * 1024 * 1024} minWidth={0} minHeight={0} title={'8:9(2)'} />
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem name='c7' style={{ marginTop: 10, marginBottom: 0, marginLeft: 0, marginRight: 0 }} >
                  <PicturesWallCheck minSize={50 * 1024} maxSize={2 * 1024 * 1024} minWidth={0} minHeight={0} title={'8:9(3)'} />
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem name='c8' style={{ marginTop: 10, marginBottom: 0, marginLeft: 0, marginRight: 0 }} >
                  <PicturesWallCheck minSize={50 * 1024} maxSize={2 * 1024 * 1024} minWidth={0} minHeight={0} title={'8:9(4)'} />
                </FormItem>
              </Col>
              <Col span={8}>
                <FormItem name='c9' style={{ marginTop: 10, marginBottom: 0, marginLeft: 0, marginRight: 0 }} >
                  <PicturesWallCheck minSize={50 * 1024} maxSize={2 * 1024 * 1024} minWidth={0} minHeight={0} title={'8:9(5)'} />
                </FormItem>
              </Col>
            </Row>
          </Form>
        </Modal>
      </PageHeaderWrapper>
    )
  }
}

function showDelTitle (asid) {
  return '确定要删除？'
}

export default MicroCover
