import React, { PureComponent } from 'react'
import { Spin, Menu, Dropdown, Avatar } from 'antd'
import styles from './index.module.less'
import { LogoutOutlined } from '@ant-design/icons'

export default class GlobalHeaderRight extends PureComponent {
  render () {
    const {
      currentUser,
      onMenuClick,
      theme
    } = this.props
    const menu = (
      <Menu className={styles.menu} selectedKeys={[]} onClick={onMenuClick}>
        <Menu.Item key='logout'>
          <LogoutOutlined />
          <span>退出登录</span>
        </Menu.Item>
      </Menu>
    )
    let className = styles.right
    if (theme === 'dark') {
      className = `${styles.right}  ${styles.dark}`
    }
    return (
      <div className={className}>
        {currentUser.name ? (
          <Dropdown overlay={menu}>
            <span className={`${styles.action} ${styles.account}`}>
              <Avatar
                size='small'
                className={styles.avatar}
                src={currentUser.avatar}
                alt='avatar'
              />
              <span className={styles.name}>{currentUser.name} ({currentUser.username})</span>
            </span>
          </Dropdown>
        ) : (
          <Spin size='small' style={{ marginLeft: 8, marginRight: 8 }} />
        )}
      </div>
    )
  }
}
