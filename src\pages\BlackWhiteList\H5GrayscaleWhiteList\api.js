import request from '@/utils/request'

export function getLists (isWhiteList) {
  let url = `/white_list/h5_grayscale_list?type=` + (isWhiteList ? 'white' : 'black')
  return request(url)
}

// 添加或删除白名单
export function whiteListAddOrDelete (operator, isWhiteList, sid, ssid, play) {
  if (operator !== 'DELETE' && operator !== 'ADD') {
    console.error('unexpect params: operator=' + operator)
    return
  }
  let form = 'sid=' + sid + '&ssid=' + ssid + '&play=' + play + '&operate=' + operator + '&type=' + (isWhiteList ? 'white' : 'black')
  return request(`/white_list/h5_grayscale_add_delete`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: form
  })
}
