import * as api from './api'
import { message } from 'antd'

export default {
  namespace: 'guildTaskRewardApproval',

  state: {
    list: [],
    notDealSum: 0,
    historyNotDealSum: 0,
    taskTime: '',
    optLimitTime: '',
    reachTaskGuildSum: 0,
    rewardSum: 0,
    tableLoading: false,
    listReferPeriod: [],
    listRewardSummary: []
  },

  reducers: {
    // 单个修改某个state成员
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    },

    displayList (state, { payload, notDealSum, historyNotDealSum, taskTime, optLimitTime, reachTaskGuildSum, rewardSum, tabIdx }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        list: payload,
        notDealSum: notDealSum,
        historyNotDealSum: historyNotDealSum,
        taskTime: taskTime,
        optLimitTime: optLimitTime,
        reachTaskGuildSum: reachTaskGuildSum,
        rewardSum: rewardSum,
        tabIdx: tabIdx
      }
    },

    displayList2 (state, { payload, taskTime, optLimitTime, reachTaskGuildSum, rewardSum, tabIdx }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        list: payload,
        taskTime: taskTime,
        optLimitTime: optLimitTime,
        reachTaskGuildSum: reachTaskGuildSum,
        rewardSum: rewardSum,
        tabIdx: tabIdx
      }
    },

    displayReferPeriodList (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        listReferPeriod: payload
      }
    },

    displayRewardSummary (state, { payload }) {
      if (!Array.isArray(payload)) {
        console.error('unexpect data type: data=', payload)
        return
      }
      return {
        ...state,
        listRewardSummary: payload
      }
    }
  },

  effects: {
    * getRewardApprovalList ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.getRewardApprovalList, payload)
      console.log(status, msg)
      let historyNotDealSum = data === null ? 0 : data.historyNotDealSum
      let notDealSum = data === null ? 0 : data.notDealSum
      let taskTime = data === null ? '' : data.taskTime
      let optLimitTime = data === null ? '' : data.optLimitTime
      let rewardSum = data === null ? 0 : data.rewardSum
      let reachTaskGuildSum = data === null ? 0 : data.reachTaskGuildSum
      let tabIdx = data === null ? 0 : data.tabIdx

      let dataNew = data === null ? [] : Array.isArray(data.list) ? data.list : []
      if (dataNew !== []) {
        for (let i = 0; i < dataNew.length; i++) {
          dataNew[i].idx = i + 1
        }
      }
      yield put({
        type: 'displayList',
        payload: dataNew,
        notDealSum: notDealSum,
        historyNotDealSum: historyNotDealSum,
        taskTime: taskTime,
        optLimitTime: optLimitTime,
        reachTaskGuildSum: reachTaskGuildSum,
        rewardSum: rewardSum,
        tabIdx: tabIdx
      })
    },

    * getRewardApprovalListReview ({ payload }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: { name: 'tableLoading', newValue: true }
      })
      let { data: { data } } = yield call(api.getRewardApprovalList, payload)
      let historyNotDealSum = data === null ? 0 : data.historyNotDealSum
      let notDealSum = data === null ? 0 : data.notDealSum
      let taskTime = data === null ? '' : data.taskTime
      let optLimitTime = data === null ? '' : data.optLimitTime
      let rewardSum = data === null ? 0 : data.rewardSum
      let reachTaskGuildSum = data === null ? 0 : data.reachTaskGuildSum
      let tabIdx = data === null ? 0 : data.tabIdx

      let dataNew = data === null ? [] : Array.isArray(data.list) ? data.list : []
      if (dataNew !== []) {
        for (let i = 0; i < dataNew.length; i++) {
          dataNew[i].idx = i + 1
        }
      }
      yield put({
        type: 'displayList',
        payload: dataNew,
        notDealSum: notDealSum,
        historyNotDealSum: historyNotDealSum,
        taskTime: taskTime,
        optLimitTime: optLimitTime,
        reachTaskGuildSum: reachTaskGuildSum,
        rewardSum: rewardSum,
        tabIdx: tabIdx
      })
      console.log('getRewardApprovalListReview')
      yield put({
        type: 'updateState',
        payload: { name: 'tableLoading', newValue: false }
      })
    },

    * getRewardRealSendList ({ payload }, { call, put }) {
      let { data: { data } } = yield call(api.getRewardRealSendList, payload)
      data = Array.isArray(data) ? data : []
      if (data !== []) {
        for (let i = 0; i < data.length; i++) {
          data[i].idx = i + 1
        }
      }
      console.log(data)
      yield put({
        type: 'displayList2',
        payload: data
      })
    },

    * rewardApproval1 ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.rewardApproval1, payload)
      if (status !== 0) {
        if (status === 10010) {
          message.info({ content: '当前不在任务结算周期内，暂无数据' })
          return
        }
        if (status === 10011) {
          message.info({ content: msg })
          return
        }
        if (status === 10013) {
          message.info({ content: msg })
          return
        }
        message.error({ content: '发起审批失败' })
      }
    },

    * rewardApproval3 ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.rewardApproval3, payload)
      if (status !== 0) {
        if (status === 10010) {
          message.info({ content: '当前不在任务结算周期内，暂无数据' })
          return
        }
        if (status === 10011) {
          message.info({ content: msg })
          return
        }
        if (status === 10013) {
          message.info({ content: msg })
          return
        }
        message.error({ content: '发起审批失败' })
      }
    },

    * getReferMonthList ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.getReferMonthList, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      if (data !== []) {
        for (let i = 0; i < data.length; i++) {
          data[i].idx = i + 1
        }
      }
      yield put({
        type: 'displayList2',
        payload: data
      })
    },

    * upsertReferMonth ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.upsertReferMonth, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      if (data !== []) {
        for (let i = 0; i < data.length; i++) {
          data[i].idx = i + 1
        }
      }
      yield put({
        type: 'displayList2',
        payload: data
      })
    },

    * deleteReferMonth ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.deleteReferMonth, payload)
      console.log(status, msg)
      yield put({
        type: 'getReferMonthList',
        payload: {}
      })
    },

    * listReferPeriod ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listReferPeriod, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      yield put({
        type: 'displayReferPeriodList',
        payload: data
      })
    },

    * addReferPeriod ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.addReferPeriod, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.warning(msg)
      }
      yield put({
        type: 'listReferPeriod',
        payload: {}
      })
    },

    * updateReferPeriod ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.updateReferPeriod, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.warning(msg)
      }
      yield put({
        type: 'listReferPeriod',
        payload: {}
      })
    },

    * deleteReferPeriod ({ payload }, { call, put }) {
      let { data: { status, msg } } = yield call(api.deleteReferPeriod, payload)
      console.log(status, msg)
      if (status !== 0) {
        message.warning(msg)
      }
      yield put({
        type: 'listReferPeriod',
        payload: {}
      })
    },

    * listRewardSummary ({ payload }, { call, put }) {
      let { data: { data, status, msg } } = yield call(api.listRewardSummary, payload)
      console.log(status, msg)
      data = Array.isArray(data) ? data : []
      yield put({
        type: 'displayRewardSummary',
        payload: data
      })
    }
  }
}
