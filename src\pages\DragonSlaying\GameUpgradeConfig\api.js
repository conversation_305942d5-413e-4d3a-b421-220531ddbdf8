import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/dragon_slaying/get_hatking_upgrade_config_info?${stringify(params)}`) // 修改 url 即可
}

export function update (params) {
  return request(`/dragon_slaying/update_hatking_upgrade_config?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function add (params) {
  return request(`/dragon_slaying/add_new_hatking_upgrade_config?`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

export function remove (params) {
  return request(`/dragon_slaying/delete_hatking_upgrade_config?${stringify(params)}`)
}

export function confirm (params) {
  return request(`/dragon_slaying/confirm_hatking_upgrade_config?${stringify(params)}`)
}

export function down (params) {
  return request(`/dragon_slaying/down_hatking_upgrade_config?${stringify(params)}`)
}
