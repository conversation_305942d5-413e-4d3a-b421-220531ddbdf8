import React, { Component } from 'react'
import { Table, Divider, But<PERSON>, Card, Form, DatePicker } from 'antd'
import { connect } from 'dva'
import exportExcel from '@/utils/exportExcel'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker
const gameMap = { 0: 'ALL', 5: '诺亚方舟' }
const chanMap = { all: 'ALL', pc: 'PC端', dreamer: 'YO交友', yomi: 'YO语音' }

let paidRangeMap = {}
let paidRangeFilter = []
for (let i = 0; i < 300; i++) {
  let label = '[' + (i * 1000).toString() + ',' + ((i + 1) * 1000).toString() + ')'
  paidRangeFilter.push({ text: label, value: i })
  paidRangeMap[i] = label
}

@connect(({ arkJy }) => ({ // model 的 namespace
  model: arkJy // model 的 namespace
}))
class ArkDailyBetRangeStatsComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      list: [],
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
  }

  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '玩法类型', dataIndex: 'arenaId', align: 'center', render: text => { return gameMap[text] }, filters: [{ text: 'ALL', value: 0 }, { text: '诺亚方舟', value: 5 }], defaultFilteredValue: ['0'], onFilter: (value, record) => record.arenaId === value },
    { title: '渠道类型', dataIndex: 'platform', align: 'center', render: text => { return chanMap[text] }, filters: [{ text: 'ALL', value: 'all' }, { text: 'PC端', value: 'pc' }, { text: 'YO交友', value: 'dreamer' }, { text: 'YO语音', value: 'yomi' }], defaultFilteredValue: ['all'], onFilter: (value, record) => record.platform.includes(value) },
    { title: '参与流水区间', dataIndex: 'rangeId', align: 'center', render: text => { return paidRangeMap[text] }, filters: paidRangeFilter, onFilter: (value, record) => record.rangeId === value },
    { title: '参与流水(元)', dataIndex: 'betAmethyst', align: 'center' },
    { title: '成功流水(元)', dataIndex: 'betRewardAmethyst', align: 'center' },
    { title: '发放占比', dataIndex: 'betRewardRatio', align: 'center' },
    { title: '参与用户数', dataIndex: 'betUser', align: 'center' },
    { title: '成功用户数', dataIndex: 'betRewardUser', align: 'center' },
    { title: '累计成功用户数', dataIndex: 'profitUser', align: 'center' },
    { title: '累计失败用户数', dataIndex: 'lossUser', align: 'center' }
  ]

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    console.log(data)
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getPaidRangeList`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  onExport = () => {
    let headers = []
    let columns = this.columns
    columns.forEach(function (item) {
      headers.push({ key: item.dataIndex, header: item.title })
    })

    const { model: { paidRangeList } } = this.props
    var exportData = paidRangeList.map(item => {
      let v = $.extend(true, {}, item)
      v.arenaId = gameMap[v.arenaId]
      v.platform = chanMap[v.platform]
      v.rangeId = paidRangeMap[v.rangeId]
      return v
    })

    exportExcel(headers, exportData)
  }
  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { paidRangeList } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form>
          <span style={{ marginLeft: 10 }}>时间范围:</span>
          <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onExport}>导出</Button>
          <Divider />
          <Table dataSource={paidRangeList} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default ArkDailyBetRangeStatsComponent
