/**
 * 页面名称: 帽子超主流水激励任务
 * 下线日期: 2023-10
 * 原因: 帽子超主相关概念已废弃，相关页面铲掉
 * 注意事项: 相关的页面代码、服务端的接口可铲掉
 * 相关需求地址：【去互动超级主持收尾】 https://docs.google.com/document/d/1jsY4ALCUMTyjE0xTM4vFW8R82nncyRVXjk73A-fUJSo/edit
 */

import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs } from 'antd'
import { connect } from 'dva'
import TaskConfig from './components/task_config.js'
import AwardDetail from './components/award_detail'
import TaskProgressReport from './components/task_progress_report'
// import RewardSummaryReport from './components/reward_summary_report'
import RewardConfirm from './components/reward_confirm'
const TabPane = Tabs.TabPane

@connect(({ hatSuperCompereTask }) => ({ // model 的 namespace
  model: hatSuperCompereTask // model 的 namespace
}))
class Index extends Component { // 默认页面组件，不需要修改
  /** *******************************页面布局*************************************************************/
  render () {
    const { route, model: { taskConfigList, taskProgressList, awardDetailList, progressSummaryInfo, rewardConfirmInfo } } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs type='card' defaultActiveKey='2' >
          <TabPane tab='任务配置' key='1'>
            <TaskConfig dataInfo={taskConfigList} navGroup={1} />
          </TabPane>
          <TabPane tab='发奖确认' key='2'>
            <RewardConfirm dataInfo={rewardConfirmInfo.list} navGroup={1} />
          </TabPane>
          <TabPane tab='任务完成进度报表' key='3'>
            <TaskProgressReport dataInfo={taskProgressList} progressSummaryInfo={progressSummaryInfo} navGroup={1} />
          </TabPane>
          <TabPane tab='发奖明细' key='4'>
            <AwardDetail dataInfo={awardDetailList} navGroup={1} />
          </TabPane>
          {/* <TabPane disabled tab='发奖金额汇总' key='5'> */}
          {/*  <RewardSummaryReport dataInfo={rewardDetailList} navGroup={1} /> */}
          {/* </TabPane> */}
        </Tabs>
      </PageHeaderWrapper>
    )
  }
}

export default Index // 保证唯一
