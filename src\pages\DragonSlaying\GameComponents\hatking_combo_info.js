import { But<PERSON>, Card, DatePicker, Divider, Form, Table } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'

var moment = require('moment')
const dateFormat = 'YYYY-MM-DD'
const { RangePicker } = DatePicker

@connect(({ dragonSlaying }) => ({ // model 的 namespace
  model1: dragonSlaying // model 的 namespace
}))
class DSComboInfoComponent extends Component {
  constructor (props) {
    super(props)

    this.state = {
      value: {},
      visible: false,
      dateRange: [moment().subtract(7, 'days'), moment().add(1, 'days')]
    }
  }

  componentDidMount () {
    this.loadData()
    this.setState()
  }

  // 日期 连中用户昵称  连中用户uid 连中次数  是否领奖（1-中奖，2-领奖） 押注金额  额外连中金额  中/领奖时间
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '连中用户昵称', dataIndex: 'nick', align: 'center' },
    { title: '连中用户uid', dataIndex: 'uid', align: 'center' },
    { title: '连中次数', dataIndex: 'combo', align: 'center' },
    { title: '是否领奖（1-中奖，2-领奖）', dataIndex: 'type', align: 'center' },
    { title: '押注金额', dataIndex: 'amethystCount', align: 'center' },
    { title: '额外连中金额', dataIndex: 'extraAmethyst', align: 'center' },
    { title: '中/领奖时间', dataIndex: 'timestamp', align: 'center' }
  ]

  loadData = () => {
    const { dispatch } = this.props
    const { dateRange } = this.state
    const data = { start: moment(dateRange[0]).format(dateFormat), end: moment(dateRange[1]).format(dateFormat) }
    const { modelName } = this.props
    dispatch({
      type: `${modelName}/getComboInfoList`,
      payload: data
    })
  }

  onClick = () => {
    this.loadData()
  }

  onChange = (date, format) => {
    console.log('date', date)
    this.setState({ dateRange: date })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { comboList } } = this.props
    const { dateRange } = this.state
    return (
      <Card>
        <Form>
          <span style={{ marginLeft: 10 }}>时间范围:</span>
          <RangePicker style={{ marginLeft: 10 }} defaultValue={dateRange} format={dateFormat} onChange={this.onChange} />
          <Button style={{ marginLeft: 10 }} type='primary' onClick={this.onClick}>查询</Button>
          <Divider />
          <Table dataSource={comboList} columns={this.columns} rowKey={(record, index) => index} pagination={{ pageSize: 500 }} size='small' />
        </Form>
      </Card>
    )
  }
}

export default DSComboInfoComponent
