import request from '@/utils/request'
import { stringify } from 'qs'
import { parseOptionsFromMultiLineString, timeFormater } from '@/utils/common'

export const configTypeOptions = [
  { label: '内置', value: 'INNER' },
  { label: '自定义', value: 'CUSTOM' }
]

export const needConfirmFlowOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 }
]

export const ruleDataTypeOptions = [
  { label: 'PARAMETER', value: 'PARAMETER' },
  { label: 'EXPRESSION', value: 'EXPRESSION' }
]

export const functionTypeOptions = [
  { label: '原生函数', value: 'INNER' },
  { label: 'JavaScript', value: 'JS' }
]

export const functionTypeWithoutInnerOptions = [
  { label: 'JavaScript', value: 'JS' }
]

export const elementTypeOptions = [
  { label: 'object', value: 'object' },
  { label: 'string', value: 'string' },
  { label: 'number', value: 'number' },
  { label: 'boolean', value: 'bool' },
  { label: 'timestamp', value: 'timestamp' }
]

export const defaultBoolElementTypeOptions = [
  { label: 'TRUE', value: 'true' },
  { label: 'FALSE', value: 'false' }
]

export const elementTypeWithoutObjectOptions = [
  { label: 'string', value: 'string' },
  { label: 'number', value: 'number' },
  { label: 'boolean', value: 'bool' },
  { label: 'timestamp', value: 'timestamp' }
]

export const visibleOptions = [
  { label: '灰度数据', value: 'GRAY' },
  { label: '正式数据', value: 'NORMAL' }
]

export const numberYesNoOptions = [
  { label: '是', value: 1 },
  { label: '否', value: 0 }
]

export const pushConfirmStatusOptions = [
  { label: '未推送', value: 0 },
  { label: '已推送', value: 1 },
  { label: '推送失败', value: 2 },
  { label: '审核通过', value: 3 },
  { label: '审核拒绝', value: 4 }
]

export const compareTypeOptions = [
  // { label: 'IN', value: 'IN' },
  { label: '等于', value: '==' },
  { label: '小于', value: '<' },
  { label: '小于等于', value: '<=' },
  { label: '大于', value: '>' },
  { label: '大于等于', value: '>=' }
]

export const logicTypeOptions = [
  { label: '并且', value: '&&' },
  { label: '或者', value: '||' }
]

export const elementTypeValidator = [{
  validator: (_, value) => {
    return value ? Promise.resolve() : Promise.reject(new Error('请选择数据类型'))
  }
}]

export const elementTypeOptionsValidator = [{
  validator: (_, value) => {
    return parseOptionsFromMultiLineString(value) !== false ? Promise.resolve() : Promise.reject(new Error('可选项格式错误或者值重复，请使用格式 [选项值:选项说明]'))
  }
}]

export const varNameValidator = [{
  validator: (_, value) => {
    if (!value) {
      return Promise.reject(new Error('请输入名称'))
    }
    if (!/^[a-zA-Z_][a-zA-Z_0-9]*$/.test(value.trim())) {
      return Promise.reject(new Error('名称不合法，必须非数字开头，支持数字、字母、下划线组成'))
    }
    return Promise.resolve()
  }
}]

export const validatorTagValidator = [{
  validator: (_, value) => {
    if (!value || !(value.trim())) {
      return Promise.resolve()
    }
    let arr = value.trim().split(/"\s+/)
    if (arr.length < 1) {
      return Promise.reject(new Error('格式错误'))
    }
    for (let i = 0; i < arr.length; ++i) {
      let v = arr[i].trim()
      let subArr = v.split(/:"/)
      if (subArr.length !== 2) {
        return Promise.reject(new Error('第[' + i + ']个验证规则[' + arr[i].trim() + ']格式错误'))
      }
      let tagName = subArr[0].trim()
      switch (tagName) {
        case 'vLen': // 开闭区间
          if (!/[(\\[]\s*[0-9\\.]*\s*,\s*[0-9\\.]*[)\]]/.test(value)) {
            return Promise.reject(new Error('第[' + (i + 1) + ']个验证规则[' + arr[i].trim() + ']开闭区间格式错误'))
          }
          break
        case 'vRange':
          if (!/[(\\[]\s*[0-9\\.]*\s*,\s*[0-9\\.]*[)\]]/.test(value)) {
            return Promise.reject(new Error('第[' + (i + 1) + ']个验证规则[' + arr[i].trim() + ']开闭区间格式错误'))
          }
          break
        case 'vIn':
          break
        case 'vRegex':
          break
        default:
          return Promise.reject(new Error('第[' + (i + 1) + ']个验证规则[' + arr[i].trim() + ']无法解析'))
      }
    }
    return Promise.resolve()
  }
}]

export const formatTimestamp = (timestamp, fmt) => {
  if (!timestamp) {
    return '-'
  }
  return timeFormater(parseInt(timestamp), fmt || 1)
}

export const doPost = function (apiPath, params) {
  return request(apiPath, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

// 分页查询业务列表
export function pageListBusiness (params) {
  params.pageNo = params.pageNo || -1
  params.pageSize = params.pageSize || -1
  return request(`/group_svr/business/list?${stringify(params)}`)
}

// 添加业务信息
export function addBusiness (params) {
  return doPost(`/group_svr/business/add`, params)
}

// 更新业务信息
export function updateBusiness (params) {
  return doPost(`/group_svr/business/update`, params)
}

// 刪除业务
export function removeBusiness (params) {
  return doPost(`/group_svr/business/remove`, params)
}

// 分页查询规则函数
export function pageListRuleFunction (params) {
  params.pageNo = params.pageNo || 1
  params.pageSize = params.pageSize || 20
  return request(`/group_svr/rule_ref/list_rule_function?${stringify(params)}`)
}

// 更新规则函数信息
export function updateRuleFunction (params) {
  return doPost(`/group_svr/rule_ref/update_rule_function`, params)
}

// 添加规则函数信息
export function addRuleFunction (params) {
  return doPost(`/group_svr/rule_ref/add_rule_function`, params)
}

// 刪除规则函数信息
export function removeRuleFunction (params) {
  return doPost(`/group_svr/rule_ref/remove_rule_function`, params)
}

// 分页查询规则数据
export function pageListRuleData (params) {
  params.pageNo = params.pageNo || 1
  params.pageSize = params.pageSize || 20
  return request(`/group_svr/rule_ref/list_rule_data?${stringify(params)}`)
}

// 添加规则数据信息
export function addRuleData (params) {
  return doPost(`/group_svr/rule_ref/add_rule_data`, params)
}

// 更新规则数据信息
export function updateRuleData (params) {
  return doPost(`/group_svr/rule_ref/update_rule_data`, params)
}

// 刪除规则数据信息
export function removeRuleData (params) {
  return doPost(`/group_svr/rule_ref/remove_rule_data`, params)
}

// 分页查询活动数据
export function pageListActivity (params) {
  params.pageNo = params.pageNo || 1
  params.pageSize = params.pageSize || 20
  return request(`/group_svr/activity/list?${stringify(params)}`)
}

// 添加活动数据信息
export function addActivity (params) {
  return doPost(`/group_svr/activity/add`, params)
}

// 删除活动数据信息
export function removeActivity (params) {
  return doPost(`/group_svr/activity/remove`, params)
}

// 更新活动数据信息
export function updateActivityBasicInfo (params) {
  return doPost(`/group_svr/activity/update_basic_info`, params)
}

// 更新活动数据信息
export function upsertActivityRoleInfo (params) {
  return doPost(`/group_svr/activity/upsert_role_info`, params)
}

// 更新活动数据信息
export function removeActivityRoleInfo (params) {
  return doPost(`/group_svr/activity/remove_role`, params)
}

// 分页查询分组结果
export function pageListGroupResult (params) {
  params.pageNo = params.pageNo || 1
  params.pageSize = params.pageSize || 20
  return request(`/group_svr/activity/list_group_result?${stringify(params)}`)
}

// 添加活动分组
export function upsertGroupResult (params) {
  return doPost(`/group_svr/activity/upsert_group_result`, params)
}

// 删除活动分组
export function removeGroupResult (params) {
  return doPost(`/group_svr/activity/remove_group_result`, params)
}

// 更新活动分组
export function updateGroupResult (params) {
  return doPost(`/group_svr/activity/update_group_result`, params)
}

// 更新活动分组
export function pushMessageFromServer (params) {
  return request(`/group_ctl/push_message`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}

// 通知活动分组确认消息
export function pushConfirmMessage (payload) {
  return doPost(`/group_svr/activity/push_confirm_message`, payload)
}

export const CacheRejectMode = 1 // 无缓存
export const CacheFirstMode = 2 // 缓存优先
export const CacheOnlyMode = 3 // 仅缓存

export const DefaultPreActCacheMode = CacheRejectMode // 活动前，默认使用 无缓存模式
export const DefaultInActCacheMode = CacheFirstMode // 活动中，默认使用 缓存优先模式
export const DefaultAfterActCacheMode = CacheOnlyMode // 活动前，默认使用 仅读取缓存模式

export const cacheModeOptions = [
  { label: '不缓存', value: CacheRejectMode },
  { label: '缓存优先', value: CacheFirstMode },
  { label: '仅缓存', value: CacheOnlyMode }
]

export function resolveCacheMode (mode, def) {
  if (mode === CacheRejectMode || mode === CacheFirstMode || mode === CacheOnlyMode) {
    return mode
  }
  return def
}
