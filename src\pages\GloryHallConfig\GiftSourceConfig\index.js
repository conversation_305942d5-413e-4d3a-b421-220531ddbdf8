import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Typography, Space, Popconfirm, Form, Modal, message, Table, Row, Col, Input, InputNumber, Button } from 'antd'
const { Text, Link } = Typography

const namespace = 'giftSourceConfig'
const defaultFormVal = { id: null, name: '', source: '' }

@connect(({ giftSourceConfig }) => ({
  model: giftSourceConfig
}))

class GiftSourceConfig extends Component {
  state = {
    opType: 'ADD', // ADD | DEL | UDPATE
    keyword: '',
    modalVisible: false
  }

  componentDidMount = () => {
    this.queryList()
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 查询列表
  queryList = () => {
    this.callModel('getSouceConfigList')
  }

  // update示例
  updateConfig = (v, opType, list) => {
    v.opType = opType
    let reason = this.paramsCheck(v, opType, list)
    if (reason) {
      message.warn('参数有误, 请检查: ' + reason)
      return
    }
    this.callModel('updateSourceConfig', {
      params: v,
      isJsonMode: true,
      isSlientMode: true,
      isDetailMode: true,
      useJsonHeader: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.error('更新失败:' + msg)
          return
        }
        message.success('更新成功')
        this.setState({ modalVisible: false })
        this.queryList()
      }
    })
  }

  // 过滤数据列表
  filterList = (before, keyword) => {
    if (!keyword) return before
    return before.filter(item => {
      return `${item.id}`.includes(keyword) || item.name.includes(keyword) || item.source.includes(keyword)
    })
  }

  // 参数检查
  paramsCheck = (v, opType, list) => {
    if (!v.id) return 'ID未填写'
    if (!v.source) return '礼物来源未填写'
    if (v.source?.length > 15) {
      return '礼物来源需要小于15字'
    }
    if (opType === 'ADD' && list.some(item => { return item.id === v.id })) {
      return `ID ${v.id} 已存在`
    }
    return ''
  }

  render () {
    const { route } = this.props
    const { list } = this.props.model || {}
    const { modalVisible, keyword, opType } = this.state

    const columns = [
      { dataIndex: 'id', title: '礼物ID' },
      { dataIndex: 'name', title: '礼物名称' },
      { dataIndex: 'source', title: '礼物来源' },
      { dataIndex: 'id',
        width: '12em',
        fixed: 'right',
        title: '操作',
        render: (v, r) => {
          return <Space>
            <Link onClick={() => { this.formRef.setFieldsValue(r); this.setState({ opType: 'UPDATE', modalVisible: true }) }} >更新</Link>
            <Popconfirm title='确认删除这个配置么？' onConfirm={() => this.updateConfig(r, 'DEL')}><Text type='danger' >删除</Text></Popconfirm>
          </Space>
        } }
    ]

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Row>
            <Col span={24} style={{ marginBottom: '1em' }}>
              <Space>
                <Input placeholder='请输入礼物名称或礼物ID' onChange={e => this.setState({ keyword: e.target.value })} style={{ width: '15em' }} />
                <Button onClick={() => this.queryList()} >刷新</Button>
                <Button type='primary' onClick={() => { this.formRef.setFieldsValue(defaultFormVal); this.setState({ opType: 'ADD', modalVisible: true }) }} >新增</Button>
              </Space>
            </Col>
            <Col span={24}>
              <Table columns={columns} dataSource={this.filterList(list, keyword)} pageSize={50} />
            </Col>
            <Modal title={opType === 'ADD' ? '新增配置' : '更新配置'} forceRender visible={modalVisible} onCancel={() => this.setState({ modalVisible: false })} onOk={() => this.formRef.submit()}>
              <Form ref={r => { this.formRef = r }} onFinish={v => this.updateConfig(v, opType, list)} initialValues={defaultFormVal} >
                <Form.Item label='礼物ID' name='id' >
                  <InputNumber disabled={opType === 'UPDATE'} style={{ width: '15em' }} />
                </Form.Item>
                {
                  opType === 'ADD'
                    ? ''
                    : <Form.Item label='礼物名称' name='name' >
                      <InputNumber disabled style={{ width: '15em' }} />
                    </Form.Item>
                }
                <Form.Item label='礼物来源' name='source' >
                  <Input.TextArea autoSize={{ minRows: 5 }} showCount />
                </Form.Item>
              </Form>
            </Modal>
          </Row>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default GiftSourceConfig
