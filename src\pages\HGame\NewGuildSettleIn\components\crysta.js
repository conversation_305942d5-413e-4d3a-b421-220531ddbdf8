import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Table, InputNumber, Select, DatePicker, Button, message, Modal, Row, Col, Divider, Input } from 'antd'
import PopImage from '@/components/PopImage'

const Option = Select.Option

const namespace = 'newGuildSettleIn'

const yesNoBoolMap = [
  { label: '是', value: true },
  { label: '否', value: false }
]
const yesNoMapBool2 = { true: '是', false: '否' }

const yesNoMap = [
  { label: '全部', value: 0 },
  { label: '是', value: 1 },
  { label: '否', value: 2 }
]
const yesNoMap2 = { 0: '全部', 1: '是', 2: '否' }
const yesNoMap3 = { 0: '-', 1: '是', 2: '否' }

const reviewMap = [
  { label: '全部', value: 0 },
  { label: '待审核', value: 1 },
  { label: '审核通过', value: 2 },
  { label: '审核不通过', value: 3 }
]

const approvalMap = [
  { label: '全部', value: 0 },
  { label: '待审核', value: 1 },
  { label: '一审中', value: 2 },
  { label: '一审通过，二审中', value: 3 },
  { label: '审核通过', value: 4 },
  { label: '审核不通过', value: 5 }
]

const companyTypeMap = { 0: '-', 1: '有限责任公司', 2: '股份有限公司' }

const faildDescList = [
  { label: '运营直播平台信息资料不足', value: '运营直播平台信息资料不足' },
  { label: '实名、资质及联系方式填写有误', value: '实名、资质及联系方式填写有误' },
  { label: '公司资质不符合要求', value: '公司资质不符合要求' }
]

var moment = require('moment')
var dateFormat = 'YYYY-MM-DD'

@connect(({ newGuildSettleIn }) => ({
  model: newGuildSettleIn
}))

class NewGuildSettleInCrysta extends Component {
  columns = [
    { title: '序号', dataIndex: 'idx', align: 'center', fixed: 'left' },
    { title: '频道ID', dataIndex: 'sid', align: 'center', fixed: 'left' },
    { title: '短位ID', dataIndex: 'asid', align: 'center', fixed: 'left' },
    { title: '是否达到申请条件', dataIndex: 'isReachCondition', align: 'center', render: v => yesNoMap.find(i => i.value === v).label },
    { title: '是否绿色通道白名单', dataIndex: 'isGreenChannel', align: 'center', render: v => yesNoMap.find(i => i.value === v).label },
    { title: '是否运营交友或其他直播平台', dataIndex: 'isJy', align: 'center', render: (text, record) => (yesNoBoolMap.find(i => i.value === record.guildPlatformInfo.isJy).label) },
    { title: '创建频道时间', dataIndex: 'createSidTime', align: 'center', render: (text, record) => (record.createSidTime === 0 ? '' : moment.unix(record.createSidTime).format(dateFormat)) },
    { title: '申请时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (record.timestamp === 0 ? '' : moment.unix(record.timestamp).format(dateFormat)) },
    { title: '运营审核状态', dataIndex: 'reviewStatus', align: 'center', render: v => reviewMap.find(i => i.value === v).label },
    { title: '业务审核状态', dataIndex: 'approvalStatus', align: 'center', render: v => approvalMap.find(i => i.value === v).label },
    { title: '审核完成时间', dataIndex: 'approvalPassTime', align: 'center', render: (text, record) => (record.approvalPassTime === 0 ? '' : moment.unix(record.approvalPassTime).format(dateFormat)) },
    { title: '运营备注', dataIndex: 'reviewReason', align: 'center' },
    { title: '历史申请记录', key: 'historyRecord', align: 'center', render: (text, record) => (<span><a onClick={this.showHistoryModal(record)}>查看</a></span>) },
    { title: '查看详情及操作', key: 'operation', align: 'center', render: (text, record) => (<span><a onClick={this.showDetailModal(record)}>查看并审批</a></span>) }
  ]

  columnshistory = [
    { title: '日期', dataIndex: 'rejectTime', align: 'center', render: (text, record) => (record.rejectTime === 0 ? '' : moment.unix(record.rejectTime).format('YYYY-MM-DD hh:mm:ss')) },
    { title: '申请时间', dataIndex: 'timestamp', align: 'center', render: (text, record) => (record.timestamp === 0 ? '' : moment.unix(record.timestamp).format(dateFormat)) },
    { title: '运营审核状态', dataIndex: 'reviewStatus', align: 'center', render: v => reviewMap.find(i => i.value === v).label },
    { title: '业务审核状态', dataIndex: 'approvalStatus', align: 'center', render: v => approvalMap.find(i => i.value === v).label },
    { title: '审核运营', dataIndex: 'reviewUser', align: 'center' },
    { title: '运营备注', dataIndex: 'reviewReason', align: 'center' },
    { title: '业务备注', dataIndex: 'approvalReason', align: 'center' }
  ]

  state = {
    approvalRejectReasonQuick: '',
    approvalRejectReason: ''
  }

  defaultPageValue = {
    defaultPageSize: 20,
    pageSizeOptions: ['20', '50', '100', '200', '500', '1000'],
    showSizeChanger: true,
    onChange: () => { this.setState({ selectedRowKeys: null }) },
    showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`
  }

  componentDidMount () {
    this.loadData()
  }

  loadData = () => {
    const { searchSID, searchIsReachCondition, searchIsGreenChannel, searchIsJy, searchReviewStatus, searchApprovalStatus, searchApplyStartTime, searchApplyEndTime } = this.state
    const { dispatch } = this.props

    let searchApplyStartTimeTmp = 0
    let searchApplyEndTimeTmp = 0
    if (searchApplyStartTime) {
      searchApplyStartTimeTmp = moment(searchApplyStartTime).unix()
    }
    if (searchApplyEndTime) {
      searchApplyEndTimeTmp = moment(searchApplyEndTime).unix()
    }

    if (searchApplyStartTimeTmp !== 0 && searchApplyEndTimeTmp !== 0 && searchApplyStartTimeTmp >= searchApplyEndTimeTmp) {
      message.warn('开始时间不能大于结束时间')
      return
    }

    let data = { sid: searchSID, isReachCondition: searchIsReachCondition, isGreenChannel: searchIsGreenChannel, isJy: searchIsJy, reviewStatus: searchReviewStatus, approvalStatus: searchApprovalStatus, applyStartTime: searchApplyStartTimeTmp, applyEndTime: searchApplyEndTimeTmp }
    console.log(data)
    dispatch({
      type: `${namespace}/listCrystal`,
      payload: data
    })
  }

  showHistoryModal = (record) => () => {
    this.setState({ visibleHistory: true, historySID: record.sid })
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/listCrystalHistory`,
      payload: { sid: record.sid }
    })
  }

  showDetailModal = (record) => () => {
    this.setState({ curID: record.id, record: record, visibleDetail: true })
  }

  searchHandle = () => () => {
    this.loadData()
  }

  handleCancelHistory = e => {
    this.setState({ visibleHistory: false })
  }

  handleCancelDetail = e => {
    this.setState({ curID: '', visibleDetail: false })
  }

  cancelDetailHandle = () => () => {
    this.setState({ curID: '', visibleDetail: false })
  }
  
  approvalPassHandle = () => () => {
    let detailHTML = document.getElementById('inner')?.innerHTML
    const { curID, approvalPassReason } = this.state
    let data = { id: curID, reviewStatus: 2, reviewReason: approvalPassReason, html: detailHTML }
    console.log(data)
    this.props.dispatch({
      type: `${namespace}/approvalCrysta`,
      payload: data
    })
    this.hiddenApprovalPass()
    this.setState({ curID: '', visibleDetail: false })
    detailHTML = null
  }

  showApprovalPass = () => () => {
    this.setState({ approvalPassVisible: true })
  }

  hiddenApprovalPass = () => {
    this.setState({ approvalPassVisible: false, approvalPassReason: '' })
  }

  hiddenApprovalPass2 = () => () => {
    this.setState({ approvalPassVisible: false, approvalPassReason: '' })
  }

  showApprovalReject = () => () => {
    this.setState({ approvalRejectVisible: true })
  }

  hiddenApprovalReject = () => {
    this.setState({ approvalRejectVisible: false, approvalRejectReason: '', approvalRejectReasonQuick: '' })
  }

  hiddenApprovalReject2 = () => () => {
    this.setState({ approvalRejectVisible: false, approvalRejectReason: '', approvalRejectReasonQuick: '' })
  }

  approvalRejectHandle = () => () => {
    const { curID, approvalRejectReason, approvalRejectReasonQuick } = this.state
    console.log(approvalRejectReason, approvalRejectReasonQuick)
    if (approvalRejectReason && approvalRejectReasonQuick && approvalRejectReason !== '' && approvalRejectReasonQuick !== '') {
      message.warning('快速选择和手动输入只能选其一')
      return
    }
    let reviewReason = ''
    if (approvalRejectReason && approvalRejectReason !== '') {
      reviewReason = approvalRejectReason
    } else if (approvalRejectReasonQuick && approvalRejectReasonQuick !== '') {
      reviewReason = approvalRejectReasonQuick
    }

    let data = { id: curID, reviewStatus: 3, reviewReason: reviewReason }
    console.log(data)

    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/approvalCrysta`,
      payload: data
    })
    this.hiddenApprovalReject()
    this.setState({ visibleDetail: false })
  }

  displayAmount = () => {
    const { record } = this.state
    let str = ''
    if (record !== null && record !== undefined && record.guildPlatformInfo !== undefined && record.guildPlatformInfo !== null) {
      for (let i = 0; i < record.guildPlatformInfo.amount.length; i++) {
        str = str.concat(record.guildPlatformInfo.amount[i], ' : ')
      }
    }
    if (str.length > 0) {
      str = str.substr(0, str.length - 3)
    }
    return str
  }

  displayScreenshot = () => {
    const { record } = this.state
    if (record !== null && record !== undefined && record.guildPlatformInfo !== undefined && record.guildPlatformInfo !== null) {
      let len = record.guildPlatformInfo.screenshot.length
      if (len === 0) {
        return <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><font style={{ marginLeft: 10, fontSize: '16px' }}>公会流水截图凭证：</font></Row>
      }
      if (len === 1) {
        return <span><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><font style={{ marginLeft: 10, fontSize: '16px' }}>公会流水截图凭证：</font></Row><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[0]} /></Col></Row></span>
      }
      if (len === 2) {
        return <span><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><font style={{ marginLeft: 10, fontSize: '16px' }}>公会流水截图凭证：</font></Row><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[0]} /></Col><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[1]} /></Col></Row></span>
      }
      if (len === 3) {
        return <span><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><font style={{ marginLeft: 10, fontSize: '16px' }}>公会流水截图凭证：</font></Row><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[0]} /></Col><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[1]} /></Col><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[2]} /></Col></Row></span>
      }
      if (len === 4) {
        return <span><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><font style={{ marginLeft: 10, fontSize: '16px' }}>公会流水截图凭证：</font></Row><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[0]} /></Col><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[1]} /></Col><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[2]} /></Col><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[3]} /></Col></Row></span>
      }
      if (len === 5) {
        return <span><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><font style={{ marginLeft: 10, fontSize: '16px' }}>公会流水截图凭证：</font></Row><Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[0]} /></Col><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[1]} /></Col><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[2]} /></Col><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[3]} /></Col><Col span={4}><PopImage value={record.guildPlatformInfo.screenshot[4]} /></Col></Row></span>
      }
    }
  }

  faildDescHandler = (value) => {
    this.setState({ approvalRejectReasonQuick: value })
  }

  render () {
    const { visibleHistory, historySID, approvalRejectVisible, approvalPassVisible, record, visibleDetail } = this.state
    const { model: { listCrystal, listCrystalHistory, tableLoadingCrystalHistory } } = this.props

    return (
      <Card>
        <span>频道ID</span>
        <InputNumber min={0} placeholder='请输入' onChange={e => this.setState({ searchSID: e })} style={{ width: 100, marginLeft: 3 }} />
        <span style={{ marginLeft: 10 }}>是否达到申请条件</span>
        <Select style={{ marginLeft: 5, width: 80 }} placeholder='请选择' onChange={(v) => this.setState({ searchIsReachCondition: v })}>{ yesNoMap.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
        <span style={{ marginLeft: 10 }}>是否绿色通道白名单</span>
        <Select style={{ marginLeft: 5, width: 80 }} placeholder='请选择' onChange={(v) => this.setState({ searchIsGreenChannel: v })}>{ yesNoMap.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
        <span style={{ marginLeft: 10 }}>是否运营交友或其他直播平台</span>
        <Select style={{ marginLeft: 5, width: 80 }} placeholder='请选择' onChange={(v) => this.setState({ searchIsJy: v })}>{ yesNoMap.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
        <span style={{ marginLeft: 10 }}>运营审核状态</span>
        <Select style={{ marginLeft: 5, width: 100 }} placeholder='请选择' onChange={(v) => this.setState({ searchReviewStatus: v })}>{ reviewMap.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
        <span style={{ marginLeft: 10 }}>业务审核状态</span>
        <Select style={{ marginLeft: 5, width: 140 }} placeholder='请选择' onChange={(v) => this.setState({ searchApprovalStatus: v })}>{ approvalMap.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
        <div style={{ marginTop: 10 }} />
        <span>申请时间</span>
        <DatePicker
          format='YYYY-MM-DD'
          placeholder='开始时间'
          onChange={(v) => this.setState({ searchApplyStartTime: v })}
          style={{ marginLeft: 10 }}
        />
        <span style={{ marginLeft: 5 }}>~</span>
        <DatePicker
          format='YYYY-MM-DD'
          placeholder='结束时间'
          onChange={(v) => this.setState({ searchApplyEndTime: v })}
          style={{ marginLeft: 5 }}
        />
        <Button style={{ marginLeft: 20 }} type='primary' onClick={this.searchHandle()}>查询</Button>
        <Table style={{ marginTop: 10 }} size='small' rowKey='idx' pagination={this.defaultPageValue} columns={this.columns} dataSource={listCrystal} scroll={{ x: 'max-content' }} />

        <Modal footer={null} forceRender width={1200} visible={visibleHistory} title='历史记录' onCancel={this.handleCancelHistory}>
          <Card>
            <span><font style={{ marginLeft: 5, fontSize: '20px' }}>频道ID:  </font><font style={{ fontSize: '20px' }}>{historySID}</font></span>
            <Table loading={tableLoadingCrystalHistory} style={{ marginTop: 10 }} size='small' rowKey='idx' pagination={this.defaultPageValue} columns={this.columnshistory} dataSource={listCrystalHistory} />
          </Card>
        </Modal>

        <Modal forceRender footer={null} width={800} visible={visibleDetail} title='申请水晶公会审批' onCancel={this.handleCancelDetail}>
          <div id='inner'>
            <div><font style={{ marginLeft: 240, fontSize: '30px', fontWeight: '500' }}>申请水晶公会审批</font></div>
            <div><font style={{ marginLeft: 40, marginTop: 10, fontSize: '24px', fontWeight: '500' }}>申请频道基本信息</font></div>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>频道ID：{record !== null && record !== undefined ? record.asid : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>申请时间：{record !== null && record !== undefined ? moment.unix(record.timestamp).format(dateFormat) : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>申请前7天礼物流水/元：{record !== null && record !== undefined ? record.amount : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>申请前7天日均有收入主持数/人：{record !== null && record !== undefined ? record.compereCount : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>是否达到申请条件：{record !== null && record !== undefined ? yesNoMap2[record.isReachCondition] : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>是否绿色通道白名单：{record !== null && record !== undefined ? yesNoMap2[record.isGreenChannel] : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公会名称：{record !== null && record !== undefined && record.guildBaseInfo !== null ? record.guildBaseInfo.name : ''}</font>
              </Col>
            </Row>
            <Divider />

            <div><font style={{ marginLeft: 40, fontSize: '24px', fontWeight: '500' }}>运营直播平台信息</font></div>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>是否运营交友或其他直播平台：{record !== null && record !== undefined && record.guildPlatformInfo !== null ? yesNoMapBool2[record.guildPlatformInfo.isJy] : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>直播平台名称：{record !== null && record !== undefined && record.guildPlatformInfo !== null ? record.guildPlatformInfo.platformName : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公会名称：{record !== null && record !== undefined && record.guildPlatformInfo !== null ? record.guildPlatformInfo.guildName : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>签约主播数/人：{record !== null && record !== undefined && record.guildPlatformInfo !== null ? record.guildPlatformInfo.compereCount : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>最近3个自然月流水/元：{this.displayAmount()}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>开播链接：{record !== null && record !== undefined && record.guildPlatformInfo !== null ? record.guildPlatformInfo.link : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公会ID：{record !== null && record !== undefined && record.guildPlatformInfo !== null && record.guildPlatformInfo.guildId !== 0 ? record.guildPlatformInfo.guildId : ''}</font>
              </Col>
            </Row>
            {this.displayScreenshot()}
            <Divider />

            <div><font style={{ marginLeft: 40, fontSize: '24px', fontWeight: '500' }}>实名、资质及联系信息</font></div>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>实名姓名：{record !== null && record !== undefined ? record.realName : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>实名身份证号：{record !== null && record !== undefined ? record.realIdentity : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>实名手机号：{record !== null && record !== undefined ? record.realPhone : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>法人真实姓名：{record !== null && record !== undefined && record.qualificationInfo !== undefined && record.qualificationInfo !== null ? record.qualificationInfo.realName : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>法人身份证号：{record !== null && record !== undefined && record.qualificationInfo !== undefined && record.qualificationInfo !== null ? record.qualificationInfo.identity : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>法人手机号：{record !== null && record !== undefined && record.qualificationInfo !== undefined && record.qualificationInfo !== null ? record.qualificationInfo.phone : ''}</font>
              </Col>
            </Row>

            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>姓名是否与实名一致：{record !== null && record !== undefined && record.realNameStatus !== undefined ? yesNoMap3[record.realNameStatus] : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>身份证号是否与实名一致：{record !== null && record !== undefined && record.realIdentityStatus !== undefined ? yesNoMap3[record.realIdentityStatus] : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>手机号是否与实名一致：{record !== null && record !== undefined && record.realPhoneStatus !== undefined ? yesNoMap3[record.realPhoneStatus] : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>联系人电话：{record !== null && record !== undefined && record.guildBaseInfo !== undefined && record.guildBaseInfo !== null ? record.guildBaseInfo.phone : ''}</font>
              </Col>
            </Row>

            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>联系人邮箱：{record !== null && record !== undefined && record.guildBaseInfo !== undefined && record.guildBaseInfo !== null ? record.guildBaseInfo.email : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>联系人地址：{record !== null && record !== undefined && record.guildBaseInfo !== undefined && record.guildBaseInfo !== null ? record.guildBaseInfo.address : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公司全称：{record !== null && record !== undefined && record.qualificationInfo !== undefined && record.qualificationInfo !== null ? record.qualificationInfo.companyName : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>企业类型：{record !== null && record !== undefined && record.qualificationInfo !== undefined && record.qualificationInfo !== null ? companyTypeMap[record.qualificationInfo.companyType] : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>注册资金/元：{record !== null && record !== undefined && record.qualificationInfo !== undefined && record.qualificationInfo !== null ? record.qualificationInfo.registerMoney : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公司地址：{record !== null && record !== undefined && record.qualificationInfo !== undefined && record.qualificationInfo !== null ? record.qualificationInfo.address : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公司基本户开户银行：{record !== null && record !== undefined && record.qualificationInfo !== undefined && record.qualificationInfo !== null ? record.qualificationInfo.bank : ''}</font>
              </Col>
              <Col span={12}>
                <font style={{ fontSize: '16px' }}>公司基本户账户：{record !== null && record !== undefined && record.qualificationInfo !== undefined && record.qualificationInfo !== null ? record.qualificationInfo.account : ''}</font>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <font style={{ marginLeft: 10, fontSize: '16px' }}>法人身份证正、反面：</font>
              <Col span={4}>
                <PopImage value={record !== null && record !== undefined && record.qualificationInfo !== undefined && record.qualificationInfo !== null && record.qualificationInfo.identityPicture[0] != null ? record.qualificationInfo.identityPicture[0] : ''} />
              </Col>
              <Col span={4}>
                <PopImage value={record !== null && record !== undefined && record.qualificationInfo !== undefined && record.qualificationInfo !== null && record.qualificationInfo.identityPicture[1] ? record.qualificationInfo.identityPicture[1] : ''} />
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <font style={{ marginLeft: 10, fontSize: '16px' }}>公司营业执照原件扫描件：</font>
              <Col span={12}>
                <PopImage value={record !== null && record !== undefined && record.qualificationInfo !== undefined && record.qualificationInfo !== null ? record.qualificationInfo.companyPicture : ''} />
              </Col>
            </Row>
            <Row gutter={24} style={{ marginLeft: 30, marginTop: 10 }}>
              <font style={{ marginLeft: 10, fontSize: '16px' }}>公司开户许可证原件扫描件：</font>
              <Col span={12}>
                <PopImage value={record !== null && record !== undefined && record.qualificationInfo !== undefined && record.qualificationInfo !== null ? record.qualificationInfo.allowPicture : ''} />
              </Col>
            </Row>
          </div>
          <Divider />
          <Button disabled={record !== null && record !== undefined && (record.reviewStatus === 2 || record.reviewStatus === 3 || record.approvalStatus === 4 || record.approvalStatus === 5)} style={{ marginLeft: 230 }} type='primary' onClick={this.showApprovalPass()}>通过</Button>
          <Button disabled={record !== null && record !== undefined && (record.reviewStatus === 2 || record.reviewStatus === 3 || record.approvalStatus === 4 || record.approvalStatus === 5)} style={{ marginLeft: 20 }} type='primary' danger onClick={this.showApprovalReject()}>不通过</Button>
          <Button style={{ marginLeft: 20 }} type='default' onClick={this.cancelDetailHandle()}>取消</Button>
        </Modal>

        <Modal footer={null} visible={approvalRejectVisible} title='是否确认审批不通过？' onCancel={this.hiddenApprovalReject}>
          <span>失败原因快速选择</span>
          <Select allowClear style={{ marginLeft: 5, width: 230 }} placeholder='请选择' onChange={(v) => { this.faildDescHandler(v) }}>{ faildDescList.map((item, index) => (<Option key={index} value={item.value}>{item.label}</Option>))}</Select>
          <div style={{ marginTop: 10 }} />
          <span style={{ marginLeft: 24, marginTop: 20 }}>手动输入原因</span>
          <Input style={{ width: 230, marginLeft: 10 }} placeholder='请输入' onChange={e => this.setState({ approvalRejectReason: e.target.value })} />
          <div style={{ marginTop: 10 }}>注：原因选填，原因最多输入20字符。提交后，公会后台将提示审批不通过，并展示填写的原因。</div>
          <Divider />
          <Button style={{ marginTop: 10, marginLeft: 150 }} type='primary' onClick={this.approvalRejectHandle()}>确认</Button>
          <Button style={{ marginTop: 10, marginLeft: 30 }} type='default' onClick={this.hiddenApprovalReject2()}>取消</Button>
        </Modal>

        <Modal footer={null} visible={approvalPassVisible} title='是否确认审批通过？' onCancel={this.hiddenApprovalPass}>
          <span style={{ marginLeft: 24, marginTop: 20 }}>输入原因</span>
          <Input style={{ width: 230, marginLeft: 10 }} placeholder='请输入' onChange={e => this.setState({ approvalPassReason: e.target.value })} />
          <div style={{ marginTop: 10 }}>注：原因选填，原因最多输入20字符。</div>
          <Divider />
          <Button style={{ marginTop: 10, marginLeft: 150 }} type='primary' onClick={this.approvalPassHandle()}>确认</Button>
          <Button style={{ marginTop: 10, marginLeft: 30 }} type='default' onClick={this.hiddenApprovalPass2()}>取消</Button>
        </Modal>
      </Card>
    )
  }
}

export default NewGuildSettleInCrysta
