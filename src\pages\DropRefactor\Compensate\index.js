import React, { Component } from 'react'
import { connect } from 'dva'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card, Tabs, Row, Col, Table, Button, InputNumber, Popconfirm, Space, Typography, Select, message, Input } from 'antd'
// import { timeFormater } from '@/utils/common'
import { propTypeOptions, isVoiceRoomPath } from '../dropCommon'
import PrizeSelector from '../DropMain/components/prizeSelector'
import { broadcastOptionsJY, broadcastOptionsVR } from '../DropMain/components/list_common'
const namespace = 'dropCompensate'

@connect(({ dropCompensate }) => ({
  model: dropCompensate
}))

class DropCompensate extends Component {
  state = {
    taskId: 1,
    businessTag: 'jy', // jy-交友 yydog-幸运小狗
    appID: 2,
    editing: false
  }

  componentDidMount = () => {
    const businessTag = isVoiceRoomPath(this.props.route.path) ? 'yydog' : 'jy'
    const appID = isVoiceRoomPath(this.props.route.path) ? 34 : 2
    this.setState({ businessTag, appID })
    this.getAllPrize()
    this.getCompensateConfig(businessTag === 'jy' ? 1 : 2, businessTag)
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  // --------------------------------------------------

  // 获取所有奖励列表
  getAllPrize = () => {
    this.callModel('getAllPrizeList', {
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('获取奖励列表失败: msg=' + msg)
        }
      }
    })
  }

  // 获取配置
  getCompensateConfig = (id, tag) => {
    this.callModel('getCompensateConfig', {
      params: { taskID: id, tag: tag }
    })
  }

  // 检查数据
  checkConfig = (raw) => {
    console.debug(raw)
    if (!raw || !raw.poolList) {
      message.warning('道具池列表为空,请检查')
      return false
    }
    const { poolList } = raw
    for (let i = 0; i < poolList.length; i++) {
      if (poolList[i].rate < 0) {
        message.warning('概率不能小于0, 请检查')
        return false
      }
      if (poolList[i].propId <= 0) {
        message.warning('奖励类型配置有误, 请检查')
        return false
      }
      if (poolList[i].count <= 0) {
        message.warning('奖励数量配置有误, 请检查')
        return false
      }
    }
    return true
  }

  // 全量更新
  updateCompensateConfig = () => {
    const { configData } = this.props.model
    if (!this.checkConfig(configData)) {
      return
    }
    const { businessTag } = this.state
    this.callModel('updateCompensateConfig', {
      params: { rawData: JSON.stringify(configData), tag: businessTag },
      cbFunc: (ok) => {
        if (ok) {
          message.info('更新成功')
          this.setState({ editing: false })
          this.refreshData()
        }
      }
    })
  }

  // 切换道具池
  onTagChange = (id) => {
    this.setState({ taskId: id })
    const { businessTag } = this.state
    this.getCompensateConfig(id, businessTag)
  }

  // 切换业务
  onBusinessTypeChange = (tag) => {
    this.setState({ businessTag: tag })
    const { taskId } = this.state
    this.getCompensateConfig(taskId, tag)
  }

  // 重置信息
  refreshData = () => {
    this.setState({ editing: false })
    this.changeState({ configData: {} })
    const { businessTag } = this.state
    this.getCompensateConfig(this.state.taskId, businessTag)
  }

  // 增加奖励
  addItemToPool = () => {
    const { configData } = this.props.model
    let cp = configData
    let defaultVal = { propsType: 0, count: 0, price: 0, dailyLimit: -1 }
    cp.poolList.push(defaultVal)
    this.changeState('configData', cp)
  }

  // 移除奖励
  removeItemFromPool = (index) => {
    const { configData } = this.props.model
    let newPoolList = []
    let newConfigData = configData
    console.debug('index=', index)
    configData.poolList.forEach((item, i) => {
      if (i !== index) newPoolList.push(item)
    })
    newConfigData.poolList = newPoolList
    this.changeState('configData', newConfigData)
  }

  // 更新道具池列表
  onPoolListChange = (index, v) => {
    const { appID } = this.state
    // console.debug('debugInfo===>', index, v, appID)
    const { configData, globalPrizeList } = this.props.model
    let detail = null
    for (let i = 0; i < globalPrizeList.length; i++) {
      if (globalPrizeList[i].appId === appID && globalPrizeList[i].id === v.value) {
        detail = globalPrizeList[i]
        break
      }
    }
    if (detail == null) return
    let newData = configData
    newData.poolList[index].propId = detail.id
    newData.poolList[index].appId = appID
    newData.poolList[index].name = detail.name
    newData.poolList[index].price = detail.price
    this.changeState('configData', newData)
  }

  onInputChange = (row, field) => value => {
    let cp = this.props.model.configData
    cp.poolList[row][field] = value
    this.changeState('compensateConfig', cp)
  }

  renderColumn = (before) => {
    const { editing, appID, businessTag } = this.state
    const { globalPrizeList } = this.props.model
    if (!editing) {
      return before.filter(item => { return item.dataIndex !== 'dailyLimit' }) // 查看时隐藏日上限字段，后面可能要改回去
    }
    let after = []
    before.forEach(col => {
      if (['propsType'].indexOf(col.dataIndex) > -1) {
        col.render = (text, record, index) => {
          let options = [propTypeOptions][['propsType'].indexOf(col.dataIndex)]
          return <Select onChange={this.onInputChange(index, col.dataIndex)} options={options} defaultValue={text} />
        }
      }
      if (['dailyLimit', 'count', 'rate'].indexOf(col.dataIndex) > -1) {
        col.render = (text, record, index) => {
          return <InputNumber onChange={this.onInputChange(index, col.dataIndex)} defaultValue={text} />
        }
      }
      if (col.dataIndex === 'propId') {
        col.render = (v, r, i) => {
          return <PrizeSelector
            value={v}
            type='select'
            prizeList={globalPrizeList}
            appIDLimit={appID}
            onComfirm={(v) => {
              this.onPoolListChange(i, v)
            }}
          />
        }
      }
      if (col.dataIndex === 'bcType') {
        col.render = (v, r, i) => {
          return <Select options={businessTag === 'jy' ? broadcastOptionsJY : broadcastOptionsVR} value={v}
            onChange={this.onInputChange(i, col.dataIndex)} />
        }
      }
      after.push(col)
    })
    after.push({ title: '操作', render: (v, r, i) => { return <Button danger size='small' onClick={() => { this.removeItemFromPool(i) }}>移除奖励</Button> } })
    return after
  }

  render () {
    const { route } = this.props
    const { editing, taskId, businessTag } = this.state
    const { configData } = this.props.model
    const { Text, Title } = Typography
    const { TabPane } = Tabs
    const columns = [
      { title: '序号', render: (v, r, i) => { return i + 1 } },
      { dataIndex: 'propId', title: '营收ID' },
      { dataIndex: 'name', title: '奖励道具名称' },
      { dataIndex: 'price', title: '单价(金钻)' },
      { dataIndex: 'count', title: '数量' },
      { dataIndex: 'sum', title: '总价值(金钻)', render: (v, r) => r.count * r.price },
      { dataIndex: 'rate', title: '成功概率' },
      { dataIndex: 'dailyLimit', title: '每日限制' },
      // { dataIndex: 'todayCount', title: '当日已发' },
      { dataIndex: 'propsType', title: '稀有程度', render: (v, r) => { return propTypeOptions.find(item => item.value === v)?.label } },
      { dataIndex: 'bcType', title: '广播类型', render: (v, r) => { return (businessTag === 'jy' ? broadcastOptionsJY : broadcastOptionsVR).find(item => item.value === v)?.label } }
    ]

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>

          <Title level={4}>{businessTag === 'jy' ? '交友空投' : '聊天室幸运小狗'}</Title>

          <Tabs defaultActiveKey={businessTag === 'jy' ? 1 : 2} type='line' size='small' onChange={(v) => this.onTagChange(v)} >
            {
              businessTag === 'jy'
                ? <TabPane tab='特别道具池' key={1} disabled={editing && taskId !== 1} />
                : ''
            }
            <TabPane tab='稀有道具池' key={2} disabled={editing && taskId !== 2} />
            <TabPane tab='史诗道具池' key={3} disabled={editing && taskId !== 3} />
            <TabPane tab='传说道具池' key={4} disabled={editing && taskId !== 4} />
          </Tabs>

          {/* <Space direction='vertical' style={{ marginBottom: '2em' }}>  FIXME:临时修改，晚点要还原回去
            <Text>1. 抽取道具次数： 用户在各个端中普通+超级模式抽取道具的总次数</Text>
            <Text>2. 上限： -1表示无上限, 0表示不发此奖励道具, 大于0表示发放上限</Text>
          </Space> */}

          <Row style={{ marginBottom: '1em' }}>
            <Space>
              <Button disabled={editing} onClick={() => this.setState({ editing: true })} >编辑配置</Button>
              <Popconfirm title='确定放弃更改吗?' onConfirm={() => this.refreshData()}>
                <Button danger type='dashed' hidden={!editing}>放弃修改</Button>
              </Popconfirm>
              <Button hidden={!editing} type='primary' onClick={() => { this.addItemToPool() }}>新增奖励</Button>
              <Popconfirm title='确定提交修改吗?' onConfirm={() => this.updateCompensateConfig()}>
                <Button danger hidden={!editing} type='primary'>确认修改</Button>
              </Popconfirm>
            </Space>
          </Row>

          {/* <Row style={{ marginBottom: '2em' }}> FIXME:临时修改，晚点要还原回去
            <Space direction='vertical'>
              <Text type='secondary'>业务标签:  {businessTag}</Text>
              <Text type='secondary'>道具池id: {configData.taskId}</Text>
              <Text type='secondary'>更新人： {configData.uid}</Text>
              <Text type='secondary'>更新时间：{timeFormater(configData.timestamp)}</Text>
              <Text type='secondary'>版本号： {configData.version}</Text>
            </Space>
          </Row> */}

          <Text type='warning' strong>抽取道具次数：</Text>
          <Row style={{ marginBottom: '2em' }}>
            {editing
              ? <InputNumber style={{ width: '10em' }} value={configData.total} onChange={(v) => { let cp = configData; cp.total = v; this.changeState('configData', cp) }} />
              : <InputNumber style={{ width: '10em' }} disabled value={configData.total} />
            }
          </Row>

          <Text type='warning' strong>道具池名称：</Text>
          <Row style={{ marginBottom: '2em' }}>
            {editing
              ? <Input style={{ width: '10em' }} value={configData.name} onChange={(v) => { let cp = configData; cp.name = v.target.value; this.changeState('configData', cp) }} />
              : <Input style={{ width: '10em' }} disabled value={configData.name} />
            }
          </Row>

          <Text type='danger'>道具实际概率=该道具普通概率/全部道具普通概率之和</Text>
          <br />
          <Text type='warning' strong>道具池列表：</Text>
          <Row style={{ marginBottom: '2em' }}>
            <Col span={24}>
              <Table columns={this.renderColumn(columns)} key={configData.poolList} bordered
                dataSource={configData.poolList || []}
                size='small' scroll={{ x: 'max-content' }} />
            </Col>
          </Row>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default DropCompensate
