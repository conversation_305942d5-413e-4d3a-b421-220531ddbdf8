import { simpleRequire2, genGetRequireTemplate } from '@/utils/common'

const submitJadeAccessCfg = genGetRequireTemplate('/seal/boss/update_exchange_jade_config')

export default {
  namespace: 'podExchangeCfg',
  state: {
    accessCfg: [],
    jadeCfg: []
  },

  reducers: {
    updateState (state, { payload }) {
      const { name, newValue } = payload
      return { ...state, [name]: newValue }
    }
  },

  effects: {
    * queryAccessCfg ({ payload }, { select, call, put }) { // 查询豆荚入口限制配置
      let resp = yield call(simpleRequire2, `/pod_mall_boss/get_pod_exchange_entry_info`, null, false)
      if (!resp || resp === 1) {
        resp = [{ name: '', yyLevelLimit: 0, operator: '', timestamp: 0, formattedTime: '' }]
      }
      yield put({
        type: 'updateState',
        payload: { name: 'accessCfg', newValue: resp }
      })
    },
    * queryPodCfg ({ payload }, { select, call, put }) { // 查询豆荚限制配置值
      let resp = yield call(simpleRequire2, `/pod_exchange_cfg/exchange_limit/get_cfg`, null, false)
      if (!resp || resp === 1) {
        resp = { value: 0 }
      }
      const { cbFunc } = payload
      if (cbFunc) {
        cbFunc(resp.value, resp.valueExt)
      }
    },
    * queryJadeCfg ({ payload }, { select, call, put }) { // 查询豆荚入口限制配置
      let resp = yield call(simpleRequire2, `/seal/boss/get_exchange_jade_config`, null, false)
      if (!resp || resp === 1) {
        resp = { name: '', jadeLimit: 0, operator: '', timestamp: 0 }
      }
      yield put({
        type: 'updateState',
        payload: { name: 'jadeCfg', newValue: resp }
      })
    },
    * submitAccessCfg ({ payload }, { select, call, put }) { // 豆荚入口限制配置更新
      const { params, cbFunc } = payload
      let resp = yield call(simpleRequire2, `/pod_mall_boss/mod_pod_exchange_entry_info`, params, false)
      if (resp == null) {
        cbFunc(false)
      }
      cbFunc(true)
    },
    * submitPodLimitValue ({ payload }, { select, call, put }) { // 更新豆荚限制配置
      const { params, cbFunc } = payload
      let resp = yield call(simpleRequire2, `/pod_exchange_cfg/exchange_limit/update`, { newValue: params.podLimitValue, newValueExt: params.exchangeVirtOrTeamLimitValue }, true)
      if (!resp) {
        cbFunc(false)
        return
      }
      cbFunc(true)
    },
    submitJadeAccessCfg
    // * submitJadeAccessCfg ({ payload }, { select, call, put }) { // 战力票兑换限制
    //   const { params, cbFunc } = payload
    //   let resp = yield call(simpleRequire2, `/seal/boss/update_exchange_jade_config`, params, false)
    //   if (resp == null) {
    //     cbFunc(false)
    //   }
    //   cbFunc(true)
    // }
  }
}
