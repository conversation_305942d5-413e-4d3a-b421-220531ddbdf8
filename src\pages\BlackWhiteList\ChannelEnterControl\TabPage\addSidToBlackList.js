import React, { Component } from 'react'
import { connect } from 'dva'
import { Row, Button, Input, Select, Form, message } from 'antd'

// 监控目标UID配置页面
const namespace = 'channelEnterControl'

@connect(({ channelEnterControl }) => ({
  model: channelEnterControl
}))

class AddSidToBlackList extends Component {
  state = {}

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  onSubmit = (v) => {
    this.callModel('updateChannelEnterConfig', {
      params: v,
      cbFunc: (ok) => {
        if (ok) {
          message.info('配置成功')
          this.formRef.resetFields()
        } else message.warn('发生错误')
      }
    })
  }

  render () {
    return (
      <div>
        <text style={{ color: '#339999ba' }}>为方便测试以及紧急修改限制配置, 可以在这里配置其他业务的频道和终端的限制规则;  </text>
        <Row >
          <Form onFinish={(v) => { this.onSubmit(v) }} ref={(v) => { this.formRef = v }}
            initialValues={{ tid: 268435460, sid: 0, ssid: 0, appIDs: 'all', opType: 'ADD' }}>
            <Form.Item name='tid' label='tid'>
              <Input />
            </Form.Item>
            <Form.Item name='sid' label='sid'>
              <Input />
            </Form.Item>
            <Form.Item name='ssid' label='ssid'>
              <Input />
            </Form.Item>
            <Form.Item name='appIDs' label='appIDs' tooltip='输入appID, 需要输入多个时可用英文逗号隔开'>
              <Input />
            </Form.Item>
            <Form.Item name='opType' label='操作'>
              <Select>
                <Select.Option value='ADD'>限制</Select.Option>
                <Select.Option value='DELETE'>取消限制</Select.Option>
              </Select>
            </Form.Item>
            <Button htmlType='submit'>提交</Button>
          </Form>
        </Row>
      </div>
    )
  }
}

export default AddSidToBlackList
