// eslint-disable-next-line no-unused-vars
import { getEssenceExchangeDailyStatsList, getEssenceExchangeDetailList } from './api'
var moment = require('moment')
const dateFormat = 'YYYY-MM-DD HH:mm:ss'

export default {
  namespace: 'essenceExchange',

  state: {
    statsList: [],
    detailList: []
  },

  reducers: {
    updatePodExchangeStatsList (state, { payload }) {
      payload.sort((a, b) => b.date - a.date)
      return {
        ...state,
        statsList: payload
      }
    },
    updatePodExchangeDetailList (state, { payload }) {
      payload.sort((a, b) => b.timestamp - a.timestamp)
      return {
        ...state,
        detailList: payload
      }
    }
  },

  effects: {
    * getPodExchangeStatsList ({ payload }, { call, put }) {
      const { data: { list } } = yield call(getEssenceExchangeDailyStatsList, payload)
      yield put({
        type: 'updatePodExchangeStatsList',
        payload: Array.isArray(list) ? list : []
      })
    },
    * getPodExchangeDetailList ({ payload }, { call, put }) {
      let mergerList = []
      let startIn = payload.startInSec
      let endIn = payload.endInSec
      for (var i = startIn; i < endIn; i += 86400 * 3) {
        let start = i
        let end = i + 86400 * 3
        if (end > endIn) {
          end = endIn
        }
        payload.start = moment(start * 1000).format(dateFormat)
        payload.end = moment(end * 1000).format(dateFormat)
        const { data: { list } } = yield call(getEssenceExchangeDetailList, payload)
        mergerList = mergerList.concat(Array.isArray(list) ? list : [])
      }

      yield put({
        type: 'updatePodExchangeDetailList',
        payload: Array.isArray(mergerList) ? mergerList : []
      })
    }
  }
}
