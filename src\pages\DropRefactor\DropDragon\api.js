import request from '@/utils/request'
import { stringify } from 'qs'

export function getLists (params) {
  return request(`/drop/admin/query_dragon?${stringify(params)}`)
}

export function remove (params) {
  return request(`/drop/admin/remove_dragon?${stringify(params)}`)
}

export function update (params) {
  return request(`/drop/admin/update_dragon?${stringify(params)}`)
}

export function add (params) {
  return request(`/drop/admin/upset_dragon`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: JSON.stringify(params)
  })
}
