import { connect } from 'dva'
import React, { Component } from 'react'
import { Row, Col, Button, Table, Modal, Space, Typography, Input, DatePicker, message, Tooltip } from 'antd'
import { InfoBoard, ReasonSelector } from '@/components/SimpleComponents'
import { timeFormater } from '@/utils/common'
import SearchParams from '@/components/SimpleComponents/searchParams'
import moment from 'moment'

const { Link, Text } = Typography

const namespace = 'hgameTingTools'

@connect(({ hgameTingTools }) => ({
  model: hgameTingTools
}))

class ApprovalList extends Component {
  state = {
    detailVisible: false,
    refruseVisible: false,
    paramsVal: {}
  }

  statusOptions = [
    { label: '申请中', value: '申请中' },
    { label: '已拒绝', value: '已拒绝' },
    { label: '已通过', value: '已通过' },
    { label: '全部', value: '' }
  ]
  // 查询条件
  parmasColumn = [
    { label: '频道ID', fieldName: 'channelID', inputType: 'InputNumber', defaultValue: null, extProps: { style: { width: '10em' } } },
    { label: '申请时间', fieldName: 'submitTime', inputType: 'TimeRange', defaultValue: [null, null], extProps: { style: { width: '16em' }, showTime: true, allowClear: true } },
    { label: '活动时间', fieldName: 'actTime', inputType: 'TimeRange', defaultValue: [null, null], extProps: { style: { width: '16em' }, showTime: true, allowClear: true } },
    { label: '状态', fieldName: 'status', inputType: 'Selecter', defaultValue: '', extProps: { style: { width: '7em' }, options: this.statusOptions } }
  ]

  componentDidMount = () => {
    this.getApprovalList()
  }

  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  getApprovalList = () => {
    this.callModel('getApprovalList')
  }

  // 查询审批详情
  showDetailBySid = (id) => {
    this.callModel('getApprovalDetail', {
      params: { id: id },
      isDetailMode: true,
      cbFunc: (ret) => {
        this.setState({ detailVisible: true })
      }
    })
  }

  // 更新并提交审批
  onApprovalPass = (detailInfo) => {
    let params = {
      result: 'pass',
      id: detailInfo.id,
      fixStartTime: detailInfo.startTime,
      flowLimit: detailInfo.rewardConfig.map(item => { return item.flowLimit }),
      extraReward: detailInfo.rewardConfig.map(item => { return item.extraReward * 100 })
    }

    let checkPass = true
    params.extraReward.forEach(val => {
      if (val % 100 !== 0) {
        message.warn('官方奖励数量需要为100的倍数')
        checkPass = false
      }
    })
    if (!checkPass) {
      return
    }

    this.callModel('activityApproval', {
      params: params,
      isJsonMode: true,
      isSlientMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn(`操作失败,请稍后再试: status=${status} msg=${msg}`)
          return
        }
        message.info('操作成功')
        this.setState({ detailVisible: false })
        this.getApprovalList()
      }
    })
  }

  // 拒绝并说明原因
  onApprovalRefuse = (detailInfo, reason) => {
    console.debug(detailInfo, reason)
    if (!reason) {
      message.warn('请输入驳回原因~')
      return
    }
    this.callModel('activityApproval', {
      params: {
        id: detailInfo.id,
        result: 'reject',
        RejectReason: reason
      },
      isJsonMode: true,
      isSlientMode: true,
      isDetailMode: true,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn(`操作失败,请稍后再试: status=${status} msg=${msg}`)
          return
        }
        message.info('操作成功')
        this.setState({ refruseVisible: false })
        this.getApprovalList()
      }
    })
  }

  // 根据参数过滤申请列表
  approvalLitsFilter = (before) => {
    const { paramsVal } = this.state
    const { channelID, submitTime1, submitTime2, actTime1, actTime2, status } = paramsVal
    let after = []
    before.forEach(item => {
      if (channelID && item.sid !== channelID && item.asid !== channelID) return
      if (submitTime1 && item.submitTime < submitTime1) return
      if (submitTime2 && item.submitTime > submitTime2) return
      if (actTime1 && item.startTime < actTime1) return
      if (actTime2 && item.startTime > actTime2) return
      if (status && item.statusDesc !== status) return
      after.push(item)
    })
    return after
  }

  statusDescHightLighter = (desc, tag) => {
    return <Tooltip title={tag}>
      <Text type={['danger', 'warning', 'secondary', 'success'][['已拒绝', '已下发', '已通过', '申请中'].indexOf(desc)]}>{desc}</Text>
    </Tooltip>
  }

  render () {
    const { approvalDetail, approvalList, pkConfig } = this.props.model
    const { detailVisible, refruseVisible, refruseReason } = this.state
    console.debug('pkConfig==>', pkConfig)
    const columns = [
      { title: '频道ID', dataIndex: 'sid', sorter: (a, b) => a.sid - b.sid, render: (v, r) => { return <Tooltip title={r.id}>{v}</Tooltip> } },
      { title: '公会短号', dataIndex: 'asid', sorter: (a, b) => a.asid - b.asid },
      { title: '发起人yy', dataIndex: 'imid', sorter: (a, b) => a.imid - b.imid },
      { title: '活动开始时间', dataIndex: 'startTime', render: (v) => { return timeFormater(v) }, sorter: (a, b) => a.startTime - b.startTime },
      { title: '申请时间', dataIndex: 'submitTime', render: (v) => { return timeFormater(v) }, sorter: (a, b) => a.submitTime - b.submitTime },
      { title: '状态', dataIndex: 'statusDesc', render: (v, r) => { return this.statusDescHightLighter(v, r.tag) }, sorter: (a, b) => a.statusDesc > b.statusDesc ? 1 : -1 },
      { title: '操作',
        dataIndex: 'id',
        render: (v) => {
          return <Link onClick={() => this.showDetailBySid(v)}>详情</Link>
        } }
    ]
    const tingColumns = [
      { title: '频道ssid', dataIndex: 'ssid' },
      { title: '频道昵称', dataIndex: 'name' }
    ]

    const detailColumns = [
      { label: '活动ID', span: 24, dataIndex: 'id' },
      { label: '发起公会短号', span: 24, dataIndex: 'asid' },
      { label: '厅战开始时间',
        span: 24,
        dataIndex: 'startTime',
        render: (v) => {
          return <DatePicker value={moment.unix(v)} showTime format={'YYYY-MM-DD HH:mm'} onChange={(v) => { let cp = { ...approvalDetail }; cp.startTime = v.unix(); this.changeState('approvalDetail', cp) }} />
        }
      },
      { label: '厅战结束时间', dataIndex: 'endTime', render: (v) => { return timeFormater(v) } },
      { label: '时长', span: 24, dataIndex: 'duration', render: (v) => { return `${v} 小时` } },
      { label: '参与房管厅',
        span: 24,
        dataIndex: 'channelList',
        render: (v) => {
          return <Table columns={tingColumns} dataSource={v} size='small' pagination={{ pageSize: 4 }} bordered rowKey={(item) => { return item.ssid }} />
        } },
      { label: '专属礼物', span: 24, dataIndex: 'ssid', render: (v) => { return `${pkConfig.giftName} (单价${pkConfig.giftPrice}黄水晶)` } },
      { label: '房管厅奖励 ( 流水门槛 -- 公会奖励 -- 官方奖励 )',
        span: 24,
        dataIndex: 'rewardConfig',
        render: (v) => {
          v = v || []
          return <Row>{v.map((item, index) => {
            return <Col span={24} style={{ marginBottom: '5px' }}>
              <Text type='secondary'>第{index + 1}名 --
                <Input value={item.flowLimit / 100} addonAfter='元' style={{ width: '8em' }} size='small'
                  onChange={(v) => { let cp = { ...approvalDetail }; cp.rewardConfig[index].flowLimit = parseInt(v.target.value * 100) || 0; this.changeState('approvalDetail', cp) }} />
                <Text > -- </Text>
                <Input value={`${(item.owRewardVal / 1000).toFixed(2)}元`} disabled style={{ width: '8em' }} size='small' />
                <Text > -- </Text>
                <Input value={item.extraReward} addonAfter='×100个' style={{ width: '10em' }} size='small'
                  onChange={(v) => { let cp = { ...approvalDetail }; cp.rewardConfig[index].extraReward = parseInt(v.target.value) || 0; this.changeState('approvalDetail', cp) }} />
                <Text> 价格{(item.extraReward * 100 * pkConfig.giftPrice / 1000).toFixed(2)}元</Text>
              </Text>
            </Col>
          })}</Row>
        }
      }
    ]

    const reasonColumns = [
      { value: `你发起的厅战已被拒绝，当前有其他活动正在进行，暂不允许发起厅战，如有疑问请联系工作人员:yy号${pkConfig.customerYY}` },
      { value: `你发起的厅战已被拒绝，厅战申请信息填写有误，请重新发起，如有疑问请联系工作人员:yy号${pkConfig.customerYY}` },
      { inputType: 'TextArea', placeholder: '请输入拒绝原因', label: '其他原因', value: '' }
    ]

    return (
      <Row>
        <Col span={24}>
          <SearchParams formatTimeRange columns={this.parmasColumn} onChange={(v) => { this.setState({ paramsVal: v }) }} />
        </Col>

        <Col span={24}>
          <Table columns={columns} dataSource={this.approvalLitsFilter(approvalList)} size='small' pagination={{ pageSize: 50 }} />
        </Col>

        {/* 详细信息模态框 */}
        <Modal visible={detailVisible} title='厅战详情' footer={false} onCancel={() => this.setState({ detailVisible: false })} width='50em'>
          <Row>
            <Col span={24}>
              <InfoBoard columns={detailColumns} dataSource={approvalDetail} />
              <Row style={{ display: 'block', textAlign: 'center' }}>
                <Space>
                  <Button type='primary' onClick={() => { this.onApprovalPass(approvalDetail) }} disabled={approvalDetail?.updateInfo}>通过</Button>
                  <Button onClick={() => { this.setState({ refruseVisible: true, detailVisible: false }) }} disabled={approvalDetail?.updateInfo}>拒绝</Button>
                  <Button onClick={() => { this.setState({ detailVisible: false }) }} >取消</Button>
                </Space>
              </Row>
            </Col>
          </Row>
        </Modal>

        {/* 拒绝确认模态框 */}
        <Modal visible={refruseVisible} title='拒绝厅战申请' onCancel={() => this.setState({ refruseVisible: false, detailVisible: true })} onOk={() => this.onApprovalRefuse(approvalDetail, refruseReason)}>
          <Row>
            <Col span={24}>
              <Text>请填写拒绝原因</Text>
            </Col>
            <Col span={24}>
              <ReasonSelector columns={reasonColumns} defaultValue={''} width='30em' onChange={(v) => this.setState({ refruseReason: v })} />
            </Col>
          </Row>
        </Modal>
      </Row>
    )
  }
}

export default ApprovalList
