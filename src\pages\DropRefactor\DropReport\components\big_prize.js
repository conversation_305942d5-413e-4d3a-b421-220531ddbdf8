import React, { Component } from 'react'
import { Card, Table, DatePicker, Divider, Button, Form, Input, Tooltip, Select, Space, message } from 'antd'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { connect } from 'dva'
import moment from 'moment'
import { CSVLink } from 'react-csv'
import { getFieldsValueByChannelID } from '../../globalConfig'

const namespace = 'dropReport'

const poolModeOptions = [
  { label: '普通', value: 0 },
  { label: '疯狂', value: 1 },
  { label: '龙宫', value: 2 },
  { label: '补给箱', value: 3 },
  { label: '组合大礼', value: 4 }
]

const businessNameListJY = [ '交友空投', '宝贝空投', '交友抢物资', '交友物资大战' ]
const businessNameListVR = [ '语音房空投', '语音房小狗', '语音房抢物资' ]
const appNameListJY = ['PC', 'Yo交友', 'Yo语音', '手Y', 'H5']
const appNameListVR = ['PC', 'Yo交友', 'Yo语音', '手Y', 'WEB', 'H5', '好看', '贴吧']

const businessNameOptionsJY = businessNameListJY.map(item => {
  return { label: item, value: item }
})

const businessNameOptionsVR = businessNameListVR.map(item => {
  return { label: item, value: item }
})

const appNameOptionsJY = appNameListJY.map(item => {
  return { label: item, value: item }
})

const appNameOptionsVR = appNameListVR.map(item => {
  return { label: item, value: item }
})

const limitModeOptions = [
  { label: '全部', value: -1, key: -1 },
  { label: '大盘动态发放道具', value: 1, key: 1 },
  { label: '道具池动态发放道具', value: 2, key: 2 },
  { label: '基础模式', value: 0, key: 0 }
]

const extraModeOptions = [
  { label: '全部', value: -1, key: -1 },
  { label: '翻倍', value: 1, key: 1 },
  { label: '不翻倍', value: 0, key: 0 }
]

const defaultFormValue = { range: [moment().add(-7, 'days'), moment()], businessName: '', appName: '', limitMode: -1, poolMode: -1, extVal: -1, propsName: '', propsId: '' }

// 大礼发放监控-Tab
@connect(({ dropReport }) => ({ // model 的 namespace
  model: dropReport // model 的 namespace
}))

class DropReportBigPrizeComponent extends Component {
  componentDidMount () {
    const { groupType } = this.props
    const businessNameAll = groupType === 'jybw' ? businessNameListJY.join(',') : businessNameListVR.join(',')
    let values = { ...defaultFormValue }
    if (values.businessName === '') {
      values.businessName = businessNameAll
    }
    values.businessName = businessNameAll
    values.begin = values.range[0].format('YYYYMMDD')
    values.end = values.range[1].format('YYYYMMDD')
    values.propsId = parseInt(values.propsId) || -1
    values.poolMode = parseInt(values.poolMode) || -1
    values.uid = 0
    delete values.range
    this.queryData(values)
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 表头显示提示语
  genColumnTooltip = (title) => {
    return {
      filterDropdown: (<span />),
      filterIcon: (
        <Tooltip placement='top' title={title}>
          <QuestionCircleOutlined style={{ fontSize: '16px' }} />
        </Tooltip>
      )
    }
  }

  // 创建tooltip
  createTooltip = (v, width = 10) => {
    return !v ? '-' : <Tooltip title={v}>
      <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', width: `${width}em` }}>{v}</div>
    </Tooltip>
  }

  // 格式化道具池
  modeFormater = (v, r) => {
    const { compensatConfig } = this.props.model
    const { csTaskTotal, csTaskId, progress, channel, extra } = r
    if (extra === 1) {
      return '翻倍'
    }
    if (v === 3) { // 补给箱显示具体的补足名称
      let csName = compensatConfig.find(val => val.taskId === csTaskId && val.channelList.indexOf(channel) >= 0)?.name
      return csName + '/' + csTaskTotal
    }
    return poolModeOptions.find(val => val.value === v)?.label + (progress > 0 ? '/' + progress : '')
  }

  appFormater = (channelID) => {
    let appName = getFieldsValueByChannelID(channelID, 'AppName')
    let platForm = getFieldsValueByChannelID(channelID, 'PlatForm')
    if (appName === platForm) {
      return appName
    }
    return `${appName}-${platForm}`
  }

  // 礼物名称渲染
  propsNameFormater = (v, r) => {
    if (r.mode !== 4) {
      return v
    }
    return r.bundleName
    // return <Text style={{ color: 'orange' }}>{}</Text>
  }

  // 礼物ID格式化
  propsIdFormater = (v, r) => {
    if (r.mode !== 4) {
      return `${v}-${r.propsName}`
    }
    let ret = ''
    if (!r.prizeList) {
      r.prizeList = []
    }
    r.prizeList.forEach(item => { ret += `${item.propsId}-${item.name}×${item.count}; ` })
    return ret
  }

  // 礼物数量格式化
  propsCountFormater = (v, r) => {
    if (r.mode !== 4) {
      return v
    }
    return r.bundleCount
  }

  // 礼物价值格式化
  totalValueFormater = (record) => {
    if (record.mode !== 4) {
      return (record.propsCount * record.propsPrice).toLocaleString()
    }
    let totalVal = 0
    if (Array.isArray(record.prizeList)) {
      record.prizeList.forEach(item => {
        totalVal += item.count * item.value
      })
    }
    return totalVal.toLocaleString()
  }

  tip1 = `格式说明 1：补给箱奖励道具=>[领取时累积次数 / 任务完成所需次数];
  2：医疗包、空投获得的任务奖励道具=>[重置时次数 / 任务完成所需次数];
  3：不影响补足进度=>[横杆]`
  tip2 = `1:补给箱=>[补给名称/任务完成所需数量]; 2：龙宫、空投=>[类型/进度]`

  // 需要修改
  columns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: '时间', dataIndex: 'microsecond', align: 'center', render: (v, rec) => moment(v > 0 ? v / 1000 : rec.timestamp * 1000).format('HH:mm:ss.SSS') },
    { title: '成功uid', dataIndex: 'uid', align: 'center' },
    { title: '业务', dataIndex: 'channel', align: 'center', render: (v) => { return getFieldsValueByChannelID(v, 'BusinessName') } },
    { title: '产品端', dataIndex: 'channel', align: 'center', render: (v) => { return this.appFormater(v) } },
    { title: '道具池', dataIndex: 'mode', align: 'center', render: (v, r) => this.modeFormater(v, r), ...this.genColumnTooltip(this.tip2) },
    { title: '礼物名称', dataIndex: 'propsName', align: 'center', render: (v, r) => { return this.propsNameFormater(v, r) } },
    { title: '礼物数量', dataIndex: 'propsCount', align: 'center', render: (v, r) => { return this.propsCountFormater(v, r) } },
    { title: '补给箱抽取道具次数', render: (v, r) => { return (r.csTaskId > 0 ? `${r.csTaskCount} / ${r.csTaskTotal}` : '-').replace('0 / 0', '-') }, ...this.genColumnTooltip(this.tip1) },
    { title: '礼物ID', dataIndex: 'propsId', align: 'center', render: (v, r) => { return this.propsIdFormater(v, r) } },
    { title: '礼物价值', align: 'center', render: (_, record) => { return this.totalValueFormater(record) } },
    { title: '成功频道', dataIndex: 'sid', align: 'center' },
    { title: '成功子频道', dataIndex: 'ssid', align: 'center' },
    { title: '当前主持人', dataIndex: 'compereUid', align: 'center' },
    { title: '发放道具模式', dataIndex: 'dynamicMode', align: 'center', render: (v) => { return limitModeOptions.find(item => item.value === v)?.label } },
    { title: '集齐花费金额', dataIndex: 'bundleCosts', align: 'center', ...this.genColumnTooltip('紫水晶') }
  ]

  onFinish = values => {
    const { groupType } = this.props
    const businessNameAll = groupType === 'jybw' ? businessNameListJY.join(',') : businessNameListVR.join(',')
    if (values.businessName === '') {
      values.businessName = businessNameAll
    }
    values.begin = values.range[0].format('YYYYMMDD')
    values.end = values.range[1].format('YYYYMMDD')
    values.propsId = parseInt(values.propsId) || -1
    values.poolMode = parseInt(values.poolMode)
    values.uid = values.uid || 0
    delete values.range
    this.queryData(values)
  }

  queryData = (values) => {
    this.callModel('getMonitorBigPrize', {
      params: values,
      isSlientMode: true,
      cbFunc: (ret) => {
        const { status, msg, list } = ret
        if (status !== 0) {
          message.warn('查询失败: ' + msg)
          return
        }
        if (list.length === 0) {
          message.warn('数据为空')
          return
        }
        message.success('查询完成,数据量=' + list.length)
      }
    })
  }

  export = () => {
    const { model: { monitorList } } = this.props
    let list = []
    monitorList.forEach(v => {
      list.push({
        日期: v.date,
        时间: moment.unix(v.timestamp).format('HH:mm:ss'),
        成功UID: v.uid,
        业务: getFieldsValueByChannelID(v.channel, 'BusinessName'),
        产品端: this.appFormater(v.channel, 'BusinessName'),
        道具池: this.modeFormater(v.mode, v),
        礼物名称: this.propsNameFormater(v.propsName, v),
        礼物数量: this.propsCountFormater(v.propsCount, v),
        礼物ID: this.propsIdFormater(v.propsId, v),
        礼物价值: this.totalValueFormater(v),
        成功频道: v.sid,
        成功子频道: v.ssid,
        当前主持人: v.compereUid,
        发放道具模式: limitModeOptions.find(item => item.value === v.dynamicMode)?.label,
        集齐花费金额: v.bundleCosts
      })
    })
    return list
  }

  /* *******************************页面布局***************************************************************/
  render () {
    const { model: { monitorList }, groupType } = this.props
    const businessNameOptions = groupType === 'jybw' ? businessNameOptionsJY : businessNameOptionsVR
    const appNameOptions = groupType === 'jybw' ? appNameOptionsJY : appNameOptionsVR

    return (
      <Card>
        <Form ref={form => { this.formRef = form }} layout='vertical' onFinish={this.onFinish} initialValues={defaultFormValue} >
          <Space direction='horizontal' size='middle' >
            <Form.Item name='range' label='时间范围' >
              <DatePicker.RangePicker />
            </Form.Item>
            <Form.Item name='businessName' label='业务'>
              <Select options={[{ label: '全部', value: '' }, ...businessNameOptions]} style={{ minWidth: '8em' }} defaultValue='' />
            </Form.Item>
            <Form.Item name='appName' label='产品端'>
              <Select options={[{ label: '全部', value: '' }, ...appNameOptions]} style={{ minWidth: '8em' }} defaultValue='' />
            </Form.Item>
            <Form.Item name='poolMode' label='道具池'>
              <Select options={[{ label: '全部', value: -1 }, ...poolModeOptions]} style={{ minWidth: '8em' }} defaultValue={-1} />
            </Form.Item>
            <Form.Item name='limitMode' label='发放道具模式'>
              <Select options={limitModeOptions} style={{ minWidth: '8em' }} defaultValue={-1} />
            </Form.Item>
            <Form.Item name='extVal' label='翻倍模式'>
              <Select options={extraModeOptions} style={{ minWidth: '8em' }} defaultValue={-1} />
            </Form.Item>
            <Form.Item name='propsName' label='礼物名称'>
              <Input style={{ width: '8em' }} />
            </Form.Item>
            <Form.Item name='propsId' label='礼物ID'>
              <Input style={{ width: '8em' }} />
            </Form.Item>
            <Form.Item name='uid' label='UID'>
              <Input style={{ width: '8em' }} placeholder='默认查全部' />
            </Form.Item>
            <Divider type='vertical' />
            <Tooltip title='大道具指单次发放道具的礼物价值大于500,000紫水晶'>
              <Button htmlType='submit' type='primary'>查询</Button>
            </Tooltip>
            <Button><CSVLink data={this.export()}>导出</CSVLink></Button>
          </Space>
        </Form>

        <Divider />
        <Table rowKey={(record, index) => index} bordered dataSource={monitorList} columns={this.columns} pagination={false} /> {/* 显示的列表 */}
      </Card>
    )
  }
}

export default DropReportBigPrizeComponent
