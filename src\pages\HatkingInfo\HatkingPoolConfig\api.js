import request from '@/utils/request'
import { stringify } from 'qs'

export function getGrandPeriodProbabilityApi () {
  return request(`/lottery_hatking/get_grand_period_probability_info`)
}

export function getGrandPeriodProbabilityHistoryApi () {
  return request(`/lottery_hatking/get_grand_period_probability_history`)
}

export function updateGrandPeriodProbabilityApi (params) {
  return request(`/lottery_hatking/mod_grand_period_probability_info?${stringify(params)}`)
}
