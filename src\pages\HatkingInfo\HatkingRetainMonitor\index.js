import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { <PERSON><PERSON>, Card, DatePicker, Divider, Form, Input, Table, Tabs } from 'antd'
import { connect } from 'dva'
import React, { Component } from 'react'
import exportExcel from '@/utils/exportExcel'

var moment = require('moment')
const namespace = 'hatkingRetainMonitor'
const getRetainListUri = `${namespace}/getRetainList`
const getWinnerListUri = `${namespace}/getWinnerList`
const { TabPane } = Tabs
const userTypeMap = { all: 'ALL', win: '成功用户', loss: '累计失败用户', profit: '累计成功用户' }
const userTypeFilters = [
  { text: 'ALL', value: 'all' },
  { text: '成功用户', value: 'win' },
  { text: '累计失败用户', value: 'loss' },
  { text: '累计成功用户', value: 'profit' }
]

@connect(({ hatkingRetainMonitor }) => ({
  model: hatkingRetainMonitor
}))
class HatkingRetainMonitor extends Component {
  // 定义列表结构，
  retainColumns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    {
      title: '用户范围',
      dataIndex: 'userType',
      align: 'center',
      render: (text) => { return userTypeMap[text] },
      filters: userTypeFilters,
      onFilter: (value, record) => record.platform.includes(value)
    },
    { title: '参与用户数', dataIndex: 'userCount', align: 'center' },
    { title: '模拟流水', dataIndex: 'betAmethyst', align: 'center', render: (text) => this.fixCurrencyUnit(text) },
    { title: '次日存留', dataIndex: 'retainRate1', align: 'center' },
    { title: '次日模拟流水', dataIndex: 'betAmethyst1', align: 'center', render: (text) => this.fixCurrencyUnit(text) },
    { title: '3日存留', dataIndex: 'retainRate2', align: 'center' },
    { title: '3日模拟流水', dataIndex: 'betAmethyst2', align: 'center', render: (text) => this.fixCurrencyUnit(text) },
    { title: '4日存留', dataIndex: 'retainRate3', align: 'center' },
    { title: '4日模拟流水', dataIndex: 'betAmethyst3', align: 'center', render: (text) => this.fixCurrencyUnit(text) },
    { title: '5日存留', dataIndex: 'retainRate4', align: 'center' },
    { title: '5日模拟流水', dataIndex: 'betAmethyst4', align: 'center', render: (text) => this.fixCurrencyUnit(text) },
    { title: '6日存留', dataIndex: 'retainRate5', align: 'center' },
    { title: '6日模拟流水', dataIndex: 'betAmethyst5', align: 'center', render: (text) => this.fixCurrencyUnit(text) },
    { title: '7日存留', dataIndex: 'retainRate6', align: 'center' },
    { title: '7日模拟流水', dataIndex: 'betAmethyst6', align: 'center', render: (text) => this.fixCurrencyUnit(text) }
  ]

  winnerColumns = [
    { title: '日期', dataIndex: 'date', align: 'center' },
    { title: 'UID', dataIndex: 'uid', align: 'center' },
    { title: '模拟流水', dataIndex: 'betAmethyst', align: 'center', render: (text) => this.fixCurrencyUnit(text) },
    { title: '参与次数', dataIndex: 'betCount', align: 'center' },
    { title: '成功率', dataIndex: 'winRate', align: 'center' },
    { title: '是否成功', dataIndex: 'isWinner', align: 'center', render: (text) => text === 1 ? '成功用户' : '未成功' }
  ]

  defaultPageValue = { defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  pagination = { pageSizeOptions: ['20', '50', '100'], showSizeChanger: true, defaultPageSize: 20, showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items` }

  state = { visible: false, isUpdate: false, value: {}, startValue: moment().subtract(7, 'days'), endValue: moment().add(1, 'days'), uid: 0 }

  // 获取列表
  componentDidMount () {
    this.loadData()
  }

  loadData = (activityKey) => {
    if (activityKey === undefined) {
      activityKey = '1'
    }

    const { dispatch } = this.props
    const { startValue, endValue, uid } = this.state
    var data = { start: moment(startValue).format('YYYY-MM-DD'), end: moment(endValue).format('YYYY-MM-DD'), query_uid: uid }
    dispatch({
      type: this.getSelectedKey(activityKey),
      payload: data
    })
  }

  fixCurrencyUnit = (amount) => {
    if (amount > 0) {
      return amount / 1000
    }
    return 0
  }

  disabledStartDate = (startValue) => {
    const endValue = this.state.endValue
    if (!startValue || !endValue) {
      return false
    }
    return startValue.valueOf() > endValue.valueOf()
  }

  disabledEndDate = (endValue) => {
    const startValue = this.state.startValue
    if (!endValue || !startValue) {
      return false
    }
    return endValue.valueOf() <= startValue.valueOf()
  }

  onChange = (field, value) => {
    this.setState({
      [field]: value
    })
  }

  onStartChange = (value) => {
    this.onChange('startValue', value)
  }

  onEndChange = (value) => {
    this.onChange('endValue', value)
  }

  onRetainClick = () => {
    const { dispatch } = this.props
    const { startValue, endValue } = this.state
    var data = { start: moment(startValue).format('YYYY-MM-DD'), end: moment(endValue).format('YYYY-MM-DD') }
    dispatch({
      type: getRetainListUri,
      payload: data
    })
  }

  onWinnerClick = () => {
    const { dispatch } = this.props
    const { startValue, endValue, uid } = this.state
    var data = { start: moment(startValue).format('YYYY-MM-DD'), end: moment(endValue).format('YYYY-MM-DD'), query_uid: uid }
    dispatch({
      type: getWinnerListUri,
      payload: data
    })
  }

  getSelectedKey = (activityKey) => {
    let uri
    if (activityKey === '1') {
      uri = getRetainListUri
    } else if (activityKey === '2') {
      uri = getWinnerListUri
    }

    return uri
  }

  tabOnChange = type => activityKey => {
    if (type !== undefined || type != null) {
      activityKey = type
    }

    this.loadData(activityKey)
  }

  onRetainExport = () => {
    let headers = []
    this.retainColumns.forEach(function (item) {
      headers.push({ key: item.dataIndex, header: item.title })
    })

    const { model: { list } } = this.props
    var exportData = list.map(item => {
      let v = $.extend(true, {}, item)
      v.userType = userTypeMap[v.userType]
      v.betAmethyst = this.fixCurrencyUnit(v.betAmethyst)
      v.betAmethyst1 = this.fixCurrencyUnit(v.betAmethyst1)
      v.betAmethyst2 = this.fixCurrencyUnit(v.betAmethyst2)
      v.betAmethyst3 = this.fixCurrencyUnit(v.betAmethyst3)
      v.betAmethyst4 = this.fixCurrencyUnit(v.betAmethyst4)
      v.betAmethyst5 = this.fixCurrencyUnit(v.betAmethyst5)
      v.betAmethyst6 = this.fixCurrencyUnit(v.betAmethyst6)
      return v
    })

    exportExcel(headers, exportData)
  }

  onWinnerExport = () => {
    let headers = []
    this.winnerColumns.forEach(function (item) {
      headers.push({ key: item.dataIndex, header: item.title })
    })

    const { model: { list } } = this.props
    var exportData = list.map(item => {
      let v = $.extend(true, {}, item)
      v.role = v.role === '' ? '未知' : v.role
      v.betAmethyst = this.fixCurrencyUnit(v.betAmethyst)
      v.winAmethyst = this.fixCurrencyUnit(v.winAmethyst)
      v.sealCostAmount1 = this.fixCurrencyUnit(v.sealCostAmount1)
      v.sealCostAmount2 = this.fixCurrencyUnit(v.sealCostAmount2)
      v.sealCostAmount3 = this.fixCurrencyUnit(v.sealCostAmount3)
      v.sealCostAmount4 = this.fixCurrencyUnit(v.sealCostAmount4)
      v.sealIssueAmount1 = this.fixCurrencyUnit(v.sealIssueAmount1)
      v.isWinner = v.isWinner === 1 ? '成功用户' : '未成功'
      v.isProfit = v.isProfit === 1 ? '累计成功用户' : v.isProfit === 2 ? '累计失败用户' : '持平'

      return v
    })

    exportExcel(headers, exportData)
  }

  render () {
    const { route, model: { list } } = this.props
    const { startValue, endValue } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Tabs onChange={this.tabOnChange()} type='card'>
          <TabPane tab='单机帽子王监控' key='2'>
            <Card>
              <div>
                UID
                <Input onChange={e => this.setState({ uid: e.target.value })} style={{ marginLeft: 5, width: 150 }} /> {/* 搜索按钮 */}
                <span>日期范围</span>
                <DatePicker
                  disabledDate={this.disabledStartDate}
                  format='YYYY-MM-DD'
                  defaultValue={startValue}
                  placeholder='开始日期'
                  onChange={this.onStartChange}
                  style={{ marginLeft: 5 }}
                />
                <span style={{ marginLeft: 5 }}>~</span>
                <DatePicker
                  disabledDate={this.disabledEndDate}
                  format='YYYY-MM-DD'
                  defaultValue={endValue}
                  placeholder='结束日期'
                  onChange={this.onEndChange}
                  style={{ marginLeft: 5 }}
                />
                <Button style={{ marginLeft: 5 }} type='primary' onClick={this.onWinnerClick}>查询</Button>
                <Button style={{ marginLeft: 5 }} type='primary' htmlType='submit' onClick={this.onWinnerExport}>导出</Button>
                <font style={{ marginLeft: 5 }} color='red'>流水的单位为元(仅作模拟测算参考)</font>
              </div>
              <Divider />
              <Form>
                <Table dataSource={list} columns={this.winnerColumns} rowKey={(record, index) => index} pagination={this.pagination} size='small' />
              </Form>
            </Card>
          </TabPane>
        </Tabs>
      </PageHeaderWrapper>
    )
  }
}
export default HatkingRetainMonitor
