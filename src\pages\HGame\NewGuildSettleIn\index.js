import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Tabs, Card } from 'antd'

import NewGuildSettleInCrysta from './components/crysta'
import NewGuildSettleInSuperCrysta from './components/superCrysta'
import NewGuildSettleInWhite from './components/white'

const TabPane = Tabs.TabPane

class NewGuildSettleIn extends Component {
  state = { activeKey: '1' }
  onTabClick = key => {
    this.setState({ activeKey: key })
  }

  render () {
    const { route } = this.props
    const { activeKey } = this.state

    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs type='card' defaultActiveKey='1' onTabClick={this.onTabClick}>
            <TabPane tab='申请水晶公会' key='1'>
              { activeKey === '1' ? <NewGuildSettleInCrysta /> : ''}
            </TabPane>
            <TabPane tab='申请超级水晶公会' key='2'>
              { activeKey === '2' ? <NewGuildSettleInSuperCrysta /> : '' }
            </TabPane>
            <TabPane tab='绿色通道白名单' key='3'>
              { activeKey === '3' ? <NewGuildSettleInWhite /> : '' }
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default NewGuildSettleIn
