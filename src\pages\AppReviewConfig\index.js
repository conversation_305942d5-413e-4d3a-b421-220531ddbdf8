import React, { Component } from 'react'
import PageHeaderWrapper from '@/components/PageHeaderWrapper'
import { Card } from 'antd'
import Tabs, { TabPane } from '@/components/SimpleComponents/smartTabs'
import AppReviewConfigList from './component/appReviewConfig'
import AppReviewApprovalList from './component/approvalList'
import { connect } from 'dva'

@connect(({ appReviewConfig }) => ({
  model: appReviewConfig
}))

class AppReviewConfig extends Component {
  render () {
    // const { route, model: { configList } } = this.props
    const { route } = this.props
    return (
      <PageHeaderWrapper title={route.name}>
        <Card>
          <Tabs id='appReview' type='card' defaultActiveKey='curAppReview' onTabClick={this.onTabClick}>
            <TabPane tab='当前配置' key='curAppReview'>
              <AppReviewConfigList />
            </TabPane>
            <TabPane tab='审批记录' key='appReviewLog'>
              <AppReviewApprovalList />
            </TabPane>
          </Tabs>
        </Card>
      </PageHeaderWrapper>
    )
  }
}

export default AppReviewConfig
