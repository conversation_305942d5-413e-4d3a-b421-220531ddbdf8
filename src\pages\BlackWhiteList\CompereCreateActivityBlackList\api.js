import request from '@/utils/request'

export function getLists () {
  let url = `/black_list/compere_create_activity_black_list`
  return request(url, { jsonp: true })
}

export function blackListAdd (uid) {
  let form = 'uid=' + uid
  return request(`/black_list/compere_create_activity_black_add`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    },
    body: form
  })
}

export function blackListDel (uid, sid) {
  let form = 'uid=' + uid + '&sid=' + sid
  return request(`/black_list/compere_create_activity_black_del`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
    },
    body: form
  })
}
