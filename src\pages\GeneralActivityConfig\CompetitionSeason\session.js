import React, { Component } from 'react'
import { connect } from 'dva'
import { Card, Input, Button, message, Form, Row, Space, Divider, InputNumber, Table, Typography, Modal, DatePicker } from 'antd'
import { deepClone } from '@/utils/common'
import { PlusOutlined } from '@ant-design/icons'
import { ImagesList, ValueMap } from '@/components/SimpleComponents'
import InputPicture from '@/components/InputPicture'

const namespace = 'competitionSession'
const moment = require('moment')

const defaultItem = { titleName: '待命名', stageID: 0, stageName: '', stageIcon: '', propsName: '', propsURL: '', propsIconEx: '', stageStart: moment().format('YYYY-MM-DD HH:mm:ss'), stageEnd: moment().format('YYYY-MM-DD HH:mm:ss') }

@connect(({ competitionSession }) => ({
  model: competitionSession
}))

class Session extends Component {
  state = {
    editing: false,
    updateVisble: false,
    targetRow: 0,
    target: {},
    session: {}
  }

  componentDidMount = () => {
    const { session } = this.props
    this.setState({ session: session })
  }

  // 调用 model 处理函数
  callModel = (funcName, params) => {
    const { dispatch } = this.props
    dispatch({
      type: `${namespace}/${funcName}`,
      payload: params
    })
  }

  // 修改单个model state 成员
  changeState = (name, newValue) => {
    this.callModel('updateState', {
      name: name, newValue: newValue
    })
  }

  genInput = (lable, field, editing, tip = '', isNumber = false) => {
    const { session } = this.state
    let cpSession = { ...session }
    const Cpm = isNumber ? InputNumber : Input
    return (
      <Form.Item label={lable} tooltip={tip}>
        <Cpm disabled={!editing} style={{ width: '100%' }} placeholder={tip} value={cpSession[field]} onChange={(e) => {
          cpSession[field] = isNumber ? e : e.target.value
          this.setState({ session: cpSession })
        }} />
      </Form.Item>
    )
  }

  genInput2 = (lable, field, tip = '', isNumber = false) => {
    let { target } = this.state
    const Cpm = isNumber ? InputNumber : Input
    return (
      <Form.Item label={lable} tooltip={tip}>
        <Cpm placeholder={tip} style={{ width: '100%' }} value={target[field]} onChange={(e) => {
          target[field] = isNumber ? e : e.target.value
          this.setState({ target })
        }} />
      </Form.Item>
    )
  }

  getTimePicker = (lable, field, tip = '') => {
    let { target } = this.state
    const stdFormat = 'YYYY-MM-DD HH:mm:ss'
    return (
      <Form.Item label={lable} tooltip={tip}>
        <DatePicker format={stdFormat} showTime value={moment(target[field], stdFormat)} onChange={(v) => {
          if (!v) {
            message.warn('时间必填')
            return
          }
          target[field] = v.format(stdFormat)
          this.setState({ target })
        }} />
      </Form.Item>
    )
  }

  genPictureInput = (lable, field, tip = '') => {
    let { target } = this.state
    const value = target[field]
    return (
      <Form.Item label={lable} tooltip={tip}>
        <InputPicture
          key={target.stageID}
          value={value}
          width={50}
          onChange={(v) => {
            target[field] = v
            this.setState({ target })
          }} />
      </Form.Item>
    )
  }

  // 检查和修正mission传参
  checkParams = (session) => {
    return session
  }

  // 更新任务配置
  saveMission = (session) => {
    let newSession = this.checkParams(session)
    if (!newSession) {
      return
    }
    this.callModel('updateConfig', {
      isDetailMode: true,
      isJsonMode: true,
      params: session,
      cbFunc: (ret) => {
        const { status, msg } = ret
        if (status !== 0) {
          message.warn('更新失败,请稍后再试: ' + msg)
          return
        }
        message.success('更新成功')
        this.callModel('getConfigList')
        this.setState({ editing: false })
      }
    })
  }

  // 重置状态
  refreshList = () => {
    this.props.onRefresh()
    this.setState({ editing: false })
  }

  render () {
    const { editing, session, updateVisble, target } = this.state
    const { Title } = Typography

    const columns = [
      { title: '赛季title', dataIndex: 'titleName' },
      { title: '赛季ID', dataIndex: 'stageID' },
      { title: '赛季名称', dataIndex: 'stageName' },
      { title: '赛季封面', dataIndex: 'stageIcon', render: (v) => { return v ? <ImagesList imgList={v} width={40} height={40} /> : '-' } },
      { title: '礼物名称', dataIndex: 'propsName' },
      { title: '礼物id配置', dataIndex: 'propsIDMap', render: (v) => { return <div style={{ maxWidth: '15em' }}><ValueMap isEdit={false} value={v} /></div> } },
      { title: '魔法礼物', dataIndex: 'jyRestrictGift' },
      { title: '礼物图片', dataIndex: 'propsURL', render: (v) => { return v ? <ImagesList imgList={v} width={40} height={40} /> : '-' } },
      { title: '礼物动图', dataIndex: 'propsIconEx', render: (v) => { return v ? <ImagesList imgList={v} width={40} height={40} /> : '-' } },
      { title: '开始时间', dataIndex: 'stageStart' },
      { title: '结束时间', dataIndex: 'stageEnd' }
    ]
    if (editing) {
      columns.push({ title: '操作',
        render: (v, r, i) => {
          return (
            <>
              <a onClick={() => { this.setState({ targetRow: i, target: r, updateVisble: true }) }}>编辑</a>
              <a style={{ color: 'red', marginLeft: '1em' }} onClick={() => {
                let newList = session.stageList.filter((item, index) => { return index !== i })
                session.stageList = newList
                this.setState({ session })
              }}>删除</a>
            </>
          )
        } })
    }

    return (
      <Card>
        <Row>

          <Space>
            <Button style={{ width: '6em' }} hidden={editing} onClick={() => { this.setState({ editing: true }) }} >编辑</Button>
            <Button style={{ width: '6em' }} hidden={!editing} onClick={() => { this.refreshList() }} >取消编辑</Button>
            <Button disabled={!editing} style={{ width: '6em' }} type='primary' onClick={() => this.saveMission(session)} >保存</Button>
          </Space>
        </Row>

        <Divider />
        <Title level={5}>基础信息</Title>
        <Form style={{ width: '24em' }} labelCol={{ span: 6, align: 'left' }} >
          {this.genInput('年份', 'year', false)}
        </Form>

        <Title level={5}>赛季配置</Title>
        <Button hidden={!editing} style={{ marginBottom: '1em' }}
          onClick={() => {
            session.stageList.push(defaultItem)
            this.setState({ session })
          }}><PlusOutlined />新增一行</Button>

        <Table bordered columns={columns} dataSource={deepClone(session.stageList || [])} rowKey={(_, index) => index} pagination={false} scroll={{ x: 'max-content' }} />

        <Modal title={`${target.titleName}季配置`} visible={updateVisble} onCancel={() => { this.setState({ updateVisble: false }) }} footer={null}>

          <Form labelCol={{ span: 6, align: 'left' }} style={{ width: '100%' }} >
            {this.genInput2('赛季title', 'titleName', '', false)}
            {this.genInput2('赛季ID', 'stageID', '', true)}
            {this.genInput2('赛季名称', 'stageName', '', false)}
            {this.genPictureInput('赛季封面', 'stageIcon', '', false)}
            {this.genInput2('礼物名称', 'propsName', '', false)}
            {this.genPictureInput('礼物图片', 'propsURL', '', false)}
            {this.genPictureInput('礼物动图', 'propsIconEx', '', false)}
            {this.getTimePicker('开始时间', 'stageStart', '', false)}
            {this.getTimePicker('结束时间', 'stageEnd', '', false)}
            <Form.Item label='礼物ID配置'>
              <ValueMap value={target.propsIDMap} isEdit
                onChange={(v) => {
                  let cp = { ...target }
                  cp.propsIDMap = v
                  this.setState({ target: cp })
                }} />
            </Form.Item>
            {this.genInput2('魔法礼物', 'jyRestrictGift', '', true)}
          </Form>

          <Button onClick={() => {
            let { session, targetRow, target } = this.state
            session.stageList[targetRow] = target
            this.setState({ session, updateVisble: false })
            message.warning('配置未保存, 请通过顶部 保存 按钮保存配置~')
          }}>确定</Button>

        </Modal>

      </Card>
    )
  }
}

export default Session
